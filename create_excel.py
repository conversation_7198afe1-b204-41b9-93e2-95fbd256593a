import pandas as pd
from datetime import datetime

# Create users.xlsx
users_data = {
    '_id': [''],
    'openid': ['test_openid_123'],
    'unionid': [''],
    'phone': ['13800138000'],
    'nickname': ['测试用户'],
    'avatar': [''],
    'create_time': ['2023-01-01 00:00:00'],
    'last_login_time': ['2023-01-01 00:00:00'],
    'is_verified': [False],
    'roles': [['user']],
    'points': [0],
    'balance': [0]
}

users_df = pd.DataFrame(users_data)
users_df.to_excel('users.xlsx', index=False)

# Create sms_codes.xlsx
sms_codes_data = {
    '_id': [''],
    'phone': ['13800138000'],
    'code': ['123456'],
    'create_time': ['2023-01-01 00:00:00'],
    'expire_time': ['2023-01-01 01:00:00'],
    'used': [False]
}

sms_codes_df = pd.DataFrame(sms_codes_data)
sms_codes_df.to_excel('sms_codes.xlsx', index=False)

print("Excel files have been created successfully.") 