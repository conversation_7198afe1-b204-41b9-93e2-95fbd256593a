# 环境变量
NODE_ENV=development

# 服务器配置
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=yizhanbang
DB_DIALECT=mysql
SYNC_DB=true

# JWT配置
JWT_SECRET=yizhanbang-jwt-secret-key
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880  # 5MB

# 短信服务配置
SMS_API_KEY=your_sms_api_key
SMS_TEMPLATE_ID=your_sms_template_id

# 微信小程序配置
WECHAT_APPID=your_appid_here
WECHAT_SECRET=your_secret_here

# 应用配置
LOG_LEVEL=info
UPLOAD_PATH=./uploads
STATIC_PATH=./static

# 微信小程序配置
WX_APP_ID=wxf20fc3e7fc1f8f5e
WX_APP_SECRET=30d9c9ed24b2f1871ac44609a1d294cc
WX_MCH_ID=
WX_MCH_KEY=
WX_NOTIFY_URL=

# 支付配置
WXPAY_ENABLED=false
WXPAY_SANDBOX=true

# Redis配置（可选）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 短信服务配置（可选）
SMS_ENABLED=false
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY_ID=
SMS_ACCESS_KEY_SECRET=
SMS_SIGN_NAME=驿站帮
SMS_TEMPLATE_CODE=

# 文件存储配置
STORAGE_TYPE=local
STORAGE_DOMAIN=

# 阿里云OSS配置（如果STORAGE_TYPE=oss）
OSS_REGION=
OSS_ACCESS_KEY_ID=
OSS_ACCESS_KEY_SECRET=
OSS_BUCKET=

# 腾讯云COS配置（如果STORAGE_TYPE=cos）
COS_REGION=
COS_SECRET_ID=
COS_SECRET_KEY=
COS_BUCKET=

# MongoDB配置（仅用于数据迁移）
MONGO_URI=mongodb://localhost:27017/yizhanbang 