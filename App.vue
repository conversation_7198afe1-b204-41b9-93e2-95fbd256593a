<script>
	import appFix from './app-fix' // 导入错误修复模块
	
	export default {
		onLaunch: function() {
			console.log('App Launch')
			// 检查登录状态
			this.checkLoginStatus()
			// 再次确保应用错误修复
			appFix.fixToLocaleLowerCaseError()
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			// 检查登录状态
			async checkLoginStatus() {
				// 检查是否有token
				const token = uni.getStorageSync('token')
				if (!token) {
					console.log('未登录状态')
					return
				}
				
				try {
					// 可以添加token验证逻辑，如调用验证token的接口
					// 这里简化处理，假设有token就是已登录
					console.log('已登录状态')
				} catch (error) {
					console.error('登录状态检查失败', error)
					// 登录失效，清除token
					uni.removeStorageSync('token')
					uni.removeStorageSync('userInfo')
				}
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	page {
		background-color: #f5f5f5;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 
						Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 
						'Microsoft Yahei', sans-serif;
	}
</style>
