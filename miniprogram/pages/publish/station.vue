<script>
// 引入相关API
import { publishStation } from '@/api/station';

export default {
  // 保留现有代码...
  
  methods: {
    // 保留其他方法...
    
    // 修改发布方法
    async publish() {
      // 表单验证
      if (!this.stationName) {
        uni.showToast({
          title: '请输入驿站名称',
          icon: 'none'
        });
        return;
      }
      
      if (!this.price) {
        uni.showToast({
          title: '请输入转让价格',
          icon: 'none'
        });
        return;
      }
      
      if (this.stationType === -1) {
        uni.showToast({
          title: '请选择驿站类型',
          icon: 'none'
        });
        return;
      }
      
      if (this.selectedArea === -1) {
        uni.showToast({
          title: '请选择所在区域',
          icon: 'none'
        });
        return;
      }
      
      if (!this.address) {
        uni.showToast({
          title: '请输入详细地址',
          icon: 'none'
        });
        return;
      }
      
      if (!this.contactName) {
        uni.showToast({
          title: '请输入联系人姓名',
          icon: 'none'
        });
        return;
      }
      
      if (!this.contactPhone) {
        uni.showToast({
          title: '请输入联系电话',
          icon: 'none'
        });
        return;
      }
      
      if (this.previewImages.length === 0) {
        uni.showToast({
          title: '请上传驿站照片',
          icon: 'none'
        });
        return;
      }
      
      try {
        uni.showLoading({ title: '提交中...' });
        
        // 先上传图片
        const imageUrls = await this.uploadImages();
        
        // 提交表单数据
        const stationData = {
          title: this.stationName,
          price: parseFloat(this.price),
          type: this.stationType + 1, // 转换为后端类型值
          area: this.areas[this.selectedArea],
          address: this.address,
          space: parseFloat(this.area) || 0,
          monthlyIncome: parseFloat(this.monthlyIncome) || 0,
          dailyPackage: parseInt(this.dailyPackages) || 0,
          advantage: this.advantage,
          reason: this.reason,
          images: imageUrls,
          contactName: this.contactName,
          contactPhone: this.contactPhone,
          wechatId: this.wechatId
        };
        
        const result = await publishStation(stationData);
        
        uni.hideLoading();
        if (result.code === 0) {
          uni.showToast({
            title: '发布成功，等待审核',
            icon: 'success'
          });
          
          setTimeout(() => {
            uni.redirectTo({
              url: '/pages/my/published'
            });
          }, 1500);
        } else {
          uni.showToast({
            title: result.message || '发布失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('发布失败:', error);
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      }
    },
    
    // 上传图片方法
    async uploadImages() {
      if (this.previewImages.length === 0) {
        return [];
      }
      
      const promises = this.previewImages.map(image => {
        return new Promise((resolve, reject) => {
          uni.uploadFile({
            url: this.$baseUrl + '/upload',
            filePath: image,
            name: 'file',
            header: {
              Authorization: 'Bearer ' + uni.getStorageSync('token')
            },
            success: (res) => {
              if (res.statusCode === 200) {
                const data = JSON.parse(res.data);
                if (data.code === 0) {
                  resolve(data.data.url);
                } else {
                  reject(new Error(data.message || '上传失败'));
                }
              } else {
                reject(new Error('上传失败'));
              }
            },
            fail: (err) => {
              reject(err);
            }
          });
        });
      });
      
      try {
        return await Promise.all(promises);
      } catch (error) {
        console.error('上传图片失败:', error);
        throw new Error('上传图片失败');
      }
    }
    
    // 保留其他方法...
  }
}
</script> 