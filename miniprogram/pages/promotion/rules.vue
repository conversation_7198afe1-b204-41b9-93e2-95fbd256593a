<template>
  <div class="rules-page">
    <!-- 标题栏 -->
    <div class="page-header">
      <div class="back-btn" @click="$router.go(-1)">
        <i class="el-icon-arrow-left"></i>
      </div>
      <div class="title">推广规则</div>
      <div class="right-btn"></div>
    </div>
    
    <!-- 规则卡片 -->
    <div class="rule-card">
      <div class="card-title">推广员等级</div>
      <div class="level-table">
        <table>
          <thead>
            <tr>
              <th>等级</th>
              <th>一级佣金比例</th>
              <th>二级佣金比例</th>
              <th>升级条件</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(rule, index) in rules" :key="index">
              <td>{{rule.name}}</td>
              <td>{{rule.first_level_rate}}%</td>
              <td>{{rule.second_level_rate}}%</td>
              <td>{{rule.requirements}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <div class="rule-card">
      <div class="card-title">推广说明</div>
      <div class="rule-content">
        <div class="rule-item">
          <div class="item-title">1. 什么是推广员？</div>
          <div class="item-content">
            <p>推广员是指通过分享平台商品、服务等获得佣金的用户。任何用户都可以申请成为推广员，经平台审核通过后，获得专属推广码和海报，可以邀请他人注册使用平台服务。</p>
          </div>
        </div>
        <div class="rule-item">
          <div class="item-title">2. 如何赚取佣金？</div>
          <div class="item-content">
            <p>当您成功邀请的用户（一级推广）在平台下单并完成支付后，您将获得订单金额一定比例的佣金；当您邀请的用户再邀请他人（二级推广）在平台消费，您也将获得相应比例的佣金。</p>
          </div>
        </div>
        <div class="rule-item">
          <div class="item-title">3. 佣金如何结算？</div>
          <div class="item-content">
            <p>佣金将在订单完成后自动结算到您的账户余额中，可随时申请提现。提现金额需满足平台设定的最低提现额度（目前为{{minWithdraw}}元）。</p>
          </div>
        </div>
        <div class="rule-item">
          <div class="item-title">4. 提现规则</div>
          <div class="item-content">
            <p>提现申请将在1-3个工作日内审核处理，审核通过后资金将在24小时内到账。提现方式包括微信、支付宝和银行卡转账，请确保填写的账户信息准确无误。</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="rule-card">
      <div class="card-title">佣金计算示例</div>
      <div class="rule-content">
        <div class="example-item">
          <div class="example-title">一级推广佣金计算：</div>
          <div class="example-content">
            <p>假设您是初级推广员（一级佣金比例为5%），您邀请的用户A购买了100元的商品，您将获得：</p>
            <p class="formula">100元 × 5% = 5元佣金</p>
          </div>
        </div>
        <div class="example-item">
          <div class="example-title">二级推广佣金计算：</div>
          <div class="example-content">
            <p>假设您是初级推广员（二级佣金比例为1%），您邀请的用户A又邀请了用户B，用户B购买了200元的商品，您将获得：</p>
            <p class="formula">200元 × 1% = 2元佣金</p>
          </div>
        </div>
      </div>
    </div>
    
    <div class="rule-card">
      <div class="card-title">注意事项</div>
      <div class="rule-content">
        <ul class="notice-list">
          <li>推广员有效期为1年，到期后需重新申请。</li>
          <li>推广员不得以任何形式进行虚假宣传，一经发现将取消推广员资格。</li>
          <li>平台有权根据市场情况调整佣金比例，调整前将提前通知推广员。</li>
          <li>推广员不得恶意刷单或进行其他违规操作，否则将被取消推广资格并冻结账户。</li>
          <li>最终解释权归平台所有。</li>
        </ul>
      </div>
    </div>
    
    <!-- 申请按钮 -->
    <div class="apply-btn-wrap" v-if="!isPromoter && applicationStatus !== 0">
      <el-button type="primary" class="apply-btn" @click="showApplyDialog">立即申请成为推广员</el-button>
    </div>
    
    <!-- 推广员申请弹窗 -->
    <el-dialog
      title="申请成为推广员"
      :visible.sync="applyDialogVisible"
      width="90%"
      :before-close="handleCloseDialog">
      <el-form :model="applyForm" :rules="applyRules" ref="applyForm" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="applyForm.name" placeholder="请输入真实姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="applyForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="申请理由" prop="reason">
          <el-input type="textarea" v-model="applyForm.reason" placeholder="请简述申请理由"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="applyDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitApplication" :loading="applyLoading">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getCommissionRules, applyPromoter, getApplicationStatus } from '@/api/promotion';
import { mapGetters } from 'vuex';

export default {
  name: 'PromotionRulesPage',
  data() {
    return {
      rules: [],
      loading: false,
      isPromoter: false,
      applicationStatus: -1, // -1:未申请, 0:审核中, 1:已通过, 2:已拒绝
      minWithdraw: 100,
      
      // 申请相关
      applyDialogVisible: false,
      applyLoading: false,
      applyForm: {
        name: '',
        phone: '',
        reason: ''
      },
      applyRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(['userId', 'userInfo'])
  },
  created() {
    this.loadRules();
    this.checkStatus();
    this.getMinWithdraw();
  },
  methods: {
    async loadRules() {
      this.loading = true;
      try {
        const res = await getCommissionRules();
        if (res.code === 200) {
          this.rules = res.data || [];
        }
      } catch (error) {
        console.error('加载佣金规则失败', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    
    async checkStatus() {
      if (!this.userId) return;
      
      try {
        // 检查是否已是推广员
        const userRes = await this.$http.get('/api/users/' + this.userId);
        if (userRes.data && userRes.data.code === 200) {
          this.isPromoter = userRes.data.data.is_promoter === 1;
        }
        
        // 如果不是推广员，检查申请状态
        if (!this.isPromoter) {
          const res = await getApplicationStatus({ userId: this.userId });
          if (res.code === 200) {
            this.applicationStatus = res.data.status;
          }
        }
      } catch (error) {
        console.error('检查状态失败', error);
      }
    },
    
    async getMinWithdraw() {
      try {
        const res = await this.$http.get('/api/system/config', { 
          params: { key: 'withdraw_min' } 
        });
        if (res.data && res.data.code === 200) {
          this.minWithdraw = Number(res.data.data.value) || 100;
        }
      } catch (error) {
        console.error('获取最低提现金额失败', error);
      }
    },
    
    showApplyDialog() {
      if (!this.userId) {
        this.$message.warning('请先登录');
        this.$router.push('/pages/user/login');
        return;
      }
      
      if (this.isPromoter) {
        this.$message.info('您已经是推广员');
        return;
      }
      
      if (this.applicationStatus === 0) {
        this.$message.info('您的申请正在审核中');
        return;
      }
      
      this.applyForm.name = this.userInfo.real_name || '';
      this.applyForm.phone = this.userInfo.phone || '';
      this.applyDialogVisible = true;
    },
    
    handleCloseDialog(done) {
      done();
    },
    
    async submitApplication() {
      if (!this.userId) {
        this.$message.warning('请先登录');
        return;
      }
      
      this.$refs.applyForm.validate(async (valid) => {
        if (valid) {
          this.applyLoading = true;
          try {
            const res = await applyPromoter({
              userId: this.userId,
              name: this.applyForm.name,
              phone: this.applyForm.phone,
              reason: this.applyForm.reason
            });
            
            if (res.code === 201) {
              this.$message.success('申请提交成功，请等待审核');
              this.applyDialogVisible = false;
              this.applicationStatus = 0;
            } else {
              this.$message.error(res.message || '申请失败');
            }
          } catch (error) {
            console.error('提交申请失败', error);
            this.$message.error('提交申请失败');
          } finally {
            this.applyLoading = false;
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.rules-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 80px;
  
  .page-header {
    position: sticky;
    top: 0;
    z-index: 10;
    height: 44px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    
    .back-btn, .right-btn {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .title {
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .rule-card {
    margin: 15px;
    padding: 15px;
    background: #fff;
    border-radius: 10px;
    
    .card-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 15px;
      position: relative;
      padding-left: 10px;
      
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: #ff6b3d;
        border-radius: 2px;
      }
    }
    
    .level-table {
      overflow-x: auto;
      
      table {
        width: 100%;
        border-collapse: collapse;
        
        th, td {
          border: 1px solid #ebeef5;
          padding: 10px;
          text-align: center;
          font-size: 14px;
        }
        
        th {
          background-color: #f7f7f7;
          color: #606266;
          font-weight: 500;
        }
        
        tr:nth-child(even) {
          background-color: #fafafa;
        }
      }
    }
    
    .rule-content {
      color: #606266;
      
      .rule-item {
        margin-bottom: 15px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .item-title {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 8px;
          color: #303133;
        }
        
        .item-content {
          font-size: 13px;
          line-height: 1.6;
          color: #606266;
          padding-left: 5px;
          
          p {
            margin: 0;
            margin-bottom: 5px;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
      
      .example-item {
        margin-bottom: 15px;
        background-color: #f9f9f9;
        padding: 10px;
        border-radius: 5px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .example-title {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 8px;
          color: #303133;
        }
        
        .example-content {
          font-size: 13px;
          line-height: 1.6;
          color: #606266;
          
          p {
            margin: 0;
            margin-bottom: 5px;
            
            &:last-child {
              margin-bottom: 0;
            }
          }
          
          .formula {
            font-weight: bold;
            color: #ff6b3d;
            text-align: center;
            margin: 8px 0;
          }
        }
      }
      
      .notice-list {
        margin: 0;
        padding-left: 20px;
        
        li {
          font-size: 13px;
          line-height: 1.8;
          color: #606266;
        }
      }
    }
  }
  
  .apply-btn-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px;
    background: #fff;
    box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.05);
    z-index: 10;
    
    .apply-btn {
      width: 100%;
      height: 44px;
      background: linear-gradient(135deg, #ff7134, #ff375f);
      border: none;
      
      &:hover, &:focus {
        background: linear-gradient(135deg, #ff5722, #ff2d54);
        border: none;
      }
    }
  }
}
</style> 