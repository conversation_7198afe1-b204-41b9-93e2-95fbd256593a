<template>
  <div class="commission-page">
    <!-- 标题栏 -->
    <div class="page-header">
      <div class="back-btn" @click="$router.go(-1)">
        <i class="el-icon-arrow-left"></i>
      </div>
      <div class="title">佣金明细</div>
      <div class="right-btn"></div>
    </div>
    
    <!-- 统计信息 -->
    <div class="stats-card">
      <div class="stats-item">
        <div class="amount">{{totalCommission}}元</div>
        <div class="label">累计佣金</div>
      </div>
      <div class="stats-item">
        <div class="amount">{{yesterdayCommission}}元</div>
        <div class="label">昨日收益</div>
      </div>
      <div class="stats-item">
        <div class="amount">{{totalWithdrawal}}元</div>
        <div class="label">已提现金额</div>
      </div>
    </div>
    
    <!-- 佣金列表 -->
    <div class="list-wrapper">
      <div class="list-title">收入明细</div>
      <div class="empty-tip" v-if="list.length === 0 && !loading">
        <img src="/static/images/empty-data.png" class="empty-img">
        <div class="empty-text">暂无佣金记录</div>
      </div>
      <div class="commission-list" v-else>
        <div class="commission-item" v-for="(item, index) in list" :key="index">
          <div class="left">
            <div class="source">{{item.remark || '订单佣金'}}</div>
            <div class="time">{{item.created_at}}</div>
          </div>
          <div class="amount" :class="{'income': item.amount > 0, 'expense': item.amount < 0}">
            {{item.amount > 0 ? '+' : ''}}{{item.amount}}
          </div>
        </div>
      </div>
      
      <!-- 加载更多 -->
      <div class="load-more" v-if="list.length > 0 && hasMore">
        <el-button type="text" @click="loadMore" :loading="loading">加载更多</el-button>
      </div>
      <div class="no-more" v-if="list.length > 0 && !hasMore">
        没有更多数据了
      </div>
    </div>
  </div>
</template>

<script>
import { getCommissionRecords } from '@/api/promotion';
import { mapGetters } from 'vuex';

export default {
  name: 'CommissionPage',
  data() {
    return {
      loading: false,
      page: 1,
      pageSize: 10,
      total: 0,
      list: [],
      hasMore: false,
      totalCommission: '0.00',
      yesterdayCommission: '0.00',
      totalWithdrawal: '0.00'
    };
  },
  computed: {
    ...mapGetters(['userId', 'userInfo'])
  },
  created() {
    this.getStatistics();
    this.loadData();
  },
  methods: {
    async getStatistics() {
      try {
        const res = await this.$http.get('/api/promotion/statistics', {
          params: { userId: this.userId }
        });
        
        if (res.data && res.data.code === 200) {
          this.totalCommission = res.data.data.totalCommission || '0.00';
          this.yesterdayCommission = res.data.data.yesterdayCommission || '0.00';
          this.totalWithdrawal = res.data.data.totalWithdrawal || '0.00';
        }
      } catch (error) {
        console.error('获取统计数据失败', error);
      }
    },
    
    async loadData(append = false) {
      if (!this.userId) {
        this.$message.warning('请先登录');
        return;
      }
      
      this.loading = true;
      
      try {
        const res = await getCommissionRecords({
          userId: this.userId,
          page: this.page,
          pageSize: this.pageSize
        });
        
        if (res.code === 200) {
          const { list, pagination } = res.data;
          
          if (append) {
            this.list = [...this.list, ...list];
          } else {
            this.list = list;
          }
          
          this.total = pagination.total;
          this.hasMore = this.list.length < this.total;
        }
      } catch (error) {
        console.error('加载佣金明细失败', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    
    loadMore() {
      if (this.loading || !this.hasMore) return;
      
      this.page += 1;
      this.loadData(true);
    }
  }
};
</script>

<style lang="scss" scoped>
.commission-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  .page-header {
    position: sticky;
    top: 0;
    z-index: 10;
    height: 44px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    
    .back-btn, .right-btn {
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .title {
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .stats-card {
    margin: 15px;
    padding: 15px;
    background: linear-gradient(135deg, #ff7134, #ff375f);
    border-radius: 10px;
    display: flex;
    justify-content: space-between;
    color: #fff;
    
    .stats-item {
      flex: 1;
      text-align: center;
      
      .amount {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .label {
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }
  
  .list-wrapper {
    margin: 15px;
    padding: 15px;
    background: #fff;
    border-radius: 10px;
    
    .list-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 15px;
    }
    
    .empty-tip {
      padding: 30px 0;
      text-align: center;
      
      .empty-img {
        width: 100px;
        height: 100px;
        opacity: 0.5;
      }
      
      .empty-text {
        font-size: 14px;
        color: #999;
        margin-top: 10px;
      }
    }
    
    .commission-list {
      .commission-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .left {
          .source {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
          }
          
          .time {
            font-size: 12px;
            color: #999;
          }
        }
        
        .amount {
          font-size: 16px;
          font-weight: 500;
          
          &.income {
            color: #f56c6c;
          }
          
          &.expense {
            color: #409eff;
          }
        }
      }
    }
    
    .load-more, .no-more {
      text-align: center;
      padding: 15px 0 5px;
      font-size: 12px;
      color: #999;
    }
  }
}
</style> 