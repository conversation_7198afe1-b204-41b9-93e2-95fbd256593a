<template>
  <div class="promotion-page">
    <!-- 头部信息 -->
    <div class="header-section">
      <div class="user-info">
        <img :src="userInfo.avatar || '/static/images/default-avatar.png'" class="avatar" />
        <div class="user-detail">
          <div class="nickname">{{userInfo.nickname || '未登录'}}</div>
          <div class="promoter-level" v-if="isPromoter">{{promoterLevelText}}</div>
          <div class="promoter-status" v-else-if="applicationStatus === 0">审核中</div>
          <div class="promoter-apply" v-else @click="showApplyDialog">申请成为推广员</div>
        </div>
      </div>
      <div class="wallet-info" v-if="isPromoter">
        <div class="balance">
          <div class="amount">{{walletInfo.balance || '0.00'}}</div>
          <div class="label">可提现余额(元)</div>
        </div>
        <div class="withdraw-btn" @click="showWithdrawDialog">提现</div>
      </div>
    </div>

    <!-- 收益统计 -->
    <div class="stats-section" v-if="isPromoter">
      <div class="stats-card">
        <div class="stats-item">
          <div class="amount">{{stats.totalCommission || '0.00'}}</div>
          <div class="label">累计佣金(元)</div>
        </div>
        <div class="stats-item">
          <div class="amount">{{stats.yesterdayCommission || '0.00'}}</div>
          <div class="label">昨日收益(元)</div>
        </div>
        <div class="stats-item">
          <div class="amount">{{stats.totalInvites || '0'}}</div>
          <div class="label">推广用户数</div>
        </div>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <div class="menu-title">推广中心</div>
      <div class="menu-grid">
        <div class="menu-item" @click="goToPage('/pages/promotion/poster')">
          <div class="icon poster-icon"></div>
          <div class="text">推广海报</div>
        </div>
        <div class="menu-item" @click="goToPage('/pages/promotion/code')">
          <div class="icon code-icon"></div>
          <div class="text">我的推广码</div>
        </div>
        <div class="menu-item" @click="goToPage('/pages/promotion/invites')">
          <div class="icon invites-icon"></div>
          <div class="text">推广用户</div>
        </div>
        <div class="menu-item" @click="goToPage('/pages/promotion/commission')">
          <div class="icon commission-icon"></div>
          <div class="text">佣金明细</div>
        </div>
        <div class="menu-item" @click="goToPage('/pages/promotion/withdraw')">
          <div class="icon withdraw-icon"></div>
          <div class="text">提现记录</div>
        </div>
        <div class="menu-item" @click="goToPage('/pages/promotion/rules')">
          <div class="icon rules-icon"></div>
          <div class="text">推广规则</div>
        </div>
      </div>
    </div>

    <!-- 推广攻略 -->
    <div class="guide-section">
      <div class="guide-title">推广攻略</div>
      <div class="guide-content">
        <div class="guide-item">
          <div class="step">1</div>
          <div class="info">
            <div class="title">申请成为推广员</div>
            <div class="desc">通过实名认证，完成推广员申请</div>
          </div>
        </div>
        <div class="guide-item">
          <div class="step">2</div>
          <div class="info">
            <div class="title">生成专属推广码</div>
            <div class="desc">获取专属推广码或推广海报</div>
          </div>
        </div>
        <div class="guide-item">
          <div class="step">3</div>
          <div class="info">
            <div class="title">分享给好友</div>
            <div class="desc">通过微信分享给好友扫码注册</div>
          </div>
        </div>
        <div class="guide-item">
          <div class="step">4</div>
          <div class="info">
            <div class="title">获得推广佣金</div>
            <div class="desc">好友下单后获得相应佣金</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 推广员申请弹窗 -->
    <el-dialog
      title="申请成为推广员"
      :visible.sync="applyDialogVisible"
      width="90%"
      :before-close="handleCloseDialog">
      <el-form :model="applyForm" :rules="applyRules" ref="applyForm" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="applyForm.name" placeholder="请输入真实姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="applyForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="申请理由" prop="reason">
          <el-input type="textarea" v-model="applyForm.reason" placeholder="请简述申请理由"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="applyDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitApplication" :loading="applyLoading">提 交</el-button>
      </span>
    </el-dialog>

    <!-- 提现弱复窗 -->
    <el-dialog
      title="申请提现"
      :visible.sync="withdrawDialogVisible"
      width="90%"
      :before-close="handleCloseDialog">
      <el-form :model="withdrawForm" :rules="withdrawRules" ref="withdrawForm" label-width="80px">
        <el-form-item label="提现金额" prop="amount">
          <el-input v-model="withdrawForm.amount" type="number">
            <template slot="append">元</template>
          </el-input>
          <div class="form-tips">可提现余额: {{walletInfo.balance || '0.00'}}元，最低提现金额{{minWithdraw}}元</div>
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="withdrawForm.realName" placeholder="请输入真实姓名"></el-input>
        </el-form-item>
        <el-form-item label="账户类型" prop="accountType">
          <el-select v-model="withdrawForm.accountType" placeholder="请选择账户类型" style="width: 100%;">
            <el-option label="微信" :value="1"></el-option>
            <el-option label="支付宝" :value="2"></el-option>
            <el-option label="银行卡" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账号" prop="accountNumber">
          <el-input v-model="withdrawForm.accountNumber" placeholder="请输入账号"></el-input>
        </el-form-item>
        <el-form-item label="开户行" prop="bankName" v-if="withdrawForm.accountType === 3">
          <el-input v-model="withdrawForm.bankName" placeholder="请输入开户行"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="withdrawDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitWithdraw" :loading="withdrawLoading">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getPromotionInfo, applyPromoter, withdraw } from '@/api/promotion';
import { mapGetters } from 'vuex';

export default {
  name: 'PromotionPage',
  data() {
    // 验证提现金额
    const validateAmount = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入提现金额'));
      }
      if (parseFloat(value) < this.minWithdraw) {
        return callback(new Error(`提现金额不能低于${this.minWithdraw}元`));
      }
      if (parseFloat(value) > parseFloat(this.walletInfo.balance || 0)) {
        return callback(new Error('提现金额不能大于可提现余额'));
      }
      callback();
    };
    
    return {
      loading: false,
      isPromoter: false,
      applicationStatus: -1, // -1:未申请, 0:审核中, 1:已通过, 2:已拒绝
      userInfo: {},
      walletInfo: {
        balance: '0.00',
        frozen_balance: '0.00',
        total_income: '0.00'
      },
      stats: {
        totalCommission: '0.00',
        yesterdayCommission: '0.00',
        totalInvites: 0
      },
      promotionCode: '',
      minWithdraw: 100,
      
      // 申请相关
      applyDialogVisible: false,
      applyLoading: false,
      applyForm: {
        name: '',
        phone: '',
        reason: ''
      },
      applyRules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },
      
      // 提现相关
      withdrawDialogVisible: false,
      withdrawLoading: false,
      withdrawForm: {
        amount: '',
        realName: '',
        accountType: 1,
        accountNumber: '',
        bankName: ''
      },
      withdrawRules: {
        amount: [
          { required: true, message: '请输入提现金额', trigger: 'blur' },
          { validator: validateAmount, trigger: 'blur' }
        ],
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        accountType: [
          { required: true, message: '请选择账户类型', trigger: 'change' }
        ],
        accountNumber: [
          { required: true, message: '请输入账号', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(['userId', 'userInfo']),
    promoterLevelText() {
      const levelMap = {
        1: '初级推广员',
        2: '中级推广员', 
        3: '高级推广员',
        4: '资深推广员'
      };
      return levelMap[this.userInfo.user_level] || '推广员';
    }
  },
  created() {
    this.loadData();
  },
  methods: {
    async loadData() {
      if (!this.userId) {
        this.$message.warning('请先登录');
        return;
      }
      
      this.loading = true;
      try {
        const res = await getPromotionInfo({ userId: this.userId });
        if (res.code === 200) {
          const { user, isPromoter, promotionCode, wallet, stats } = res.data;
          this.userInfo = user || {};
          this.isPromoter = isPromoter;
          this.promotionCode = promotionCode;
          this.walletInfo = wallet || this.walletInfo;
          this.stats = stats || this.stats;
          
          // 获取系统配置的最低提现金额
          const configRes = await this.$http.get('/api/system/config', { 
            params: { key: 'withdraw_min' } 
          });
          if (configRes.data && configRes.data.code === 200) {
            this.minWithdraw = Number(configRes.data.data.value) || 100;
          }
          
          // 获取推广员申请状态
          if (!isPromoter) {
            const applicationRes = await this.$http.get('/api/promotion/application/status', { 
              params: { userId: this.userId } 
            });
            if (applicationRes.data && applicationRes.data.code === 200) {
              this.applicationStatus = applicationRes.data.data.status;
            }
          }
        }
      } catch (error) {
        console.error('加载推广数据失败', error);
        this.$message.error('加载数据失败');
      } finally {
        this.loading = false;
      }
    },
    
    goToPage(path) {
      if (!this.userId) {
        this.$message.warning('请先登录');
        this.$router.push('/pages/user/login');
        return;
      }
      
      if (!this.isPromoter && path !== '/pages/promotion/rules') {
        this.$message.warning('请先成为推广员');
        return;
      }
      
      this.$router.push(path);
    },
    
    showApplyDialog() {
      if (!this.userId) {
        this.$message.warning('请先登录');
        this.$router.push('/pages/user/login');
        return;
      }
      
      if (this.isPromoter) {
        this.$message.info('您已经是推广员');
        return;
      }
      
      if (this.applicationStatus === 0) {
        this.$message.info('您的申请正在审核中');
        return;
      }
      
      this.applyForm.name = this.userInfo.real_name || '';
      this.applyForm.phone = this.userInfo.phone || '';
      this.applyDialogVisible = true;
    },
    
    showWithdrawDialog() {
      if (!this.userId) {
        this.$message.warning('请先登录');
        this.$router.push('/pages/user/login');
        return;
      }
      
      if (!this.isPromoter) {
        this.$message.warning('请先成为推广员');
        return;
      }
      
      if (parseFloat(this.walletInfo.balance) < this.minWithdraw) {
        this.$message.warning(`余额不足，最低提现金额${this.minWithdraw}元`);
        return;
      }
      
      this.withdrawForm.realName = this.userInfo.real_name || '';
      this.withdrawDialogVisible = true;
    },
    
    handleCloseDialog(done) {
      done();
    },
    
    async submitApplication() {
      if (!this.userId) {
        this.$message.warning('请先登录');
        return;
      }
      
      this.$refs.applyForm.validate(async (valid) => {
        if (valid) {
          this.applyLoading = true;
          try {
            const res = await applyPromoter({
              userId: this.userId,
              name: this.applyForm.name,
              phone: this.applyForm.phone,
              reason: this.applyForm.reason
            });
            
            if (res.code === 201) {
              this.$message.success('申请提交成功，请等待审核');
              this.applyDialogVisible = false;
              this.applicationStatus = 0;
              this.loadData();
            } else {
              this.$message.error(res.message || '申请失败');
            }
          } catch (error) {
            console.error('提交申请失败', error);
            this.$message.error('提交申请失败');
          } finally {
            this.applyLoading = false;
          }
        }
      });
    },
    
    async submitWithdraw() {
      if (!this.userId) {
        this.$message.warning('请先登录');
        return;
      }
      
      this.$refs.withdrawForm.validate(async (valid) => {
        if (valid) {
          this.withdrawLoading = true;
          try {
            const res = await withdraw({
              userId: this.userId,
              amount: this.withdrawForm.amount,
              realName: this.withdrawForm.realName,
              accountType: this.withdrawForm.accountType,
              accountNumber: this.withdrawForm.accountNumber,
              bankName: this.withdrawForm.accountType === 3 ? this.withdrawForm.bankName : ''
            });
            
            if (res.code === 201) {
              this.$message.success('提现申请提交成功，请等待审核');
              this.withdrawDialogVisible = false;
              this.loadData();
            } else {
              this.$message.error(res.message || '提现申请失败');
            }
          } catch (error) {
            console.error('提交提现申请失败', error);
            this.$message.error('提交提现申请失败');
          } finally {
            this.withdrawLoading = false;
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.promotion-page {
  padding-bottom: 30px;
  background-color: #f5f5f5;
  min-height: 100vh;
  
  .header-section {
    background: linear-gradient(135deg, #ff7134, #ff375f);
    color: #fff;
    padding: 20px 15px;
    border-radius: 0 0 15px 15px;
    
    .user-info {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      
      .avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        border: 2px solid rgba(255, 255, 255, 0.6);
        background-color: #fff;
        object-fit: cover;
      }
      
      .user-detail {
        margin-left: 15px;
        
        .nickname {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        .promoter-level {
          font-size: 12px;
          background: rgba(255, 255, 255, 0.2);
          padding: 2px 8px;
          border-radius: 10px;
          display: inline-block;
        }
        
        .promoter-status {
          font-size: 12px;
          background: rgba(255, 255, 255, 0.2);
          padding: 2px 8px;
          border-radius: 10px;
          display: inline-block;
        }
        
        .promoter-apply {
          font-size: 12px;
          background: rgba(255, 255, 255, 0.2);
          padding: 2px 8px;
          border-radius: 10px;
          display: inline-block;
        }
      }
    }
    
    .wallet-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .balance {
        .amount {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        
        .label {
          font-size: 12px;
          opacity: 0.8;
        }
      }
      
      .withdraw-btn {
        background: rgba(255, 255, 255, 0.2);
        padding: 6px 15px;
        border-radius: 20px;
        font-size: 14px;
      }
    }
  }
  
  .stats-section {
    margin: -20px 15px 15px;
    
    .stats-card {
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      padding: 15px;
      display: flex;
      justify-content: space-between;
      
      .stats-item {
        flex: 1;
        text-align: center;
        
        .amount {
          font-size: 18px;
          font-weight: bold;
          color: #ff5722;
          margin-bottom: 5px;
        }
        
        .label {
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
  
  .menu-section {
    background: #fff;
    border-radius: 10px;
    margin: 15px;
    padding: 15px;
    
    .menu-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    
    .menu-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 15px;
      
      .menu-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10px 0;
        
        .icon {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #f5f7fa;
          margin-bottom: 8px;
          background-position: center;
          background-repeat: no-repeat;
          background-size: 22px;
        }
        
        .poster-icon {
          background-image: url('/static/icons/poster.png');
          background-color: #ffebee;
        }
        
        .code-icon {
          background-image: url('/static/icons/qrcode.png');
          background-color: #e3f2fd;
        }
        
        .invites-icon {
          background-image: url('/static/icons/users.png');
          background-color: #e8f5e9;
        }
        
        .commission-icon {
          background-image: url('/static/icons/wallet.png');
          background-color: #fff3e0;
        }
        
        .withdraw-icon {
          background-image: url('/static/icons/withdraw.png');
          background-color: #ede7f6;
        }
        
        .rules-icon {
          background-image: url('/static/icons/rule.png');
          background-color: #e0f7fa;
        }
        
        .text {
          font-size: 12px;
          color: #333;
        }
      }
    }
  }
  
  .guide-section {
    background: #fff;
    border-radius: 10px;
    margin: 15px;
    padding: 15px;
    
    .guide-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 15px;
      color: #333;
    }
    
    .guide-content {
      .guide-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .step {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #ff6b3d;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          margin-right: 10px;
          flex-shrink: 0;
        }
        
        .info {
          .title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 3px;
          }
          
          .desc {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
  
  .form-tips {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
  }
}
</style> 