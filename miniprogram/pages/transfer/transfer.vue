<script>
// 引入API
import { getStationList } from '@/api/station';

export default {
  // 保留现有代码...
  
  methods: {
    // 保留其他方法...
    
    // 修改加载驿站数据方法
    async loadStations() {
      this.loading = true;
      
      try {
        const params = {
          page: this.page,
          pageSize: this.pageSize,
          keyword: this.keyword || '',
          type: this.activeType !== '全部' ? this.getTypeValue(this.activeType) : '',
          area: this.selectedArea !== '全部' ? this.selectedArea : '',
          priceRange: this.selectedPrice !== '全部' ? this.getPriceRange(this.selectedPrice) : '',
          sizeRange: this.selectedSize !== '全部' ? this.getSizeRange(this.selectedSize) : ''
        };
        
        const res = await getStationList(params);
        
        if (res.code === 0) {
          // 第一页数据直接赋值，加载更多时追加
          if (this.page === 1) {
            this.stationList = res.data.list || [];
          } else {
            this.stationList = [...this.stationList, ...(res.data.list || [])];
          }
          this.hasMore = this.stationList.length < res.data.total;
          this.total = res.data.total;
        } else {
          uni.showToast({
            title: res.message || '加载失败',
            icon: 'none'
          });
        }
      } catch (e) {
        console.error('加载驿站数据失败', e);
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        // 结束下拉刷新状态
        uni.stopPullDownRefresh();
      }
    },
    
    // 获取类型对应的值
    getTypeValue(typeName) {
      const typeMap = {
        '社区驿站': 1,
        '校园驿站': 2,
        '企业驿站': 3,
        '商业驿站': 4
      };
      return typeMap[typeName] || '';
    },
    
    // 获取价格范围
    getPriceRange(priceText) {
      const rangeMap = {
        '1万以下': '0-10000',
        '1-3万': '10000-30000',
        '3-5万': '30000-50000',
        '5-10万': '50000-100000',
        '10万以上': '100000-'
      };
      return rangeMap[priceText] || '';
    },
    
    // 获取面积范围
    getSizeRange(sizeText) {
      const rangeMap = {
        '10㎡以下': '0-10',
        '10-30㎡': '10-30',
        '30-50㎡': '30-50',
        '50-100㎡': '50-100',
        '100㎡以上': '100-'
      };
      return rangeMap[sizeText] || '';
    }
    
    // 保留其他方法...
  }
}
</script> 