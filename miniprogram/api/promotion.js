import request from '@/utils/request';

/**
 * 获取推广员信息
 * @param {Object} params 查询参数
 * @param {String|Number} params.userId 用户ID
 */
export function getPromotionInfo(params) {
  return request({
    url: '/api/promotion/info',
    method: 'get',
    params
  });
}

/**
 * 申请成为推广员
 * @param {Object} data 申请数据
 * @param {String|Number} data.userId 用户ID
 * @param {String} data.name 姓名
 * @param {String} data.phone 手机号
 * @param {String} data.reason 申请理由
 */
export function applyPromoter(data) {
  return request({
    url: '/api/promotion/apply',
    method: 'post',
    data
  });
}

/**
 * 获取推广佣金规则
 */
export function getCommissionRules() {
  return request({
    url: '/api/promotion/rules',
    method: 'get'
  });
}

/**
 * 获取推广佣金记录
 * @param {Object} params 查询参数
 * @param {String|Number} params.userId 用户ID
 * @param {Number} params.page 页码
 * @param {Number} params.pageSize 每页数量
 */
export function getCommissionRecords(params) {
  return request({
    url: '/api/promotion/commissions',
    method: 'get',
    params
  });
}

/**
 * 获取推广用户列表
 * @param {Object} params 查询参数
 * @param {String|Number} params.userId 用户ID
 * @param {Number} params.page 页码
 * @param {Number} params.pageSize 每页数量
 * @param {Number} params.level 推广层级：1-一级推广，2-二级推广
 */
export function getInvitesList(params) {
  return request({
    url: '/api/promotion/invites',
    method: 'get',
    params
  });
}

/**
 * 申请提现
 * @param {Object} data 提现数据
 * @param {String|Number} data.userId 用户ID
 * @param {String|Number} data.amount 提现金额
 * @param {String} data.realName 真实姓名
 * @param {Number} data.accountType 账户类型：1-微信，2-支付宝，3-银行卡
 * @param {String} data.accountNumber 账号
 * @param {String} data.bankName 开户行(仅银行卡时需要)
 */
export function withdraw(data) {
  return request({
    url: '/api/promotion/withdraw',
    method: 'post',
    data
  });
}

/**
 * 获取提现记录
 * @param {Object} params 查询参数
 * @param {String|Number} params.userId 用户ID
 * @param {Number} params.page 页码
 * @param {Number} params.pageSize 每页数量
 */
export function getWithdrawalRecords(params) {
  return request({
    url: '/api/promotion/withdrawals',
    method: 'get',
    params
  });
}

/**
 * 生成推广二维码
 * @param {Object} params 查询参数
 * @param {String|Number} params.userId 用户ID
 */
export function generateQrcode(params) {
  return request({
    url: '/api/promotion/qrcode',
    method: 'get',
    params
  });
}

/**
 * 生成推广海报
 * @param {Object} params 查询参数
 * @param {String|Number} params.userId 用户ID
 * @param {Number} params.type 海报类型：1-普通海报，2-商品海报
 * @param {String|Number} params.productId 商品ID(type=2时需要)
 */
export function generatePoster(params) {
  return request({
    url: '/api/promotion/poster',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

/**
 * 获取推广员申请状态
 * @param {Object} params 查询参数
 * @param {String|Number} params.userId 用户ID
 */
export function getApplicationStatus(params) {
  return request({
    url: '/api/promotion/application/status',
    method: 'get',
    params
  });
}

export default {
  getPromotionInfo,
  applyPromoter,
  getCommissionRules,
  getCommissionRecords,
  getInvitesList,
  withdraw,
  getWithdrawalRecords,
  generateQrcode,
  generatePoster,
  getApplicationStatus
};