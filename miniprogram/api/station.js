import request from '@/utils/request'

/**
 * 获取驿站列表（已审核通过）
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getStationList(params) {
  return request({
    url: '/stations',
    method: 'GET',
    data: params
  })
}

/**
 * 获取驿站详情
 * @param {Number} id - 驿站ID
 * @returns {Promise}
 */
export function getStationDetail(id) {
  return request({
    url: `/stations/${id}`,
    method: 'GET'
  })
}

/**
 * 发布驿站信息
 * @param {Object} data - 驿站数据
 * @returns {Promise}
 */
export function publishStation(data) {
  return request({
    url: '/stations',
    method: 'POST',
    data
  })
}

/**
 * 获取用户发布的驿站列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getUserStations(params) {
  return request({
    url: '/user/stations',
    method: 'GET',
    data: params
  })
} 