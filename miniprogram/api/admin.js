/**
 * 后台管理系统API调用接口
 * 用于在小程序中调用后台管理系统的API
 */
import request from '@/utils/request'
import { API_VERSION } from '@/api/config'

/**
 * 身份认证相关API
 */
export const identityApi = {
  /**
   * 提交身份认证申请
   * @param {Object} data - 身份认证数据
   * @returns {Promise}
   */
  submitVerification(data) {
    return request.post(`/api/${API_VERSION}/admin/identity/submit`, data);
  },
  
  /**
   * 获取身份认证状态
   * @param {String} userId - 用户ID
   * @returns {Promise}
   */
  getVerificationStatus(userId) {
    return request.get(`/api/${API_VERSION}/admin/identity/status`, { userId });
  },
  
  /**
   * 获取身份认证详情
   * @param {String} id - 认证ID
   * @returns {Promise}
   */
  getVerificationDetail(id) {
    return request.get(`/api/${API_VERSION}/admin/identity/detail`, { id });
  }
};

/**
 * 驿站管理相关API
 */
export const stationApi = {
  /**
   * 提交驿站申请
   * @param {Object} data - 驿站数据
   * @returns {Promise}
   */
  submitStation(data) {
    return request.post(`/api/${API_VERSION}/admin/station/submit`, data);
  },
  
  /**
   * 获取驿站审核状态
   * @param {String} id - 驿站ID
   * @returns {Promise}
   */
  getStationStatus(id) {
    return request.get(`/api/${API_VERSION}/admin/station/status`, { id });
  },
  
  /**
   * 获取驿站详情
   * @param {String} id - 驿站ID
   * @returns {Promise}
   */
  getStationDetail(id) {
    return request.get(`/api/${API_VERSION}/admin/station/detail`, { id });
  },
  
  /**
   * 获取驿站列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getStationList(params) {
    return request.get(`/api/${API_VERSION}/admin/station/list`, params);
  }
};

/**
 * 推广系统相关API
 */
export const promotionApi = {
  /**
   * 获取推广佣金统计
   * @param {String} userId - 用户ID
   * @returns {Promise}
   */
  getCommissionStats(userId) {
    return request.get(`/api/${API_VERSION}/admin/promotion/commission/stats`, { userId });
  },
  
  /**
   * 获取推广佣金明细
   * @param {String} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getCommissionRecords(userId, params = {}) {
    return request.get(`/api/${API_VERSION}/admin/promotion/commission/records`, { userId, ...params });
  },
  
  /**
   * 提交提现申请
   * @param {Object} data - 提现数据
   * @returns {Promise}
   */
  submitWithdrawal(data) {
    return request.post(`/api/${API_VERSION}/admin/promotion/withdrawal/submit`, data);
  },
  
  /**
   * 获取提现记录
   * @param {String} userId - 用户ID
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getWithdrawalRecords(userId, params = {}) {
    return request.get(`/api/${API_VERSION}/admin/promotion/withdrawal/records`, { userId, ...params });
  },
  
  /**
   * 获取推广二维码
   * @param {String} userId - 用户ID
   * @param {String} type - 推广类型
   * @returns {Promise}
   */
  getPromotionQrcode(userId, type) {
    return request.get(`/api/${API_VERSION}/admin/promotion/qrcode`, { userId, type });
  }
};

/**
 * 数据同步API
 */
export const syncApi = {
  /**
   * 同步数据到后台
   * @param {String} module - 模块名称
   * @param {String} action - 动作类型
   * @param {Object} data - 数据
   * @returns {Promise}
   */
  syncData(module, action, data) {
    return request.post(`/api/${API_VERSION}/sync/${module}/${action}`, { data, timestamp: Date.now() });
  },
  
  /**
   * 全量同步数据
   * @param {String} module - 模块名称
   * @param {String} lastSync - 上次同步时间
   * @returns {Promise}
   */
  syncAll(module, lastSync) {
    return request.post(`/api/${API_VERSION}/sync/${module}/all`, { lastSync });
  }
};

export default {
  identityApi,
  stationApi,
  promotionApi,
  syncApi
}; 