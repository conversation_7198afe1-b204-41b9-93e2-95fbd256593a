"use strict";
const api_request = require("./request.js");
const api_modules_user = require("./modules/user.js");
const api_modules_station = require("./modules/station.js");
const api_modules_order = require("./modules/order.js");
const api_modules_service = require("./modules/service.js");
const api_modules_job = require("./modules/job.js");
const api_modules_device = require("./modules/device.js");
const api_modules_common = require("./modules/common.js");
const api_modules_product = require("./modules/product.js");
const api_modules_auth = require("./modules/auth.js");
const api = {
  // 请求方法
  request: api_request.request,
  // 各模块API
  user: api_modules_user.userApi,
  station: api_modules_station.stationApi,
  order: api_modules_order.orderApi,
  service: api_modules_service.serviceApi,
  job: api_modules_job.jobApi,
  device: api_modules_device.deviceApi,
  common: api_modules_common.commonApi,
  product: api_modules_product.productApi,
  auth: api_modules_auth.authApi
};
exports.api = api;
