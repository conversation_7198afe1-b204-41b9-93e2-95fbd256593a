"use strict";
const api_request = require("./request.js");
function checkPhoneExists(phone) {
  return api_request.request.get("/api/auth/check-phone", { phone });
}
function sendSmsCode(phone, type = "login") {
  return api_request.request.post("/api/sms/send", { phone, type });
}
function loginByPhone(phone, code) {
  return api_request.request.post("/api/auth/phone-login", { phone, code });
}
function loginByPassword(phone, password) {
  return api_request.request.post("/api/auth/password-login", { phone, password });
}
function register(data) {
  return api_request.request.post("/api/auth/register", data);
}
function resetPassword(phone, code, password) {
  return api_request.request.post("/api/auth/reset-password", { phone, code, password });
}
exports.checkPhoneExists = checkPhoneExists;
exports.loginByPassword = loginByPassword;
exports.loginByPhone = loginByPhone;
exports.register = register;
exports.resetPassword = resetPassword;
exports.sendSmsCode = sendSmsCode;
