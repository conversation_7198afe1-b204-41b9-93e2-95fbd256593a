"use strict";
const api_request = require("../request.js");
const API_PREFIX = "/common";
const commonApi = {
  /**
   * 获取小程序基础配置（联系方式、关于我们等）
   */
  getAppConfig() {
    return api_request.request.get(`${API_PREFIX}/app-config`);
  },
  /**
   * 获取服务协议
   */
  getServiceAgreement() {
    return api_request.request.get(`${API_PREFIX}/service-agreement`);
  },
  /**
   * 获取隐私政策
   */
  getPrivacyPolicy() {
    return api_request.request.get(`${API_PREFIX}/privacy-policy`);
  },
  /**
   * 获取版本信息
   */
  getVersionInfo() {
    return api_request.request.get(`${API_PREFIX}/version`);
  },
  /**
   * 获取消息列表
   * @param {Object} params - 查询参数
   */
  getMessageList(params) {
    return api_request.request.get(`${API_PREFIX}/messages`, params);
  },
  /**
   * 获取消息详情
   * @param {String} id - 消息ID
   */
  getMessageDetail(id) {
    return api_request.request.get(`${API_PREFIX}/messages/${id}`);
  },
  /**
   * 标记消息为已读
   * @param {String} id - 消息ID
   */
  readMessage(id) {
    return api_request.request.put(`${API_PREFIX}/messages/${id}/read`);
  },
  /**
   * 删除消息
   * @param {String} id - 消息ID
   */
  deleteMessage(id) {
    return api_request.request.delete(`${API_PREFIX}/messages/${id}`);
  },
  /**
   * 获取用户位置
   * @param {Object} data - 位置数据
   */
  getUserLocation(data) {
    return api_request.request.post(`${API_PREFIX}/location`, data);
  },
  /**
   * 搜索地址（可对接地图API）
   * @param {Object} params - 搜索参数
   */
  searchAddress(params) {
    return api_request.request.get(`${API_PREFIX}/search-address`, params);
  },
  /**
   * 上传图片
   * @param {String} filePath - 文件路径
   * @param {Object} formData - 额外表单数据
   */
  uploadImage(filePath, formData = {}) {
    return api_request.request.upload(`${API_PREFIX}/upload-image`, filePath, "image", formData);
  },
  /**
   * 上传文件
   * @param {String} filePath - 文件路径
   * @param {Object} formData - 额外表单数据
   */
  uploadFile(filePath, formData = {}) {
    return api_request.request.upload(`${API_PREFIX}/upload-file`, filePath, "file", formData);
  },
  /**
   * 上报用户行为数据（页面访问、停留时间等）
   * @param {Object} data - 行为数据
   */
  reportUserBehavior(data) {
    return api_request.request.post(`${API_PREFIX}/report-behavior`, data);
  },
  /**
   * 上报错误信息
   * @param {Object} data - 错误信息
   */
  reportError(data) {
    return api_request.request.post(`${API_PREFIX}/report-error`, data);
  },
  /**
   * 获取地区列表
   */
  getRegions() {
    return api_request.request.get(`${API_PREFIX}/regions`);
  },
  /**
   * 获取服务分类
   */
  getServiceCategories() {
    return api_request.request.get(`${API_PREFIX}/service-categories`);
  },
  /**
   * 获取支付方式
   */
  getPaymentMethods() {
    return api_request.request.get(`${API_PREFIX}/payment-methods`);
  },
  /**
   * 获取首页数据
   */
  getHomeData() {
    return api_request.request.get(`${API_PREFIX}/home`);
  },
  /**
   * 反馈意见
   * @param {Object} data - 反馈数据
   */
  submitFeedback(data) {
    return api_request.request.post(`${API_PREFIX}/feedback`, data);
  },
  /**
   * 获取首页轮播图
   */
  getBanners() {
    return api_request.request.get(`${API_PREFIX}/banners`);
  },
  /**
   * 获取首页活动公告
   */
  getAnnouncements() {
    return api_request.request.get(`${API_PREFIX}/announcements`);
  },
  /**
   * 获取配置参数
   * @param {Object} params - 参数名称
   */
  getConfig(params) {
    return api_request.request.get(`${API_PREFIX}/config`, params);
  },
  /**
   * 获取应用版本信息
   */
  getAppVersion() {
    return api_request.request.get(`${API_PREFIX}/app-version`);
  },
  /**
   * 获取协议内容
   * @param {Object} params - 协议类型参数
   */
  getAgreement(params) {
    return api_request.request.get(`${API_PREFIX}/agreement`, params);
  },
  /**
   * 获取帮助中心内容
   */
  getHelpCenter() {
    return api_request.request.get(`${API_PREFIX}/help-center`);
  },
  /**
   * 获取常见问题列表
   */
  getFaqs() {
    return api_request.request.get(`${API_PREFIX}/faqs`);
  }
};
exports.commonApi = commonApi;
