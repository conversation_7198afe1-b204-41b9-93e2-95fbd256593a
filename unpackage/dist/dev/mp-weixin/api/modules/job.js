"use strict";
const api_request = require("../request.js");
const API_PREFIX = "/job";
const jobApi = {
  /**
   * 获取工作岗位列表
   * @param {Object} params - 查询参数，包括筛选条件、排序、分页等
   */
  getJobList(params) {
    return api_request.request.get(`${API_PREFIX}/list`, params);
  },
  /**
   * 获取工作岗位详情
   * @param {String} id - 工作岗位ID
   */
  getJobDetail(id) {
    return api_request.request.get(`${API_PREFIX}/detail/${id}`);
  },
  /**
   * 发布工作岗位
   * @param {Object} data - 工作岗位数据
   */
  publishJob(data) {
    return api_request.request.post(`${API_PREFIX}/publish`, data);
  },
  /**
   * 更新工作岗位
   * @param {String} id - 工作岗位ID
   * @param {Object} data - 工作岗位数据
   */
  updateJob(id, data) {
    return api_request.request.put(`${API_PREFIX}/update/${id}`, data);
  },
  /**
   * 删除工作岗位
   * @param {String} id - 工作岗位ID
   */
  deleteJob(id) {
    return api_request.request.delete(`${API_PREFIX}/delete/${id}`);
  },
  /**
   * 上传工作岗位相关图片
   * @param {String} filePath - 文件路径
   * @param {Object} formData - 额外表单数据
   */
  uploadJobImage(filePath, formData = {}) {
    return api_request.request.upload(`${API_PREFIX}/upload-image`, filePath, "image", formData);
  },
  /**
   * 获取工作类型列表
   */
  getJobTypes() {
    return api_request.request.get(`${API_PREFIX}/types`);
  },
  /**
   * 获取薪资范围列表
   */
  getSalaryRanges() {
    return api_request.request.get(`${API_PREFIX}/salary-ranges`);
  },
  /**
   * 获取工作经验要求列表
   */
  getExperienceRequirements() {
    return api_request.request.get(`${API_PREFIX}/experience-requirements`);
  },
  /**
   * 获取学历要求列表
   */
  getEducationRequirements() {
    return api_request.request.get(`${API_PREFIX}/education-requirements`);
  },
  /**
   * 投递简历
   * @param {String} jobId - 工作岗位ID
   * @param {Object} data - 简历数据
   */
  applyJob(jobId, data) {
    return api_request.request.post(`${API_PREFIX}/apply/${jobId}`, data);
  },
  /**
   * 获取我的求职申请列表
   * @param {Object} params - 查询参数
   */
  getMyApplications(params) {
    return api_request.request.get(`${API_PREFIX}/my-applications`, params);
  },
  /**
   * 获取收到的简历列表（企业用户）
   * @param {Object} params - 查询参数
   */
  getReceivedResumes(params) {
    return api_request.request.get(`${API_PREFIX}/received-resumes`, params);
  },
  /**
   * 处理简历（接受/拒绝）
   * @param {String} id - 申请ID
   * @param {Object} data - 处理数据
   */
  processResume(id, data) {
    return api_request.request.post(`${API_PREFIX}/process-resume/${id}`, data);
  },
  /**
   * 获取我发布的工作岗位列表
   * @param {Object} params - 查询参数
   */
  getMyJobs(params) {
    return api_request.request.get(`${API_PREFIX}/my-jobs`, params);
  },
  /**
   * 获取最新推荐工作岗位
   * @param {Object} params - 查询参数
   */
  getRecommendedJobs(params) {
    return api_request.request.get(`${API_PREFIX}/recommended`, params);
  },
  /**
   * 获取我的岗位申请列表
   * @param {Object} params - 查询参数
   */
  getMyJobApplications(params) {
    return api_request.request.get(`${API_PREFIX}/my-applications`, params);
  },
  /**
   * 发布顶班需求
   * @param {Object} data - 顶班需求数据
   */
  publishShiftDemand(data) {
    return api_request.request.post(`${API_PREFIX}/shift/publish`, data);
  },
  /**
   * 获取顶班需求列表
   * @param {Object} params - 查询参数
   */
  getShiftDemandList(params) {
    return api_request.request.get(`${API_PREFIX}/shift/list`, params);
  },
  /**
   * 获取顶班需求详情
   * @param {String} id - 顶班需求ID
   */
  getShiftDemandDetail(id) {
    return api_request.request.get(`${API_PREFIX}/shift/detail/${id}`);
  },
  /**
   * 接受顶班任务
   * @param {String} id - 顶班需求ID
   * @param {Object} data - 接单数据
   */
  acceptShift(id, data) {
    return api_request.request.post(`${API_PREFIX}/shift/accept/${id}`, data);
  },
  /**
   * 取消顶班任务（发布方取消）
   * @param {String} id - 顶班需求ID
   * @param {Object} data - 取消原因
   */
  cancelShiftDemand(id, data) {
    return api_request.request.post(`${API_PREFIX}/shift/cancel-demand/${id}`, data);
  },
  /**
   * 取消顶班任务（接单方取消）
   * @param {String} id - 顶班需求ID
   * @param {Object} data - 取消原因
   */
  cancelShiftAcceptance(id, data) {
    return api_request.request.post(`${API_PREFIX}/shift/cancel-acceptance/${id}`, data);
  },
  /**
   * 确认顶班任务完成
   * @param {String} id - 顶班需求ID
   */
  confirmShiftCompleted(id) {
    return api_request.request.post(`${API_PREFIX}/shift/confirm/${id}`);
  },
  /**
   * 评价顶班任务
   * @param {String} id - 顶班需求ID
   * @param {Object} data - 评价数据
   */
  reviewShift(id, data) {
    return api_request.request.post(`${API_PREFIX}/shift/review/${id}`, data);
  },
  /**
   * 获取我的顶班任务列表（发布的）
   * @param {Object} params - 查询参数
   */
  getMyPublishedShifts(params) {
    return api_request.request.get(`${API_PREFIX}/shift/my-published`, params);
  },
  /**
   * 获取我的顶班任务列表（接单的）
   * @param {Object} params - 查询参数
   */
  getMyAcceptedShifts(params) {
    return api_request.request.get(`${API_PREFIX}/shift/my-accepted`, params);
  },
  /**
   * 获取顶班结算记录
   * @param {Object} params - 查询参数
   */
  getShiftSettlements(params) {
    return api_request.request.get(`${API_PREFIX}/shift/settlements`, params);
  },
  /**
   * 获取可提供顶班的时间段
   */
  getAvailableShiftTimes() {
    return api_request.request.get(`${API_PREFIX}/shift/available-times`);
  },
  /**
   * 设置可提供顶班的时间段
   * @param {Object} data - 时间段数据
   */
  setAvailableShiftTimes(data) {
    return api_request.request.post(`${API_PREFIX}/shift/available-times`, data);
  },
  /**
   * 获取顶班统计数据
   */
  getShiftStatistics() {
    return api_request.request.get(`${API_PREFIX}/shift/statistics`);
  },
  /**
   * 获取招聘列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getList(params = {}) {
    return api_request.request({
      url: "/jobs",
      method: "GET",
      data: params
    });
  },
  /**
   * 获取招聘详情
   * @param {String} id 招聘ID
   * @returns {Promise}
   */
  getDetail(id) {
    return api_request.request({
      url: `/jobs/${id}`,
      method: "GET"
    });
  },
  /**
   * 创建招聘信息
   * @param {Object} data 招聘数据
   * @returns {Promise}
   */
  create(data) {
    return api_request.request({
      url: "/jobs",
      method: "POST",
      data
    });
  },
  /**
   * 更新招聘信息
   * @param {String} id 招聘ID
   * @param {Object} data 更新数据
   * @returns {Promise}
   */
  update(id, data) {
    return api_request.request({
      url: `/jobs/${id}`,
      method: "PUT",
      data
    });
  },
  /**
   * 删除招聘信息
   * @param {String} id 招聘ID
   * @returns {Promise}
   */
  remove(id) {
    return api_request.request({
      url: `/jobs/${id}`,
      method: "DELETE"
    });
  }
};
exports.jobApi = jobApi;
