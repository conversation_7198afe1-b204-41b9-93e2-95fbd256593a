"use strict";
const api_request = require("../request.js");
const API_PREFIX = "/product";
const productApi = {
  /**
   * 获取产品列表
   * @param {Object} params - 查询参数，包括分类、分页等
   */
  getProductList(params) {
    return api_request.request.get(`${API_PREFIX}/list`, params);
  },
  /**
   * 获取产品详情
   * @param {String} id - 产品ID
   */
  getProductDetail(id) {
    return api_request.request.get(`${API_PREFIX}/detail/${id}`);
  },
  /**
   * 获取产品分类
   */
  getProductCategories() {
    return api_request.request.get(`${API_PREFIX}/categories`);
  },
  /**
   * 获取热门产品
   * @param {Object} params - 查询参数，如数量限制等
   */
  getHotProducts(params) {
    return api_request.request.get(`${API_PREFIX}/hot`, params);
  },
  /**
   * 获取推荐产品
   * @param {Object} params - 查询参数，如数量限制等
   */
  getRecommendProducts(params) {
    return api_request.request.get(`${API_PREFIX}/recommend`, params);
  },
  /**
   * 获取新品
   * @param {Object} params - 查询参数，如数量限制等
   */
  getNewProducts(params) {
    return api_request.request.get(`${API_PREFIX}/new`, params);
  },
  /**
   * 搜索产品
   * @param {Object} params - 搜索参数，如关键词、分类等
   */
  searchProducts(params) {
    return api_request.request.get(`${API_PREFIX}/search`, params);
  },
  /**
   * 获取产品评价
   * @param {String} id - 产品ID
   * @param {Object} params - 查询参数，如分页等
   */
  getProductReviews(id, params) {
    return api_request.request.get(`${API_PREFIX}/reviews/${id}`, params);
  },
  /**
   * 添加产品评价
   * @param {String} id - 产品ID
   * @param {Object} data - 评价数据
   */
  addProductReview(id, data) {
    return api_request.request.post(`${API_PREFIX}/reviews/${id}`, data);
  }
};
exports.productApi = productApi;
