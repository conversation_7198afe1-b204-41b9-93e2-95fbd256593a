"use strict";
const api_request = require("../request.js");
const API_PREFIX = "/user";
const userApi = {
  /**
   * 用户登录
   * @param {Object} data - 登录数据，包含手机号和验证码
   */
  login(data) {
    return api_request.request.post(`${API_PREFIX}/login`, data);
  },
  /**
   * 微信登录授权接口，获取用户openid和session_key
   * @param {Object} data - 微信登录凭证
   */
  wxLogin(data) {
    return api_request.request.post(`${API_PREFIX}/wx-login`, data);
  },
  /**
   * 获取用户基本信息（昵称、头像等）
   */
  getUserProfile() {
    return api_request.request.get(`${API_PREFIX}/profile`);
  },
  /**
   * 绑定用户手机号
   * @param {Object} data - 包含手机号和验证数据
   */
  bindPhone(data) {
    return api_request.request.post(`${API_PREFIX}/bind-phone`, data);
  },
  /**
   * 获取用户信息
   */
  getUserInfo() {
    return api_request.request.get(`${API_PREFIX}/info`);
  },
  /**
   * 更新用户信息（头像、昵称等）
   * @param {Object} data - 用户信息数据
   */
  updateUserInfo(data) {
    return api_request.request.put(`${API_PREFIX}/info`, data);
  },
  /**
   * 用户退出登录
   */
  logout() {
    return api_request.request.post(`${API_PREFIX}/logout`);
  },
  /**
   * 获取用户订单统计（待付款、待服务、已完成等）
   */
  getUserOrderStats() {
    return api_request.request.get(`${API_PREFIX}/order-stats`);
  },
  /**
   * 获取用户地址列表
   */
  getUserAddressList() {
    return api_request.request.get(`${API_PREFIX}/addresses`);
  },
  /**
   * 添加用户地址
   * @param {Object} data - 地址数据
   */
  addUserAddress(data) {
    return api_request.request.post(`${API_PREFIX}/addresses`, data);
  },
  /**
   * 更新用户地址
   * @param {String} id - 地址ID
   * @param {Object} data - 地址数据
   */
  updateUserAddress(id, data) {
    return api_request.request.put(`${API_PREFIX}/addresses/${id}`, data);
  },
  /**
   * 删除用户地址
   * @param {String} id - 地址ID
   */
  deleteUserAddress(id) {
    return api_request.request.delete(`${API_PREFIX}/addresses/${id}`);
  },
  /**
   * 设置默认地址
   * @param {String} id - 地址ID
   */
  setDefaultAddress(id) {
    return api_request.request.put(`${API_PREFIX}/addresses/${id}/default`);
  },
  /**
   * 获取用户优惠券列表
   * @param {Object} params - 查询参数，如状态等
   */
  getUserCouponList(params) {
    return api_request.request.get(`${API_PREFIX}/coupons`, params);
  },
  /**
   * 获取用户积分
   */
  getUserPoints() {
    return api_request.request.get(`${API_PREFIX}/points`);
  },
  /**
   * 获取积分历史记录
   * @param {Object} params - 查询参数，如分页等
   */
  getPointsHistory(params) {
    return api_request.request.get(`${API_PREFIX}/points/history`, params);
  },
  /**
   * 上传用户头像
   * @param {String} filePath - 文件路径
   */
  uploadAvatar(filePath) {
    return api_request.request.upload(`${API_PREFIX}/avatar`, filePath, "avatar");
  },
  /**
   * 实名认证
   * @param {Object} data - 实名认证数据
   */
  verifyIdentity(data) {
    return api_request.request.post(`${API_PREFIX}/verify-identity`, data);
  },
  /**
   * 获取实名认证信息
   */
  getIdentityInfo() {
    return api_request.request.get(`${API_PREFIX}/identity-info`);
  },
  /**
   * 获取用户钱包信息
   */
  getWalletInfo() {
    return api_request.request.get(`${API_PREFIX}/wallet`);
  },
  /**
   * 获取交易记录
   * @param {Object} params - 查询参数
   */
  getTransactions(params) {
    return api_request.request.get(`${API_PREFIX}/transactions`, params);
  },
  /**
   * 发送短信验证码
   * @param {Object} data - 包含手机号
   */
  sendSmsCode(data) {
    return api_request.request.post(`${API_PREFIX}/send-sms-code`, data);
  },
  /**
   * 获取收藏列表
   * @param {Object} params - 查询参数
   */
  getFavorites(params) {
    return api_request.request.get(`${API_PREFIX}/favorites`, params);
  },
  /**
   * 添加收藏
   * @param {Object} data - 收藏数据
   */
  addFavorite(data) {
    return api_request.request.post(`${API_PREFIX}/favorites`, data);
  },
  /**
   * 取消收藏
   * @param {String} id - 收藏ID
   */
  removeFavorite(id) {
    return api_request.request.delete(`${API_PREFIX}/favorites/${id}`);
  },
  /**
   * 获取浏览历史
   * @param {Object} params - 查询参数
   */
  getHistory(params) {
    return api_request.request.get(`${API_PREFIX}/history`, params);
  },
  /**
   * 清空浏览历史
   */
  clearHistory() {
    return api_request.request.delete(`${API_PREFIX}/history`);
  }
};
exports.userApi = userApi;
