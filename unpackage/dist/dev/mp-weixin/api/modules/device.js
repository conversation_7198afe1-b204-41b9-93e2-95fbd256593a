"use strict";
const api_request = require("../request.js");
const deviceApi = {
  /**
   * 获取设备列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getList(params = {}) {
    return api_request.request({
      url: "/devices",
      method: "GET",
      data: params
    });
  },
  /**
   * 获取设备详情
   * @param {String} id 设备ID
   * @returns {Promise}
   */
  getDetail(id) {
    return api_request.request({
      url: `/devices/${id}`,
      method: "GET"
    });
  },
  /**
   * 创建设备信息
   * @param {Object} data 设备数据
   * @returns {Promise}
   */
  create(data) {
    return api_request.request({
      url: "/devices",
      method: "POST",
      data
    });
  },
  /**
   * 更新设备信息
   * @param {String} id 设备ID
   * @param {Object} data 更新数据
   * @returns {Promise}
   */
  update(id, data) {
    return api_request.request({
      url: `/devices/${id}`,
      method: "PUT",
      data
    });
  },
  /**
   * 删除设备信息
   * @param {String} id 设备ID
   * @returns {Promise}
   */
  remove(id) {
    return api_request.request({
      url: `/devices/${id}`,
      method: "DELETE"
    });
  },
  /**
   * 发布设备信息
   * @param {Object} data - 设备数据
   */
  publishDevice(data) {
    return api_request.request.post("/devices/publish", data);
  },
  /**
   * 获取设备类型列表
   */
  getDeviceTypes() {
    return api_request.request.get("/devices/types");
  },
  /**
   * 获取设备品牌列表
   */
  getDeviceBrands() {
    return api_request.request.get("/devices/brands");
  },
  /**
   * 获取新旧程度选项
   */
  getConditionOptions() {
    return api_request.request.get("/devices/condition-options");
  },
  /**
   * 获取我的设备列表
   * @param {Object} params - 查询参数
   */
  getMyDevices(params) {
    return api_request.request.get("/devices/my-devices", params);
  },
  /**
   * 购买设备
   * @param {Object} data - 购买数据
   */
  purchaseDevice(data) {
    return api_request.request.post("/devices/purchase", data);
  },
  /**
   * 评价设备
   * @param {String} id - 设备ID
   * @param {Object} data - 评价数据
   */
  reviewDevice(id, data) {
    return api_request.request.post(`/devices/review/${id}`, data);
  },
  /**
   * 获取设备评价列表
   * @param {String} id - 设备ID
   * @param {Object} params - 查询参数
   */
  getDeviceReviews(id, params) {
    return api_request.request.get(`/devices/reviews/${id}`, params);
  },
  /**
   * 获取推荐设备
   * @param {Object} params - 查询参数
   */
  getRecommendedDevices(params) {
    return api_request.request.get("/devices/recommended", params);
  },
  /**
   * 更新设备状态（上架/下架）
   * @param {String} id - 设备ID
   * @param {Object} data - 状态数据
   */
  updateDeviceStatus(id, data) {
    return api_request.request.put(`/devices/status/${id}`, data);
  },
  /**
   * 上传设备图片
   * @param {String} filePath - 文件路径
   * @param {Object} formData - 额外表单数据
   */
  uploadDeviceImage(filePath, formData = {}) {
    return api_request.request.upload("/devices/upload-image", filePath, "image", formData);
  }
};
exports.deviceApi = deviceApi;
