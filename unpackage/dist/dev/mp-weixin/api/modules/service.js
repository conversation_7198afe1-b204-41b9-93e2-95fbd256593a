"use strict";
const api_request = require("../request.js");
const API_PREFIX = "/service";
const serviceApi = {
  /**
   * 获取服务列表
   * @param {Object} params - 查询参数，包括筛选条件、排序、分页等
   */
  getServiceList(params) {
    return api_request.request.get(`${API_PREFIX}/list`, params);
  },
  /**
   * 获取服务详情
   * @param {String} id - 服务ID
   */
  getServiceDetail(id) {
    return api_request.request.get(`${API_PREFIX}/detail/${id}`);
  },
  /**
   * 发布服务
   * @param {Object} data - 服务数据
   */
  publishService(data) {
    return api_request.request.post(`${API_PREFIX}/publish`, data);
  },
  /**
   * 更新服务
   * @param {String} id - 服务ID
   * @param {Object} data - 服务数据
   */
  updateService(id, data) {
    return api_request.request.put(`${API_PREFIX}/update/${id}`, data);
  },
  /**
   * 删除服务
   * @param {String} id - 服务ID
   */
  deleteService(id) {
    return api_request.request.delete(`${API_PREFIX}/delete/${id}`);
  },
  /**
   * 上传服务图片
   * @param {String} filePath - 文件路径
   * @param {Object} formData - 额外表单数据
   */
  uploadServiceImage(filePath, formData = {}) {
    return api_request.request.upload(`${API_PREFIX}/upload-image`, filePath, "image", formData);
  },
  /**
   * 获取服务类型列表
   */
  getServiceTypes() {
    return api_request.request.get(`${API_PREFIX}/types`);
  },
  /**
   * 获取服务订单列表
   * @param {Object} params - 查询参数
   */
  getServiceOrderList(params) {
    return api_request.request.get(`${API_PREFIX}/orders`, params);
  },
  /**
   * 获取服务订单详情
   * @param {String} id - 订单ID
   */
  getServiceOrderDetail(id) {
    return api_request.request.get(`${API_PREFIX}/order-detail/${id}`);
  },
  /**
   * 接受服务订单
   * @param {String} id - 订单ID
   */
  acceptServiceOrder(id) {
    return api_request.request.post(`${API_PREFIX}/accept-order/${id}`);
  },
  /**
   * 拒绝服务订单
   * @param {String} id - 订单ID
   * @param {Object} data - 拒绝原因等数据
   */
  rejectServiceOrder(id, data) {
    return api_request.request.post(`${API_PREFIX}/reject-order/${id}`, data);
  },
  /**
   * 完成服务
   * @param {String} id - 订单ID
   */
  completeService(id) {
    return api_request.request.post(`${API_PREFIX}/complete/${id}`);
  },
  /**
   * 评价服务
   * @param {String} id - 服务ID或订单ID
   * @param {Object} data - 评价数据
   */
  reviewService(id, data) {
    return api_request.request.post(`${API_PREFIX}/review/${id}`, data);
  },
  /**
   * 获取服务评价列表
   * @param {String} id - 服务ID
   * @param {Object} params - 查询参数
   */
  getServiceReviews(id, params) {
    return api_request.request.get(`${API_PREFIX}/reviews/${id}`, params);
  },
  /**
   * 获取我的服务列表
   * @param {Object} params - 查询参数
   */
  getMyServices(params) {
    return api_request.request.get(`${API_PREFIX}/my-services`, params);
  },
  /**
   * 获取服务列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getList(params = {}) {
    return api_request.request({
      url: "/services",
      method: "GET",
      data: params
    });
  },
  /**
   * 获取服务详情
   * @param {String} id 服务ID
   * @returns {Promise}
   */
  getDetail(id) {
    return api_request.request({
      url: `/services/${id}`,
      method: "GET"
    });
  },
  /**
   * 创建服务信息
   * @param {Object} data 服务数据
   * @returns {Promise}
   */
  create(data) {
    return api_request.request({
      url: "/services",
      method: "POST",
      data
    });
  },
  /**
   * 更新服务信息
   * @param {String} id 服务ID
   * @param {Object} data 更新数据
   * @returns {Promise}
   */
  update(id, data) {
    return api_request.request({
      url: `/services/${id}`,
      method: "PUT",
      data
    });
  },
  /**
   * 删除服务信息
   * @param {String} id 服务ID
   * @returns {Promise}
   */
  remove(id) {
    return api_request.request({
      url: `/services/${id}`,
      method: "DELETE"
    });
  }
};
exports.serviceApi = serviceApi;
