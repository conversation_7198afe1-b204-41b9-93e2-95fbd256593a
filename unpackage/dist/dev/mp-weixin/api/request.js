"use strict";
const common_vendor = require("../common/vendor.js");
const config = {
  baseURL: "http://localhost:3000",
  timeout: 3e4,
  // 增加超时时间到30秒
  success_code: 0,
  redirect_login_code: 401,
  enable_log: true
};
const getApiBaseUrl = () => {
  return config.baseURL;
};
const requestInterceptor = (options) => {
  const token = common_vendor.index.getStorageSync("token");
  if (token) {
    if (!options.header) {
      options.header = {};
    }
    options.header["Authorization"] = "Bearer " + token;
  }
  {
    console.log("Request:", options.url, options.data || options.params);
  }
  return options;
};
const responseInterceptor = (response) => {
  var _a;
  const res = response.data;
  {
    console.log("Response:", (_a = response.config) == null ? void 0 : _a.url, res);
  }
  if (res.code !== config.success_code) {
    if (res.code === config.redirect_login_code) {
      console.error("身份验证失败，请重新登录");
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
    }
    common_vendor.index.showToast({
      title: res.message || "请求失败",
      icon: "none"
    });
    throw new Error(res.message || "未知错误");
  }
  return res;
};
const request = (options) => {
  const finalOptions = {
    ...options,
    url: options.url.startsWith("http") ? options.url : `${getApiBaseUrl()}${options.url}`,
    timeout: options.timeout || config.timeout
  };
  const finalConfig = requestInterceptor(finalOptions);
  return new Promise((resolve, reject) => {
    let retryCount = 0;
    const maxRetries = 2;
    function sendRequest() {
      common_vendor.index.request({
        ...finalConfig,
        success: (res) => {
          try {
            const result = responseInterceptor(res);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        },
        fail: (err) => {
          console.error("Request failed:", finalConfig.url, err);
          if (err.errMsg && err.errMsg.includes("timeout") && retryCount < maxRetries) {
            retryCount++;
            console.log(`请求超时，第${retryCount}次重试...`);
            setTimeout(() => {
              sendRequest();
            }, 1e3);
            return;
          }
          common_vendor.index.showToast({
            title: "网络请求失败",
            icon: "none"
          });
          reject(err);
        }
      });
    }
    sendRequest();
  });
};
const request$1 = {
  get(url, params = {}, options = {}) {
    return request({
      url,
      method: "GET",
      data: params,
      ...options
    });
  },
  post(url, data = {}, options = {}) {
    return request({
      url,
      method: "POST",
      data,
      ...options
    });
  },
  put(url, data = {}, options = {}) {
    return request({
      url,
      method: "PUT",
      data,
      ...options
    });
  },
  delete(url, data = {}, options = {}) {
    return request({
      url,
      method: "DELETE",
      data,
      ...options
    });
  },
  // 上传文件
  upload(url, filePath, name = "file", formData = {}, options = {}) {
    const token = common_vendor.index.getStorageSync("token");
    const header = token ? { "Authorization": `Bearer ${token}` } : {};
    {
      console.log("Upload request:", `${getApiBaseUrl()}${url}`, { filePath, name, formData });
    }
    return new Promise((resolve, reject) => {
      common_vendor.index.uploadFile({
        url: `${getApiBaseUrl()}${url}`,
        filePath,
        name,
        formData,
        header,
        ...options,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            try {
              const data = JSON.parse(res.data);
              if (config.enable_log) {
                console.log("Upload response:", url, data);
              }
              if (data.code === config.success_code) {
                resolve(data);
              } else {
                common_vendor.index.showToast({
                  title: data.message || "上传失败",
                  icon: "none"
                });
                reject(data);
              }
            } catch (e) {
              common_vendor.index.showToast({
                title: "上传结果解析失败",
                icon: "none"
              });
              reject(e);
            }
          } else {
            common_vendor.index.showToast({
              title: `上传失败(${res.statusCode})`,
              icon: "none"
            });
            reject(res);
          }
        },
        fail: (err) => {
          common_vendor.index.showToast({
            title: "网络请求失败",
            icon: "none"
          });
          {
            console.error("Upload failed:", url, err);
          }
          reject(err);
        }
      });
    });
  }
};
exports.request = request$1;
