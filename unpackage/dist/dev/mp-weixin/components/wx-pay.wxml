<view class="wx-pay-container"><view wx:if="{{a}}" class="pay-popup"><view class="pay-panel"><view class="pay-header"><text class="pay-title">支付订单</text><view class="close-btn" bindtap="{{b}}"><text>×</text></view></view><view class="pay-amount"><text class="currency">¥</text><text class="amount">{{c}}</text></view><view class="pay-method"><view class="method-item" bindtap="{{g}}"><view class="method-info"><image src="{{d}}" mode="aspectFit"></image><text>微信支付</text></view><view class="{{['check-icon', f && 'checked']}}"><view wx:if="{{e}}" class="inner-circle"></view></view></view><view wx:if="{{h}}" class="method-item" bindtap="{{m}}"><view class="method-info"><image src="{{i}}" mode="aspectFit"></image><text>余额支付</text><text class="balance-info">(可用余额: ¥{{j}})</text></view><view class="{{['check-icon', l && 'checked']}}"><view wx:if="{{k}}" class="inner-circle"></view></view></view></view><view class="pay-action"><view class="pay-btn" bindtap="{{n}}">立即支付</view></view></view></view><view wx:if="{{o}}" class="result-popup"><view class="result-panel"><view class="{{['result-icon', q && 'success', r && 'fail']}}"><text wx:if="{{p}}">✓</text><text wx:else>×</text></view><view class="result-title"><text wx:if="{{s}}">支付成功</text><text wx:else>支付失败</text></view><view class="result-desc"><text wx:if="{{t}}">您的订单支付已完成</text><text wx:else>{{v}}</text></view><view class="result-action"><view class="result-btn" bindtap="{{x}}"><text wx:if="{{w}}">完成</text><text wx:else>重新支付</text></view></view></view></view></view>