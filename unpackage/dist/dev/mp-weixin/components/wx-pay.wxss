
.wx-pay-container {
  /* 容器样式 */
}

/* 支付弹窗 */
.pay-popup {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: flex-end;
}
.pay-panel {
  width: 100%;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
  animation: slideUp 0.3s ease;
}
@keyframes slideUp {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
.pay-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}
.pay-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
}
.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.close-btn text {
  font-size: 40rpx;
  color: #999999;
}
.pay-amount {
  text-align: center;
  margin-bottom: 50rpx;
}
.currency {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}
.amount {
  font-size: 60rpx;
  font-weight: bold;
  color: #333333;
}
.pay-method {
  margin-bottom: 50rpx;
}
.method-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.method-info {
  display: flex;
  align-items: center;
}
.method-info image {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}
.method-info text {
  font-size: 30rpx;
  color: #333333;
}
.balance-info {
  font-size: 24rpx;
  color: #999999;
  margin-left: 10rpx;
}
.check-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #dddddd;
  display: flex;
  align-items: center;
  justify-content: center;
}
.check-icon.checked {
  border-color: #ff5a5f;
}
.inner-circle {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #ff5a5f;
}
.pay-action {
  margin-top: 50rpx;
}
.pay-btn {
  width: 100%;
  height: 90rpx;
  background-color: #ff5a5f;
  color: #ffffff;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

/* 支付结果弹窗 */
.result-popup {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.result-panel {
  width: 80%;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 50rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.result-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}
.result-icon.success {
  background-color: #07c160;
}
.result-icon.fail {
  background-color: #ff5a5f;
}
.result-icon text {
  font-size: 60rpx;
  color: #ffffff;
  font-weight: bold;
}
.result-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}
.result-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
  text-align: center;
}
.result-action {
  width: 100%;
}
.result-btn {
  width: 100%;
  height: 90rpx;
  background-color: #ff5a5f;
  color: #ffffff;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}
