"use strict";
const common_vendor = require("../common/vendor.js");
const common_assets = require("../common/assets.js");
const _sfc_main = {
  props: {
    // 订单ID
    orderId: {
      type: String,
      default: ""
    },
    // 支付金额（单位：分）
    amount: {
      type: Number,
      default: 0
    },
    // 支付描述
    description: {
      type: String,
      default: "驿站帮-商品购买"
    },
    // 显示支付面板
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showPayPanel: false,
      showResultPanel: false,
      payMethod: "wxpay",
      payAmount: 0,
      userBalance: 0,
      paySuccess: false,
      payErrorMsg: "",
      paymentId: "",
      tradeNo: "",
      isProcessing: false,
      pollingTimer: null
    };
  },
  watch: {
    visible(newVal) {
      this.showPayPanel = newVal;
      if (newVal) {
        this.payAmount = this.amount;
        this.loadUserInfo();
      }
    },
    // 监控支付面板状态
    showPayPanel(newVal) {
      if (!newVal) {
        this.$emit("update:visible", false);
      }
    }
  },
  beforeDestroy() {
    this.clearPollingTimer();
  },
  methods: {
    // 加载用户信息
    async loadUserInfo() {
      try {
        const userInfoStr = common_vendor.index.getStorageSync("userInfo");
        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          this.userBalance = userInfo.balance || 0;
        }
      } catch (err) {
        console.error("获取用户信息失败", err);
        this.userBalance = 0;
      }
    },
    // 选择支付方式
    selectPayMethod(method) {
      this.payMethod = method;
    },
    // 关闭支付面板
    closePayPanel() {
      this.showPayPanel = false;
    },
    // 确认支付
    async confirmPay() {
      if (this.isProcessing)
        return;
      if (this.payMethod === "balance") {
        await this.balancePay();
        return;
      }
      await this.wxPay();
    },
    // 微信支付
    async wxPay() {
      if (!this.orderId || this.payAmount <= 0) {
        this.showPayError("无效的订单信息");
        return;
      }
      this.isProcessing = true;
      common_vendor.index.showLoading({ title: "处理中..." });
      try {
        const result = await common_vendor.wx$1.cloud.callFunction({
          name: "pay",
          data: {
            action: "createPayment",
            orderId: this.orderId,
            totalFee: this.payAmount,
            body: this.description
          }
        });
        if (result.result && result.result.code === 0) {
          const { paymentId, tradeNo, payParams } = result.result.data;
          this.paymentId = paymentId;
          this.tradeNo = tradeNo;
          common_vendor.wx$1.requestPayment({
            ...payParams,
            success: () => {
              this.startPaymentPolling();
            },
            fail: (err) => {
              console.error("支付失败", err);
              if (err.errMsg === "requestPayment:fail cancel") {
                this.closePayPanel();
              } else {
                this.showPayError("支付失败: " + err.errMsg);
              }
            },
            complete: () => {
              this.isProcessing = false;
              common_vendor.index.hideLoading();
            }
          });
        } else {
          this.showPayError(result.result.msg || "创建支付订单失败");
          this.isProcessing = false;
          common_vendor.index.hideLoading();
        }
      } catch (err) {
        console.error("微信支付调用失败", err);
        this.showPayError("支付异常，请重试");
        this.isProcessing = false;
        common_vendor.index.hideLoading();
      }
    },
    // 余额支付
    async balancePay() {
      if (!this.orderId || this.payAmount <= 0) {
        this.showPayError("无效的订单信息");
        return;
      }
      if (this.userBalance < this.payAmount) {
        this.showPayError("余额不足");
        return;
      }
      this.isProcessing = true;
      common_vendor.index.showLoading({ title: "处理中..." });
      try {
        const result = await common_vendor.wx$1.cloud.callFunction({
          name: "balancePay",
          data: {
            orderId: this.orderId,
            amount: this.payAmount
          }
        });
        if (result.result && result.result.code === 0) {
          this.showPayPanel = false;
          this.paySuccess = true;
          this.showResultPanel = true;
          this.$emit("pay-success", {
            orderId: this.orderId,
            paymentMethod: "balance",
            amount: this.payAmount
          });
        } else {
          this.showPayError(result.result.msg || "余额支付失败");
        }
      } catch (err) {
        console.error("余额支付失败", err);
        this.showPayError("支付异常，请重试");
      } finally {
        this.isProcessing = false;
        common_vendor.index.hideLoading();
      }
    },
    // 开始轮询支付结果
    startPaymentPolling() {
      this.clearPollingTimer();
      this.queryPaymentResult();
      let count = 0;
      const maxCount = 10;
      this.pollingTimer = setInterval(async () => {
        count++;
        const success = await this.queryPaymentResult();
        if (success || count >= maxCount) {
          this.clearPollingTimer();
          if (!success && count >= maxCount) {
            this.showPayError("支付结果查询超时，请稍后在订单中心查看");
          }
        }
      }, 2e3);
    },
    // 清除轮询定时器
    clearPollingTimer() {
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
      }
    },
    // 查询支付结果
    async queryPaymentResult() {
      try {
        if (!this.orderId)
          return false;
        const result = await common_vendor.wx$1.cloud.callFunction({
          name: "pay",
          data: {
            action: "queryPayment",
            orderId: this.orderId
          }
        });
        if (result.result && result.result.code === 0 && result.result.data.isPaid) {
          this.showPayPanel = false;
          this.paySuccess = true;
          this.showResultPanel = true;
          this.$emit("pay-success", {
            orderId: this.orderId,
            paymentMethod: "wxpay",
            amount: this.payAmount,
            tradeNo: result.result.data.tradeNo
          });
          return true;
        }
        return false;
      } catch (err) {
        console.error("查询支付结果失败", err);
        return false;
      }
    },
    // 显示支付错误
    showPayError(msg) {
      this.showPayPanel = false;
      this.paySuccess = false;
      this.payErrorMsg = msg;
      this.showResultPanel = true;
    },
    // 处理结果面板关闭
    handleResultClose() {
      if (this.paySuccess) {
        this.showResultPanel = false;
      } else {
        this.showResultPanel = false;
        this.showPayPanel = true;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.showPayPanel
  }, $data.showPayPanel ? common_vendor.e({
    b: common_vendor.o((...args) => $options.closePayPanel && $options.closePayPanel(...args)),
    c: common_vendor.t(($data.payAmount / 100).toFixed(2)),
    d: common_assets._imports_0$6,
    e: $data.payMethod === "wxpay"
  }, $data.payMethod === "wxpay" ? {} : {}, {
    f: $data.payMethod === "wxpay" ? 1 : "",
    g: common_vendor.o(($event) => $options.selectPayMethod("wxpay")),
    h: $data.userBalance >= $data.payAmount
  }, $data.userBalance >= $data.payAmount ? common_vendor.e({
    i: common_assets._imports_1$7,
    j: common_vendor.t(($data.userBalance / 100).toFixed(2)),
    k: $data.payMethod === "balance"
  }, $data.payMethod === "balance" ? {} : {}, {
    l: $data.payMethod === "balance" ? 1 : "",
    m: common_vendor.o(($event) => $options.selectPayMethod("balance"))
  }) : {}, {
    n: common_vendor.o((...args) => $options.confirmPay && $options.confirmPay(...args))
  }) : {}, {
    o: $data.showResultPanel
  }, $data.showResultPanel ? common_vendor.e({
    p: $data.paySuccess
  }, $data.paySuccess ? {} : {}, {
    q: $data.paySuccess ? 1 : "",
    r: !$data.paySuccess ? 1 : "",
    s: $data.paySuccess
  }, $data.paySuccess ? {} : {}, {
    t: $data.paySuccess
  }, $data.paySuccess ? {} : {
    v: common_vendor.t($data.payErrorMsg || "支付过程中出现错误，请重试")
  }, {
    w: $data.paySuccess
  }, $data.paySuccess ? {} : {}, {
    x: common_vendor.o((...args) => $options.handleResultClose && $options.handleResultClose(...args))
  }) : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
