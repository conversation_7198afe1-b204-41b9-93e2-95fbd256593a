"use strict";
const common_vendor = require("./common/vendor.js");
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
function applyPolyfills() {
  console.error = function(...args) {
    const errorMessage = args.join(" ");
    if (errorMessage.includes("Cannot read property") && errorMessage.includes("toLocaleLowerCase") && errorMessage.includes("undefined")) {
      console.log("[已拦截] toLocaleLowerCase错误");
      return;
    }
    if (errorMessage.includes("Page not found") || errorMessage.includes("Cannot find page")) {
      console.log("[已拦截] 页面不存在错误:", errorMessage);
      return;
    }
    return originalConsoleError.apply(console, args);
  };
  console.warn = function(...args) {
    const warnMessage = args.join(" ");
    if (warnMessage.includes("deprecated") && warnMessage.includes("getSystemInfoSync")) {
      return;
    }
    return originalConsoleWarn.apply(console, args);
  };
  enhanceSystemInfoMethods();
  setupGlobalErrorHandlers();
  console.log("[Polyfill] 已应用错误处理和兼容性修复");
}
function enhanceSystemInfoMethods() {
  if (typeof common_vendor.wx$1 !== "undefined") {
    if (common_vendor.wx$1.getSystemInfoSync) {
      const originalGetSystemInfoSync = common_vendor.wx$1.getSystemInfoSync;
      common_vendor.wx$1.getSystemInfoSync = function() {
        try {
          const result = originalGetSystemInfoSync.apply(this, arguments);
          if (!result.system) {
            result.system = result.platform || "unknown";
          }
          return result;
        } catch (error) {
          console.log("[Polyfill] 获取系统信息失败:", error);
          return {
            system: "unknown",
            platform: "unknown",
            brand: "",
            model: "",
            pixelRatio: 2,
            windowWidth: 375,
            windowHeight: 667,
            language: "zh_CN",
            version: "0.0.0",
            SDKVersion: "0.0.0"
          };
        }
      };
    }
    if (common_vendor.wx$1.getDeviceInfo) {
      const originalGetDeviceInfo = common_vendor.wx$1.getDeviceInfo;
      common_vendor.wx$1.getDeviceInfo = function() {
        try {
          const result = originalGetDeviceInfo.apply(this, arguments);
          if (!result.system) {
            result.system = result.platform || "unknown";
          }
          return result;
        } catch (error) {
          console.log("[Polyfill] 获取设备信息失败:", error);
          return {
            system: "unknown",
            platform: "unknown",
            brand: "",
            model: ""
          };
        }
      };
    }
    if (common_vendor.wx$1.getAppBaseInfo) {
      const originalGetAppBaseInfo = common_vendor.wx$1.getAppBaseInfo;
      common_vendor.wx$1.getAppBaseInfo = function() {
        try {
          return originalGetAppBaseInfo.apply(this, arguments);
        } catch (error) {
          console.log("[Polyfill] 获取应用信息失败:", error);
          return {
            language: "zh_CN",
            version: "0.0.0",
            SDKVersion: "0.0.0"
          };
        }
      };
    }
  }
}
function setupGlobalErrorHandlers() {
  if (typeof common_vendor.wx$1 !== "undefined") {
    if (common_vendor.wx$1.onError) {
      common_vendor.wx$1.onError((error) => {
        console.log("[Polyfill] 捕获全局错误:", error);
      });
    }
    if (common_vendor.wx$1.onUnhandledRejection) {
      common_vendor.wx$1.onUnhandledRejection(({ reason }) => {
        console.log("[Polyfill] 捕获Promise错误:", reason);
      });
    }
    if (common_vendor.wx$1.onPageNotFound) {
      common_vendor.wx$1.onPageNotFound((res) => {
        console.log("[Polyfill] 页面不存在:", res.path);
        common_vendor.wx$1.switchTab({
          url: "/pages/index/index",
          fail: (err) => {
            console.log("[Polyfill] 重定向失败:", err);
          }
        });
      });
    }
  }
}
exports.applyPolyfills = applyPolyfills;
