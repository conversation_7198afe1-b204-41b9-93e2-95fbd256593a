"use strict";
const common_vendor = require("./common/vendor.js");
function fixToLocaleLowerCaseError() {
  const originalConsoleError = console.error;
  console.error = function(...args) {
    const errorMessage = args.join(" ");
    if (errorMessage.includes("Cannot read property") && errorMessage.includes("toLocaleLowerCase") && errorMessage.includes("undefined")) {
      console.log("[已拦截] toLocaleLowerCase错误");
      return;
    }
    return originalConsoleError.apply(console, args);
  };
  if (typeof common_vendor.wx$1 !== "undefined" && common_vendor.wx$1.getSystemInfoSync) {
    const originalGetSystemInfoSync = common_vendor.wx$1.getSystemInfoSync;
    common_vendor.wx$1.getSystemInfoSync = function() {
      try {
        const result = originalGetSystemInfoSync.apply(this, arguments);
        if (!result.system) {
          result.system = result.platform || "unknown";
        }
        return result;
      } catch (error) {
        console.log("[错误] 获取系统信息失败:", error);
        return {
          system: "unknown",
          platform: "unknown",
          brand: "",
          model: "",
          pixelRatio: 2,
          windowWidth: 375,
          windowHeight: 667,
          language: "zh_CN",
          version: "0.0.0",
          SDKVersion: "0.0.0"
        };
      }
    };
  }
  if (typeof common_vendor.wx$1 !== "undefined") {
    if (common_vendor.wx$1.getDeviceInfo) {
      const originalGetDeviceInfo = common_vendor.wx$1.getDeviceInfo;
      common_vendor.wx$1.getDeviceInfo = function() {
        try {
          const result = originalGetDeviceInfo.apply(this, arguments);
          if (!result.system) {
            result.system = result.platform || "unknown";
          }
          return result;
        } catch (error) {
          console.log("[错误] 获取设备信息失败:", error);
          return {
            system: "unknown",
            platform: "unknown",
            brand: "",
            model: ""
          };
        }
      };
    }
    if (common_vendor.wx$1.getAppBaseInfo) {
      const originalGetAppBaseInfo = common_vendor.wx$1.getAppBaseInfo;
      common_vendor.wx$1.getAppBaseInfo = function() {
        try {
          return originalGetAppBaseInfo.apply(this, arguments);
        } catch (error) {
          console.log("[错误] 获取应用信息失败:", error);
          return {
            language: "zh_CN",
            version: "0.0.0",
            SDKVersion: "0.0.0"
          };
        }
      };
    }
  }
}
function setupErrorHandlers() {
  if (typeof common_vendor.wx$1 !== "undefined" && common_vendor.wx$1.onError) {
    common_vendor.wx$1.onError((error) => {
      console.log("[小程序错误]", error);
    });
  }
  if (typeof common_vendor.wx$1 !== "undefined" && common_vendor.wx$1.onUnhandledRejection) {
    common_vendor.wx$1.onUnhandledRejection(({ reason }) => {
      console.log("[Promise错误]", reason);
    });
  }
  if (typeof common_vendor.wx$1 !== "undefined" && common_vendor.wx$1.onPageNotFound) {
    common_vendor.wx$1.onPageNotFound((res) => {
      console.log("[页面不存在]", res.path);
    });
  }
}
const appFix = {
  fixToLocaleLowerCaseError,
  setupErrorHandlers
};
exports.appFix = appFix;
