"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const api_index = require("./api/index.js");
const polyfill = require("./polyfill.js");
require("./uni.promisify.adaptor.js");
const appFix = require("./app-fix.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/station/station.js";
  "./pages/device/device.js";
  "./pages/job/job.js";
  "./pages/service/service.js";
  "./pages/transfer/transfer.js";
  "./pages/publish/publish.js";
  "./pages/my/my.js";
  "./pages/message/message.js";
  "./pages/detail/detail.js";
  "./pages/publish/station.js";
  "./pages/publish/service.js";
  "./pages/publish/job.js";
  "./pages/publish/device.js";
  "./pages/my/login.js";
  "./pages/my/posts.js";
  "./pages/my/favorites.js";
  "./pages/my/profile.js";
  "./pages/my/history.js";
  "./pages/my/orders.js";
  "./pages/my/wallet.js";
  "./pages/my/agreement.js";
  "./pages/my/privacy.js";
  "./pages/my/cart.js";
  "./pages/my/checkout.js";
  "./pages/my/promotion.js";
  "./pages/my/verify-identity.js";
  "./pages/my/verify-station.js";
  "./pages/my/business-settings.js";
  "./pages/my/service-orders.js";
  "./pages/my/settings.js";
  "./pages/my/service-order-detail.js";
  "./pages/my/address-list.js";
  "./pages/my/address-edit.js";
}
const _sfc_main = {
  onLaunch: function() {
    console.log("App Launch");
    this.checkLoginStatus();
    appFix.appFix.fixToLocaleLowerCaseError();
  },
  onShow: function() {
    console.log("App Show");
  },
  onHide: function() {
    console.log("App Hide");
  },
  methods: {
    // 检查登录状态
    async checkLoginStatus() {
      const token = common_vendor.index.getStorageSync("token");
      if (!token) {
        console.log("未登录状态");
        return;
      }
      try {
        console.log("已登录状态");
      } catch (error) {
        console.error("登录状态检查失败", error);
        common_vendor.index.removeStorageSync("token");
        common_vendor.index.removeStorageSync("userInfo");
      }
    }
  }
};
polyfill.applyPolyfills();
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.config.globalProperties.$api = api_index.api;
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
