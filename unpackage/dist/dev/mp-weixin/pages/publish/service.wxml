<view class="container"><view class="form-section"><view class="section-title"><view class="title-bar"></view><text>基本信息</text></view><view class="form-item"><view class="label"><text>发布标题</text><text class="required">*</text></view><input type="text" placeholder="请输入标题，如：金三角地铁站附近快递员急需顶班" placeholder-class="placeholder" value="{{a}}" bindinput="{{b}}"/></view><view class="form-item"><view class="label"><text>顶班价格</text><text class="required">*</text></view><view class="price-input"><text class="price-symbol">¥</text><input type="digit" placeholder="请输入顶班价格/天" placeholder-class="placeholder" value="{{c}}" bindinput="{{d}}"/></view></view><view class="form-item"><view class="label"><text>所在区域</text><text class="required">*</text></view><picker mode="selector" range="{{g}}" bindchange="{{h}}"><view class="picker-value"><text wx:if="{{e}}">{{f}}</text><text wx:else class="placeholder">请选择所在区域</text><text class="picker-arrow">▼</text></view></picker></view><view class="form-item"><view class="label"><text>具体位置</text><text class="required">*</text></view><input type="text" placeholder="请输入具体位置" placeholder-class="placeholder" value="{{i}}" bindinput="{{j}}"/></view><view class="form-item"><view class="label"><text>工作时间</text><text class="required">*</text></view><picker mode="selector" range="{{m}}" bindchange="{{n}}"><view class="picker-value"><text wx:if="{{k}}">{{l}}</text><text wx:else class="placeholder">请选择工作时间</text><text class="picker-arrow">▼</text></view></picker></view><view class="form-item"><text class="label">日期选择 <text class="required">*</text></text><view class="date-select"><picker mode="date" value="{{q}}" start="{{r}}" bindchange="{{s}}"><view class="date-picker"><text wx:if="{{o}}">{{p}}</text><text wx:else class="placeholder">开始日期</text></view></picker><text class="date-separator">至</text><picker mode="date" value="{{w}}" start="{{x}}" bindchange="{{y}}"><view class="date-picker"><text wx:if="{{t}}">{{v}}</text><text wx:else class="placeholder">结束日期</text></view></picker></view></view></view><view class="form-section"><view class="section-title"><view class="title-bar"></view><text>工作描述</text></view><view class="form-item"><view class="label"><text>工作类型</text><text class="required">*</text></view><picker mode="selector" range="{{B}}" bindchange="{{C}}"><view class="picker-value"><text wx:if="{{z}}">{{A}}</text><text wx:else class="placeholder">请选择工作类型</text><text class="picker-arrow">▼</text></view></picker></view><view class="form-item"><text class="label">快递公司</text><picker mode="selector" range="{{F}}" bindchange="{{G}}"><view class="picker-value"><text wx:if="{{D}}">{{E}}</text><text wx:else class="placeholder">请选择快递公司</text><text class="picker-arrow">▼</text></view></picker></view><view class="form-item"><text class="label">日均件量</text><input type="number" placeholder="请输入日均派件量/揽件量" value="{{H}}" bindinput="{{I}}"/></view><view class="form-item"><text class="label">工作说明</text><block wx:if="{{r0}}"><textarea placeholder="请详细描述工作内容、要求等信息" value="{{J}}" bindinput="{{K}}"/></block></view></view><view class="form-section"><view class="section-title"><view class="title-bar"></view><text>联系方式</text></view><view class="form-item"><view class="label"><text>联系人</text><text class="required">*</text></view><input type="text" placeholder="请输入联系人姓名" placeholder-class="placeholder" value="{{L}}" bindinput="{{M}}"/></view><view class="form-item"><view class="label"><text>手机号码</text><text class="required">*</text></view><input type="number" placeholder="请输入手机号码" maxlength="11" placeholder-class="placeholder" value="{{N}}" bindinput="{{O}}"/></view><view class="form-item"><view class="label"><text>微信号</text></view><input type="text" placeholder="请输入微信号" placeholder-class="placeholder" value="{{P}}" bindinput="{{Q}}"/></view></view><view class="button-group"><button class="btn-draft" bindtap="{{R}}">保存草稿</button><button class="btn-publish" bindtap="{{S}}">立即发布</button></view></view>