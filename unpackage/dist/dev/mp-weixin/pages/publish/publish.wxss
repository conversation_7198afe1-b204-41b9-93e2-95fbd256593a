
.container {
		min-height: 100vh;
		background-color: #f8f8f8;
}
.header-section {
		background: linear-gradient(to right, #ff758c, #ff7eb3);
		padding: 60rpx 30rpx;
		text-align: center;
		color: #ffffff;
		border-radius: 0 0 30rpx 30rpx;
		margin-bottom: 40rpx;
}
.title {
		font-size: 42rpx;
		font-weight: bold;
		margin-bottom: 15rpx;
}
.subtitle {
		font-size: 28rpx;
		opacity: 0.9;
}
.option-cards {
		padding: 0 30rpx;
}
.option-card {
		background-color: #ffffff;
		border-radius: 15rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.card-icon {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		margin-right: 30rpx;
		position: relative;
}
.icon-box {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
}
.card-icon image {
		width: 50rpx;
		height: 50rpx;
}
.station {
		background-color: #ffeff0;
}
.service {
		background-color: #e8f4ff;
}
.job {
		background-color: #f0e8ff;
}
.device {
		background-color: #ffe8e8;
}
.card-content {
		flex: 1;
}
.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
}
.card-desc {
		font-size: 24rpx;
		color: #999999;
}
.card-arrow {
		color: #cccccc;
		font-size: 32rpx;
}
.publish-tips {
		margin: 30rpx 30rpx 80rpx 30rpx;
		background-color: #ffffff;
		border-radius: 15rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.tips-title {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
}
.tips-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
}
.tips-icon image {
		width: 100%;
		height: 100%;
}
.tips-list {
		padding-left: 20rpx;
}
.tip-item {
		display: flex;
		align-items: flex-start;
		font-size: 26rpx;
		color: #ff758c;
		line-height: 2;
}
.dot {
		margin-right: 10rpx;
}
