"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      apiBaseUrl: "",
      // 添加API基础URL
      selectedPosition: "sorting",
      // 默认选中分拣员
      title: "",
      selectedSalary: null,
      salaryRanges: ["3000-4000元/月", "4000-5000元/月", "5000-6000元/月", "6000-7000元/月", "7000-8000元/月", "8000元以上/月", "面议"],
      selectedArea: null,
      areas: ["海淀区", "朝阳区", "西城区", "东城区", "丰台区", "石景山区", "通州区", "昌平区", "大兴区", "顺义区", "房山区", "门头沟区", "平谷区", "密云区", "怀柔区", "延庆区"],
      address: "",
      selectedJobType: null,
      jobTypes: ["全职", "兼职", "临时工"],
      responsibilities: "",
      requirements: "",
      workTime: "",
      company: "",
      benefits: "",
      contactName: "",
      contactPhone: "",
      wechatId: ""
    };
  },
  computed: {
    // 根据所选职位动态计算可用的工作类型
    availableJobTypes() {
      if (this.selectedPosition === "sorting" || this.selectedPosition === "delivery") {
        return ["全职", "兼职", "临时工"];
      } else {
        return ["全职", "兼职"];
      }
    }
  },
  watch: {
    // 当职位类型变化时，重置工作类型选择
    selectedPosition(newValue) {
      this.selectedJobType = null;
      this.setTitleBasedOnPosition(newValue);
    }
  },
  methods: {
    // 选择职位类型
    selectPosition(position) {
      this.selectedPosition = position;
    },
    // 根据职位类型设置职位名称
    setTitleBasedOnPosition(position) {
      switch (position) {
        case "sorting":
          this.title = "招聘驿站分拣员";
          break;
        case "delivery":
          this.title = "招聘快递配送员";
          break;
        case "manager":
          this.title = "招聘驿站管理员";
          break;
        case "customer":
          this.title = "招聘驿站客服";
          break;
        case "warehouse":
          this.title = "招聘仓储人员";
          break;
      }
    },
    onSalaryChange(e) {
      this.selectedSalary = e.detail.value;
    },
    onAreaChange(e) {
      this.selectedArea = e.detail.value;
    },
    onJobTypeChange(e) {
      this.selectedJobType = e.detail.value;
    },
    validateForm() {
      if (!this.title.trim()) {
        common_vendor.index.showToast({
          title: "请输入职位名称",
          icon: "none"
        });
        return false;
      }
      if (this.selectedSalary === null) {
        common_vendor.index.showToast({
          title: "请选择薪资范围",
          icon: "none"
        });
        return false;
      }
      if (this.selectedArea === null) {
        common_vendor.index.showToast({
          title: "请选择所在区域",
          icon: "none"
        });
        return false;
      }
      if (!this.address.trim()) {
        common_vendor.index.showToast({
          title: "请输入工作地址",
          icon: "none"
        });
        return false;
      }
      if (this.selectedJobType === null) {
        common_vendor.index.showToast({
          title: "请选择工作类型",
          icon: "none"
        });
        return false;
      }
      if (!this.responsibilities.trim()) {
        common_vendor.index.showToast({
          title: "请描述岗位职责",
          icon: "none"
        });
        return false;
      }
      if (!this.requirements.trim()) {
        common_vendor.index.showToast({
          title: "请描述任职要求",
          icon: "none"
        });
        return false;
      }
      if (!this.contactName.trim()) {
        common_vendor.index.showToast({
          title: "请输入联系人姓名",
          icon: "none"
        });
        return false;
      }
      if (!this.contactPhone.trim()) {
        common_vendor.index.showToast({
          title: "请输入手机号码",
          icon: "none"
        });
        return false;
      }
      return true;
    },
    saveDraft() {
      common_vendor.index.showToast({
        title: "已保存草稿",
        icon: "success"
      });
    },
    publish() {
      if (!this.validateForm()) {
        return;
      }
      common_vendor.index.showLoading({
        title: "发布中..."
      });
      const jobInfo = {
        id: Date.now().toString(),
        // 使用时间戳作为临时ID
        title: this.title,
        salary: this.salaryRanges[this.selectedSalary],
        company: this.company,
        address: this.areas[this.selectedArea] + " " + this.address,
        tags: [
          this.availableJobTypes[this.selectedJobType],
          this.workTime ? "工作时间: " + this.workTime : "",
          this.benefits ? "有福利" : ""
        ].filter((tag) => tag),
        // 过滤掉空字符串
        experience: this.requirements.split("\n")[0] || "经验不限",
        education: "学历不限",
        publishTime: (/* @__PURE__ */ new Date()).toLocaleString(),
        responsibilities: this.responsibilities,
        jobDescription: this.responsibilities,
        // 兼容详情页的jobDescription字段
        benefits: this.benefits,
        workTime: this.workTime,
        jobType: this.availableJobTypes[this.selectedJobType],
        ageRequirement: "不限",
        contactInfo: {
          name: this.contactName,
          phone: this.contactPhone,
          wechat: this.wechatId
        },
        // 添加职位类型信息，用于在列表页筛选
        positionType: this.selectedPosition
      };
      try {
        let jobList = common_vendor.index.getStorageSync("jobList") || [];
        jobList.unshift(jobInfo);
        common_vendor.index.setStorageSync("jobList", jobList);
        common_vendor.index.request({
          url: "/api/job/publish",
          // 使用相对路径，移除this.apiBaseUrl
          method: "POST",
          data: jobInfo,
          success: (res) => {
            console.log("发布成功响应:", res.data);
            if (res.data && res.data.code === 200 && res.data.data && res.data.data.id) {
              jobInfo.id = res.data.data.id;
              common_vendor.index.setStorageSync("jobList", jobList);
            }
          },
          fail: (err) => {
            console.error("API请求失败:", err);
          }
        });
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "发布成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/detail/detail?id=" + jobInfo.id + "&type=job"
          });
        }, 1500);
      } catch (e) {
        console.error("保存工作信息失败:", e);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "发布失败，请重试",
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.selectedPosition === "sorting" ? 1 : "",
    b: common_vendor.o(($event) => $options.selectPosition("sorting")),
    c: $data.selectedPosition === "delivery" ? 1 : "",
    d: common_vendor.o(($event) => $options.selectPosition("delivery")),
    e: $data.selectedPosition === "manager" ? 1 : "",
    f: common_vendor.o(($event) => $options.selectPosition("manager")),
    g: $data.selectedPosition === "customer" ? 1 : "",
    h: common_vendor.o(($event) => $options.selectPosition("customer")),
    i: $data.selectedPosition === "warehouse" ? 1 : "",
    j: common_vendor.o(($event) => $options.selectPosition("warehouse")),
    k: $data.title,
    l: common_vendor.o(($event) => $data.title = $event.detail.value),
    m: $data.selectedSalary !== null
  }, $data.selectedSalary !== null ? {
    n: common_vendor.t($data.salaryRanges[$data.selectedSalary])
  } : {}, {
    o: $data.salaryRanges,
    p: common_vendor.o((...args) => $options.onSalaryChange && $options.onSalaryChange(...args)),
    q: $data.selectedArea !== null
  }, $data.selectedArea !== null ? {
    r: common_vendor.t($data.areas[$data.selectedArea])
  } : {}, {
    s: $data.areas,
    t: common_vendor.o((...args) => $options.onAreaChange && $options.onAreaChange(...args)),
    v: $data.address,
    w: common_vendor.o(($event) => $data.address = $event.detail.value),
    x: $data.selectedJobType !== null
  }, $data.selectedJobType !== null ? {
    y: common_vendor.t($data.jobTypes[$data.selectedJobType])
  } : {}, {
    z: $data.jobTypes,
    A: common_vendor.o((...args) => $options.onJobTypeChange && $options.onJobTypeChange(...args)),
    B: $data.responsibilities,
    C: common_vendor.o(($event) => $data.responsibilities = $event.detail.value),
    D: $data.requirements,
    E: common_vendor.o(($event) => $data.requirements = $event.detail.value),
    F: $data.workTime,
    G: common_vendor.o(($event) => $data.workTime = $event.detail.value),
    H: $data.company,
    I: common_vendor.o(($event) => $data.company = $event.detail.value),
    J: $data.benefits,
    K: common_vendor.o(($event) => $data.benefits = $event.detail.value),
    L: $data.contactName,
    M: common_vendor.o(($event) => $data.contactName = $event.detail.value),
    N: $data.contactPhone,
    O: common_vendor.o(($event) => $data.contactPhone = $event.detail.value),
    P: $data.wechatId,
    Q: common_vendor.o(($event) => $data.wechatId = $event.detail.value),
    R: common_vendor.o((...args) => $options.saveDraft && $options.saveDraft(...args)),
    S: common_vendor.o((...args) => $options.publish && $options.publish(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
