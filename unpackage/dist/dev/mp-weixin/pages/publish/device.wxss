
.container {
		padding-bottom: 120rpx;
		background-color: #f7f7f7;
}
.form-section {
		background-color: #ffffff;
		margin-bottom: 30rpx;
}
.section-title {
		display: flex;
		align-items: center;
		padding: 25rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
}
.title-bar {
		width: 8rpx;
		height: 40rpx;
		background-color: #1989fa;
		margin-right: 20rpx;
		border-radius: 6rpx;
}
.section-title text {
		font-size: 34rpx;
		font-weight: 500;
		color: #333;
}
.form-item {
		padding: 40rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
}
.form-item:last-child {
		border-bottom: none;
}
.label {
		display: flex;
		margin-bottom: 30rpx;
		font-size: 32rpx;
		color: #333;
}
.required {
		color: #ff4d4f;
		margin-left: 6rpx;
}
input, textarea {
		width: 100%;
		background-color: #f7f7f7;
		padding: 0 30rpx;
		font-size: 32rpx;
		border-radius: 12rpx;
		box-sizing: border-box;
		height: 92rpx;
		line-height: 92rpx;
}
.placeholder {
		color: #999;
}
textarea {
		height: 240rpx;
		line-height: 1.5;
		padding: 30rpx;
}
.picker-value {
		background-color: #f7f7f7;
		padding: 30rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		position: relative;
		height: 92rpx;
		line-height: 32rpx;
		box-sizing: border-box;
}
.picker-arrow {
		position: absolute;
		right: 25rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #bbb;
		font-size: 24rpx;
}
.price-input {
		display: flex;
		align-items: center;
		background-color: #f7f7f7;
		border-radius: 12rpx;
		height: 92rpx;
}
.price-symbol {
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333;
}
.price-input input {
		flex: 1;
		background-color: transparent;
		height: 92rpx;
		line-height: 92rpx;
		padding: 0;
}
.upload-area {
		margin-top: 20rpx;
}
.upload-list {
		display: flex;
		flex-wrap: wrap;
}
.upload-item, .upload-btn {
		width: 200rpx;
		height: 200rpx;
		margin-right: 26rpx;
		margin-bottom: 26rpx;
		border-radius: 12rpx;
		overflow: hidden;
}
.upload-item {
		position: relative;
}
.upload-item image {
		width: 100%;
		height: 100%;
}
.delete-btn {
		position: absolute;
		top: 0;
		right: 0;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0,0,0,0.5);
		color: #fff;
		text-align: center;
		line-height: 38rpx;
		font-size: 28rpx;
}
.upload-btn {
		background-color: #f7f7f7;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		border: 1rpx dashed #ddd;
}
.upload-icon {
		font-size: 56rpx;
		color: #bbb;
		margin-bottom: 10rpx;
}
.upload-text {
		font-size: 28rpx;
		color: #bbb;
}
.upload-tips {
		font-size: 28rpx;
		color: #999;
		margin-top: 10rpx;
}
.checkbox-group {
		display: flex;
		flex-wrap: wrap;
}
.checkbox-item {
		display: flex;
		align-items: center;
		margin-right: 40rpx;
		margin-bottom: 20rpx;
		font-size: 32rpx;
}
.checkbox {
		width: 36rpx;
		height: 36rpx;
		border: 1rpx solid #ddd;
		border-radius: 36rpx;
		margin-right: 10rpx;
		position: relative;
}
.checkbox-item.checked .checkbox:after {
		content: "";
		position: absolute;
		width: 24rpx;
		height: 24rpx;
		background-color: #1989fa;
		border-radius: 24rpx;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
}
.button-group {
		display: flex;
		padding: 25rpx 30rpx 40rpx;
		background-color: #fff;
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 10;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.btn-draft, .btn-publish {
		flex: 1;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 32rpx;
		border-radius: 45rpx;
}
.btn-draft {
		background-color: #f5f5f5;
		color: #666;
		margin-right: 20rpx;
}
.btn-publish {
		background-color: #1989fa;
		color: #fff;
}
