"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {};
  },
  methods: {
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0,
    b: common_vendor.o(($event) => $options.navigateTo("/pages/publish/station")),
    c: common_assets._imports_5,
    d: common_vendor.o(($event) => $options.navigateTo("/pages/publish/service")),
    e: common_assets._imports_2,
    f: common_vendor.o(($event) => $options.navigateTo("/pages/publish/job")),
    g: common_assets._imports_3,
    h: common_vendor.o(($event) => $options.navigateTo("/pages/publish/device")),
    i: common_assets._imports_4$2
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
