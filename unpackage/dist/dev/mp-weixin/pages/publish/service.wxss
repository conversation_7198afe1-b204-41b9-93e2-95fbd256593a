
.container {
		padding-bottom: 120rpx;
		background-color: #f7f7f7;
}
.form-section {
		background-color: #ffffff;
		margin-bottom: 30rpx;
}
.section-title {
		display: flex;
		align-items: center;
		padding: 25rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
}
.title-bar {
		width: 8rpx;
		height: 40rpx;
		background-color: #1989fa;
		margin-right: 20rpx;
		border-radius: 6rpx;
}
.section-title text {
		font-size: 34rpx;
		font-weight: 500;
		color: #333;
}
.form-item {
		padding: 40rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
}
.form-item:last-child {
		border-bottom: none;
}
.label {
		display: flex;
		margin-bottom: 30rpx;
		font-size: 32rpx;
		color: #333;
}
.required {
		color: #ff4d4f;
		margin-left: 6rpx;
}
input, textarea {
		width: 100%;
		background-color: #f7f7f7;
		padding: 0 30rpx;
		font-size: 32rpx;
		border-radius: 12rpx;
		box-sizing: border-box;
		height: 92rpx;
		line-height: 92rpx;
}
.placeholder {
		color: #999;
}
textarea {
		height: 240rpx;
		line-height: 1.5;
		padding: 30rpx;
}
.picker-value {
		background-color: #f7f7f7;
		padding: 30rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		position: relative;
		height: 92rpx;
		line-height: 32rpx;
		box-sizing: border-box;
}
.picker-arrow {
		position: absolute;
		right: 25rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #bbb;
		font-size: 24rpx;
}
.price-input {
		display: flex;
		align-items: center;
		background-color: #f7f7f7;
		border-radius: 12rpx;
		height: 92rpx;
}
.price-symbol {
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333;
}
.price-input input {
		flex: 1;
		background-color: transparent;
		height: 92rpx;
		line-height: 92rpx;
		padding: 0;
}
.date-select {
		display: flex;
		align-items: center;
}
.date-picker {
		flex: 1;
		background-color: #f7f7f7;
		padding: 30rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		height: 92rpx;
		line-height: 32rpx;
		box-sizing: border-box;
}
.date-separator {
		padding: 0 20rpx;
		font-size: 32rpx;
		color: #333;
}
.button-group {
		display: flex;
		padding: 25rpx 30rpx 40rpx;
		background-color: #fff;
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 10;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.btn-draft, .btn-publish {
		flex: 1;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 32rpx;
		border-radius: 45rpx;
}
.btn-draft {
		background-color: #f5f5f5;
		color: #666;
		margin-right: 20rpx;
}
.btn-publish {
		background-color: #1989fa;
		color: #fff;
}
