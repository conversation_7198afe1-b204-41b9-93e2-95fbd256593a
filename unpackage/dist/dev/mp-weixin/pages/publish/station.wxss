
.container {
		padding-bottom: 120rpx;
		padding-top: 100rpx; /* 为顶部导航栏留出空间 */
		background-color: #f7f7f7;
}
	
	/* 导航栏样式 */
.nav-bar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #ffffff;
		padding: 0 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		z-index: 100;
}
.nav-back {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-icon {
		font-size: 40rpx;
		font-weight: bold;
		color: #333333;
}
.nav-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
}
.nav-placeholder {
		width: 60rpx;
}
.form-block {
		background-color: #ffffff;
		margin-bottom: 30rpx;
}
.block-title {
		display: flex;
		align-items: center;
		padding: 25rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
}
.title-bar {
		width: 8rpx;
		height: 40rpx;
		background-color: #1989fa;
		margin-right: 20rpx;
		border-radius: 6rpx;
}
.block-title text {
		font-size: 34rpx;
		font-weight: 500;
		color: #333;
}
.form-item {
		padding: 40rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
}
.form-item:last-child {
		border-bottom: none;
}
.label {
		display: flex;
		margin-bottom: 30rpx;
		align-items: center;
}
.label text {
		font-size: 32rpx;
		color: #333;
}
.label .required {
		color: #ff5a5f;
		margin-left: 6rpx;
}
.label .tip {
		font-size: 24rpx;
		color: #999999;
		margin-left: 10rpx;
}
input, textarea {
		width: 100%;
		background-color: #f7f7f7;
		padding: 0 30rpx;
		font-size: 32rpx;
		border-radius: 12rpx;
		box-sizing: border-box;
		height: 92rpx;
		line-height: 92rpx;
}
.placeholder {
		color: #999;
}
textarea {
		height: 240rpx;
		line-height: 1.5;
		padding: 30rpx;
}
.picker-box {
		background-color: #f7f7f7;
		padding: 30rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		position: relative;
		height: 92rpx;
		line-height: 32rpx;
		box-sizing: border-box;
}
.picker-arrow {
		position: absolute;
		right: 25rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #bbb;
		font-size: 24rpx;
}
.price-input, .area-input {
		display: flex;
		align-items: center;
		background-color: #f7f7f7;
		border-radius: 12rpx;
		height: 92rpx;
}
.price-symbol {
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333;
}
.area-unit {
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333;
}
.price-input input, .area-input input {
		flex: 1;
		background-color: transparent;
		height: 92rpx;
		line-height: 92rpx;
		padding: 0;
}
.upload-list {
		display: flex;
		flex-wrap: wrap;
}
.upload-item, .upload-btn {
		width: 200rpx;
		height: 200rpx;
		margin-right: 26rpx;
		margin-bottom: 26rpx;
		border-radius: 12rpx;
		overflow: hidden;
}
.upload-item {
		position: relative;
}
.upload-item image {
		width: 100%;
		height: 100%;
}
.delete-btn {
		position: absolute;
		top: 0;
		right: 0;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0,0,0,0.5);
		color: #fff;
		text-align: center;
		line-height: 38rpx;
		font-size: 28rpx;
}
.upload-btn {
		background-color: #f7f7f7;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		border: 1rpx dashed #ddd;
}
.upload-icon {
		font-size: 56rpx;
		color: #bbb;
		margin-bottom: 10rpx;
}
.upload-text {
		font-size: 28rpx;
		color: #bbb;
}
.upload-area {
		position: relative;
}
.upload-hint {
		position: absolute;
		top: 0;
		right: 0;
		background-color: rgba(0,0,0,0.5);
		color: #fff;
		padding: 5rpx 10rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
}
.footer-actions {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		padding: 25rpx 30rpx 40rpx;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 10;
}
.btn-draft, .btn-publish {
		flex: 1;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 32rpx;
		border-radius: 45rpx;
}
.btn-draft {
		background-color: #f5f5f5;
		color: #666;
		margin-right: 20rpx;
}
.btn-publish {
		background-color: #1989fa;
		color: #fff;
}
