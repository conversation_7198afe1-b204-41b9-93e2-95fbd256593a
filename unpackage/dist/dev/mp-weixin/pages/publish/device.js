"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      title: "",
      price: "",
      selectedDeviceType: null,
      deviceTypes: ["扫描设备", "打印设备", "货架柜子", "电子秤", "运输工具", "其他设备"],
      brand: "",
      selectedCondition: null,
      conditions: ["全新", "9成新", "8成新", "7成新", "6成新", "5成新及以下"],
      selectedArea: null,
      areas: ["海淀区", "朝阳区", "西城区", "东城区", "丰台区", "石景山区", "通州区", "昌平区", "大兴区", "顺义区", "房山区", "门头沟区", "平谷区", "密云区", "怀柔区", "延庆区"],
      address: "",
      description: "",
      uploadImages: [],
      tradeWays: ["self"],
      // 默认选中自提
      contactName: "",
      contactPhone: "",
      wechatId: ""
    };
  },
  methods: {
    onDeviceTypeChange(e) {
      this.selectedDeviceType = e.detail.value;
    },
    onConditionChange(e) {
      this.selectedCondition = e.detail.value;
    },
    onAreaChange(e) {
      this.selectedArea = e.detail.value;
    },
    chooseImage() {
      const that = this;
      common_vendor.index.chooseImage({
        count: 6 - that.uploadImages.length,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: function(res) {
          that.uploadImages = [...that.uploadImages, ...res.tempFilePaths];
        }
      });
    },
    deleteImage(index) {
      this.uploadImages.splice(index, 1);
    },
    toggleTradeWay(way) {
      const index = this.tradeWays.indexOf(way);
      if (index === -1) {
        this.tradeWays.push(way);
      } else {
        if (this.tradeWays.length > 1) {
          this.tradeWays.splice(index, 1);
        }
      }
    },
    validateForm() {
      if (!this.title.trim()) {
        common_vendor.index.showToast({
          title: "请输入设备名称",
          icon: "none"
        });
        return false;
      }
      if (!this.price.trim()) {
        common_vendor.index.showToast({
          title: "请输入设备价格",
          icon: "none"
        });
        return false;
      }
      if (this.selectedDeviceType === null) {
        common_vendor.index.showToast({
          title: "请选择设备类型",
          icon: "none"
        });
        return false;
      }
      if (this.selectedCondition === null) {
        common_vendor.index.showToast({
          title: "请选择新旧程度",
          icon: "none"
        });
        return false;
      }
      if (this.selectedArea === null) {
        common_vendor.index.showToast({
          title: "请选择所在区域",
          icon: "none"
        });
        return false;
      }
      if (!this.description.trim()) {
        common_vendor.index.showToast({
          title: "请输入设备描述",
          icon: "none"
        });
        return false;
      }
      if (this.uploadImages.length === 0) {
        common_vendor.index.showToast({
          title: "请上传设备照片",
          icon: "none"
        });
        return false;
      }
      if (!this.contactName.trim()) {
        common_vendor.index.showToast({
          title: "请输入联系人姓名",
          icon: "none"
        });
        return false;
      }
      if (!this.contactPhone.trim()) {
        common_vendor.index.showToast({
          title: "请输入手机号码",
          icon: "none"
        });
        return false;
      }
      return true;
    },
    saveDraft() {
      common_vendor.index.showToast({
        title: "已保存草稿",
        icon: "success"
      });
    },
    publish() {
      if (this.validateForm()) {
        common_vendor.index.showLoading({
          title: "发布中..."
        });
        const newDeviceInfo = {
          id: Date.now(),
          // 使用时间戳作为临时ID
          title: this.title,
          price: this.price,
          location: "北京·" + this.areas[this.selectedArea],
          type: this.deviceTypes[this.selectedDeviceType],
          images: this.uploadImages.length > 0 ? this.uploadImages : ["/static/device/default.jpg"],
          condition: this.conditions[this.selectedCondition],
          publishTime: "刚刚发布",
          contactInfo: {
            name: this.contactName,
            phone: this.contactPhone,
            wechat: this.wechatId || "未提供"
          }
        };
        let deviceList = common_vendor.index.getStorageSync("deviceList") || [];
        deviceList.unshift(newDeviceInfo);
        common_vendor.index.setStorageSync("deviceList", deviceList);
        setTimeout(() => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "发布成功",
            icon: "success",
            duration: 1500
          });
          setTimeout(() => {
            common_vendor.index.navigateTo({
              url: "/pages/device/device"
            });
          }, 1500);
        }, 1e3);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.title,
    b: common_vendor.o(($event) => $data.title = $event.detail.value),
    c: $data.price,
    d: common_vendor.o(($event) => $data.price = $event.detail.value),
    e: $data.selectedDeviceType !== null
  }, $data.selectedDeviceType !== null ? {
    f: common_vendor.t($data.deviceTypes[$data.selectedDeviceType])
  } : {}, {
    g: $data.deviceTypes,
    h: common_vendor.o((...args) => $options.onDeviceTypeChange && $options.onDeviceTypeChange(...args)),
    i: $data.brand,
    j: common_vendor.o(($event) => $data.brand = $event.detail.value),
    k: $data.selectedCondition !== null
  }, $data.selectedCondition !== null ? {
    l: common_vendor.t($data.conditions[$data.selectedCondition])
  } : {}, {
    m: $data.conditions,
    n: common_vendor.o((...args) => $options.onConditionChange && $options.onConditionChange(...args)),
    o: $data.selectedArea !== null
  }, $data.selectedArea !== null ? {
    p: common_vendor.t($data.areas[$data.selectedArea])
  } : {}, {
    q: $data.areas,
    r: common_vendor.o((...args) => $options.onAreaChange && $options.onAreaChange(...args)),
    s: $data.address,
    t: common_vendor.o(($event) => $data.address = $event.detail.value),
    v: $data.description,
    w: common_vendor.o(($event) => $data.description = $event.detail.value),
    x: common_vendor.f($data.uploadImages, (item, index, i0) => {
      return {
        a: item,
        b: common_vendor.o(($event) => $options.deleteImage(index), index),
        c: index
      };
    }),
    y: $data.uploadImages.length < 6
  }, $data.uploadImages.length < 6 ? {
    z: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}, {
    A: $data.tradeWays.includes("self") ? 1 : "",
    B: common_vendor.o(($event) => $options.toggleTradeWay("self")),
    C: $data.tradeWays.includes("deliver") ? 1 : "",
    D: common_vendor.o(($event) => $options.toggleTradeWay("deliver")),
    E: $data.tradeWays.includes("express") ? 1 : "",
    F: common_vendor.o(($event) => $options.toggleTradeWay("express")),
    G: $data.contactName,
    H: common_vendor.o(($event) => $data.contactName = $event.detail.value),
    I: $data.contactPhone,
    J: common_vendor.o(($event) => $data.contactPhone = $event.detail.value),
    K: $data.wechatId,
    L: common_vendor.o(($event) => $data.wechatId = $event.detail.value),
    M: common_vendor.o((...args) => $options.saveDraft && $options.saveDraft(...args)),
    N: common_vendor.o((...args) => $options.publish && $options.publish(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
