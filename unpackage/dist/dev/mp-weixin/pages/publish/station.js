"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      stationName: "",
      price: "",
      stationType: null,
      stationTypes: ["社区驿站", "校园驿站", "商场驿站", "门店驿站", "其他类型"],
      selectedArea: null,
      areas: ["海淀区", "朝阳区", "西城区", "东城区", "丰台区", "石景山区", "通州区", "昌平区", "大兴区", "顺义区", "房山区", "门头沟区", "平谷区", "密云区", "怀柔区", "延庆区"],
      address: "",
      area: "",
      monthlyIncome: "",
      dailyPackages: "",
      advantage: "",
      reason: "",
      uploadImages: [],
      contactName: "",
      contactPhone: "",
      wechatId: ""
    };
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    },
    onStationTypeChange(e) {
      this.stationType = e.detail.value;
    },
    onAreaChange(e) {
      this.selectedArea = e.detail.value;
    },
    chooseImage() {
      const maxImages = 5;
      const currentCount = this.uploadImages.length;
      const remainCount = maxImages - currentCount;
      if (remainCount <= 0) {
        common_vendor.index.showToast({
          title: "最多只能上传5张图片",
          icon: "none"
        });
        return;
      }
      common_vendor.index.chooseImage({
        count: remainCount,
        // 最多可以选择的图片张数
        sizeType: ["compressed"],
        // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ["album", "camera"],
        // 从相册选择或使用相机拍摄
        success: (res) => {
          this.uploadImages = [...this.uploadImages, ...res.tempFilePaths];
        }
      });
    },
    deleteImage(index) {
      this.uploadImages.splice(index, 1);
    },
    validateForm() {
      if (!this.stationName.trim()) {
        common_vendor.index.showToast({
          title: "请输入驿站名称",
          icon: "none"
        });
        return false;
      }
      if (!this.price.trim()) {
        common_vendor.index.showToast({
          title: "请输入转让价格",
          icon: "none"
        });
        return false;
      }
      if (this.stationType === null) {
        common_vendor.index.showToast({
          title: "请选择驿站类型",
          icon: "none"
        });
        return false;
      }
      if (this.selectedArea === null) {
        common_vendor.index.showToast({
          title: "请选择所在区域",
          icon: "none"
        });
        return false;
      }
      if (!this.address.trim()) {
        common_vendor.index.showToast({
          title: "请输入详细地址",
          icon: "none"
        });
        return false;
      }
      if (!this.area.trim()) {
        common_vendor.index.showToast({
          title: "请输入驿站面积",
          icon: "none"
        });
        return false;
      }
      if (!this.contactName.trim()) {
        common_vendor.index.showToast({
          title: "请输入联系人姓名",
          icon: "none"
        });
        return false;
      }
      if (!this.contactPhone.trim()) {
        common_vendor.index.showToast({
          title: "请输入手机号码",
          icon: "none"
        });
        return false;
      }
      return true;
    },
    saveDraft() {
      common_vendor.index.showToast({
        title: "已保存草稿",
        icon: "success"
      });
    },
    publish() {
      if (this.validateForm()) {
        common_vendor.index.showLoading({
          title: "发布中..."
        });
        const newTransferInfo = {
          id: Date.now(),
          // 使用时间戳作为临时ID
          title: this.stationName,
          price: this.price,
          priceUnit: "万元",
          address: this.areas[this.selectedArea] + " " + this.address,
          area: this.areas[this.selectedArea],
          space: this.area,
          package: this.dailyPackages,
          type: this.stationTypes[this.stationType],
          tags: [this.stationTypes[this.stationType], "新发布", "待转让"],
          advantage: this.advantage || "无详细描述",
          reason: this.reason || "无详细原因",
          description: this.advantage + "\n" + this.reason,
          // 保存所有上传的图片数组
          images: this.uploadImages.length > 0 ? [...this.uploadImages] : ["/static/images/station-default.jpg"],
          // 保留image字段用于兼容
          image: this.uploadImages.length > 0 ? this.uploadImages[0] : "/static/images/station-default.jpg",
          publishTime: "刚刚发布",
          contactInfo: {
            name: this.contactName,
            phone: this.contactPhone,
            wechat: this.wechatId || "未提供"
          }
        };
        let transferList = common_vendor.index.getStorageSync("transferList") || [];
        transferList.unshift(newTransferInfo);
        common_vendor.index.setStorageSync("transferList", transferList);
        setTimeout(() => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "发布成功",
            icon: "success",
            duration: 1500
          });
          setTimeout(() => {
            common_vendor.index.redirectTo({
              url: "/pages/transfer/transfer"
            });
          }, 1500);
        }, 1e3);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    b: $data.stationName,
    c: common_vendor.o(($event) => $data.stationName = $event.detail.value),
    d: $data.price,
    e: common_vendor.o(($event) => $data.price = $event.detail.value),
    f: $data.stationType !== null
  }, $data.stationType !== null ? {
    g: common_vendor.t($data.stationTypes[$data.stationType])
  } : {}, {
    h: $data.stationTypes,
    i: common_vendor.o((...args) => $options.onStationTypeChange && $options.onStationTypeChange(...args)),
    j: $data.selectedArea !== null
  }, $data.selectedArea !== null ? {
    k: common_vendor.t($data.areas[$data.selectedArea])
  } : {}, {
    l: $data.areas,
    m: common_vendor.o((...args) => $options.onAreaChange && $options.onAreaChange(...args)),
    n: $data.address,
    o: common_vendor.o(($event) => $data.address = $event.detail.value),
    p: $data.area,
    q: common_vendor.o(($event) => $data.area = $event.detail.value),
    r: $data.monthlyIncome,
    s: common_vendor.o(($event) => $data.monthlyIncome = $event.detail.value),
    t: $data.dailyPackages,
    v: common_vendor.o(($event) => $data.dailyPackages = $event.detail.value),
    w: $data.advantage,
    x: common_vendor.o(($event) => $data.advantage = $event.detail.value),
    y: $data.reason,
    z: common_vendor.o(($event) => $data.reason = $event.detail.value),
    A: common_vendor.f($data.uploadImages, (item, index, i0) => {
      return {
        a: item,
        b: common_vendor.o(($event) => $options.deleteImage(index), index),
        c: index
      };
    }),
    B: $data.uploadImages.length < 5
  }, $data.uploadImages.length < 5 ? {
    C: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}, {
    D: $data.uploadImages.length > 0
  }, $data.uploadImages.length > 0 ? {
    E: common_vendor.t($data.uploadImages.length)
  } : {}, {
    F: $data.contactName,
    G: common_vendor.o(($event) => $data.contactName = $event.detail.value),
    H: $data.contactPhone,
    I: common_vendor.o(($event) => $data.contactPhone = $event.detail.value),
    J: $data.wechatId,
    K: common_vendor.o(($event) => $data.wechatId = $event.detail.value),
    L: common_vendor.o((...args) => $options.saveDraft && $options.saveDraft(...args)),
    M: common_vendor.o((...args) => $options.publish && $options.publish(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
