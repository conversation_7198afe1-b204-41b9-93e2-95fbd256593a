"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    const now = /* @__PURE__ */ new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, "0");
    const day = now.getDate().toString().padStart(2, "0");
    const today = `${year}-${month}-${day}`;
    return {
      title: "",
      price: "",
      selectedArea: null,
      areas: ["海淀区", "朝阳区", "西城区", "东城区", "丰台区", "石景山区", "通州区", "昌平区", "大兴区", "顺义区", "房山区", "门头沟区", "平谷区", "密云区", "怀柔区", "延庆区"],
      address: "",
      selectedWorkTime: null,
      workTimes: ["全天班（8-10小时）", "上午班（4-5小时）", "下午班（4-5小时）", "晚班（4-5小时）", "其他时间"],
      today,
      startDate: "",
      endDate: "",
      selectedJobType: null,
      jobTypes: ["快递员", "分拣员", "驿站业务员", "仓库管理员", "其他"],
      selectedCompany: null,
      expressCompanies: ["顺丰速运", "中通快递", "圆通速递", "申通快递", "韵达快递", "百世快递", "京东物流", "邮政EMS", "其他"],
      dailyPackages: "",
      description: "",
      contactName: "",
      contactPhone: "",
      wechatId: ""
    };
  },
  methods: {
    onAreaChange(e) {
      this.selectedArea = e.detail.value;
    },
    onWorkTimeChange(e) {
      this.selectedWorkTime = e.detail.value;
    },
    onStartDateChange(e) {
      this.startDate = e.detail.value;
      if (this.endDate && this.endDate < this.startDate) {
        this.endDate = "";
      }
    },
    onEndDateChange(e) {
      this.endDate = e.detail.value;
    },
    onJobTypeChange(e) {
      this.selectedJobType = e.detail.value;
    },
    onCompanyChange(e) {
      this.selectedCompany = e.detail.value;
    },
    validateForm() {
      if (!this.title.trim()) {
        common_vendor.index.showToast({
          title: "请输入发布标题",
          icon: "none"
        });
        return false;
      }
      if (!this.price.trim()) {
        common_vendor.index.showToast({
          title: "请输入顶班价格",
          icon: "none"
        });
        return false;
      }
      if (this.selectedArea === null) {
        common_vendor.index.showToast({
          title: "请选择所在区域",
          icon: "none"
        });
        return false;
      }
      if (!this.address.trim()) {
        common_vendor.index.showToast({
          title: "请输入具体位置",
          icon: "none"
        });
        return false;
      }
      if (this.selectedWorkTime === null) {
        common_vendor.index.showToast({
          title: "请选择工作时间",
          icon: "none"
        });
        return false;
      }
      if (!this.startDate) {
        common_vendor.index.showToast({
          title: "请选择开始日期",
          icon: "none"
        });
        return false;
      }
      if (!this.endDate) {
        common_vendor.index.showToast({
          title: "请选择结束日期",
          icon: "none"
        });
        return false;
      }
      if (this.selectedJobType === null) {
        common_vendor.index.showToast({
          title: "请选择工作类型",
          icon: "none"
        });
        return false;
      }
      if (!this.contactName.trim()) {
        common_vendor.index.showToast({
          title: "请输入联系人姓名",
          icon: "none"
        });
        return false;
      }
      if (!this.contactPhone.trim()) {
        common_vendor.index.showToast({
          title: "请输入手机号码",
          icon: "none"
        });
        return false;
      }
      return true;
    },
    saveDraft() {
      common_vendor.index.showToast({
        title: "已保存草稿",
        icon: "success"
      });
    },
    publish() {
      if (this.validateForm()) {
        common_vendor.index.showLoading({
          title: "发布中..."
        });
        const newServiceInfo = {
          id: Date.now(),
          // 使用时间戳作为临时ID
          title: this.title,
          price: this.price + "元/天",
          time: this.workTimes[this.selectedWorkTime],
          date: this.startDate + " 至 " + this.endDate,
          address: this.areas[this.selectedArea] + " " + this.address,
          tags: [
            this.jobTypes[this.selectedJobType],
            this.expressCompanies[this.selectedCompany] || "无指定公司",
            "日均" + (this.dailyPackages || "不详") + "件"
          ],
          description: this.description || "无详细描述",
          publishTime: "刚刚发布",
          contactInfo: {
            name: this.contactName,
            phone: this.contactPhone,
            wechat: this.wechatId || "未提供"
          }
        };
        let serviceList = common_vendor.index.getStorageSync("serviceList") || [];
        serviceList.unshift(newServiceInfo);
        common_vendor.index.setStorageSync("serviceList", serviceList);
        setTimeout(() => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "发布成功",
            icon: "success",
            duration: 1500
          });
          setTimeout(() => {
            common_vendor.index.navigateTo({
              url: "/pages/service/service"
            });
          }, 1500);
        }, 1e3);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.title,
    b: common_vendor.o(($event) => $data.title = $event.detail.value),
    c: $data.price,
    d: common_vendor.o(($event) => $data.price = $event.detail.value),
    e: $data.selectedArea
  }, $data.selectedArea ? {
    f: common_vendor.t($data.areas[$data.selectedArea])
  } : {}, {
    g: $data.areas,
    h: common_vendor.o((...args) => $options.onAreaChange && $options.onAreaChange(...args)),
    i: $data.address,
    j: common_vendor.o(($event) => $data.address = $event.detail.value),
    k: $data.selectedWorkTime !== null
  }, $data.selectedWorkTime !== null ? {
    l: common_vendor.t($data.workTimes[$data.selectedWorkTime])
  } : {}, {
    m: $data.workTimes,
    n: common_vendor.o((...args) => $options.onWorkTimeChange && $options.onWorkTimeChange(...args)),
    o: $data.startDate
  }, $data.startDate ? {
    p: common_vendor.t($data.startDate)
  } : {}, {
    q: $data.startDate,
    r: $data.today,
    s: common_vendor.o((...args) => $options.onStartDateChange && $options.onStartDateChange(...args)),
    t: $data.endDate
  }, $data.endDate ? {
    v: common_vendor.t($data.endDate)
  } : {}, {
    w: $data.endDate,
    x: $data.startDate || $data.today,
    y: common_vendor.o((...args) => $options.onEndDateChange && $options.onEndDateChange(...args)),
    z: $data.selectedJobType !== null
  }, $data.selectedJobType !== null ? {
    A: common_vendor.t($data.jobTypes[$data.selectedJobType])
  } : {}, {
    B: $data.jobTypes,
    C: common_vendor.o((...args) => $options.onJobTypeChange && $options.onJobTypeChange(...args)),
    D: $data.selectedCompany !== null
  }, $data.selectedCompany !== null ? {
    E: common_vendor.t($data.expressCompanies[$data.selectedCompany])
  } : {}, {
    F: $data.expressCompanies,
    G: common_vendor.o((...args) => $options.onCompanyChange && $options.onCompanyChange(...args)),
    H: $data.dailyPackages,
    I: common_vendor.o(($event) => $data.dailyPackages = $event.detail.value),
    J: $data.description,
    K: common_vendor.o(($event) => $data.description = $event.detail.value),
    L: $data.contactName,
    M: common_vendor.o(($event) => $data.contactName = $event.detail.value),
    N: $data.contactPhone,
    O: common_vendor.o(($event) => $data.contactPhone = $event.detail.value),
    P: $data.wechatId,
    Q: common_vendor.o(($event) => $data.wechatId = $event.detail.value),
    R: common_vendor.o((...args) => $options.saveDraft && $options.saveDraft(...args)),
    S: common_vendor.o((...args) => $options.publish && $options.publish(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
