
.container {
		padding: 20rpx;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
}
.city-selector {
		font-size: 32rpx;
		font-weight: bold;
		padding-right: 20rpx;
}
.search-bar {
		flex: 1;
		background-color: #eee;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		height: 60rpx;
}
.search-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
}
.search-placeholder {
		color: #999;
		font-size: 28rpx;
}
	
	/* 搜索面板样式 */
.search-panel {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		z-index: 999;
		padding: 20rpx;
		display: flex;
		flex-direction: column;
}
.search-panel-header {
		display: flex;
		align-items: center;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
}
.search-input-container {
		flex: 1;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		padding: 10rpx 20rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		height: 60rpx;
}
.search-input-container input {
		flex: 1;
		font-size: 28rpx;
		height: 60rpx;
}
.clear-btn {
		width: 40rpx;
		height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.clear-btn image {
		width: 24rpx;
		height: 24rpx;
}
.cancel-btn {
		font-size: 28rpx;
		color: #333;
}
.search-panel-content {
		flex: 1;
		overflow-y: auto;
		padding-top: 20rpx;
}
.section-title {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
}
.result-list {
		margin-bottom: 30rpx;
}
.result-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
}
.result-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 20rpx;
}
.result-text {
		font-size: 28rpx;
		color: #333;
}
.no-results {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 60rpx 0;
}
.no-result-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
}
.no-result-text {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
}
.no-result-tip {
		font-size: 26rpx;
		color: #999;
}
.history-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
}
.clear-history {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #999;
}
.clear-history image {
		width: 24rpx;
		height: 24rpx;
		margin-right: 6rpx;
}
.history-list {
		margin-bottom: 30rpx;
}
.history-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
}
.history-icon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 20rpx;
}
.history-item text {
		font-size: 28rpx;
		color: #333;
}
.hot-list {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 30rpx;
}
.hot-item {
		margin: 0 20rpx 20rpx 0;
		padding: 10rpx 20rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		font-size: 24rpx;
		color: #666;
		display: flex;
		align-items: center;
		position: relative;
}
.hot-rank {
		width: 30rpx;
		height: 30rpx;
		border-radius: 50%;
		font-size: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 10rpx;
		color: #fff;
}
.rank-1 {
		background-color: #ff5a5f;
}
.rank-2 {
		background-color: #ff9500;
}
.rank-3 {
		background-color: #34c759;
}
.banner {
		width: 100%;
		height: 300rpx;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 30rpx;
}
.banner image {
		width: 100%;
		height: 100%;
}
.nav-section {
		display: flex;
		justify-content: space-between;
		padding: 30rpx 0;
		background-color: #fff;
		border-radius: 12rpx;
		margin-bottom: 30rpx;
}
.nav-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 25%;
}
.nav-item image {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 10rpx;
}
.nav-item text {
		font-size: 26rpx;
		color: #333;
}
.section {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
}
.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
}
.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		position: relative;
		padding-left: 20rpx;
}
.section-title::before {
		content: '';
		position: absolute;
		left: 0;
		top: 6rpx;
		width: 6rpx;
		height: 30rpx;
		background-color: #ff5a5f;
		border-radius: 3rpx;
}
.view-more {
		font-size: 24rpx;
		color: #999;
}
.station-list, .job-list, .service-list {
		display: flex;
		flex-direction: column;
}
.station-item {
		display: flex;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
}
.station-image {
		width: 180rpx;
		height: 140rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
}
.station-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
}
.station-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
}
.station-address {
		font-size: 24rpx;
		color: #666;
}
.station-price {
		font-size: 30rpx;
		color: #ff5a5f;
		font-weight: bold;
}
.price-unit {
		font-size: 22rpx;
		font-weight: normal;
		margin-left: 6rpx;
}
.job-item {
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
}
.job-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
}
.job-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
}
.job-salary {
		font-size: 26rpx;
		color: #ff5a5f;
}
.job-company {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 10rpx;
}
.job-tags {
		display: flex;
		flex-wrap: wrap;
}
.job-tag {
		font-size: 22rpx;
		color: #666;
		padding: 4rpx 12rpx;
		background-color: #f5f5f5;
		border-radius: 4rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
}
.service-item {
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.service-info {
		flex: 1;
}
.service-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
}
.service-time {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 10rpx;
}
.service-address {
		font-size: 24rpx;
		color: #999;
}
.service-price {
		font-size: 26rpx;
		color: #ff5a5f;
}
