<view class="container"><view class="header"><view class="city-selector">北京市</view><view class="search-bar" bindtap="{{b}}"><image src="{{a}}" mode="aspectFit" class="search-icon"></image><text class="search-placeholder">搜索驿站/设备/职位</text></view></view><view wx:if="{{c}}" class="search-panel"><view class="search-panel-header"><view class="search-input-container"><image class="search-icon" src="{{d}}" mode="aspectFit"></image><input type="text" placeholder="搜索驿站/设备/职位" confirm-type="search" focus bindconfirm="{{e}}" bindinput="{{f}}" value="{{g}}"/><view wx:if="{{h}}" class="clear-btn" catchtap="{{j}}"><image src="{{i}}" mode="aspectFit"></image></view></view><view class="cancel-btn" bindtap="{{k}}"><text>取消</text></view></view><view class="search-panel-content"><view wx:if="{{l}}" class="search-results"><view class="section-title">搜索结果</view><view class="result-list"><view wx:for="{{m}}" wx:for-item="item" wx:key="b" class="result-item" bindtap="{{item.c}}"><image src="{{n}}" mode="aspectFit" class="result-icon"></image><rich-text class="result-text" nodes="{{item.a}}"></rich-text></view></view></view><view wx:if="{{o}}" class="no-results"><image src="{{p}}" mode="aspectFit" class="no-result-icon"></image><text class="no-result-text">未找到与"{{q}}"相关的内容</text><text class="no-result-tip">换个关键词试试吧</text></view><view wx:if="{{r}}" class="search-history"><view class="history-header"><text class="section-title">搜索历史</text><view class="clear-history" bindtap="{{t}}"><image src="{{s}}" mode="aspectFit"></image><text>清空</text></view></view><view class="history-list"><view wx:for="{{v}}" wx:for-item="item" wx:key="b" class="history-item" bindtap="{{item.c}}"><image class="history-icon" src="{{w}}" mode="aspectFit"></image><text>{{item.a}}</text></view></view></view><view wx:if="{{x}}" class="hot-searches"><view class="section-title">热门搜索</view><view class="hot-list"><view wx:for="{{y}}" wx:for-item="item" wx:key="e" class="hot-item" bindtap="{{item.f}}"><view wx:if="{{item.a}}" class="{{['hot-rank', item.c]}}">{{item.b}}</view><text>{{item.d}}</text></view></view></view></view></view><swiper class="banner" indicator-dots autoplay circular><swiper-item wx:for="{{z}}" wx:for-item="item" wx:key="b"><image src="{{item.a}}" mode="aspectFill"></image></swiper-item></swiper><view class="nav-section"><view class="nav-item" bindtap="{{B}}"><image src="{{A}}" mode="aspectFit"></image><text>驿站转让</text></view><view class="nav-item" bindtap="{{D}}"><image src="{{C}}" mode="aspectFit"></image><text>设备交易</text></view><view class="nav-item" bindtap="{{F}}"><image src="{{E}}" mode="aspectFit"></image><text>招聘求职</text></view><view class="nav-item" bindtap="{{H}}"><image src="{{G}}" mode="aspectFit"></image><text>顶班服务</text></view></view><view class="section"><view class="section-header"><text class="section-title">驿站转让</text><view class="view-more" bindtap="{{I}}">查看更多 ></view></view><view class="station-list"><view wx:for="{{J}}" wx:for-item="item" wx:key="f" class="station-item" bindtap="{{item.g}}"><image src="{{item.a}}" mode="aspectFill" class="station-image"></image><view class="station-info"><view class="station-title">{{item.b}}</view><view class="station-address">{{item.c}}</view><view class="station-price">¥{{item.d}}<text class="price-unit">{{item.e}}</text></view></view></view></view></view><view class="section"><view class="section-header"><text class="section-title">招聘求职</text><view class="view-more" bindtap="{{K}}">查看更多 ></view></view><view class="job-list"><view wx:for="{{L}}" wx:for-item="item" wx:key="e" class="job-item" bindtap="{{item.f}}"><view class="job-header"><view class="job-title">{{item.a}}</view><view class="job-salary">{{item.b}}</view></view><view class="job-company">{{item.c}}</view><view class="job-tags"><text wx:for="{{item.d}}" wx:for-item="tag" wx:key="b" class="job-tag">{{tag.a}}</text></view></view></view></view><view class="section"><view class="section-header"><text class="section-title">顶班服务</text><view class="view-more" bindtap="{{M}}">查看更多 ></view></view><view class="service-list"><view wx:for="{{N}}" wx:for-item="item" wx:key="e" class="service-item" bindtap="{{item.f}}"><view class="service-info"><view class="service-title">{{item.a}}</view><view class="service-time">{{item.b}}</view><view class="service-address">{{item.c}}</view></view><view class="service-price">{{item.d}}</view></view></view></view></view>