"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      banners: [
        { image: "/static/images/banner1.jpg" },
        { image: "/static/images/banner2.jpg" }
      ],
      stationList: [
        {
          id: 1,
          title: "城北社区驿站转让",
          address: "海淀区北太平庄",
          price: "45000",
          priceUnit: "元",
          image: "/static/images/station1.jpg"
        },
        {
          id: 2,
          title: "校园驿站急转",
          address: "朝阳区北苑路",
          price: "3800",
          priceUnit: "元/月",
          image: "/static/images/station2.jpg"
        }
      ],
      jobList: [
        {
          id: 1,
          title: "驿站分拣员",
          salary: "4500-5500元/月",
          company: "城北速递驿站",
          tags: ["五险", "餐补", "全职"]
        },
        {
          id: 2,
          title: "快递配送员",
          salary: "8000-10000元/月",
          company: "优速快递",
          tags: ["无需经验", "兼职可选", "多劳多得"]
        }
      ],
      serviceList: [
        {
          id: 1,
          title: "社区驿站收发员顶班",
          time: "09:00-18:00",
          address: "海淀区中关村南大街",
          price: "200元/天"
        },
        {
          id: 2,
          title: "快递分拣打包顶班",
          time: "18:00-22:00",
          address: "朝阳区望京西园",
          price: "35元/小时"
        }
      ],
      // 搜索相关数据
      searchKeyword: "",
      showSearchPanel: false,
      searchHistory: [],
      searchResults: [],
      isSearching: false,
      hotSearches: [
        "驿站转让",
        "电子秤",
        "驿站工作",
        "顶班服务",
        "快递柜",
        "打印机",
        "收银系统",
        "货架"
      ]
    };
  },
  onLoad() {
    this.loadSearchHistory();
  },
  methods: {
    navigateTo(url) {
      if (url === "/pages/station/station") {
        common_vendor.index.switchTab({
          url
        });
      } else {
        common_vendor.index.navigateTo({
          url
        });
      }
    },
    viewDetail(item) {
      common_vendor.index.navigateTo({
        url: "/pages/detail/detail?id=" + item.id + "&type=" + this.getTypeFromItem(item)
      });
    },
    getTypeFromItem(item) {
      if (item.priceUnit)
        return "station";
      if (item.salary)
        return "job";
      if (item.time)
        return "service";
      return "other";
    },
    // 打开搜索面板
    showSearch() {
      this.showSearchPanel = true;
    },
    // 关闭搜索面板
    closeSearchPanel() {
      this.showSearchPanel = false;
      this.searchKeyword = "";
      this.searchResults = [];
      this.isSearching = false;
    },
    // 清除搜索关键词
    clearSearch() {
      this.searchKeyword = "";
      this.searchResults = [];
      this.isSearching = false;
    },
    // 使用历史关键词
    useHistoryKeyword(keyword) {
      this.searchKeyword = keyword;
      this.performLiveSearch();
    },
    // 加载搜索历史
    loadSearchHistory() {
      const history = common_vendor.index.getStorageSync("searchHistory");
      if (history) {
        this.searchHistory = JSON.parse(history);
      }
    },
    // 保存搜索历史
    saveSearchHistory(keyword) {
      if (!keyword.trim())
        return;
      let history = this.searchHistory.filter((item) => item !== keyword);
      history.unshift(keyword);
      if (history.length > 10) {
        history = history.slice(0, 10);
      }
      this.searchHistory = history;
      common_vendor.index.setStorageSync("searchHistory", JSON.stringify(history));
    },
    // 清空搜索历史
    clearHistory() {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要清空搜索历史吗？",
        success: (res) => {
          if (res.confirm) {
            this.searchHistory = [];
            common_vendor.index.removeStorageSync("searchHistory");
          }
        }
      });
    },
    // 高亮关键词
    highlightKeyword(text) {
      if (!this.searchKeyword)
        return text;
      const keyword = this.searchKeyword.trim();
      const parts = text.split(new RegExp(`(${keyword})`, "gi"));
      let result = "";
      parts.forEach((part) => {
        if (part.toLowerCase() === keyword.toLowerCase()) {
          result += `<span style="color: #ff5a5f;">${part}</span>`;
        } else {
          result += part;
        }
      });
      return result;
    },
    // 搜索输入变化
    onSearchInput() {
      this.performLiveSearch();
    },
    // 执行实时搜索
    performLiveSearch() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = [];
        this.isSearching = false;
        return;
      }
      this.isSearching = true;
      const keyword = this.searchKeyword.toLowerCase();
      const results = [
        ...this.stationList.map((item) => ({ ...item, type: "station" })),
        ...this.jobList.map((item) => ({ ...item, type: "job" })),
        ...this.serviceList.map((item) => ({ ...item, type: "service" }))
      ].filter((item) => {
        if (item.type === "station") {
          return item.title.toLowerCase().includes(keyword) || item.address.toLowerCase().includes(keyword);
        } else if (item.type === "job") {
          return item.title.toLowerCase().includes(keyword) || item.company.toLowerCase().includes(keyword);
        } else if (item.type === "service") {
          return item.title.toLowerCase().includes(keyword) || item.address.toLowerCase().includes(keyword);
        }
        return false;
      });
      this.searchResults = results;
      this.isSearching = false;
    },
    // 选择搜索结果
    selectSearchResult(item) {
      this.saveSearchHistory(this.searchKeyword);
      let targetPage = "";
      if (item.type === "station") {
        targetPage = "/pages/transfer/transfer";
      } else if (item.type === "job") {
        targetPage = "/pages/job/job";
      } else if (item.type === "service") {
        targetPage = "/pages/service/service";
      } else if (item.type === "device") {
        targetPage = "/pages/device/device";
      }
      this.closeSearchPanel();
      if (targetPage) {
        common_vendor.index.navigateTo({
          url: `${targetPage}?keyword=${encodeURIComponent(this.searchKeyword)}`
        });
      }
    },
    // 处理搜索提交
    handleSearch() {
      this.saveSearchHistory(this.searchKeyword);
      let targetPage = "";
      const keyword = this.searchKeyword.toLowerCase();
      if (keyword.includes("电子秤") || keyword.includes("打印机") || keyword.includes("货架") || keyword.includes("出库") || keyword.includes("灯带") || keyword.includes("设备")) {
        targetPage = "/pages/device/device";
      } else if (keyword.includes("驿站") || keyword.includes("转让")) {
        targetPage = "/pages/transfer/transfer";
      } else if (keyword.includes("招聘") || keyword.includes("求职") || keyword.includes("工作")) {
        targetPage = "/pages/job/job";
      } else if (keyword.includes("顶班") || keyword.includes("服务")) {
        targetPage = "/pages/service/service";
      } else {
        targetPage = "/pages/device/device";
      }
      this.closeSearchPanel();
      common_vendor.index.navigateTo({
        url: `${targetPage}?keyword=${encodeURIComponent(this.searchKeyword)}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_1,
    b: common_vendor.o(($event) => $data.showSearchPanel = true),
    c: $data.showSearchPanel
  }, $data.showSearchPanel ? common_vendor.e({
    d: common_assets._imports_1,
    e: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    f: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearchInput && $options.onSearchInput(...args)]),
    g: $data.searchKeyword,
    h: $data.searchKeyword
  }, $data.searchKeyword ? {
    i: common_assets._imports_2$1,
    j: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    k: common_vendor.o((...args) => $options.closeSearchPanel && $options.closeSearchPanel(...args)),
    l: $data.searchKeyword && $data.searchResults.length > 0
  }, $data.searchKeyword && $data.searchResults.length > 0 ? {
    m: common_vendor.f($data.searchResults, (item, index, i0) => {
      return {
        a: $options.highlightKeyword(item.title),
        b: index,
        c: common_vendor.o(($event) => $options.selectSearchResult(item), index)
      };
    }),
    n: common_assets._imports_3$1
  } : {}, {
    o: $data.searchKeyword && $data.isSearching && $data.searchResults.length === 0
  }, $data.searchKeyword && $data.isSearching && $data.searchResults.length === 0 ? {
    p: common_assets._imports_4,
    q: common_vendor.t($data.searchKeyword)
  } : {}, {
    r: $data.searchHistory.length > 0 && !$data.searchKeyword
  }, $data.searchHistory.length > 0 && !$data.searchKeyword ? {
    s: common_assets._imports_2$2,
    t: common_vendor.o((...args) => $options.clearHistory && $options.clearHistory(...args)),
    v: common_vendor.f($data.searchHistory, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index,
        c: common_vendor.o(($event) => $options.useHistoryKeyword(item), index)
      };
    }),
    w: common_assets._imports_6
  } : {}, {
    x: !$data.searchKeyword
  }, !$data.searchKeyword ? {
    y: common_vendor.f($data.hotSearches, (item, index, i0) => {
      return common_vendor.e({
        a: index < 3
      }, index < 3 ? {
        b: common_vendor.t(index + 1),
        c: common_vendor.n("rank-" + (index + 1))
      } : {}, {
        d: common_vendor.t(item),
        e: index,
        f: common_vendor.o(($event) => $options.useHistoryKeyword(item), index)
      });
    })
  } : {}) : {}, {
    z: common_vendor.f($data.banners, (item, index, i0) => {
      return {
        a: item.image,
        b: index
      };
    }),
    A: common_assets._imports_0,
    B: common_vendor.o(($event) => $options.navigateTo("/pages/transfer/transfer")),
    C: common_assets._imports_3,
    D: common_vendor.o(($event) => $options.navigateTo("/pages/device/device")),
    E: common_assets._imports_2,
    F: common_vendor.o(($event) => $options.navigateTo("/pages/job/job")),
    G: common_assets._imports_5,
    H: common_vendor.o(($event) => $options.navigateTo("/pages/service/service")),
    I: common_vendor.o(($event) => $options.navigateTo("/pages/transfer/transfer")),
    J: common_vendor.f($data.stationList, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.address),
        d: common_vendor.t(item.price),
        e: common_vendor.t(item.priceUnit),
        f: index,
        g: common_vendor.o(($event) => $options.viewDetail(item), index)
      };
    }),
    K: common_vendor.o(($event) => $options.navigateTo("/pages/job/job")),
    L: common_vendor.f($data.jobList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.salary),
        c: common_vendor.t(item.company),
        d: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        e: index,
        f: common_vendor.o(($event) => $options.viewDetail(item), index)
      };
    }),
    M: common_vendor.o(($event) => $options.navigateTo("/pages/service/service")),
    N: common_vendor.f($data.serviceList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.time),
        c: common_vendor.t(item.address),
        d: common_vendor.t(item.price),
        e: index,
        f: common_vendor.o(($event) => $options.viewDetail(item), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
