
.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		position: relative;
}
.search-bar {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
}
.search-input-box {
		flex: 1;
		background-color: #ffffff;
		border-radius: 60rpx;
		padding: 15rpx 30rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.search-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 15rpx;
}
.filter-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.filter-btn image {
		width: 40rpx;
		height: 40rpx;
}
.filter-section {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.filter-row {
		margin-bottom: 20rpx;
}
.filter-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 15rpx;
}
.filter-options {
		display: flex;
		flex-wrap: wrap;
}
.filter-option {
		padding: 10rpx 20rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		margin-right: 15rpx;
		margin-bottom: 15rpx;
		font-size: 24rpx;
		color: #666666;
}
.filter-option.active {
		background-color: #ff5a5f;
		color: #ffffff;
}
.filter-actions {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
}
.reset-btn, .confirm-btn {
		width: 45%;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 40rpx;
		font-size: 28rpx;
}
.reset-btn {
		background-color: #f5f5f5;
		color: #666666;
}
.confirm-btn {
		background-color: #ff5a5f;
		color: #ffffff;
}
.job-tabs {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
}
.tabs-scroll-view {
		white-space: nowrap;
		padding: 0 20rpx;
}
.tab-item {
		display: inline-block;
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		color: #666666;
		position: relative;
}
.tab-item.active {
		color: #ff5a5f;
		font-weight: bold;
}
.tab-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background-color: #ff5a5f;
}
.job-list {
		margin-top: 20rpx;
}
.job-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.job-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
}
.job-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
}
.job-salary {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff5a5f;
}
.job-company {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 10rpx;
}
.job-address {
		font-size: 26rpx;
		color: #666666;
		margin-bottom: 15rpx;
}
.job-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 15rpx;
}
.job-tag {
		font-size: 22rpx;
		padding: 6rpx 16rpx;
		background-color: #f8f8f8;
		color: #666666;
		margin-right: 15rpx;
		margin-bottom: 10rpx;
		border-radius: 30rpx;
}
.job-bottom {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-top: 1rpx solid #f0f0f0;
		padding-top: 15rpx;
}
.job-requirements {
		display: flex;
}
.requirement {
		font-size: 24rpx;
		color: #999999;
		margin-right: 20rpx;
}
.job-time {
		font-size: 24rpx;
		color: #999999;
}
.loading-more, .no-more {
		text-align: center;
		padding: 30rpx 0;
}
.loading-more text, .no-more text {
		font-size: 26rpx;
		color: #999999;
}
