"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      showFilterSection: false,
      activeArea: "all",
      activeSalary: "all",
      activeType: "all",
      activeTab: "all",
      hasMore: true,
      pageNum: 1,
      pageSize: 10,
      jobList: [
        {
          id: 1,
          title: "驿站分拣员",
          salary: "4500-5500元/月",
          company: "城北速递驿站",
          address: "海淀区北太平庄",
          tags: ["五险", "餐补", "全职"],
          experience: "经验不限",
          education: "学历不限",
          publishTime: "1小时前"
        },
        {
          id: 2,
          title: "快递配送员",
          salary: "8000-10000元/月",
          company: "优速快递",
          address: "朝阳区望京",
          tags: ["无需经验", "兼职可选", "多劳多得"],
          experience: "经验不限",
          education: "学历不限",
          publishTime: "3小时前"
        },
        {
          id: 3,
          title: "驿站管理员",
          salary: "6000-7000元/月",
          company: "中通快递",
          address: "东城区东四十条",
          tags: ["五险一金", "双休", "有晋升"],
          experience: "1-3年",
          education: "大专及以上",
          publishTime: "6小时前"
        },
        {
          id: 4,
          title: "快递分拣兼职",
          salary: "180元/天",
          company: "申通快递",
          address: "西城区德胜门",
          tags: ["日结", "兼职", "包吃"],
          experience: "经验不限",
          education: "学历不限",
          publishTime: "12小时前"
        },
        {
          id: 5,
          title: "驿站客服专员",
          salary: "4000-5000元/月",
          company: "韵达快递",
          address: "丰台区方庄",
          tags: ["五险", "包吃住", "有提成"],
          experience: "无经验可培训",
          education: "中专及以上",
          publishTime: "1天前"
        }
      ]
    };
  },
  onLoad() {
    this.loadStoredData();
  },
  onShow() {
    this.loadStoredData();
  },
  onReachBottom() {
    if (this.hasMore) {
      this.loadMoreData();
    }
  },
  methods: {
    handleSearch() {
      common_vendor.index.showToast({
        title: "搜索：" + this.searchKeyword,
        icon: "none"
      });
    },
    showFilter() {
      this.showFilterSection = !this.showFilterSection;
    },
    selectArea(area) {
      this.activeArea = area;
    },
    selectSalary(salary) {
      this.activeSalary = salary;
    },
    selectType(type) {
      this.activeType = type;
    },
    resetFilter() {
      this.activeArea = "all";
      this.activeSalary = "all";
      this.activeType = "all";
    },
    applyFilter() {
      this.showFilterSection = false;
      common_vendor.index.showToast({
        title: "筛选条件已应用",
        icon: "none"
      });
      this.filterJobList();
    },
    changeTab(tab) {
      this.activeTab = tab;
      this.filterJobList();
    },
    // 添加筛选职位数据的方法
    filterJobList() {
      let originalJobList = common_vendor.index.getStorageSync("jobList") || [];
      if (originalJobList.length === 0) {
        originalJobList = this.jobList;
      }
      if (this.activeTab !== "all") {
        const positionMap = {
          "sorting": "sorting",
          // 分拣员
          "delivery": "delivery",
          // 配送员
          "manager": "manager",
          // 站点管理
          "customer": "customer",
          // 客服
          "warehouse": "warehouse"
          // 仓储
        };
        originalJobList = originalJobList.filter(
          (job) => job.positionType === positionMap[this.activeTab] || // 为了兼容旧数据，对标题进行关键词匹配
          this.activeTab === "sorting" && job.title.includes("分拣") || this.activeTab === "delivery" && job.title.includes("配送") || this.activeTab === "manager" && job.title.includes("管理") || this.activeTab === "customer" && job.title.includes("客服") || this.activeTab === "warehouse" && job.title.includes("仓储")
        );
      }
      if (this.activeArea !== "all") {
        const areaMap = {
          "haidian": "海淀区",
          "chaoyang": "朝阳区",
          "dongcheng": "东城区",
          "xicheng": "西城区"
        };
        originalJobList = originalJobList.filter(
          (job) => job.address && job.address.includes(areaMap[this.activeArea])
        );
      }
      if (this.activeSalary !== "all") {
        originalJobList = originalJobList.filter((job) => {
          var _a;
          const salary = job.salary || "";
          const salaryNumber = parseInt(((_a = salary.match(/\d+/g)) == null ? void 0 : _a[0]) || "0");
          switch (this.activeSalary) {
            case "low":
              return salaryNumber < 4e3;
            case "medium":
              return salaryNumber >= 4e3 && salaryNumber < 6e3;
            case "high":
              return salaryNumber >= 6e3 && salaryNumber < 8e3;
            case "very_high":
              return salaryNumber >= 8e3;
            default:
              return true;
          }
        });
      }
      if (this.activeType !== "all") {
        const typeMap = {
          "full": "全职",
          "part": "兼职",
          "intern": "临时工"
        };
        originalJobList = originalJobList.filter((job) => {
          const jobType = job.jobType || "";
          const tags = job.tags || [];
          return jobType.includes(typeMap[this.activeType]) || tags.some((tag) => tag.includes(typeMap[this.activeType]));
        });
      }
      this.jobList = originalJobList;
      this.pageNum = 1;
      this.hasMore = originalJobList.length >= this.pageSize;
    },
    viewDetail(item) {
      common_vendor.index.navigateTo({
        url: "/pages/detail/detail?id=" + item.id + "&type=job"
      });
    },
    loadMoreData() {
      setTimeout(() => {
        if (this.pageNum >= 3) {
          this.hasMore = false;
        } else {
          this.pageNum++;
        }
      }, 1e3);
    },
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 加载本地存储的数据
    loadStoredData() {
      const storedData = common_vendor.index.getStorageSync("jobList");
      if (storedData && storedData.length > 0) {
        this.jobList = storedData;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_1,
    b: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    c: $data.searchKeyword,
    d: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    e: common_assets._imports_1$1,
    f: common_vendor.o((...args) => $options.showFilter && $options.showFilter(...args)),
    g: $data.showFilterSection
  }, $data.showFilterSection ? {
    h: $data.activeArea === "all" ? 1 : "",
    i: common_vendor.o(($event) => $options.selectArea("all")),
    j: $data.activeArea === "haidian" ? 1 : "",
    k: common_vendor.o(($event) => $options.selectArea("haidian")),
    l: $data.activeArea === "chaoyang" ? 1 : "",
    m: common_vendor.o(($event) => $options.selectArea("chaoyang")),
    n: $data.activeArea === "dongcheng" ? 1 : "",
    o: common_vendor.o(($event) => $options.selectArea("dongcheng")),
    p: $data.activeArea === "xicheng" ? 1 : "",
    q: common_vendor.o(($event) => $options.selectArea("xicheng")),
    r: $data.activeSalary === "all" ? 1 : "",
    s: common_vendor.o(($event) => $options.selectSalary("all")),
    t: $data.activeSalary === "low" ? 1 : "",
    v: common_vendor.o(($event) => $options.selectSalary("low")),
    w: $data.activeSalary === "medium" ? 1 : "",
    x: common_vendor.o(($event) => $options.selectSalary("medium")),
    y: $data.activeSalary === "high" ? 1 : "",
    z: common_vendor.o(($event) => $options.selectSalary("high")),
    A: $data.activeSalary === "very_high" ? 1 : "",
    B: common_vendor.o(($event) => $options.selectSalary("very_high")),
    C: $data.activeType === "all" ? 1 : "",
    D: common_vendor.o(($event) => $options.selectType("all")),
    E: $data.activeType === "full" ? 1 : "",
    F: common_vendor.o(($event) => $options.selectType("full")),
    G: $data.activeType === "part" ? 1 : "",
    H: common_vendor.o(($event) => $options.selectType("part")),
    I: $data.activeType === "intern" ? 1 : "",
    J: common_vendor.o(($event) => $options.selectType("intern")),
    K: common_vendor.o((...args) => $options.resetFilter && $options.resetFilter(...args)),
    L: common_vendor.o((...args) => $options.applyFilter && $options.applyFilter(...args))
  } : {}, {
    M: $data.activeTab === "all" ? 1 : "",
    N: common_vendor.o(($event) => $options.changeTab("all")),
    O: $data.activeTab === "sorting" ? 1 : "",
    P: common_vendor.o(($event) => $options.changeTab("sorting")),
    Q: $data.activeTab === "delivery" ? 1 : "",
    R: common_vendor.o(($event) => $options.changeTab("delivery")),
    S: $data.activeTab === "manager" ? 1 : "",
    T: common_vendor.o(($event) => $options.changeTab("manager")),
    U: $data.activeTab === "customer" ? 1 : "",
    V: common_vendor.o(($event) => $options.changeTab("customer")),
    W: $data.activeTab === "warehouse" ? 1 : "",
    X: common_vendor.o(($event) => $options.changeTab("warehouse")),
    Y: common_vendor.f($data.jobList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.salary),
        c: common_vendor.t(item.company),
        d: common_vendor.t(item.address),
        e: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        f: common_vendor.t(item.experience),
        g: common_vendor.t(item.education),
        h: common_vendor.t(item.publishTime),
        i: index,
        j: common_vendor.o(($event) => $options.viewDetail(item), index)
      };
    }),
    Z: $data.hasMore
  }, $data.hasMore ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
