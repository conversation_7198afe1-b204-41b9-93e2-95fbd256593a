
.container {
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		position: relative;
}
.search-bar {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
		gap: 20rpx;
}
.search-input-box {
		flex: 1;
		background-color: #ffffff;
		border-radius: 60rpx;
		padding: 20rpx 35rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.search-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
}
.filter-btn {
		width: 90rpx;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
		border-radius: 45rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.filter-btn image {
		width: 44rpx;
		height: 44rpx;
}
	
	/* 快捷分类标签 */
.quick-tags {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 25rpx 0;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.tags-scroll-view {
		white-space: nowrap;
		padding: 0 25rpx;
}
.tag-item {
		display: inline-block;
		padding: 12rpx 35rpx;
		margin-right: 20rpx;
		font-size: 28rpx;
		color: #666666;
		background-color: #f5f5f5;
		border-radius: 35rpx;
}
.tag-item.active {
		background-color: #ff5a5f;
		color: #ffffff;
}
	
	/* 筛选区域 */
.filter-section {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		z-index: 10;
}
.filter-row {
		margin-bottom: 25rpx;
}
.filter-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
}
.filter-options {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
}
.filter-option {
		padding: 12rpx 25rpx;
		background-color: #f5f5f5;
		border-radius: 35rpx;
		font-size: 26rpx;
		color: #666666;
}
.filter-option.active {
		background-color: #ff5a5f;
		color: #ffffff;
}
.filter-actions {
		display: flex;
		justify-content: space-between;
		margin-top: 35rpx;
		gap: 20rpx;
}
.reset-btn, .confirm-btn {
		flex: 1;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 45rpx;
		font-size: 30rpx;
}
.reset-btn {
		background-color: #f5f5f5;
		color: #666666;
}
.confirm-btn {
		background-color: #ff5a5f;
		color: #ffffff;
}
	
	/* 驿站列表 */
.station-list {
		margin-top: 20rpx;
}
.station-item {
		background-color: #ffffff;
		border-radius: 16rpx;
		margin-bottom: 25rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.station-image {
		width: 100%;
		height: 380rpx;
		background-color: #f9f9f9;
}
.station-info {
		padding: 25rpx;
}
.station-title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
}
.station-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 15rpx;
		margin-bottom: 20rpx;
}
.station-tag {
		font-size: 24rpx;
		padding: 8rpx 20rpx;
		background-color: #FFF0F0;
		color: #FF5A5F;
		border-radius: 8rpx;
}
.station-address {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 20rpx;
}
.station-address image {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
}
.station-data {
		display: flex;
		flex-wrap: wrap;
		gap: 30rpx;
		margin-bottom: 20rpx;
}
.data-item {
		font-size: 26rpx;
		color: #666666;
}
.data-item .label {
		color: #999999;
}
.data-item .value {
		color: #333333;
}
.station-price {
		font-size: 26rpx;
		color: #FF5A5F;
}
.price-value {
		font-size: 40rpx;
		font-weight: bold;
}
.price-unit {
		font-size: 26rpx;
		margin-left: 6rpx;
}
	
	/* 加载更多 */
.loading-more, .no-more {
		text-align: center;
		padding: 35rpx 0;
}
.loading-more text, .no-more text {
		font-size: 28rpx;
		color: #999999;
}
