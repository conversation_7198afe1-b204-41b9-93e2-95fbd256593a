"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      keyword: "",
      // 搜索关键词
      showFilter: false,
      // 是否显示筛选区域
      activeType: "全部",
      // 当前选中的类型
      selectedArea: "全部",
      // 选中的区域
      selectedPrice: "全部",
      // 选中的价格
      selectedSize: "全部",
      // 选中的规模
      stationList: [],
      // 驿站列表
      loading: false,
      // 是否正在加载
      hasMore: true,
      // 是否有更多数据
      page: 1,
      // 当前页码
      pageSize: 10,
      // 每页数量
      types: ["全部", "快递驿站", "社区驿站", "校园驿站", "写字楼驿站"],
      // 驿站类型选项
      areas: ["全部", "朝阳区", "海淀区", "东城区", "西城区", "丰台区", "石景山区"],
      // 区域选项
      prices: ["全部", "5万以下", "5-10万", "10-20万", "20-50万", "50万以上"],
      // 价格选项
      sizes: ["全部", "50m²以下", "50-100m²", "100-200m²", "200m²以上"]
      // 规模选项
    };
  },
  computed: {
    // 根据筛选条件过滤驿站列表
    filteredStations() {
      return this.stationList;
    }
  },
  onLoad() {
    this.loadStations();
  },
  onShow() {
    this.page = 1;
    this.stationList = [];
    this.loadStations();
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore();
    }
  },
  methods: {
    // 加载驿站列表数据
    async loadStations() {
      this.loading = true;
      try {
        const params = {
          page: this.page,
          pageSize: this.pageSize,
          keyword: this.keyword || "",
          type: this.activeType !== "全部" ? this.activeType : "",
          area: this.selectedArea !== "全部" ? this.selectedArea : "",
          priceRange: this.selectedPrice !== "全部" ? this.getPriceRange(this.selectedPrice) : "",
          sizeRange: this.selectedSize !== "全部" ? this.getSizeRange(this.selectedSize) : ""
        };
        const res = await this.$api.station.getStationList(params);
        if (res.code === 0) {
          this.stationList = res.data.list || [];
          this.hasMore = this.stationList.length < res.data.total;
        } else {
          common_vendor.index.showToast({
            title: res.message || "加载数据失败",
            icon: "none"
          });
        }
      } catch (e) {
        console.error("加载驿站数据失败", e);
        common_vendor.index.showToast({
          title: "网络异常，请稍后重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 加载更多数据
    loadMore() {
      this.page++;
      this.loadStations();
    },
    // 搜索
    search() {
      this.page = 1;
      this.stationList = [];
      this.loadStations();
    },
    // 切换筛选区域显示状态
    toggleFilter() {
      this.showFilter = !this.showFilter;
    },
    // 选择类型
    selectType(type) {
      this.activeType = type;
      if (!this.showFilter) {
        this.page = 1;
        this.stationList = [];
        this.loadStations();
      }
    },
    // 选择区域
    selectArea(area) {
      this.selectedArea = area;
    },
    // 选择价格
    selectPrice(price) {
      this.selectedPrice = price;
    },
    // 选择规模
    selectSize(size) {
      this.selectedSize = size;
    },
    // 确认筛选
    confirmFilter() {
      this.page = 1;
      this.stationList = [];
      this.loadStations();
      this.showFilter = false;
    },
    // 重置筛选条件
    resetFilter() {
      this.activeType = "全部";
      this.selectedArea = "全部";
      this.selectedPrice = "全部";
      this.selectedSize = "全部";
    },
    // 获取价格范围参数
    getPriceRange(priceOption) {
      const priceRangeMap = {
        "5万以下": "0,50000",
        "5-10万": "50000,100000",
        "10-20万": "100000,200000",
        "20-50万": "200000,500000",
        "50万以上": "500000,100000000"
      };
      return priceRangeMap[priceOption] || "";
    },
    // 获取规模范围参数
    getSizeRange(sizeOption) {
      const sizeRangeMap = {
        "50m²以下": "0,50",
        "50-100m²": "50,100",
        "100-200m²": "100,200",
        "200m²以上": "200,10000"
      };
      return sizeRangeMap[sizeOption] || "";
    },
    // 跳转到发布页面
    goToPublish() {
      common_vendor.index.navigateTo({
        url: "/pages/publish/station"
      });
    },
    // 跳转到详情页
    goToDetail(id) {
      const currentStation = this.stationList.find((item) => item.id.toString() === id.toString());
      if (currentStation) {
        const transferList = common_vendor.index.getStorageSync("transferList") || [];
        const existingIndex = transferList.findIndex((item) => item.id.toString() === id.toString());
        if (existingIndex !== -1) {
          transferList[existingIndex] = currentStation;
        } else {
          transferList.push(currentStation);
        }
        common_vendor.index.setStorageSync("transferList", transferList);
      }
      common_vendor.index.navigateTo({
        url: `/pages/detail/detail?id=${id}&type=station`
      });
    },
    // 立即购买
    buyNow(id) {
      common_vendor.index.navigateTo({
        url: `/pages/my/checkout?id=${id}&type=station`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_1,
    b: common_vendor.o((...args) => $options.search && $options.search(...args)),
    c: $data.keyword,
    d: common_vendor.o(($event) => $data.keyword = $event.detail.value),
    e: common_assets._imports_1$1,
    f: common_vendor.o((...args) => $options.toggleFilter && $options.toggleFilter(...args)),
    g: $data.activeType === "全部" ? 1 : "",
    h: common_vendor.o(($event) => $options.selectType("全部")),
    i: $data.activeType === "快递驿站" ? 1 : "",
    j: common_vendor.o(($event) => $options.selectType("快递驿站")),
    k: $data.activeType === "社区驿站" ? 1 : "",
    l: common_vendor.o(($event) => $options.selectType("社区驿站")),
    m: $data.activeType === "校园驿站" ? 1 : "",
    n: common_vendor.o(($event) => $options.selectType("校园驿站")),
    o: $data.activeType === "写字楼驿站" ? 1 : "",
    p: common_vendor.o(($event) => $options.selectType("写字楼驿站")),
    q: $data.showFilter
  }, $data.showFilter ? {
    r: $data.selectedArea === "全部" ? 1 : "",
    s: common_vendor.o(($event) => $options.selectArea("全部")),
    t: $data.selectedArea === "朝阳区" ? 1 : "",
    v: common_vendor.o(($event) => $options.selectArea("朝阳区")),
    w: $data.selectedArea === "海淀区" ? 1 : "",
    x: common_vendor.o(($event) => $options.selectArea("海淀区")),
    y: $data.selectedArea === "东城区" ? 1 : "",
    z: common_vendor.o(($event) => $options.selectArea("东城区")),
    A: $data.selectedArea === "西城区" ? 1 : "",
    B: common_vendor.o(($event) => $options.selectArea("西城区")),
    C: $data.selectedArea === "丰台区" ? 1 : "",
    D: common_vendor.o(($event) => $options.selectArea("丰台区")),
    E: $data.selectedArea === "石景山区" ? 1 : "",
    F: common_vendor.o(($event) => $options.selectArea("石景山区")),
    G: $data.selectedPrice === "全部" ? 1 : "",
    H: common_vendor.o(($event) => $options.selectPrice("全部")),
    I: $data.selectedPrice === "5万以下" ? 1 : "",
    J: common_vendor.o(($event) => $options.selectPrice("5万以下")),
    K: $data.selectedPrice === "5-10万" ? 1 : "",
    L: common_vendor.o(($event) => $options.selectPrice("5-10万")),
    M: $data.selectedPrice === "10-20万" ? 1 : "",
    N: common_vendor.o(($event) => $options.selectPrice("10-20万")),
    O: $data.selectedPrice === "20-50万" ? 1 : "",
    P: common_vendor.o(($event) => $options.selectPrice("20-50万")),
    Q: $data.selectedPrice === "50万以上" ? 1 : "",
    R: common_vendor.o(($event) => $options.selectPrice("50万以上")),
    S: $data.selectedSize === "全部" ? 1 : "",
    T: common_vendor.o(($event) => $options.selectSize("全部")),
    U: $data.selectedSize === "50m²以下" ? 1 : "",
    V: common_vendor.o(($event) => $options.selectSize("50m²以下")),
    W: $data.selectedSize === "50-100m²" ? 1 : "",
    X: common_vendor.o(($event) => $options.selectSize("50-100m²")),
    Y: $data.selectedSize === "100-200m²" ? 1 : "",
    Z: common_vendor.o(($event) => $options.selectSize("100-200m²")),
    aa: $data.selectedSize === "200m²以上" ? 1 : "",
    ab: common_vendor.o(($event) => $options.selectSize("200m²以上")),
    ac: common_vendor.o((...args) => $options.resetFilter && $options.resetFilter(...args)),
    ad: common_vendor.o((...args) => $options.confirmFilter && $options.confirmFilter(...args))
  } : {}, {
    ae: common_vendor.f($data.stationList, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.title),
        c: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        d: common_vendor.t(item.address),
        e: common_vendor.t(item.space),
        f: common_vendor.t(item.package),
        g: common_vendor.t(item.price),
        h: common_vendor.t(item.priceUnit),
        i: index,
        j: common_vendor.o(($event) => $options.goToDetail(item.id), index)
      };
    }),
    af: common_assets._imports_1$2,
    ag: $data.loading
  }, $data.loading ? {} : !$data.hasMore ? {} : {}, {
    ah: !$data.hasMore
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
