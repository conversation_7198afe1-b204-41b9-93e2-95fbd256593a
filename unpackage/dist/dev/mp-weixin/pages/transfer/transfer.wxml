<view class="container"><view class="search-bar"><view class="search-input-box"><image class="search-icon" src="{{a}}" mode="aspectFit"></image><input type="text" placeholder="搜索驿站位置/类型" confirm-type="search" bindconfirm="{{b}}" value="{{c}}" bindinput="{{d}}"/></view><view class="filter-btn" bindtap="{{f}}"><image src="{{e}}" mode="aspectFit"></image></view></view><view class="quick-tags"><scroll-view scroll-x class="tags-scroll-view"><view class="{{['tag-item', g && 'active']}}" bindtap="{{h}}">全部</view><view class="{{['tag-item', i && 'active']}}" bindtap="{{j}}">快递驿站</view><view class="{{['tag-item', k && 'active']}}" bindtap="{{l}}">社区驿站</view><view class="{{['tag-item', m && 'active']}}" bindtap="{{n}}">校园驿站</view><view class="{{['tag-item', o && 'active']}}" bindtap="{{p}}">写字楼驿站</view></scroll-view></view><view wx:if="{{q}}" class="filter-section"><view class="filter-row"><view class="filter-title">区域</view><view class="filter-options"><view class="{{['filter-option', r && 'active']}}" bindtap="{{s}}">全部</view><view class="{{['filter-option', t && 'active']}}" bindtap="{{v}}">朝阳区</view><view class="{{['filter-option', w && 'active']}}" bindtap="{{x}}">海淀区</view><view class="{{['filter-option', y && 'active']}}" bindtap="{{z}}">东城区</view><view class="{{['filter-option', A && 'active']}}" bindtap="{{B}}">西城区</view><view class="{{['filter-option', C && 'active']}}" bindtap="{{D}}">丰台区</view><view class="{{['filter-option', E && 'active']}}" bindtap="{{F}}">石景山区</view></view></view><view class="filter-row"><view class="filter-title">价格区间</view><view class="filter-options"><view class="{{['filter-option', G && 'active']}}" bindtap="{{H}}">全部</view><view class="{{['filter-option', I && 'active']}}" bindtap="{{J}}">5万以下</view><view class="{{['filter-option', K && 'active']}}" bindtap="{{L}}">5-10万</view><view class="{{['filter-option', M && 'active']}}" bindtap="{{N}}">10-20万</view><view class="{{['filter-option', O && 'active']}}" bindtap="{{P}}">20-50万</view><view class="{{['filter-option', Q && 'active']}}" bindtap="{{R}}">50万以上</view></view></view><view class="filter-row"><view class="filter-title">驿站面积</view><view class="filter-options"><view class="{{['filter-option', S && 'active']}}" bindtap="{{T}}">全部</view><view class="{{['filter-option', U && 'active']}}" bindtap="{{V}}">50m²以下</view><view class="{{['filter-option', W && 'active']}}" bindtap="{{X}}">50-100m²</view><view class="{{['filter-option', Y && 'active']}}" bindtap="{{Z}}">100-200m²</view><view class="{{['filter-option', aa && 'active']}}" bindtap="{{ab}}">200m²以上</view></view></view><view class="filter-actions"><view class="reset-btn" bindtap="{{ac}}">重置</view><view class="confirm-btn" bindtap="{{ad}}">确认</view></view></view><view class="station-list"><view wx:for="{{ae}}" wx:for-item="item" wx:key="i" class="station-item" bindtap="{{item.j}}"><image class="station-image" src="{{item.a}}" mode="aspectFill"></image><view class="station-info"><view class="station-title">{{item.b}}</view><view class="station-tags"><text wx:for="{{item.c}}" wx:for-item="tag" wx:key="b" class="station-tag">{{tag.a}}</text></view><view class="station-address"><image src="{{af}}" mode="aspectFit"></image><text>{{item.d}}</text></view><view class="station-data"><view class="data-item"><text class="label">面积：</text><text class="value">{{item.e}}m²</text></view><view class="data-item"><text class="label">日均件量：</text><text class="value">{{item.f}}件</text></view></view><view class="station-price">¥<text class="price-value">{{item.g}}</text><text class="price-unit">{{item.h}}</text></view></view></view></view><view wx:if="{{ag}}" class="loading-more"><text>正在加载更多...</text></view><view wx:elif="{{ah}}" class="no-more"><text>没有更多数据了</text></view></view>