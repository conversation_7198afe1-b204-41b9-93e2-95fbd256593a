"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      showFilterSection: false,
      activeLocation: "all",
      activePrice: "all",
      activeType: "all",
      activeTab: "all",
      hasMore: true,
      pageNum: 1,
      pageSize: 10,
      deviceList: [
        {
          id: 1,
          title: "9成新菜鸟智能电子秤",
          price: "380",
          location: "北京·海淀",
          type: "电子秤",
          images: ["/static/device/scale.jpg"],
          condition: "9成新",
          publishTime: "1小时前",
          contactInfo: {
            name: "王先生",
            phone: "13312345678"
          }
        },
        {
          id: 2,
          title: "二手标签打印机 小票热敏打印机",
          price: "580",
          location: "北京·朝阳",
          type: "打印机",
          images: ["/static/device/printer.jpg"],
          condition: "95成新",
          publishTime: "3小时前",
          contactInfo: {
            name: "李小姐",
            phone: "13588889999"
          }
        },
        {
          id: 3,
          title: "快递包装货架 五层置物架",
          price: "260",
          location: "北京·丰台",
          type: "货架",
          images: ["/static/device/shelf.jpg"],
          condition: "8成新",
          publishTime: "昨天",
          contactInfo: {
            name: "张经理",
            phone: "18612345678"
          }
        },
        {
          id: 4,
          title: "全新驿站收银系统 扫码枪套装",
          price: "1200",
          location: "北京·西城",
          type: "收银设备",
          images: ["/static/device/scanner.jpg"],
          condition: "全新",
          publishTime: "前天",
          contactInfo: {
            name: "赵先生",
            phone: "13912345678"
          }
        },
        {
          id: 5,
          title: "二手快递打包台 工作台",
          price: "350",
          location: "北京·通州",
          type: "工作台",
          images: ["/static/device/table.jpg"],
          condition: "7成新",
          publishTime: "3天前",
          contactInfo: {
            name: "刘小姐",
            phone: "13800138000"
          }
        }
      ]
    };
  },
  onLoad() {
    this.loadStoredData();
  },
  onShow() {
    this.loadStoredData();
  },
  onReachBottom() {
    if (this.hasMore) {
      this.loadMoreData();
    }
  },
  methods: {
    handleSearch() {
      common_vendor.index.showToast({
        title: "搜索：" + this.searchKeyword,
        icon: "none"
      });
    },
    showFilter() {
      this.showFilterSection = !this.showFilterSection;
    },
    selectLocation(location) {
      this.activeLocation = location;
    },
    selectPrice(price) {
      this.activePrice = price;
    },
    selectType(type) {
      this.activeType = type;
    },
    resetFilter() {
      this.activeLocation = "all";
      this.activePrice = "all";
      this.activeType = "all";
    },
    applyFilter() {
      this.showFilterSection = false;
      common_vendor.index.showToast({
        title: "筛选条件已应用",
        icon: "none"
      });
    },
    changeTab(tab) {
      this.activeTab = tab;
    },
    viewDetail(item) {
      common_vendor.index.navigateTo({
        url: "/pages/detail/detail?id=" + item.id + "&type=device"
      });
    },
    loadMoreData() {
      setTimeout(() => {
        if (this.pageNum >= 3) {
          this.hasMore = false;
        } else {
          this.pageNum++;
        }
      }, 1e3);
    },
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 加载本地存储的数据
    loadStoredData() {
      const storedData = common_vendor.index.getStorageSync("deviceList");
      if (storedData && storedData.length > 0) {
        this.deviceList = storedData;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_1,
    b: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    c: $data.searchKeyword,
    d: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    e: common_assets._imports_1$1,
    f: common_vendor.o((...args) => $options.showFilter && $options.showFilter(...args)),
    g: $data.showFilterSection
  }, $data.showFilterSection ? {
    h: $data.activeLocation === "all" ? 1 : "",
    i: common_vendor.o(($event) => $options.selectLocation("all")),
    j: $data.activeLocation === "haidian" ? 1 : "",
    k: common_vendor.o(($event) => $options.selectLocation("haidian")),
    l: $data.activeLocation === "chaoyang" ? 1 : "",
    m: common_vendor.o(($event) => $options.selectLocation("chaoyang")),
    n: $data.activeLocation === "dongcheng" ? 1 : "",
    o: common_vendor.o(($event) => $options.selectLocation("dongcheng")),
    p: $data.activeLocation === "xicheng" ? 1 : "",
    q: common_vendor.o(($event) => $options.selectLocation("xicheng")),
    r: $data.activePrice === "all" ? 1 : "",
    s: common_vendor.o(($event) => $options.selectPrice("all")),
    t: $data.activePrice === "low" ? 1 : "",
    v: common_vendor.o(($event) => $options.selectPrice("low")),
    w: $data.activePrice === "medium" ? 1 : "",
    x: common_vendor.o(($event) => $options.selectPrice("medium")),
    y: $data.activePrice === "high" ? 1 : "",
    z: common_vendor.o(($event) => $options.selectPrice("high")),
    A: $data.activeType === "all" ? 1 : "",
    B: common_vendor.o(($event) => $options.selectType("all")),
    C: $data.activeType === "scanner" ? 1 : "",
    D: common_vendor.o(($event) => $options.selectType("scanner")),
    E: $data.activeType === "printer" ? 1 : "",
    F: common_vendor.o(($event) => $options.selectType("printer")),
    G: $data.activeType === "shelf" ? 1 : "",
    H: common_vendor.o(($event) => $options.selectType("shelf")),
    I: $data.activeType === "vehicle" ? 1 : "",
    J: common_vendor.o(($event) => $options.selectType("vehicle")),
    K: $data.activeType === "other" ? 1 : "",
    L: common_vendor.o(($event) => $options.selectType("other")),
    M: common_vendor.o((...args) => $options.resetFilter && $options.resetFilter(...args)),
    N: common_vendor.o((...args) => $options.applyFilter && $options.applyFilter(...args))
  } : {}, {
    O: $data.activeTab === "all" ? 1 : "",
    P: common_vendor.o(($event) => $options.changeTab("all")),
    Q: $data.activeTab === "scanner" ? 1 : "",
    R: common_vendor.o(($event) => $options.changeTab("scanner")),
    S: $data.activeTab === "printer" ? 1 : "",
    T: common_vendor.o(($event) => $options.changeTab("printer")),
    U: $data.activeTab === "shelf" ? 1 : "",
    V: common_vendor.o(($event) => $options.changeTab("shelf")),
    W: $data.activeTab === "scale" ? 1 : "",
    X: common_vendor.o(($event) => $options.changeTab("scale")),
    Y: $data.activeTab === "vehicle" ? 1 : "",
    Z: common_vendor.o(($event) => $options.changeTab("vehicle")),
    aa: common_vendor.f($data.deviceList, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.t(item.title),
        c: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        d: common_vendor.t(item.price),
        e: common_vendor.t(item.address),
        f: index,
        g: common_vendor.o(($event) => $options.viewDetail(item), index)
      };
    }),
    ab: $data.hasMore
  }, $data.hasMore ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
