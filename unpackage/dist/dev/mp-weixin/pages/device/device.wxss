
.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		position: relative;
}
.search-bar {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
}
.search-input-box {
		flex: 1;
		background-color: #ffffff;
		border-radius: 60rpx;
		padding: 15rpx 30rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.search-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 15rpx;
}
.filter-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.filter-btn image {
		width: 40rpx;
		height: 40rpx;
}
.filter-section {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.filter-row {
		margin-bottom: 20rpx;
}
.filter-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 15rpx;
}
.filter-options {
		display: flex;
		flex-wrap: wrap;
}
.filter-option {
		padding: 10rpx 20rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		margin-right: 15rpx;
		margin-bottom: 15rpx;
		font-size: 24rpx;
		color: #666666;
}
.filter-option.active {
		background-color: #ff5a5f;
		color: #ffffff;
}
.filter-actions {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
}
.reset-btn, .confirm-btn {
		width: 45%;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 40rpx;
		font-size: 28rpx;
}
.reset-btn {
		background-color: #f5f5f5;
		color: #666666;
}
.confirm-btn {
		background-color: #ff5a5f;
		color: #ffffff;
}
.device-tabs {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
}
.tabs-scroll-view {
		white-space: nowrap;
		padding: 0 20rpx;
}
.tab-item {
		display: inline-block;
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		color: #666666;
		position: relative;
}
.tab-item.active {
		color: #ff5a5f;
		font-weight: bold;
}
.tab-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background-color: #ff5a5f;
}
	
	/* 网格布局 */
.device-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-top: 20rpx;
}
.device-card {
		width: 345rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
}
.device-image {
		width: 100%;
		height: 345rpx;
		background-color: #f9f9f9;
}
.device-info {
		padding: 16rpx;
}
.device-title {
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 12rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		height: 78rpx;
}
.device-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 12rpx;
}
.device-tag {
		font-size: 20rpx;
		padding: 4rpx 10rpx;
		background-color: #f8f8f8;
		color: #999999;
		margin-right: 10rpx;
		margin-bottom: 8rpx;
		border-radius: 6rpx;
}
.device-price {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff5a5f;
		margin-bottom: 8rpx;
}
.device-meta {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.device-address {
		font-size: 22rpx;
		color: #999999;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
}
.loading-more, .no-more {
		text-align: center;
		padding: 30rpx 0;
}
.loading-more text, .no-more text {
		font-size: 26rpx;
		color: #999999;
}
