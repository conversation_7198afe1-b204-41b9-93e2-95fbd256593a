
.service-orders-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
}
.header {
		display: flex;
		align-items: center;
		height: 90rpx;
		padding: 0 30rpx;
		background-color: #ffffff;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.search-box {
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.search-input {
		height: 70rpx;
		background-color: #f5f5f5;
		border-radius: 35rpx;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
}
.search-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
}
.search-input input {
		flex: 1;
		height: 70rpx;
		font-size: 28rpx;
}
.clear-icon {
		width: 40rpx;
		height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.clear-icon image {
		width: 24rpx;
		height: 24rpx;
}
.tabs {
		display: flex;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
		margin-bottom: 20rpx;
}
.tab-item {
		flex: 1;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666666;
		position: relative;
}
.tab-item.active {
		color: #ff5a5f;
		font-weight: bold;
}
.tab-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 4rpx;
		background-color: #ff5a5f;
}
.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 200rpx;
}
.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
}
.empty-text {
		font-size: 28rpx;
		color: #999999;
}
.order-list {
		padding: 0 20rpx;
}
.order-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
}
.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
}
.order-id {
		font-size: 26rpx;
		color: #666666;
}
.order-status {
		font-size: 26rpx;
		font-weight: bold;
}
.order-status.pending {
		color: #ff9500;
}
.order-status.processing {
		color: #007aff;
}
.order-status.completed {
		color: #34c759;
}
.order-status.cancelled {
		color: #999999;
}
.order-content {
		display: flex;
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
}
.station-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
		background-color: #f0f0f0;
		flex-shrink: 0;
}
.order-info {
		flex: 1;
}
.station-name {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
}
.station-address {
		font-size: 26rpx;
		color: #666666;
		margin-bottom: 20rpx;
}
.info-item {
		display: flex;
		margin-bottom: 10rpx;
}
.label {
		width: 140rpx;
		font-size: 26rpx;
		color: #999999;
}
.value {
		flex: 1;
		font-size: 26rpx;
		color: #333333;
}
.order-footer {
		padding: 20rpx;
}
.order-time {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 20rpx;
}
.order-btns {
		display: flex;
		justify-content: flex-end;
}
.btn {
		min-width: 160rpx;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		background-color: #ffffff;
		border: 1rpx solid #dddddd;
		border-radius: 30rpx;
		font-size: 26rpx;
		color: #666666;
		margin-left: 20rpx;
}
.btn.primary {
		background-color: #ff5a5f;
		color: #ffffff;
		border-color: #ff5a5f;
}
