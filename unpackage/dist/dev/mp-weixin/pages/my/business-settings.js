"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      isStationVerified: false,
      settings: {
        contactName: "",
        contactPhone: "",
        wechat: "",
        acceptEquipment: true,
        acceptStation: true,
        acceptRecruitment: true,
        smallPrice: "1.00",
        largePrice: "3.00",
        openTime: "08:00",
        closeTime: "20:00",
        weekendOpen: true
      }
    };
  },
  onLoad() {
    this.checkVerificationStatus();
    this.loadSettings();
  },
  methods: {
    // 检查认证状态
    checkVerificationStatus() {
      const userInfoStorage = common_vendor.index.getStorageSync("userInfo");
      if (userInfoStorage) {
        const userInfo = JSON.parse(userInfoStorage);
        this.isStationVerified = userInfo.isStationVerified || false;
      }
    },
    // 加载设置
    loadSettings() {
      const settingsStorage = common_vendor.index.getStorageSync("business_settings");
      if (settingsStorage) {
        this.settings = { ...this.settings, ...JSON.parse(settingsStorage) };
      }
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 前往认证页面
    goToVerify() {
      common_vendor.index.navigateTo({
        url: "/pages/my/verify-station"
      });
    },
    // 开关变化
    onSwitchChange(key, event) {
      this.settings[key] = event.detail.value;
    },
    // 时间变化
    onTimeChange(key, event) {
      this.settings[key] = event.detail.value;
    },
    // 保存设置
    saveSettings() {
      if (!this.settings.contactName || !this.settings.contactPhone) {
        common_vendor.index.showToast({
          title: "请完善联系人信息",
          icon: "none"
        });
        return;
      }
      if (!/^1\d{10}$/.test(this.settings.contactPhone)) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.setStorageSync("business_settings", JSON.stringify(this.settings));
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
      }, 1e3);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: !$data.isStationVerified
  }, !$data.isStationVerified ? {
    d: common_vendor.o((...args) => $options.goToVerify && $options.goToVerify(...args))
  } : {}, {
    e: $data.isStationVerified
  }, $data.isStationVerified ? {
    f: $data.settings.contactName,
    g: common_vendor.o(($event) => $data.settings.contactName = $event.detail.value),
    h: $data.settings.contactPhone,
    i: common_vendor.o(($event) => $data.settings.contactPhone = $event.detail.value),
    j: $data.settings.wechat,
    k: common_vendor.o(($event) => $data.settings.wechat = $event.detail.value),
    l: $data.settings.acceptEquipment,
    m: common_vendor.o(($event) => $options.onSwitchChange("acceptEquipment", $event)),
    n: $data.settings.acceptStation,
    o: common_vendor.o(($event) => $options.onSwitchChange("acceptStation", $event)),
    p: $data.settings.acceptRecruitment,
    q: common_vendor.o(($event) => $options.onSwitchChange("acceptRecruitment", $event)),
    r: $data.settings.smallPrice,
    s: common_vendor.o(($event) => $data.settings.smallPrice = $event.detail.value),
    t: $data.settings.largePrice,
    v: common_vendor.o(($event) => $data.settings.largePrice = $event.detail.value),
    w: common_vendor.t($data.settings.openTime),
    x: $data.settings.openTime,
    y: common_vendor.o(($event) => $options.onTimeChange("openTime", $event)),
    z: common_vendor.t($data.settings.closeTime),
    A: $data.settings.closeTime,
    B: common_vendor.o(($event) => $options.onTimeChange("closeTime", $event)),
    C: $data.settings.weekendOpen,
    D: common_vendor.o(($event) => $options.onSwitchChange("weekendOpen", $event)),
    E: common_vendor.o((...args) => $options.saveSettings && $options.saveSettings(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
