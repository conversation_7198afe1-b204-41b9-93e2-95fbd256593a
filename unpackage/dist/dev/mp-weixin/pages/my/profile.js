"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      userInfo: {
        username: "",
        avatar: "/static/images/default-avatar.png",
        gender: "",
        phoneNumber: "",
        bio: "",
        userId: "",
        isLoggedIn: false
      },
      genderOptions: ["男", "女", "保密"]
    };
  },
  onLoad() {
    this.getUserInfo();
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      const userInfoStorage = common_vendor.index.getStorageSync("userInfo");
      if (userInfoStorage) {
        const storedInfo = JSON.parse(userInfoStorage);
        this.userInfo = {
          ...storedInfo,
          gender: storedInfo.gender || "",
          bio: storedInfo.bio || ""
        };
      } else {
        common_vendor.index.showToast({
          title: "请先登录",
          icon: "none"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }
    },
    // 选择头像
    chooseAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.userInfo.avatar = res.tempFilePaths[0];
        }
      });
    },
    // 显示性别选择器
    showGenderPicker() {
      common_vendor.index.showActionSheet({
        itemList: this.genderOptions,
        success: (res) => {
          this.userInfo.gender = this.genderOptions[res.tapIndex];
        }
      });
    },
    // 格式化手机号
    formatPhone(phone) {
      if (!phone)
        return "未绑定";
      return phone.substr(0, 3) + "****" + phone.substr(7);
    },
    // 修改手机号
    modifyPhone() {
      common_vendor.index.showToast({
        title: "暂不支持修改手机号",
        icon: "none"
      });
    },
    // 前往实名认证页
    goToIdentityVerify() {
      common_vendor.index.navigateTo({
        url: "/pages/my/verify-identity"
      });
    },
    // 前往驿站认证页
    goToStationVerify() {
      common_vendor.index.navigateTo({
        url: "/pages/my/verify-station"
      });
    },
    // 保存个人资料
    saveProfile() {
      if (!this.userInfo.username.trim()) {
        common_vendor.index.showToast({
          title: "昵称不能为空",
          icon: "none"
        });
        return;
      }
      common_vendor.index.setStorageSync("userInfo", JSON.stringify(this.userInfo));
      common_vendor.index.showToast({
        title: "保存成功",
        icon: "success"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: $data.userInfo.avatar,
    d: common_assets._imports_1$4,
    e: common_vendor.o((...args) => $options.chooseAvatar && $options.chooseAvatar(...args)),
    f: $data.userInfo.username,
    g: common_vendor.o(($event) => $data.userInfo.username = $event.detail.value),
    h: common_vendor.t($data.userInfo.gender || "未设置"),
    i: common_assets._imports_6$2,
    j: common_vendor.o((...args) => $options.showGenderPicker && $options.showGenderPicker(...args)),
    k: common_vendor.t($options.formatPhone($data.userInfo.phoneNumber)),
    l: common_vendor.o((...args) => $options.modifyPhone && $options.modifyPhone(...args)),
    m: common_vendor.t($data.userInfo.isVerified ? "已认证" : "未认证"),
    n: common_vendor.n($data.userInfo.isVerified ? "verified" : "unverified"),
    o: common_assets._imports_6$2,
    p: common_vendor.o((...args) => $options.goToIdentityVerify && $options.goToIdentityVerify(...args)),
    q: common_vendor.t($data.userInfo.isStationVerified ? "已认证" : "未认证"),
    r: common_vendor.n($data.userInfo.isStationVerified ? "verified" : "unverified"),
    s: common_assets._imports_6$2,
    t: common_vendor.o((...args) => $options.goToStationVerify && $options.goToStationVerify(...args)),
    v: $data.userInfo.bio,
    w: common_vendor.o(($event) => $data.userInfo.bio = $event.detail.value),
    x: common_vendor.t($data.userInfo.bio ? $data.userInfo.bio.length : 0),
    y: common_vendor.o((...args) => $options.saveProfile && $options.saveProfile(...args))
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
