"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      isEdit: false,
      addressId: null,
      addressForm: {
        id: null,
        name: "",
        phone: "",
        province: "",
        city: "",
        district: "",
        address: "",
        isDefault: false
      }
    };
  },
  onLoad(options) {
    if (options.id) {
      this.isEdit = true;
      this.addressId = parseInt(options.id);
      this.loadAddressData();
    } else {
      this.addressForm.id = (/* @__PURE__ */ new Date()).getTime();
    }
  },
  methods: {
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 加载地址数据
    loadAddressData() {
      const addressStorage = common_vendor.index.getStorageSync("address_list");
      if (addressStorage) {
        const addressList = JSON.parse(addressStorage);
        const address = addressList.find((item) => item.id === this.addressId);
        if (address) {
          this.addressForm = JSON.parse(JSON.stringify(address));
        }
      }
    },
    // 显示地区选择器
    showRegionPicker() {
      setTimeout(() => {
        this.addressForm.province = "浙江省";
        this.addressForm.city = "杭州市";
        this.addressForm.district = "西湖区";
        common_vendor.index.showToast({
          title: "已选择地区",
          icon: "none"
        });
      }, 500);
    },
    // 默认地址开关变化
    onDefaultChange(e) {
      this.addressForm.isDefault = e.detail.value;
    },
    // 保存地址
    saveAddress() {
      if (!this.addressForm.name) {
        common_vendor.index.showToast({
          title: "请输入收货人姓名",
          icon: "none"
        });
        return;
      }
      if (!this.addressForm.phone) {
        common_vendor.index.showToast({
          title: "请输入手机号码",
          icon: "none"
        });
        return;
      }
      if (!/^1\d{10}$/.test(this.addressForm.phone)) {
        common_vendor.index.showToast({
          title: "手机号码格式不正确",
          icon: "none"
        });
        return;
      }
      if (!this.addressForm.province || !this.addressForm.city || !this.addressForm.district) {
        common_vendor.index.showToast({
          title: "请选择所在地区",
          icon: "none"
        });
        return;
      }
      if (!this.addressForm.address) {
        common_vendor.index.showToast({
          title: "请输入详细地址",
          icon: "none"
        });
        return;
      }
      let addressList = [];
      const addressStorage = common_vendor.index.getStorageSync("address_list");
      if (addressStorage) {
        addressList = JSON.parse(addressStorage);
      }
      if (this.addressForm.isDefault) {
        addressList.forEach((item) => {
          if (item.id !== this.addressForm.id) {
            item.isDefault = false;
          }
        });
      }
      if (this.isEdit) {
        const index = addressList.findIndex((item) => item.id === this.addressForm.id);
        if (index > -1) {
          addressList[index] = this.addressForm;
        }
      } else {
        if (addressList.length === 0) {
          this.addressForm.isDefault = true;
        }
        addressList.push(this.addressForm);
      }
      common_vendor.index.setStorageSync("address_list", JSON.stringify(addressList));
      common_vendor.index.showToast({
        title: "保存成功",
        icon: "success"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_vendor.t($data.isEdit ? "编辑地址" : "新增地址"),
    d: $data.addressForm.name,
    e: common_vendor.o(($event) => $data.addressForm.name = $event.detail.value),
    f: $data.addressForm.phone,
    g: common_vendor.o(($event) => $data.addressForm.phone = $event.detail.value),
    h: $data.addressForm.province
  }, $data.addressForm.province ? {
    i: common_vendor.t($data.addressForm.province),
    j: common_vendor.t($data.addressForm.city),
    k: common_vendor.t($data.addressForm.district)
  } : {}, {
    l: common_assets._imports_1$9,
    m: common_vendor.o((...args) => $options.showRegionPicker && $options.showRegionPicker(...args)),
    n: $data.addressForm.address,
    o: common_vendor.o(($event) => $data.addressForm.address = $event.detail.value),
    p: $data.addressForm.isDefault,
    q: common_vendor.o((...args) => $options.onDefaultChange && $options.onDefaultChange(...args)),
    r: common_vendor.o((...args) => $options.saveAddress && $options.saveAddress(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
