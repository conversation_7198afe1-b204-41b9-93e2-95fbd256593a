
.cart-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
}
.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.right-btn {
		font-size: 28rpx;
		color: #666666;
		padding: 10rpx;
}
.empty-cart {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
}
.empty-image {
		width: 240rpx;
		height: 240rpx;
		margin-bottom: 30rpx;
}
.empty-text {
		font-size: 30rpx;
		color: #999999;
		margin-bottom: 40rpx;
}
.go-shopping-btn {
		width: 240rpx;
		height: 80rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.go-shopping-btn text {
		font-size: 30rpx;
		color: #ffffff;
}
.cart-content {
		flex: 1;
		display: flex;
		flex-direction: column;
}
.cart-scroll {
		flex: 1;
		height: 0;
}
.cart-list {
		padding: 20rpx;
}
.cart-item {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
}
.check-box {
		width: 50rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
}
.check-inner {
		width: 36rpx;
		height: 36rpx;
		border-radius: 50%;
		border: 2rpx solid #dddddd;
		display: flex;
		align-items: center;
		justify-content: center;
}
.check-inner.checked {
		background-color: #ff5a5f;
		border-color: #ff5a5f;
}
.check-inner image {
		width: 24rpx;
		height: 24rpx;
}
.product-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
}
.product-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow: hidden;
}
.product-name {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
}
.product-spec {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 20rpx;
}
.price-quantity {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.price {
		font-size: 30rpx;
		font-weight: bold;
		color: #ff5a5f;
}
.quantity-control {
		display: flex;
		align-items: center;
}
.control-btn {
		width: 50rpx;
		height: 50rpx;
		background-color: #f5f5f5;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #333333;
}
.quantity-input {
		width: 70rpx;
		height: 50rpx;
		background-color: #f5f5f5;
		margin: 0 2rpx;
		text-align: center;
		font-size: 26rpx;
}
.delete-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 20rpx;
}
.delete-btn image {
		width: 40rpx;
		height: 40rpx;
}
.checkout-bar {
		height: 100rpx;
		background-color: #ffffff;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.select-all {
		display: flex;
		align-items: center;
}
.select-all text {
		font-size: 28rpx;
		color: #333333;
}
.right-section {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		align-items: center;
}
.total-price {
		margin-right: 20rpx;
		font-size: 28rpx;
		color: #333333;
}
.price-value {
		font-weight: bold;
		color: #ff5a5f;
}
.checkout-btn {
		width: 200rpx;
		height: 70rpx;
		background-color: #ff5a5f;
		border-radius: 35rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.checkout-btn text {
		font-size: 28rpx;
		color: #ffffff;
}
.clear-btn {
		width: 200rpx;
		height: 70rpx;
		background-color: #ff5a5f;
		border-radius: 35rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.clear-btn text {
		font-size: 28rpx;
		color: #ffffff;
}
