
.posts-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.placeholder {
		width: 60rpx;
}
.tabs {
		display: flex;
		background-color: #ffffff;
		border-bottom: 1rpx solid #eeeeee;
}
.tab-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx 0;
		position: relative;
}
.tab-item text {
		font-size: 28rpx;
		color: #666666;
}
.tab-item.active text {
		color: #ff5a5f;
		font-weight: bold;
}
.tab-line {
		position: absolute;
		bottom: 0;
		width: 60rpx;
		height: 6rpx;
		background-color: #ff5a5f;
		border-radius: 3rpx;
}
.content-swiper {
		flex: 1;
}
.scroll-view {
		height: 100%;
}
.empty-view {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 200rpx;
}
.empty-view image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
}
.empty-view text {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 30rpx;
}
.add-btn {
		padding: 15rpx 40rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
}
.add-btn text {
		font-size: 28rpx;
		color: #ffffff;
		margin-bottom: 0;
}
.post-list {
		padding: 20rpx;
}
.post-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.post-image {
		width: 100%;
		height: 300rpx;
		border-radius: 8rpx;
		margin-bottom: 15rpx;
		background-color: #f9f9f9;
}
.post-info {
		margin-bottom: 15rpx;
}
.post-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
		display: block;
}
.post-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 10rpx;
}
.tag {
		font-size: 22rpx;
		padding: 6rpx 16rpx;
		background-color: #FFF0F0;
		color: #FF5A5F;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
		border-radius: 6rpx;
}
.post-address {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #666666;
		margin-bottom: 10rpx;
}
.post-address image {
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
}
.post-price {
		font-size: 24rpx;
		color: #FF5A5F;
}
.price-value {
		font-size: 32rpx;
		font-weight: bold;
}
.price-unit {
		font-size: 24rpx;
		margin-left: 5rpx;
}
.post-actions {
		display: flex;
		justify-content: flex-end;
		border-top: 1rpx solid #eeeeee;
		padding-top: 15rpx;
		margin-top: 15rpx;
}
.action-btn {
		display: flex;
		align-items: center;
		padding: 10rpx 20rpx;
		margin-left: 15rpx;
		border-radius: 30rpx;
}
.action-btn image {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
}
.action-btn text {
		font-size: 24rpx;
}
.edit {
		background-color: #f8f8f8;
		color: #666666;
}
.delete {
		background-color: #FFF0F0;
		color: #FF5A5F;
}
.service-info, .job-info {
		margin-bottom: 15rpx;
}
.service-time {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #666666;
		margin-bottom: 10rpx;
}
.service-time image {
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
}
.job-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
}
.job-salary {
		font-size: 28rpx;
		color: #FF5A5F;
		font-weight: bold;
}
.job-company {
		font-size: 26rpx;
		color: #666666;
		margin-bottom: 10rpx;
		display: block;
}
.device-condition {
		font-size: 24rpx;
		color: #666666;
		margin-bottom: 10rpx;
}
.load-more, .no-more {
		text-align: center;
		padding: 30rpx 0;
}
.load-more text, .no-more text {
		font-size: 24rpx;
		color: #999999;
}
