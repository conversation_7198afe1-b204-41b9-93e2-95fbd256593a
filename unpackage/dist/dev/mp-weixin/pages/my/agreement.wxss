
.agreement-container {
		min-height: 100vh;
		background-color: #ffffff;
}
.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #f5f5f5;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
		margin-right: 60rpx;
}
.content-section {
		padding: 30rpx;
}
.last-update {
		font-size: 24rpx;
		color: #999999;
		display: block;
		margin-bottom: 40rpx;
}
.content-block {
		margin-bottom: 40rpx;
}
.block-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
		display: block;
}
.block-text {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.8;
		margin-bottom: 16rpx;
		display: block;
}
.indent {
		padding-left: 40rpx;
}
