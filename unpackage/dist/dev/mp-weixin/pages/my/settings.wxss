
.settings-container {
		min-height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.settings-list {
		padding: 20rpx;
}
.settings-group {
		background-color: #ffffff;
		border-radius: 8rpx;
		margin-bottom: 20rpx;
}
.group-title {
		padding: 20rpx;
		font-size: 28rpx;
		color: #999999;
		border-bottom: 1rpx solid #f5f5f5;
}
.setting-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
}
.setting-item:last-child {
		border-bottom: none;
}
.item-name {
		font-size: 28rpx;
		color: #333333;
}
.item-arrow image {
		width: 30rpx;
		height: 30rpx;
}
.cache-size {
		font-size: 28rpx;
		color: #999999;
}
.logout-btn {
		margin: 40rpx 20rpx;
		height: 80rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.logout-btn text {
		font-size: 30rpx;
		color: #ffffff;
}
	
	/* 地址相关样式 */
.address-preview {
		padding: 20rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		margin: 0 20rpx 20rpx;
}
.address-info {
		display: flex;
		flex-direction: column;
}
.user-info {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
}
.name {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		margin-right: 15rpx;
}
.phone {
		font-size: 26rpx;
		color: #666666;
		margin-right: 15rpx;
}
.default-tag {
		font-size: 22rpx;
		color: #ffffff;
		background-color: #ff5a5f;
		padding: 4rpx 10rpx;
		border-radius: 4rpx;
}
.address-text {
		font-size: 26rpx;
		color: #333333;
		line-height: 1.4;
}
.no-address {
		padding: 30rpx 20rpx;
		text-align: center;
		color: #999999;
		margin: 0 20rpx 20rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
}
