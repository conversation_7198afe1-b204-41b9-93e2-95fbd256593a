<view class="orders-container"><view class="fixed-header"><view class="header"><view class="back-btn" bindtap="{{b}}"><image src="{{a}}" mode="aspectFit"></image></view><text class="title">我的订单</text><view class="right-btn"><text></text></view></view><view class="tab-section"><view wx:for="{{c}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c]}}" bindtap="{{tab.d}}"><text>{{tab.a}}</text></view></view></view><scroll-view scroll-y class="orders-list" bindscrolltolower="{{j}}" refresher-enabled="{{true}}" refresher-triggered="{{k}}" bindrefresherrefresh="{{l}}" style="{{'height:' + m}}" show-scrollbar="false"><view wx:if="{{d}}" class="empty-tip"><image src="{{e}}" mode="aspectFit"></image><text>暂无订单</text></view><view wx:else class="orders-content"><view wx:for="{{f}}" wx:for-item="order" wx:key="m" class="order-item"><view class="order-header"><view class="store-info"><image src="{{g}}" mode="aspectFit" class="store-icon"></image><text class="store-name">{{order.a}}</text></view><text class="{{['order-status', order.c]}}">{{order.b}}</text></view><view class="order-content" bindtap="{{order.i}}"><image src="{{order.d}}" mode="aspectFill" class="product-image"></image><view class="order-info"><view class="product-name">{{order.e}}</view><view class="product-spec">{{order.f}}</view><view class="price-quantity"><text class="price">¥{{order.g}}</text><text class="quantity">x{{order.h}}</text></view></view></view><view class="order-footer"><view class="total-section"><text>共{{order.j}}件商品</text><text>实付：<text class="total-price">¥{{order.k}}</text></text></view><view class="action-section"><view wx:for="{{order.l}}" wx:for-item="action" wx:key="b" class="{{['action-btn', action.c]}}" catchtap="{{action.d}}"><text>{{action.a}}</text></view></view></view></view></view><view wx:if="{{h}}" class="load-more"><text>加载中...</text></view><view wx:if="{{i}}" class="no-more"><text>已加载全部内容</text></view></scroll-view></view>