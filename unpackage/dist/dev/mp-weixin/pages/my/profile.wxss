
.profile-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
}
.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.placeholder {
		width: 60rpx;
}
.profile-form {
		background-color: #ffffff;
		margin-top: 20rpx;
		padding: 0 30rpx;
}
.form-item {
		padding: 30rpx 0;
		border-bottom: 1rpx solid #eeeeee;
}
.form-item:last-child {
		border-bottom: none;
}
.label {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 20rpx;
		display: block;
}
input {
		height: 80rpx;
		font-size: 28rpx;
		color: #333333;
}
textarea {
		width: 100%;
		height: 200rpx;
		font-size: 28rpx;
		color: #333333;
}
.word-count {
		position: absolute;
		right: 20rpx;
		bottom: 20rpx;
		font-size: 24rpx;
		color: #999999;
}
.avatar-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
}
.avatar-item .label {
		margin-bottom: 0;
}
.avatar-picker {
		position: relative;
}
.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		border: 2rpx solid #eeeeee;
}
.avatar-edit-icon {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 40rpx;
		height: 40rpx;
		background-color: #ff5a5f;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
}
.avatar-edit-icon image {
		width: 24rpx;
		height: 24rpx;
}
.picker-box {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
}
.picker-box text {
		font-size: 28rpx;
		color: #333333;
}
.right-icon {
		width: 30rpx;
		height: 30rpx;
}
.phone-box {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
}
.phone-box text {
		font-size: 28rpx;
		color: #333333;
}
.modify-btn {
		padding: 8rpx 20rpx;
		background-color: #f8f8f8;
		border-radius: 30rpx;
}
.modify-btn text {
		font-size: 24rpx;
		color: #666666;
}
.verify-status-box {
		background-color: #f8f8f8;
		border-radius: 8rpx;
		overflow: hidden;
}
.verify-item {
		display: flex;
		align-items: center;
		padding: 24rpx 20rpx;
		border-bottom: 1rpx solid #eeeeee;
}
.verify-item:last-child {
		border-bottom: none;
}
.verify-item text {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
}
.status-tag {
		font-size: 24rpx;
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
		margin-right: 20rpx;
}
.status-tag.verified {
		background-color: rgba(52, 199, 89, 0.1);
		color: #34c759;
}
.status-tag.unverified {
		background-color: rgba(255, 149, 0, 0.1);
		color: #ff9500;
}
.save-btn {
		margin: 60rpx 30rpx;
		height: 90rpx;
		background-color: #ff5a5f;
		border-radius: 45rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.save-btn text {
		font-size: 32rpx;
		color: #ffffff;
}
