
.history-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
}
.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.right-btn {
		font-size: 28rpx;
		color: #666666;
}
.tab-section {
		display: flex;
		background-color: #ffffff;
		border-bottom: 1rpx solid #eeeeee;
}
.tab-item {
		flex: 1;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666666;
		position: relative;
}
.active-tab {
		color: #ff5a5f;
		font-weight: bold;
}
.active-tab::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 4rpx;
		background-color: #ff5a5f;
}
.history-list {
		flex: 1;
		padding: 20rpx;
}
.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-top: 200rpx;
}
.empty-tip image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 20rpx;
}
.empty-tip text {
		font-size: 28rpx;
		color: #999999;
}
.history-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		display: flex;
		align-items: center;
}
.item-content {
		flex: 1;
}
.item-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 16rpx;
}
.item-meta {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 16rpx;
}
.price, .salary {
		font-size: 28rpx;
		color: #ff5a5f;
		margin-right: 20rpx;
}
.area, .time, .exp, .condition {
		font-size: 26rpx;
		color: #666666;
		margin-right: 20rpx;
}
.address {
		font-size: 26rpx;
		color: #999999;
}
.item-advantage, .item-company, .item-desc {
		font-size: 26rpx;
		color: #666666;
		margin-bottom: 16rpx;
}
.label {
		color: #999999;
}
.item-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 16rpx;
}
.tag {
		font-size: 24rpx;
		color: #ff5a5f;
		background-color: rgba(255, 90, 95, 0.1);
		padding: 4rpx 12rpx;
		border-radius: 4rpx;
		margin-right: 12rpx;
		margin-bottom: 10rpx;
}
.item-time {
		font-size: 24rpx;
		color: #999999;
}
.delete-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.delete-btn image {
		width: 32rpx;
		height: 32rpx;
}
.load-more, .no-more {
		text-align: center;
		padding: 20rpx 0;
}
.load-more text, .no-more text {
		font-size: 24rpx;
		color: #999999;
}
