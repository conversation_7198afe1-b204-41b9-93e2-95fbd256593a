"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      tabs: [
        { name: "全部", status: "all" },
        { name: "待付款", status: "pending" },
        { name: "进行中", status: "processing" },
        { name: "待评价", status: "delivered" },
        { name: "已完成", status: "completed" }
      ],
      activeTab: 0,
      ordersList: [],
      pageNum: 1,
      pageSize: 10,
      hasMore: true,
      isRefreshing: false,
      scrollViewHeight: "calc(100vh - 180rpx)",
      // 默认高度，将在onReady中计算
      loading: false
    };
  },
  computed: {
    filteredOrders() {
      if (this.activeTab === 0) {
        return this.ordersList;
      } else {
        const status = this.tabs[this.activeTab].status;
        return this.ordersList.filter((order) => order.status === status);
      }
    }
  },
  onLoad() {
    this.loadOrdersData();
  },
  onReady() {
    this.calculateScrollViewHeight();
  },
  methods: {
    // 计算滚动区域的高度
    calculateScrollViewHeight() {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      const headerHeight = 180;
      const headerPx = headerHeight * systemInfo.windowWidth / 750;
      const scrollHeight = systemInfo.windowHeight - headerPx;
      this.scrollViewHeight = scrollHeight + "px";
    },
    // 下拉刷新
    onRefresh() {
      this.isRefreshing = true;
      setTimeout(() => {
        this.refreshData();
        this.isRefreshing = false;
      }, 1e3);
    },
    // 刷新数据
    refreshData() {
      this.ordersList = [];
      this.pageNum = 1;
      this.hasMore = true;
      this.loadOrdersData();
    },
    // 加载订单数据
    loadOrdersData() {
      this.loading = true;
      const params = {
        page: this.pageNum,
        pageSize: this.pageSize,
        status: this.activeTab === 0 ? "" : this.getStatusByTab(this.activeTab)
      };
      this.$api.order.getOrderList(params).then((res) => {
        if (res.code === 0) {
          if (this.pageNum === 1) {
            this.ordersList = res.data.list || [];
          } else {
            this.ordersList = [...this.ordersList, ...res.data.list || []];
          }
          this.hasMore = this.ordersList.length < res.data.total;
        } else {
          common_vendor.index.showToast({
            title: res.message || "获取订单数据失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        console.error("获取订单数据失败", err);
        common_vendor.index.showToast({
          title: "网络异常，请稍后重试",
          icon: "none"
        });
      }).finally(() => {
        this.loading = false;
        common_vendor.index.stopPullDownRefresh();
      });
    },
    // 获取状态对应的Tab索引
    getStatusByTab(tabIndex) {
      switch (tabIndex) {
        case 1:
          return "pending";
        case 2:
          return "processing";
        case 3:
          return "delivered";
        case 4:
          return "completed";
        default:
          return "";
      }
    },
    // 加载更多数据
    loadMore() {
      if (!this.hasMore || this.loading)
        return;
      this.pageNum++;
      this.loadOrdersData();
    },
    // 切换标签
    switchTab(index) {
      this.activeTab = index;
    },
    // 获取订单状态文本
    getStatusText(status) {
      switch (status) {
        case "pending":
          return "待付款";
        case "processing":
          return "处理中";
        case "shipping":
          return "配送中";
        case "delivered":
          return "待评价";
        case "completed":
          return "已完成";
        case "cancelled":
          return "已取消";
        default:
          return "未知状态";
      }
    },
    // 获取订单操作按钮
    getActions(status) {
      switch (status) {
        case "pending":
          return [
            { text: "取消订单", type: "cancel" },
            { text: "立即付款", type: "pay", primary: true }
          ];
        case "processing":
          return [
            { text: "查看物流", type: "logistics" },
            { text: "联系客服", type: "contact" }
          ];
        case "delivered":
          return [
            { text: "联系客服", type: "contact" },
            { text: "立即评价", type: "review", primary: true }
          ];
        case "completed":
          return [
            { text: "删除订单", type: "delete" },
            { text: "再次购买", type: "rebuy", primary: true }
          ];
        default:
          return [];
      }
    },
    // 处理订单操作
    handleAction(type, order) {
      switch (type) {
        case "cancel":
          this.cancelOrder(order);
          break;
        case "pay":
          this.payOrder(order);
          break;
        case "logistics":
          this.viewLogistics(order);
          break;
        case "contact":
          this.contactService(order);
          break;
        case "review":
          this.reviewOrder(order);
          break;
        case "delete":
          this.deleteOrder(order);
          break;
        case "rebuy":
          this.rebuyOrder(order);
          break;
      }
    },
    // 取消订单
    cancelOrder(order) {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要取消此订单吗？",
        success: (res) => {
          if (res.confirm) {
            const index = this.ordersList.findIndex((item) => item.id === order.id);
            if (index !== -1) {
              this.ordersList[index].status = "cancelled";
              common_vendor.index.setStorageSync("user_orders", JSON.stringify(this.ordersList));
            }
            common_vendor.index.showToast({
              title: "订单已取消",
              icon: "success"
            });
          }
        }
      });
    },
    // 支付订单
    payOrder(order) {
      common_vendor.index.showLoading({
        title: "正在跳转支付..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showModal({
          title: "模拟支付",
          content: "这是一个模拟的支付过程，点击确定表示支付成功",
          success: (res) => {
            if (res.confirm) {
              const index = this.ordersList.findIndex((item) => item.id === order.id);
              if (index !== -1) {
                this.ordersList[index].status = "processing";
                common_vendor.index.setStorageSync("user_orders", JSON.stringify(this.ordersList));
              }
              common_vendor.index.showToast({
                title: "支付成功",
                icon: "success"
              });
            }
          }
        });
      }, 1e3);
    },
    // 查看物流
    viewLogistics(order) {
      common_vendor.index.showToast({
        title: "正在查询物流信息",
        icon: "none"
      });
    },
    // 联系客服
    contactService(order) {
      common_vendor.index.showToast({
        title: "正在连接客服...",
        icon: "none"
      });
    },
    // 评价订单
    reviewOrder(order) {
      common_vendor.index.navigateTo({
        url: "/pages/my/review?orderId=" + order.id
      });
    },
    // 删除订单
    deleteOrder(order) {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除此订单吗？删除后无法恢复",
        success: (res) => {
          if (res.confirm) {
            this.ordersList = this.ordersList.filter((item) => item.id !== order.id);
            common_vendor.index.setStorageSync("user_orders", JSON.stringify(this.ordersList));
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    },
    // 再次购买
    rebuyOrder(order) {
      common_vendor.index.showToast({
        title: "已加入购物车",
        icon: "success"
      });
    },
    // 查看订单详情
    viewOrderDetail(order) {
      common_vendor.index.navigateTo({
        url: "/pages/my/order-detail?id=" + order.id
      });
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: common_vendor.n($data.activeTab === index ? "active-tab" : ""),
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    d: $options.filteredOrders.length === 0
  }, $options.filteredOrders.length === 0 ? {
    e: common_assets._imports_7$2
  } : {
    f: common_vendor.f($options.filteredOrders, (order, index, i0) => {
      return {
        a: common_vendor.t(order.storeName),
        b: common_vendor.t($options.getStatusText(order.status)),
        c: common_vendor.n("status-" + order.status),
        d: order.productImage,
        e: common_vendor.t(order.productName),
        f: common_vendor.t(order.productSpec),
        g: common_vendor.t(order.price),
        h: common_vendor.t(order.quantity),
        i: common_vendor.o(($event) => $options.viewOrderDetail(order), order.id),
        j: common_vendor.t(order.quantity),
        k: common_vendor.t(order.totalAmount),
        l: common_vendor.f($options.getActions(order.status), (action, actionIndex, i1) => {
          return {
            a: common_vendor.t(action.text),
            b: actionIndex,
            c: common_vendor.n(action.primary ? "primary-btn" : ""),
            d: common_vendor.o(($event) => $options.handleAction(action.type, order), actionIndex)
          };
        }),
        m: order.id
      };
    }),
    g: common_assets._imports_2$3
  }, {
    h: $data.hasMore && $options.filteredOrders.length > 0
  }, $data.hasMore && $options.filteredOrders.length > 0 ? {} : {}, {
    i: !$data.hasMore && $options.filteredOrders.length > 0
  }, !$data.hasMore && $options.filteredOrders.length > 0 ? {} : {}, {
    j: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args)),
    k: $data.isRefreshing,
    l: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args)),
    m: $data.scrollViewHeight
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
