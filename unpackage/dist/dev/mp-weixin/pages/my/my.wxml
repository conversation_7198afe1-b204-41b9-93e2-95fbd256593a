<view class="my-container"><view wx:if="{{a}}" class="user-info-section"><view class="avatar-container"><image class="avatar" src="{{b}}"></image></view><view class="user-details"><text class="username">{{c}}</text><text class="user-id">ID: {{d}}</text></view><view class="edit-profile" bindtap="{{e}}"><text>编辑资料</text></view></view><view wx:else class="user-info-section not-login" bindtap="{{g}}"><view class="avatar-container"><image class="avatar" src="{{f}}"></image></view><view class="user-details"><text class="username">点击登录</text><text class="user-id">登录后体验更多功能</text></view><view class="login-arrow"><text class="arrow">></text></view></view><view class="function-section"><view class="section-title">常用功能</view><view class="function-grid"><view wx:for="{{h}}" wx:for-item="item" wx:key="d" class="function-item" bindtap="{{item.e}}"><view class="{{['function-icon', item.b]}}"><image src="{{item.a}}" mode="aspectFit"></image></view><text class="function-name">{{item.c}}</text></view></view></view></view>