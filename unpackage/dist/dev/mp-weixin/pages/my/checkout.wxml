<view class="checkout-container"><view class="header"><view class="back-btn" bindtap="{{b}}"><image src="{{a}}" mode="aspectFit"></image></view><text class="title">确认订单</text></view><view class="address-section" bindtap="{{m}}"><view wx:if="{{c}}" class="address-empty"><image src="{{d}}" mode="aspectFit" class="location-icon"></image><text class="add-address">请添加收货地址</text><image src="{{e}}" mode="aspectFit" class="right-icon"></image></view><view wx:else class="address-info"><view class="address-details"><view class="user-info"><text class="name">{{f}}</text><text class="phone">{{g}}</text></view><text class="address-text">{{h}}{{i}}{{j}}{{k}}</text></view><image src="{{l}}" mode="aspectFit" class="right-icon"></image></view></view><view class="goods-section"><view class="section-title"><text>商品信息</text></view><view class="goods-list"><view wx:for="{{n}}" wx:for-item="item" wx:key="g" class="goods-item"><image src="{{item.a}}" mode="aspectFill" class="goods-image"></image><view class="goods-info"><text class="goods-name">{{item.b}}</text><text wx:if="{{item.c}}" class="goods-spec">{{item.d}}</text><view class="goods-price-qty"><text class="goods-price">¥{{item.e}}</text><text class="goods-qty">×{{item.f}}</text></view></view></view></view></view><view class="delivery-section"><view class="section-title"><text>配送方式</text></view><view class="delivery-options"><view class="{{['delivery-option', p && 'selected']}}" bindtap="{{q}}"><text>快递配送</text><view wx:if="{{o}}" class="select-circle"><view class="inner-circle"></view></view><view wx:else class="select-circle"></view></view><view class="{{['delivery-option', s && 'selected']}}" bindtap="{{t}}"><text>到店自提</text><view wx:if="{{r}}" class="select-circle"><view class="inner-circle"></view></view><view wx:else class="select-circle"></view></view></view></view><view class="payment-section"><view class="section-title"><text>支付方式</text></view><view class="payment-options"><view class="{{['payment-option', x && 'selected']}}" bindtap="{{y}}"><view class="option-left"><image src="{{v}}" mode="aspectFit" class="payment-icon"></image><text>微信支付</text></view><view wx:if="{{w}}" class="select-circle"><view class="inner-circle"></view></view><view wx:else class="select-circle"></view></view><view wx:if="{{z}}" class="{{['payment-option', D && 'selected']}}" bindtap="{{E}}"><view class="option-left"><image src="{{A}}" mode="aspectFit" class="payment-icon"></image><text>余额支付</text><text class="balance-info">(可用余额: ¥{{B}})</text></view><view wx:if="{{C}}" class="select-circle"><view class="inner-circle"></view></view><view wx:else class="select-circle"></view></view></view></view><view class="remark-section"><view class="section-item"><text>订单备注</text><input class="remark-input" type="text" placeholder="选填，请填写其他需求" maxlength="50" value="{{F}}" bindinput="{{G}}"/></view></view><view class="amount-section"><view class="amount-item"><text>商品金额</text><text>¥{{H}}</text></view><view class="amount-item"><text>运费</text><text>¥{{I}}</text></view></view><view class="submit-bar"><view class="total-amount"><text>合计：</text><text class="amount">¥{{J}}</text></view><view bindtap="{{L}}" class="{{['submit-btn', M && 'disabled']}}"><text>{{K}}</text></view></view><weixin-pay wx:if="{{P}}" class="r" u-r="wxPay" bindpaySuccess="{{O}}" u-i="0a0cd21a-0" bind:__l="__l" u-p="{{P}}"></weixin-pay></view>