"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      tabs: ["全部", "待接单", "进行中", "已完成"],
      currentTab: 0,
      orderList: [],
      searchKeyword: "",
      hasMoreData: true,
      isLoading: false
    };
  },
  computed: {
    // 过滤后的订单列表
    filteredOrderList() {
      let filtered = this.orderList;
      if (this.currentTab !== 0) {
        const statusMap = ["", "pending", "processing", "completed"];
        filtered = filtered.filter((item) => item.status === statusMap[this.currentTab]);
      }
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        filtered = filtered.filter((item) => {
          return item.orderId.toLowerCase().includes(keyword) || item.stationName.toLowerCase().includes(keyword) || item.stationAddress.toLowerCase().includes(keyword);
        });
      }
      return filtered;
    }
  },
  onLoad() {
    this.loadOrders();
  },
  methods: {
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 切换选项卡
    changeTab(index) {
      this.currentTab = index;
    },
    // 搜索
    onSearch(e) {
      this.searchKeyword = e.detail.value;
    },
    // 清除搜索
    clearSearch() {
      this.searchKeyword = "";
    },
    // 加载订单数据
    loadOrders() {
      const storedOrders = common_vendor.index.getStorageSync("serviceOrders");
      if (storedOrders) {
        this.orderList = JSON.parse(storedOrders);
        this.hasMoreData = false;
      } else {
        const sampleOrders = [
          {
            orderId: "SO2023112401",
            stationName: "菜鸟驿站-西湖文化广场店",
            stationAddress: "杭州市西湖区文化广场12号",
            stationContact: "李经理",
            stationPhone: "13800138001",
            stationImage: "/static/images/station1.jpg",
            workTime: "2023-12-05 09:00-18:00",
            hourlyRate: 20,
            duration: 8,
            totalAmount: 160,
            workContent: "负责快递包裹的收发和管理工作，需要熟悉菜鸟驿站系统操作。",
            requirements: "有驿站工作经验优先，工作认真负责，有良好的沟通能力。",
            status: "pending",
            createTime: /* @__PURE__ */ new Date("2023-11-24 10:30:00")
          },
          {
            orderId: "SO2023112402",
            stationName: "京东快递-滨江星光店",
            stationAddress: "杭州市滨江区星光大道125号",
            stationContact: "张店长",
            stationPhone: "13900139002",
            stationImage: "/static/images/station2.jpg",
            workTime: "2023-12-06 14:00-22:00",
            hourlyRate: 22,
            duration: 8,
            totalAmount: 176,
            workContent: "负责京东快递的收派件工作，需要熟练使用PDA设备扫描和登记包裹。",
            requirements: "有快递行业经验，能吃苦耐劳，有责任心。",
            status: "pending",
            createTime: /* @__PURE__ */ new Date("2023-11-24 14:15:00")
          },
          {
            orderId: "SO2023112301",
            stationName: "顺丰驿站-湖滨银泰店",
            stationAddress: "杭州市上城区湖滨银泰5楼",
            stationContact: "王站长",
            stationPhone: "13700137003",
            stationImage: "/static/images/station3.jpg",
            workTime: "2023-12-03 10:00-18:00",
            hourlyRate: 25,
            duration: 8,
            totalAmount: 200,
            workContent: "负责顺丰快递的收发工作，包括包裹验收、分类、存储和交付。",
            requirements: "形象气质佳，熟悉快递业务流程，有良好的服务意识。",
            status: "processing",
            createTime: /* @__PURE__ */ new Date("2023-11-23 09:45:00")
          },
          {
            orderId: "SO2023112201",
            stationName: "中通驿站-城西银泰店",
            stationAddress: "杭州市拱墅区城西银泰2楼",
            stationContact: "周经理",
            stationPhone: "13600136004",
            stationImage: "/static/images/station4.jpg",
            workTime: "2023-11-25 09:30-17:30",
            hourlyRate: 18,
            duration: 8,
            totalAmount: 144,
            workContent: "负责中通快递包裹的收发和客户服务工作。",
            requirements: "有耐心，做事细心，能适应高强度工作。",
            status: "completed",
            createTime: /* @__PURE__ */ new Date("2023-11-22 11:20:00")
          },
          {
            orderId: "SO2023112101",
            stationName: "韵达快递-西溪印象城店",
            stationAddress: "杭州市余杭区五常大道59号",
            stationContact: "赵站长",
            stationPhone: "13500135005",
            stationImage: "/static/images/station5.jpg",
            workTime: "2023-11-24 13:00-21:00",
            hourlyRate: 20,
            duration: 8,
            totalAmount: 160,
            workContent: "负责韵达快递的收派件和日常运营工作。",
            requirements: "熟悉电脑操作，有快递行业经验优先考虑。",
            status: "completed",
            createTime: /* @__PURE__ */ new Date("2023-11-21 16:30:00")
          }
        ];
        common_vendor.index.setStorageSync("serviceOrders", JSON.stringify(sampleOrders));
        this.orderList = sampleOrders;
        this.hasMoreData = false;
      }
      this.isLoading = false;
    },
    // 格式化日期
    formatDate(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, "0");
      const day = String(d.getDate()).padStart(2, "0");
      const hours = String(d.getHours()).padStart(2, "0");
      const minutes = String(d.getMinutes()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case "pending":
          return "待接单";
        case "processing":
          return "进行中";
        case "completed":
          return "已完成";
        case "cancelled":
          return "已取消";
        default:
          return "未知状态";
      }
    },
    // 查看订单详情
    viewOrderDetail(item) {
      common_vendor.index.setStorageSync("currentOrder", JSON.stringify(item));
      common_vendor.index.navigateTo({
        url: "/pages/my/service-order-detail"
      });
    },
    // 联系驿站
    contactStation(item) {
      common_vendor.index.showActionSheet({
        itemList: ["拨打电话", "复制微信号"],
        success: (res) => {
          if (res.tapIndex === 0) {
            common_vendor.index.makePhoneCall({
              phoneNumber: item.stationPhone,
              fail: () => {
                common_vendor.index.showToast({
                  title: "拨号已取消",
                  icon: "none"
                });
              }
            });
          } else if (res.tapIndex === 1) {
            common_vendor.index.setClipboardData({
              data: item.stationPhone,
              success: () => {
                common_vendor.index.showToast({
                  title: "微信号已复制",
                  icon: "success"
                });
              }
            });
          }
        }
      });
    },
    // 接单
    applyOrder(item) {
      if (item.status !== "pending") {
        common_vendor.index.showToast({
          title: "该订单不可接",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认接单",
        content: `您确定要接${item.stationName}的顶班订单吗？`,
        success: (res) => {
          if (res.confirm) {
            const index = this.orderList.findIndex((order) => order.orderId === item.orderId);
            if (index !== -1) {
              this.orderList[index].status = "processing";
              common_vendor.index.setStorageSync("serviceOrders", JSON.stringify(this.orderList));
              common_vendor.index.showToast({
                title: "接单成功",
                icon: "success"
              });
            }
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_assets._imports_1,
    d: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearch && $options.onSearch(...args)]),
    e: $data.searchKeyword,
    f: $data.searchKeyword
  }, $data.searchKeyword ? {
    g: common_assets._imports_2$1,
    h: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    i: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab),
        b: index,
        c: $data.currentTab === index ? 1 : "",
        d: common_vendor.o(($event) => $options.changeTab(index), index)
      };
    }),
    j: $options.filteredOrderList.length === 0
  }, $options.filteredOrderList.length === 0 ? common_vendor.e({
    k: common_assets._imports_3$8,
    l: $data.searchKeyword
  }, $data.searchKeyword ? {} : {
    m: common_vendor.t($data.tabs[$data.currentTab])
  }) : {
    n: common_vendor.f($options.filteredOrderList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.orderId),
        b: common_vendor.t($options.getStatusText(item.status)),
        c: common_vendor.n(item.status),
        d: item.stationImage,
        e: common_vendor.t(item.stationName),
        f: common_vendor.t(item.stationAddress),
        g: common_vendor.t(item.workTime),
        h: common_vendor.t(item.hourlyRate),
        i: common_vendor.t(item.duration),
        j: common_vendor.t($options.formatDate(item.createTime)),
        k: common_vendor.o(($event) => $options.contactStation(item), index),
        l: common_vendor.o(($event) => $options.applyOrder(item), index),
        m: index,
        n: common_vendor.o(($event) => $options.viewOrderDetail(item), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
