"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      addressList: []
    };
  },
  onShow() {
    this.loadAddressList();
  },
  methods: {
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 加载地址列表
    loadAddressList() {
      const addressStorage = common_vendor.index.getStorageSync("address_list");
      if (addressStorage) {
        this.addressList = JSON.parse(addressStorage);
      } else {
        this.addressList = [];
        common_vendor.index.setStorageSync("address_list", JSON.stringify(this.addressList));
      }
    },
    // 格式化手机号
    formatPhone(phone) {
      if (!phone)
        return "";
      return phone.substr(0, 3) + "****" + phone.substr(7);
    },
    // 选择地址
    selectAddress(address) {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        const prevPage = pages[pages.length - 2];
        if (prevPage.route === "pages/my/checkout") {
          prevPage.$vm.selectedAddress = address;
        }
        common_vendor.index.navigateBack();
      }
    },
    // 编辑地址
    editAddress(address) {
      common_vendor.index.navigateTo({
        url: `/pages/my/address-edit?id=${address.id}`
      });
    },
    // 删除地址
    deleteAddress(index) {
      common_vendor.index.showModal({
        title: "删除地址",
        content: "确定要删除该地址吗？",
        success: (res) => {
          if (res.confirm) {
            this.addressList.splice(index, 1);
            common_vendor.index.setStorageSync("address_list", JSON.stringify(this.addressList));
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    },
    // 跳转到添加地址页面
    goToAddAddress() {
      common_vendor.index.navigateTo({
        url: "/pages/my/address-edit"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_vendor.f($data.addressList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.name),
        b: common_vendor.t($options.formatPhone(item.phone)),
        c: item.isDefault
      }, item.isDefault ? {} : {}, {
        d: common_vendor.t(item.province),
        e: common_vendor.t(item.city),
        f: common_vendor.t(item.district),
        g: common_vendor.t(item.address),
        h: common_vendor.o(($event) => $options.selectAddress(item), index),
        i: common_vendor.o(($event) => $options.editAddress(item), index),
        j: common_vendor.o(($event) => $options.deleteAddress(index), index),
        k: index
      });
    }),
    d: common_assets._imports_1$4,
    e: common_assets._imports_2$2,
    f: $data.addressList.length === 0
  }, $data.addressList.length === 0 ? {
    g: common_assets._imports_3$9
  } : {}, {
    h: common_vendor.o((...args) => $options.goToAddAddress && $options.goToAddAddress(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
