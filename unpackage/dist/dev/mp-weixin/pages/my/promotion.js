"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      userInfo: {
        username: "测试用户",
        avatar: "/static/images/default-avatar.png",
        inviteCode: "YZB888"
      },
      totalEarnings: 0,
      todayEarnings: 0,
      pendingEarnings: 0,
      withdrawableAmount: 0,
      invitedCount: 0,
      inviteGoal: 20,
      inviteReward: 100,
      monthlyInvites: 0,
      monthlyGoal: 10,
      monthlyReward: 50,
      weeklyInvites: 0,
      activeRate: 0,
      invitedFriends: []
    };
  },
  onLoad() {
    this.loadUserInfo();
    this.loadPromotionData();
    this.loadInvitedFriends();
  },
  methods: {
    // 加载用户信息
    loadUserInfo() {
      const userInfoStorage = common_vendor.index.getStorageSync("userInfo");
      if (userInfoStorage) {
        const userInfo = JSON.parse(userInfoStorage);
        this.userInfo.username = userInfo.username;
        this.userInfo.avatar = userInfo.avatar;
        if (!userInfo.inviteCode) {
          const inviteCode = "YZB" + Math.floor(1e3 + Math.random() * 9e3);
          userInfo.inviteCode = inviteCode;
          this.userInfo.inviteCode = inviteCode;
          common_vendor.index.setStorageSync("userInfo", JSON.stringify(userInfo));
        } else {
          this.userInfo.inviteCode = userInfo.inviteCode;
        }
      }
    },
    // 加载推广数据
    loadPromotionData() {
      const promotionStorage = common_vendor.index.getStorageSync("promotion_data");
      if (promotionStorage) {
        const data = JSON.parse(promotionStorage);
        this.totalEarnings = data.totalEarnings;
        this.todayEarnings = data.todayEarnings;
        this.pendingEarnings = data.pendingEarnings;
        this.withdrawableAmount = data.withdrawableAmount;
        this.invitedCount = data.invitedCount;
        this.monthlyInvites = data.monthlyInvites;
        this.weeklyInvites = data.weeklyInvites;
        this.activeRate = data.activeRate;
      } else {
        this.totalEarnings = 658.88;
        this.todayEarnings = 25.5;
        this.pendingEarnings = 120;
        this.withdrawableAmount = 538.88;
        this.invitedCount = 12;
        this.monthlyInvites = 5;
        this.weeklyInvites = 2;
        this.activeRate = 75;
        const promotionData = {
          totalEarnings: this.totalEarnings,
          todayEarnings: this.todayEarnings,
          pendingEarnings: this.pendingEarnings,
          withdrawableAmount: this.withdrawableAmount,
          invitedCount: this.invitedCount,
          monthlyInvites: this.monthlyInvites,
          weeklyInvites: this.weeklyInvites,
          activeRate: this.activeRate
        };
        common_vendor.index.setStorageSync("promotion_data", JSON.stringify(promotionData));
      }
    },
    // 加载邀请的好友
    loadInvitedFriends() {
      const friendsStorage = common_vendor.index.getStorageSync("invited_friends");
      if (friendsStorage) {
        this.invitedFriends = JSON.parse(friendsStorage);
      } else {
        this.invitedFriends = [
          {
            id: "user_001",
            username: "张三",
            avatar: "/static/images/avatar1.png",
            joinTime: Date.now() - 3 * 24 * 60 * 60 * 1e3,
            // 3天前
            reward: 5,
            active: true
          },
          {
            id: "user_002",
            username: "李四",
            avatar: "/static/images/avatar2.png",
            joinTime: Date.now() - 7 * 24 * 60 * 60 * 1e3,
            // 7天前
            reward: 5,
            active: true
          },
          {
            id: "user_003",
            username: "王五",
            avatar: "/static/images/avatar3.png",
            joinTime: Date.now() - 15 * 24 * 60 * 60 * 1e3,
            // 15天前
            reward: 5,
            active: false
          },
          {
            id: "user_004",
            username: "赵六",
            avatar: "/static/images/avatar4.png",
            joinTime: Date.now() - 30 * 24 * 60 * 60 * 1e3,
            // 30天前
            reward: 5,
            active: true
          },
          {
            id: "user_005",
            username: "孙七",
            avatar: "/static/images/avatar5.png",
            joinTime: Date.now() - 1 * 24 * 60 * 60 * 1e3,
            // 1天前
            reward: 5,
            active: true
          },
          {
            id: "user_006",
            username: "周八",
            avatar: "/static/images/avatar6.png",
            joinTime: Date.now() - 2 * 24 * 60 * 60 * 1e3,
            // 2天前
            reward: 5,
            active: false
          }
        ];
        common_vendor.index.setStorageSync("invited_friends", JSON.stringify(this.invitedFriends));
      }
    },
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 提现
    withdraw() {
      if (this.withdrawableAmount < 100) {
        common_vendor.index.showModal({
          title: "提示",
          content: "提现金额不足100元，暂时无法提现",
          showCancel: false
        });
        return;
      }
      common_vendor.index.navigateTo({
        url: "/pages/my/withdraw"
      });
    },
    // 查看收益明细
    viewEarningsDetail() {
      common_vendor.index.navigateTo({
        url: "/pages/my/earnings-detail"
      });
    },
    // 保存推广码
    savePromoCode() {
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
      }, 1500);
    },
    // 分享到微信
    shareToWechat() {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    },
    // 分享到朋友圈
    shareToMoments() {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    },
    // 复制邀请链接
    copyInviteLink() {
      const inviteLink = `https://yizhanbang.com/invite?code=${this.userInfo.inviteCode}`;
      common_vendor.index.setClipboardData({
        data: inviteLink,
        success: () => {
          common_vendor.index.showToast({
            title: "链接已复制",
            icon: "success"
          });
        }
      });
    },
    // 查看所有好友
    viewAllFriends() {
      common_vendor.index.navigateTo({
        url: "/pages/my/invited-friends"
      });
    },
    // 查看推广规则
    showRules() {
      common_vendor.index.showModal({
        title: "推广规则",
        content: "1. 邀请好友扫码进入小程序，您将获得5元奖励\n2. 无需好友消费，仅通过二维码推广即可获得奖励\n3. 当月邀请10人及以上，额外奖励50元\n4. 累计邀请20人及以上，额外奖励100元\n5. 佣金将在好友进入小程序24小时后自动结算到您的账户",
        showCancel: false
      });
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_assets._imports_1$8,
    d: common_vendor.o((...args) => $options.showRules && $options.showRules(...args)),
    e: common_vendor.t($data.totalEarnings.toFixed(2)),
    f: common_vendor.o((...args) => $options.withdraw && $options.withdraw(...args)),
    g: common_vendor.o((...args) => $options.viewEarningsDetail && $options.viewEarningsDetail(...args)),
    h: common_vendor.t($data.todayEarnings.toFixed(2)),
    i: common_vendor.t($data.pendingEarnings.toFixed(2)),
    j: common_vendor.t($data.withdrawableAmount.toFixed(2)),
    k: common_vendor.t($data.invitedCount),
    l: $data.invitedCount / $data.inviteGoal * 100 + "%",
    m: common_vendor.t($data.inviteGoal),
    n: common_vendor.t($data.inviteReward.toFixed(2)),
    o: common_vendor.t($data.monthlyInvites),
    p: $data.monthlyInvites / $data.monthlyGoal * 100 + "%",
    q: common_vendor.t($data.monthlyGoal),
    r: common_vendor.t($data.monthlyReward.toFixed(2)),
    s: common_vendor.o((...args) => $options.savePromoCode && $options.savePromoCode(...args)),
    t: $data.userInfo.avatar,
    v: common_vendor.t($data.userInfo.username),
    w: common_vendor.t($data.userInfo.inviteCode),
    x: common_assets._imports_2$6,
    y: common_assets._imports_3$3,
    z: common_vendor.o((...args) => $options.shareToWechat && $options.shareToWechat(...args)),
    A: common_assets._imports_4$5,
    B: common_vendor.o((...args) => $options.shareToMoments && $options.shareToMoments(...args)),
    C: common_assets._imports_5$2,
    D: common_vendor.o((...args) => $options.copyInviteLink && $options.copyInviteLink(...args)),
    E: common_assets._imports_6$2,
    F: common_vendor.o((...args) => $options.viewAllFriends && $options.viewAllFriends(...args)),
    G: $data.invitedFriends.length === 0
  }, $data.invitedFriends.length === 0 ? {
    H: common_assets._imports_7$2
  } : {
    I: common_vendor.f($data.invitedFriends.slice(0, 5), (friend, index, i0) => {
      return {
        a: friend.avatar,
        b: common_vendor.t(friend.username),
        c: common_vendor.t($options.formatTime(friend.joinTime)),
        d: common_vendor.t(friend.active ? "活跃" : "未活跃"),
        e: common_vendor.n(friend.active ? "active" : "inactive"),
        f: common_vendor.t(friend.reward.toFixed(2)),
        g: friend.id
      };
    })
  }, {
    J: common_vendor.t($data.activeRate),
    K: common_vendor.t($data.weeklyInvites),
    L: common_vendor.t($data.monthlyInvites)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
