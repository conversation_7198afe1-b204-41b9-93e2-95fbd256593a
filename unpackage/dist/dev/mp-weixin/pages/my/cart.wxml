<view class="cart-container"><view class="header"><view class="back-btn" bindtap="{{b}}"><image src="{{a}}" mode="aspectFit"></image></view><text class="title">购物车</text><view class="right-btn" bindtap="{{d}}"><text>{{c}}</text></view></view><view wx:if="{{e}}" class="empty-cart"><image src="{{f}}" mode="aspectFit" class="empty-image"></image><text class="empty-text">购物车还是空的</text><view class="go-shopping-btn" bindtap="{{g}}"><text>去选购</text></view></view><view wx:else class="cart-content"><scroll-view scroll-y class="cart-scroll"><view class="cart-list"><view wx:for="{{h}}" wx:for-item="item" wx:key="o" class="cart-item"><view class="check-box" bindtap="{{item.d}}"><view class="{{['check-inner', item.c && 'checked']}}"><image wx:if="{{item.a}}" src="{{item.b}}" mode="aspectFit"></image></view></view><image src="{{item.e}}" mode="aspectFill" class="product-image"></image><view class="product-info"><text class="product-name">{{item.f}}</text><text class="product-spec">{{item.g}}</text><view class="price-quantity"><text class="price">¥{{item.h}}</text><view class="quantity-control"><view class="control-btn" bindtap="{{item.i}}">-</view><input type="number" class="quantity-input" value="{{item.j}}" bindinput="{{item.k}}"/><view class="control-btn" bindtap="{{item.l}}">+</view></view></view></view><view wx:if="{{i}}" class="delete-btn" bindtap="{{item.n}}"><image src="{{item.m}}" mode="aspectFit"></image></view></view></view></scroll-view><view class="checkout-bar"><view class="select-all" bindtap="{{m}}"><view class="check-box"><view class="{{['check-inner', l && 'checked']}}"><image wx:if="{{j}}" src="{{k}}" mode="aspectFit"></image></view></view><text>全选</text></view><view wx:if="{{n}}" class="right-section"><view class="total-price"><text>合计：</text><text class="price-value">¥{{o}}</text></view><view class="checkout-btn" bindtap="{{q}}"><text>结算({{p}})</text></view></view><view wx:else class="right-section"><view class="clear-btn" bindtap="{{r}}"><text>清空购物车</text></view></view></view></view></view>