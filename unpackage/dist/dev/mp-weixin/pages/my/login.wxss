/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-container.data-v-0a40c652 {
  padding: 30rpx;
  min-height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.login-container .header.data-v-0a40c652 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 60rpx 0;
}
.login-container .header .logo.data-v-0a40c652 {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}
.login-container .header .title.data-v-0a40c652 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.login-container .form-container.data-v-0a40c652 {
  margin-top: 20rpx;
}
.login-container .form-container .tabs.data-v-0a40c652 {
  display: flex;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 30rpx;
}
.login-container .form-container .tabs .tab.data-v-0a40c652 {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 30rpx;
  color: #666;
  position: relative;
}
.login-container .form-container .tabs .tab.active.data-v-0a40c652 {
  color: #007AFF;
  font-weight: bold;
}
.login-container .form-container .tabs .tab.active.data-v-0a40c652::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #007AFF;
}
.login-container .form-container .form-title.data-v-0a40c652 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.login-container .form-container .form-title text.data-v-0a40c652 {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}
.login-container .form-container .form-title .back-btn.data-v-0a40c652 {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: normal;
}
.login-container .form-container .input-group.data-v-0a40c652 {
  margin-bottom: 30rpx;
}
.login-container .form-container .input-group .label.data-v-0a40c652 {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}
.login-container .form-container .input-group .input-box.data-v-0a40c652 {
  display: flex;
  align-items: center;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  height: 90rpx;
  position: relative;
}
.login-container .form-container .input-group .input-box .prefix.data-v-0a40c652 {
  color: #666;
  margin-right: 10rpx;
  font-size: 28rpx;
}
.login-container .form-container .input-group .input-box input.data-v-0a40c652 {
  flex: 1;
  height: 90rpx;
  font-size: 30rpx;
}
.login-container .form-container .input-group .input-box .verify-btn.data-v-0a40c652 {
  width: 200rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  background-color: #007AFF;
  color: #fff;
  border-radius: 6rpx;
  font-size: 26rpx;
}
.login-container .form-container .input-group .input-box .verify-btn.disabled.data-v-0a40c652 {
  background-color: #ccc;
}
.login-container .form-container .input-group .input-box .eye-icon.data-v-0a40c652 {
  width: 40rpx;
  height: 40rpx;
  padding: 0 10rpx;
}
.login-container .form-container .input-group .input-box .eye-icon image.data-v-0a40c652 {
  width: 40rpx;
  height: 40rpx;
}
.login-container .form-container .login-btn.data-v-0a40c652 {
  background-color: #007AFF;
  color: #fff;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 8rpx;
  text-align: center;
  font-size: 32rpx;
  margin-top: 50rpx;
}
.login-container .form-container .login-btn.disabled.data-v-0a40c652 {
  background-color: #ccc;
}
.login-container .form-container .options.data-v-0a40c652 {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}
.login-container .form-container .options .option-link.data-v-0a40c652 {
  color: #007AFF;
  font-size: 28rpx;
}
.login-container .form-container .agreement.data-v-0a40c652 {
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}
.login-container .form-container .agreement checkbox.data-v-0a40c652 {
  transform: scale(0.7);
  margin-right: 6rpx;
}
.login-container .form-container .agreement .link.data-v-0a40c652 {
  color: #007AFF;
}