<view class="wallet-container"><view class="header"><view class="back-btn" bindtap="{{b}}"><image src="{{a}}" mode="aspectFit"></image></view><text class="title">我的钱包</text><view class="right-btn" bindtap="{{d}}"><image src="{{c}}" mode="aspectFit"></image></view></view><view class="wallet-info"><view class="balance-section"><text class="balance-label">账户余额(元)</text><text class="balance-value">{{e}}</text><view class="action-buttons"><view class="action-btn" bindtap="{{f}}"><text>充值</text></view><view class="action-btn" bindtap="{{g}}"><text>提现</text></view></view></view><view class="asset-section"><view class="asset-item" bindtap="{{i}}"><text class="asset-value">{{h}}</text><text class="asset-label">积分</text></view><view class="asset-line"></view><view class="asset-item" bindtap="{{k}}"><text class="asset-value">{{j}}</text><text class="asset-label">优惠券</text></view><view class="asset-line"></view><view class="asset-item" bindtap="{{m}}"><text class="asset-value">{{l}}</text><text class="asset-label">红包</text></view></view></view><view class="service-section"><view class="service-item" bindtap="{{o}}"><image src="{{n}}" mode="aspectFit" class="service-icon"></image><text class="service-name">账单明细</text></view><view class="service-item" bindtap="{{q}}"><image src="{{p}}" mode="aspectFit" class="service-icon"></image><text class="service-name">银行卡</text></view><view class="service-item" bindtap="{{s}}"><image src="{{r}}" mode="aspectFit" class="service-icon"></image><text class="service-name">安全中心</text></view><view class="service-item" bindtap="{{v}}"><image src="{{t}}" mode="aspectFit" class="service-icon"></image><text class="service-name">联系客服</text></view></view><view class="record-section"><view class="section-header"><text class="section-title">交易记录</text><view class="more-btn" bindtap="{{x}}"><text>更多</text><image src="{{w}}" mode="aspectFit" class="right-icon"></image></view></view><view wx:if="{{y}}" class="empty-tip"><image src="{{z}}" mode="aspectFit"></image><text>暂无交易记录</text></view><view wx:else class="record-list"><view wx:for="{{A}}" wx:for-item="record" wx:key="g" class="record-item"><view class="record-left"><text class="record-title">{{record.a}}</text><text class="record-time">{{record.b}}</text></view><view class="record-right"><text class="{{['record-amount', record.e]}}">{{record.c}}{{record.d}}</text><text class="record-status">{{record.f}}</text></view></view></view></view></view>