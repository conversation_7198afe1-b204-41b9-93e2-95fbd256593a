"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const WxPay = () => "../../components/wx-pay.js";
const _sfc_main = {
  components: {
    WxPay
  },
  data() {
    return {
      selectedAddress: null,
      orderItems: [],
      // 用于存储所有订单商品，包括购物车或直接购买的商品
      deliveryMethod: "express",
      paymentMethod: "wechat",
      deliveryFee: 0,
      remark: "",
      buyNow: false,
      // 标记是否为直接购买
      isSubmitting: false,
      // 是否正在提交订单
      createdOrderId: "",
      // 创建的订单ID
      showPayPanel: false,
      // 是否显示支付面板
      userBalance: 0
      // 用户余额（单位：分）
    };
  },
  computed: {
    // 商品总额
    goodsAmount() {
      return this.orderItems.reduce((total, item) => {
        return total + item.price * item.quantity;
      }, 0);
    },
    // 总金额
    totalAmount() {
      return this.goodsAmount + this.deliveryFee;
    }
  },
  onLoad(options) {
    this.checkLoginStatus();
    if (options.buyNow && options.goodsId) {
      this.buyNow = true;
      this.loadSingleGoodsInfo(options.goodsId, options.quantity || 1);
    } else {
      this.loadCartItems();
    }
    this.loadAddress();
    this.calculateDeliveryFee();
  },
  methods: {
    // 检查用户登录状态
    checkLoginStatus() {
      const token = common_vendor.index.getStorageSync("token");
      const userInfoStr = common_vendor.index.getStorageSync("userInfo");
      if (!token || !userInfoStr) {
        common_vendor.index.showToast({
          title: "请先登录",
          icon: "none"
        });
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/my/login"
          });
        }, 1500);
        return;
      }
      const userInfo = JSON.parse(userInfoStr);
      this.userBalance = userInfo.balance || 0;
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 加载单个商品信息（直接购买）
    async loadSingleGoodsInfo(goodsId, quantity) {
      if (goodsId) {
        let found = false;
        try {
          const res = await this.$api.product.getProductDetail(goodsId);
          if (res.code === 0 && res.data) {
            const product = res.data;
            const orderItem = {
              id: product.id,
              name: product.name,
              price: product.price,
              image: product.cover,
              spec: product.spec || "标准规格",
              quantity: parseInt(quantity) || 1
            };
            this.orderItems = [orderItem];
            found = true;
          }
        } catch (err) {
          console.error("获取商品信息失败", err);
          common_vendor.index.showToast({
            title: "获取商品信息失败",
            icon: "none"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        }
        if (!found) {
          common_vendor.index.showToast({
            title: "商品信息不存在",
            icon: "none"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        }
      }
      this.calculateDeliveryFee();
    },
    // 加载购物车中选中的商品
    loadCartItems() {
      const cartStorage = common_vendor.index.getStorageSync("cart");
      if (cartStorage) {
        const cart = JSON.parse(cartStorage);
        this.orderItems = cart.filter((item) => item.selected);
        if (this.orderItems.length === 0) {
          common_vendor.index.showToast({
            title: "请先选择商品",
            icon: "none"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        }
      }
    },
    // 加载地址
    loadAddress() {
      const addressStorage = common_vendor.index.getStorageSync("address_list");
      if (addressStorage) {
        const addresses = JSON.parse(addressStorage);
        this.selectedAddress = addresses.find((addr) => addr.isDefault) || (addresses.length > 0 ? addresses[0] : null);
      }
    },
    // 选择地址
    selectAddress() {
      common_vendor.index.navigateTo({
        url: "/pages/my/address-list?select=true"
      });
    },
    // 选择配送方式
    selectDelivery(method) {
      this.deliveryMethod = method;
      this.calculateDeliveryFee();
    },
    // 选择支付方式
    selectPayment(method) {
      if (method === "balance" && this.userBalance < this.convertToFen(this.totalAmount)) {
        common_vendor.index.showToast({
          title: "余额不足",
          icon: "none"
        });
        return;
      }
      this.paymentMethod = method;
    },
    // 计算配送费
    calculateDeliveryFee() {
      if (this.deliveryMethod === "express") {
        this.deliveryFee = this.goodsAmount >= 99 ? 0 : 10;
      } else {
        this.deliveryFee = 0;
      }
    },
    // 格式化手机号
    formatPhone(phone) {
      if (!phone)
        return "";
      return phone.substr(0, 3) + "****" + phone.substr(7);
    },
    // 将元转换为分
    convertToFen(amount) {
      return Math.round(amount * 100);
    },
    // 提交订单
    async submitOrder() {
      if (!this.selectedAddress) {
        common_vendor.index.showToast({
          title: "请选择收货地址",
          icon: "none"
        });
        return;
      }
      if (this.isSubmitting)
        return;
      this.isSubmitting = true;
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      try {
        const orderItems = this.orderItems.map((item) => ({
          goods_id: item.id,
          name: item.name,
          price: this.convertToFen(item.price),
          // 转换为分
          quantity: item.quantity,
          image: item.image,
          spec: item.spec || ""
        }));
        const totalFee = this.convertToFen(this.totalAmount);
        const result = await common_vendor.wx$1.cloud.callFunction({
          name: "createOrder",
          data: {
            items: orderItems,
            address: this.selectedAddress,
            delivery_method: this.deliveryMethod,
            payment_method: this.paymentMethod,
            goods_amount: this.convertToFen(this.goodsAmount),
            delivery_fee: this.convertToFen(this.deliveryFee),
            total_fee: totalFee,
            remark: this.remark
          }
        });
        if (result.result && result.result.code === 0) {
          this.createdOrderId = result.result.data.order_id;
          if (!this.buyNow) {
            const cartStorage = common_vendor.index.getStorageSync("cart");
            if (cartStorage) {
              const cart = JSON.parse(cartStorage);
              const newCart = cart.filter((item) => !item.selected);
              common_vendor.index.setStorageSync("cart", JSON.stringify(newCart));
            }
          }
          if (this.paymentMethod === "wechat") {
            this.showPayPanel = true;
          } else if (this.paymentMethod === "balance") {
            await this.balancePay();
          }
        } else {
          common_vendor.index.showToast({
            title: result.result.msg || "创建订单失败",
            icon: "none"
          });
        }
      } catch (err) {
        console.error("提交订单失败", err);
        common_vendor.index.showToast({
          title: "订单提交异常，请重试",
          icon: "none"
        });
      } finally {
        this.isSubmitting = false;
        common_vendor.index.hideLoading();
      }
    },
    // 余额支付
    async balancePay() {
      common_vendor.index.showLoading({
        title: "支付中..."
      });
      try {
        const result = await common_vendor.wx$1.cloud.callFunction({
          name: "balancePay",
          data: {
            orderId: this.createdOrderId,
            amount: this.convertToFen(this.totalAmount)
          }
        });
        if (result.result && result.result.code === 0) {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "支付成功",
            icon: "success"
          });
          const userInfoStr = common_vendor.index.getStorageSync("userInfo");
          if (userInfoStr) {
            const userInfo = JSON.parse(userInfoStr);
            userInfo.balance -= this.convertToFen(this.totalAmount);
            common_vendor.index.setStorageSync("userInfo", JSON.stringify(userInfo));
          }
          setTimeout(() => {
            common_vendor.index.redirectTo({
              url: "/pages/my/orders"
            });
          }, 1500);
        } else {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: result.result.msg || "支付失败",
            icon: "none"
          });
        }
      } catch (err) {
        console.error("余额支付失败", err);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "支付失败，请重试",
          icon: "none"
        });
      }
    },
    // 处理支付成功回调
    handlePaySuccess(payResult) {
      console.log("支付成功", payResult);
      common_vendor.index.showToast({
        title: "支付成功",
        icon: "success"
      });
      setTimeout(() => {
        common_vendor.index.redirectTo({
          url: "/pages/my/orders"
        });
      }, 1500);
    }
  }
};
if (!Array) {
  const _component_wx_pay = common_vendor.resolveComponent("wx-pay");
  _component_wx_pay();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: !$data.selectedAddress
  }, !$data.selectedAddress ? {
    d: common_assets._imports_1$2,
    e: common_assets._imports_6$2
  } : {
    f: common_vendor.t($data.selectedAddress.name),
    g: common_vendor.t($options.formatPhone($data.selectedAddress.phone)),
    h: common_vendor.t($data.selectedAddress.province),
    i: common_vendor.t($data.selectedAddress.city),
    j: common_vendor.t($data.selectedAddress.district),
    k: common_vendor.t($data.selectedAddress.address),
    l: common_assets._imports_6$2
  }, {
    m: common_vendor.o((...args) => $options.selectAddress && $options.selectAddress(...args)),
    n: common_vendor.f($data.orderItems, (item, index, i0) => {
      return common_vendor.e({
        a: item.image,
        b: common_vendor.t(item.name),
        c: item.spec
      }, item.spec ? {
        d: common_vendor.t(item.spec)
      } : {}, {
        e: common_vendor.t(item.price.toFixed(2)),
        f: common_vendor.t(item.quantity),
        g: index
      });
    }),
    o: $data.deliveryMethod === "express"
  }, $data.deliveryMethod === "express" ? {} : {}, {
    p: $data.deliveryMethod === "express" ? 1 : "",
    q: common_vendor.o(($event) => $options.selectDelivery("express")),
    r: $data.deliveryMethod === "self"
  }, $data.deliveryMethod === "self" ? {} : {}, {
    s: $data.deliveryMethod === "self" ? 1 : "",
    t: common_vendor.o(($event) => $options.selectDelivery("self")),
    v: common_assets._imports_3$6,
    w: $data.paymentMethod === "wechat"
  }, $data.paymentMethod === "wechat" ? {} : {}, {
    x: $data.paymentMethod === "wechat" ? 1 : "",
    y: common_vendor.o(($event) => $options.selectPayment("wechat")),
    z: $data.userBalance > 0
  }, $data.userBalance > 0 ? common_vendor.e({
    A: common_assets._imports_1$7,
    B: common_vendor.t($data.userBalance / 100),
    C: $data.paymentMethod === "balance"
  }, $data.paymentMethod === "balance" ? {} : {}, {
    D: $data.paymentMethod === "balance" ? 1 : "",
    E: common_vendor.o(($event) => $options.selectPayment("balance"))
  }) : {}, {
    F: $data.remark,
    G: common_vendor.o(($event) => $data.remark = $event.detail.value),
    H: common_vendor.t($options.goodsAmount.toFixed(2)),
    I: common_vendor.t($data.deliveryFee.toFixed(2)),
    J: common_vendor.t($options.totalAmount.toFixed(2)),
    K: common_vendor.t($data.isSubmitting ? "提交中..." : "提交订单"),
    L: common_vendor.o((...args) => $options.submitOrder && $options.submitOrder(...args)),
    M: $data.isSubmitting ? 1 : "",
    N: common_vendor.sr("wxPay", "0a0cd21a-0"),
    O: common_vendor.o($options.handlePaySuccess),
    P: common_vendor.p({
      ["order-id"]: $data.createdOrderId,
      amount: $options.convertToFen($options.totalAmount),
      description: "驿站帮-商品购买",
      visible: $data.showPayPanel
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
