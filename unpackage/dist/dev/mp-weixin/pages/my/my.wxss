
.my-container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 40rpx;
}
.user-info-section {
		background-color: #ffffff;
		padding: 40rpx 30rpx;
		display: flex;
		align-items: center;
}
.user-info-section.not-login {
		background-color: #ffffff;
}
.avatar-container {
		position: relative;
}
.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		border: 4rpx solid #f0f0f0;
}
.user-details {
		flex: 1;
		padding-left: 20rpx;
}
.username {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
		display: block;
		margin-bottom: 6rpx;
}
.user-id {
		font-size: 24rpx;
		color: #999999;
}
.edit-profile {
		padding: 10rpx 20rpx;
		background-color: #f8f8f8;
		border-radius: 30rpx;
}
.edit-profile text {
		font-size: 24rpx;
		color: #666666;
}
.login-arrow {
		padding: 10rpx;
}
.arrow {
		font-size: 30rpx;
		color: #999999;
}
.function-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
}
.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 30rpx;
}
.function-grid {
		display: flex;
		flex-wrap: wrap;
}
.function-item {
		width: 25%;
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 40rpx;
}
.function-icon {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 10rpx;
}
.function-icon image {
		width: 50rpx;
		height: 50rpx;
}
.function-name {
		font-size: 26rpx;
		color: #666666;
		text-align: center;
}
.bg-blue {
		background-color: rgba(0, 122, 255, 0.1);
}
.bg-yellow {
		background-color: rgba(255, 204, 0, 0.1);
}
.bg-green {
		background-color: rgba(52, 199, 89, 0.1);
}
.bg-purple {
		background-color: rgba(175, 82, 222, 0.1);
}
.bg-pink {
		background-color: rgba(255, 45, 85, 0.1);
}
.bg-orange {
		background-color: rgba(255, 149, 0, 0.1);
}
.bg-gray {
		background-color: rgba(142, 142, 147, 0.1);
}
