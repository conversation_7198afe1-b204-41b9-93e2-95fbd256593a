
.wallet-container {
		min-height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn, .right-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image, .right-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.wallet-info {
		background-color: #ff5a5f;
		padding: 40rpx 30rpx 30rpx;
		color: #ffffff;
		border-bottom-left-radius: 30rpx;
		border-bottom-right-radius: 30rpx;
		box-shadow: 0 10rpx 20rpx rgba(255, 90, 95, 0.2);
}
.balance-section {
		text-align: center;
		margin-bottom: 40rpx;
}
.balance-label {
		font-size: 28rpx;
		opacity: 0.9;
		margin-bottom: 20rpx;
		display: block;
}
.balance-value {
		font-size: 60rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
		display: block;
}
.action-buttons {
		display: flex;
		justify-content: center;
}
.action-btn {
		width: 160rpx;
		height: 60rpx;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 20rpx;
}
.action-btn text {
		font-size: 28rpx;
}
.asset-section {
		display: flex;
		justify-content: space-around;
		margin-top: 20rpx;
}
.asset-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
}
.asset-value {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
}
.asset-label {
		font-size: 24rpx;
		opacity: 0.9;
}
.asset-line {
		width: 2rpx;
		height: 50rpx;
		background-color: rgba(255, 255, 255, 0.3);
		align-self: center;
}
.service-section {
		margin: 20rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
}
.service-item {
		display: flex;
		flex-direction: column;
		align-items: center;
}
.service-icon {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 10rpx;
}
.service-name {
		font-size: 24rpx;
		color: #666666;
}
.record-section {
		margin: 0 20rpx 20rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 30rpx;
}
.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
}
.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
}
.more-btn {
		display: flex;
		align-items: center;
}
.more-btn text {
		font-size: 24rpx;
		color: #999999;
		margin-right: 10rpx;
}
.right-icon {
		width: 24rpx;
		height: 24rpx;
}
.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40rpx 0;
}
.empty-tip image {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 20rpx;
}
.empty-tip text {
		font-size: 28rpx;
		color: #999999;
}
.record-list {
}
.record-item {
		display: flex;
		justify-content: space-between;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
}
.record-item:last-child {
		border-bottom: none;
}
.record-left {
		display: flex;
		flex-direction: column;
}
.record-title {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 10rpx;
}
.record-time {
		font-size: 24rpx;
		color: #999999;
}
.record-right {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
}
.record-amount {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
}
.income {
		color: #ff5a5f;
}
.expense {
		color: #333333;
}
.record-status {
		font-size: 24rpx;
		color: #34c759;
}
