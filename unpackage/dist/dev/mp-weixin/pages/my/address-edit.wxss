
.address-edit-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
}
.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.form-container {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 0 30rpx;
}
.form-item {
		padding: 30rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
}
.form-label {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 20rpx;
		display: block;
}
.form-input {
		height: 80rpx;
		font-size: 28rpx;
		color: #333333;
}
.form-textarea {
		width: 100%;
		height: 160rpx;
		font-size: 28rpx;
		color: #333333;
}
.region-picker {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
}
.region-text {
		font-size: 28rpx;
		color: #333333;
}
.placeholder {
		font-size: 28rpx;
		color: #999999;
}
.arrow-icon {
		width: 30rpx;
		height: 30rpx;
}
.switch-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.switch-item .form-label {
		margin-bottom: 0;
}
.submit-btn {
		position: fixed;
		bottom: 30rpx;
		left: 30rpx;
		right: 30rpx;
		height: 80rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.submit-btn text {
		font-size: 30rpx;
		color: #ffffff;
		font-weight: bold;
}
