"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      cartList: [],
      isEditing: false,
      isAllSelected: false
    };
  },
  computed: {
    // 计算选中商品总价
    totalPrice() {
      return this.cartList.reduce((total, item) => {
        return item.selected ? total + item.price * item.quantity : total;
      }, 0);
    },
    // 计算选中商品数量
    selectedCount() {
      return this.cartList.filter((item) => item.selected).length;
    }
  },
  onShow() {
    this.loadCartData();
    this.checkSelectAll();
  },
  methods: {
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 加载购物车数据
    loadCartData() {
      const cart = common_vendor.index.getStorageSync("cart");
      if (cart) {
        this.cartList = JSON.parse(cart);
      }
    },
    // 保存购物车数据
    saveCartData() {
      common_vendor.index.setStorageSync("cart", JSON.stringify(this.cartList));
    },
    // 切换编辑模式
    editCart() {
      this.isEditing = !this.isEditing;
    },
    // 去购物
    goShopping() {
      common_vendor.index.switchTab({
        url: "/pages/station/station"
      });
    },
    // 切换单个商品选中状态
    toggleSelect(index) {
      this.cartList[index].selected = !this.cartList[index].selected;
      this.saveCartData();
      this.checkSelectAll();
    },
    // 切换全选状态
    toggleSelectAll() {
      this.isAllSelected = !this.isAllSelected;
      this.cartList.forEach((item) => {
        item.selected = this.isAllSelected;
      });
      this.saveCartData();
    },
    // 检查是否全部选中
    checkSelectAll() {
      this.isAllSelected = this.cartList.length > 0 && this.cartList.every((item) => item.selected);
    },
    // 减少商品数量
    decreaseQuantity(index) {
      if (this.cartList[index].quantity > 1) {
        this.cartList[index].quantity--;
        this.saveCartData();
      }
    },
    // 增加商品数量
    increaseQuantity(index) {
      const item = this.cartList[index];
      if (item.quantity < (item.stock || 99)) {
        item.quantity++;
        this.saveCartData();
      } else {
        common_vendor.index.showToast({
          title: "已达到最大库存",
          icon: "none"
        });
      }
    },
    // 移除单个商品
    removeItem(index) {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要移除该商品吗？",
        success: (res) => {
          if (res.confirm) {
            this.cartList.splice(index, 1);
            this.saveCartData();
            this.checkSelectAll();
          }
        }
      });
    },
    // 清空购物车
    clearCart() {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要清空购物车吗？",
        success: (res) => {
          if (res.confirm) {
            this.cartList = [];
            this.saveCartData();
          }
        }
      });
    },
    // 结算
    checkout() {
      if (this.selectedCount === 0) {
        common_vendor.index.showToast({
          title: "请选择要结算的商品",
          icon: "none"
        });
        return;
      }
      const checkoutOrder = {
        products: this.cartList.filter((item) => item.selected),
        from: "cart"
        // 标记为购物车结算
      };
      common_vendor.index.setStorageSync("tempOrder", JSON.stringify(checkoutOrder));
      common_vendor.index.navigateTo({
        url: "/pages/my/checkout"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_vendor.t($data.isEditing ? "完成" : "编辑"),
    d: common_vendor.o((...args) => $options.editCart && $options.editCart(...args)),
    e: $data.cartList.length === 0
  }, $data.cartList.length === 0 ? {
    f: common_assets._imports_1$6,
    g: common_vendor.o((...args) => $options.goShopping && $options.goShopping(...args))
  } : common_vendor.e({
    h: common_vendor.f($data.cartList, (item, index, i0) => {
      return common_vendor.e({
        a: item.selected
      }, item.selected ? {
        b: common_assets._imports_2$5
      } : {}, {
        c: item.selected ? 1 : "",
        d: common_vendor.o(($event) => $options.toggleSelect(index), index),
        e: item.productImage,
        f: common_vendor.t(item.productName),
        g: common_vendor.t(item.productSpec),
        h: common_vendor.t(item.price ? item.price.toFixed(2) : "0.00"),
        i: common_vendor.o(($event) => $options.decreaseQuantity(index), index),
        j: item.quantity,
        k: common_vendor.o(($event) => item.quantity = $event.detail.value, index),
        l: common_vendor.o(($event) => $options.increaseQuantity(index), index)
      }, $data.isEditing ? {
        m: common_assets._imports_2$2,
        n: common_vendor.o(($event) => $options.removeItem(index), index)
      } : {}, {
        o: index
      });
    }),
    i: $data.isEditing,
    j: $data.isAllSelected
  }, $data.isAllSelected ? {
    k: common_assets._imports_2$5
  } : {}, {
    l: $data.isAllSelected ? 1 : "",
    m: common_vendor.o((...args) => $options.toggleSelectAll && $options.toggleSelectAll(...args)),
    n: !$data.isEditing
  }, !$data.isEditing ? {
    o: common_vendor.t($options.totalPrice.toFixed(2)),
    p: common_vendor.t($options.selectedCount),
    q: common_vendor.o((...args) => $options.checkout && $options.checkout(...args))
  } : {
    r: common_vendor.o((...args) => $options.clearCart && $options.clearCart(...args))
  }));
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
