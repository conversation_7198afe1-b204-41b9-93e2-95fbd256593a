
.verify-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
}
.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn, .right-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image, .right-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.status-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 50rpx 30rpx;
		background-color: #ffffff;
}
.status-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
}
.status-icon image {
		width: 100%;
		height: 100%;
}
.status-text {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
}
.status-desc {
		font-size: 28rpx;
		color: #666666;
		text-align: center;
		line-height: 1.6;
}
.form-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
}
.form-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 30rpx;
}
.form-item {
		margin-bottom: 30rpx;
}
.item-label {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 20rpx;
		display: block;
}
.optional {
		font-size: 24rpx;
		color: #999999;
		font-weight: normal;
}
.item-input {
		width: 100%;
		height: 90rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333333;
		box-sizing: border-box;
}
.phone-input-wrapper {
		display: flex;
		align-items: center;
}
.phone-input-wrapper .item-input {
		flex: 1;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
}
.verify-btn {
		width: 200rpx;
		height: 90rpx;
		background-color: #ff5a5f;
		border-top-right-radius: 8rpx;
		border-bottom-right-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.verify-btn.disabled {
		background-color: #cccccc;
}
.verify-btn text {
		font-size: 28rpx;
		color: #ffffff;
}
.upload-area, .image-preview {
		width: 100%;
		height: 360rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		overflow: hidden;
		position: relative;
}
.upload-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
}
.upload-placeholder image {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 20rpx;
}
.upload-placeholder text {
		font-size: 28rpx;
		color: #999999;
}
.preview-image {
		width: 100%;
		height: 100%;
}
.delete-btn {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		width: 60rpx;
		height: 60rpx;
		background-color: rgba(0, 0, 0, 0.5);
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.delete-btn image {
		width: 30rpx;
		height: 30rpx;
}
.agreement-item {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
}
.checkbox {
		width: 40rpx;
		height: 40rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.checkbox-inner {
		width: 24rpx;
		height: 24rpx;
		background-color: #ff5a5f;
		border-radius: 4rpx;
}
.agreement-text {
		font-size: 26rpx;
		color: #666666;
}
.agreement-link {
		font-size: 26rpx;
		color: #ff5a5f;
}
.submit-btn {
		width: 100%;
		height: 90rpx;
		background-color: #ff5a5f;
		border-radius: 45rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
}
.submit-btn.disabled {
		background-color: #cccccc;
}
.submit-btn text {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: bold;
}
.tips-section {
		margin-top: 30rpx;
}
.tip-item {
		display: flex;
		margin-bottom: 16rpx;
}
.dot {
		margin-right: 10rpx;
		color: #999999;
}
.tip-text {
		font-size: 24rpx;
		color: #999999;
		line-height: 1.6;
}
