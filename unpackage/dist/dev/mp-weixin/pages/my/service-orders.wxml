<view class="service-orders-container"><view class="header"><view class="back-btn" bindtap="{{b}}"><image src="{{a}}" mode="aspectFit"></image></view><text class="title">顶班接单</text></view><view class="search-box"><view class="search-input"><image src="{{c}}" mode="aspectFit" class="search-icon"></image><input type="text" placeholder="搜索站点名称、订单号" bindinput="{{d}}" value="{{e}}"/><view wx:if="{{f}}" class="clear-icon" bindtap="{{h}}"><image src="{{g}}" mode="aspectFit"></image></view></view></view><view class="tabs"><view wx:for="{{i}}" wx:for-item="tab" wx:key="b" class="{{['tab-item', tab.c && 'active']}}" bindtap="{{tab.d}}"><text>{{tab.a}}</text></view></view><view wx:if="{{j}}" class="empty-state"><image src="{{k}}" mode="aspectFit" class="empty-icon"></image><text wx:if="{{l}}" class="empty-text">未找到相关订单</text><text wx:else class="empty-text">暂无{{m}}订单</text></view><view wx:else class="order-list"><view wx:for="{{n}}" wx:for-item="item" wx:key="m" class="order-item" bindtap="{{item.n}}"><view class="order-header"><text class="order-id">订单号：{{item.a}}</text><text class="{{['order-status', item.c]}}">{{item.b}}</text></view><view class="order-content"><image src="{{item.d}}" mode="aspectFill" class="station-image"></image><view class="order-info"><text class="station-name">{{item.e}}</text><text class="station-address">{{item.f}}</text><view class="info-item"><text class="label">工作时间</text><text class="value">{{item.g}}</text></view><view class="info-item"><text class="label">时薪</text><text class="value">{{item.h}}元/小时</text></view><view class="info-item"><text class="label">工作时长</text><text class="value">{{item.i}}小时</text></view></view></view><view class="order-footer"><view class="order-time">发布时间：{{item.j}}</view><view class="order-btns"><view class="btn" catchtap="{{item.k}}">立即联系</view><view class="btn primary" catchtap="{{item.l}}">立即接单</view></view></view></view></view></view>