
.promotion-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
}
.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn, .right-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image, .right-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.earnings-overview {
		background-color: #ff5a5f;
		padding: 40rpx 30rpx;
		color: #ffffff;
}
.total-earnings {
		text-align: center;
		margin-bottom: 40rpx;
}
.earnings-label {
		font-size: 28rpx;
		opacity: 0.9;
		margin-bottom: 20rpx;
		display: block;
}
.earnings-value {
		font-size: 60rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
		display: block;
}
.action-buttons {
		display: flex;
		justify-content: center;
}
.action-btn {
		width: 160rpx;
		height: 60rpx;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 20rpx;
}
.action-btn text {
		font-size: 28rpx;
}
.earnings-stats {
		display: flex;
		justify-content: space-around;
}
.stat-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
}
.stat-value {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
}
.stat-label {
		font-size: 24rpx;
		opacity: 0.9;
}
.promotion-stats {
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
}
.stat-card {
		width: 48%;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.card-top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
}
.card-title {
		font-size: 28rpx;
		color: #666666;
}
.card-value {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
}
.progress-bar {
		height: 10rpx;
		background-color: #f5f5f5;
		border-radius: 5rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
}
.progress-inner {
		height: 100%;
		background-color: #ff5a5f;
		border-radius: 5rpx;
		transition: width 0.3s;
}
.card-bottom {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.goal-text, .reward-text {
		font-size: 24rpx;
		color: #999999;
}
.promo-code-section, .commission-rules, .friends-section {
		margin: 20rpx 30rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
}
.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
}
.save-btn, .more-btn {
		display: flex;
		align-items: center;
}
.save-btn text, .more-btn text {
		font-size: 26rpx;
		color: #666666;
}
.more-btn image {
		width: 24rpx;
		height: 24rpx;
		margin-left: 6rpx;
}
.promo-code-card {
		background-color: #f9f9f9;
		border-radius: 12rpx;
		padding: 30rpx;
}
.card-info {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
}
.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		margin-right: 20rpx;
}
.user-info {
		flex: 1;
}
.username {
		font-size: 30rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
}
.code-label {
		font-size: 26rpx;
		color: #666666;
}
.qrcode-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 30rpx;
}
.qrcode {
		width: 300rpx;
		height: 300rpx;
		margin-bottom: 20rpx;
}
.scan-tip {
		font-size: 26rpx;
		color: #666666;
}
.share-btns {
		display: flex;
		justify-content: space-around;
}
.share-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
}
.share-btn image {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 10rpx;
}
.share-btn text {
		font-size: 24rpx;
		color: #666666;
}
.rule-list {
}
.rule-item {
		display: flex;
		margin-bottom: 30rpx;
}
.rule-item:last-child {
		margin-bottom: 0;
}
.rule-icon {
		width: 50rpx;
		height: 50rpx;
		background-color: #ff5a5f;
		color: #ffffff;
		border-radius: 25rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		font-weight: bold;
		margin-right: 20rpx;
}
.rule-content {
		flex: 1;
}
.rule-title {
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
}
.rule-desc {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.4;
}
.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40rpx 0;
}
.empty-tip image {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 20rpx;
}
.empty-tip text {
		font-size: 28rpx;
		color: #999999;
}
.friends-list {
}
.friend-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
}
.friend-item:last-child {
		border-bottom: none;
}
.friend-avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		margin-right: 20rpx;
}
.friend-info {
		flex: 1;
}
.friend-name {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 10rpx;
		display: block;
}
.join-time {
		font-size: 24rpx;
		color: #999999;
}
.friend-status {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
}
.status {
		font-size: 24rpx;
		padding: 6rpx 12rpx;
		border-radius: 20rpx;
		margin-bottom: 10rpx;
}
.status.active {
		background-color: rgba(46, 204, 113, 0.1);
		color: #2ecc71;
}
.status.inactive {
		background-color: rgba(189, 195, 199, 0.1);
		color: #bdc3c7;
}
.reward {
		font-size: 28rpx;
		color: #ff5a5f;
		font-weight: bold;
}
.invite-stats {
		display: flex;
		margin-top: 30rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.stats-item {
		flex: 1;
		text-align: center;
		padding: 30rpx 0;
		border-right: 1px solid #f0f0f0;
}
.stats-item:last-child {
		border-right: none;
}
.stats-label {
		font-size: 24rpx;
		color: #999999;
		display: block;
		margin-bottom: 10rpx;
}
.stats-value {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
}
