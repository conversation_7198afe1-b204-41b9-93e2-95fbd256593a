
.favorites-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
}
.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.placeholder {
		width: 60rpx;
}
.tabs {
		display: flex;
		background-color: #ffffff;
		border-bottom: 1rpx solid #eeeeee;
}
.tab-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx 0;
		position: relative;
}
.tab-item text {
		font-size: 28rpx;
		color: #666666;
}
.tab-item.active text {
		color: #ff5a5f;
		font-weight: bold;
}
.tab-line {
		position: absolute;
		bottom: 0;
		width: 60rpx;
		height: 6rpx;
		background-color: #ff5a5f;
		border-radius: 3rpx;
}
.content-swiper {
		flex: 1;
}
.scroll-view {
		height: 100%;
}
.empty-view {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 200rpx;
}
.empty-view image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
}
.empty-view text {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 30rpx;
}
.add-btn {
		padding: 15rpx 40rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
}
.add-btn text {
		font-size: 28rpx;
		color: #ffffff;
		margin-bottom: 0;
}
.favorites-list {
		padding: 20rpx;
}
.favorite-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.favorite-image {
		width: 100%;
		height: 300rpx;
		border-radius: 8rpx;
		margin-bottom: 15rpx;
		background-color: #f9f9f9;
}
.favorite-info {
		margin-bottom: 15rpx;
}
.favorite-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
		display: block;
}
.favorite-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 10rpx;
}
.tag {
		font-size: 22rpx;
		padding: 6rpx 16rpx;
		background-color: #FFF0F0;
		color: #FF5A5F;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
		border-radius: 6rpx;
}
.favorite-address {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #666666;
		margin-bottom: 10rpx;
}
.favorite-address image {
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
}
.favorite-price {
		font-size: 24rpx;
		color: #FF5A5F;
}
.price-value {
		font-size: 32rpx;
		font-weight: bold;
}
.favorite-actions {
		display: flex;
		justify-content: flex-end;
		border-top: 1rpx solid #eeeeee;
		padding-top: 15rpx;
		margin-top: 15rpx;
}
.action-btn {
		display: flex;
		align-items: center;
		padding: 10rpx 20rpx;
		margin-left: 15rpx;
		border-radius: 30rpx;
}
.action-btn image {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
}
.action-btn text {
		font-size: 24rpx;
}
.cancel {
		background-color: #FFF0F0;
		color: #FF5A5F;
}
.load-more, .no-more {
		text-align: center;
		padding: 30rpx 0;
}
.load-more text, .no-more text {
		font-size: 24rpx;
		color: #999999;
}
