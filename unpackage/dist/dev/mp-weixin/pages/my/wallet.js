"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      walletInfo: {
        balance: 0,
        points: 0,
        coupons: 0,
        redPackets: 0
      },
      recordList: []
    };
  },
  onLoad() {
    this.loadWalletData();
    this.loadRecordData();
  },
  methods: {
    // 加载钱包数据
    loadWalletData() {
      const walletStorage = common_vendor.index.getStorageSync("wallet_info");
      if (walletStorage) {
        this.walletInfo = JSON.parse(walletStorage);
      } else {
        this.walletInfo = {
          balance: 520.88,
          points: 1280,
          coupons: 3,
          redPackets: 2
        };
        common_vendor.index.setStorageSync("wallet_info", JSON.stringify(this.walletInfo));
      }
    },
    // 加载交易记录数据
    loadRecordData() {
      const recordStorage = common_vendor.index.getStorageSync("transaction_records");
      if (recordStorage) {
        this.recordList = JSON.parse(recordStorage);
      } else {
        this.recordList = [
          {
            id: "tr_001",
            title: "充值",
            time: Date.now() - 36e5,
            amount: 200,
            type: "in",
            status: "成功"
          },
          {
            id: "tr_002",
            title: "购买电子秤",
            time: Date.now() - 864e5,
            amount: 258,
            type: "out",
            status: "成功"
          },
          {
            id: "tr_003",
            title: "推广收入",
            time: Date.now() - 1728e5,
            amount: 88.88,
            type: "in",
            status: "成功"
          }
        ];
        common_vendor.index.setStorageSync("transaction_records", JSON.stringify(this.recordList));
      }
    },
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hour = date.getHours().toString().padStart(2, "0");
      const minute = date.getMinutes().toString().padStart(2, "0");
      return `${year}-${month}-${day} ${hour}:${minute}`;
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 显示帮助信息
    showHelp() {
      common_vendor.index.showModal({
        title: "钱包使用说明",
        content: "钱包余额可用于在平台内消费。\n充值需满足最低单笔10元，提现需满足最低单笔100元。\n如有问题请联系客服。",
        showCancel: false
      });
    },
    // 前往充值页面
    goToRecharge() {
      common_vendor.index.showModal({
        title: "充值提示",
        content: "当前为测试环境，无法进行实际充值操作。",
        showCancel: false
      });
    },
    // 前往提现页面
    goToWithdraw() {
      if (this.walletInfo.balance < 100) {
        common_vendor.index.showModal({
          title: "提示",
          content: "账户余额不足100元，暂时无法提现",
          showCancel: false
        });
        return;
      }
      common_vendor.index.showModal({
        title: "提现提示",
        content: "当前为测试环境，无法进行实际提现操作。",
        showCancel: false
      });
    },
    // 前往积分页面
    goToPoint() {
      common_vendor.index.showToast({
        title: "积分功能开发中",
        icon: "none"
      });
    },
    // 前往优惠券页面
    goToCoupon() {
      common_vendor.index.showToast({
        title: "优惠券功能开发中",
        icon: "none"
      });
    },
    // 前往红包页面
    goToRedPacket() {
      common_vendor.index.showToast({
        title: "红包功能开发中",
        icon: "none"
      });
    },
    // 前往账单明细页面
    goToBill() {
      common_vendor.index.showToast({
        title: "账单明细功能开发中",
        icon: "none"
      });
    },
    // 前往银行卡页面
    goToBankCard() {
      common_vendor.index.showToast({
        title: "银行卡功能开发中",
        icon: "none"
      });
    },
    // 前往安全中心页面
    goToSecurity() {
      common_vendor.index.showToast({
        title: "安全中心功能开发中",
        icon: "none"
      });
    },
    // 前往客服页面
    goToService() {
      common_vendor.index.showToast({
        title: "正在连接客服...",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_assets._imports_1$5,
    d: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    e: common_vendor.t($data.walletInfo.balance.toFixed(2)),
    f: common_vendor.o((...args) => $options.goToRecharge && $options.goToRecharge(...args)),
    g: common_vendor.o((...args) => $options.goToWithdraw && $options.goToWithdraw(...args)),
    h: common_vendor.t($data.walletInfo.points),
    i: common_vendor.o((...args) => $options.goToPoint && $options.goToPoint(...args)),
    j: common_vendor.t($data.walletInfo.coupons),
    k: common_vendor.o((...args) => $options.goToCoupon && $options.goToCoupon(...args)),
    l: common_vendor.t($data.walletInfo.redPackets),
    m: common_vendor.o((...args) => $options.goToRedPacket && $options.goToRedPacket(...args)),
    n: common_assets._imports_2$4,
    o: common_vendor.o((...args) => $options.goToBill && $options.goToBill(...args)),
    p: common_assets._imports_3$5,
    q: common_vendor.o((...args) => $options.goToBankCard && $options.goToBankCard(...args)),
    r: common_assets._imports_4$4,
    s: common_vendor.o((...args) => $options.goToSecurity && $options.goToSecurity(...args)),
    t: common_assets._imports_5,
    v: common_vendor.o((...args) => $options.goToService && $options.goToService(...args)),
    w: common_assets._imports_6$2,
    x: common_vendor.o((...args) => $options.goToBill && $options.goToBill(...args)),
    y: $data.recordList.length === 0
  }, $data.recordList.length === 0 ? {
    z: common_assets._imports_7$2
  } : {
    A: common_vendor.f($data.recordList, (record, index, i0) => {
      return {
        a: common_vendor.t(record.title),
        b: common_vendor.t($options.formatTime(record.time)),
        c: common_vendor.t(record.type === "in" ? "+" : "-"),
        d: common_vendor.t(record.amount.toFixed(2)),
        e: common_vendor.n(record.type === "in" ? "income" : "expense"),
        f: common_vendor.t(record.status),
        g: record.id
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
