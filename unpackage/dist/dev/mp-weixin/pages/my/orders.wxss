
.orders-container {
		position: relative;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
}
	
	/* 固定头部区域样式 */
.fixed-header {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 100;
		background-color: #fff;
		/* 使用transform硬件加速提高性能 */
		transform: translateZ(0);
		will-change: transform;
}
.header {
		display: flex;
		align-items: center;
		height: 90rpx;
		padding: 0 30rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.right-btn {
		width: 60rpx;
}
	
	/* Tab栏样式 - 固定高度 */
.tab-section {
		display: flex;
		height: 90rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
}
.tab-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666666;
		position: relative;
}
.active-tab {
		color: #ff5a5f;
		font-weight: bold;
}
.active-tab::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 25%;
		width: 50%;
		height: 4rpx;
		background-color: #ff5a5f;
}
	
	/* 订单列表区域样式 */
.orders-list {
		flex: 1;
		margin-top: 180rpx; /* 与固定头部高度一致 */
		position: relative;
		/* 优化滚动性能 */
		-webkit-overflow-scrolling: touch;
}
.orders-content {
		padding-bottom: 30rpx;
}
.empty-tip {
		padding-top: 200rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
}
.empty-tip image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
}
.empty-tip text {
		font-size: 28rpx;
		color: #999999;
}
.order-item {
		margin: 20rpx 20rpx 0;
		background-color: #ffffff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		/* 使用transform创建GPU层，提高性能 */
		transform: translateZ(0);
}
.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
}
.store-info {
		display: flex;
		align-items: center;
}
.store-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
}
.store-name {
		font-size: 28rpx;
		color: #333333;
}
.order-status {
		font-size: 28rpx;
}
.status-pending {
		color: #ff5a5f;
}
.status-processing {
		color: #ff9500;
}
.status-delivered {
		color: #4cd964;
}
.status-completed {
		color: #999999;
}
.order-content {
		display: flex;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
}
.product-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
		background-color: #f5f5f5;
}
.order-info {
		flex: 1;
		display: flex;
		flex-direction: column;
}
.product-name {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 10rpx;
		line-height: 1.4;
}
.product-spec {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 20rpx;
}
.price-quantity {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.price {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff5a5f;
}
.quantity {
		font-size: 28rpx;
		color: #999999;
}
.order-footer {
		padding: 20rpx 30rpx;
}
.total-section {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		margin-bottom: 20rpx;
}
.total-section text {
		font-size: 26rpx;
		color: #666666;
		margin-left: 20rpx;
}
.total-price {
		font-size: 30rpx;
		font-weight: bold;
		color: #ff5a5f;
}
.action-section {
		display: flex;
		justify-content: flex-end;
}
.action-btn {
		min-width: 140rpx;
		height: 60rpx;
		border: 1rpx solid #dddddd;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 20rpx;
}
.action-btn text {
		font-size: 26rpx;
		color: #666666;
}
.primary-btn {
		background-color: #ff5a5f;
		border-color: #ff5a5f;
}
.primary-btn text {
		color: #ffffff;
}
.load-more, .no-more {
		text-align: center;
		padding: 20rpx 0;
}
.load-more text, .no-more text {
		font-size: 24rpx;
		color: #999999;
}
