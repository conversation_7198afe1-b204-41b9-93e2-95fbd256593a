"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      tabs: [
        { name: "全部", type: "all" },
        { name: "驿站转让", type: "transfer" },
        { name: "招聘信息", type: "job" },
        { name: "设备出售", type: "device" }
      ],
      currentTab: 0,
      allList: [],
      transferList: [],
      jobList: [],
      deviceList: [],
      allHasMore: false,
      transferHasMore: false,
      jobHasMore: false,
      deviceHasMore: false,
      userInfo: null
    };
  },
  onLoad() {
    const userInfoStorage = common_vendor.index.getStorageSync("userInfo");
    if (userInfoStorage) {
      this.userInfo = JSON.parse(userInfoStorage);
      this.loadFavorites();
    } else {
      common_vendor.index.showToast({
        title: "请先登录",
        icon: "none"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 加载收藏数据
    loadFavorites() {
      const favorites = common_vendor.index.getStorageSync("favorites_" + this.userInfo.userId) || [];
      this.allList = favorites;
      this.transferList = favorites.filter((item) => item.type === "transfer");
      this.jobList = favorites.filter((item) => item.type === "job");
      this.deviceList = favorites.filter((item) => item.type === "device");
      this.allHasMore = false;
      this.transferHasMore = false;
      this.jobHasMore = false;
      this.deviceHasMore = false;
    },
    // 切换选项卡
    switchTab(index) {
      this.currentTab = index;
    },
    // 滑动切换选项卡
    onSwiperChange(e) {
      this.currentTab = e.detail.current;
    },
    // 加载更多
    loadMore(type) {
      common_vendor.index.showToast({
        title: "没有更多数据了",
        icon: "none"
      });
    },
    // 查看详情
    viewDetail(item) {
      let url = "/pages/detail/detail?id=" + item.id + "&type=" + item.type;
      common_vendor.index.navigateTo({
        url
      });
    },
    // 取消收藏
    cancelFavorite(item) {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要取消收藏该内容吗？",
        success: (res) => {
          if (res.confirm) {
            let favorites = common_vendor.index.getStorageSync("favorites_" + this.userInfo.userId) || [];
            favorites = favorites.filter((favorite) => !(favorite.id === item.id && favorite.type === item.type));
            common_vendor.index.setStorageSync("favorites_" + this.userInfo.userId, favorites);
            this.loadFavorites();
            common_vendor.index.showToast({
              title: "已取消收藏",
              icon: "success"
            });
          }
        }
      });
    },
    // 页面导航
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_vendor.f($data.tabs, (tab, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(tab.name),
        b: $data.currentTab === index
      }, $data.currentTab === index ? {} : {}, {
        c: index,
        d: $data.currentTab === index ? 1 : "",
        e: common_vendor.o(($event) => $options.switchTab(index), index)
      });
    }),
    d: $data.allList.length === 0
  }, $data.allList.length === 0 ? {
    e: common_assets._imports_7$2,
    f: common_vendor.o(($event) => $options.navigateTo("/pages/index/index"))
  } : common_vendor.e({
    g: common_vendor.f($data.allList, (item, index, i0) => {
      return common_vendor.e({
        a: item.image,
        b: common_vendor.t(item.title),
        c: item.tags && item.tags.length > 0
      }, item.tags && item.tags.length > 0 ? {
        d: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        })
      } : {}, {
        e: item.address
      }, item.address ? {
        f: common_assets._imports_1$2,
        g: common_vendor.t(item.address)
      } : {}, {
        h: item.price
      }, item.price ? {
        i: common_vendor.t(item.price)
      } : {}, {
        j: common_vendor.o(($event) => $options.cancelFavorite(item), index),
        k: index,
        l: common_vendor.o(($event) => $options.viewDetail(item), index)
      });
    }),
    h: common_assets._imports_3$4,
    i: $data.allHasMore
  }, $data.allHasMore ? {} : {}), {
    j: common_vendor.o(($event) => $options.loadMore("all")),
    k: $data.transferList.length === 0
  }, $data.transferList.length === 0 ? {
    l: common_assets._imports_7$2,
    m: common_vendor.o(($event) => $options.navigateTo("/pages/transfer/transfer"))
  } : common_vendor.e({
    n: common_vendor.f($data.transferList, (item, index, i0) => {
      return common_vendor.e({
        a: item.image,
        b: common_vendor.t(item.title),
        c: item.tags && item.tags.length > 0
      }, item.tags && item.tags.length > 0 ? {
        d: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        })
      } : {}, {
        e: common_vendor.t(item.address),
        f: common_vendor.t(item.price),
        g: common_vendor.o(($event) => $options.cancelFavorite(item), index),
        h: index,
        i: common_vendor.o(($event) => $options.viewDetail(item), index)
      });
    }),
    o: common_assets._imports_1$2,
    p: common_assets._imports_3$4,
    q: $data.transferHasMore
  }, $data.transferHasMore ? {} : {}), {
    r: common_vendor.o(($event) => $options.loadMore("transfer")),
    s: $data.currentTab,
    t: common_vendor.o((...args) => $options.onSwiperChange && $options.onSwiperChange(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
