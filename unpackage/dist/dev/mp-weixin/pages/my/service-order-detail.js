"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      orderDetail: {}
    };
  },
  onLoad() {
    this.loadOrderDetail();
  },
  methods: {
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 加载订单详情
    loadOrderDetail() {
      const currentOrder = common_vendor.index.getStorageSync("currentOrder");
      if (currentOrder) {
        this.orderDetail = JSON.parse(currentOrder);
        if (typeof this.orderDetail.createTime === "string") {
          this.orderDetail.createTime = new Date(this.orderDetail.createTime);
        }
        if (this.orderDetail.acceptTime && typeof this.orderDetail.acceptTime === "string") {
          this.orderDetail.acceptTime = new Date(this.orderDetail.acceptTime);
        }
        if (this.orderDetail.completeTime && typeof this.orderDetail.completeTime === "string") {
          this.orderDetail.completeTime = new Date(this.orderDetail.completeTime);
        }
      } else {
        common_vendor.index.showToast({
          title: "订单详情加载失败",
          icon: "none"
        });
        setTimeout(() => {
          common_vendor.index.navigateBack();
        }, 1500);
      }
    },
    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case "pending":
          return "待接单";
        case "processing":
          return "进行中";
        case "completed":
          return "已完成";
        case "cancelled":
          return "已取消";
        default:
          return "未知状态";
      }
    },
    // 格式化日期
    formatDate(date) {
      if (!date)
        return "";
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, "0");
      const day = String(d.getDate()).padStart(2, "0");
      const hours = String(d.getHours()).padStart(2, "0");
      const minutes = String(d.getMinutes()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    // 复制电话号码
    copyPhone() {
      common_vendor.index.setClipboardData({
        data: this.orderDetail.stationPhone,
        success: () => {
          common_vendor.index.showToast({
            title: "电话号码已复制",
            icon: "success"
          });
        }
      });
    },
    // 联系驿站
    contactStation() {
      common_vendor.index.showActionSheet({
        itemList: ["拨打电话", "复制微信号"],
        success: (res) => {
          if (res.tapIndex === 0) {
            common_vendor.index.makePhoneCall({
              phoneNumber: this.orderDetail.stationPhone,
              fail: () => {
                common_vendor.index.showToast({
                  title: "拨号已取消",
                  icon: "none"
                });
              }
            });
          } else if (res.tapIndex === 1) {
            common_vendor.index.setClipboardData({
              data: this.orderDetail.stationPhone,
              success: () => {
                common_vendor.index.showToast({
                  title: "微信号已复制",
                  icon: "success"
                });
              }
            });
          }
        }
      });
    },
    // 显示取消订单弹窗
    showCancelModal() {
      common_vendor.index.showModal({
        title: "确认取消",
        content: "您确定要取消此订单吗？",
        confirmColor: "#ff5a5f",
        success: (res) => {
          if (res.confirm) {
            this.cancelOrder();
          }
        }
      });
    },
    // 取消订单
    cancelOrder() {
      const orders = common_vendor.index.getStorageSync("serviceOrders");
      if (orders) {
        let orderList = JSON.parse(orders);
        const index = orderList.findIndex((order) => order.orderId === this.orderDetail.orderId);
        if (index !== -1) {
          orderList[index].status = "cancelled";
          common_vendor.index.setStorageSync("serviceOrders", JSON.stringify(orderList));
          this.orderDetail.status = "cancelled";
          common_vendor.index.setStorageSync("currentOrder", JSON.stringify(this.orderDetail));
          common_vendor.index.showToast({
            title: "订单已取消",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        }
      }
    },
    // 确认接单
    confirmOrder() {
      if (this.orderDetail.status !== "pending") {
        common_vendor.index.showToast({
          title: "该订单不可接",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认接单",
        content: `您确定要接${this.orderDetail.stationName}的顶班订单吗？`,
        confirmColor: "#ff5a5f",
        success: (res) => {
          if (res.confirm) {
            const orders = common_vendor.index.getStorageSync("serviceOrders");
            if (orders) {
              let orderList = JSON.parse(orders);
              const index = orderList.findIndex((order) => order.orderId === this.orderDetail.orderId);
              if (index !== -1) {
                orderList[index].status = "processing";
                orderList[index].acceptTime = /* @__PURE__ */ new Date();
                common_vendor.index.setStorageSync("serviceOrders", JSON.stringify(orderList));
                this.orderDetail.status = "processing";
                this.orderDetail.acceptTime = /* @__PURE__ */ new Date();
                common_vendor.index.setStorageSync("currentOrder", JSON.stringify(this.orderDetail));
                common_vendor.index.showToast({
                  title: "接单成功",
                  icon: "success"
                });
              }
            }
          }
        }
      });
    },
    // 确认完成
    completeOrder() {
      if (this.orderDetail.status !== "processing") {
        common_vendor.index.showToast({
          title: "无法完成此订单",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认完成",
        content: "您确定已经完成此订单的工作内容吗？",
        confirmColor: "#ff5a5f",
        success: (res) => {
          if (res.confirm) {
            const orders = common_vendor.index.getStorageSync("serviceOrders");
            if (orders) {
              let orderList = JSON.parse(orders);
              const index = orderList.findIndex((order) => order.orderId === this.orderDetail.orderId);
              if (index !== -1) {
                orderList[index].status = "completed";
                orderList[index].completeTime = /* @__PURE__ */ new Date();
                common_vendor.index.setStorageSync("serviceOrders", JSON.stringify(orderList));
                this.orderDetail.status = "completed";
                this.orderDetail.completeTime = /* @__PURE__ */ new Date();
                common_vendor.index.setStorageSync("currentOrder", JSON.stringify(this.orderDetail));
                common_vendor.index.showToast({
                  title: "工作已完成",
                  icon: "success"
                });
              }
            }
          }
        }
      });
    },
    // 导航到顶班接单列表
    navigateToList() {
      common_vendor.index.navigateBack({
        delta: 1
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_vendor.t($options.getStatusText($data.orderDetail.status)),
    d: common_vendor.n($data.orderDetail.status),
    e: $data.orderDetail.status === "pending"
  }, $data.orderDetail.status === "pending" ? {} : $data.orderDetail.status === "processing" ? {} : $data.orderDetail.status === "completed" ? {} : {}, {
    f: $data.orderDetail.status === "processing",
    g: $data.orderDetail.status === "completed",
    h: $data.orderDetail.stationImage,
    i: common_vendor.t($data.orderDetail.stationName),
    j: common_vendor.t($data.orderDetail.stationAddress),
    k: common_vendor.t($data.orderDetail.workTime),
    l: common_vendor.t($data.orderDetail.hourlyRate),
    m: common_vendor.t($data.orderDetail.duration),
    n: common_vendor.t($data.orderDetail.totalAmount),
    o: common_vendor.t($data.orderDetail.workContent || "暂无工作内容描述"),
    p: common_vendor.t($data.orderDetail.requirements || "暂无特殊要求"),
    q: common_vendor.t($data.orderDetail.orderId),
    r: common_vendor.t($options.formatDate($data.orderDetail.createTime)),
    s: $data.orderDetail.acceptTime
  }, $data.orderDetail.acceptTime ? {
    t: common_vendor.t($options.formatDate($data.orderDetail.acceptTime))
  } : {}, {
    v: $data.orderDetail.completeTime
  }, $data.orderDetail.completeTime ? {
    w: common_vendor.t($options.formatDate($data.orderDetail.completeTime))
  } : {}, {
    x: common_vendor.t($data.orderDetail.stationContact),
    y: common_vendor.t($data.orderDetail.stationPhone),
    z: common_vendor.o((...args) => $options.copyPhone && $options.copyPhone(...args)),
    A: $data.orderDetail.status === "pending"
  }, $data.orderDetail.status === "pending" ? {
    B: common_vendor.o((...args) => $options.showCancelModal && $options.showCancelModal(...args)),
    C: common_vendor.o((...args) => $options.contactStation && $options.contactStation(...args)),
    D: common_vendor.o((...args) => $options.confirmOrder && $options.confirmOrder(...args))
  } : $data.orderDetail.status === "processing" ? {
    F: common_vendor.o((...args) => $options.showCancelModal && $options.showCancelModal(...args)),
    G: common_vendor.o((...args) => $options.contactStation && $options.contactStation(...args)),
    H: common_vendor.o((...args) => $options.completeOrder && $options.completeOrder(...args))
  } : $data.orderDetail.status === "completed" ? {
    J: common_vendor.o((...args) => $options.navigateToList && $options.navigateToList(...args))
  } : {}, {
    E: $data.orderDetail.status === "processing",
    I: $data.orderDetail.status === "completed"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
