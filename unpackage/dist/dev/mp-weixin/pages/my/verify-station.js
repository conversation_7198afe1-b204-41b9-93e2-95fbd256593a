"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      verificationStatus: "",
      // 空字符串表示未认证，'processing'表示审核中，'approved'表示已认证，'rejected'表示被拒绝
      statusIcon: {
        "processing": "/static/icons/processing.png",
        "approved": "/static/icons/success.png",
        "rejected": "/static/icons/error.png"
      },
      statusText: {
        "processing": "审核中",
        "approved": "认证成功",
        "rejected": "认证失败"
      },
      statusDesc: {
        "processing": "您的驿站认证信息正在审核中，预计1-3个工作日内完成审核，请耐心等待。",
        "approved": "您已通过驿站认证，可以使用平台的全部功能。",
        "rejected": "很抱歉，您的驿站认证未通过审核，请检查认证信息或联系客服。"
      },
      formData: {
        stationName: "",
        stationCode: "",
        stationType: "",
        brand: "",
        address: "",
        contactName: "",
        contactPhone: "",
        businessLicense: "",
        authorization: "",
        storefront: "",
        interior: "",
        agreement: false
      },
      stationTypes: ["社区驿站", "校园驿站", "商超驿站", "便利店驿站", "其他"],
      brands: ["菜鸟驿站", "京东快递", "顺丰驿站", "中通快递", "韵达快递", "圆通快递", "百世快递", "其他"],
      verificationReason: ""
      // 拒绝原因
    };
  },
  computed: {
    // 判断表单是否有效
    isFormValid() {
      return this.formData.stationName && this.formData.stationCode && this.formData.stationType && this.formData.brand && this.formData.address && this.formData.contactName && this.formData.contactPhone && this.formData.businessLicense && this.formData.storefront && this.formData.interior && this.formData.agreement;
    },
    // 判断表单是否被禁用
    isDisabled() {
      return this.verificationStatus === "processing";
    }
  },
  onLoad() {
    this.checkVerificationStatus();
  },
  methods: {
    // 检查认证状态
    checkVerificationStatus() {
      const verificationStorage = common_vendor.index.getStorageSync("station_verification");
      if (verificationStorage) {
        const verification = JSON.parse(verificationStorage);
        this.verificationStatus = verification.status;
        this.formData = verification.formData || this.formData;
        this.verificationReason = verification.reason || "";
      }
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 显示帮助信息
    showHelp() {
      common_vendor.index.showModal({
        title: "关于驿站认证",
        content: "驿站认证是为了验证您作为驿站经营者的真实身份和资质。通过认证后，您可以在平台上发布驿站转让、招聘、设备购买等信息，并享受更多平台提供的专属权益和服务。",
        showCancel: false
      });
    },
    // 驿站类型变化
    onStationTypeChange(e) {
      const index = e.detail.value;
      this.formData.stationType = this.stationTypes[index];
    },
    // 品牌变化
    onBrandChange(e) {
      const index = e.detail.value;
      this.formData.brand = this.brands[index];
    },
    // 使用当前位置
    useCurrentLocation() {
      if (this.isDisabled) {
        return;
      }
      common_vendor.index.showLoading({
        title: "获取位置中..."
      });
      common_vendor.index.getLocation({
        type: "gcj02",
        success: (res) => {
          this.reverseGeocoding(res.latitude, res.longitude);
        },
        fail: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showModal({
            title: "提示",
            content: "获取位置失败，请检查定位权限或手动输入地址",
            showCancel: false
          });
        }
      });
    },
    // 逆地址解析
    reverseGeocoding(latitude, longitude) {
      setTimeout(() => {
        common_vendor.index.hideLoading();
        this.formData.address = "浙江省杭州市西湖区文三路478号华星时代广场";
        common_vendor.index.showToast({
          title: "已获取当前位置",
          icon: "success"
        });
      }, 1500);
    },
    // 上传图片
    uploadImage(type) {
      if (this.isDisabled) {
        return;
      }
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.showLoading({
            title: "上传中..."
          });
          setTimeout(() => {
            common_vendor.index.hideLoading();
            switch (type) {
              case "license":
                this.formData.businessLicense = tempFilePath;
                break;
              case "authorization":
                this.formData.authorization = tempFilePath;
                break;
              case "storefront":
                this.formData.storefront = tempFilePath;
                break;
              case "interior":
                this.formData.interior = tempFilePath;
                break;
            }
          }, 1500);
        }
      });
    },
    // 删除图片
    deleteImage(type) {
      switch (type) {
        case "license":
          this.formData.businessLicense = "";
          break;
        case "authorization":
          this.formData.authorization = "";
          break;
        case "storefront":
          this.formData.storefront = "";
          break;
        case "interior":
          this.formData.interior = "";
          break;
      }
    },
    // 切换协议同意状态
    toggleAgreement() {
      if (!this.isDisabled) {
        this.formData.agreement = !this.formData.agreement;
      }
    },
    // 查看服务条款
    viewServiceTerms() {
      common_vendor.index.navigateTo({
        url: "/pages/my/agreement?type=station"
      });
    },
    // 提交认证
    submitVerification() {
      if (!this.isFormValid || this.isDisabled) {
        return;
      }
      if (!/^1\d{10}$/.test(this.formData.contactPhone)) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        this.verificationStatus = "processing";
        const verification = {
          status: this.verificationStatus,
          formData: this.formData,
          submitTime: (/* @__PURE__ */ new Date()).getTime()
        };
        common_vendor.index.setStorageSync("station_verification", JSON.stringify(verification));
        common_vendor.index.showToast({
          title: "提交成功",
          icon: "success"
        });
        setTimeout(() => {
          const result = Math.random() > 0.3 ? "approved" : "rejected";
          this.verificationStatus = result;
          verification.status = result;
          if (result === "rejected") {
            verification.reason = "营业执照信息与提交的驿站信息不符，请核对后重新提交";
            this.verificationReason = verification.reason;
          }
          common_vendor.index.setStorageSync("station_verification", JSON.stringify(verification));
          if (result === "approved") {
            const userInfoStorage = common_vendor.index.getStorageSync("userInfo");
            if (userInfoStorage) {
              const userInfo = JSON.parse(userInfoStorage);
              userInfo.isStationVerified = true;
              userInfo.stationInfo = {
                name: this.formData.stationName,
                code: this.formData.stationCode,
                type: this.formData.stationType,
                brand: this.formData.brand
              };
              common_vendor.index.setStorageSync("userInfo", JSON.stringify(userInfo));
            }
          }
        }, 5e3);
      }, 1500);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_assets._imports_1$5,
    d: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    e: $data.verificationStatus
  }, $data.verificationStatus ? {
    f: $data.statusIcon[$data.verificationStatus],
    g: common_vendor.t($data.statusText[$data.verificationStatus]),
    h: common_vendor.t($data.statusDesc[$data.verificationStatus])
  } : {}, {
    i: $data.verificationStatus !== "approved" && $data.verificationStatus !== "processing"
  }, $data.verificationStatus !== "approved" && $data.verificationStatus !== "processing" ? common_vendor.e({
    j: $options.isDisabled,
    k: $data.formData.stationName,
    l: common_vendor.o(($event) => $data.formData.stationName = $event.detail.value),
    m: $options.isDisabled,
    n: $data.formData.stationCode,
    o: common_vendor.o(($event) => $data.formData.stationCode = $event.detail.value),
    p: common_vendor.t($data.formData.stationType || "请选择驿站类型"),
    q: common_assets._imports_2$7,
    r: $data.stationTypes,
    s: common_vendor.o((...args) => $options.onStationTypeChange && $options.onStationTypeChange(...args)),
    t: $options.isDisabled,
    v: common_vendor.t($data.formData.brand || "请选择所属品牌"),
    w: common_assets._imports_2$7,
    x: $data.brands,
    y: common_vendor.o((...args) => $options.onBrandChange && $options.onBrandChange(...args)),
    z: $options.isDisabled,
    A: $options.isDisabled,
    B: $data.formData.address,
    C: common_vendor.o(($event) => $data.formData.address = $event.detail.value),
    D: !$options.isDisabled
  }, !$options.isDisabled ? {
    E: common_vendor.o((...args) => $options.useCurrentLocation && $options.useCurrentLocation(...args))
  } : {}, {
    F: $options.isDisabled,
    G: $data.formData.contactName,
    H: common_vendor.o(($event) => $data.formData.contactName = $event.detail.value),
    I: $options.isDisabled,
    J: $data.formData.contactPhone,
    K: common_vendor.o(($event) => $data.formData.contactPhone = $event.detail.value),
    L: !$data.formData.businessLicense || $options.isDisabled
  }, !$data.formData.businessLicense || $options.isDisabled ? common_vendor.e({
    M: $data.formData.businessLicense
  }, $data.formData.businessLicense ? {
    N: $data.formData.businessLicense
  } : {
    O: common_assets._imports_3$7
  }, {
    P: common_vendor.o(($event) => $options.uploadImage("license"))
  }) : {
    Q: $data.formData.businessLicense,
    R: common_assets._imports_2$1,
    S: common_vendor.o(($event) => $options.deleteImage("license"))
  }, {
    T: !$data.formData.authorization || $options.isDisabled
  }, !$data.formData.authorization || $options.isDisabled ? common_vendor.e({
    U: $data.formData.authorization
  }, $data.formData.authorization ? {
    V: $data.formData.authorization
  } : {
    W: common_assets._imports_3$7
  }, {
    X: common_vendor.o(($event) => $options.uploadImage("authorization"))
  }) : {
    Y: $data.formData.authorization,
    Z: common_assets._imports_2$1,
    aa: common_vendor.o(($event) => $options.deleteImage("authorization"))
  }, {
    ab: !$data.formData.storefront || $options.isDisabled
  }, !$data.formData.storefront || $options.isDisabled ? common_vendor.e({
    ac: $data.formData.storefront
  }, $data.formData.storefront ? {
    ad: $data.formData.storefront
  } : {
    ae: common_assets._imports_3$7
  }, {
    af: common_vendor.o(($event) => $options.uploadImage("storefront"))
  }) : {
    ag: $data.formData.storefront,
    ah: common_assets._imports_2$1,
    ai: common_vendor.o(($event) => $options.deleteImage("storefront"))
  }, {
    aj: !$data.formData.interior || $options.isDisabled
  }, !$data.formData.interior || $options.isDisabled ? common_vendor.e({
    ak: $data.formData.interior
  }, $data.formData.interior ? {
    al: $data.formData.interior
  } : {
    am: common_assets._imports_3$7
  }, {
    an: common_vendor.o(($event) => $options.uploadImage("interior"))
  }) : {
    ao: $data.formData.interior,
    ap: common_assets._imports_2$1,
    aq: common_vendor.o(($event) => $options.deleteImage("interior"))
  }, {
    ar: $data.formData.agreement
  }, $data.formData.agreement ? {} : {}, {
    as: common_vendor.o((...args) => $options.toggleAgreement && $options.toggleAgreement(...args)),
    at: common_vendor.o((...args) => $options.viewServiceTerms && $options.viewServiceTerms(...args)),
    av: common_vendor.t($data.verificationStatus === "rejected" ? "重新提交" : "提交认证"),
    aw: !$options.isFormValid || $options.isDisabled ? 1 : "",
    ax: common_vendor.o((...args) => $options.submitVerification && $options.submitVerification(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
