"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      tabs: [
        { name: "驿站转让", type: "transfer" },
        { name: "顶班服务", type: "service" },
        { name: "招聘信息", type: "job" },
        { name: "设备出售", type: "device" }
      ],
      currentTab: 0,
      transferList: [],
      serviceList: [],
      jobList: [],
      deviceList: [],
      transferHasMore: false,
      serviceHasMore: false,
      jobHasMore: false,
      deviceHasMore: false,
      userInfo: null
    };
  },
  onLoad() {
    const userInfoStorage = common_vendor.index.getStorageSync("userInfo");
    if (userInfoStorage) {
      this.userInfo = JSON.parse(userInfoStorage);
      this.loadTransferList();
      this.loadServiceList();
      this.loadJobList();
      this.loadDeviceList();
    } else {
      common_vendor.index.showToast({
        title: "请先登录",
        icon: "none"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 获取驿站转让列表
    loadTransferList() {
      const list = common_vendor.index.getStorageSync("transferList") || [];
      this.transferList = list.filter((item) => {
        return item.contactInfo && item.contactInfo.phone === this.userInfo.phoneNumber;
      });
      this.transferHasMore = false;
    },
    // 获取顶班服务列表
    loadServiceList() {
      const list = common_vendor.index.getStorageSync("serviceList") || [];
      this.serviceList = list.filter((item) => {
        return item.contactInfo && item.contactInfo.phone === this.userInfo.phoneNumber;
      });
      this.serviceHasMore = false;
    },
    // 获取招聘信息列表
    loadJobList() {
      const list = common_vendor.index.getStorageSync("jobList") || [];
      this.jobList = list.filter((item) => {
        return item.contactInfo && item.contactInfo.phone === this.userInfo.phoneNumber;
      });
      this.jobHasMore = false;
    },
    // 获取设备出售列表
    loadDeviceList() {
      const list = common_vendor.index.getStorageSync("deviceList") || [];
      this.deviceList = list.filter((item) => {
        return item.contactInfo && item.contactInfo.phone === this.userInfo.phoneNumber;
      });
      this.deviceHasMore = false;
    },
    // 切换选项卡
    switchTab(index) {
      this.currentTab = index;
    },
    // 滑动切换选项卡
    onSwiperChange(e) {
      this.currentTab = e.detail.current;
    },
    // 加载更多
    loadMore(type) {
      common_vendor.index.showToast({
        title: "没有更多数据了",
        icon: "none"
      });
    },
    // 查看详情
    viewDetail(item, type) {
      let url = "/pages/detail/detail?id=" + item.id + "&type=" + type;
      common_vendor.index.navigateTo({
        url
      });
    },
    // 编辑帖子
    editPost(item, type) {
      let url = "";
      switch (type) {
        case "transfer":
          url = "/pages/publish/station?id=" + item.id;
          break;
        case "service":
          url = "/pages/publish/service?id=" + item.id;
          break;
        case "job":
          url = "/pages/publish/job?id=" + item.id;
          break;
        case "device":
          url = "/pages/publish/device?id=" + item.id;
          break;
      }
      if (url) {
        common_vendor.index.navigateTo({
          url
        });
      }
    },
    // 删除帖子
    deletePost(item, type) {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除该发布吗？",
        success: (res) => {
          if (res.confirm) {
            let storageKey = type + "List";
            let list = common_vendor.index.getStorageSync(storageKey) || [];
            list = list.filter((post) => post.id !== item.id);
            common_vendor.index.setStorageSync(storageKey, list);
            switch (type) {
              case "transfer":
                this.transferList = this.transferList.filter((post) => post.id !== item.id);
                break;
              case "service":
                this.serviceList = this.serviceList.filter((post) => post.id !== item.id);
                break;
              case "job":
                this.jobList = this.jobList.filter((post) => post.id !== item.id);
                break;
              case "device":
                this.deviceList = this.deviceList.filter((post) => post.id !== item.id);
                break;
            }
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    },
    // 页面导航
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_vendor.f($data.tabs, (tab, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(tab.name),
        b: $data.currentTab === index
      }, $data.currentTab === index ? {} : {}, {
        c: index,
        d: $data.currentTab === index ? 1 : "",
        e: common_vendor.o(($event) => $options.switchTab(index), index)
      });
    }),
    d: $data.transferList.length === 0
  }, $data.transferList.length === 0 ? {
    e: common_assets._imports_7$2,
    f: common_vendor.o(($event) => $options.navigateTo("/pages/publish/station"))
  } : common_vendor.e({
    g: common_vendor.f($data.transferList, (item, index, i0) => {
      return common_vendor.e({
        a: item.images[0],
        b: common_vendor.t(item.title),
        c: item.stationType
      }, item.stationType ? {
        d: common_vendor.t(item.stationType)
      } : {}, {
        e: common_vendor.t(item.address),
        f: common_vendor.t(item.price.replace("万元", "")),
        g: common_vendor.o(($event) => $options.editPost(item, "transfer"), index),
        h: common_vendor.o(($event) => $options.deletePost(item, "transfer"), index),
        i: index,
        j: common_vendor.o(($event) => $options.viewDetail(item, "transfer"), index)
      });
    }),
    h: common_assets._imports_1$2,
    i: common_assets._imports_1$4,
    j: common_assets._imports_2$2,
    k: $data.transferHasMore
  }, $data.transferHasMore ? {} : {}), {
    l: common_vendor.o(($event) => $options.loadMore("transfer")),
    m: $data.serviceList.length === 0
  }, $data.serviceList.length === 0 ? {
    n: common_assets._imports_7$2,
    o: common_vendor.o(($event) => $options.navigateTo("/pages/publish/service"))
  } : common_vendor.e({
    p: common_vendor.f($data.serviceList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.time),
        c: common_vendor.t(item.address),
        d: common_vendor.t(item.price.replace("元/天", "")),
        e: common_vendor.o(($event) => $options.editPost(item, "service"), index),
        f: common_vendor.o(($event) => $options.deletePost(item, "service"), index),
        g: index,
        h: common_vendor.o(($event) => $options.viewDetail(item, "service"), index)
      };
    }),
    q: common_assets._imports_5$1,
    r: common_assets._imports_1$2,
    s: common_assets._imports_1$4,
    t: common_assets._imports_2$2,
    v: $data.serviceHasMore
  }, $data.serviceHasMore ? {} : {}), {
    w: common_vendor.o(($event) => $options.loadMore("service")),
    x: $data.jobList.length === 0
  }, $data.jobList.length === 0 ? {
    y: common_assets._imports_7$2,
    z: common_vendor.o(($event) => $options.navigateTo("/pages/publish/job"))
  } : common_vendor.e({
    A: common_vendor.f($data.jobList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.salary),
        c: common_vendor.t(item.company),
        d: common_vendor.t(item.address),
        e: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        f: common_vendor.o(($event) => $options.editPost(item, "job"), index),
        g: common_vendor.o(($event) => $options.deletePost(item, "job"), index),
        h: index,
        i: common_vendor.o(($event) => $options.viewDetail(item, "job"), index)
      };
    }),
    B: common_assets._imports_1$2,
    C: common_assets._imports_1$4,
    D: common_assets._imports_2$2,
    E: $data.jobHasMore
  }, $data.jobHasMore ? {} : {}), {
    F: common_vendor.o(($event) => $options.loadMore("job")),
    G: $data.deviceList.length === 0
  }, $data.deviceList.length === 0 ? {
    H: common_assets._imports_7$2,
    I: common_vendor.o(($event) => $options.navigateTo("/pages/publish/device"))
  } : common_vendor.e({
    J: common_vendor.f($data.deviceList, (item, index, i0) => {
      return {
        a: item.images[0],
        b: common_vendor.t(item.title),
        c: common_vendor.t(item.condition),
        d: common_vendor.t(item.address),
        e: common_vendor.t(item.price.replace("元", "")),
        f: common_vendor.o(($event) => $options.editPost(item, "device"), index),
        g: common_vendor.o(($event) => $options.deletePost(item, "device"), index),
        h: index,
        i: common_vendor.o(($event) => $options.viewDetail(item, "device"), index)
      };
    }),
    K: common_assets._imports_1$2,
    L: common_assets._imports_1$4,
    M: common_assets._imports_2$2,
    N: $data.deviceHasMore
  }, $data.deviceHasMore ? {} : {}), {
    O: common_vendor.o(($event) => $options.loadMore("device")),
    P: $data.currentTab,
    Q: common_vendor.o((...args) => $options.onSwiperChange && $options.onSwiperChange(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
