
.address-list-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
}
.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.address-list {
		padding: 20rpx;
}
.address-item {
		background-color: #ffffff;
		border-radius: 8rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
}
.address-info {
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
}
.user-info {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
}
.name {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		margin-right: 15rpx;
}
.phone {
		font-size: 26rpx;
		color: #666666;
		margin-right: 15rpx;
}
.default-tag {
		font-size: 22rpx;
		color: #ffffff;
		background-color: #ff5a5f;
		padding: 4rpx 10rpx;
		border-radius: 4rpx;
}
.address-text {
		font-size: 26rpx;
		color: #333333;
		line-height: 1.4;
}
.address-actions {
		display: flex;
		border-top: 1rpx solid #f5f5f5;
}
.action-btn {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx 0;
}
.action-btn image {
		width: 30rpx;
		height: 30rpx;
		margin-right: 10rpx;
}
.action-btn text {
		font-size: 26rpx;
		color: #666666;
}
.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
}
.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
}
.empty-text {
		font-size: 28rpx;
		color: #999999;
}
.add-address-btn {
		position: fixed;
		bottom: 30rpx;
		left: 30rpx;
		right: 30rpx;
		height: 80rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.add-address-btn text {
		font-size: 30rpx;
		color: #ffffff;
		font-weight: bold;
}
