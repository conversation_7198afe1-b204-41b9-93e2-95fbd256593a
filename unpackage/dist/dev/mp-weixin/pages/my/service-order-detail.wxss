
.order-detail-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
}
.header {
		display: flex;
		align-items: center;
		height: 90rpx;
		padding: 0 30rpx;
		background-color: #ffffff;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.status-card {
		margin: 20rpx;
		border-radius: 12rpx;
		overflow: hidden;
		background-color: #ffffff;
}
.status-header {
		padding: 30rpx;
		background-color: #ff9500;
}
.status-header.pending {
		background-color: #ff9500;
}
.status-header.processing {
		background-color: #007aff;
}
.status-header.completed {
		background-color: #34c759;
}
.status-header.cancelled {
		background-color: #8e8e93;
}
.status-text {
		font-size: 36rpx;
		font-weight: bold;
		color: #ffffff;
}
.status-info {
		padding: 20rpx 30rpx;
		background-color: rgba(255, 255, 255, 0.8);
}
.info-text {
		font-size: 28rpx;
		color: #666666;
}
.info-card {
		margin: 20rpx;
		padding: 30rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
}
.station-header {
		display: flex;
		margin-bottom: 30rpx;
}
.station-image {
		width: 120rpx;
		height: 120rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
		background-color: #f0f0f0;
}
.station-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
}
.station-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
}
.station-address {
		font-size: 28rpx;
		color: #666666;
}
.divider {
		height: 1rpx;
		background-color: #f0f0f0;
		margin: 20rpx 0;
}
.work-info {
		padding-top: 10rpx;
}
.info-row {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
}
.info-label {
		width: 160rpx;
		font-size: 28rpx;
		color: #999999;
}
.info-value {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
}
.info-value.highlight {
		color: #ff5a5f;
		font-weight: bold;
		font-size: 32rpx;
}
.card-title {
		font-size: a2rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
		position: relative;
		padding-left: 20rpx;
}
.card-title::before {
		content: '';
		position: absolute;
		left: 0;
		top: 8rpx;
		width: 6rpx;
		height: 28rpx;
		background-color: #ff5a5f;
		border-radius: 3rpx;
}
.content-text {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.6;
}
.copy-btn {
		width: 80rpx;
		height: 50rpx;
		line-height: 50rpx;
		text-align: center;
		font-size: 24rpx;
		color: #007aff;
		border: 1rpx solid #007aff;
		border-radius: 25rpx;
		margin-left: 20rpx;
}
.bottom-btns {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		height: 100rpx;
		background-color: #ffffff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.btn {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		font-size: 32rpx;
}
.cancel-btn {
		color: #666666;
		background-color: #ffffff;
}
.contact-btn {
		color: #ff9500;
		background-color: #ffffff;
}
.confirm-btn {
		color: #ffffff;
		background-color: #ff5a5f;
}
.wide-btn {
		color: #ffffff;
		background-color: #ff5a5f;
}
