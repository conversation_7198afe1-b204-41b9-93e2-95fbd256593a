"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      cacheSize: "12.5MB",
      settings: {
        systemNotification: true,
        marketingNotification: false,
        messageSound: true,
        allowProfileView: true,
        allowPushMessage: true
      },
      defaultAddress: null
    };
  },
  onLoad() {
    this.loadSettings();
    this.loadDefaultAddress();
  },
  methods: {
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 加载设置
    loadSettings() {
      const storedSettings = common_vendor.index.getStorageSync("userSettings");
      if (storedSettings) {
        this.settings = JSON.parse(storedSettings);
      } else {
        common_vendor.index.setStorageSync("userSettings", JSON.stringify(this.settings));
      }
    },
    // 加载默认地址
    loadDefaultAddress() {
      const addressStorage = common_vendor.index.getStorageSync("address_list");
      if (addressStorage) {
        const addresses = JSON.parse(addressStorage);
        this.defaultAddress = addresses.find((addr) => addr.isDefault) || (addresses.length > 0 ? addresses[0] : null);
      }
    },
    // 格式化手机号
    formatPhone(phone) {
      if (!phone)
        return "";
      return phone.substr(0, 3) + "****" + phone.substr(7);
    },
    // 管理收货地址
    manageAddress() {
      common_vendor.index.navigateTo({
        url: "/pages/my/address-list"
      });
    },
    // 添加收货地址
    addAddress() {
      common_vendor.index.navigateTo({
        url: "/pages/my/address-edit"
      });
    },
    // 开关变化
    onSwitchChange(key, event) {
      this.settings[key] = event.detail.value;
      common_vendor.index.setStorageSync("userSettings", JSON.stringify(this.settings));
      common_vendor.index.showToast({
        title: "设置已保存",
        icon: "success",
        duration: 1500
      });
    },
    // 修改密码
    changePassword() {
      common_vendor.index.showToast({
        title: "功能开发中",
        icon: "none"
      });
    },
    // 修改手机号
    changePhone() {
      common_vendor.index.showToast({
        title: "功能开发中",
        icon: "none"
      });
    },
    // 清除缓存
    clearCache() {
      common_vendor.index.showModal({
        title: "清除缓存",
        content: "确定要清除缓存吗？",
        success: (res) => {
          if (res.confirm) {
            setTimeout(() => {
              this.cacheSize = "0MB";
              common_vendor.index.showToast({
                title: "缓存已清除",
                icon: "success"
              });
            }, 500);
          }
        }
      });
    },
    // 用户协议
    goToAgreement() {
      common_vendor.index.navigateTo({
        url: "/pages/my/agreement"
      });
    },
    // 隐私政策
    goToPrivacy() {
      common_vendor.index.navigateTo({
        url: "/pages/my/privacy"
      });
    },
    // 关于我们
    showAbout() {
      common_vendor.index.showModal({
        title: "关于驿站帮Pro",
        content: "驿站帮Pro v1.0.0\n专注于驿站资源、设备交易及人才服务的综合平台",
        showCancel: false
      });
    },
    // 退出登录
    logout() {
      common_vendor.index.showModal({
        title: "退出登录",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            const userInfo = common_vendor.index.getStorageSync("userInfo");
            if (userInfo) {
              const parsedInfo = JSON.parse(userInfo);
              parsedInfo.isLoggedIn = false;
              common_vendor.index.setStorageSync("userInfo", JSON.stringify(parsedInfo));
            }
            common_vendor.index.navigateTo({
              url: "/pages/my/login"
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_assets._imports_1$9,
    d: common_vendor.o((...args) => $options.changePassword && $options.changePassword(...args)),
    e: common_assets._imports_1$9,
    f: common_vendor.o((...args) => $options.changePhone && $options.changePhone(...args)),
    g: common_assets._imports_1$9,
    h: common_vendor.o((...args) => $options.manageAddress && $options.manageAddress(...args)),
    i: $data.defaultAddress
  }, $data.defaultAddress ? common_vendor.e({
    j: common_vendor.t($data.defaultAddress.name),
    k: common_vendor.t($options.formatPhone($data.defaultAddress.phone)),
    l: $data.defaultAddress.isDefault
  }, $data.defaultAddress.isDefault ? {} : {}, {
    m: common_vendor.t($data.defaultAddress.province),
    n: common_vendor.t($data.defaultAddress.city),
    o: common_vendor.t($data.defaultAddress.district),
    p: common_vendor.t($data.defaultAddress.address)
  }) : {
    q: common_vendor.o((...args) => $options.addAddress && $options.addAddress(...args))
  }, {
    r: $data.settings.systemNotification,
    s: common_vendor.o(($event) => $options.onSwitchChange("systemNotification", $event)),
    t: $data.settings.marketingNotification,
    v: common_vendor.o(($event) => $options.onSwitchChange("marketingNotification", $event)),
    w: $data.settings.messageSound,
    x: common_vendor.o(($event) => $options.onSwitchChange("messageSound", $event)),
    y: $data.settings.allowProfileView,
    z: common_vendor.o(($event) => $options.onSwitchChange("allowProfileView", $event)),
    A: $data.settings.allowPushMessage,
    B: common_vendor.o(($event) => $options.onSwitchChange("allowPushMessage", $event)),
    C: common_vendor.t($data.cacheSize),
    D: common_vendor.o((...args) => $options.clearCache && $options.clearCache(...args)),
    E: common_assets._imports_1$9,
    F: common_vendor.o((...args) => $options.goToAgreement && $options.goToAgreement(...args)),
    G: common_assets._imports_1$9,
    H: common_vendor.o((...args) => $options.goToPrivacy && $options.goToPrivacy(...args)),
    I: common_assets._imports_1$9,
    J: common_vendor.o((...args) => $options.showAbout && $options.showAbout(...args)),
    K: common_vendor.o((...args) => $options.logout && $options.logout(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
