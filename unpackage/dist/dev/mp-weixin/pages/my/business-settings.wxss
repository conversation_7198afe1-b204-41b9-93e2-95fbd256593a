
.business-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
}
.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
		flex: 1;
		text-align: center;
		margin-right: 60rpx;
}
.verify-notice {
		display: flex;
		align-items: center;
		padding: 30rpx;
		background-color: #fff8f8;
		margin: 20rpx;
		border-radius: 8rpx;
}
.notice-content {
		flex: 1;
}
.notice-title {
		font-size: 28rpx;
		color: #ff5a5f;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
}
.notice-desc {
		font-size: 24rpx;
		color: #999999;
		display: block;
}
.notice-btn {
		padding: 10rpx 30rpx;
		background-color: #ff5a5f;
		color: #ffffff;
		font-size: 28rpx;
		border-radius: 30rpx;
}
.settings-section {
		padding: 0 20rpx;
}
.settings-group {
		background-color: #ffffff;
		border-radius: 8rpx;
		padding: 20rpx 30rpx;
		margin-bottom: 20rpx;
}
.group-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 30rpx;
		position: relative;
		padding-left: 20rpx;
}
.group-title:before {
		content: '';
		position: absolute;
		left: 0;
		top: 6rpx;
		width: 6rpx;
		height: 32rpx;
		background-color: #ff5a5f;
		border-radius: 3rpx;
}
.settings-item {
		margin-bottom: 30rpx;
}
.item-label {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 15rpx;
		display: block;
}
.item-input {
		width: 100%;
		height: 90rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333333;
		box-sizing: border-box;
}
.price-input {
		text-align: right;
}
.switch-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
}
.switch-item:last-child {
		border-bottom: none;
}
.switch-label {
		font-size: 28rpx;
		color: #333333;
}
.time-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
}
.time-label {
		font-size: 28rpx;
		color: #333333;
}
.time-picker-group {
		display: flex;
		align-items: center;
}
.time-picker {
		background-color: #f9f9f9;
		padding: 10rpx 20rpx;
		border-radius: 6rpx;
		font-size: 28rpx;
		color: #333333;
		min-width: 140rpx;
		text-align: center;
}
.time-separator {
		margin: 0 15rpx;
		color: #999999;
		font-size: 24rpx;
}
.submit-btn {
		width: 100%;
		height: 90rpx;
		background-color: #ff5a5f;
		border-radius: 45rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 30rpx;
		margin-bottom: 50rpx;
}
.submit-btn text {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: bold;
}
