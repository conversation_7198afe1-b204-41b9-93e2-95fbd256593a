
.checkout-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 150rpx;
}
.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
}
.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.back-btn image {
		width: 40rpx;
		height: 40rpx;
}
.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
.address-section {
		background-color: #ffffff;
		margin-top: 20rpx;
		padding: 30rpx;
}
.address-empty {
		display: flex;
		align-items: center;
}
.location-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
}
.add-address {
		flex: 1;
		font-size: 30rpx;
		color: #333333;
}
.right-icon {
		width: 30rpx;
		height: 30rpx;
}
.address-info {
		display: flex;
		align-items: flex-start;
}
.address-details {
		flex: 1;
}
.user-info {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
}
.name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-right: 20rpx;
}
.phone {
		font-size: 28rpx;
		color: #666666;
}
.address-text {
		font-size: 28rpx;
		color: #333333;
		line-height: 1.4;
}
.goods-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
}
.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 30rpx;
}
.goods-list {
}
.goods-item {
		display: flex;
		margin-bottom: 30rpx;
}
.goods-item:last-child {
		margin-bottom: 0;
}
.goods-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
}
.goods-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
}
.goods-name {
		font-size: 28rpx;
		color: #333333;
		line-height: 1.4;
		margin-bottom: 10rpx;
}
.goods-spec {
		font-size: 26rpx;
		color: #999999;
		margin-bottom: 10rpx;
}
.goods-price-qty {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.goods-price {
		font-size: 30rpx;
		color: #ff5a5f;
		font-weight: bold;
}
.goods-qty {
		font-size: 26rpx;
		color: #999999;
}
.delivery-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
}
.delivery-options {
}
.delivery-option {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
}
.delivery-option:last-child {
		border-bottom: none;
}
.delivery-option.selected {
		color: #ff5a5f;
}
.select-circle {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		border: 2rpx solid #ddd;
		display: flex;
		align-items: center;
		justify-content: center;
}
.selected .select-circle {
		border-color: #ff5a5f;
}
.inner-circle {
		width: 24rpx;
		height: 24rpx;
		border-radius: 50%;
		background-color: #ff5a5f;
}
.payment-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
}
.payment-options {
}
.payment-option {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
}
.payment-option:last-child {
		border-bottom: none;
}
.option-left {
		display: flex;
		align-items: center;
}
.payment-icon {
		width: 60rpx;
		height: 60rpx;
		margin-right: 20rpx;
}
.payment-option.selected {
		color: #ff5a5f;
}
.balance-info {
		font-size: 24rpx;
		color: #999999;
		margin-left: 10rpx;
}
.coupon-section, .remark-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
}
.section-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
}
.item-right {
		display: flex;
		align-items: center;
}
.right-text {
		font-size: 28rpx;
		color: #666666;
		margin-right: 10rpx;
}
.remark-input {
		flex: 1;
		text-align: right;
		font-size: 28rpx;
		color: #333333;
		height: 60rpx;
}
.amount-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
}
.amount-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
}
.amount-item:last-child {
		margin-bottom: 0;
}
.discount {
		color: #ff5a5f;
}
.submit-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #ffffff;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.total-amount {
		flex: 1;
		display: flex;
		align-items: center;
}
.amount {
		font-size: 36rpx;
		color: #ff5a5f;
		font-weight: bold;
}
.submit-btn {
		width: 240rpx;
		height: 80rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.submit-btn.disabled {
		background-color: #cccccc;
}
.submit-btn text {
		font-size: 30rpx;
		color: #ffffff;
		font-weight: bold;
}
