"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      type: "user",
      // 默认为用户协议
      title: "用户协议"
    };
  },
  onLoad(options) {
    if (options.type) {
      this.type = options.type;
      if (this.type === "privacy") {
        this.title = "隐私政策";
      } else {
        this.title = "用户协议";
      }
    }
  },
  methods: {
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_vendor.t($data.title),
    d: $data.type === "user"
  }, $data.type === "user" ? {} : $data.type === "privacy" ? {} : {}, {
    e: $data.type === "privacy"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
