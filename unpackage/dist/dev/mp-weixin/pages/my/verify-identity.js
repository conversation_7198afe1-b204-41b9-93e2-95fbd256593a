"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      verificationStatus: "",
      // 空字符串表示未认证，'processing'表示审核中，'approved'表示已认证，'rejected'表示被拒绝
      statusIcon: {
        "processing": "/static/icons/processing.png",
        "approved": "/static/icons/success.png",
        "rejected": "/static/icons/error.png"
      },
      statusText: {
        "processing": "审核中",
        "approved": "认证成功",
        "rejected": "认证失败"
      },
      statusDesc: {
        "processing": "您的实名认证信息正在审核中，预计1-2个工作日内完成审核，请耐心等待。",
        "approved": "您已通过实名认证，可以使用平台的全部功能。",
        "rejected": "很抱歉，您的实名认证未通过审核，请检查认证信息或联系客服。"
      },
      formData: {
        realName: "",
        idNumber: "",
        phoneNumber: "",
        verifyCode: "",
        idCardFront: "",
        idCardBack: "",
        selfieWithIdCard: "",
        agreement: false
      },
      showVerifyCodeInput: false,
      isPhoneVerified: false,
      isCounting: false,
      countdown: 60,
      codeText: "获取验证码",
      verificationReason: ""
      // 拒绝原因
    };
  },
  computed: {
    // 判断手机号是否有效
    isPhoneValid() {
      return /^1\d{10}$/.test(this.formData.phoneNumber);
    },
    // 判断表单是否有效
    isFormValid() {
      return this.formData.realName && this.formData.idNumber && this.formData.phoneNumber && (this.isPhoneVerified || this.formData.verifyCode) && this.formData.idCardFront && this.formData.idCardBack && this.formData.agreement;
    },
    // 判断表单是否被禁用
    isDisabled() {
      return this.verificationStatus === "processing";
    }
  },
  onLoad() {
    this.checkVerificationStatus();
  },
  methods: {
    // 检查认证状态
    checkVerificationStatus() {
      const verificationStorage = common_vendor.index.getStorageSync("user_verification");
      if (verificationStorage) {
        const verification = JSON.parse(verificationStorage);
        this.verificationStatus = verification.status;
        this.formData = verification.formData || this.formData;
        this.verificationReason = verification.reason || "";
        this.isPhoneVerified = !!this.formData.phoneNumber;
      }
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 显示帮助信息
    showHelp() {
      common_vendor.index.showModal({
        title: "关于实名认证",
        content: "实名认证是为了保障用户的合法权益和平台的安全运营。通过实名认证后，您可以使用平台的全部功能，包括发布信息、交易和提现等操作。我们会严格保护您的个人信息安全。",
        showCancel: false
      });
    },
    // 发送验证码
    sendCode() {
      if (!this.isPhoneValid || this.isCounting) {
        return;
      }
      this.isCounting = true;
      this.countdown = 60;
      this.codeText = `${this.countdown}秒后重发`;
      common_vendor.index.showToast({
        title: "验证码已发送",
        icon: "success"
      });
      this.showVerifyCodeInput = true;
      const timer = setInterval(() => {
        this.countdown--;
        this.codeText = `${this.countdown}秒后重发`;
        if (this.countdown <= 0) {
          clearInterval(timer);
          this.isCounting = false;
          this.codeText = "获取验证码";
        }
      }, 1e3);
    },
    // 验证手机号
    verifyPhone() {
      if (!this.formData.verifyCode) {
        common_vendor.index.showToast({
          title: "请输入验证码",
          icon: "none"
        });
        return;
      }
      if (this.formData.verifyCode === "123456" || this.formData.verifyCode === "1234") {
        this.isPhoneVerified = true;
        common_vendor.index.showToast({
          title: "手机号验证成功",
          icon: "success"
        });
      } else {
        common_vendor.index.showToast({
          title: "验证码错误",
          icon: "none"
        });
      }
    },
    // 上传图片
    uploadImage(type) {
      if (this.isDisabled) {
        return;
      }
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.showLoading({
            title: "上传中..."
          });
          setTimeout(() => {
            common_vendor.index.hideLoading();
            switch (type) {
              case "front":
                this.formData.idCardFront = tempFilePath;
                break;
              case "back":
                this.formData.idCardBack = tempFilePath;
                break;
              case "selfie":
                this.formData.selfieWithIdCard = tempFilePath;
                break;
            }
          }, 1500);
        }
      });
    },
    // 删除图片
    deleteImage(type) {
      switch (type) {
        case "front":
          this.formData.idCardFront = "";
          break;
        case "back":
          this.formData.idCardBack = "";
          break;
        case "selfie":
          this.formData.selfieWithIdCard = "";
          break;
      }
    },
    // 切换协议同意状态
    toggleAgreement() {
      if (!this.isDisabled) {
        this.formData.agreement = !this.formData.agreement;
      }
    },
    // 查看用户协议
    viewUserAgreement() {
      common_vendor.index.navigateTo({
        url: "/pages/my/agreement?type=user"
      });
    },
    // 查看隐私政策
    viewPrivacyPolicy() {
      common_vendor.index.navigateTo({
        url: "/pages/my/agreement?type=privacy"
      });
    },
    // 提交认证
    submitVerification() {
      if (!this.isFormValid || this.isDisabled) {
        return;
      }
      if (!this.isPhoneVerified) {
        this.verifyPhone();
        if (!this.isPhoneVerified) {
          return;
        }
      }
      if (!this.validateIdNumber(this.formData.idNumber)) {
        common_vendor.index.showToast({
          title: "身份证号格式不正确",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "提交中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        this.verificationStatus = "processing";
        const verification = {
          status: this.verificationStatus,
          formData: this.formData,
          submitTime: (/* @__PURE__ */ new Date()).getTime()
        };
        common_vendor.index.setStorageSync("user_verification", JSON.stringify(verification));
        common_vendor.index.showToast({
          title: "提交成功",
          icon: "success"
        });
        setTimeout(() => {
          const result = Math.random() > 0.3 ? "approved" : "rejected";
          this.verificationStatus = result;
          verification.status = result;
          if (result === "rejected") {
            verification.reason = "身份证信息与实际不符，请核对后重新提交";
            this.verificationReason = verification.reason;
          }
          common_vendor.index.setStorageSync("user_verification", JSON.stringify(verification));
          if (result === "approved") {
            const userInfoStorage = common_vendor.index.getStorageSync("userInfo");
            if (userInfoStorage) {
              const userInfo = JSON.parse(userInfoStorage);
              userInfo.isVerified = true;
              userInfo.realName = this.formData.realName;
              common_vendor.index.setStorageSync("userInfo", JSON.stringify(userInfo));
            }
          }
        }, 5e3);
      }, 1500);
    },
    // 验证身份证号
    validateIdNumber(idNumber) {
      const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      return reg.test(idNumber);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_assets._imports_1$5,
    d: common_vendor.o((...args) => $options.showHelp && $options.showHelp(...args)),
    e: $data.verificationStatus
  }, $data.verificationStatus ? {
    f: $data.statusIcon[$data.verificationStatus],
    g: common_vendor.t($data.statusText[$data.verificationStatus]),
    h: common_vendor.t($data.statusDesc[$data.verificationStatus])
  } : {}, {
    i: $data.verificationStatus !== "approved" && $data.verificationStatus !== "processing"
  }, $data.verificationStatus !== "approved" && $data.verificationStatus !== "processing" ? common_vendor.e({
    j: $options.isDisabled,
    k: $data.formData.realName,
    l: common_vendor.o(($event) => $data.formData.realName = $event.detail.value),
    m: $options.isDisabled,
    n: $data.formData.idNumber,
    o: common_vendor.o(($event) => $data.formData.idNumber = $event.detail.value),
    p: $options.isDisabled || $data.isPhoneVerified,
    q: $data.formData.phoneNumber,
    r: common_vendor.o(($event) => $data.formData.phoneNumber = $event.detail.value),
    s: !$data.isPhoneVerified
  }, !$data.isPhoneVerified ? {
    t: common_vendor.t($data.codeText),
    v: !$options.isPhoneValid || $data.isCounting ? 1 : "",
    w: common_vendor.o((...args) => $options.sendCode && $options.sendCode(...args))
  } : {}, {
    x: $data.showVerifyCodeInput
  }, $data.showVerifyCodeInput ? {
    y: $options.isDisabled,
    z: $data.formData.verifyCode,
    A: common_vendor.o(($event) => $data.formData.verifyCode = $event.detail.value)
  } : {}, {
    B: !$data.formData.idCardFront || $options.isDisabled
  }, !$data.formData.idCardFront || $options.isDisabled ? common_vendor.e({
    C: $data.formData.idCardFront
  }, $data.formData.idCardFront ? {
    D: $data.formData.idCardFront
  } : {
    E: common_assets._imports_3$7
  }, {
    F: common_vendor.o(($event) => $options.uploadImage("front"))
  }) : {
    G: $data.formData.idCardFront,
    H: common_assets._imports_2$1,
    I: common_vendor.o(($event) => $options.deleteImage("front"))
  }, {
    J: !$data.formData.idCardBack || $options.isDisabled
  }, !$data.formData.idCardBack || $options.isDisabled ? common_vendor.e({
    K: $data.formData.idCardBack
  }, $data.formData.idCardBack ? {
    L: $data.formData.idCardBack
  } : {
    M: common_assets._imports_3$7
  }, {
    N: common_vendor.o(($event) => $options.uploadImage("back"))
  }) : {
    O: $data.formData.idCardBack,
    P: common_assets._imports_2$1,
    Q: common_vendor.o(($event) => $options.deleteImage("back"))
  }, {
    R: !$data.formData.selfieWithIdCard || $options.isDisabled
  }, !$data.formData.selfieWithIdCard || $options.isDisabled ? common_vendor.e({
    S: $data.formData.selfieWithIdCard
  }, $data.formData.selfieWithIdCard ? {
    T: $data.formData.selfieWithIdCard
  } : {
    U: common_assets._imports_3$7
  }, {
    V: common_vendor.o(($event) => $options.uploadImage("selfie"))
  }) : {
    W: $data.formData.selfieWithIdCard,
    X: common_assets._imports_2$1,
    Y: common_vendor.o(($event) => $options.deleteImage("selfie"))
  }, {
    Z: $data.formData.agreement
  }, $data.formData.agreement ? {} : {}, {
    aa: common_vendor.o((...args) => $options.toggleAgreement && $options.toggleAgreement(...args)),
    ab: common_vendor.o((...args) => $options.viewPrivacyPolicy && $options.viewPrivacyPolicy(...args)),
    ac: common_vendor.o((...args) => $options.viewUserAgreement && $options.viewUserAgreement(...args)),
    ad: common_vendor.t($data.verificationStatus === "rejected" ? "重新提交" : "提交认证"),
    ae: !$options.isFormValid || $options.isDisabled ? 1 : "",
    af: common_vendor.o((...args) => $options.submitVerification && $options.submitVerification(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
