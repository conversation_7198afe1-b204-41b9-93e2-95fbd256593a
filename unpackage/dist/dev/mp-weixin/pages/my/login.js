"use strict";
const common_vendor = require("../../common/vendor.js");
const api_auth = require("../../api/auth.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      currentMode: "login",
      // login, register, forgotPassword
      loginType: "phone",
      // phone, password
      phoneNumber: "",
      verifyCode: "",
      password: "",
      username: "",
      showPassword: false,
      countdown: 0,
      codeText: "获取验证码",
      agreeProtocol: true,
      timer: null,
      loading: false
    };
  },
  computed: {
    canSendCode() {
      return this.isValidPhone() && this.countdown === 0;
    },
    canLogin() {
      if (this.loginType === "phone") {
        return this.isValidPhone() && this.verifyCode.length === 6 && this.agreeProtocol;
      } else {
        return this.isValidPhone() && this.password.length >= 6 && this.agreeProtocol;
      }
    },
    canRegister() {
      return this.isValidPhone() && this.verifyCode.length === 6 && this.password.length >= 6 && this.username.length >= 2 && this.agreeProtocol;
    },
    canResetPassword() {
      return this.isValidPhone() && this.verifyCode.length === 6 && this.password.length >= 6;
    }
  },
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  methods: {
    // 检查手机号是否有效
    isValidPhone() {
      return /^1[3-9]\d{9}$/.test(this.phoneNumber);
    },
    // 切换密码显示状态
    togglePasswordVisibility() {
      this.showPassword = !this.showPassword;
    },
    // 切换到登录模式
    switchToLogin() {
      this.currentMode = "login";
      this.clearForm();
    },
    // 切换到注册模式
    switchToRegister() {
      this.currentMode = "register";
      this.clearForm();
    },
    // 切换到找回密码模式
    switchToForgotPassword() {
      this.currentMode = "forgotPassword";
      this.clearForm();
    },
    // 清除表单
    clearForm() {
      this.verifyCode = "";
      this.password = "";
      this.username = "";
    },
    // 切换协议同意状态
    toggleAgreement() {
      this.agreeProtocol = !this.agreeProtocol;
    },
    // 跳转页面
    navigateTo(url) {
      common_vendor.index.navigateTo({
        url
      });
    },
    // 发送验证码
    async sendVerifyCode() {
      if (!this.canSendCode || this.loading)
        return;
      let smsType = "login";
      if (this.currentMode === "register") {
        smsType = "register";
      } else if (this.currentMode === "forgotPassword") {
        smsType = "resetPassword";
      }
      this.loading = true;
      try {
        if (smsType === "register") {
          const checkResult = await api_auth.checkPhoneExists(this.phoneNumber);
          if (checkResult.data.exists) {
            common_vendor.index.showToast({
              title: "该手机号已注册，请直接登录",
              icon: "none"
            });
            this.switchToLogin();
            return;
          }
        }
        const result = await api_auth.sendSmsCode(this.phoneNumber, smsType);
        if (result.code === 0) {
          common_vendor.index.showToast({
            title: "验证码已发送",
            icon: "success"
          });
          this.countdown = 60;
          this.codeText = `${this.countdown}秒`;
          this.timer = setInterval(() => {
            this.countdown--;
            this.codeText = `${this.countdown}秒`;
            if (this.countdown === 0) {
              clearInterval(this.timer);
              this.timer = null;
              this.codeText = "获取验证码";
            }
          }, 1e3);
        } else {
          common_vendor.index.showToast({
            title: result.message || "发送失败，请重试",
            icon: "none"
          });
        }
      } catch (err) {
        console.error("发送验证码失败", err);
        common_vendor.index.showToast({
          title: "发送失败，请重试",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 登录
    async login() {
      if (!this.canLogin || this.loading)
        return;
      if (!this.agreeProtocol) {
        common_vendor.index.showToast({
          title: "请先同意用户协议和隐私政策",
          icon: "none"
        });
        return;
      }
      this.loading = true;
      common_vendor.index.showLoading({
        title: "登录中..."
      });
      try {
        let result;
        if (this.loginType === "phone") {
          result = await api_auth.loginByPhone(this.phoneNumber, this.verifyCode);
        } else {
          result = await api_auth.loginByPassword(this.phoneNumber, this.password);
        }
        if (result.code === 0) {
          common_vendor.index.setStorageSync("token", result.data.token);
          common_vendor.index.setStorageSync("userInfo", result.data.user);
          common_vendor.index.showToast({
            title: "登录成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: result.message || "登录失败，请检查账号密码",
            icon: "none"
          });
        }
      } catch (err) {
        console.error("登录失败", err);
        common_vendor.index.showToast({
          title: "登录失败，请检查网络并重试",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
        this.loading = false;
      }
    },
    // 注册
    async register() {
      if (!this.canRegister || this.loading)
        return;
      if (!this.agreeProtocol) {
        common_vendor.index.showToast({
          title: "请先同意用户协议和隐私政策",
          icon: "none"
        });
        return;
      }
      this.loading = true;
      common_vendor.index.showLoading({
        title: "注册中..."
      });
      try {
        const result = await api_auth.register({
          username: this.username,
          phone: this.phoneNumber,
          password: this.password,
          code: this.verifyCode
        });
        if (result.code === 0) {
          common_vendor.index.setStorageSync("token", result.data.token);
          common_vendor.index.setStorageSync("userInfo", result.data.user);
          common_vendor.index.showToast({
            title: "注册成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: result.message || "注册失败，请重试",
            icon: "none"
          });
        }
      } catch (err) {
        console.error("注册失败", err);
        common_vendor.index.showToast({
          title: "注册失败，请检查网络并重试",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
        this.loading = false;
      }
    },
    // 重置密码
    async resetPassword() {
      if (!this.canResetPassword || this.loading)
        return;
      this.loading = true;
      common_vendor.index.showLoading({
        title: "重置中..."
      });
      try {
        const result = await api_auth.resetPassword(this.phoneNumber, this.verifyCode, this.password);
        if (result.code === 0) {
          common_vendor.index.showToast({
            title: "密码重置成功，请登录",
            icon: "success"
          });
          setTimeout(() => {
            this.switchToLogin();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: result.message || "重置失败，请重试",
            icon: "none"
          });
        }
      } catch (err) {
        console.error("重置密码失败", err);
        common_vendor.index.showToast({
          title: "重置失败，请检查网络并重试",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
        this.loading = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$4,
    b: $data.currentMode === "login"
  }, $data.currentMode === "login" ? common_vendor.e({
    c: $data.loginType === "phone" ? 1 : "",
    d: common_vendor.o(($event) => $data.loginType = "phone"),
    e: $data.loginType === "password" ? 1 : "",
    f: common_vendor.o(($event) => $data.loginType = "password"),
    g: $data.phoneNumber,
    h: common_vendor.o(($event) => $data.phoneNumber = $event.detail.value),
    i: $data.loginType === "phone"
  }, $data.loginType === "phone" ? {
    j: $data.verifyCode,
    k: common_vendor.o(($event) => $data.verifyCode = $event.detail.value),
    l: common_vendor.t($data.codeText),
    m: !$options.canSendCode ? 1 : "",
    n: common_vendor.o((...args) => $options.sendVerifyCode && $options.sendVerifyCode(...args))
  } : {}, {
    o: $data.loginType === "password"
  }, $data.loginType === "password" ? {
    p: $data.showPassword ? "text" : "password",
    q: $data.password,
    r: common_vendor.o(($event) => $data.password = $event.detail.value),
    s: $data.showPassword ? "/static/icons/eye-open.png" : "/static/icons/eye-close.png",
    t: common_vendor.o((...args) => $options.togglePasswordVisibility && $options.togglePasswordVisibility(...args))
  } : {}, {
    v: !$options.canLogin ? 1 : "",
    w: common_vendor.o((...args) => $options.login && $options.login(...args)),
    x: common_vendor.o((...args) => $options.switchToForgotPassword && $options.switchToForgotPassword(...args)),
    y: common_vendor.o((...args) => $options.switchToRegister && $options.switchToRegister(...args)),
    z: $data.agreeProtocol,
    A: common_vendor.o((...args) => $options.toggleAgreement && $options.toggleAgreement(...args)),
    B: common_vendor.o(($event) => $options.navigateTo("/pages/my/agreement")),
    C: common_vendor.o(($event) => $options.navigateTo("/pages/my/privacy"))
  }) : {}, {
    D: $data.currentMode === "register"
  }, $data.currentMode === "register" ? {
    E: common_vendor.o((...args) => $options.switchToLogin && $options.switchToLogin(...args)),
    F: $data.username,
    G: common_vendor.o(($event) => $data.username = $event.detail.value),
    H: $data.phoneNumber,
    I: common_vendor.o(($event) => $data.phoneNumber = $event.detail.value),
    J: $data.verifyCode,
    K: common_vendor.o(($event) => $data.verifyCode = $event.detail.value),
    L: common_vendor.t($data.codeText),
    M: !$options.canSendCode ? 1 : "",
    N: common_vendor.o((...args) => $options.sendVerifyCode && $options.sendVerifyCode(...args)),
    O: $data.showPassword ? "text" : "password",
    P: $data.password,
    Q: common_vendor.o(($event) => $data.password = $event.detail.value),
    R: $data.showPassword ? "/static/icons/eye-open.png" : "/static/icons/eye-close.png",
    S: common_vendor.o((...args) => $options.togglePasswordVisibility && $options.togglePasswordVisibility(...args)),
    T: !$options.canRegister ? 1 : "",
    U: common_vendor.o((...args) => $options.register && $options.register(...args)),
    V: $data.agreeProtocol,
    W: common_vendor.o((...args) => $options.toggleAgreement && $options.toggleAgreement(...args)),
    X: common_vendor.o(($event) => $options.navigateTo("/pages/my/agreement")),
    Y: common_vendor.o(($event) => $options.navigateTo("/pages/my/privacy"))
  } : {}, {
    Z: $data.currentMode === "forgotPassword"
  }, $data.currentMode === "forgotPassword" ? {
    aa: common_vendor.o((...args) => $options.switchToLogin && $options.switchToLogin(...args)),
    ab: $data.phoneNumber,
    ac: common_vendor.o(($event) => $data.phoneNumber = $event.detail.value),
    ad: $data.verifyCode,
    ae: common_vendor.o(($event) => $data.verifyCode = $event.detail.value),
    af: common_vendor.t($data.codeText),
    ag: !$options.canSendCode ? 1 : "",
    ah: common_vendor.o((...args) => $options.sendVerifyCode && $options.sendVerifyCode(...args)),
    ai: $data.showPassword ? "text" : "password",
    aj: $data.password,
    ak: common_vendor.o(($event) => $data.password = $event.detail.value),
    al: $data.showPassword ? "/static/icons/eye-open.png" : "/static/icons/eye-close.png",
    am: common_vendor.o((...args) => $options.togglePasswordVisibility && $options.togglePasswordVisibility(...args)),
    an: !$options.canResetPassword ? 1 : "",
    ao: common_vendor.o((...args) => $options.resetPassword && $options.resetPassword(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0a40c652"]]);
wx.createPage(MiniProgramPage);
