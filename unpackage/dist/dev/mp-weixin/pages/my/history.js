"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      tabs: [
        { name: "全部", type: "all" },
        { name: "驿站转让", type: "station" },
        { name: "顶班服务", type: "service" },
        { name: "招聘信息", type: "job" },
        { name: "设备信息", type: "device" }
      ],
      activeTab: 0,
      historyList: [],
      pageNum: 1,
      pageSize: 10,
      hasMore: true,
      loading: false
    };
  },
  computed: {
    filteredHistory() {
      if (this.activeTab === 0) {
        return this.historyList;
      } else {
        const type = this.tabs[this.activeTab].type;
        return this.historyList.filter((item) => item.type === type);
      }
    }
  },
  onLoad() {
    this.loadHistoryData();
  },
  methods: {
    // 加载历史记录数据
    loadHistoryData() {
      this.loading = true;
      const params = {
        page: this.pageNum,
        pageSize: this.pageSize,
        type: this.activeTab === 0 ? "" : this.tabs[this.activeTab].type
      };
      this.$api.user.getHistory(params).then((res) => {
        if (res.code === 0) {
          if (this.pageNum === 1) {
            this.historyList = res.data.list || [];
          } else {
            this.historyList = [...this.historyList, ...res.data.list || []];
          }
          this.hasMore = this.historyList.length < res.data.total;
        } else {
          common_vendor.index.showToast({
            title: res.message || "获取浏览历史失败",
            icon: "none"
          });
        }
      }).catch((err) => {
        console.error("获取浏览历史失败", err);
        common_vendor.index.showToast({
          title: "网络异常，请稍后重试",
          icon: "none"
        });
      }).finally(() => {
        this.loading = false;
        common_vendor.index.stopPullDownRefresh();
      });
    },
    // 加载更多数据
    loadMore() {
      if (!this.hasMore || this.loading)
        return;
      this.pageNum++;
      this.loadHistoryData();
    },
    // 切换标签
    switchTab(index) {
      this.activeTab = index;
    },
    // 删除历史记录
    deleteItem(id) {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要删除此条浏览记录吗？",
        success: (res) => {
          if (res.confirm) {
            this.historyList = this.historyList.filter((item) => item.id !== id);
            common_vendor.index.setStorageSync("browsing_history", JSON.stringify(this.historyList));
            common_vendor.index.showToast({
              title: "删除成功",
              icon: "success"
            });
          }
        }
      });
    },
    // 清空历史记录
    clearHistory() {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要清空全部浏览记录吗？",
        success: (res) => {
          if (res.confirm) {
            if (this.activeTab === 0) {
              this.historyList = [];
            } else {
              const type = this.tabs[this.activeTab].type;
              this.historyList = this.historyList.filter((item) => item.type !== type);
            }
            common_vendor.index.setStorageSync("browsing_history", JSON.stringify(this.historyList));
            common_vendor.index.showToast({
              title: "清空成功",
              icon: "success"
            });
          }
        }
      });
    },
    // 查看详情
    viewDetail(item) {
      let url = "";
      switch (item.type) {
        case "station":
          url = "/pages/detail/station?id=" + item.id;
          break;
        case "service":
          url = "/pages/detail/service?id=" + item.id;
          break;
        case "job":
          url = "/pages/detail/job?id=" + item.id;
          break;
        case "device":
          url = "/pages/detail/device?id=" + item.id;
          break;
      }
      if (url) {
        common_vendor.index.navigateTo({
          url
        });
      }
    },
    // 返回上一页
    navigateBack() {
      common_vendor.index.navigateBack();
    },
    // 格式化时间
    formatTime(timestamp) {
      const now = Date.now();
      const diff = now - timestamp;
      if (diff < 36e5) {
        return Math.floor(diff / 6e4) + "分钟前";
      } else if (diff < 864e5) {
        return Math.floor(diff / 36e5) + "小时前";
      } else {
        const date = new Date(timestamp);
        return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()}`;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0$3,
    b: common_vendor.o((...args) => $options.navigateBack && $options.navigateBack(...args)),
    c: common_vendor.o((...args) => $options.clearHistory && $options.clearHistory(...args)),
    d: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: common_vendor.n($data.activeTab === index ? "active-tab" : ""),
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    e: $options.filteredHistory.length === 0
  }, $options.filteredHistory.length === 0 ? {
    f: common_assets._imports_7$2
  } : common_vendor.e({
    g: $data.activeTab === 0 || $data.activeTab === 1
  }, $data.activeTab === 0 || $data.activeTab === 1 ? {
    h: common_vendor.f($options.filteredHistory, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.price),
        c: common_vendor.t(item.area),
        d: common_vendor.t(item.address),
        e: common_vendor.t(item.advantage),
        f: common_vendor.t($options.formatTime(item.viewTime)),
        g: common_vendor.o(($event) => $options.deleteItem(item.id), item.id),
        h: item.id,
        i: common_vendor.o(($event) => $options.viewDetail(item), item.id)
      };
    }),
    i: common_assets._imports_2$2
  } : {}, {
    j: $data.activeTab === 0 || $data.activeTab === 2
  }, $data.activeTab === 0 || $data.activeTab === 2 ? {
    k: common_vendor.f($options.filteredHistory, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.price),
        c: common_vendor.t(item.workTime),
        d: common_vendor.t(item.address),
        e: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        f: common_vendor.t($options.formatTime(item.viewTime)),
        g: common_vendor.o(($event) => $options.deleteItem(item.id), item.id),
        h: item.id,
        i: common_vendor.o(($event) => $options.viewDetail(item), item.id)
      };
    }),
    l: common_assets._imports_2$2
  } : {}, {
    m: $data.activeTab === 0 || $data.activeTab === 3
  }, $data.activeTab === 0 || $data.activeTab === 3 ? {
    n: common_vendor.f($options.filteredHistory, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.salary),
        c: common_vendor.t(item.experience),
        d: common_vendor.t(item.address),
        e: common_vendor.t(item.company),
        f: common_vendor.t($options.formatTime(item.viewTime)),
        g: common_vendor.o(($event) => $options.deleteItem(item.id), item.id),
        h: item.id,
        i: common_vendor.o(($event) => $options.viewDetail(item), item.id)
      };
    }),
    o: common_assets._imports_2$2
  } : {}, {
    p: $data.activeTab === 0 || $data.activeTab === 4
  }, $data.activeTab === 0 || $data.activeTab === 4 ? {
    q: common_vendor.f($options.filteredHistory, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.price),
        c: common_vendor.t(item.condition),
        d: common_vendor.t(item.address),
        e: common_vendor.t(item.description),
        f: common_vendor.t($options.formatTime(item.viewTime)),
        g: common_vendor.o(($event) => $options.deleteItem(item.id), item.id),
        h: item.id,
        i: common_vendor.o(($event) => $options.viewDetail(item), item.id)
      };
    }),
    r: common_assets._imports_2$2
  } : {}), {
    s: $data.hasMore && $options.filteredHistory.length > 0
  }, $data.hasMore && $options.filteredHistory.length > 0 ? {} : {}, {
    t: !$data.hasMore && $options.filteredHistory.length > 0
  }, !$data.hasMore && $options.filteredHistory.length > 0 ? {} : {}, {
    v: common_vendor.o((...args) => $options.loadMore && $options.loadMore(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
