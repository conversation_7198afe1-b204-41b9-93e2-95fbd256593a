
	/* container */
.container {
		padding-bottom: 30rpx;
}
	
	/* 搜索栏 */
.search-bar {
		position: -webkit-sticky;
		position: sticky;
		top: 0;
		background-color: #fff;
		z-index: 100;
		padding: 20rpx;
		display: flex;
		align-items: center;
}
	
	/* 搜索输入框 */
.search-input-box {
		flex: 1;
		background-color: #f5f5f5;
		border-radius: 36rpx;
		height: 72rpx;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		margin-right: 20rpx;
}
	
	/* 搜索图标 */
.search-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 15rpx;
}
	
	/* 输入框 */
.search-input-box input {
		flex: 1;
		height: 72rpx;
		font-size: 28rpx;
}
	
	/* 清除按钮 */
.clear-btn {
		width: 36rpx;
		height: 36rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
	
	/* 清除按钮图标 */
.clear-btn image {
		width: 24rpx;
		height: 24rpx;
}
	
	/* 筛选按钮 */
.filter-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
	
	/* 筛选按钮图标 */
.filter-btn image {
		width: 36rpx;
		height: 36rpx;
}
	
	/* 分类导航 */
.category-nav {
		display: flex;
		padding: 20rpx 0;
		background-color: #ffffff;
		margin-bottom: 20rpx;
		overflow-x: auto;
		white-space: nowrap;
}
	
	/* 分类项 */
.category-item {
		flex: 0 0 auto;
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 16.666%;
		padding: 0 10rpx;
		box-sizing: border-box;
}
	
	/* 分类图标 */
.category-item image {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 10rpx;
}
	
	/* 分类文本 */
.category-item text {
		font-size: 24rpx;
		color: #666666;
		white-space: nowrap;
}
	
	/* 当前选中分类文本 */
.category-item.active text {
		color: #ff5a5f;
		font-weight: bold;
}
	
	/* 当前选中分类图标 */
.category-item.active image {
		transform: scale(1.1);
}
.quick-filter {
		display: flex;
		background-color: #ffffff;
		padding: 20rpx 0;
		margin-bottom: 20rpx;
}
.filter-tab {
		flex: 1;
		text-align: center;
		font-size: 28rpx;
		color: #666666;
		position: relative;
}
.filter-tab.active {
		color: #ff5a5f;
		font-weight: bold;
}
.sort-arrow {
		margin-left: 5rpx;
}
.filter-section {
		background-color: #ffffff;
		padding: 20rpx;
		margin-bottom: 20rpx;
}
.filter-row {
		margin-bottom: 30rpx;
}
.filter-title {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
}
.filter-options {
		display: flex;
		flex-wrap: wrap;
}
.filter-option {
		padding: 10rpx 30rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		font-size: 26rpx;
}
.filter-option.active {
		background-color: #ffecee;
		color: #ff5a5f;
		border: 1rpx solid #ff5a5f;
}
.filter-actions {
		display: flex;
		justify-content: space-between;
}
.reset-btn, .confirm-btn {
		width: 48%;
		height: 80rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
}
.reset-btn {
		background-color: #f5f5f5;
		color: #666666;
}
.confirm-btn {
		background-color: #ff5a5f;
		color: #ffffff;
}
	
	/* 商品图片占位符 */
.product-image-placeholder {
		width: 100%;
		height: 320rpx;
		background-color: #f5f5f5;
		display: flex;
		align-items: center;
		justify-content: center;
}
.product-image-placeholder text {
		color: #999;
		font-size: 28rpx;
}
	
	/* 商品网格 */
.product-grid {
		padding: 0 20rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
}
	
	/* 商品卡片 */
.product-card {
		width: 48%;
		background-color: #ffffff;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
	
	/* 商品信息 */
.product-info {
		padding: 20rpx;
}
	
	/* 商品标题 */
.product-title {
		font-size: 28rpx;
		color: #333333;
		line-height: 1.4;
		height: 78rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		margin-bottom: 10rpx;
}
	
	/* 商品标签 */
.product-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 10rpx;
}
	
	/* 标签项 */
.product-tag {
		font-size: 22rpx;
		color: #ff5a5f;
		background-color: #ffecee;
		border-radius: 4rpx;
		padding: 4rpx 10rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
}
	
	/* 商品价格 */
.product-price {
		font-size: 26rpx;
		color: #ff5a5f;
}
	
	/* 价格数值 */
.price-value {
		font-size: 32rpx;
		font-weight: bold;
}
	
	/* 商品销量 */
.product-sales {
		font-size: 24rpx;
		color: #999;
}
	
	/* 加载更多 */
.loading-more, .no-more {
		text-align: center;
		padding: 20rpx 0;
}
	
	/* 加载文本样式 */
.loading-more text, .no-more text {
		font-size: 24rpx;
		color: #999;
}
	
	/* 搜索面板样式 */
.search-panel {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		z-index: 1000;
		display: flex;
		flex-direction: column;
}
.search-panel-header {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
}
.search-input-container {
		flex: 1;
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		border-radius: 36rpx;
		padding: 0 20rpx;
		height: 72rpx;
		margin-right: 20rpx;
}
.search-input-container input {
		flex: 1;
		height: 72rpx;
		font-size: 28rpx;
}
.search-panel-content {
		flex: 1;
		padding: 20rpx;
}
.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
}
.result-list {
		margin-bottom: 30rpx;
}
.result-item {
		display: flex;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
}
.result-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 20rpx;
}
.result-info {
		flex: 1;
}
.result-title {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
}
.result-tags {
		display: flex;
}
.result-tag {
		font-size: 22rpx;
		color: #666;
		background-color: #f5f5f5;
		padding: 4rpx 12rpx;
		border-radius: 4rpx;
		margin-right: 10rpx;
}
.result-category {
		font-size: 24rpx;
		color: #999;
		margin-left: 20rpx;
}
.no-results {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 80rpx 0;
}
.no-result-icon {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 30rpx;
}
.no-result-text {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
}
.no-result-tip {
		font-size: 26rpx;
		color: #999;
}
.history-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
}
.clear-history {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #999;
}
.clear-history image {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
}
.history-list {
		margin-bottom: 30rpx;
}
.history-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
}
.history-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 20rpx;
}
.history-item text {
		font-size: 28rpx;
		color: #333;
}
.hot-list {
		display: flex;
		flex-wrap: wrap;
}
.hot-item {
		padding: 12rpx 24rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		margin: 0 20rpx 20rpx 0;
		font-size: 24rpx;
		color: #666;
		display: flex;
		align-items: center;
}
.hot-rank {
		width: 32rpx;
		height: 32rpx;
		border-radius: 50%;
		font-size: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 10rpx;
		color: #fff;
}
.rank-1 {
		background-color: #ff5a5f;
}
.rank-2 {
		background-color: #ff9500;
}
.rank-3 {
		background-color: #34c759;
}
.cancel-btn {
		font-size: 28rpx;
		color: #333;
		padding: 0 10rpx;
}
