"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      showFilterSection: false,
      activeCategory: "all",
      activePrice: "all",
      activeType: "all",
      activeTab: "all",
      priceSortAsc: false,
      hasMore: true,
      pageNum: 1,
      pageSize: 10,
      allProducts: [
        {
          id: 1,
          title: "菜鸟驿站专用电子秤 高精度计价台秤",
          tags: ["全新", "顺丰可用", "包邮"],
          price: "298",
          sales: 235,
          image: "/static/images/scale1.jpg",
          category: "scales",
          condition: "new",
          priceRange: "low",
          publishTime: "2023-12-15"
        },
        {
          id: 2,
          title: "热敏标签打印机 快递面单打印",
          tags: ["二手9成新", "通用", "送纸"],
          price: "880",
          sales: 158,
          image: "/static/images/printer1.jpg",
          category: "printer",
          condition: "used",
          priceRange: "medium",
          publishTime: "2023-11-20"
        },
        {
          id: 3,
          title: "驿站分拣架 多层快递存放柜",
          tags: ["全新", "多规格", "可定制"],
          price: "1680",
          sales: 86,
          image: "/static/images/shelf1.jpg",
          category: "shelves",
          condition: "new",
          priceRange: "medium",
          publishTime: "2023-10-25"
        },
        {
          id: 4,
          title: "二手条码扫描枪 有线扫描器",
          tags: ["二手", "通用", "保修3个月"],
          price: "120",
          sales: 367,
          image: "/static/images/scanner1.jpg",
          category: "scanner",
          condition: "used",
          priceRange: "low",
          publishTime: "2024-01-10"
        },
        {
          id: 5,
          title: "快递打包封箱机 胶带切割器",
          tags: ["全新", "包邮", "送胶带"],
          price: "89",
          sales: 512,
          image: "/static/images/sealer1.jpg",
          category: "other",
          condition: "new",
          priceRange: "low",
          publishTime: "2024-02-05"
        },
        // 添加更多电子秤产品
        {
          id: 6,
          title: "高精度电子台秤 不锈钢称重平台",
          tags: ["全新", "带蓝牙", "大显示屏"],
          price: "458",
          sales: 127,
          image: "/static/images/scale2.jpg",
          category: "scales",
          condition: "new",
          priceRange: "low",
          publishTime: "2024-01-12"
        },
        {
          id: 7,
          title: "快递专用电子秤 防水耐用型",
          tags: ["全新", "顺丰专供", "快递可用"],
          price: "329",
          sales: 203,
          image: "/static/images/scale3.jpg",
          category: "scales",
          condition: "new",
          priceRange: "low",
          publishTime: "2023-11-15"
        },
        {
          id: 8,
          title: "二手电子台秤 商用精准计价秤",
          tags: ["二手8成新", "校准精准", "送充电器"],
          price: "199",
          sales: 89,
          image: "/static/images/scale4.jpg",
          category: "scales",
          condition: "used",
          priceRange: "low",
          publishTime: "2023-12-20"
        },
        // 添加更多打印机产品
        {
          id: 9,
          title: "热敏不干胶标签打印机 网口USB接口",
          tags: ["全新", "高速打印", "驱动安装简单"],
          price: "1180",
          sales: 97,
          image: "/static/images/printer2.jpg",
          category: "printer",
          condition: "new",
          priceRange: "medium",
          publishTime: "2024-01-25"
        },
        {
          id: 10,
          title: "二手立象条码打印机 工业级标签机",
          tags: ["二手9成新", "高清打印", "兼容性好"],
          price: "850",
          sales: 65,
          image: "/static/images/printer3.jpg",
          category: "printer",
          condition: "used",
          priceRange: "medium",
          publishTime: "2023-10-18"
        },
        {
          id: 11,
          title: "快递面单打印机 热敏打印 自动切纸",
          tags: ["全新", "兼容多快递", "一键打印"],
          price: "1280",
          sales: 155,
          image: "/static/images/printer4.jpg",
          category: "printer",
          condition: "new",
          priceRange: "medium",
          publishTime: "2024-02-08"
        },
        // 添加更多货架柜产品
        {
          id: 12,
          title: "驿站快递分拣柜 多格收纳架",
          tags: ["全新", "组装简单", "可扩展"],
          price: "1280",
          sales: 72,
          image: "/static/images/shelf2.jpg",
          category: "shelves",
          condition: "new",
          priceRange: "medium",
          publishTime: "2023-11-30"
        },
        {
          id: 13,
          title: "钢制货架 多层重型仓储架",
          tags: ["全新", "承重大", "稳固耐用"],
          price: "2380",
          sales: 56,
          image: "/static/images/shelf3.jpg",
          category: "shelves",
          condition: "new",
          priceRange: "high",
          publishTime: "2024-01-18"
        },
        {
          id: 14,
          title: "可移动快递分拣车 多层推车",
          tags: ["全新", "带刹车", "方便移动"],
          price: "860",
          sales: 108,
          image: "/static/images/shelf4.jpg",
          category: "shelves",
          condition: "new",
          priceRange: "medium",
          publishTime: "2023-12-05"
        },
        {
          id: 15,
          title: "大容量包裹存放柜 DIY组装",
          tags: ["全新", "经济实惠", "包安装"],
          price: "980",
          sales: 87,
          image: "/static/images/shelf5.jpg",
          category: "shelves",
          condition: "new",
          priceRange: "medium",
          publishTime: "2024-02-01"
        },
        // 添加出库仪产品
        {
          id: 16,
          title: "智能出库仪 自动验货拍照",
          tags: ["全新", "自动称重", "智能验货"],
          price: "3980",
          sales: 42,
          image: "/static/images/outdevice1.jpg",
          category: "outdevice",
          condition: "new",
          priceRange: "high",
          publishTime: "2024-01-15"
        },
        {
          id: 17,
          title: "快递出库仪 带扫描拍照功能",
          tags: ["全新", "一体化", "智能防呆"],
          price: "2880",
          sales: 65,
          image: "/static/images/outdevice2.jpg",
          category: "outdevice",
          condition: "new",
          priceRange: "high",
          publishTime: "2023-12-18"
        },
        {
          id: 18,
          title: "二手智能出库仪 自动重量检测",
          tags: ["二手9成新", "带摄像头", "数据上传"],
          price: "1980",
          sales: 37,
          image: "/static/images/outdevice3.jpg",
          category: "outdevice",
          condition: "used",
          priceRange: "medium",
          publishTime: "2024-02-10"
        },
        // 添加智能灯带产品
        {
          id: 19,
          title: "智能分拣灯带 按钮控制系统",
          tags: ["全新", "颜色可调", "自动感应"],
          price: "1580",
          sales: 78,
          image: "/static/images/lightstrip1.jpg",
          category: "lightstrip",
          condition: "new",
          priceRange: "medium",
          publishTime: "2024-01-25"
        },
        {
          id: 20,
          title: "驿站智能灯带 格口指示系统",
          tags: ["全新", "USB供电", "安装简单"],
          price: "680",
          sales: 126,
          image: "/static/images/lightstrip2.jpg",
          category: "lightstrip",
          condition: "new",
          priceRange: "medium",
          publishTime: "2023-11-30"
        },
        {
          id: 21,
          title: "LED智能提示灯带 分拣辅助",
          tags: ["全新", "低功耗", "可连接系统"],
          price: "880",
          sales: 95,
          image: "/static/images/lightstrip3.jpg",
          category: "lightstrip",
          condition: "new",
          priceRange: "medium",
          publishTime: "2024-02-05"
        }
      ],
      productList: [],
      // 添加搜索相关数据
      searchHistory: [],
      hotSearches: [
        "电子秤",
        "打印机",
        "货架",
        "收银机",
        "包装材料",
        "标签打印机",
        "扫码枪",
        "快递单打印机"
      ],
      showSearchPanel: false,
      searchResults: [],
      isSearching: false,
      // 增加搜索相关的数据
      recentlyViewed: [],
      popularCategories: ["出库仪", "智能灯带", "电子秤", "打印机"],
      searchDebounceTimer: null
    };
  },
  onLoad() {
    this.initProductList();
    this.loadSearchHistory();
  },
  onShow() {
    this.filterProducts();
  },
  onReachBottom() {
    if (this.hasMore) {
      this.loadMoreData();
    }
  },
  methods: {
    // 初始化商品列表
    initProductList() {
      this.filterProducts();
    },
    // 根据当前筛选条件过滤商品
    filterProducts() {
      try {
        let filteredList = [...this.allProducts];
        if (this.activeCategory !== "all") {
          filteredList = filteredList.filter((item) => item.category === this.activeCategory);
        }
        if (this.activePrice !== "all") {
          filteredList = filteredList.filter((item) => item.priceRange === this.activePrice);
        }
        if (this.activeType !== "all") {
          filteredList = filteredList.filter((item) => item.condition === this.activeType);
        }
        if (this.searchKeyword && this.searchKeyword.trim()) {
          const keyword = this.searchKeyword.trim().toLowerCase();
          filteredList = filteredList.filter((item) => {
            if (item.title.toLowerCase().includes(keyword)) {
              return true;
            }
            if (item.tags && item.tags.some((tag) => tag.toLowerCase().includes(keyword))) {
              return true;
            }
            const category = this.getCategoryName(item.category).toLowerCase();
            if (category.includes(keyword)) {
              return true;
            }
            return false;
          });
        }
        this.sortProducts(filteredList);
        this.productList = filteredList;
        this.pageNum = 1;
        this.hasMore = this.productList.length > this.pageSize;
        console.log("过滤后的商品列表:", this.productList.length);
      } catch (error) {
        console.error("过滤商品时出错:", error);
        this.productList = [...this.allProducts].slice(0, 10);
      }
    },
    // 对商品列表进行排序
    sortProducts(list) {
      switch (this.activeTab) {
        case "sales":
          list.sort((a, b) => b.sales - a.sales);
          break;
        case "newest":
          list.sort((a, b) => new Date(b.publishTime) - new Date(a.publishTime));
          break;
        case "price":
          if (this.priceSortAsc) {
            list.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
          } else {
            list.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
          }
          break;
        default:
          list.sort((a, b) => {
            const scoreA = a.sales * 0.7 + new Date(a.publishTime).getTime() * 0.3;
            const scoreB = b.sales * 0.7 + new Date(b.publishTime).getTime() * 0.3;
            return scoreB - scoreA;
          });
      }
    },
    handleSearch() {
      this.filterProducts();
    },
    showFilter() {
      this.showFilterSection = !this.showFilterSection;
    },
    changeCategory(category) {
      this.activeCategory = category;
      this.filterProducts();
    },
    changeTab(tab) {
      if (this.activeTab === tab && tab === "price") {
        this.priceSortAsc = !this.priceSortAsc;
      } else {
        this.activeTab = tab;
        if (tab === "price") {
          this.priceSortAsc = true;
        }
      }
      this.filterProducts();
    },
    selectPrice(price) {
      this.activePrice = price;
    },
    selectType(type) {
      this.activeType = type;
    },
    resetFilter() {
      this.activePrice = "all";
      this.activeType = "all";
    },
    applyFilter() {
      this.showFilterSection = false;
      this.filterProducts();
    },
    viewDetail(item) {
      this.addToRecentlyViewed(item);
      if (this.searchKeyword) {
        this.saveSearchHistory(this.searchKeyword);
      }
      common_vendor.index.setStorageSync("currentProduct", JSON.stringify(item));
      common_vendor.index.navigateTo({
        url: "/pages/detail/detail?id=" + item.id + "&type=product"
      });
    },
    loadMoreData() {
      setTimeout(() => {
        this.hasMore = false;
      }, 1e3);
    },
    navigateTo(url) {
      if (url.includes("/pages/station/") || url.includes("/pages/device/") || url.includes("/pages/job/") || url.includes("/pages/service/") || url.includes("/pages/message/") || url.includes("/pages/my/")) {
        common_vendor.index.switchTab({
          url
        });
      } else {
        common_vendor.index.navigateTo({
          url
        });
      }
    },
    // 显示搜索面板
    showSearch() {
      this.showSearchPanel = true;
      this.loadSearchHistory();
    },
    // 关闭搜索面板
    closeSearchPanel() {
      this.showSearchPanel = false;
      if (this.searchKeyword && !this.productList.length) {
        this.searchKeyword = "";
      }
    },
    // 加载搜索历史
    loadSearchHistory() {
      const history = common_vendor.index.getStorageSync("stationSearchHistory");
      if (history) {
        this.searchHistory = JSON.parse(history);
      }
    },
    // 保存搜索历史
    saveSearchHistory(keyword) {
      if (!keyword.trim())
        return;
      let history = this.searchHistory.filter((item) => item !== keyword);
      history.unshift(keyword);
      if (history.length > 10) {
        history = history.slice(0, 10);
      }
      this.searchHistory = history;
      common_vendor.index.setStorageSync("stationSearchHistory", JSON.stringify(history));
    },
    // 清空搜索历史
    clearHistory() {
      common_vendor.index.showModal({
        title: "提示",
        content: "确定要清空搜索历史吗？",
        success: (res) => {
          if (res.confirm) {
            this.searchHistory = [];
            common_vendor.index.removeStorageSync("stationSearchHistory");
          }
        }
      });
    },
    // 使用历史关键词
    useHistoryKeyword(keyword) {
      this.searchKeyword = keyword;
      this.performLiveSearch();
    },
    // 选择搜索结果
    selectSearchResult(item) {
      this.saveSearchHistory(this.searchKeyword);
      this.closeSearchPanel();
      this.viewDetail(item);
    },
    // 高亮关键词
    highlightKeyword(text) {
      if (!this.searchKeyword)
        return text;
      const keyword = this.searchKeyword.trim();
      const parts = text.split(new RegExp(`(${keyword})`, "gi"));
      let result = "";
      parts.forEach((part) => {
        if (part.toLowerCase() === keyword.toLowerCase()) {
          result += `<span style="color: #ff5a5f;">${part}</span>`;
        } else {
          result += part;
        }
      });
      return result;
    },
    // 搜索输入变化
    onSearchInput() {
      this.performLiveSearch();
    },
    // 执行实时搜索
    performLiveSearch() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = [];
        this.isSearching = false;
        return;
      }
      this.isSearching = true;
      const keyword = this.searchKeyword.toLowerCase();
      const weightedResults = this.allProducts.map((item) => {
        let score = 0;
        if (item.title.toLowerCase().includes(keyword)) {
          score += 10;
          if (item.title.toLowerCase().startsWith(keyword)) {
            score += 5;
          }
        }
        if (item.tags && item.tags.some((tag) => tag.toLowerCase().includes(keyword))) {
          score += 5;
        }
        if (item.category.toLowerCase().includes(keyword)) {
          score += 3;
        }
        return { item, score };
      }).filter((result) => result.score > 0).sort((a, b) => b.score - a.score).map((result) => result.item);
      this.searchResults = weightedResults;
      this.isSearching = false;
    },
    // 获取分类名称
    getCategoryName(category) {
      const categoryMap = {
        "scales": "电子秤",
        "printer": "打印机",
        "shelves": "货架柜",
        "outdevice": "出库仪",
        "lightstrip": "智能灯带"
      };
      return categoryMap[category] || "其他";
    },
    // 增加最近浏览记录
    addToRecentlyViewed(product) {
      const viewed = common_vendor.index.getStorageSync("recentlyViewed");
      let recentList = viewed ? JSON.parse(viewed) : [];
      const index = recentList.findIndex((item) => item.id === product.id);
      if (index !== -1) {
        recentList.splice(index, 1);
      }
      recentList.unshift(product);
      if (recentList.length > 5) {
        recentList = recentList.slice(0, 5);
      }
      common_vendor.index.setStorageSync("recentlyViewed", JSON.stringify(recentList));
      this.recentlyViewed = recentList;
    },
    // 清除搜索关键词
    clearSearch() {
      this.searchKeyword = "";
      this.searchResults = [];
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_1,
    b: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    c: common_vendor.o(($event) => $data.showSearchPanel = true),
    d: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearchInput && $options.onSearchInput(...args)]),
    e: $data.searchKeyword,
    f: $data.searchKeyword
  }, $data.searchKeyword ? {
    g: common_assets._imports_2$1,
    h: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    i: common_vendor.o((...args) => $options.showSearch && $options.showSearch(...args)),
    j: common_assets._imports_1$1,
    k: common_vendor.o((...args) => $options.showFilter && $options.showFilter(...args)),
    l: $data.showSearchPanel
  }, $data.showSearchPanel ? common_vendor.e({
    m: common_assets._imports_1,
    n: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    o: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearchInput && $options.onSearchInput(...args)]),
    p: $data.searchKeyword,
    q: $data.searchKeyword
  }, $data.searchKeyword ? {
    r: common_assets._imports_2$1,
    s: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    t: common_vendor.o((...args) => $options.closeSearchPanel && $options.closeSearchPanel(...args)),
    v: $data.searchKeyword && $data.searchResults.length > 0
  }, $data.searchKeyword && $data.searchResults.length > 0 ? {
    w: common_vendor.f($data.searchResults, (item, index, i0) => {
      return common_vendor.e({
        a: $options.highlightKeyword(item.title),
        b: item.tags && item.tags.length > 0
      }, item.tags && item.tags.length > 0 ? {
        c: common_vendor.f(item.tags.slice(0, 2), (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        })
      } : {}, {
        d: common_vendor.t($options.getCategoryName(item.category)),
        e: index,
        f: common_vendor.o(($event) => $options.selectSearchResult(item), index)
      });
    }),
    x: common_assets._imports_3$1
  } : {}, {
    y: $data.searchKeyword && !$data.isSearching && $data.searchResults.length === 0
  }, $data.searchKeyword && !$data.isSearching && $data.searchResults.length === 0 ? {
    z: common_assets._imports_4,
    A: common_vendor.t($data.searchKeyword)
  } : {}, {
    B: $data.searchHistory.length > 0 && !$data.searchKeyword
  }, $data.searchHistory.length > 0 && !$data.searchKeyword ? {
    C: common_assets._imports_2$2,
    D: common_vendor.o((...args) => $options.clearHistory && $options.clearHistory(...args)),
    E: common_vendor.f($data.searchHistory, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index,
        c: common_vendor.o(($event) => $options.useHistoryKeyword(item), index)
      };
    }),
    F: common_assets._imports_6
  } : {}, {
    G: !$data.searchKeyword
  }, !$data.searchKeyword ? {
    H: common_vendor.f($data.hotSearches, (item, index, i0) => {
      return common_vendor.e({
        a: index < 3
      }, index < 3 ? {
        b: common_vendor.t(index + 1),
        c: common_vendor.n("rank-" + (index + 1))
      } : {}, {
        d: common_vendor.t(item),
        e: index,
        f: common_vendor.o(($event) => $options.useHistoryKeyword(item), index)
      });
    })
  } : {}) : {}, {
    I: common_assets._imports_7,
    J: $data.activeCategory === "all" ? 1 : "",
    K: common_vendor.o(($event) => $options.changeCategory("all")),
    L: common_assets._imports_8,
    M: $data.activeCategory === "scales" ? 1 : "",
    N: common_vendor.o(($event) => $options.changeCategory("scales")),
    O: common_assets._imports_9,
    P: $data.activeCategory === "printer" ? 1 : "",
    Q: common_vendor.o(($event) => $options.changeCategory("printer")),
    R: common_assets._imports_10,
    S: $data.activeCategory === "shelves" ? 1 : "",
    T: common_vendor.o(($event) => $options.changeCategory("shelves")),
    U: common_assets._imports_7,
    V: $data.activeCategory === "outdevice" ? 1 : "",
    W: common_vendor.o(($event) => $options.changeCategory("outdevice")),
    X: common_assets._imports_7,
    Y: $data.activeCategory === "lightstrip" ? 1 : "",
    Z: common_vendor.o(($event) => $options.changeCategory("lightstrip")),
    aa: $data.activeTab === "all" ? 1 : "",
    ab: common_vendor.o(($event) => $options.changeTab("all")),
    ac: $data.activeTab === "sales" ? 1 : "",
    ad: common_vendor.o(($event) => $options.changeTab("sales")),
    ae: $data.activeTab === "newest" ? 1 : "",
    af: common_vendor.o(($event) => $options.changeTab("newest")),
    ag: $data.activeTab === "price"
  }, $data.activeTab === "price" ? {
    ah: common_vendor.t($data.priceSortAsc ? "↑" : "↓")
  } : {}, {
    ai: $data.activeTab === "price" ? 1 : "",
    aj: common_vendor.o(($event) => $options.changeTab("price")),
    ak: $data.showFilterSection
  }, $data.showFilterSection ? {
    al: $data.activePrice === "all" ? 1 : "",
    am: common_vendor.o(($event) => $options.selectPrice("all")),
    an: $data.activePrice === "low" ? 1 : "",
    ao: common_vendor.o(($event) => $options.selectPrice("low")),
    ap: $data.activePrice === "medium" ? 1 : "",
    aq: common_vendor.o(($event) => $options.selectPrice("medium")),
    ar: $data.activePrice === "high" ? 1 : "",
    as: common_vendor.o(($event) => $options.selectPrice("high")),
    at: $data.activeType === "all" ? 1 : "",
    av: common_vendor.o(($event) => $options.selectType("all")),
    aw: $data.activeType === "new" ? 1 : "",
    ax: common_vendor.o(($event) => $options.selectType("new")),
    ay: $data.activeType === "used" ? 1 : "",
    az: common_vendor.o(($event) => $options.selectType("used")),
    aA: $data.activeType === "refurbished" ? 1 : "",
    aB: common_vendor.o(($event) => $options.selectType("refurbished")),
    aC: common_vendor.o((...args) => $options.resetFilter && $options.resetFilter(...args)),
    aD: common_vendor.o((...args) => $options.applyFilter && $options.applyFilter(...args))
  } : {}, {
    aE: common_vendor.f($data.productList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        c: common_vendor.t(item.price),
        d: common_vendor.t(item.sales),
        e: index,
        f: common_vendor.o(($event) => $options.viewDetail(item), index)
      };
    }),
    aF: $data.hasMore
  }, $data.hasMore ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
