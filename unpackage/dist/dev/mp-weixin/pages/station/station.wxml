<view class="container"><view class="search-bar"><view class="search-input-box" bindtap="{{i}}"><image class="search-icon" src="{{a}}" mode="aspectFit"></image><input type="text" placeholder="搜索驿站用品" confirm-type="search" bindconfirm="{{b}}" bindfocus="{{c}}" bindinput="{{d}}" value="{{e}}"/><view wx:if="{{f}}" class="clear-btn" catchtap="{{h}}"><image src="{{g}}" mode="aspectFit"></image></view></view><view class="filter-btn" bindtap="{{k}}"><image src="{{j}}" mode="aspectFit"></image></view></view><view wx:if="{{l}}" class="search-panel"><view class="search-panel-header"><view class="search-input-container"><image class="search-icon" src="{{m}}" mode="aspectFit"></image><input type="text" placeholder="搜索驿站用品" confirm-type="search" focus bindconfirm="{{n}}" bindinput="{{o}}" value="{{p}}"/><view wx:if="{{q}}" class="clear-btn" catchtap="{{s}}"><image src="{{r}}" mode="aspectFit"></image></view></view><view class="cancel-btn" bindtap="{{t}}"><text>取消</text></view></view><scroll-view class="search-panel-content" scroll-y><view wx:if="{{v}}" class="search-results"><view class="section-title">搜索结果</view><view class="result-list"><view wx:for="{{w}}" wx:for-item="item" wx:key="e" class="result-item" bindtap="{{item.f}}"><image class="result-icon" src="{{x}}" mode="aspectFit"></image><view class="result-info"><rich-text class="result-title" nodes="{{item.a}}"></rich-text><view wx:if="{{item.b}}" class="result-tags"><text wx:for="{{item.c}}" wx:for-item="tag" wx:key="b" class="result-tag">{{tag.a}}</text></view></view><view class="result-category">{{item.d}}</view></view></view></view><view wx:if="{{y}}" class="no-results"><image class="no-result-icon" src="{{z}}" mode="aspectFit"></image><text class="no-result-text">未找到与"{{A}}"相关的商品</text><text class="no-result-tip">换个关键词试试吧</text></view><view wx:if="{{B}}" class="search-history"><view class="history-header"><text class="section-title">搜索历史</text><view class="clear-history" bindtap="{{D}}"><image src="{{C}}" mode="aspectFit"></image><text>清空</text></view></view><view class="history-list"><view wx:for="{{E}}" wx:for-item="item" wx:key="b" class="history-item" bindtap="{{item.c}}"><image class="history-icon" src="{{F}}" mode="aspectFit"></image><text>{{item.a}}</text></view></view></view><view wx:if="{{G}}" class="hot-searches"><view class="section-title">热门搜索</view><view class="hot-list"><view wx:for="{{H}}" wx:for-item="item" wx:key="e" class="hot-item" bindtap="{{item.f}}"><view wx:if="{{item.a}}" class="{{['hot-rank', item.c]}}">{{item.b}}</view><text>{{item.d}}</text></view></view></view></scroll-view></view><view class="category-nav"><view class="{{['category-item', J && 'active']}}" bindtap="{{K}}"><image src="{{I}}" mode="aspectFit"></image><text>全部</text></view><view class="{{['category-item', M && 'active']}}" bindtap="{{N}}"><image src="{{L}}" mode="aspectFit"></image><text>电子秤</text></view><view class="{{['category-item', P && 'active']}}" bindtap="{{Q}}"><image src="{{O}}" mode="aspectFit"></image><text>打印机</text></view><view class="{{['category-item', S && 'active']}}" bindtap="{{T}}"><image src="{{R}}" mode="aspectFit"></image><text>货架柜</text></view><view class="{{['category-item', V && 'active']}}" bindtap="{{W}}"><image src="{{U}}" mode="aspectFit"></image><text>出库仪</text></view><view class="{{['category-item', Y && 'active']}}" bindtap="{{Z}}"><image src="{{X}}" mode="aspectFit"></image><text>智能灯带</text></view></view><view class="quick-filter"><view class="{{['filter-tab', aa && 'active']}}" bindtap="{{ab}}">综合</view><view class="{{['filter-tab', ac && 'active']}}" bindtap="{{ad}}">销量</view><view class="{{['filter-tab', ae && 'active']}}" bindtap="{{af}}">新品</view><view class="{{['filter-tab', ai && 'active']}}" bindtap="{{aj}}">价格 <text wx:if="{{ag}}" class="sort-arrow">{{ah}}</text></view></view><view wx:if="{{ak}}" class="filter-section"><view class="filter-row"><view class="filter-title">价格区间</view><view class="filter-options"><view class="{{['filter-option', al && 'active']}}" bindtap="{{am}}">全部</view><view class="{{['filter-option', an && 'active']}}" bindtap="{{ao}}">0-500元</view><view class="{{['filter-option', ap && 'active']}}" bindtap="{{aq}}">500-2000元</view><view class="{{['filter-option', ar && 'active']}}" bindtap="{{as}}">2000元以上</view></view></view><view class="filter-row"><view class="filter-title">商品类型</view><view class="filter-options"><view class="{{['filter-option', at && 'active']}}" bindtap="{{av}}">全部</view><view class="{{['filter-option', aw && 'active']}}" bindtap="{{ax}}">全新</view><view class="{{['filter-option', ay && 'active']}}" bindtap="{{az}}">二手</view><view class="{{['filter-option', aA && 'active']}}" bindtap="{{aB}}">翻新</view></view></view><view class="filter-actions"><view class="reset-btn" bindtap="{{aC}}">重置</view><view class="confirm-btn" bindtap="{{aD}}">确认</view></view></view><view class="product-grid"><view wx:for="{{aE}}" wx:for-item="item" wx:key="e" class="product-card" bindtap="{{item.f}}"><view class="product-image-placeholder"><text>商品图片</text></view><view class="product-info"><view class="product-title">{{item.a}}</view><view class="product-tags"><text wx:for="{{item.b}}" wx:for-item="tag" wx:key="b" class="product-tag">{{tag.a}}</text></view><view class="product-price">¥<text class="price-value">{{item.c}}</text></view><view class="product-sales">已售{{item.d}}件</view></view></view></view><view wx:if="{{aF}}" class="loading-more"><text>正在加载更多...</text></view><view wx:else class="no-more"><text>没有更多数据了</text></view></view>