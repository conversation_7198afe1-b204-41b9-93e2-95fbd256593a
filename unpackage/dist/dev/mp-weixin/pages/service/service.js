"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      showFilterSection: false,
      activeShift: "all",
      activeArea: "all",
      activeSalary: "all",
      activeTime: "all",
      hasMore: true,
      pageNum: 1,
      pageSize: 10,
      serviceList: [
        {
          id: 1,
          title: "朝阳区劲松地铁站附近顶班",
          price: "180元/天",
          time: "全天班（8小时）",
          address: "朝阳区劲松地铁站北口200米",
          tags: ["快递员", "中通快递", "日均100件"],
          publishTime: "1小时前",
          contactInfo: {
            name: "王师傅",
            phone: "13312345678"
          }
        },
        {
          id: 2,
          title: "海淀区中关村顶班一个月",
          price: "210元/天",
          time: "全天班（9小时）",
          address: "海淀区中关村大街28号",
          tags: ["快递员", "顺丰速运", "日均80件"],
          publishTime: "3小时前",
          contactInfo: {
            name: "李师傅",
            phone: "13687654321"
          }
        },
        {
          id: 3,
          title: "丰台区角门西地铁站附近驿站临时工",
          price: "160元/天",
          time: "上午班（5小时）",
          address: "丰台区角门西路甲1号",
          tags: ["驿站业务员", "京东物流", "日均200件"],
          publishTime: "5小时前",
          contactInfo: {
            name: "张经理",
            phone: "13898765432"
          }
        },
        {
          id: 4,
          title: "西城区西单商圈快递员急需顶班",
          price: "230元/天",
          time: "全天班（10小时）",
          address: "西城区西单北大街155号",
          tags: ["快递员", "百世快递", "日均150件"],
          publishTime: "昨天",
          contactInfo: {
            name: "刘师傅",
            phone: "13765432109"
          }
        },
        {
          id: 5,
          title: "东城区东直门驿站急招分拣员",
          price: "170元/天",
          time: "晚班（4小时）",
          address: "东城区东直门外大街42号",
          tags: ["分拣员", "圆通速递", "日均300件"],
          publishTime: "前天",
          contactInfo: {
            name: "赵经理",
            phone: "13456789012"
          }
        }
      ]
    };
  },
  onLoad() {
    this.loadStoredData();
  },
  onShow() {
    this.loadStoredData();
  },
  onReachBottom() {
    if (this.hasMore) {
      this.loadMoreData();
    }
  },
  methods: {
    handleSearch() {
      common_vendor.index.showToast({
        title: "搜索：" + this.searchKeyword,
        icon: "none"
      });
    },
    showFilter() {
      this.showFilterSection = !this.showFilterSection;
    },
    selectShift(shift) {
      this.activeShift = shift;
      common_vendor.index.showToast({
        title: "已选择：" + shift,
        icon: "none"
      });
    },
    selectArea(area) {
      this.activeArea = area;
    },
    selectSalary(salary) {
      this.activeSalary = salary;
    },
    selectTime(time) {
      this.activeTime = time;
    },
    resetFilter() {
      this.activeArea = "all";
      this.activeSalary = "all";
      this.activeTime = "all";
    },
    applyFilter() {
      this.showFilterSection = false;
      common_vendor.index.showToast({
        title: "筛选条件已应用",
        icon: "none"
      });
    },
    viewDetail(item) {
      common_vendor.index.navigateTo({
        url: "/pages/detail/detail?id=" + item.id + "&type=service"
      });
    },
    contactService(item) {
      common_vendor.index.makePhoneCall({
        phoneNumber: item.contactInfo.phone
      });
    },
    loadMoreData() {
      setTimeout(() => {
        if (this.pageNum >= 3) {
          this.hasMore = false;
        } else {
          this.pageNum++;
        }
      }, 1e3);
    },
    // 加载本地存储的数据
    loadStoredData() {
      const storedData = common_vendor.index.getStorageSync("serviceList");
      if (storedData && storedData.length > 0) {
        this.serviceList = storedData;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_1,
    b: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    c: $data.searchKeyword,
    d: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    e: common_assets._imports_1$1,
    f: common_vendor.o((...args) => $options.showFilter && $options.showFilter(...args)),
    g: $data.activeShift === "all" ? 1 : "",
    h: common_vendor.o(($event) => $options.selectShift("all")),
    i: $data.activeShift === "morning" ? 1 : "",
    j: common_vendor.o(($event) => $options.selectShift("morning")),
    k: $data.activeShift === "afternoon" ? 1 : "",
    l: common_vendor.o(($event) => $options.selectShift("afternoon")),
    m: $data.activeShift === "evening" ? 1 : "",
    n: common_vendor.o(($event) => $options.selectShift("evening")),
    o: $data.activeShift === "fullday" ? 1 : "",
    p: common_vendor.o(($event) => $options.selectShift("fullday")),
    q: $data.activeShift === "urgent" ? 1 : "",
    r: common_vendor.o(($event) => $options.selectShift("urgent")),
    s: $data.showFilterSection
  }, $data.showFilterSection ? {
    t: $data.activeArea === "all" ? 1 : "",
    v: common_vendor.o(($event) => $options.selectArea("all")),
    w: $data.activeArea === "haidian" ? 1 : "",
    x: common_vendor.o(($event) => $options.selectArea("haidian")),
    y: $data.activeArea === "chaoyang" ? 1 : "",
    z: common_vendor.o(($event) => $options.selectArea("chaoyang")),
    A: $data.activeArea === "dongcheng" ? 1 : "",
    B: common_vendor.o(($event) => $options.selectArea("dongcheng")),
    C: $data.activeArea === "xicheng" ? 1 : "",
    D: common_vendor.o(($event) => $options.selectArea("xicheng")),
    E: $data.activeSalary === "all" ? 1 : "",
    F: common_vendor.o(($event) => $options.selectSalary("all")),
    G: $data.activeSalary === "low" ? 1 : "",
    H: common_vendor.o(($event) => $options.selectSalary("low")),
    I: $data.activeSalary === "medium" ? 1 : "",
    J: common_vendor.o(($event) => $options.selectSalary("medium")),
    K: $data.activeSalary === "high" ? 1 : "",
    L: common_vendor.o(($event) => $options.selectSalary("high")),
    M: $data.activeTime === "all" ? 1 : "",
    N: common_vendor.o(($event) => $options.selectTime("all")),
    O: $data.activeTime === "morning" ? 1 : "",
    P: common_vendor.o(($event) => $options.selectTime("morning")),
    Q: $data.activeTime === "afternoon" ? 1 : "",
    R: common_vendor.o(($event) => $options.selectTime("afternoon")),
    S: $data.activeTime === "evening" ? 1 : "",
    T: common_vendor.o(($event) => $options.selectTime("evening")),
    U: $data.activeTime === "fullday" ? 1 : "",
    V: common_vendor.o(($event) => $options.selectTime("fullday")),
    W: common_vendor.o((...args) => $options.resetFilter && $options.resetFilter(...args)),
    X: common_vendor.o((...args) => $options.applyFilter && $options.applyFilter(...args))
  } : {}, {
    Y: common_vendor.f($data.serviceList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.title),
        b: common_vendor.t(item.price),
        c: common_vendor.t(item.time),
        d: common_vendor.t(item.address),
        e: common_vendor.f(item.tags, (tag, tagIndex, i1) => {
          return {
            a: common_vendor.t(tag),
            b: tagIndex
          };
        }),
        f: common_vendor.o(($event) => $options.contactService(item), index),
        g: common_vendor.t(item.publishTime),
        h: index,
        i: common_vendor.o(($event) => $options.viewDetail(item), index)
      };
    }),
    Z: common_assets._imports_5$1,
    aa: common_assets._imports_1$2,
    ab: common_assets._imports_4$1,
    ac: $data.hasMore
  }, $data.hasMore ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
