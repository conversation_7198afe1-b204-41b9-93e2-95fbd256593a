
.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		position: relative;
}
.search-bar {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
}
.search-input-box {
		flex: 1;
		background-color: #ffffff;
		border-radius: 60rpx;
		padding: 15rpx 30rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.search-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 15rpx;
}
.filter-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.filter-btn image {
		width: 40rpx;
		height: 40rpx;
}
	
	/* 快捷分类标签 */
.quick-tags {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx 0;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.tags-scroll-view {
		white-space: nowrap;
		padding: 0 20rpx;
}
.tag-item {
		display: inline-block;
		padding: 10rpx 30rpx;
		margin-right: 15rpx;
		font-size: 26rpx;
		color: #666666;
		background-color: #f5f5f5;
		border-radius: 30rpx;
}
.tag-item.active {
		background-color: #ff5a5f;
		color: #ffffff;
}
.filter-section {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.filter-row {
		margin-bottom: 20rpx;
}
.filter-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 15rpx;
}
.filter-options {
		display: flex;
		flex-wrap: wrap;
}
.filter-option {
		padding: 10rpx 20rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		margin-right: 15rpx;
		margin-bottom: 15rpx;
		font-size: 24rpx;
		color: #666666;
}
.filter-option.active {
		background-color: #ff5a5f;
		color: #ffffff;
}
.filter-actions {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
}
.reset-btn, .confirm-btn {
		width: 45%;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 40rpx;
		font-size: 28rpx;
}
.reset-btn {
		background-color: #f5f5f5;
		color: #666666;
}
.confirm-btn {
		background-color: #ff5a5f;
		color: #ffffff;
}
.service-list {
		margin-top: 10rpx;
}
.service-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 25rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.service-top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
}
.service-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		max-width: 70%;
}
.service-price {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff5a5f;
}
.service-time, .service-address {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
		font-size: 26rpx;
		color: #666666;
}
.service-time image, .service-address image {
		width: 28rpx;
		height: 28rpx;
		margin-right: 10rpx;
}
.service-tags {
		display: flex;
		flex-wrap: wrap;
		margin: 15rpx 0;
}
.service-tag {
		padding: 6rpx 16rpx;
		background-color: #f8f8f8;
		border-radius: 6rpx;
		font-size: 22rpx;
		color: #666666;
		margin-right: 15rpx;
		margin-bottom: 10rpx;
}
.service-bottom {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 15rpx;
		padding-top: 15rpx;
		border-top: 1rpx solid #f0f0f0;
}
.service-contact {
		display: flex;
		align-items: center;
		background-color: #ff5a5f;
		color: #ffffff;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		font-size: 24rpx;
}
.service-contact image {
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
}
.service-time-ago {
		font-size: 24rpx;
		color: #999999;
}
.loading-more, .no-more {
		text-align: center;
		padding: 30rpx 0;
}
.loading-more text, .no-more text {
		font-size: 26rpx;
		color: #999999;
}
