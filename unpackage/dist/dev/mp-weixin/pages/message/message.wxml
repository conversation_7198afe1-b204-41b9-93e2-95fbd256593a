<view class="container"><view class="header"><text class="title">消息中心</text></view><view class="message-types"><view wx:for="{{a}}" wx:for-item="tab" wx:key="b" class="{{['type-item', tab.c]}}" bindtap="{{tab.d}}"><text>{{tab.a}}</text></view></view><scroll-view scroll-y class="message-list" refresher-enabled="{{true}}" refresher-triggered="{{f}}" bindrefresherrefresh="{{g}}" show-scrollbar="false"><view wx:if="{{b}}" class="empty-tip"><image src="{{c}}" mode="aspectFit"></image><text>暂无消息</text></view><view wx:else><view wx:for="{{d}}" wx:for-item="message" wx:key="g" class="{{['message-item', message.h]}}" bindtap="{{message.i}}"><view class="message-avatar"><image src="{{message.a}}" mode="aspectFill"></image></view><view class="message-content"><view class="message-header"><text class="message-title">{{message.b}}</text><text class="message-time">{{message.c}}</text></view><view class="message-desc">{{message.d}}</view></view><view wx:if="{{message.e}}" class="unread-badge">{{message.f}}</view></view></view><view wx:if="{{e}}" class="no-more"><text>没有更多消息了</text></view></scroll-view></view>