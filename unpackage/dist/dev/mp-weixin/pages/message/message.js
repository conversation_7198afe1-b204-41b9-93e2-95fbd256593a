"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      tabs: [
        { name: "全部", type: "all" },
        { name: "系统通知", type: "system" },
        { name: "官方客服", type: "service" }
      ],
      activeTab: 0,
      isRefreshing: false,
      messages: [
        {
          id: 1,
          type: "system",
          title: "系统通知",
          content: "欢迎使用驿站帮Pro，希望能帮助您更好地管理驿站业务！",
          time: Date.now() - 36e5,
          // 1小时前
          unread: 1
        },
        {
          id: 2,
          type: "service",
          title: "官方客服",
          content: "您好，有任何使用问题可以随时咨询我们的客服团队。",
          time: Date.now() - 864e5,
          // 1天前
          unread: 2
        },
        {
          id: 3,
          type: "system",
          title: "认证提醒",
          content: "您的驿站身份认证已通过审核，现在可以使用更多功能啦！",
          time: Date.now() - 1728e5,
          // 2天前
          unread: 0
        },
        {
          id: 4,
          type: "service",
          title: "订单通知",
          content: "您有一个新的订单已经确认，请及时处理。",
          time: Date.now() - 2592e5,
          // 3天前
          unread: 0
        },
        {
          id: 5,
          type: "system",
          title: "系统更新",
          content: "系统已更新到最新版本，新增了多项功能和优化体验。",
          time: Date.now() - 432e6,
          // 5天前
          unread: 0
        }
      ]
    };
  },
  computed: {
    filteredMessages() {
      if (this.activeTab === 0) {
        return this.messages;
      } else {
        const type = this.tabs[this.activeTab].type;
        return this.messages.filter((msg) => msg.type === type);
      }
    }
  },
  methods: {
    // 切换标签
    switchTab(index) {
      this.activeTab = index;
    },
    // 下拉刷新
    onRefresh() {
      this.isRefreshing = true;
      setTimeout(() => {
        const types = ["system", "service"];
        const titles = {
          system: ["系统通知", "安全提醒", "更新提示"],
          service: ["官方客服", "订单通知", "售后服务"]
        };
        const contents = [
          "您的账号安全等级较低，建议设置更复杂的密码。",
          "您有一个新的通知，请注意查看。",
          "系统将于今晚22:00进行例行维护，期间服务可能短暂不可用。"
        ];
        const type = types[Math.floor(Math.random() * types.length)];
        const title = titles[type][Math.floor(Math.random() * titles[type].length)];
        const content = contents[Math.floor(Math.random() * contents.length)];
        this.messages.unshift({
          id: this.messages.length + 1,
          type,
          title,
          content,
          time: Date.now(),
          unread: 1
        });
        this.isRefreshing = false;
      }, 1e3);
    },
    // 获取消息图标
    getMessageIcon(type) {
      switch (type) {
        case "system":
          return "/static/icons/system-notice.png";
        case "service":
          return "/static/icons/service.png";
        default:
          return "/static/icons/system-notice.png";
      }
    },
    // 格式化时间
    formatTime(timestamp) {
      const now = Date.now();
      const diff = now - timestamp;
      if (diff < 36e5) {
        return "刚刚";
      } else if (diff < 864e5) {
        return "今天 " + new Date(timestamp).toTimeString().substr(0, 5);
      } else if (diff < 1728e5) {
        return "昨天 " + new Date(timestamp).toTimeString().substr(0, 5);
      } else if (diff < 6048e5) {
        return Math.floor(diff / 864e5) + "天前";
      } else {
        const date = new Date(timestamp);
        return `${date.getMonth() + 1}月${date.getDate()}日`;
      }
    },
    // 查看消息详情
    viewMessageDetail(message) {
      if (message.unread > 0) {
        const index = this.messages.findIndex((m) => m.id === message.id);
        if (index !== -1) {
          this.messages[index].unread = 0;
        }
      }
      common_vendor.index.navigateTo({
        url: `/pages/message/detail?id=${message.id}&type=${message.type}`
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.tabs, (tab, index, i0) => {
      return {
        a: common_vendor.t(tab.name),
        b: index,
        c: common_vendor.n($data.activeTab === index ? "active" : ""),
        d: common_vendor.o(($event) => $options.switchTab(index), index)
      };
    }),
    b: $options.filteredMessages.length === 0
  }, $options.filteredMessages.length === 0 ? {
    c: common_assets._imports_1$3
  } : {
    d: common_vendor.f($options.filteredMessages, (message, index, i0) => {
      return common_vendor.e({
        a: $options.getMessageIcon(message.type),
        b: common_vendor.t(message.title),
        c: common_vendor.t($options.formatTime(message.time)),
        d: common_vendor.t(message.content),
        e: message.unread > 0
      }, message.unread > 0 ? {
        f: common_vendor.t(message.unread)
      } : {}, {
        g: index,
        h: common_vendor.n(message.type),
        i: common_vendor.o(($event) => $options.viewMessageDetail(message), index)
      });
    })
  }, {
    e: $options.filteredMessages.length > 0
  }, $options.filteredMessages.length > 0 ? {} : {}, {
    f: $data.isRefreshing,
    g: common_vendor.o((...args) => $options.onRefresh && $options.onRefresh(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
