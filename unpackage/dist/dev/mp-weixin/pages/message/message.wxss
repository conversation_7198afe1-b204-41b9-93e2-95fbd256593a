
.container {
		background-color: #f5f5f5;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
}
	
	/* 头部样式 */
.header {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 90rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
		position: -webkit-sticky;
		position: sticky;
		top: 0;
		z-index: 10;
}
.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
}
	
	/* 分类标签样式 */
.message-types {
		display: flex;
		background-color: #ffffff;
		margin-bottom: 20rpx;
		overflow: hidden;
		height: 90rpx;
		position: -webkit-sticky;
		position: sticky;
		top: 90rpx;
		z-index: 9;
}
.type-item {
		flex: 1;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666666;
		position: relative;
}
.type-item.active {
		color: #ff5a5f;
		font-weight: bold;
}
.type-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 4rpx;
		background-color: #ff5a5f;
}
	
	/* 消息列表样式 */
.message-list {
		flex: 1;
		padding: 0 20rpx;
}
	
	/* 空消息提示 */
.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 200rpx;
}
.empty-tip image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
}
.empty-tip text {
		font-size: 28rpx;
		color: #999999;
}
	
	/* 消息项样式 */
.message-item {
		display: flex;
		align-items: flex-start;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		position: relative;
		/* 提高渲染性能 */
		transform: translateZ(0);
}
.message-avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-right: 20rpx;
		overflow: hidden;
		background-color: #f5f5f5;
}
.message-avatar image {
		width: 100%;
		height: 100%;
}
.message-content {
		flex: 1;
}
.message-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
}
.message-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
}
.message-time {
		font-size: 24rpx;
		color: #999999;
}
.message-desc {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.5;
}
	
	/* 未读标记 */
.unread-badge {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		min-width: 36rpx;
		height: 36rpx;
		background-color: #ff5a5f;
		border-radius: 18rpx;
		color: #ffffff;
		font-size: 24rpx;
		text-align: center;
		line-height: 36rpx;
		padding: 0 8rpx;
}
	
	/* 系统消息样式 */
.message-item.system .message-avatar {
		background-color: #FFF2F2;
}
	
	/* 客服消息样式 */
.message-item.service .message-avatar {
		background-color: #F2F7FF;
}
	
	/* 底部加载提示 */
.no-more {
		text-align: center;
		padding: 30rpx 0;
}
.no-more text {
		font-size: 26rpx;
		color: #999999;
}
