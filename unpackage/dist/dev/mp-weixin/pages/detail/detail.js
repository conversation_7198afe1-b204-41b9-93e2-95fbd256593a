"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      id: 0,
      detailType: "station",
      isCollected: false,
      loading: true,
      error: null,
      stationDetail: {},
      jobDetail: {},
      productDetail: {},
      deviceDetail: {},
      serviceDetail: {},
      isEmpty: false,
      debugInfo: {}
    };
  },
  onLoad(options) {
    console.log("详情页载入参数:", options);
    if (options.id) {
      this.id = options.id;
      this.id = this.id.toString();
    } else {
      this.error = "未提供有效的ID参数";
      this.loading = false;
      return;
    }
    if (options.type) {
      this.detailType = options.type;
    } else {
      this.error = "未提供详情类型参数";
      this.loading = false;
      return;
    }
    console.log("开始加载详情数据, ID:", this.id, "类型:", this.detailType);
    setTimeout(() => {
      this.loadDetailData();
    }, 300);
    this.checkCollectStatus();
  },
  computed: {
    // 根据详情类型返回对应的详情数据
    currentDetail() {
      switch (this.detailType) {
        case "station":
          return this.stationDetail;
        case "job":
          return this.jobDetail;
        case "device":
          return this.deviceDetail;
        case "product":
          return this.productDetail;
        case "service":
          return this.serviceDetail;
        default:
          return {};
      }
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    },
    // 重试加载
    retryLoading() {
      this.error = null;
      this.loading = true;
      this.isEmpty = false;
      this.loadDetailData();
    },
    // 检查数据是否为空
    checkIfEmpty() {
      let detail = this.currentDetail;
      this.isEmpty = !detail || Object.keys(detail).length === 0;
      console.log(`检查${this.detailType}数据是否为空:`, this.isEmpty);
      if (this.isEmpty) {
        console.error("详情数据为空，请检查数据加载是否正确");
      }
      return this.isEmpty;
    },
    // 加载详情数据
    async loadDetailData() {
      this.loading = true;
      this.error = null;
      try {
        console.log("开始获取详情数据...");
        if (this.detailType === "station") {
          await this.fetchStationDetail();
        } else if (this.detailType === "job") {
          await this.fetchJobDetail();
        } else if (this.detailType === "product") {
          await this.fetchProductDetail();
        } else if (this.detailType === "device") {
          await this.fetchDeviceDetail();
        } else if (this.detailType === "service") {
          await this.fetchServiceDetail();
        } else {
          throw new Error("未知的详情类型: " + this.detailType);
        }
        const isEmpty = this.checkIfEmpty();
        if (isEmpty) {
          this.useStaticData();
        }
        console.log("详情数据加载完成");
      } catch (error) {
        console.error("加载详情数据失败", error);
        this.error = "加载详情数据失败，请重试: " + (error.message || "未知错误");
        this.useStaticData();
        common_vendor.index.showToast({
          title: "加载失败，已使用默认数据",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 使用静态数据
    useStaticData() {
      if (this.detailType === "station") {
        this.useStaticStationData();
      } else if (this.detailType === "job") {
        this.useStaticJobData();
      } else if (this.detailType === "product") {
        this.useStaticProductData();
      } else if (this.detailType === "device") {
        this.useStaticDeviceData();
      } else if (this.detailType === "service") {
        this.useStaticServiceData();
      }
      this.isEmpty = false;
    },
    // 获取驿站详情
    async fetchStationDetail() {
      var _a, _b, _c;
      try {
        const transferList = common_vendor.index.getStorageSync("transferList") || [];
        const localStation = transferList.find((item) => item.id.toString() === this.id.toString());
        if (localStation) {
          this.stationDetail = {
            id: localStation.id,
            title: localStation.title,
            price: localStation.price,
            priceUnit: localStation.priceUnit || "万元",
            tags: localStation.tags || [],
            publishTime: localStation.publishTime || this.formatDateTime(/* @__PURE__ */ new Date()),
            viewCount: localStation.viewCount || 0,
            // 确保图片数据是数组格式
            images: Array.isArray(localStation.images) ? localStation.images : localStation.image ? [localStation.image] : ["/static/images/station-default.jpg"],
            address: localStation.address || "",
            area: localStation.space || "0",
            monthlyIncome: localStation.monthlyIncome || "0",
            dailyPackage: localStation.package || "0",
            reason: localStation.reason || "未提供",
            description: localStation.description || localStation.advantage || "未提供详细描述",
            contact: {
              name: ((_a = localStation.contactInfo) == null ? void 0 : _a.name) || "未提供",
              phone: ((_b = localStation.contactInfo) == null ? void 0 : _b.phone) || "未提供",
              wechat: ((_c = localStation.contactInfo) == null ? void 0 : _c.wechat) || "未提供"
            }
          };
          return;
        }
        const that = this;
        common_vendor.index.request({
          url: "/api/station/detail",
          method: "GET",
          data: {
            id: this.id
          },
          success: (res) => {
            var _a2;
            if (res.data.code === 200 && res.data.data) {
              that.stationDetail = {
                ...res.data.data,
                // 兼容原有数据结构
                contact: {
                  name: res.data.data.contact_name || "未提供",
                  phone: res.data.data.contact_phone || "未提供",
                  wechat: ((_a2 = res.data.data.additional_fields) == null ? void 0 : _a2.wechat) || "未提供"
                },
                priceUnit: res.data.data.price_unit || "元",
                publishTime: that.formatDateTime(res.data.data.publish_time),
                viewCount: res.data.data.views || 0,
                area: res.data.data.area || "0",
                monthlyIncome: res.data.data.monthly_income || "0",
                dailyPackage: res.data.data.daily_package || "0",
                reason: res.data.data.reason || "未提供",
                // 确保API返回的图片数据也是数组格式
                images: Array.isArray(res.data.data.images) ? res.data.data.images : ["/static/images/station-default.jpg"]
              };
            } else {
              console.error("获取驿站详情失败:", res.data.msg);
              that.useStaticStationData();
            }
          },
          fail: (err) => {
            console.error("请求驿站详情失败:", err);
            that.useStaticStationData();
          }
        });
      } catch (error) {
        console.error("获取驿站详情失败", error);
        this.useStaticStationData();
      }
    },
    // 使用静态驿站数据（作为备用）
    useStaticStationData() {
      this.stationDetail = {
        title: "城北社区驿站转让",
        price: "45000",
        priceUnit: "元",
        tags: ["社区驿站", "有固定客源", "设备齐全"],
        publishTime: "2023-04-07 09:32",
        viewCount: 256,
        images: [
          "/static/images/station1.jpg",
          "/static/images/station2.jpg",
          "/static/images/station3.jpg"
        ],
        address: "海淀区北太平庄街道",
        area: "60",
        monthlyIncome: "25000",
        dailyPackage: "300",
        reason: "个人原因无暇经营，转手给有意向的伙伴",
        description: "本驿站位于北太平庄小区门口，周边居民区密集，客源稳定。驿站面积60平米，每日快递包裹300件左右，月均营业额2.5万元。现因个人原因无暇经营，寻求有意向的伙伴接手。驿站内设备齐全，可直接经营，接手即可盈利。价格可小刀，诚心人联系！",
        contact: {
          name: "王先生",
          phone: "13812345678",
          wechat: "wx123456"
        }
      };
    },
    // 获取职位详情
    async fetchJobDetail() {
      var _a, _b, _c;
      console.log("开始获取工作详情，ID:", this.id);
      try {
        const jobList = common_vendor.index.getStorageSync("jobList") || [];
        const localJob = jobList.find((job) => job.id == this.id);
        if (localJob) {
          console.log("从本地存储找到工作详情:", localJob);
          this.jobDetail = {
            id: localJob.id,
            title: localJob.title,
            salary: localJob.salary,
            company: localJob.company,
            address: localJob.address,
            tags: localJob.tags,
            publishTime: localJob.publishTime || "刚刚",
            viewCount: "100+",
            // 默认值
            experience: localJob.experience,
            education: localJob.education || "学历不限",
            ageRequirement: localJob.ageRequirement || "不限",
            jobType: localJob.jobType,
            workTime: localJob.workTime,
            jobDescription: localJob.jobDescription || localJob.responsibilities || "",
            benefits: localJob.benefits || "",
            contact: {
              name: ((_a = localJob.contactInfo) == null ? void 0 : _a.name) || "",
              phone: ((_b = localJob.contactInfo) == null ? void 0 : _b.phone) || "",
              wechat: ((_c = localJob.contactInfo) == null ? void 0 : _c.wechat) || ""
            }
          };
          return;
        }
      } catch (e) {
        console.error("获取本地工作数据失败:", e);
      }
      console.log("本地未找到数据，尝试从API获取");
      const that = this;
      common_vendor.index.request({
        url: "/api/job/detail",
        // 修改为相对路径
        method: "GET",
        data: {
          id: this.id
        },
        success: (res) => {
          console.log("API响应:", res.data);
          if (res.data.code === 200) {
            that.jobDetail = res.data.data;
          } else {
            console.error("获取工作详情失败:", res.data.msg);
            that.useStaticJobData();
          }
        },
        fail: (err) => {
          console.error("请求工作详情失败:", err);
          that.useStaticJobData();
        }
      });
    },
    // 使用静态工作数据（作为备用）
    useStaticJobData() {
      console.log("使用静态工作数据");
      this.jobDetail = {
        id: this.id,
        title: "招聘驿站理货员",
        salary: "3000-5000元/月",
        company: "顺丰驿站",
        address: "上海市浦东新区张江高科技园区",
        tags: ["全职", "工作时间灵活", "有福利"],
        publishTime: "2小时前",
        viewCount: "253",
        experience: "经验不限",
        education: "学历不限",
        ageRequirement: "18-45岁",
        jobType: "全职",
        workTime: "9:00-18:00，单休",
        jobDescription: "负责驿站日常收发快递、整理货架、清点库存等工作。\n要求认真负责，有耐心，能吃苦耐劳。",
        benefits: "五险一金、节日福利、团队旅游",
        contact: {
          name: "张经理",
          phone: "13812345678",
          wechat: "sf_12345"
        }
      };
    },
    // 获取商品详情
    async fetchProductDetail() {
      const productData = common_vendor.index.getStorageSync("currentProduct");
      if (productData) {
        this.productDetail = JSON.parse(productData);
        return;
      }
      try {
        const that = this;
        common_vendor.index.request({
          url: "/api/product/detail",
          method: "GET",
          data: {
            id: this.id
          },
          success: (res) => {
            if (res.data.code === 200 && res.data.data) {
              that.productDetail = {
                id: res.data.data.id,
                title: res.data.data.title,
                price: res.data.data.price,
                tags: res.data.data.tags || [],
                sales: res.data.data.sales || 0,
                image: res.data.data.images && res.data.data.images.length > 0 ? res.data.data.images[0] : "/static/images/device-default.jpg",
                category: res.data.data.type || "",
                condition: res.data.data.condition || "new",
                publishTime: that.formatDateTime(res.data.data.publish_time)
              };
            } else {
              throw new Error("获取商品详情失败");
            }
          },
          fail: (err) => {
            console.error("请求商品详情失败:", err);
            that.useStaticProductData();
          }
        });
      } catch (error) {
        console.error("获取商品详情失败", error);
        this.useStaticProductData();
      }
    },
    // 使用静态商品数据（作为备用）
    useStaticProductData() {
      this.productDetail = {
        id: this.id,
        title: "快递电子秤",
        price: "499",
        tags: ["耐用", "精准", "全新"],
        sales: 28,
        image: "/static/images/device1.jpg",
        category: "scales",
        condition: "new",
        publishTime: "2023-04-05"
      };
      common_vendor.index.showToast({
        title: "商品数据加载失败",
        icon: "none"
      });
    },
    // 获取设备详情
    async fetchDeviceDetail() {
      try {
        const deviceList = common_vendor.index.getStorageSync("deviceList") || [];
        const localDevice = deviceList.find((item) => item.id.toString() === this.id.toString());
        if (localDevice) {
          this.deviceDetail = {
            ...localDevice,
            // 确保图片数据是数组格式
            images: Array.isArray(localDevice.images) ? localDevice.images : ["/static/images/device-default.jpg"],
            description: localDevice.description || "卖家暂未提供详细描述"
          };
          return;
        }
        const that = this;
        common_vendor.index.request({
          url: "/api/device/detail",
          method: "GET",
          data: {
            id: this.id
          },
          success: (res) => {
            if (res.data.code === 200 && res.data.data) {
              that.deviceDetail = {
                ...res.data.data,
                location: res.data.data.address || "未提供地址",
                publishTime: that.formatDateTime(res.data.data.publish_time)
              };
            } else {
              throw new Error("获取设备详情失败");
            }
          },
          fail: (err) => {
            console.error("请求设备详情失败:", err);
            that.useStaticDeviceData();
          }
        });
      } catch (error) {
        console.error("获取设备详情失败", error);
        this.useStaticDeviceData();
      }
    },
    // 使用静态设备数据（作为备用）
    useStaticDeviceData() {
      this.deviceDetail = {
        title: "9成新菜鸟智能电子秤",
        price: "380",
        location: "北京·海淀",
        type: "电子秤",
        images: ["/static/device/scale.jpg"],
        condition: "9成新",
        publishTime: "1小时前",
        description: "这是一台使用仅三个月的电子秤，功能完好，称重精准，外观几乎全新，适合快递驿站使用。",
        contactInfo: {
          name: "王先生",
          phone: "13312345678"
        }
      };
    },
    // 获取服务详情
    async fetchServiceDetail() {
      var _a, _b;
      try {
        const serviceList = common_vendor.index.getStorageSync("serviceList") || [];
        const localService = serviceList.find((item) => item.id.toString() === this.id.toString());
        if (localService) {
          this.serviceDetail = {
            ...localService,
            publishTime: localService.publishTime || this.formatDateTime(/* @__PURE__ */ new Date()),
            viewCount: localService.viewCount || 0,
            contact: {
              name: ((_a = localService.contactInfo) == null ? void 0 : _a.name) || "未提供",
              phone: ((_b = localService.contactInfo) == null ? void 0 : _b.phone) || "未提供"
            }
          };
          return;
        }
        const that = this;
        common_vendor.index.request({
          url: "/api/service/detail",
          method: "GET",
          data: {
            id: this.id
          },
          success: (res) => {
            if (res.data.code === 200 && res.data.data) {
              const additionalFields = res.data.data.additional_fields || {};
              that.serviceDetail = {
                ...res.data.data,
                // 兼容原有数据结构
                publishTime: that.formatDateTime(res.data.data.publish_time),
                viewCount: res.data.data.views || 0,
                contact: {
                  name: additionalFields.contact_name || "未提供",
                  phone: additionalFields.contact_phone || "未提供"
                },
                serviceType: additionalFields.service_type || "未提供",
                serviceTime: additionalFields.service_time || res.data.data.time || "未提供",
                servicePeriod: additionalFields.service_period || "未提供",
                specialRequirements: additionalFields.special_requirements || "无"
              };
            } else {
              throw new Error("获取服务详情失败");
            }
          },
          fail: (err) => {
            console.error("请求服务详情失败:", err);
            that.useStaticServiceData();
          }
        });
      } catch (error) {
        console.error("获取服务详情失败", error);
        this.useStaticServiceData();
      }
    },
    // 使用静态服务数据（作为备用）
    useStaticServiceData() {
      this.serviceDetail = {
        title: "顶班服务 - 包裹分拣员",
        price: "300",
        address: "海淀区北太平庄",
        description: "驿站分拣员一名，有经验者优先，熟悉快递分拣、扫描、地址识别等工作。每天工作8小时，主要负责包裹分拣、整理和交付。",
        publishTime: "2023-04-07 15:28",
        viewCount: 42,
        contact: {
          name: "张站长",
          phone: "13800138000"
        },
        serviceType: "临时顶班",
        serviceTime: "2023-04-15 至 2023-04-20",
        servicePeriod: "早9:00-晚17:00",
        specialRequirements: "要求有驿站工作经验，能吃苦耐劳，工作认真负责。"
      };
    },
    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr)
        return "";
      try {
        const date = new Date(dateTimeStr);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
      } catch (e) {
        return dateTimeStr;
      }
    },
    // 检查收藏状态
    checkCollectStatus() {
      const collects = common_vendor.index.getStorageSync("collects") || "[]";
      const collectList = JSON.parse(collects);
      if (this.detailType === "product") {
        this.isCollected = collectList.some(
          (item) => item.type === "product" && item.id === this.id
        );
      } else if (this.detailType === "station") {
        this.isCollected = collectList.some(
          (item) => item.type === "station" && item.id === this.id
        );
      } else if (this.detailType === "job") {
        this.isCollected = collectList.some(
          (item) => item.type === "job" && item.id === this.id
        );
      } else if (this.detailType === "service") {
        this.isCollected = collectList.some(
          (item) => item.type === "service" && item.id === this.id
        );
      }
    },
    // 切换收藏状态
    toggleCollect() {
      const collects = common_vendor.index.getStorageSync("collects") || "[]";
      const collectList = JSON.parse(collects);
      let collectItem = null;
      if (this.detailType === "product") {
        collectItem = {
          id: this.productDetail.id,
          type: "product",
          title: this.productDetail.title,
          image: this.productDetail.image,
          price: this.productDetail.price,
          collectTime: (/* @__PURE__ */ new Date()).toISOString()
        };
      } else if (this.detailType === "station") {
        collectItem = {
          id: this.stationDetail.id || this.id,
          type: "station",
          title: this.stationDetail.title,
          image: this.stationDetail.images && this.stationDetail.images.length > 0 ? this.stationDetail.images[0] : "/static/images/station-default.jpg",
          price: this.stationDetail.price,
          collectTime: (/* @__PURE__ */ new Date()).toISOString()
        };
      } else if (this.detailType === "job") {
        collectItem = {
          id: this.jobDetail.id || this.id,
          type: "job",
          title: this.jobDetail.title,
          image: "/static/images/job-default.jpg",
          salary: this.jobDetail.salary,
          collectTime: (/* @__PURE__ */ new Date()).toISOString()
        };
      } else if (this.detailType === "service") {
        collectItem = {
          id: this.serviceDetail.id || this.id,
          type: "service",
          title: this.serviceDetail.title,
          image: "/static/images/service-default.jpg",
          price: this.serviceDetail.price,
          collectTime: (/* @__PURE__ */ new Date()).toISOString()
        };
      }
      if (this.isCollected) {
        const index = collectList.findIndex(
          (item) => item.type === collectItem.type && item.id === collectItem.id
        );
        if (index !== -1) {
          collectList.splice(index, 1);
        }
        this.isCollected = false;
        common_vendor.index.showToast({
          title: "已取消收藏",
          icon: "success"
        });
      } else {
        collectList.push(collectItem);
        this.isCollected = true;
        common_vendor.index.showToast({
          title: "收藏成功",
          icon: "success"
        });
      }
      common_vendor.index.setStorageSync("collects", JSON.stringify(collectList));
    },
    // 分享商品
    shareItem() {
      common_vendor.index.showToast({
        title: "分享功能开发中",
        icon: "none"
      });
    },
    // 联系卖家
    contactSeller() {
      if (this.detailType === "station" && this.stationDetail.contact) {
        this.callPhone(this.stationDetail.contact.phone);
      } else if (this.detailType === "job" && this.jobDetail.contact) {
        this.callPhone(this.jobDetail.contact.phone);
      } else if (this.detailType === "service" && this.serviceDetail.contact) {
        this.callPhone(this.serviceDetail.contact.phone);
      } else {
        common_vendor.index.showToast({
          title: "联系信息不可用",
          icon: "none"
        });
      }
    },
    // 接单功能
    takeOrder() {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (!userInfo) {
        common_vendor.index.showModal({
          title: "提示",
          content: "请先登录后再接单",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/login/login"
              });
            }
          }
        });
        return;
      }
      const certificationInfo = common_vendor.index.getStorageSync("certificationInfo");
      const isCertified = certificationInfo && certificationInfo.substituteVerified;
      if (!isCertified) {
        common_vendor.index.showModal({
          title: "认证提示",
          content: "您需要通过顶班认证后才能接单",
          confirmText: "去认证",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.navigateTo({
                url: "/pages/certification/substitute"
              });
            }
          }
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "接单中..."
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        const orderRecord = {
          id: this.id,
          type: this.detailType,
          title: this.serviceDetail.title,
          price: this.serviceDetail.price,
          orderTime: (/* @__PURE__ */ new Date()).toISOString(),
          status: "已接单"
        };
        const myOrders = common_vendor.index.getStorageSync("myOrders") || "[]";
        const myOrdersList = JSON.parse(myOrders);
        myOrdersList.push(orderRecord);
        common_vendor.index.setStorageSync("myOrders", JSON.stringify(myOrdersList));
        common_vendor.index.showToast({
          title: "接单成功",
          icon: "success"
        });
        setTimeout(() => {
          common_vendor.index.navigateTo({
            url: "/pages/my/orders"
          });
        }, 1500);
      }, 1e3);
    },
    // 拨打电话
    callPhone(phone) {
      if (!phone) {
        common_vendor.index.showToast({
          title: "电话号码不可用",
          icon: "none"
        });
        return;
      }
      common_vendor.index.makePhoneCall({
        phoneNumber: phone,
        fail: () => {
          common_vendor.index.showToast({
            title: "拨号已取消",
            icon: "none"
          });
        }
      });
    },
    // 复制微信号
    copyWechat(wechat) {
      if (!wechat) {
        common_vendor.index.showToast({
          title: "微信号不可用",
          icon: "none"
        });
        return;
      }
      common_vendor.index.setClipboardData({
        data: wechat,
        success: () => {
          common_vendor.index.showToast({
            title: "微信号已复制",
            icon: "success"
          });
        }
      });
    },
    // 举报
    reportItem() {
      common_vendor.index.showToast({
        title: "举报功能开发中",
        icon: "none"
      });
    },
    // 获取分类名称
    getCategoryName(category) {
      switch (category) {
        case "scales":
          return "电子秤";
        case "printer":
          return "打印机";
        case "shelves":
          return "货架柜";
        case "scanner":
          return "扫描器";
        default:
          return "其他设备";
      }
    },
    // 获取商品状态名称
    getConditionName(condition) {
      switch (condition) {
        case "new":
          return "全新";
        case "used":
          return "二手";
        case "refurbished":
          return "翻新";
        default:
          return "未知";
      }
    },
    // 导航到首页
    navToHome() {
      common_vendor.index.switchTab({
        url: "/pages/station/station"
      });
    },
    // 导航到购物车
    navToCart() {
      common_vendor.index.navigateTo({
        url: "/pages/my/cart"
      });
    },
    // 联系客服
    contactService() {
      common_vendor.index.showToast({
        title: "客服功能开发中",
        icon: "none"
      });
    },
    // 进入店铺
    enterShop() {
      common_vendor.index.showToast({
        title: "店铺页面开发中",
        icon: "none"
      });
    },
    // 加入购物车
    addToCart() {
      const cart = common_vendor.index.getStorageSync("cart") || "[]";
      const cartList = JSON.parse(cart);
      const cartItem = {
        id: this.productDetail.id,
        title: this.productDetail.title,
        image: this.productDetail.image,
        price: this.productDetail.price,
        count: 1,
        selected: true,
        addTime: (/* @__PURE__ */ new Date()).toISOString()
      };
      const existingIndex = cartList.findIndex((item) => item.id === cartItem.id);
      if (existingIndex !== -1) {
        cartList[existingIndex].count += 1;
      } else {
        cartList.push(cartItem);
      }
      common_vendor.index.setStorageSync("cart", JSON.stringify(cartList));
      common_vendor.index.showToast({
        title: "已加入购物车",
        icon: "success"
      });
    },
    // 立即购买
    buyNow() {
      const orderItems = [{
        id: this.productDetail.id,
        title: this.productDetail.title,
        image: this.productDetail.image,
        price: this.productDetail.price,
        count: 1,
        selected: true
      }];
      common_vendor.index.setStorageSync("checkoutItems", JSON.stringify(orderItems));
      common_vendor.index.navigateTo({
        url: "/pages/my/checkout"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m;
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : $data.error ? {
    c: common_assets._imports_0$2,
    d: common_vendor.t($data.error),
    e: common_vendor.o((...args) => $options.retryLoading && $options.retryLoading(...args))
  } : $data.isEmpty ? {
    g: common_assets._imports_1$3
  } : common_vendor.e({
    h: common_assets._imports_0$3,
    i: common_vendor.o((...args) => $options.goBack && $options.goBack(...args)),
    j: $data.isCollected ? "/static/icons/collected.png" : "/static/icons/collect.png",
    k: common_vendor.o((...args) => $options.toggleCollect && $options.toggleCollect(...args)),
    l: common_assets._imports_3$2,
    m: common_vendor.o((...args) => $options.shareItem && $options.shareItem(...args)),
    n: $data.detailType === "station"
  }, $data.detailType === "station" ? common_vendor.e({
    o: common_vendor.f($data.stationDetail.images, (img, index, i0) => {
      return {
        a: img,
        b: index
      };
    }),
    p: common_vendor.t($data.stationDetail.title),
    q: common_vendor.t($data.stationDetail.price),
    r: common_vendor.t($data.stationDetail.priceUnit),
    s: common_vendor.f($data.stationDetail.tags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index
      };
    }),
    t: common_vendor.t($data.stationDetail.publishTime),
    v: common_vendor.t($data.stationDetail.viewCount),
    w: common_vendor.t(((_a = $data.stationDetail.contact) == null ? void 0 : _a.name) || "未提供"),
    x: common_vendor.t(((_b = $data.stationDetail.contact) == null ? void 0 : _b.phone) || "未提供"),
    y: common_assets._imports_4$3,
    z: common_vendor.o(($event) => {
      var _a2;
      return $options.callPhone((_a2 = $data.stationDetail.contact) == null ? void 0 : _a2.phone);
    }),
    A: (_c = $data.stationDetail.contact) == null ? void 0 : _c.wechat
  }, ((_d = $data.stationDetail.contact) == null ? void 0 : _d.wechat) ? {
    B: common_vendor.t((_e = $data.stationDetail.contact) == null ? void 0 : _e.wechat),
    C: common_assets._imports_3$3,
    D: common_vendor.o(($event) => {
      var _a2;
      return $options.copyWechat((_a2 = $data.stationDetail.contact) == null ? void 0 : _a2.wechat);
    })
  } : {}, {
    E: common_vendor.t($data.stationDetail.address || "未提供"),
    F: common_vendor.t($data.stationDetail.area || "0"),
    G: common_vendor.t($data.stationDetail.monthlyIncome || "0"),
    H: common_vendor.t($data.stationDetail.dailyPackage || "0"),
    I: common_vendor.t($data.stationDetail.reason || "未提供"),
    J: common_vendor.t($data.stationDetail.description || "暂无详细描述")
  }) : {}, {
    K: $data.detailType === "job"
  }, $data.detailType === "job" ? {
    L: common_vendor.t($data.jobDetail.title),
    M: common_vendor.t($data.jobDetail.salary),
    N: common_vendor.t($data.jobDetail.company),
    O: common_vendor.t($data.jobDetail.address),
    P: common_vendor.f($data.jobDetail.tags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index
      };
    }),
    Q: common_vendor.t($data.jobDetail.publishTime),
    R: common_vendor.t($data.jobDetail.viewCount),
    S: common_vendor.t($data.jobDetail.experience),
    T: common_vendor.t($data.jobDetail.education),
    U: common_vendor.t($data.jobDetail.ageRequirement),
    V: common_vendor.t($data.jobDetail.jobType),
    W: common_vendor.t($data.jobDetail.workTime),
    X: common_vendor.t($data.jobDetail.jobDescription),
    Y: common_vendor.t($data.jobDetail.benefits),
    Z: common_vendor.t(((_f = $data.jobDetail.contact) == null ? void 0 : _f.name) || "未提供"),
    aa: common_vendor.t(((_g = $data.jobDetail.contact) == null ? void 0 : _g.phone) || "未提供"),
    ab: common_assets._imports_4$3,
    ac: common_vendor.o(($event) => {
      var _a2;
      return $options.callPhone((_a2 = $data.jobDetail.contact) == null ? void 0 : _a2.phone);
    })
  } : {}, {
    ad: $data.detailType === "device"
  }, $data.detailType === "device" ? common_vendor.e({
    ae: common_vendor.f($data.deviceDetail.images, (img, index, i0) => {
      return {
        a: img,
        b: index
      };
    }),
    af: common_vendor.t($data.deviceDetail.title),
    ag: common_vendor.t($data.deviceDetail.price),
    ah: common_vendor.t($data.deviceDetail.location),
    ai: common_vendor.t($data.deviceDetail.type),
    aj: common_vendor.t($data.deviceDetail.condition),
    ak: common_vendor.t($data.deviceDetail.publishTime),
    al: common_vendor.t($data.deviceDetail.description || "卖家暂未提供详细描述"),
    am: common_vendor.t(((_h = $data.deviceDetail.contactInfo) == null ? void 0 : _h.name) || "未提供"),
    an: common_vendor.t(((_i = $data.deviceDetail.contactInfo) == null ? void 0 : _i.phone) || "未提供"),
    ao: common_assets._imports_4$3,
    ap: common_vendor.o(($event) => {
      var _a2;
      return $options.callPhone((_a2 = $data.deviceDetail.contactInfo) == null ? void 0 : _a2.phone);
    }),
    aq: (_j = $data.deviceDetail.contactInfo) == null ? void 0 : _j.wechat
  }, ((_k = $data.deviceDetail.contactInfo) == null ? void 0 : _k.wechat) ? {
    ar: common_vendor.t($data.deviceDetail.contactInfo.wechat),
    as: common_assets._imports_3$3,
    at: common_vendor.o(($event) => $options.copyWechat($data.deviceDetail.contactInfo.wechat))
  } : {}) : {}, {
    av: $data.detailType === "service"
  }, $data.detailType === "service" ? common_vendor.e({
    aw: common_vendor.t($data.serviceDetail.title),
    ax: common_vendor.t($data.serviceDetail.price),
    ay: $data.serviceDetail.address
  }, $data.serviceDetail.address ? {
    az: common_vendor.t($data.serviceDetail.address)
  } : {}, {
    aA: $data.serviceDetail.tags && $data.serviceDetail.tags.length
  }, $data.serviceDetail.tags && $data.serviceDetail.tags.length ? {
    aB: common_vendor.f($data.serviceDetail.tags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index
      };
    })
  } : {}, {
    aC: common_vendor.t($data.serviceDetail.publishTime || "未知"),
    aD: common_vendor.t($data.serviceDetail.viewCount || 0),
    aE: common_vendor.t($data.serviceDetail.description || "暂无服务描述"),
    aF: common_vendor.t($data.serviceDetail.serviceType || "未提供"),
    aG: common_vendor.t($data.serviceDetail.serviceTime || "未提供"),
    aH: common_vendor.t($data.serviceDetail.servicePeriod || "未提供"),
    aI: common_vendor.t($data.serviceDetail.specialRequirements || "无"),
    aJ: common_vendor.t(((_l = $data.serviceDetail.contact) == null ? void 0 : _l.name) || "未提供"),
    aK: common_vendor.t(((_m = $data.serviceDetail.contact) == null ? void 0 : _m.phone) || "未提供"),
    aL: common_assets._imports_4$3,
    aM: common_vendor.o(($event) => {
      var _a2;
      return $options.callPhone((_a2 = $data.serviceDetail.contact) == null ? void 0 : _a2.phone);
    })
  }) : {}, {
    aN: $data.detailType === "product"
  }, $data.detailType === "product" ? {
    aO: $data.productDetail.image,
    aP: common_vendor.t($data.productDetail.title),
    aQ: common_vendor.t($data.productDetail.price),
    aR: common_vendor.f($data.productDetail.tags, (tag, index, i0) => {
      return {
        a: common_vendor.t(tag),
        b: index
      };
    }),
    aS: common_vendor.t($data.productDetail.sales),
    aT: common_vendor.t($options.getCategoryName($data.productDetail.category)),
    aU: common_vendor.t($options.getConditionName($data.productDetail.condition)),
    aV: common_vendor.t($data.productDetail.publishTime),
    aW: common_vendor.t($data.productDetail.condition === "new" ? "全新商品，保修1年" : "二手商品，保修3个月"),
    aX: common_vendor.t($data.productDetail.title),
    aY: $data.productDetail.image,
    aZ: common_assets._imports_6$1,
    ba: common_vendor.o((...args) => $options.enterShop && $options.enterShop(...args))
  } : {}, {
    bb: $data.detailType === "product"
  }, $data.detailType === "product" ? {
    bc: common_assets._imports_7$1,
    bd: common_vendor.o((...args) => $options.navToHome && $options.navToHome(...args)),
    be: common_assets._imports_8$1,
    bf: common_vendor.o((...args) => $options.navToCart && $options.navToCart(...args)),
    bg: common_assets._imports_5,
    bh: common_vendor.o((...args) => $options.contactService && $options.contactService(...args)),
    bi: common_vendor.o((...args) => $options.addToCart && $options.addToCart(...args)),
    bj: common_vendor.o((...args) => $options.buyNow && $options.buyNow(...args))
  } : common_vendor.e({
    bk: $data.detailType === "service"
  }, $data.detailType === "service" ? {
    bl: common_vendor.o((...args) => $options.takeOrder && $options.takeOrder(...args))
  } : {
    bm: common_vendor.o((...args) => $options.contactSeller && $options.contactSeller(...args))
  }, {
    bn: common_vendor.o((...args) => $options.reportItem && $options.reportItem(...args))
  })), {
    b: $data.error,
    f: $data.isEmpty
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
