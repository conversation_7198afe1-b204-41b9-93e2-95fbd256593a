
.container {
		padding-bottom: 120rpx;
		background-color: #f5f5f5;
		min-height: 100vh; /* 确保容器至少有一屏高度 */
}
	
	/* 加载状态样式 */
.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
		min-height: 50vh;
}
.loading-spinner {
		width: 80rpx;
		height: 80rpx;
		border: 6rpx solid #f3f3f3;
		border-top: 6rpx solid #1989fa;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.loading-text {
		font-size: 28rpx;
		color: #999;
}
	
	/* 错误提示样式 */
.error-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 30rpx;
		min-height: 50vh;
}
.error-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
}
.error-text {
		font-size: 28rpx;
		color: #666;
		text-align: center;
		margin-bottom: 40rpx;
}
.retry-btn {
		padding: 20rpx 60rpx;
		background-color: #1989fa;
		color: #fff;
		border-radius: 40rpx;
		font-size: 28rpx;
}
	
	/* 空状态样式 */
.empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
		min-height: 50vh;
}
.empty-icon {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 30rpx;
}
.empty-text {
		font-size: 28rpx;
		color: #999;
}
.action-bar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		height: 88rpx;
		background-color: rgba(0, 0, 0, 0.3);
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20rpx;
		z-index: 100;
}
.action-left, .action-right {
		display: flex;
}
.action-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
}
.action-btn image {
		width: 40rpx;
		height: 40rpx;
		filter: brightness(2);
}
.detail-swiper {
		width: 100%;
		height: 500rpx;
		margin-top: 0;
}
.swiper-image {
		width: 100%;
		height: 100%;
}
.detail-section {
		background-color: #ffffff;
		margin-bottom: 20rpx;
		padding: 30rpx;
}
.detail-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
}
.detail-price {
		font-size: 40rpx;
		font-weight: bold;
		color: #ff5a5f;
		margin-bottom: 20rpx;
}
.price-unit {
		font-size: 28rpx;
		font-weight: normal;
}
.detail-company {
		font-size: 32rpx;
		color: #333333;
		margin-bottom: 10rpx;
}
.detail-address {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 20rpx;
}
.detail-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 20rpx;
}
.detail-tag {
		font-size: 24rpx;
		padding: 6rpx 16rpx;
		background-color: #f8f8f8;
		color: #999999;
		margin-right: 15rpx;
		margin-bottom: 10rpx;
		border-radius: 6rpx;
}
.detail-publish-info {
		display: flex;
		justify-content: space-between;
		font-size: 24rpx;
		color: #999999;
}
.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
		position: relative;
		padding-left: 20rpx;
}
.section-title::before {
		content: '';
		position: absolute;
		left: 0;
		top: 6rpx;
		height: 30rpx;
		width: 6rpx;
		background-color: #ff5a5f;
}
.contact-info, .station-info, .job-requirements, .service-info {
		padding: 10rpx 0;
}
.contact-item, .info-item, .requirement-item {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
}
.contact-label, .info-label, .requirement-label {
		width: 160rpx;
		font-size: 28rpx;
		color: #666666;
}
.contact-value, .info-value, .requirement-value {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
}
.contact-btn {
		display: flex;
		align-items: center;
		background-color: #f8f8f8;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		margin-left: 20rpx;
}
.contact-btn image {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
}
.contact-btn text {
		font-size: 24rpx;
		color: #666666;
}
.detail-description, .job-description, .job-benefits {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.6;
}
.bottom-action-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #ffffff;
		display: flex;
		justify-content: space-around;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.action-btn-large {
		width: 45%;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 40rpx;
		font-size: 28rpx;
}
.action-btn-large.primary {
		background-color: #ff5a5f;
		color: #ffffff;
}
.action-btn-large.outline {
		background-color: #ffffff;
		color: #666666;
		border: 1rpx solid #dddddd;
}
	
	/* 添加产品详情相关样式 */
.shop-info {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
}
.shop-logo {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		margin-right: 20rpx;
}
.shop-detail {
		flex: 1;
		display: flex;
		flex-direction: column;
}
.shop-name {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 6rpx;
}
.shop-desc {
		font-size: 24rpx;
		color: #999;
}
.shop-btn {
		padding: 10rpx 30rpx;
		border: 1rpx solid #ff5a5f;
		border-radius: 30rpx;
}
.shop-btn text {
		font-size: 24rpx;
		color: #ff5a5f;
}
.detail-sales {
		display: flex;
		justify-content: space-between;
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
}
.spec-list {
		margin-top: 20rpx;
}
.spec-item {
		display: flex;
		margin-bottom: 16rpx;
}
.spec-label {
		width: 160rpx;
		font-size: 26rpx;
		color: #999;
}
.spec-value {
		flex: 1;
		font-size: 26rpx;
		color: #333;
}
.product-description {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
}
.desc-image {
		width: 100%;
		margin-top: 20rpx;
		border-radius: 8rpx;
}
	
	/* 底部按钮样式 */
.action-left {
		display: flex;
}
.icon-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 0 20rpx;
}
.icon-btn image {
		width: 50rpx;
		height: 50rpx;
		margin-bottom: 6rpx;
}
.icon-btn text {
		font-size: 22rpx;
		color: #666;
}
.action-right {
		flex: 1;
		display: flex;
}
.add-cart {
		background-color: #ffecee;
		color: #ff5a5f;
		border: 1rpx solid #ff5a5f;
}
.buy-now {
		background-color: #ff5a5f;
		color: #fff;
}
