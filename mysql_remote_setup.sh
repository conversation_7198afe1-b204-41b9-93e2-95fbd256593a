#!/bin/bash

echo "=== MySQL远程连接设置指南 ==="
echo ""
echo "请按照以下步骤允许MySQL远程连接："
echo ""
echo "1. 编辑MySQL配置文件："
echo "   sudo nano /opt/homebrew/etc/my.cnf"
echo ""
echo "2. 找到并修改以下行："
echo "   bind-address = 127.0.0.1"
echo "   修改为："
echo "   bind-address = 0.0.0.0"
echo ""
echo "   同样也修改："
echo "   mysqlx-bind-address = 127.0.0.1"
echo "   修改为："
echo "   mysqlx-bind-address = 0.0.0.0"
echo ""
echo "3. 保存并退出编辑器 (Ctrl+O, 然后 Ctrl+X)"
echo ""
echo "4. 重启MySQL服务："
echo "   brew services restart mysql"
echo ""
echo "5. 进入MySQL创建远程访问用户并授权："
echo "   mysql -u root -p"
echo ""
echo "6. 在MySQL提示符下运行以下命令："
echo "   CREATE USER 'remote_user'@'%' IDENTIFIED BY '您的密码';"
echo "   GRANT ALL PRIVILEGES ON *.* TO 'remote_user'@'%';"
echo "   FLUSH PRIVILEGES;"
echo "   EXIT;"
echo ""
echo "7. 确保Mac防火墙允许3306端口："
echo "   sudo /usr/libexec/ApplicationFirewall/socketfilterfw --add /opt/homebrew/opt/mysql/bin/mysqld"
echo "   sudo /usr/libexec/ApplicationFirewall/socketfilterfw --unblockapp /opt/homebrew/opt/mysql/bin/mysqld"
echo ""
echo "8. 获取您的公网IP地址以供远程连接："
echo "   curl ifconfig.me"
echo ""
echo "9. 远程连接信息："
echo "   主机: <您的公网IP>"
echo "   端口: 3306"
echo "   用户名: remote_user"
echo "   密码: <您设置的密码>"
echo ""
echo "注意：请确保您的网络环境（路由器、ISP等）允许3306端口的流量。"
echo "      您可能需要在路由器上设置端口转发。"
echo "      出于安全考虑，建议使用强密码并限制允许连接的IP地址。" 