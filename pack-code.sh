#!/bin/bash

# 创建用于腾讯云托管部署的代码包

# 显示彩色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}开始创建新的打包结构...${NC}"

# 创建一个临时目录用于打包
TEMP_DIR="$(pwd)/temp-deploy"
rm -rf "$TEMP_DIR"
mkdir -p "$TEMP_DIR"

# 复制Dockerfile到临时目录的根目录
echo -e "${YELLOW}复制Dockerfile到根目录...${NC}"
cat > "$TEMP_DIR/Dockerfile" << 'EOF'
# 使用Nginx作为基础镜像
FROM nginx:alpine

# 设置工作目录
WORKDIR /usr/share/nginx/html

# 复制小程序文件到Nginx默认目录
COPY ./app/ .

# 暴露80端口
EXPOSE 80

# 启动Nginx服务器
CMD ["nginx", "-g", "daemon off;"]
EOF

# 创建app目录并复制小程序文件
echo -e "${YELLOW}复制微信小程序文件...${NC}"
mkdir -p "$TEMP_DIR/app"
cp -r "$(pwd)/unpackage/dist/dev/mp-weixin/"* "$TEMP_DIR/app/"

# 进入临时目录
cd "$TEMP_DIR" || {
  echo -e "${YELLOW}错误: 无法进入临时目录${NC}"
  exit 1
}

# 列出目录内容
echo -e "${YELLOW}临时目录内容:${NC}"
ls -la

# 验证Dockerfile存在
if [ -f "Dockerfile" ]; then
  echo -e "${GREEN}Dockerfile存在于根目录${NC}"
  cat Dockerfile
else
  echo -e "${YELLOW}错误: Dockerfile未创建${NC}"
  exit 1
fi

# 打包文件
ZIP_FILE="$(pwd)/yizhanbang.zip"
echo -e "${YELLOW}正在创建代码包: $ZIP_FILE${NC}"

if command -v zip >/dev/null 2>&1; then
  zip -r "$ZIP_FILE" ./* && {
    echo -e "${GREEN}代码包已成功创建: $ZIP_FILE${NC}"
    
    # 将zip文件移回原始目录
    cp "$ZIP_FILE" "$(pwd)/../"
    
    echo -e "${GREEN}请按照以下步骤操作:${NC}"
    echo -e "1. 在腾讯云托管平台，选择\"部署发布\" -> \"选择代码\" -> \"手动上传代码包\""
    echo -e "2. 上传刚才创建的代码包 $(pwd)/../yizhanbang.zip"
    echo -e "3. 确保在高级配置中，Dockerfile路径为: / (根目录)"
    echo -e "4. 配置部署环境（端口应为80，与Dockerfile中匹配）"
    echo -e "5. 点击\"发布\"按钮完成部署"
  } || {
    echo -e "${YELLOW}错误: 创建zip文件失败${NC}"
    exit 1
  }
else
  echo -e "${YELLOW}错误: 未找到zip命令，请安装zip或手动创建zip文件${NC}"
  echo -e "可以使用以下命令安装zip: brew install zip (MacOS) 或 apt-get install zip (Linux)"
  exit 1
fi 