/**
 * 微信小程序应用错误处理模块
 * 用于修复常见错误，如toLocaleLowerCase错误
 */

// 修复getOSInfo函数中的toLocaleLowerCase错误
export function fixToLocaleLowerCaseError() {
  // 1. 拦截console.error以防止错误显示
  const originalConsoleError = console.error;
  console.error = function(...args) {
    // 检查是否为toLocaleLowerCase错误
    const errorMessage = args.join(' ');
    if (
      errorMessage.includes('Cannot read property') && 
      errorMessage.includes('toLocaleLowerCase') && 
      errorMessage.includes('undefined')
    ) {
      console.log('[已拦截] toLocaleLowerCase错误');
      return;
    }
    
    // 其他错误正常输出
    return originalConsoleError.apply(console, args);
  };
  
  // 2. 替换wx.getSystemInfoSync方法，确保返回的system不为undefined
  if (typeof wx !== 'undefined' && wx.getSystemInfoSync) {
    const originalGetSystemInfoSync = wx.getSystemInfoSync;
    wx.getSystemInfoSync = function() {
      try {
        const result = originalGetSystemInfoSync.apply(this, arguments);
        // 确保system字段不为undefined
        if (!result.system) {
          result.system = result.platform || 'unknown';
        }
        return result;
      } catch (error) {
        console.log('[错误] 获取系统信息失败:', error);
        // 返回一个带有必要字段的默认对象
        return {
          system: 'unknown',
          platform: 'unknown',
          brand: '',
          model: '',
          pixelRatio: 2,
          windowWidth: 375,
          windowHeight: 667,
          language: 'zh_CN',
          version: '0.0.0',
          SDKVersion: '0.0.0'
        };
      }
    };
  }
  
  // 3. 同样为wx.getDeviceInfo和wx.getAppBaseInfo应用相同的修复
  if (typeof wx !== 'undefined') {
    if (wx.getDeviceInfo) {
      const originalGetDeviceInfo = wx.getDeviceInfo;
      wx.getDeviceInfo = function() {
        try {
          const result = originalGetDeviceInfo.apply(this, arguments);
          if (!result.system) {
            result.system = result.platform || 'unknown';
          }
          return result;
        } catch (error) {
          console.log('[错误] 获取设备信息失败:', error);
          return {
            system: 'unknown',
            platform: 'unknown',
            brand: '',
            model: ''
          };
        }
      };
    }
    
    if (wx.getAppBaseInfo) {
      const originalGetAppBaseInfo = wx.getAppBaseInfo;
      wx.getAppBaseInfo = function() {
        try {
          return originalGetAppBaseInfo.apply(this, arguments);
        } catch (error) {
          console.log('[错误] 获取应用信息失败:', error);
          return {
            language: 'zh_CN',
            version: '0.0.0',
            SDKVersion: '0.0.0'
          };
        }
      };
    }
  }
}

// 通用错误处理
export function setupErrorHandlers() {
  // 拦截JS错误
  if (typeof wx !== 'undefined' && wx.onError) {
    wx.onError((error) => {
      console.log('[小程序错误]', error);
      // 可以在这里添加错误上报逻辑
    });
  }
  
  // 拦截未捕获的Promise错误
  if (typeof wx !== 'undefined' && wx.onUnhandledRejection) {
    wx.onUnhandledRejection(({ reason }) => {
      console.log('[Promise错误]', reason);
      // 可以在这里添加错误上报逻辑
    });
  }
  
  // 拦截页面不存在的错误
  if (typeof wx !== 'undefined' && wx.onPageNotFound) {
    wx.onPageNotFound((res) => {
      console.log('[页面不存在]', res.path);
      // 可以在这里添加重定向逻辑
    });
  }
}

// 导出默认对象，包含所有修复函数
export default {
  fixToLocaleLowerCaseError,
  setupErrorHandlers
}; 