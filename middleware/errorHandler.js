/**
 * 统一错误处理中间件
 */

const config = require('../config');

/**
 * 404错误处理
 */
const notFoundHandler = (req, res) => {
  res.status(404).json({
    code: 404,
    message: `路径 ${req.originalUrl} 不存在`,
    timestamp: new Date().toISOString()
  });
};

/**
 * 全局错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  // 记录错误日志
  console.error('服务器错误:', {
    message: err.message,
    stack: config.IS_DEVELOPMENT ? err.stack : undefined,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });

  // 默认错误信息
  let statusCode = 500;
  let message = '服务器内部错误';
  let code = 500;

  // 根据错误类型设置响应
  if (err.name === 'ValidationError') {
    // Sequelize验证错误
    statusCode = 400;
    code = 400;
    message = '数据验证失败';
  } else if (err.name === 'SequelizeUniqueConstraintError') {
    // 唯一约束错误
    statusCode = 400;
    code = 400;
    message = '数据已存在，请检查重复字段';
  } else if (err.name === 'SequelizeForeignKeyConstraintError') {
    // 外键约束错误
    statusCode = 400;
    code = 400;
    message = '关联数据不存在';
  } else if (err.name === 'JsonWebTokenError') {
    // JWT错误
    statusCode = 401;
    code = 401;
    message = '无效的访问令牌';
  } else if (err.name === 'TokenExpiredError') {
    // JWT过期错误
    statusCode = 401;
    code = 401;
    message = '访问令牌已过期';
  } else if (err.status || err.statusCode) {
    // 自定义状态码错误
    statusCode = err.status || err.statusCode;
    code = statusCode;
    message = err.message || message;
  }

  // 返回错误响应
  res.status(statusCode).json({
    code,
    message,
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    // 开发环境返回详细错误信息
    ...(config.IS_DEVELOPMENT && {
      error: err.message,
      stack: err.stack
    })
  });
};

/**
 * 异步错误包装器
 * 用于包装异步路由处理函数，自动捕获Promise错误
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 创建自定义错误
 */
class AppError extends Error {
  constructor(message, statusCode = 500, code = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code || statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

module.exports = {
  notFoundHandler,
  errorHandler,
  asyncHandler,
  AppError
};
