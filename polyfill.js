/**
 * 微信小程序全局polyfill
 * 用于处理常见错误和兼容性问题
 */

// 保存原始console方法
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

/**
 * 应用所有polyfill和错误处理
 */
export function applyPolyfills() {
  // 拦截console.error
  console.error = function(...args) {
    const errorMessage = args.join(' ');
    
    // 过滤toLocaleLowerCase错误
    if (
      errorMessage.includes('Cannot read property') && 
      errorMessage.includes('toLocaleLowerCase') && 
      errorMessage.includes('undefined')
    ) {
      console.log('[已拦截] toLocaleLowerCase错误');
      return;
    }
    
    // 过滤页面找不到错误
    if (errorMessage.includes('Page not found') || 
        errorMessage.includes('Cannot find page')) {
      console.log('[已拦截] 页面不存在错误:', errorMessage);
      return;
    }
    
    // 其他错误正常输出
    return originalConsoleError.apply(console, args);
  };
  
  // 拦截console.warn
  console.warn = function(...args) {
    const warnMessage = args.join(' ');
    
    // 过滤getSystemInfoSync弃用警告
    if (warnMessage.includes('deprecated') && 
        warnMessage.includes('getSystemInfoSync')) {
      return;
    }
    
    // 其他警告正常输出
    return originalConsoleWarn.apply(console, args);
  };
  
  // 增强系统信息获取方法
  enhanceSystemInfoMethods();
  
  // 设置全局错误处理
  setupGlobalErrorHandlers();
  
  console.log('[Polyfill] 已应用错误处理和兼容性修复');
}

/**
 * 增强系统信息获取方法
 */
function enhanceSystemInfoMethods() {
  if (typeof wx !== 'undefined') {
    // 增强wx.getSystemInfoSync
    if (wx.getSystemInfoSync) {
      const originalGetSystemInfoSync = wx.getSystemInfoSync;
      wx.getSystemInfoSync = function() {
        try {
          const result = originalGetSystemInfoSync.apply(this, arguments);
          // 确保关键字段不为undefined
          if (!result.system) {
            result.system = result.platform || 'unknown';
          }
          return result;
        } catch (error) {
          console.log('[Polyfill] 获取系统信息失败:', error);
          // 返回默认值
          return {
            system: 'unknown',
            platform: 'unknown',
            brand: '',
            model: '',
            pixelRatio: 2,
            windowWidth: 375,
            windowHeight: 667,
            language: 'zh_CN',
            version: '0.0.0',
            SDKVersion: '0.0.0'
          };
        }
      };
    }
    
    // 增强wx.getDeviceInfo
    if (wx.getDeviceInfo) {
      const originalGetDeviceInfo = wx.getDeviceInfo;
      wx.getDeviceInfo = function() {
        try {
          const result = originalGetDeviceInfo.apply(this, arguments);
          if (!result.system) {
            result.system = result.platform || 'unknown';
          }
          return result;
        } catch (error) {
          console.log('[Polyfill] 获取设备信息失败:', error);
          return {
            system: 'unknown',
            platform: 'unknown',
            brand: '',
            model: ''
          };
        }
      };
    }
    
    // 增强wx.getAppBaseInfo
    if (wx.getAppBaseInfo) {
      const originalGetAppBaseInfo = wx.getAppBaseInfo;
      wx.getAppBaseInfo = function() {
        try {
          return originalGetAppBaseInfo.apply(this, arguments);
        } catch (error) {
          console.log('[Polyfill] 获取应用信息失败:', error);
          return {
            language: 'zh_CN',
            version: '0.0.0',
            SDKVersion: '0.0.0'
          };
        }
      };
    }
  }
}

/**
 * 设置全局错误处理
 */
function setupGlobalErrorHandlers() {
  if (typeof wx !== 'undefined') {
    // 拦截JS错误
    if (wx.onError) {
      wx.onError((error) => {
        console.log('[Polyfill] 捕获全局错误:', error);
        // 错误可以在这里上报
      });
    }
    
    // 拦截未处理的Promise错误
    if (wx.onUnhandledRejection) {
      wx.onUnhandledRejection(({ reason }) => {
        console.log('[Polyfill] 捕获Promise错误:', reason);
        // 错误可以在这里上报
      });
    }
    
    // 拦截页面不存在错误
    if (wx.onPageNotFound) {
      wx.onPageNotFound((res) => {
        console.log('[Polyfill] 页面不存在:', res.path);
        // 可以添加重定向逻辑
        wx.switchTab({
          url: '/pages/index/index',
          fail: (err) => {
            console.log('[Polyfill] 重定向失败:', err);
          }
        });
      });
    }
  }
}

export default {
  applyPolyfills
}; 