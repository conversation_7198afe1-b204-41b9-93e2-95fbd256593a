const { sequelize } = require('./database/sequelize');

async function checkDatabase() {
  try {
    // 测试连接
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 查看用户表结构
    const [results] = await sequelize.query("DESCRIBE users");
    console.log('用户表结构:');
    console.table(results);
    
    // 查看用户数据
    const [users] = await sequelize.query("SELECT * FROM users LIMIT 5");
    console.log('用户数据:');
    console.table(users);
    
  } catch (error) {
    console.error('数据库检查失败:', error);
  } finally {
    await sequelize.close();
  }
}

checkDatabase();
