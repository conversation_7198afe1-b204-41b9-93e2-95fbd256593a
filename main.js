import api from './api' // 导入API接口
// import appFix from './app-fix' // 导入错误修复模块
import Vue from 'vue'
import { applyPolyfills } from './polyfill'
import './uni.promisify.adaptor'

// 应用polyfill，解决兼容性问题
applyPolyfills()

// 不再需要单独应用app-fix
// appFix.fixToLocaleLowerCaseError();
// appFix.setupErrorHandlers();

// #ifndef VUE3
import App from './App'

// 全局注册API接口
Vue.prototype.$api = api

Vue.config.productionTip = false
App.mpType = 'app'

const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import App from './App.vue'
export function createApp() {
  const app = createSSRApp(App)
  
  // 全局注册API接口
  app.config.globalProperties.$api = api
  
  return {
    app
  }
}
// #endif