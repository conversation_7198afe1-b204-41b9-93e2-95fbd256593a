/**
 * 统一数据库接口模拟测试
 * 用于在没有实际数据库连接时测试API功能
 */

// 模拟数据
const mockData = {
  users: [
    { id: 1, phone: '13800138000', nickname: '管理员', is_admin: 1, status: 1 },
    { id: 2, phone: '13900001111', nickname: '测试用户', is_admin: 0, status: 1 },
    { id: 3, phone: '13900002222', nickname: '张三', is_admin: 0, status: 1 },
    { id: 4, phone: '13900003333', nickname: '李四', is_admin: 0, status: 0 }
  ],
  wallets: [
    { id: 1, user_id: 1, balance: 1000.00, frozen_balance: 0.00 },
    { id: 2, user_id: 2, balance: 500.50, frozen_balance: 0.00 }
  ],
  products: [
    { id: 1, name: '测试商品1', price: 99.00, stock: 100 },
    { id: 2, name: '测试商品2', price: 199.00, stock: 50 }
  ]
};

// 模拟数据库接口
const mockDb = {
  pool: {
    end: async () => console.log('[Mock] Connection pool closed')
  },
  logger: {
    error: console.error,
    info: console.log
  },
  /**
   * 模拟查询单条记录
   * @param {string} table - 表名
   * @param {Object} conditions - 查询条件
   * @returns {Promise<Object>} - 查询结果
   */
  findOne: async (table, conditions) => {
    console.log(`[Mock] findOne from ${table} with conditions:`, conditions);
    
    if (!mockData[table]) {
      return null;
    }
    
    const keys = Object.keys(conditions);
    const record = mockData[table].find(item => {
      return keys.every(key => item[key] === conditions[key]);
    });
    
    return record || null;
  },
  
  /**
   * 模拟查询多条记录
   * @param {string} table - 表名
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} - 查询结果
   */
  find: async (table, options = {}) => {
    const { conditions = {}, limit = 0, offset = 0 } = options;
    console.log(`[Mock] find from ${table} with options:`, options);
    
    if (!mockData[table]) {
      return [];
    }
    
    let results = [...mockData[table]];
    
    // 应用查询条件
    if (conditions && Object.keys(conditions).length > 0) {
      const keys = Object.keys(conditions);
      results = results.filter(item => {
        return keys.every(key => item[key] === conditions[key]);
      });
    }
    
    // 应用分页
    if (limit > 0) {
      results = results.slice(offset, offset + limit);
    }
    
    return results;
  },
  
  /**
   * 模拟插入记录
   * @param {string} table - 表名
   * @param {Object} data - 插入数据
   * @returns {Promise<Object>} - 插入结果
   */
  insert: async (table, data) => {
    console.log(`[Mock] insert into ${table} with data:`, data);
    
    if (!mockData[table]) {
      mockData[table] = [];
    }
    
    const newId = mockData[table].length > 0 
      ? Math.max(...mockData[table].map(item => item.id)) + 1 
      : 1;
    
    const newRecord = { id: newId, ...data };
    mockData[table].push(newRecord);
    
    return { id: newId, affectedRows: 1 };
  },
  
  /**
   * 模拟更新记录
   * @param {string} table - 表名
   * @param {Object} data - 更新数据
   * @param {Object} conditions - 更新条件
   * @returns {Promise<Object>} - 更新结果
   */
  update: async (table, data, conditions) => {
    console.log(`[Mock] update ${table} with data:`, data, 'and conditions:', conditions);
    
    if (!mockData[table]) {
      return { affectedRows: 0 };
    }
    
    let affectedRows = 0;
    const keys = Object.keys(conditions);
    
    mockData[table] = mockData[table].map(item => {
      const matches = keys.every(key => item[key] === conditions[key]);
      
      if (matches) {
        affectedRows++;
        return { ...item, ...data };
      }
      
      return item;
    });
    
    return { affectedRows };
  },
  
  /**
   * 模拟删除记录
   * @param {string} table - 表名
   * @param {Object} conditions - 删除条件
   * @returns {Promise<Object>} - 删除结果
   */
  remove: async (table, conditions) => {
    console.log(`[Mock] remove from ${table} with conditions:`, conditions);
    
    if (!mockData[table]) {
      return { affectedRows: 0 };
    }
    
    const originalLength = mockData[table].length;
    const keys = Object.keys(conditions);
    
    mockData[table] = mockData[table].filter(item => {
      return !keys.every(key => item[key] === conditions[key]);
    });
    
    const affectedRows = originalLength - mockData[table].length;
    return { affectedRows };
  },
  
  /**
   * 模拟计数查询
   * @param {string} table - 表名
   * @param {Object} conditions - 查询条件
   * @returns {Promise<number>} - 记录数量
   */
  count: async (table, conditions = {}) => {
    console.log(`[Mock] count from ${table} with conditions:`, conditions);
    
    if (!mockData[table]) {
      return 0;
    }
    
    if (Object.keys(conditions).length === 0) {
      return mockData[table].length;
    }
    
    const keys = Object.keys(conditions);
    const count = mockData[table].filter(item => {
      return keys.every(key => item[key] === conditions[key]);
    }).length;
    
    return count;
  },
  
  /**
   * 模拟SQL查询
   * @param {string} sql - SQL语句
   * @param {Array} params - 查询参数
   * @returns {Promise<Array>} - 查询结果
   */
  query: async (sql, params = []) => {
    console.log(`[Mock] SQL query: ${sql}`);
    console.log(`[Mock] SQL params:`, params);
    
    // 返回一些模拟数据，这里只是简单示例
    if (sql.includes('SELECT 1 + 1')) {
      return [{ solution: 2 }];
    }
    
    if (sql.includes('SELECT * FROM users')) {
      return mockData.users.slice(0, 10);
    }
    
    return [];
  },
  
  /**
   * 模拟事务
   * @param {Function} callback - 回调函数
   * @returns {Promise<any>} - 执行结果
   */
  transaction: async (callback) => {
    console.log(`[Mock] Starting transaction`);
    
    const connection = {
      execute: async (sql, params) => {
        console.log(`[Mock] Transaction SQL: ${sql}`);
        console.log(`[Mock] Transaction params:`, params);
        
        // 模拟插入操作返回值
        if (sql.includes('INSERT INTO')) {
          return [{ insertId: 999, affectedRows: 1 }];
        }
        
        // 模拟更新或删除操作返回值
        if (sql.includes('UPDATE') || sql.includes('DELETE')) {
          return [{ affectedRows: 1 }];
        }
        
        return [[]];
      },
      commit: async () => console.log(`[Mock] Transaction committed`),
      rollback: async () => console.log(`[Mock] Transaction rolled back`),
      release: () => console.log(`[Mock] Connection released`)
    };
    
    try {
      const result = await callback(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }
};

// 自定义用户服务（使用模拟数据库）
const UserService = {
  async findByPhone(phone) {
    return await mockDb.findOne('users', { phone });
  },
  
  async findById(id) {
    return await mockDb.findOne('users', { id });
  },
  
  async create(userData) {
    return await mockDb.insert('users', userData);
  },
  
  async update(id, userData) {
    return await mockDb.update('users', userData, { id });
  },
  
  async getList(options = {}) {
    const { page = 1, pageSize = 10, status, keyword } = options;
    
    let conditions = {};
    if (status !== undefined) {
      conditions.status = status;
    }
    
    let users;
    
    if (keyword) {
      // 假设这是一个复杂查询，这里简化处理
      users = await mockDb.find('users', {
        conditions,
        limit: pageSize,
        offset: (page - 1) * pageSize
      });
      
      // 简单模拟关键字过滤
      users = users.filter(user => 
        user.phone.includes(keyword) || 
        user.nickname.includes(keyword)
      );
    } else {
      users = await mockDb.find('users', {
        conditions,
        limit: pageSize,
        offset: (page - 1) * pageSize
      });
    }
    
    const total = await mockDb.count('users', conditions);
    
    return {
      list: users,
      pagination: {
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }
};

// 自定义钱包服务（使用模拟数据库）
const WalletService = {
  async getWallet(userId) {
    let wallet = await mockDb.findOne('wallets', { user_id: userId });
    
    if (!wallet) {
      // 如果钱包不存在，创建一个新钱包
      await mockDb.insert('wallets', { user_id: userId, balance: 0 });
      wallet = await mockDb.findOne('wallets', { user_id: userId });
    }
    
    return wallet;
  },
  
  async addTransaction(transactionData) {
    return await mockDb.transaction(async (connection) => {
      // 模拟事务处理
      console.log(`[Mock] Processing transaction for user ${transactionData.userId}`);
      
      return {
        transactionId: 999,
        affectedWallets: 1
      };
    });
  }
};

// 运行示例测试
async function runTests() {
  console.log('开始模拟测试统一数据库接口...');
  
  try {
    // 测试用户服务
    console.log('\n1. 测试用户服务');
    
    // 查询用户
    const user = await UserService.findByPhone('13800138000');
    console.log('  查询用户结果:', user);
    
    // 创建用户
    const newUser = await UserService.create({
      phone: '13900009999',
      nickname: '新用户',
      password: 'password123',
      status: 1
    });
    console.log('  创建用户结果:', newUser);
    
    // 更新用户
    const updateResult = await UserService.update(2, { nickname: '已更新用户名' });
    console.log('  更新用户结果:', updateResult);
    
    // 获取用户列表
    const userList = await UserService.getList({ page: 1, pageSize: 10 });
    console.log('  用户列表查询结果:', userList);
    
    // 测试钱包服务
    console.log('\n2. 测试钱包服务');
    
    // 获取钱包
    const wallet = await WalletService.getWallet(1);
    console.log('  获取钱包结果:', wallet);
    
    // 添加交易
    const transaction = await WalletService.addTransaction({
      userId: 1,
      type: 1, // 充值
      amount: 100,
      balance: 1100
    });
    console.log('  添加交易结果:', transaction);
    
    console.log('\n所有模拟测试完成！');
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 运行测试
runTests(); 