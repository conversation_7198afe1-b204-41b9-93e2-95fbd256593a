const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');

// 获取用户列表
router.get('/list', userController.getUserList);

// 获取用户详情
router.get('/:id', userController.getUserDetail);

// 更新用户状态
router.put('/:id/status', userController.updateUserStatus);

// 删除用户
router.delete('/:id', userController.deleteUser);

// 获取用户统计
router.get('/stats/overview', userController.getUserStats);

module.exports = router;
