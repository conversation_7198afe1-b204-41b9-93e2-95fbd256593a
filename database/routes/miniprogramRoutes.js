const express = require('express');
const miniprogramController = require('../controllers/miniprogramController');

const router = express.Router();

// 小程序用户相关接口
router.post('/user/register', miniprogramController.registerUser);
router.post('/user/login', miniprogramController.loginUser);
router.get('/user/profile', miniprogramController.getUserProfile);
router.put('/user/profile', miniprogramController.updateUserProfile);

// 小程序订单相关接口
router.post('/order/create', miniprogramController.createOrder);
router.get('/order/list', miniprogramController.getOrderList);
router.get('/order/:id', miniprogramController.getOrderDetail);
router.put('/order/:id/status', miniprogramController.updateOrderStatus);

// 小程序驿站相关接口
router.get('/station/list', miniprogramController.getStationList);
router.get('/station/:id', miniprogramController.getStationDetail);
router.get('/station/nearby', miniprogramController.getNearbyStations);

// 小程序服务相关接口
router.get('/service/list', miniprogramController.getServiceList);
router.post('/service/book', miniprogramController.bookService);

// 小程序支付相关接口
router.post('/payment/create', miniprogramController.createPayment);
router.post('/payment/notify', miniprogramController.paymentNotify);

module.exports = router;
