const express = require('express');
const router = express.Router();
const stationTransferController = require('../controllers/stationTransferController');

// 获取转让列表
router.get('/list', stationTransferController.getTransferList);

// 获取转让详情
router.get('/:id', stationTransferController.getTransferDetail);

// 创建转让信息
router.post('/', stationTransferController.createTransfer);

// 更新转让信息
router.put('/:id', stationTransferController.updateTransfer);

// 审核转让信息
router.post('/:id/approve', stationTransferController.approveTransfer);

// 删除转让信息
router.delete('/:id', stationTransferController.deleteTransfer);

// 获取转让统计
router.get('/stats/overview', stationTransferController.getTransferStats);

module.exports = router;
