const express = require('express');
const router = express.Router();
const stationController = require('../controllers/stationController');

// 获取驿站列表
router.get('/list', stationController.getStationList);

// 获取驿站详情
router.get('/:id', stationController.getStationDetail);

// 创建驿站
router.post('/', stationController.createStation);

// 更新驿站信息
router.put('/:id', stationController.updateStation);

// 审核驿站
router.post('/:id/approve', stationController.approveStation);

// 删除驿站
router.delete('/:id', stationController.deleteStation);

// 获取驿站统计
router.get('/stats/overview', stationController.getStationStats);

module.exports = router;
