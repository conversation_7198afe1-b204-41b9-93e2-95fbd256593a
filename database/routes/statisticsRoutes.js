const express = require('express');
const statisticsController = require('../controllers/statisticsController');

const router = express.Router();

// 获取仪表板数据
router.get('/dashboard', statisticsController.getDashboardData);

// 获取统计概览
router.get('/overview', statisticsController.getOverview);

// 获取订单趋势
router.get('/orderTrend', statisticsController.getOrderTrend);

// 获取订单类型分布
router.get('/orderTypeDistribution', statisticsController.getOrderTypeDistribution);

// 获取用户增长趋势
router.get('/userGrowthTrend', statisticsController.getUserGrowthTrend);

// 获取用户地区分布
router.get('/userRegionDistribution', statisticsController.getUserRegionDistribution);

// 获取热门驿站排行
router.get('/hotStations', statisticsController.getHotStations);

// 导出统计数据
router.get('/export', statisticsController.exportData);

module.exports = router;
