const express = require('express');
const authController = require('../controllers/authController');
const authMiddleware = require('../middlewares/authMiddleware');

const router = express.Router();

// 检查手机号是否已注册
router.get('/check-phone', authController.checkPhoneExists);

// 发送短信验证码
router.post('/send-code', authController.sendSmsCode);

// 微信登录
router.post('/wx-login', authController.wxLogin);

// 手机号登录
router.post('/phone-login', authController.phoneLogin);

// 密码登录
router.post('/password-login', authController.passwordLogin);

// 用户注册
router.post('/register', authController.register);

// 重置密码
router.post('/reset-password', authController.resetPassword);

// 登出
router.post('/logout', authController.logout);

// 刷新Token（需要身份验证）
router.post('/refresh-token', authMiddleware.protect, authController.refreshToken);

// 获取登录状态（需要身份验证）
router.get('/status', authMiddleware.protect, authController.getLoginStatus);

module.exports = router; 