/**
 * MongoDB到MySQL数据迁移脚本
 * 
 * 使用方法:
 * 1. 确保MongoDB和MySQL都已正确配置
 * 2. 运行 node database/scripts/migrateDb.js
 */

// 导入所需模块
require('dotenv').config();
const { MongoClient } = require('mongodb');
const { sequelize } = require('../index');
const User = require('../models/User');

// MongoDB连接配置
const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/yizhanbang';

// 迁移用户数据
async function migrateUsers() {
  console.log('开始迁移用户数据...');
  const mongoClient = new MongoClient(MONGO_URI);
  
  try {
    // 连接到MongoDB
    await mongoClient.connect();
    console.log('MongoDB连接成功');
    
    const db = mongoClient.db();
    const mongoUsers = await db.collection('users').find({}).toArray();
    
    console.log(`找到 ${mongoUsers.length} 个用户记录需要迁移`);
    
    // 连接到MySQL
    await sequelize.authenticate();
    console.log('MySQL连接成功');
    
    // 确保用户表存在
    await User.sync();
    
    // 迁移每个用户
    let successCount = 0;
    let errorCount = 0;
    
    for (const mongoUser of mongoUsers) {
      try {
        // 转换用户数据格式
        const userData = {
          nickname: mongoUser.nickname,
          avatar: mongoUser.avatar || '',
          gender: mongoUser.gender || 0,
          phone: mongoUser.phone,
          email: mongoUser.email,
          password: mongoUser.password, // 密码已经加密，可以直接迁移
          openid: mongoUser.openid,
          unionid: mongoUser.unionid,
          sessionKey: mongoUser.sessionKey,
          mpOpenid: mongoUser.mpOpenid,
          isActive: mongoUser.isActive !== undefined ? mongoUser.isActive : true,
          isVerified: mongoUser.isVerified || false,
          roles: mongoUser.roles || ['user'],
          tags: mongoUser.tags || [],
          registerIp: mongoUser.registerIp,
          lastLoginIp: mongoUser.lastLoginIp,
          lastLoginTime: mongoUser.lastLoginTime,
          loginCount: mongoUser.loginCount || 0,
          registerSource: mongoUser.registerSource || 'other',
          orderCount: mongoUser.orderCount || 0,
          realNameAuth: mongoUser.realNameAuth || {
            isVerified: false,
            realName: null,
            idCardNumber: null,
            idCardFrontUrl: null,
            idCardBackUrl: null,
            verifyTime: null
          },
          createdAt: mongoUser.createdAt || new Date(),
          updatedAt: mongoUser.updatedAt || new Date()
        };
        
        // 检查用户是否已存在
        const existingUser = await User.findOne({
          where: {
            [sequelize.Op.or]: [
              { phone: userData.phone },
              { email: userData.email },
              { mpOpenid: userData.mpOpenid },
              { openid: userData.openid }
            ].filter(condition => 
              condition.phone || condition.email || condition.mpOpenid || condition.openid
            )
          }
        });
        
        if (existingUser) {
          console.log(`用户已存在，跳过: ${userData.nickname || userData.phone || userData.email || userData.mpOpenid}`);
          continue;
        }
        
        // 创建新用户记录
        await User.create(userData, { hooks: false }); // 禁用钩子以保留原始密码哈希
        successCount++;
        console.log(`成功迁移用户: ${userData.nickname || userData.phone || userData.email || userData.mpOpenid}`);
      } catch (error) {
        console.error(`迁移用户失败: ${mongoUser._id}`, error);
        errorCount++;
      }
    }
    
    console.log(`用户迁移完成: 成功 ${successCount}, 失败 ${errorCount}`);
  } catch (error) {
    console.error('迁移过程中出错:', error);
  } finally {
    // 关闭连接
    await mongoClient.close();
    console.log('MongoDB连接已关闭');
  }
}

// 主函数
async function migrate() {
  try {
    // 迁移用户数据
    await migrateUsers();
    
    // 可以添加其他集合的迁移
    // await migrateOtherCollection();
    
    console.log('数据迁移完成');
    await sequelize.close();
    process.exit(0);
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  }
}

// 执行迁移
migrate(); 