/**
 * 数据库初始化脚本
 * 用于首次安装或重置数据库
 * 
 * 使用方法:
 * 1. 确保MySQL已正确配置并运行
 * 2. 运行 node database/scripts/initDB.js
 */

require('dotenv').config();
const { sequelize } = require('../index');
const User = require('../models/User');
const bcrypt = require('bcryptjs');

// 初始化数据库
async function initDatabase() {
  try {
    console.log('连接到数据库...');
    await sequelize.authenticate();
    console.log('MySQL连接成功');
    
    // 同步所有模型
    console.log('开始同步数据库模型...');
    await sequelize.sync({ force: true }); // force: true 会删除现有表并重新创建
    console.log('数据库模型同步完成');
    
    // 创建默认管理员账户
    console.log('创建默认管理员账户...');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);
    
    await User.create({
      nickname: '系统管理员',
      phone: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      roles: ['admin', 'super_admin'],
      isActive: true,
      isVerified: true,
      registerSource: 'system',
      registerIp: '127.0.0.1',
      lastLoginIp: '127.0.0.1',
      lastLoginTime: new Date(),
      loginCount: 0
    });
    
    console.log('默认管理员账户创建成功');
    console.log('- 用户名: <EMAIL>');
    console.log('- 密码: admin123');
    
    // 创建测试用户
    console.log('创建测试用户...');
    await User.create({
      nickname: '测试用户',
      phone: '13800138000',
      email: '<EMAIL>',
      password: await bcrypt.hash('123456', salt),
      roles: ['user'],
      isActive: true,
      isVerified: true,
      registerSource: 'system',
      registerIp: '127.0.0.1',
      lastLoginIp: '127.0.0.1',
      lastLoginTime: new Date(),
      loginCount: 0
    });
    
    console.log('测试用户创建成功');
    console.log('- 手机号: 13800138000');
    console.log('- 密码: 123456');
    
    // 添加驿站认证管理员
    console.log('创建驿站认证管理员...');
    await User.create({
      nickname: '认证管理员',
      phone: '13900139000',
      email: '<EMAIL>',
      password: await bcrypt.hash('123456', salt),
      roles: ['admin', 'verifier'],
      isActive: true,
      isVerified: true,
      registerSource: 'system',
      registerIp: '127.0.0.1',
      lastLoginIp: '127.0.0.1',
      lastLoginTime: new Date(),
      loginCount: 0
    });
    
    console.log('认证管理员创建成功');
    console.log('- 手机号: 13900139000');
    console.log('- 密码: 123456');
    
    console.log('数据库初始化完成');
  } catch (error) {
    console.error('数据库初始化失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    process.exit(0);
  }
}

// 执行初始化
initDatabase(); 