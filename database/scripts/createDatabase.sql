-- 创建数据库
CREATE DATABASE IF NOT EXISTS yizhanbang CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE yizhanbang;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  nickname VA<PERSON><PERSON><PERSON>(255),
  avatar VARCHAR(255),
  gender TINYINT NOT NULL DEFAULT 0,
  phone VARCHAR(20) UNIQUE,
  email VARCHAR(255) UNIQUE,
  password VARCHAR(255),
  openid VARCHAR(255) UNIQUE,
  unionid VARCHAR(255) UNIQUE,
  sessionKey VARCHAR(255),
  mpOpenid VARCHAR(255) UNIQUE,
  isActive BOOLEAN NOT NULL DEFAULT TRUE,
  isVerified BOOLEAN NOT NULL DEFAULT FALSE,
  roles JSON NOT NULL,
  tags JSON,
  registerIp VARCHAR(50),
  lastLoginIp VARCHAR(50),
  lastLoginTime DATETIME,
  loginCount INT NOT NULL DEFAULT 0,
  registerSource VARCHAR(20) NOT NULL DEFAULT 'other',
  orderCount INT NOT NULL DEFAULT 0,
  realNameAuth JSON NOT NULL,
  createdAt DATETIME NOT NULL,
  updatedAt DATETIME NOT NULL,
  INDEX idx_createdAt (createdAt),
  INDEX idx_updatedAt (updatedAt),
  INDEX idx_phone (phone),
  INDEX idx_email (email),
  INDEX idx_openid (openid),
  INDEX idx_mpOpenid (mpOpenid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建默认管理员用户（密码: admin123）
-- 注意：实际应用中应该使用加密后的密码
INSERT INTO users (
  nickname, phone, email, password, roles, isActive, isVerified, 
  registerSource, registerIp, lastLoginIp, lastLoginTime, loginCount,
  realNameAuth, createdAt, updatedAt
) VALUES (
  '系统管理员', 'admin', '<EMAIL>', 
  '$2a$10$qBOsRvSeLPU9jAVF2WUhZO5fe1K/4ZD0W0Cd.eT2PeULrPFo1Dg1S', -- 加密后的 admin123
  '["admin", "super_admin"]', 1, 1, 'system', '127.0.0.1', '127.0.0.1', 
  NOW(), 0, '{"isVerified": false}', NOW(), NOW()
);

-- 添加测试用户（密码: 123456）
INSERT INTO users (
  nickname, phone, email, password, roles, isActive, isVerified, 
  registerSource, registerIp, lastLoginIp, lastLoginTime, loginCount,
  realNameAuth, createdAt, updatedAt
) VALUES (
  '测试用户', '13800138000', '<EMAIL>', 
  '$2a$10$PJQwdmI5Cu.aTRNnxLjsIeB8o0uJ2xFUWmTfKKg2V9pC3bU9TfOji', -- 加密后的 123456
  '["user"]', 1, 1, 'system', '127.0.0.1', '127.0.0.1', 
  NOW(), 0, '{"isVerified": false}', NOW(), NOW()
);

-- 创建认证管理员（密码: 123456）
INSERT INTO users (
  nickname, phone, email, password, roles, isActive, isVerified, 
  registerSource, registerIp, lastLoginIp, lastLoginTime, loginCount,
  realNameAuth, createdAt, updatedAt
) VALUES (
  '认证管理员', '13900139000', '<EMAIL>', 
  '$2a$10$PJQwdmI5Cu.aTRNnxLjsIeB8o0uJ2xFUWmTfKKg2V9pC3bU9TfOji', -- 加密后的 123456
  '["admin", "verifier"]', 1, 1, 'system', '127.0.0.1', '127.0.0.1', 
  NOW(), 0, '{"isVerified": false}', NOW(), NOW()
);

-- 添加索引
ALTER TABLE users ADD INDEX idx_roles ((CAST(roles AS CHAR(255))));

-- 数据库初始化完成提示
SELECT 'MySQL数据库初始化完成' AS message; 