-- 创建数据库
CREATE DATABASE IF NOT EXISTS yizhanbang DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE yizhanbang;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
  id VARCHAR(32) PRIMARY KEY,
  openid VARCHAR(32) UNIQUE,
  unionid VARCHAR(32),
  phone VARCHAR(20),
  nickname VARCHAR(50),
  avatar VARCHAR(255),
  create_time DATETIME NOT NULL,
  last_login_time DATETIME,
  is_verified BOOLEAN DEFAULT FALSE,
  roles JSON,
  points INT DEFAULT 0,
  balance DECIMAL(10,2) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 短信验证码表
CREATE TABLE IF NOT EXISTS sms_codes (
  id VARCHAR(32) PRIMARY KEY,
  phone VARCHAR(20) NOT NULL,
  code VARCHAR(10) NOT NULL,
  create_time DATETIME NOT NULL,
  expire_time DATETIME NOT NULL,
  used BOOLEAN DEFAULT FALSE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 驿站信息表
CREATE TABLE IF NOT EXISTS stations (
  id VARCHAR(32) PRIMARY KEY,
  title VARCHAR(100) NOT NULL,
  address VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  price_unit VARCHAR(20),
  contact_name VARCHAR(50),
  contact_phone VARCHAR(20),
  images JSON,
  publish_time DATETIME NOT NULL,
  status VARCHAR(20) DEFAULT 'ACTIVE',
  user_id VARCHAR(32) NOT NULL,
  buyer_id VARCHAR(32),
  deal_time DATETIME,
  views INT DEFAULT 0,
  update_time DATETIME,
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 设备信息表
CREATE TABLE IF NOT EXISTS devices (
  id VARCHAR(32) PRIMARY KEY,
  title VARCHAR(100) NOT NULL,
  type VARCHAR(50) NOT NULL,
  brand VARCHAR(50),
  price DECIMAL(10,2) NOT NULL,
  condition VARCHAR(20),
  description TEXT,
  images JSON,
  user_id VARCHAR(32) NOT NULL,
  status VARCHAR(20) DEFAULT 'ACTIVE',
  buyer_id VARCHAR(32),
  deal_time DATETIME,
  publish_time DATETIME NOT NULL,
  update_time DATETIME,
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 服务信息表
CREATE TABLE IF NOT EXISTS services (
  id VARCHAR(32) PRIMARY KEY,
  title VARCHAR(100) NOT NULL,
  time VARCHAR(100),
  address VARCHAR(255),
  price DECIMAL(10,2) NOT NULL,
  description TEXT,
  user_id VARCHAR(32) NOT NULL,
  status VARCHAR(20) DEFAULT 'ACTIVE',
  publish_time DATETIME NOT NULL,
  update_time DATETIME,
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 招聘信息表
CREATE TABLE IF NOT EXISTS jobs (
  id VARCHAR(32) PRIMARY KEY,
  title VARCHAR(100) NOT NULL,
  company VARCHAR(100),
  salary VARCHAR(50),
  requirements TEXT,
  address VARCHAR(255),
  description TEXT,
  user_id VARCHAR(32) NOT NULL,
  status VARCHAR(20) DEFAULT 'ACTIVE',
  promote_status VARCHAR(20) DEFAULT 'NORMAL',
  promote_expire_time DATETIME,
  publish_time DATETIME NOT NULL,
  update_time DATETIME,
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
  id VARCHAR(32) PRIMARY KEY,
  order_no VARCHAR(32) UNIQUE NOT NULL,
  user_id VARCHAR(32) NOT NULL,
  seller_id VARCHAR(32),
  provider_id VARCHAR(32),
  order_type VARCHAR(20) NOT NULL,
  item_id VARCHAR(32) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  shipping_fee DECIMAL(10,2) DEFAULT 0,
  discount DECIMAL(10,2) DEFAULT 0,
  status VARCHAR(20) NOT NULL,
  address_id VARCHAR(32),
  transaction_id VARCHAR(64),
  pay_time DATETIME,
  pay_method VARCHAR(20),
  delivery_method VARCHAR(50),
  tracking_number VARCHAR(50),
  complete_time DATETIME,
  refund_status VARCHAR(20) DEFAULT 'NO_REFUND',
  refund_time DATETIME,
  refund_amount DECIMAL(10,2),
  create_time DATETIME NOT NULL,
  update_time DATETIME,
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 支付日志表
CREATE TABLE IF NOT EXISTS payment_logs (
  id VARCHAR(32) PRIMARY KEY,
  order_id VARCHAR(32),
  order_no VARCHAR(32),
  user_id VARCHAR(32),
  amount DECIMAL(10,2),
  operation VARCHAR(50) NOT NULL COMMENT '操作类型',
  status VARCHAR(20) NOT NULL COMMENT '状态',
  message TEXT COMMENT '消息说明',
  create_time DATETIME NOT NULL,
  update_time DATETIME,
  raw_data TEXT COMMENT '原始数据',
  notify_data TEXT COMMENT '通知数据',
  request_data TEXT COMMENT '请求数据',
  response_data TEXT COMMENT '响应数据',
  error_message TEXT COMMENT '错误信息',
  INDEX (order_id),
  INDEX (order_no),
  INDEX (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 用户地址表
CREATE TABLE IF NOT EXISTS addresses (
  id VARCHAR(32) PRIMARY KEY,
  user_id VARCHAR(32) NOT NULL,
  name VARCHAR(50) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  province VARCHAR(50) NOT NULL,
  city VARCHAR(50) NOT NULL,
  district VARCHAR(50) NOT NULL,
  address VARCHAR(255) NOT NULL,
  is_default BOOLEAN DEFAULT FALSE,
  create_time DATETIME NOT NULL,
  update_time DATETIME,
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 消息表
CREATE TABLE IF NOT EXISTS messages (
  id VARCHAR(32) PRIMARY KEY,
  user_id VARCHAR(32) NOT NULL,
  type VARCHAR(50) NOT NULL,
  title VARCHAR(100) NOT NULL,
  content TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  order_id VARCHAR(32),
  create_time DATETIME NOT NULL,
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 评价表
CREATE TABLE IF NOT EXISTS reviews (
  id VARCHAR(32) PRIMARY KEY,
  user_id VARCHAR(32) NOT NULL,
  item_id VARCHAR(32) NOT NULL,
  item_type VARCHAR(20) NOT NULL,
  content TEXT,
  rating INT NOT NULL,
  images JSON,
  create_time DATETIME NOT NULL,
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建索引
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_seller_id ON orders(seller_id);
CREATE INDEX idx_orders_provider_id ON orders(provider_id);
CREATE INDEX idx_orders_item_id ON orders(item_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_stations_user_id ON stations(user_id);
CREATE INDEX idx_stations_status ON stations(status);
CREATE INDEX idx_devices_user_id ON devices(user_id);
CREATE INDEX idx_devices_status ON devices(status);
CREATE INDEX idx_services_user_id ON services(user_id);
CREATE INDEX idx_services_status ON services(status);
CREATE INDEX idx_jobs_user_id ON jobs(user_id);
CREATE INDEX idx_jobs_status ON jobs(status);
CREATE INDEX idx_users_openid ON users(openid);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_sms_codes_phone ON sms_codes(phone);
CREATE INDEX idx_payment_logs_operation ON payment_logs(operation);
CREATE INDEX idx_payment_logs_status ON payment_logs(status); 