const jwt = require('jsonwebtoken');
const User = require('../models/User');

// JWT配置
const JWT_SECRET = process.env.JWT_SECRET || 'yizhanbang-secret-key';

/**
 * 身份验证中间件
 * 检查请求中的JWT令牌并验证用户身份
 */
exports.protect = async (req, res, next) => {
  try {
    let token;
    
    // 从请求头中获取令牌
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }
    
    // 检查是否存在令牌
    if (!token) {
      return res.status(401).json({
        code: 401,
        message: '未授权，请登录'
      });
    }
    
    try {
      // 验证令牌
      const decoded = jwt.verify(token, JWT_SECRET);
      
      // 获取用户信息
      const user = await User.findByPk(decoded.id);
      
      if (!user) {
        return res.status(401).json({
          code: 401,
          message: '用户不存在，请重新登录'
        });
      }
      
      // 将用户信息添加到请求对象中
      req.user = {
        id: user.id,
        roles: user.roles
      };
      
      next();
    } catch (error) {
      console.error('JWT验证错误:', error);
      
      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          code: 401,
          message: '登录已过期，请重新登录'
        });
      }
      
      return res.status(401).json({
        code: 401,
        message: '无效的令牌，请重新登录'
      });
    }
  } catch (error) {
    console.error('认证中间件错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试'
    });
  }
};

/**
 * 角色授权中间件
 * 检查用户是否具有指定的角色
 * @param  {...String} roles 允许的角色列表
 */
exports.authorize = (...roles) => {
  return (req, res, next) => {
    // 检查用户是否存在
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        message: '未授权，请登录'
      });
    }
    
    // 检查用户是否具有允许的角色
    const hasRole = req.user.roles.some(role => roles.includes(role));
    
    if (!hasRole) {
      return res.status(403).json({
        code: 403,
        message: '权限不足，无法访问此资源'
      });
    }
    
    next();
  };
}; 