/**
 * 验证请求参数中间件
 * 支持验证 body, query, params
 */
exports.validateRequest = (schema) => {
  return (req, res, next) => {
    const errors = [];
    
    // 验证请求体
    if (schema.body) {
      const bodyErrors = validateObject(req.body, schema.body);
      errors.push(...bodyErrors.map(err => `body.${err}`));
    }
    
    // 验证查询参数
    if (schema.query) {
      const queryErrors = validateObject(req.query, schema.query);
      errors.push(...queryErrors.map(err => `query.${err}`));
    }
    
    // 验证URL参数
    if (schema.params) {
      const paramsErrors = validateObject(req.params, schema.params);
      errors.push(...paramsErrors.map(err => `params.${err}`));
    }
    
    if (errors.length > 0) {
      return res.status(400).json({
        code: 1,
        message: '请求参数错误',
        errors
      });
    }
    
    next();
  };
};

/**
 * 验证对象字段
 * @param {Object} obj 待验证对象
 * @param {Object} schema 验证规则
 * @returns {Array} 错误信息数组
 */
function validateObject(obj, schema) {
  const errors = [];
  
  for (const [field, rules] of Object.entries(schema)) {
    // 必填字段检查
    if (rules.required && (obj[field] === undefined || obj[field] === null || obj[field] === '')) {
      errors.push(`${field} 是必填字段`);
      continue;
    }
    
    // 如果字段不存在且非必填，则跳过后续验证
    if (obj[field] === undefined || obj[field] === null) {
      continue;
    }
    
    // 类型检查
    if (rules.type && typeof obj[field] !== rules.type) {
      errors.push(`${field} 应为 ${rules.type} 类型`);
    }
    
    // 枚举值检查
    if (rules.enum && !rules.enum.includes(obj[field])) {
      errors.push(`${field} 的值必须是 ${rules.enum.join(', ')} 之一`);
    }
    
    // 长度检查
    if (rules.minLength !== undefined && obj[field].length < rules.minLength) {
      errors.push(`${field} 长度不能小于 ${rules.minLength}`);
    }
    
    if (rules.maxLength !== undefined && obj[field].length > rules.maxLength) {
      errors.push(`${field} 长度不能大于 ${rules.maxLength}`);
    }
    
    // 正则表达式检查
    if (rules.pattern && !new RegExp(rules.pattern).test(obj[field])) {
      errors.push(`${field} 格式不正确`);
    }
    
    // 数值范围检查
    if (rules.min !== undefined && obj[field] < rules.min) {
      errors.push(`${field} 不能小于 ${rules.min}`);
    }
    
    if (rules.max !== undefined && obj[field] > rules.max) {
      errors.push(`${field} 不能大于 ${rules.max}`);
    }
    
    // 自定义验证函数
    if (rules.validate && typeof rules.validate === 'function') {
      const result = rules.validate(obj[field]);
      if (result !== true) {
        errors.push(`${field} ${result}`);
      }
    }
  }
  
  return errors;
} 