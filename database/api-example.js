/**
 * 统一数据库接口使用示例
 * 演示如何在小程序和后台管理系统中使用统一数据库接口
 */

const db = require('./db-interface');

/**
 * 用户模块示例
 * 封装用户相关的数据库操作
 */
const UserService = {
  /**
   * 根据手机号查找用户
   * @param {string} phone - 手机号
   * @returns {Promise<Object>} 用户信息
   */
  async findByPhone(phone) {
    return await db.findOne('users', { phone });
  },
  
  /**
   * 根据ID查找用户
   * @param {number} id - 用户ID
   * @returns {Promise<Object>} 用户信息
   */
  async findById(id) {
    return await db.findOne('users', { id });
  },
  
  /**
   * 创建新用户
   * @param {Object} userData - 用户数据
   * @returns {Promise<Object>} 创建结果
   */
  async create(userData) {
    return await db.insert('users', userData);
  },
  
  /**
   * 更新用户信息
   * @param {number} id - 用户ID
   * @param {Object} userData - 更新的用户数据
   * @returns {Promise<Object>} 更新结果
   */
  async update(id, userData) {
    return await db.update('users', userData, { id });
  },
  
  /**
   * 获取用户列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 用户列表
   */
  async getList(options = {}) {
    const { page = 1, pageSize = 10, status, keyword } = options;
    
    let conditions = {};
    if (status !== undefined) {
      conditions.status = status;
    }
    
    let users;
    
    if (keyword) {
      // 如果有关键字，使用自定义SQL查询
      const sql = `
        SELECT * FROM users 
        WHERE (phone LIKE ? OR nickname LIKE ?)
        ${status !== undefined ? 'AND status = ?' : ''}
        ORDER BY id DESC
        LIMIT ? OFFSET ?
      `;
      
      const params = [
        `%${keyword}%`,
        `%${keyword}%`
      ];
      
      if (status !== undefined) {
        params.push(status);
      }
      
      params.push(pageSize, (page - 1) * pageSize);
      
      users = await db.query(sql, params);
    } else {
      // 没有关键字，使用find方法
      users = await db.find('users', {
        conditions,
        orderBy: 'id DESC',
        limit: pageSize,
        offset: (page - 1) * pageSize
      });
    }
    
    // 获取总数
    const total = await db.count('users', conditions);
    
    return {
      list: users,
      pagination: {
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }
};

/**
 * 钱包模块示例
 * 封装钱包相关的数据库操作
 */
const WalletService = {
  /**
   * 获取用户钱包
   * @param {number} userId - 用户ID
   * @returns {Promise<Object>} 钱包信息
   */
  async getWallet(userId) {
    let wallet = await db.findOne('wallets', { user_id: userId });
    
    if (!wallet) {
      // 如果钱包不存在，创建一个新钱包
      await db.insert('wallets', { user_id: userId, balance: 0 });
      wallet = await db.findOne('wallets', { user_id: userId });
    }
    
    return wallet;
  },
  
  /**
   * 记录交易
   * @param {Object} transactionData - 交易数据
   * @returns {Promise<Object>} 交易结果
   */
  async addTransaction(transactionData) {
    return await db.transaction(async (connection) => {
      // 1. 插入交易记录
      const [transResult] = await connection.execute(
        `INSERT INTO wallet_transactions 
         (user_id, type, amount, balance, order_id, remark, status) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          transactionData.userId,
          transactionData.type,
          transactionData.amount,
          transactionData.balance,
          transactionData.orderId || null,
          transactionData.remark || '',
          transactionData.status || 1
        ]
      );
      
      const transactionId = transResult.insertId;
      
      // 2. 更新钱包余额
      const [walletResult] = await connection.execute(
        `UPDATE wallets 
         SET balance = balance + ?, 
             ${transactionData.type === 1 ? 'total_income = total_income + ?' : ''}
             ${transactionData.type === 5 ? 'total_withdraw = total_withdraw + ?' : ''}
             updated_at = NOW()
         WHERE user_id = ?`,
        [
          transactionData.amount,
          ...(transactionData.type === 1 ? [transactionData.amount] : []),
          ...(transactionData.type === 5 ? [Math.abs(transactionData.amount)] : []),
          transactionData.userId
        ]
      );
      
      return {
        transactionId,
        affectedWallets: walletResult.affectedRows
      };
    });
  }
};

/**
 * 订单模块示例
 * 封装订单相关的数据库操作
 */
const OrderService = {
  /**
   * 创建订单
   * @param {Object} orderData - 订单数据
   * @param {Array} items - 订单商品
   * @returns {Promise<Object>} 创建结果
   */
  async createOrder(orderData, items) {
    return await db.transaction(async (connection) => {
      // 1. 创建订单
      const [orderResult] = await connection.execute(
        `INSERT INTO orders 
         (order_no, user_id, seller_id, total_amount, pay_amount, order_type, 
          receiver_name, receiver_phone, receiver_address, remark) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          orderData.orderNo,
          orderData.userId,
          orderData.sellerId,
          orderData.totalAmount,
          orderData.payAmount,
          orderData.orderType,
          orderData.receiverName,
          orderData.receiverPhone,
          orderData.receiverAddress,
          orderData.remark || ''
        ]
      );
      
      const orderId = orderResult.insertId;
      
      // 2. 创建订单商品
      for (const item of items) {
        await connection.execute(
          `INSERT INTO order_items 
           (order_id, product_id, product_name, product_cover, price, quantity, total_amount) 
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [
            orderId,
            item.productId,
            item.productName,
            item.productCover,
            item.price,
            item.quantity,
            item.totalAmount
          ]
        );
        
        // 3. 更新商品销量
        await connection.execute(
          `UPDATE products 
           SET sold_count = sold_count + ?, 
               stock = stock - ?,
               updated_at = NOW()
           WHERE id = ?`,
          [item.quantity, item.quantity, item.productId]
        );
      }
      
      return { orderId, orderNo: orderData.orderNo };
    });
  },
  
  /**
   * 获取订单详情
   * @param {number} orderId - 订单ID
   * @returns {Promise<Object>} 订单详情
   */
  async getOrderDetail(orderId) {
    // 查询订单基本信息
    const order = await db.findOne('orders', { id: orderId });
    
    if (!order) {
      return null;
    }
    
    // 查询订单商品
    const items = await db.find('order_items', {
      conditions: { order_id: orderId }
    });
    
    return {
      ...order,
      items
    };
  }
};

// 示例：如何在实际应用中使用这些服务
async function exampleUsage() {
  try {
    // 查询用户
    const user = await UserService.findByPhone('13800138000');
    console.log('查询用户:', user ? user.nickname : '未找到用户');
    
    // 获取用户钱包
    if (user) {
      const wallet = await WalletService.getWallet(user.id);
      console.log('用户钱包余额:', wallet.balance);
    }
    
    // 其他示例...
    
  } catch (error) {
    console.error('示例执行错误:', error);
  } finally {
    // 关闭连接池
    await db.pool.end();
  }
}

// 导出服务，可供小程序和后台管理系统使用
module.exports = {
  UserService,
  WalletService,
  OrderService
}; 