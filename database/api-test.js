/**
 * 统一数据库接口测试工具
 * 用于测试database/db-interface.js提供的各种数据库操作功能
 */

const db = require('./db-interface');
const config = require('./config');

// 测试配置
const TEST_TABLE = 'users';
const TEST_DATA = {
  phone: '13900001111',
  nickname: '测试用户',
  password: '$2a$10$NlkKxlEHf8TLtQCFRwCuw.83C2xgg2qW9lgE9jXFJ.iKsFhjL1Jd.', // 加密后的admin123
  status: 1
};

// 测试函数
async function runTests() {
  console.log('开始测试统一数据库接口...');
  
  try {
    // 测试数据库连接
    console.log('1. 测试数据库连接');
    await testConnection();
    
    // 测试插入操作
    console.log('\n2. 测试插入操作');
    const insertedId = await testInsert();
    
    // 测试查询操作
    console.log('\n3. 测试查询操作');
    await testFind(insertedId);
    
    // 测试更新操作
    console.log('\n4. 测试更新操作');
    await testUpdate(insertedId);
    
    // 测试计数操作
    console.log('\n5. 测试计数操作');
    await testCount();
    
    // 测试事务操作
    console.log('\n6. 测试事务操作');
    await testTransaction();
    
    // 测试删除操作
    console.log('\n7. 测试删除操作');
    await testDelete(insertedId);
    
    console.log('\n所有测试完成！');
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  } finally {
    // 关闭连接池
    await db.pool.end();
  }
}

// 测试数据库连接
async function testConnection() {
  try {
    const result = await db.query('SELECT 1 + 1 AS solution');
    console.log('  ✅ 数据库连接成功:', result[0].solution === 2 ? '2 (正确)' : result[0].solution);
  } catch (error) {
    console.error('  ❌ 数据库连接失败:', error.message);
    throw error;
  }
}

// 测试插入操作
async function testInsert() {
  try {
    const result = await db.insert(TEST_TABLE, TEST_DATA);
    console.log(`  ✅ 插入成功: ID=${result.id}, 受影响行数=${result.affectedRows}`);
    return result.id;
  } catch (error) {
    console.error('  ❌ 插入失败:', error.message);
    throw error;
  }
}

// 测试查询操作
async function testFind(id) {
  try {
    // 测试findOne
    const user = await db.findOne(TEST_TABLE, { id });
    console.log(`  ✅ findOne成功:`, user ? `找到用户 ${user.nickname}` : '未找到用户');
    
    // 测试find
    const users = await db.find(TEST_TABLE, { 
      conditions: { id },
      fields: ['id', 'nickname', 'phone', 'status'],
      limit: 10
    });
    console.log(`  ✅ find成功: 找到${users.length}条记录`);
    
    return user;
  } catch (error) {
    console.error('  ❌ 查询失败:', error.message);
    throw error;
  }
}

// 测试更新操作
async function testUpdate(id) {
  try {
    const newData = {
      nickname: '已更新用户名'
    };
    
    const result = await db.update(TEST_TABLE, newData, { id });
    console.log(`  ✅ 更新成功: 受影响行数=${result.affectedRows}`);
    
    // 验证更新结果
    const user = await db.findOne(TEST_TABLE, { id });
    console.log(`  ✅ 验证更新: nickname=${user.nickname}`);
    
    return result;
  } catch (error) {
    console.error('  ❌ 更新失败:', error.message);
    throw error;
  }
}

// 测试计数操作
async function testCount() {
  try {
    const count = await db.count(TEST_TABLE);
    console.log(`  ✅ 计数成功: ${TEST_TABLE}表共有${count}条记录`);
    
    return count;
  } catch (error) {
    console.error('  ❌ 计数失败:', error.message);
    throw error;
  }
}

// 测试事务操作
async function testTransaction() {
  try {
    const result = await db.transaction(async (connection) => {
      // 在事务中执行多个操作
      const [tempResult] = await connection.execute(
        `INSERT INTO ${TEST_TABLE} (phone, nickname, password, status) VALUES (?, ?, ?, ?)`,
        ['13900002222', '事务测试用户', '$2a$10$NlkKxlEHf8TLtQCFRwCuw.83C2xgg2qW9lgE9jXFJ.iKsFhjL1Jd.', 1]
      );
      
      const tempId = tempResult.insertId;
      
      // 更新刚刚创建的记录
      await connection.execute(
        `UPDATE ${TEST_TABLE} SET nickname = ? WHERE id = ?`,
        ['已在事务中更新', tempId]
      );
      
      // 删除该记录（清理测试数据）
      await connection.execute(
        `DELETE FROM ${TEST_TABLE} WHERE id = ?`,
        [tempId]
      );
      
      return { success: true, tempId };
    });
    
    console.log(`  ✅ 事务测试成功:`, result);
    
    return result;
  } catch (error) {
    console.error('  ❌ 事务测试失败:', error.message);
    throw error;
  }
}

// 测试删除操作
async function testDelete(id) {
  try {
    const result = await db.remove(TEST_TABLE, { id });
    console.log(`  ✅ 删除成功: 受影响行数=${result.affectedRows}`);
    
    // 验证删除结果
    const user = await db.findOne(TEST_TABLE, { id });
    console.log(`  ✅ 验证删除:`, user ? '删除失败，记录仍存在' : '删除成功，记录已不存在');
    
    return result;
  } catch (error) {
    console.error('  ❌ 删除失败:', error.message);
    throw error;
  }
}

// 运行测试
runTests(); 