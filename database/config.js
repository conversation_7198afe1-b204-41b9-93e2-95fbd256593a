/**
 * 统一配置文件
 * 为小程序和后台管理系统提供统一的配置
 */

// 数据库配置
const database = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'yizhanbang',
  dialect: process.env.DB_DIALECT || 'mysql',
  charset: 'utf8mb4',
  collate: 'utf8mb4_unicode_ci',
  connectionLimit: 10,
  waitForConnections: true,
  queueLimit: 0,
  timezone: '+08:00'
};

// 应用配置
const app = {
  port: parseInt(process.env.PORT) || 3000,
  jwtSecret: process.env.JWT_SECRET || 'yizhanbang-secret-key',
  jwtExpire: '7d',
  uploadPath: './uploads',
  staticPath: './static',
  logLevel: process.env.LOG_LEVEL || 'info',
  apiPrefix: '/api'
};

// JWT配置
const jwt = {
  secret: process.env.JWT_SECRET || 'yizhanbang-secret-key',
  refreshSecret: process.env.JWT_REFRESH_SECRET || 'yizhanbang-refresh-secret',
  expiresIn: '1d',
  refreshExpiresIn: '7d'
};

// 微信小程序配置
const wechat = {
  appId: process.env.WX_APP_ID || '',
  appSecret: process.env.WX_APP_SECRET || '',
  mchId: process.env.WX_MCH_ID || '',
  mchKey: process.env.WX_MCH_KEY || '',
  notifyUrl: process.env.WX_NOTIFY_URL || ''
};

// 支付配置
const payment = {
  wxpay: {
    enabled: process.env.ENABLE_WXPAY === 'true' || false,
    sandbox: process.env.WXPAY_SANDBOX === 'true' || false
  },
  balance: {
    enabled: true
  }
};

// Redis配置（可选）
const redis = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT) || 6379,
  password: process.env.REDIS_PASSWORD || '',
  db: parseInt(process.env.REDIS_DB) || 0
};

// 短信服务配置（可选）
const sms = {
  enabled: process.env.SMS_ENABLED === 'true' || false,
  provider: process.env.SMS_PROVIDER || 'aliyun',
  accessKeyId: process.env.SMS_ACCESS_KEY_ID || '',
  accessKeySecret: process.env.SMS_ACCESS_KEY_SECRET || '',
  signName: process.env.SMS_SIGN_NAME || '驿站帮',
  templateCode: process.env.SMS_TEMPLATE_CODE || ''
};

// 文件存储配置
const storage = {
  type: process.env.STORAGE_TYPE || 'local', // local, oss, cos
  domain: process.env.STORAGE_DOMAIN || '',
  oss: {
    region: process.env.OSS_REGION || '',
    accessKeyId: process.env.OSS_ACCESS_KEY_ID || '',
    accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET || '',
    bucket: process.env.OSS_BUCKET || ''
  },
  cos: {
    region: process.env.COS_REGION || '',
    secretId: process.env.COS_SECRET_ID || '',
    secretKey: process.env.COS_SECRET_KEY || '',
    bucket: process.env.COS_BUCKET || ''
  }
};

module.exports = {
  database,
  app,
  jwt,
  wechat,
  payment,
  redis,
  sms,
  storage
}; 