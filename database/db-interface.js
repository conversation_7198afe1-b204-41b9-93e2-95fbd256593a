/**
 * 统一数据库接口
 * 为小程序和后台管理系统提供统一的数据访问接口
 */

const mysql = require('mysql2/promise');
const config = require('./config');
const winston = require('winston');

// 配置日志
const logger = winston.createLogger({
  level: config.app.logLevel,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'yizhan-bang-pro' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});

// 创建数据库连接池
const pool = mysql.createPool({
  host: config.database.host,
  port: config.database.port,
  user: config.database.user,
  password: config.database.password,
  database: config.database.database,
  waitForConnections: config.database.waitForConnections,
  connectionLimit: config.database.connectionLimit,
  queueLimit: config.database.queueLimit
});

/**
 * 执行SQL查询
 * @param {string} sql - SQL语句
 * @param {Array} params - 查询参数
 * @returns {Promise} 查询结果
 */
async function query(sql, params = []) {
  try {
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    logger.error('Database query error:', { error: error.message, sql, params });
    throw error;
  }
}

/**
 * 获取一条记录
 * @param {string} table - 表名
 * @param {Object} conditions - 查询条件
 * @returns {Promise} 查询结果
 */
async function findOne(table, conditions = {}) {
  try {
    const keys = Object.keys(conditions);
    if (keys.length === 0) {
      return null;
    }
    
    const whereClause = keys.map(key => `${key} = ?`).join(' AND ');
    const values = keys.map(key => conditions[key]);
    
    const sql = `SELECT * FROM ${table} WHERE ${whereClause} LIMIT 1`;
    const rows = await query(sql, values);
    
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    logger.error('Find one error:', { error: error.message, table, conditions });
    throw error;
  }
}

/**
 * 获取多条记录
 * @param {string} table - 表名
 * @param {Object} options - 查询选项
 * @returns {Promise} 查询结果
 */
async function find(table, options = {}) {
  try {
    const { conditions = {}, fields = '*', orderBy = '', limit = 0, offset = 0 } = options;
    
    let sql = `SELECT ${Array.isArray(fields) ? fields.join(', ') : fields} FROM ${table}`;
    const values = [];
    
    // 构建WHERE子句
    const keys = Object.keys(conditions);
    if (keys.length > 0) {
      const whereClause = keys.map(key => {
        if (conditions[key] === null) {
          return `${key} IS NULL`;
        } else if (Array.isArray(conditions[key])) {
          values.push(...conditions[key]);
          const placeholders = conditions[key].map(() => '?').join(', ');
          return `${key} IN (${placeholders})`;
        } else {
          values.push(conditions[key]);
          return `${key} = ?`;
        }
      }).join(' AND ');
      
      sql += ` WHERE ${whereClause}`;
    }
    
    // 添加排序
    if (orderBy) {
      sql += ` ORDER BY ${orderBy}`;
    }
    
    // 添加分页
    if (limit > 0) {
      sql += ` LIMIT ?`;
      values.push(limit);
      
      if (offset > 0) {
        sql += ` OFFSET ?`;
        values.push(offset);
      }
    }
    
    return await query(sql, values);
  } catch (error) {
    logger.error('Find error:', { error: error.message, table, options });
    throw error;
  }
}

/**
 * 插入记录
 * @param {string} table - 表名
 * @param {Object} data - 插入数据
 * @returns {Promise} 插入结果
 */
async function insert(table, data) {
  try {
    const keys = Object.keys(data);
    const values = Object.values(data);
    
    const placeholders = keys.map(() => '?').join(', ');
    const columns = keys.join(', ');
    
    const sql = `INSERT INTO ${table} (${columns}) VALUES (${placeholders})`;
    const result = await query(sql, values);
    
    return {
      id: result.insertId,
      affectedRows: result.affectedRows
    };
  } catch (error) {
    logger.error('Insert error:', { error: error.message, table, data });
    throw error;
  }
}

/**
 * 更新记录
 * @param {string} table - 表名
 * @param {Object} data - 更新数据
 * @param {Object} conditions - 更新条件
 * @returns {Promise} 更新结果
 */
async function update(table, data, conditions) {
  try {
    const dataKeys = Object.keys(data);
    const dataValues = Object.values(data);
    
    const setClause = dataKeys.map(key => `${key} = ?`).join(', ');
    
    const conditionKeys = Object.keys(conditions);
    const conditionValues = Object.values(conditions);
    
    const whereClause = conditionKeys.map(key => `${key} = ?`).join(' AND ');
    
    const sql = `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`;
    const result = await query(sql, [...dataValues, ...conditionValues]);
    
    return {
      affectedRows: result.affectedRows
    };
  } catch (error) {
    logger.error('Update error:', { error: error.message, table, data, conditions });
    throw error;
  }
}

/**
 * 删除记录
 * @param {string} table - 表名
 * @param {Object} conditions - 删除条件
 * @returns {Promise} 删除结果
 */
async function remove(table, conditions) {
  try {
    const keys = Object.keys(conditions);
    const values = Object.values(conditions);
    
    const whereClause = keys.map(key => `${key} = ?`).join(' AND ');
    
    const sql = `DELETE FROM ${table} WHERE ${whereClause}`;
    const result = await query(sql, values);
    
    return {
      affectedRows: result.affectedRows
    };
  } catch (error) {
    logger.error('Remove error:', { error: error.message, table, conditions });
    throw error;
  }
}

/**
 * 事务执行
 * @param {Function} callback - 回调函数，接收一个事务连接作为参数
 * @returns {Promise} 执行结果
 */
async function transaction(callback) {
  let connection;
  try {
    connection = await pool.getConnection();
    await connection.beginTransaction();
    
    const result = await callback(connection);
    
    await connection.commit();
    return result;
  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    logger.error('Transaction error:', { error: error.message });
    throw error;
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

/**
 * 计数查询
 * @param {string} table - 表名
 * @param {Object} conditions - 查询条件
 * @returns {Promise<number>} 记录数量
 */
async function count(table, conditions = {}) {
  try {
    const keys = Object.keys(conditions);
    let sql = `SELECT COUNT(*) as count FROM ${table}`;
    const values = [];
    
    if (keys.length > 0) {
      const whereClause = keys.map(key => {
        if (conditions[key] === null) {
          return `${key} IS NULL`;
        } else {
          values.push(conditions[key]);
          return `${key} = ?`;
        }
      }).join(' AND ');
      
      sql += ` WHERE ${whereClause}`;
    }
    
    const rows = await query(sql, values);
    return rows[0].count;
  } catch (error) {
    logger.error('Count error:', { error: error.message, table, conditions });
    throw error;
  }
}

module.exports = {
  pool,
  query,
  findOne,
  find,
  insert,
  update,
  remove,
  transaction,
  count,
  logger
}; 