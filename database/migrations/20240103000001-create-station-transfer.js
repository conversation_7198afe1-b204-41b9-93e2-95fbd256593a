'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('station_transfers', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      transferNo: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
        comment: '转让编号'
      },
      sellerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '卖家用户ID',
        references: {
          model: 'users',
          key: 'id'
        }
      },
      buyerId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '买家用户ID',
        references: {
          model: 'users',
          key: 'id'
        }
      },
      stationId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '驿站ID',
        references: {
          model: 'stations',
          key: 'id'
        }
      },
      title: {
        type: Sequelize.STRING(200),
        allowNull: false,
        comment: '转让标题'
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '转让描述'
      },
      price: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
        comment: '转让价格'
      },
      originalPrice: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true,
        comment: '原价'
      },
      transferType: {
        type: Sequelize.ENUM('full_transfer', 'share_transfer', 'lease'),
        allowNull: false,
        defaultValue: 'full_transfer',
        comment: '转让类型：完全转让、股份转让、租赁'
      },
      businessInfo: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '经营信息：月收入、客流量等'
      },
      facilities: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '设施设备清单'
      },
      location: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '位置信息'
      },
      images: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '转让图片'
      },
      contactInfo: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: '联系方式'
      },
      status: {
        type: Sequelize.ENUM('pending', 'published', 'negotiating', 'completed', 'cancelled'),
        allowNull: false,
        defaultValue: 'pending',
        comment: '转让状态'
      },
      viewCount: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '浏览次数'
      },
      favoriteCount: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '收藏次数'
      },
      publishedAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '发布时间'
      },
      completedAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '完成时间'
      },
      expiresAt: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '过期时间'
      },
      isUrgent: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否紧急'
      },
      isTop: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: '是否置顶'
      },
      remark: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: '备注'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });

    // 添加索引
    await queryInterface.addIndex('station_transfers', ['sellerId']);
    await queryInterface.addIndex('station_transfers', ['buyerId']);
    await queryInterface.addIndex('station_transfers', ['stationId']);
    await queryInterface.addIndex('station_transfers', ['status']);
    await queryInterface.addIndex('station_transfers', ['transferType']);
    await queryInterface.addIndex('station_transfers', ['publishedAt']);
    await queryInterface.addIndex('station_transfers', ['price']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('station_transfers');
  }
};
