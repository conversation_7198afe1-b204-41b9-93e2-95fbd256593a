const { sequelize } = require('./sequelize');
const User = require('./models/User');
const Order = require('./models/Order');
const Station = require('./models/Station');

/**
 * 初始化数据库
 */
async function initDatabase() {
  try {
    console.log('开始初始化数据库...');
    
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 同步所有模型
    console.log('正在同步数据库模型...');
    
    // 定义模型关联关系
    defineAssociations();
    
    // 同步数据库结构 - 使用force重新创建表
    await sequelize.sync({ force: true });
    console.log('数据库模型同步完成');
    
    // 创建示例数据
    await createSampleData();
    
    console.log('数据库初始化完成');
    return true;
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
}

/**
 * 定义模型关联关系
 */
function defineAssociations() {
  // 用户和订单的关联
  User.hasMany(Order, { foreignKey: 'userId', as: 'orders' });
  Order.belongsTo(User, { foreignKey: 'userId', as: 'user' });
  
  // 用户和驿站的关联
  User.hasMany(Station, { foreignKey: 'ownerId', as: 'stations' });
  Station.belongsTo(User, { foreignKey: 'ownerId', as: 'owner' });
  
  // 驿站和订单的关联
  Station.hasMany(Order, { foreignKey: 'stationId', as: 'orders' });
  Order.belongsTo(Station, { foreignKey: 'stationId', as: 'station' });
}

/**
 * 创建示例数据
 */
async function createSampleData() {
  try {
    console.log('正在创建示例数据...');
    
    // 检查是否已有数据
    const userCount = await User.count();
    if (userCount > 0) {
      console.log('数据库中已有数据，跳过示例数据创建');
      return;
    }
    
    // 创建示例用户
    const users = await User.bulkCreate([
      {
        phone: '13800138001',
        nickName: '张三',
        avatarUrl: 'https://example.com/avatar1.jpg',
        registerSource: 'miniprogram'
      },
      {
        phone: '13800138002',
        nickName: '李四',
        avatarUrl: 'https://example.com/avatar2.jpg',
        registerSource: 'miniprogram'
      },
      {
        phone: '13800138003',
        nickName: '王五',
        avatarUrl: 'https://example.com/avatar3.jpg',
        registerSource: 'miniprogram'
      }
    ]);
    
    // 创建示例驿站
    const stations = await Station.bulkCreate([
      {
        name: '中关村驿站',
        code: 'ZGC001',
        type: 'community',
        ownerId: users[0].id,
        address: '北京市海淀区中关村大街1号',
        province: '北京市',
        city: '北京市',
        district: '海淀区',
        phone: '010-12345678',
        status: 'approved',
        verifiedAt: new Date(),
        orderCount: 156,
        revenue: 15600.00
      },
      {
        name: '望京驿站',
        code: 'WJ001',
        type: 'office',
        ownerId: users[1].id,
        address: '北京市朝阳区望京SOHO',
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        phone: '010-87654321',
        status: 'approved',
        verifiedAt: new Date(),
        orderCount: 89,
        revenue: 8900.00
      },
      {
        name: '国贸驿站',
        code: 'GM001',
        type: 'commercial',
        ownerId: users[2].id,
        address: '北京市朝阳区国贸中心',
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        phone: '010-11223344',
        status: 'approved',
        verifiedAt: new Date(),
        orderCount: 234,
        revenue: 23400.00
      }
    ]);
    
    // 创建示例订单
    const orders = [];
    const serviceTypes = ['express_pickup', 'express_send', 'print_copy', 'other'];
    const statuses = ['pending', 'paid', 'processing', 'completed'];
    
    for (let i = 0; i < 50; i++) {
      const randomUser = users[Math.floor(Math.random() * users.length)];
      const randomStation = stations[Math.floor(Math.random() * stations.length)];
      const randomServiceType = serviceTypes[Math.floor(Math.random() * serviceTypes.length)];
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
      const amount = (Math.random() * 50 + 5).toFixed(2);
      
      // 创建不同日期的订单
      const createdAt = new Date();
      createdAt.setDate(createdAt.getDate() - Math.floor(Math.random() * 30));
      
      orders.push({
        orderNo: `ORD${Date.now()}${i.toString().padStart(3, '0')}`,
        userId: randomUser.id,
        stationId: randomStation.id,
        serviceType: randomServiceType,
        title: `${randomServiceType === 'express_pickup' ? '快递代收' : 
                 randomServiceType === 'express_send' ? '快递代发' : 
                 randomServiceType === 'print_copy' ? '打印复印' : '其他服务'}订单`,
        description: `这是一个${randomServiceType}订单的描述`,
        amount: parseFloat(amount),
        status: randomStatus,
        paymentMethod: randomStatus !== 'pending' ? 'wechat' : null,
        paymentTime: randomStatus !== 'pending' ? createdAt : null,
        completedTime: randomStatus === 'completed' ? createdAt : null,
        createdAt: createdAt,
        updatedAt: createdAt
      });
    }
    
    await Order.bulkCreate(orders);
    
    console.log(`示例数据创建完成: ${users.length}个用户, ${stations.length}个驿站, ${orders.length}个订单`);
  } catch (error) {
    console.error('创建示例数据失败:', error);
    throw error;
  }
}

module.exports = {
  initDatabase,
  defineAssociations,
  createSampleData
};
