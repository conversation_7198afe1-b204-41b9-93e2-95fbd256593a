const User = require('../models/User');
const { Op } = require('sequelize');
const jwt = require('jsonwebtoken');
const config = require('../../config');

/**
 * 小程序用户注册
 */
const registerUser = async (req, res) => {
  try {
    const { phone, code, openid, nickName, avatarUrl } = req.body;

    // 验证验证码（这里应该验证短信验证码）
    // TODO: 验证短信验证码

    // 检查用户是否已存在
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [
          { phone: phone },
          { openid: openid }
        ]
      }
    });

    if (existingUser) {
      return res.json({
        code: 1,
        message: '用户已存在',
        data: null
      });
    }

    // 创建新用户
    const newUser = await User.create({
      phone,
      openid,
      nickName,
      avatarUrl,
      registerSource: 'miniprogram',
      registerIp: req.ip,
      lastLoginIp: req.ip,
      lastLoginTime: new Date(),
      loginCount: 1
    });

    // 生成JWT token
    const token = jwt.sign(
      { userId: newUser.id, phone: newUser.phone },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );

    res.json({
      code: 0,
      message: '注册成功',
      data: {
        token,
        user: {
          id: newUser.id,
          phone: newUser.phone,
          nickName: newUser.nickName,
          avatarUrl: newUser.avatarUrl
        }
      }
    });
  } catch (error) {
    console.error('用户注册失败:', error);
    res.status(500).json({
      code: 500,
      message: '注册失败',
      error: error.message
    });
  }
};

/**
 * 小程序用户登录
 */
const loginUser = async (req, res) => {
  try {
    const { phone, code, openid } = req.body;

    // 验证验证码（这里应该验证短信验证码）
    // TODO: 验证短信验证码

    // 查找用户
    let user = await User.findOne({
      where: {
        [Op.or]: [
          { phone: phone },
          { openid: openid }
        ]
      }
    });

    if (!user) {
      return res.json({
        code: 1,
        message: '用户不存在，请先注册',
        data: null
      });
    }

    // 更新登录信息
    await user.update({
      lastLoginIp: req.ip,
      lastLoginTime: new Date(),
      loginCount: user.loginCount + 1
    });

    // 生成JWT token
    const token = jwt.sign(
      { userId: user.id, phone: user.phone },
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn }
    );

    res.json({
      code: 0,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          phone: user.phone,
          nickName: user.nickName,
          avatarUrl: user.avatarUrl
        }
      }
    });
  } catch (error) {
    console.error('用户登录失败:', error);
    res.status(500).json({
      code: 500,
      message: '登录失败',
      error: error.message
    });
  }
};

/**
 * 获取用户信息
 */
const getUserProfile = async (req, res) => {
  try {
    const { userId } = req.user; // 从JWT中获取

    const user = await User.findByPk(userId);
    if (!user) {
      return res.json({
        code: 1,
        message: '用户不存在',
        data: null
      });
    }

    res.json({
      code: 0,
      message: '获取用户信息成功',
      data: {
        id: user.id,
        phone: user.phone,
        nickName: user.nickName,
        avatarUrl: user.avatarUrl,
        realNameAuth: user.realNameAuth,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取用户信息失败',
      error: error.message
    });
  }
};

/**
 * 更新用户信息
 */
const updateUserProfile = async (req, res) => {
  try {
    const { userId } = req.user;
    const { nickName, avatarUrl, realName, idCard } = req.body;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.json({
        code: 1,
        message: '用户不存在',
        data: null
      });
    }

    const updateData = {};
    if (nickName) updateData.nickName = nickName;
    if (avatarUrl) updateData.avatarUrl = avatarUrl;
    if (realName && idCard) {
      updateData.realNameAuth = {
        isVerified: true,
        realName,
        idCard,
        verifiedAt: new Date()
      };
    }

    await user.update(updateData);

    res.json({
      code: 0,
      message: '更新用户信息成功',
      data: null
    });
  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新用户信息失败',
      error: error.message
    });
  }
};

/**
 * 创建订单
 */
const createOrder = async (req, res) => {
  try {
    const { userId } = req.user;
    const { stationId, serviceType, description, amount } = req.body;

    // 这里应该创建订单记录
    // TODO: 实现订单创建逻辑

    res.json({
      code: 0,
      message: '订单创建成功',
      data: {
        orderId: 'ORDER_' + Date.now(),
        amount,
        status: 'pending'
      }
    });
  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建订单失败',
      error: error.message
    });
  }
};

/**
 * 获取订单列表
 */
const getOrderList = async (req, res) => {
  try {
    const { userId } = req.user;
    const { page = 1, limit = 10, status } = req.query;

    // 这里应该查询订单列表
    // TODO: 实现订单列表查询

    res.json({
      code: 0,
      message: '获取订单列表成功',
      data: {
        list: [],
        total: 0,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取订单列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取订单列表失败',
      error: error.message
    });
  }
};

/**
 * 获取订单详情
 */
const getOrderDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.user;

    // 这里应该查询订单详情
    // TODO: 实现订单详情查询

    res.json({
      code: 0,
      message: '获取订单详情成功',
      data: {
        id,
        status: 'pending',
        amount: 10.00,
        createdAt: new Date()
      }
    });
  } catch (error) {
    console.error('获取订单详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取订单详情失败',
      error: error.message
    });
  }
};

/**
 * 更新订单状态
 */
const updateOrderStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // 这里应该更新订单状态
    // TODO: 实现订单状态更新

    res.json({
      code: 0,
      message: '订单状态更新成功',
      data: null
    });
  } catch (error) {
    console.error('更新订单状态失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新订单状态失败',
      error: error.message
    });
  }
};

// 其他方法的模拟实现
const getStationList = async (req, res) => {
  res.json({ code: 0, message: '获取驿站列表成功', data: { list: [], total: 0 } });
};

const getStationDetail = async (req, res) => {
  res.json({ code: 0, message: '获取驿站详情成功', data: {} });
};

const getNearbyStations = async (req, res) => {
  res.json({ code: 0, message: '获取附近驿站成功', data: { list: [] } });
};

const getServiceList = async (req, res) => {
  res.json({ code: 0, message: '获取服务列表成功', data: { list: [] } });
};

const bookService = async (req, res) => {
  res.json({ code: 0, message: '预约服务成功', data: null });
};

const createPayment = async (req, res) => {
  res.json({ code: 0, message: '创建支付成功', data: { paymentId: 'PAY_' + Date.now() } });
};

const paymentNotify = async (req, res) => {
  res.json({ code: 0, message: '支付通知处理成功', data: null });
};

module.exports = {
  registerUser,
  loginUser,
  getUserProfile,
  updateUserProfile,
  createOrder,
  getOrderList,
  getOrderDetail,
  updateOrderStatus,
  getStationList,
  getStationDetail,
  getNearbyStations,
  getServiceList,
  bookService,
  createPayment,
  paymentNotify
};
