const jwt = require('jsonwebtoken');
const axios = require('axios');
const User = require('../models/User');
const { Op } = require('sequelize');
const bcrypt = require('bcryptjs');
const smsController = require('./smsController');

// JWT配置
const JWT_SECRET = process.env.JWT_SECRET || 'yizhanbang-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// 微信配置
const WECHAT_APPID = process.env.WECHAT_APPID;
const WECHAT_SECRET = process.env.WECHAT_SECRET;

/**
 * 生成JWT令牌
 * @param {Object} user 用户对象
 * @returns {String} JWT令牌
 */
const generateToken = (user) => {
  return jwt.sign(
    { 
      id: user.id,
      roles: user.roles
    },
    JWT_SECRET,
    { expiresIn: JWT_EXPIRES_IN }
  );
};

/**
 * 检查手机号是否已注册
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.checkPhoneExists = async (req, res) => {
  try {
    const { phone } = req.query;
    
    if (!phone) {
      return res.status(400).json({
        code: 1,
        message: '手机号不能为空'
      });
    }
    
    const user = await User.findOne({ where: { phone } });
    
    return res.json({
      code: 0,
      message: '检查成功',
      data: {
        exists: !!user
      }
    });
  } catch (error) {
    console.error('检查手机号错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试'
    });
  }
};

/**
 * 发送短信验证码
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.sendSmsCode = async (req, res) => {
  // 调用smsController中的sendSmsCode方法
  return smsController.sendSmsCode(req, res);
};

/**
 * 微信登录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.wxLogin = async (req, res) => {
  try {
    console.log('收到微信登录请求:', req.body);
    const { code, userInfo } = req.body;
    
    if (!code) {
      console.log('微信登录失败: code不能为空');
      return res.status(400).json({
        code: 1,
        message: '微信登录code不能为空'
      });
    }
    
    console.log('微信登录配置:', { 
      appid: WECHAT_APPID, 
      secret: WECHAT_SECRET ? '已配置' : '未配置' 
    });
    
    // 通过code获取openid和session_key
    const wxApiUrl = `https://api.weixin.qq.com/sns/jscode2session?appid=${WECHAT_APPID}&secret=${WECHAT_SECRET}&js_code=${code}&grant_type=authorization_code`;
    console.log('请求微信API:', wxApiUrl);
    
    try {
      const wxRes = await axios.get(wxApiUrl);
      console.log('微信API返回:', wxRes.data);
      
      const { openid, session_key, unionid, errcode, errmsg } = wxRes.data;
      
      if (errcode) {
        console.log('微信API返回错误:', { errcode, errmsg });
        return res.status(400).json({
          code: errcode,
          message: `微信登录失败: ${errmsg}`
        });
      }
      
      if (!openid) {
        console.log('微信API返回错误: 没有openid');
        return res.status(400).json({
          code: 2,
          message: '微信登录失败: 无法获取openid'
        });
      }
      
      // 检查用户是否存在
      console.log('查询用户, openid:', openid);
      let user = await User.findOne({ where: { mpOpenid: openid } });
      let isNewUser = false;
      
      if (!user) {
        // 创建新用户
        console.log('用户不存在，创建新用户');
        isNewUser = true;
        
        const userData = {
          mpOpenid: openid,
          sessionKey: session_key,
          registerSource: 'wechat',
          registerIp: req.ip,
          lastLoginIp: req.ip,
          lastLoginTime: new Date(),
          loginCount: 1,
          realNameAuth: {
            isVerified: false
          }
        };
        
        // 如果有unionid，添加到用户数据中
        if (unionid) {
          userData.unionid = unionid;
        }
        
        // 如果有用户信息，添加到用户数据中
        if (userInfo) {
          userData.nickname = userInfo.nickName;
          userData.avatar = userInfo.avatarUrl;
          userData.gender = userInfo.gender;
        }
        
        console.log('创建用户数据:', userData);
        user = await User.create(userData);
        console.log('新用户创建成功, ID:', user.id);
      } else {
        // 更新现有用户
        console.log('用户已存在，更新用户信息, ID:', user.id);
        user.sessionKey = session_key;
        user.lastLoginIp = req.ip;
        user.lastLoginTime = new Date();
        user.loginCount = user.loginCount + 1;
        
        // 如果有unionid且用户未设置，更新用户的unionid
        if (unionid && !user.unionid) {
          user.unionid = unionid;
        }
        
        // 如果有用户信息且用户未设置，更新用户信息
        if (userInfo && (!user.nickname || !user.avatar)) {
          if (!user.nickname) user.nickname = userInfo.nickName;
          if (!user.avatar) user.avatar = userInfo.avatarUrl;
          if (user.gender === 0) user.gender = userInfo.gender;
        }
        
        await user.save();
        console.log('用户信息更新成功');
      }
      
      // 生成token
      const token = generateToken(user);
      console.log('生成token成功');
      
      // 返回用户信息和token
      const response = {
        code: 0,
        message: '登录成功',
        data: {
          token,
          user: {
            id: user.id,
            nickname: user.nickname,
            avatar: user.avatar,
            roles: user.roles,
            isNewUser
          }
        }
      };
      
      console.log('登录成功，返回数据:', response);
      return res.json(response);
    } catch (wxError) {
      console.error('调用微信API出错:', wxError);
      return res.status(500).json({
        code: 500,
        message: `调用微信API出错: ${wxError.message}`,
        error: process.env.NODE_ENV === 'development' ? wxError.toString() : undefined
      });
    }
  } catch (error) {
    console.error('微信登录处理出错:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试',
      error: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
};

/**
 * 手机号码登录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.phoneLogin = async (req, res) => {
  try {
    const { phone, code } = req.body;
    
    if (!phone || !code) {
      return res.status(400).json({
        code: 1,
        message: '手机号和验证码不能为空'
      });
    }
    
    // 验证短信验证码
    const verifyResult = smsController.verifyCode(phone, code, 'login');
    if (!verifyResult.valid) {
      return res.status(400).json({
        code: 2,
        message: verifyResult.message
      });
    }
    
    // 检查用户是否存在
    let user = await User.findOne({ where: { phone } });
    let isNewUser = false;
    
    if (!user) {
      // 创建新用户
      isNewUser = true;
      
      user = await User.create({
        phone,
        registerSource: 'phone',
        registerIp: req.ip,
        lastLoginIp: req.ip,
        lastLoginTime: new Date(),
        loginCount: 1
      });
    } else {
      // 更新现有用户
      user.lastLoginIp = req.ip;
      user.lastLoginTime = new Date();
      user.loginCount = user.loginCount + 1;
      
      await user.save();
    }
    
    // 生成token
    const token = generateToken(user);
    
    // 返回用户信息和token
    return res.json({
      code: 0,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          roles: user.roles,
          isNewUser
        }
      }
    });
  } catch (error) {
    console.error('手机登录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试'
    });
  }
};

/**
 * 密码登录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.passwordLogin = async (req, res) => {
  try {
    const { phone, password } = req.body;
    
    if (!phone || !password) {
      return res.status(400).json({
        code: 1,
        message: '手机号和密码不能为空'
      });
    }
    
    // 查找用户
    const user = await User.scope('withPassword').findOne({ where: { phone } });
    
    if (!user) {
      return res.status(400).json({
        code: 2,
        message: '账号不存在'
      });
    }
    
    // 验证密码
    const isPasswordValid = await user.comparePassword(password);
    
    if (!isPasswordValid) {
      return res.status(400).json({
        code: 3,
        message: '密码错误'
      });
    }
    
    // 更新登录信息
    user.lastLoginIp = req.ip;
    user.lastLoginTime = new Date();
    user.loginCount = user.loginCount + 1;
    await user.save();
    
    // 生成token
    const token = generateToken(user);
    
    // 返回用户信息和token
    return res.json({
      code: 0,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          roles: user.roles
        }
      }
    });
  } catch (error) {
    console.error('密码登录错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试'
    });
  }
};

/**
 * 用户注册
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.register = async (req, res) => {
  try {
    const { phone, password, code, username } = req.body;
    
    if (!phone || !password || !code || !username) {
      return res.status(400).json({
        code: 1,
        message: '用户名、手机号、密码和验证码不能为空'
      });
    }
    
    // 验证短信验证码
    const verifyResult = smsController.verifyCode(phone, code, 'register');
    if (!verifyResult.valid) {
      return res.status(400).json({
        code: 2,
        message: verifyResult.message
      });
    }
    
    // 检查用户是否已存在
    const existingUser = await User.findOne({ where: { phone } });
    
    if (existingUser) {
      return res.status(400).json({
        code: 3,
        message: '该手机号已注册'
      });
    }
    
    // 创建新用户
    const user = await User.create({
      phone,
      password,
      nickname: username,
      registerSource: 'phone',
      registerIp: req.ip,
      lastLoginIp: req.ip,
      lastLoginTime: new Date(),
      loginCount: 1
    });
    
    // 生成token
    const token = generateToken(user);
    
    // 返回用户信息和token
    return res.json({
      code: 0,
      message: '注册成功',
      data: {
        token,
        user: {
          id: user.id,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          roles: user.roles
        }
      }
    });
  } catch (error) {
    console.error('注册错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试'
    });
  }
};

/**
 * 重置密码
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.resetPassword = async (req, res) => {
  try {
    const { phone, code, password } = req.body;
    
    if (!phone || !code || !password) {
      return res.status(400).json({
        code: 1,
        message: '手机号、验证码和新密码不能为空'
      });
    }
    
    // 验证短信验证码
    const verifyResult = smsController.verifyCode(phone, code, 'resetPassword');
    if (!verifyResult.valid) {
      return res.status(400).json({
        code: 2,
        message: verifyResult.message
      });
    }
    
    // 查找用户
    const user = await User.findOne({ where: { phone } });
    
    if (!user) {
      return res.status(404).json({
        code: 3,
        message: '用户不存在'
      });
    }
    
    // 更新密码
    user.password = password;
    await user.save();
    
    return res.json({
      code: 0,
      message: '密码重置成功'
    });
  } catch (error) {
    console.error('重置密码错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试'
    });
  }
};

/**
 * 退出登录
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.logout = async (req, res) => {
  try {
    // 客户端需要清除token
    // 这里可以实现token黑名单，但通常由客户端处理登出
    
    return res.json({
      code: 0,
      message: '登出成功'
    });
  } catch (error) {
    console.error('登出错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试'
    });
  }
};

/**
 * 刷新Token
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.refreshToken = async (req, res) => {
  try {
    const { id } = req.user;
    
    // 查询用户
    const user = await User.findByPk(id);
    
    if (!user) {
      return res.status(404).json({
        code: 1,
        message: '用户不存在'
      });
    }
    
    // 生成新token
    const token = generateToken(user);
    
    // 返回新token
    return res.json({
      code: 0,
      message: 'Token刷新成功',
      data: {
        token
      }
    });
  } catch (error) {
    console.error('刷新Token错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试'
    });
  }
};

/**
 * 获取登录状态
 * @param {Object} req 请求对象
 * @param {Object} res 响应对象
 */
exports.getLoginStatus = async (req, res) => {
  try {
    const { id } = req.user;
    
    // 查询用户
    const user = await User.findByPk(id);
    
    if (!user) {
      return res.status(404).json({
        code: 1,
        message: '用户不存在'
      });
    }
    
    // 返回用户信息
    return res.json({
      code: 0,
      message: '获取成功',
      data: {
        user: {
          id: user.id,
          nickname: user.nickname,
          avatar: user.avatar,
          phone: user.phone,
          roles: user.roles,
          lastLoginTime: user.lastLoginTime
        }
      }
    });
  } catch (error) {
    console.error('获取登录状态错误:', error);
    return res.status(500).json({
      code: 500,
      message: '服务器错误，请稍后再试'
    });
  }
}; 