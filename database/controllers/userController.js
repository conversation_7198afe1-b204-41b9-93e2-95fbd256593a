const User = require('../models/User');
const { Op } = require('sequelize');

/**
 * 获取用户列表
 */
const getUserList = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      phone,
      registerSource,
      startDate,
      endDate,
      isActive
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // 构建查询条件
    if (phone) {
      where.phone = { [Op.like]: `%${phone}%` };
    }
    // registerSource 字段不存在，暂时注释
    // if (registerSource) {
    //   where.register_source = registerSource;
    // }
    // isActive 字段不存在，暂时注释
    // if (isActive !== undefined) {
    //   where.is_active = isActive === 'true';
    // }
    if (startDate && endDate) {
      where.created_at = {
        [Op.between]: [new Date(startDate), new Date(endDate + ' 23:59:59')]
      };
    }

    const { count, rows } = await User.findAndCountAll({
      where,
      attributes: [
        'id', 'phone', 'nickname', 'avatar',
        'last_login_time', 'created_at', 'is_verified'
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      code: 0,
      message: '获取用户列表成功',
      data: {
        list: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取用户列表失败',
      error: error.message
    });
  }
};

/**
 * 获取用户详情
 */
const getUserDetail = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    res.json({
      code: 0,
      message: '获取用户详情成功',
      data: user
    });
  } catch (error) {
    console.error('获取用户详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取用户详情失败',
      error: error.message
    });
  }
};

/**
 * 更新用户状态
 */
const updateUserStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    await user.update({ isActive });

    res.json({
      code: 0,
      message: '更新用户状态成功',
      data: user
    });
  } catch (error) {
    console.error('更新用户状态失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新用户状态失败',
      error: error.message
    });
  }
};

/**
 * 删除用户
 */
const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({
        code: 404,
        message: '用户不存在'
      });
    }

    await user.destroy();

    res.json({
      code: 0,
      message: '删除用户成功'
    });
  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除用户失败',
      error: error.message
    });
  }
};

/**
 * 获取用户统计
 */
const getUserStats = async (req, res) => {
  try {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const todayStart = new Date(today.setHours(0, 0, 0, 0));
    const yesterdayStart = new Date(yesterday.setHours(0, 0, 0, 0));
    const yesterdayEnd = new Date(yesterday.setHours(23, 59, 59, 999));

    const [
      totalUsers,
      activeUsers,
      todayUsers,
      yesterdayUsers,
      miniprogramUsers,
      h5Users,
      appUsers
    ] = await Promise.all([
      User.count(),
      User.count({ where: { isActive: true } }),
      User.count({
        where: {
          createdAt: { [Op.gte]: todayStart }
        }
      }),
      User.count({
        where: {
          createdAt: { [Op.between]: [yesterdayStart, yesterdayEnd] }
        }
      }),
      User.count({ where: { registerSource: 'miniprogram' } }),
      User.count({ where: { registerSource: 'h5' } }),
      User.count({ where: { registerSource: 'app' } })
    ]);

    res.json({
      code: 0,
      message: '获取用户统计成功',
      data: {
        total: totalUsers,
        active: activeUsers,
        todayNew: todayUsers,
        yesterdayNew: yesterdayUsers,
        growth: todayUsers - yesterdayUsers,
        sourceDistribution: {
          miniprogram: miniprogramUsers,
          h5: h5Users,
          app: appUsers
        }
      }
    });
  } catch (error) {
    console.error('获取用户统计失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取用户统计失败',
      error: error.message
    });
  }
};

module.exports = {
  getUserList,
  getUserDetail,
  updateUserStatus,
  deleteUser,
  getUserStats
};
