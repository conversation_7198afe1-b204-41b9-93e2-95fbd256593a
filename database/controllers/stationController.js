const Station = require('../models/Station');
const User = require('../models/User');
const { Op } = require('sequelize');

/**
 * 获取驿站列表
 */
const getStationList = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      keyword,
      status,
      type
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // 构建查询条件
    if (keyword) {
      where[Op.or] = [
        { name: { [Op.like]: `%${keyword}%` } },
        { ownerName: { [Op.like]: `%${keyword}%` } }
      ];
    }
    if (status) {
      where.status = status;
    }
    if (type) {
      where.type = type;
    }

    const { count, rows } = await Station.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'nickName', 'phone']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      code: 0,
      message: '获取驿站列表成功',
      data: {
        list: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取驿站列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取驿站列表失败',
      error: error.message
    });
  }
};

/**
 * 获取驿站详情
 */
const getStationDetail = async (req, res) => {
  try {
    const { id } = req.params;

    const station = await Station.findByPk(id, {
      include: [
        {
          model: User,
          as: 'owner',
          attributes: ['id', 'nickName', 'phone', 'avatarUrl']
        }
      ]
    });

    if (!station) {
      return res.status(404).json({
        code: 404,
        message: '驿站不存在'
      });
    }

    res.json({
      code: 0,
      message: '获取驿站详情成功',
      data: station
    });
  } catch (error) {
    console.error('获取驿站详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取驿站详情失败',
      error: error.message
    });
  }
};

/**
 * 创建驿站
 */
const createStation = async (req, res) => {
  try {
    const {
      name,
      type,
      address,
      location,
      ownerId,
      ownerName,
      phone,
      businessHours,
      description
    } = req.body;

    const station = await Station.create({
      name,
      type,
      address,
      location,
      ownerId,
      ownerName,
      phone,
      businessHours,
      description,
      status: 'pending' // 新创建的驿站默认为待审核状态
    });

    res.json({
      code: 0,
      message: '创建驿站成功',
      data: station
    });
  } catch (error) {
    console.error('创建驿站失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建驿站失败',
      error: error.message
    });
  }
};

/**
 * 更新驿站信息
 */
const updateStation = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const station = await Station.findByPk(id);
    if (!station) {
      return res.status(404).json({
        code: 404,
        message: '驿站不存在'
      });
    }

    await station.update(updateData);

    res.json({
      code: 0,
      message: '更新驿站信息成功',
      data: station
    });
  } catch (error) {
    console.error('更新驿站信息失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新驿站信息失败',
      error: error.message
    });
  }
};

/**
 * 审核驿站
 */
const approveStation = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, remark } = req.body;

    const station = await Station.findByPk(id);
    if (!station) {
      return res.status(404).json({
        code: 404,
        message: '驿站不存在'
      });
    }

    const updateData = { status, remark };
    if (status === 'approved') {
      updateData.verifiedAt = new Date();
    }

    await station.update(updateData);

    res.json({
      code: 0,
      message: '审核成功',
      data: station
    });
  } catch (error) {
    console.error('审核驿站失败:', error);
    res.status(500).json({
      code: 500,
      message: '审核驿站失败',
      error: error.message
    });
  }
};

/**
 * 删除驿站
 */
const deleteStation = async (req, res) => {
  try {
    const { id } = req.params;

    const station = await Station.findByPk(id);
    if (!station) {
      return res.status(404).json({
        code: 404,
        message: '驿站不存在'
      });
    }

    await station.destroy();

    res.json({
      code: 0,
      message: '删除驿站成功'
    });
  } catch (error) {
    console.error('删除驿站失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除驿站失败',
      error: error.message
    });
  }
};

/**
 * 获取驿站统计
 */
const getStationStats = async (req, res) => {
  try {
    const [
      totalCount,
      pendingCount,
      approvedCount,
      rejectedCount,
      disabledCount
    ] = await Promise.all([
      Station.count(),
      Station.count({ where: { status: 'pending' } }),
      Station.count({ where: { status: 'approved' } }),
      Station.count({ where: { status: 'rejected' } }),
      Station.count({ where: { status: 'disabled' } })
    ]);

    res.json({
      code: 0,
      message: '获取驿站统计成功',
      data: {
        total: totalCount,
        pending: pendingCount,
        approved: approvedCount,
        rejected: rejectedCount,
        disabled: disabledCount,
        active: approvedCount // 已审核的就是活跃的
      }
    });
  } catch (error) {
    console.error('获取驿站统计失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取驿站统计失败',
      error: error.message
    });
  }
};

module.exports = {
  getStationList,
  getStationDetail,
  createStation,
  updateStation,
  approveStation,
  deleteStation,
  getStationStats
};
