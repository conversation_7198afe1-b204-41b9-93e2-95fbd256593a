const User = require('../models/User');
const { Op } = require('sequelize');

/**
 * 获取仪表板数据
 */
const getDashboardData = async (req, res) => {
  try {
    // 获取今天和昨天的日期
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const todayStart = new Date(today.setHours(0, 0, 0, 0));
    const yesterdayStart = new Date(yesterday.setHours(0, 0, 0, 0));
    const yesterdayEnd = new Date(yesterday.setHours(23, 59, 59, 999));

    // 获取总用户数
    const totalUsers = await User.count();
    
    // 获取昨日新增用户数
    const yesterdayUsers = await User.count({
      where: {
        createdAt: {
          [Op.between]: [yesterdayStart, yesterdayEnd]
        }
      }
    });

    // 获取今日新增用户数
    const todayUsers = await User.count({
      where: {
        createdAt: {
          [Op.gte]: todayStart
        }
      }
    });

    // 模拟其他数据（实际项目中应该从相应的表中获取）
    const mockData = {
      totalUsers: totalUsers,
      totalStations: 358,
      totalOrders: 6832,
      totalRevenue: 235698.56,
      userGrowth: todayUsers - yesterdayUsers,
      stationGrowth: 5,
      orderGrowth: 43,
      revenueGrowth: 1268.32
    };

    res.json({
      code: 0,
      message: '获取仪表板数据成功',
      data: mockData
    });
  } catch (error) {
    console.error('获取仪表板数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取仪表板数据失败',
      error: error.message
    });
  }
};

/**
 * 获取统计概览
 */
const getOverview = async (req, res) => {
  try {
    const { period = '7d' } = req.query;
    
    // 根据时间周期计算日期范围
    const endDate = new Date();
    const startDate = new Date();
    
    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      default:
        startDate.setDate(endDate.getDate() - 7);
    }

    // 获取用户增长数据
    const userCount = await User.count({
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate]
        }
      }
    });

    // 模拟数据
    const overview = {
      period,
      userGrowth: userCount,
      orderGrowth: Math.floor(Math.random() * 100) + 50,
      revenueGrowth: Math.floor(Math.random() * 10000) + 5000,
      stationGrowth: Math.floor(Math.random() * 20) + 5
    };

    res.json({
      code: 0,
      message: '获取统计概览成功',
      data: overview
    });
  } catch (error) {
    console.error('获取统计概览失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取统计概览失败',
      error: error.message
    });
  }
};

/**
 * 获取订单趋势
 */
const getOrderTrend = async (req, res) => {
  try {
    const { period = '7d' } = req.query;
    
    // 模拟订单趋势数据
    let trendData = [];
    
    switch (period) {
      case '7d':
        trendData = [
          { date: '6-27', orders: 120, revenue: 12000 },
          { date: '6-28', orders: 132, revenue: 13200 },
          { date: '6-29', orders: 101, revenue: 10100 },
          { date: '6-30', orders: 134, revenue: 13400 },
          { date: '7-01', orders: 190, revenue: 19000 },
          { date: '7-02', orders: 230, revenue: 23000 },
          { date: '7-03', orders: 210, revenue: 21000 }
        ];
        break;
      case '30d':
        trendData = [
          { date: '6月', orders: 820, revenue: 22000 },
          { date: '7月', orders: 932, revenue: 25000 },
          { date: '8月', orders: 901, revenue: 24000 },
          { date: '9月', orders: 934, revenue: 26000 },
          { date: '10月', orders: 1290, revenue: 32000 },
          { date: '11月', orders: 1330, revenue: 35000 },
          { date: '12月', orders: 1320, revenue: 34000 }
        ];
        break;
      case '90d':
        trendData = [
          { date: 'Q1', orders: 2653, revenue: 71000 },
          { date: 'Q2', orders: 2890, revenue: 78000 },
          { date: 'Q3', orders: 3120, revenue: 85000 },
          { date: 'Q4', orders: 3456, revenue: 92000 }
        ];
        break;
    }

    res.json({
      code: 0,
      message: '获取订单趋势成功',
      data: trendData
    });
  } catch (error) {
    console.error('获取订单趋势失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取订单趋势失败',
      error: error.message
    });
  }
};

/**
 * 获取订单类型分布
 */
const getOrderTypeDistribution = async (req, res) => {
  try {
    // 模拟订单类型分布数据
    const distribution = [
      { name: '快递代收', value: 45, color: '#409EFF' },
      { name: '快递代发', value: 30, color: '#67C23A' },
      { name: '打印复印', value: 15, color: '#E6A23C' },
      { name: '其他服务', value: 10, color: '#F56C6C' }
    ];

    res.json({
      code: 0,
      message: '获取订单类型分布成功',
      data: distribution
    });
  } catch (error) {
    console.error('获取订单类型分布失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取订单类型分布失败',
      error: error.message
    });
  }
};

/**
 * 获取用户增长趋势
 */
const getUserGrowthTrend = async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    
    // 这里应该根据实际数据库查询用户增长趋势
    // 现在使用模拟数据
    const growthData = [
      { date: '2024-01', newUsers: 156, totalUsers: 1256 },
      { date: '2024-02', newUsers: 189, totalUsers: 1445 },
      { date: '2024-03', newUsers: 234, totalUsers: 1679 },
      { date: '2024-04', newUsers: 198, totalUsers: 1877 },
      { date: '2024-05', newUsers: 267, totalUsers: 2144 },
      { date: '2024-06', newUsers: 298, totalUsers: 2442 }
    ];

    res.json({
      code: 0,
      message: '获取用户增长趋势成功',
      data: growthData
    });
  } catch (error) {
    console.error('获取用户增长趋势失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取用户增长趋势失败',
      error: error.message
    });
  }
};

/**
 * 获取用户地区分布
 */
const getUserRegionDistribution = async (req, res) => {
  try {
    // 模拟用户地区分布数据
    const regionData = [
      { region: '北京', users: 456, percentage: 18.7 },
      { region: '上海', users: 389, percentage: 15.9 },
      { region: '广州', users: 334, percentage: 13.7 },
      { region: '深圳', users: 298, percentage: 12.2 },
      { region: '杭州', users: 267, percentage: 10.9 },
      { region: '其他', users: 698, percentage: 28.6 }
    ];

    res.json({
      code: 0,
      message: '获取用户地区分布成功',
      data: regionData
    });
  } catch (error) {
    console.error('获取用户地区分布失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取用户地区分布失败',
      error: error.message
    });
  }
};

/**
 * 获取热门驿站排行
 */
const getHotStations = async (req, res) => {
  try {
    // 模拟热门驿站数据
    const hotStations = [
      { id: 1, name: '中关村驿站', orders: 1234, revenue: 45678.90 },
      { id: 2, name: '望京驿站', orders: 1156, revenue: 42345.67 },
      { id: 3, name: '国贸驿站', orders: 1089, revenue: 39876.54 },
      { id: 4, name: '三里屯驿站', orders: 987, revenue: 36543.21 },
      { id: 5, name: '西单驿站', orders: 876, revenue: 32109.87 }
    ];

    res.json({
      code: 0,
      message: '获取热门驿站排行成功',
      data: hotStations
    });
  } catch (error) {
    console.error('获取热门驿站排行失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取热门驿站排行失败',
      error: error.message
    });
  }
};

/**
 * 导出统计数据
 */
const exportData = async (req, res) => {
  try {
    // 这里应该生成Excel或CSV文件
    // 现在返回模拟响应
    res.json({
      code: 0,
      message: '数据导出功能开发中',
      data: null
    });
  } catch (error) {
    console.error('导出数据失败:', error);
    res.status(500).json({
      code: 500,
      message: '导出数据失败',
      error: error.message
    });
  }
};

module.exports = {
  getDashboardData,
  getOverview,
  getOrderTrend,
  getOrderTypeDistribution,
  getUserGrowthTrend,
  getUserRegionDistribution,
  getHotStations,
  exportData
};
