const StationTransfer = require('../models/StationTransfer');
const User = require('../models/User');
const Station = require('../models/Station');
const { Op } = require('sequelize');

/**
 * 获取驿站转让列表
 */
const getTransferList = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      transferNo,
      transferType,
      status,
      minPrice,
      maxPrice,
      sellerId,
      stationId
    } = req.query;

    const offset = (page - 1) * limit;
    const where = {};

    // 构建查询条件
    if (transferNo) {
      where.transferNo = { [Op.like]: `%${transferNo}%` };
    }
    if (transferType) {
      where.transferType = transferType;
    }
    if (status) {
      where.status = status;
    }
    if (sellerId) {
      where.sellerId = sellerId;
    }
    if (stationId) {
      where.stationId = stationId;
    }
    if (minPrice || maxPrice) {
      where.price = {};
      if (minPrice) where.price[Op.gte] = minPrice;
      if (maxPrice) where.price[Op.lte] = maxPrice;
    }

    const { count, rows } = await StationTransfer.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'seller',
          attributes: ['id', 'nickName', 'phone']
        },
        {
          model: User,
          as: 'buyer',
          attributes: ['id', 'nickName', 'phone']
        },
        {
          model: Station,
          as: 'station',
          attributes: ['id', 'name', 'address', 'type']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      code: 0,
      message: '获取转让列表成功',
      data: {
        list: rows,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取转让列表失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取转让列表失败',
      error: error.message
    });
  }
};

/**
 * 获取转让详情
 */
const getTransferDetail = async (req, res) => {
  try {
    const { id } = req.params;

    const transfer = await StationTransfer.findByPk(id, {
      include: [
        {
          model: User,
          as: 'seller',
          attributes: ['id', 'nickName', 'phone', 'avatarUrl']
        },
        {
          model: User,
          as: 'buyer',
          attributes: ['id', 'nickName', 'phone', 'avatarUrl']
        },
        {
          model: Station,
          as: 'station',
          attributes: ['id', 'name', 'address', 'type', 'location']
        }
      ]
    });

    if (!transfer) {
      return res.status(404).json({
        code: 404,
        message: '转让信息不存在'
      });
    }

    // 增加浏览次数
    await transfer.increment('viewCount');

    res.json({
      code: 0,
      message: '获取转让详情成功',
      data: transfer
    });
  } catch (error) {
    console.error('获取转让详情失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取转让详情失败',
      error: error.message
    });
  }
};

/**
 * 创建转让信息
 */
const createTransfer = async (req, res) => {
  try {
    const {
      sellerId,
      stationId,
      title,
      description,
      price,
      originalPrice,
      transferType,
      businessInfo,
      facilities,
      location,
      images,
      contactInfo,
      expiresAt
    } = req.body;

    // 生成转让编号
    const transferNo = `TF${Date.now()}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;

    const transfer = await StationTransfer.create({
      transferNo,
      sellerId,
      stationId,
      title,
      description,
      price,
      originalPrice,
      transferType,
      businessInfo,
      facilities,
      location,
      images,
      contactInfo,
      expiresAt,
      status: 'pending'
    });

    res.json({
      code: 0,
      message: '创建转让信息成功',
      data: transfer
    });
  } catch (error) {
    console.error('创建转让信息失败:', error);
    res.status(500).json({
      code: 500,
      message: '创建转让信息失败',
      error: error.message
    });
  }
};

/**
 * 更新转让信息
 */
const updateTransfer = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const transfer = await StationTransfer.findByPk(id);
    if (!transfer) {
      return res.status(404).json({
        code: 404,
        message: '转让信息不存在'
      });
    }

    await transfer.update(updateData);

    res.json({
      code: 0,
      message: '更新转让信息成功',
      data: transfer
    });
  } catch (error) {
    console.error('更新转让信息失败:', error);
    res.status(500).json({
      code: 500,
      message: '更新转让信息失败',
      error: error.message
    });
  }
};

/**
 * 审核转让信息
 */
const approveTransfer = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, remark } = req.body;

    const transfer = await StationTransfer.findByPk(id);
    if (!transfer) {
      return res.status(404).json({
        code: 404,
        message: '转让信息不存在'
      });
    }

    const updateData = { status, remark };
    if (status === 'published') {
      updateData.publishedAt = new Date();
    }

    await transfer.update(updateData);

    res.json({
      code: 0,
      message: '审核成功',
      data: transfer
    });
  } catch (error) {
    console.error('审核转让信息失败:', error);
    res.status(500).json({
      code: 500,
      message: '审核转让信息失败',
      error: error.message
    });
  }
};

/**
 * 删除转让信息
 */
const deleteTransfer = async (req, res) => {
  try {
    const { id } = req.params;

    const transfer = await StationTransfer.findByPk(id);
    if (!transfer) {
      return res.status(404).json({
        code: 404,
        message: '转让信息不存在'
      });
    }

    await transfer.destroy();

    res.json({
      code: 0,
      message: '删除转让信息成功'
    });
  } catch (error) {
    console.error('删除转让信息失败:', error);
    res.status(500).json({
      code: 500,
      message: '删除转让信息失败',
      error: error.message
    });
  }
};

/**
 * 获取转让统计
 */
const getTransferStats = async (req, res) => {
  try {
    const [
      totalCount,
      pendingCount,
      publishedCount,
      completedCount,
      cancelledCount
    ] = await Promise.all([
      StationTransfer.count(),
      StationTransfer.count({ where: { status: 'pending' } }),
      StationTransfer.count({ where: { status: 'published' } }),
      StationTransfer.count({ where: { status: 'completed' } }),
      StationTransfer.count({ where: { status: 'cancelled' } })
    ]);

    res.json({
      code: 0,
      message: '获取转让统计成功',
      data: {
        total: totalCount,
        pending: pendingCount,
        published: publishedCount,
        completed: completedCount,
        cancelled: cancelledCount
      }
    });
  } catch (error) {
    console.error('获取转让统计失败:', error);
    res.status(500).json({
      code: 500,
      message: '获取转让统计失败',
      error: error.message
    });
  }
};

module.exports = {
  getTransferList,
  getTransferDetail,
  createTransfer,
  updateTransfer,
  approveTransfer,
  deleteTransfer,
  getTransferStats
};
