-- 创建驿站帮Pro统一数据库
CREATE DATABASE IF NOT EXISTS yizhan_bang_pro CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE yizhan_bang_pro;

-- 用户相关表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    openid VARCHAR(100) UNIQUE,
    unionid VARCHAR(100),
    phone VARCHAR(20) UNIQUE,
    password VARCHAR(100),
    nickname VARCHAR(50),
    avatar VARCHAR(255),
    gender TINYINT DEFAULT 0 COMMENT '0-未知, 1-男, 2-女',
    birthday DATE,
    region VARCHAR(100),
    address VARCHAR(255),
    is_verified TINYINT DEFAULT 0 COMMENT '0-未实名认证, 1-已实名认证',
    user_level TINYINT DEFAULT 1 COMMENT '用户等级',
    user_points INT DEFAULT 0 COMMENT '用户积分',
    is_promoter TINYINT DEFAULT 0 COMMENT '0-普通用户, 1-推广员',
    is_admin TINYINT DEFAULT 0 COMMENT '0-普通用户, 1-管理员',
    status TINYINT DEFAULT 1 COMMENT '0-禁用, 1-正常',
    last_login_time DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 用户实名认证表
CREATE TABLE IF NOT EXISTS user_verification (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    real_name VARCHAR(50),
    id_card VARCHAR(20),
    id_card_front VARCHAR(255) COMMENT '身份证正面照片',
    id_card_back VARCHAR(255) COMMENT '身份证背面照片',
    status TINYINT DEFAULT 0 COMMENT '0-待审核, 1-已通过, 2-未通过',
    remark VARCHAR(255) COMMENT '审核备注',
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 钱包表
CREATE TABLE IF NOT EXISTS wallets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNIQUE,
    balance DECIMAL(10,2) DEFAULT 0.00,
    frozen_balance DECIMAL(10,2) DEFAULT 0.00,
    total_income DECIMAL(10,2) DEFAULT 0.00,
    total_withdraw DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 钱包交易记录表
CREATE TABLE IF NOT EXISTS wallet_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    type TINYINT COMMENT '1-充值, 2-消费, 3-退款, 4-佣金, 5-提现',
    amount DECIMAL(10,2),
    balance DECIMAL(10,2) COMMENT '交易后余额',
    order_id INT NULL COMMENT '相关订单ID',
    remark VARCHAR(255),
    status TINYINT DEFAULT 1 COMMENT '0-失败, 1-成功, 2-处理中',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 提现申请表
CREATE TABLE IF NOT EXISTS withdraw_applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    amount DECIMAL(10,2),
    real_name VARCHAR(50),
    account_type TINYINT COMMENT '1-微信, 2-支付宝, 3-银行卡',
    account_number VARCHAR(100),
    bank_name VARCHAR(100),
    status TINYINT DEFAULT 0 COMMENT '0-待审核, 1-已通过, 2-未通过, 3-已打款',
    remark VARCHAR(255),
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 驿站相关表
CREATE TABLE IF NOT EXISTS stations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    type TINYINT COMMENT '1-社区驿站, 2-校园驿站, 3-企业驿站, 4-商业驿站',
    region VARCHAR(100),
    address VARCHAR(255),
    longitude DECIMAL(10,6),
    latitude DECIMAL(10,6),
    contact_name VARCHAR(50),
    contact_phone VARCHAR(20),
    business_hours VARCHAR(100),
    images JSON COMMENT '驿站图片，JSON数组',
    monthly_income DECIMAL(10,2) COMMENT '月营业额',
    transfer_price DECIMAL(10,2) COMMENT '转让价格',
    transfer_reason TEXT,
    views INT DEFAULT 0 COMMENT '浏览次数',
    status TINYINT DEFAULT 1 COMMENT '1-正常运营, 2-转让中, 3-已转让, 4-已关闭',
    user_id INT COMMENT '所有者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 驿站认证表
CREATE TABLE IF NOT EXISTS station_verification (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    station_id INT,
    business_license VARCHAR(255) COMMENT '营业执照',
    other_certificates JSON COMMENT '其他证件，JSON数组',
    status TINYINT DEFAULT 0 COMMENT '0-待审核, 1-已通过, 2-未通过',
    remark VARCHAR(255),
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (station_id) REFERENCES stations(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 驿站转让表
CREATE TABLE IF NOT EXISTS station_transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    station_id INT,
    seller_id INT COMMENT '转让人ID',
    buyer_id INT NULL COMMENT '购买人ID',
    price DECIMAL(10,2),
    reason TEXT,
    images JSON COMMENT '相关图片，JSON数组',
    status TINYINT DEFAULT 0 COMMENT '0-待审核, 1-转让中, 2-已完成, 3-已取消',
    remark VARCHAR(255),
    contact_logs JSON COMMENT '联系记录，JSON数组',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (station_id) REFERENCES stations(id),
    FOREIGN KEY (seller_id) REFERENCES users(id),
    FOREIGN KEY (buyer_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 商品分类表
CREATE TABLE IF NOT EXISTS product_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50),
    icon VARCHAR(255),
    sort INT DEFAULT 0,
    status TINYINT DEFAULT 1 COMMENT '0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 设备分类表
CREATE TABLE IF NOT EXISTS equipment_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50),
    icon VARCHAR(255),
    sort INT DEFAULT 0,
    status TINYINT DEFAULT 1 COMMENT '0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 商品表
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT,
    name VARCHAR(100),
    cover VARCHAR(255),
    images JSON COMMENT '商品图片，JSON数组',
    description TEXT,
    price DECIMAL(10,2),
    original_price DECIMAL(10,2),
    stock INT DEFAULT 0,
    sold_count INT DEFAULT 0,
    is_hot TINYINT DEFAULT 0,
    is_new TINYINT DEFAULT 0,
    is_recommend TINYINT DEFAULT 0,
    views INT DEFAULT 0,
    sort INT DEFAULT 0,
    status TINYINT DEFAULT 1 COMMENT '0-下架, 1-上架',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES product_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 设备表
CREATE TABLE IF NOT EXISTS devices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT,
    user_id INT,
    title VARCHAR(100),
    description TEXT,
    brand VARCHAR(50),
    price DECIMAL(10,2),
    condition_type VARCHAR(20) COMMENT '设备状况',
    images JSON COMMENT '设备图片，JSON数组',
    views INT DEFAULT 0,
    status TINYINT DEFAULT 1 COMMENT '0-下架, 1-上架, 2-已售出',
    buyer_id INT NULL,
    deal_time DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES equipment_categories(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (buyer_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 服务表
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    title VARCHAR(100),
    description TEXT,
    time VARCHAR(100),
    address VARCHAR(255),
    price DECIMAL(10,2),
    additional_fields JSON,
    views INT DEFAULT 0,
    status TINYINT DEFAULT 1 COMMENT '0-下架, 1-上架',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 招聘信息表
CREATE TABLE IF NOT EXISTS jobs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    title VARCHAR(100),
    company VARCHAR(100),
    salary VARCHAR(50),
    requirements TEXT,
    address VARCHAR(255),
    description TEXT,
    additional_fields JSON,
    promote_status TINYINT DEFAULT 0 COMMENT '0-普通, 1-置顶',
    promote_expire_time DATETIME,
    views INT DEFAULT 0,
    status TINYINT DEFAULT 1 COMMENT '0-下架, 1-上架',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_no VARCHAR(50) UNIQUE,
    user_id INT,
    seller_id INT NULL,
    provider_id INT NULL,
    station_id INT NULL COMMENT '相关驿站ID',
    order_type TINYINT COMMENT '1-普通订单, 2-驿站服务订单, 3-设备购买订单, 4-服务订单',
    item_id INT NULL COMMENT '关联的商品或服务ID',
    total_amount DECIMAL(10,2),
    pay_amount DECIMAL(10,2),
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    shipping_fee DECIMAL(10,2) DEFAULT 0.00,
    pay_type TINYINT COMMENT '1-微信支付, 2-余额支付',
    pay_status TINYINT DEFAULT 0 COMMENT '0-未支付, 1-已支付, 2-已退款',
    order_status TINYINT DEFAULT 0 COMMENT '0-待支付, 1-待发货, 2-待收货, 3-已完成, 4-已取消, 5-已退款',
    transaction_id VARCHAR(64),
    receiver_name VARCHAR(50),
    receiver_phone VARCHAR(20),
    receiver_address VARCHAR(255),
    delivery_method VARCHAR(50),
    tracking_number VARCHAR(50),
    remark VARCHAR(255),
    paid_at TIMESTAMP NULL,
    shipped_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    cancelled_at TIMESTAMP NULL,
    refund_status TINYINT DEFAULT 0 COMMENT '0-无退款, 1-申请中, 2-已退款, 3-拒绝退款',
    refund_time DATETIME,
    refund_amount DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (seller_id) REFERENCES users(id),
    FOREIGN KEY (provider_id) REFERENCES users(id),
    FOREIGN KEY (station_id) REFERENCES stations(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 订单商品表
CREATE TABLE IF NOT EXISTS order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT,
    product_id INT,
    product_name VARCHAR(100),
    product_cover VARCHAR(255),
    price DECIMAL(10,2),
    quantity INT,
    total_amount DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 服务订单表
CREATE TABLE IF NOT EXISTS service_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT,
    service_id INT,
    provider_id INT COMMENT '服务提供者ID',
    requester_id INT COMMENT '服务请求者ID',
    status TINYINT DEFAULT 0 COMMENT '0-待接单, 1-已接单, 2-进行中, 3-已完成, 4-已取消',
    price DECIMAL(10,2),
    start_time DATETIME,
    end_time DATETIME,
    actual_hours DECIMAL(5,2),
    address VARCHAR(255),
    remarks TEXT,
    rating INT NULL COMMENT '评分',
    review TEXT NULL COMMENT '评价内容',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (service_id) REFERENCES services(id),
    FOREIGN KEY (provider_id) REFERENCES users(id),
    FOREIGN KEY (requester_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 支付日志表
CREATE TABLE IF NOT EXISTS payment_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT,
    order_no VARCHAR(50),
    user_id INT,
    amount DECIMAL(10,2),
    operation VARCHAR(50) COMMENT '操作类型',
    status TINYINT DEFAULT 0 COMMENT '0-失败, 1-成功, 2-处理中',
    message TEXT COMMENT '消息说明',
    raw_data TEXT COMMENT '原始数据',
    notify_data TEXT COMMENT '通知数据',
    request_data TEXT COMMENT '请求数据',
    response_data TEXT COMMENT '响应数据',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 用户地址表
CREATE TABLE IF NOT EXISTS addresses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    name VARCHAR(50),
    phone VARCHAR(20),
    province VARCHAR(50),
    city VARCHAR(50),
    district VARCHAR(50),
    address VARCHAR(255),
    is_default TINYINT DEFAULT 0 COMMENT '0-非默认, 1-默认',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 消息表
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    type VARCHAR(50) COMMENT '消息类型',
    title VARCHAR(100),
    content TEXT,
    is_read TINYINT DEFAULT 0 COMMENT '0-未读, 1-已读',
    related_id INT NULL COMMENT '相关ID',
    related_type VARCHAR(50) NULL COMMENT '相关类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 评价表
CREATE TABLE IF NOT EXISTS reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    item_id INT COMMENT '评价对象ID',
    item_type VARCHAR(20) COMMENT '评价对象类型',
    order_id INT NULL COMMENT '关联订单ID',
    content TEXT,
    rating INT COMMENT '评分',
    images JSON COMMENT '图片，JSON数组',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 系统活动记录表
CREATE TABLE IF NOT EXISTS system_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action_type VARCHAR(50) COMMENT '活动类型',
    description TEXT,
    ip VARCHAR(50),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(50) UNIQUE,
    config_value TEXT,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 佣金规则表
CREATE TABLE IF NOT EXISTS commission_rules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level INT COMMENT '分销等级',
    rate DECIMAL(5,2) COMMENT '佣金比例',
    description VARCHAR(255),
    status TINYINT DEFAULT 1 COMMENT '0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 推广码表
CREATE TABLE IF NOT EXISTS promotion_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    code VARCHAR(50) UNIQUE,
    times_used INT DEFAULT 0 COMMENT '使用次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 用户邀请关系表
CREATE TABLE IF NOT EXISTS user_invites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT COMMENT '被邀请用户',
    inviter_id INT COMMENT '邀请人',
    promotion_code VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 推广员申请表
CREATE TABLE IF NOT EXISTS promoter_applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    real_name VARCHAR(50),
    phone VARCHAR(20),
    reason TEXT,
    status TINYINT DEFAULT 0 COMMENT '0-待审核, 1-已通过, 2-未通过',
    remark VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 文件上传表
CREATE TABLE IF NOT EXISTS uploads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    path VARCHAR(255) NOT NULL,
    url VARCHAR(255) NOT NULL,
    thumb_url VARCHAR(255),
    mime_type VARCHAR(100) NOT NULL,
    size INT NOT NULL,
    user_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 初始数据
INSERT INTO system_configs (config_key, config_value, description)
VALUES 
('site_name', '驿站帮Pro', '站点名称'),
('site_logo', '/static/logo.png', '站点Logo'),
('site_description', '驿站帮Pro - 专业的驿站服务平台', '站点描述'),
('commission_enable', '1', '是否启用分销功能'),
('withdraw_min', '100', '最小提现金额'),
('withdraw_fee', '0', '提现手续费率'),
('service_phone', '************', '客服电话');

-- 初始化管理员账号（密码：admin123）
INSERT INTO users (phone, password, nickname, is_admin, status, created_at)
VALUES 
('13800138000', '$2a$10$NlkKxlEHf8TLtQCFRwCuw.83C2xgg2qW9lgE9jXFJ.iKsFhjL1Jd.', '管理员', 1, 1, NOW());

-- 创建索引
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_openid ON users(openid);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_stations_status ON stations(status);
CREATE INDEX idx_stations_user_id ON stations(user_id);
CREATE INDEX idx_devices_user_id ON devices(user_id);
CREATE INDEX idx_devices_status ON devices(status);
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_order_no ON orders(order_no);
CREATE INDEX idx_orders_status ON orders(order_status);
CREATE INDEX idx_service_orders_provider_id ON service_orders(provider_id);
CREATE INDEX idx_service_orders_requester_id ON service_orders(requester_id);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_payment_logs_order_id ON payment_logs(order_id);
CREATE INDEX idx_payment_logs_user_id ON payment_logs(user_id); 