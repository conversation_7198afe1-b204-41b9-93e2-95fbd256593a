const { DataTypes } = require('sequelize');
const { sequelize } = require('../sequelize');

const Equipment = sequelize.define('Equipment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  equipmentNo: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '设备编号'
  },
  sellerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '卖家用户ID'
  },
  buyerId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '买家用户ID'
  },
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: '设备标题'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '设备描述'
  },
  category: {
    type: DataTypes.ENUM('printer', 'scanner', 'computer', 'monitor', 'shelf', 'counter', 'security', 'other'),
    allowNull: false,
    comment: '设备类别：打印机、扫描仪、电脑、显示器、货架、柜台、安防、其他'
  },
  brand: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '品牌'
  },
  model: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: '型号'
  },
  condition: {
    type: DataTypes.ENUM('new', 'like_new', 'good', 'fair', 'poor'),
    allowNull: false,
    comment: '设备状况：全新、几乎全新、良好、一般、较差'
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '价格'
  },
  originalPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '原价'
  },
  purchaseDate: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '购买日期'
  },
  warrantyInfo: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '保修信息'
  },
  specifications: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '设备规格参数'
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '设备图片'
  },
  location: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '所在位置'
  },
  contactInfo: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '联系方式'
  },
  tradeType: {
    type: DataTypes.ENUM('sell', 'buy', 'exchange'),
    allowNull: false,
    defaultValue: 'sell',
    comment: '交易类型：出售、求购、交换'
  },
  deliveryMethod: {
    type: DataTypes.ENUM('pickup', 'delivery', 'express', 'both'),
    allowNull: false,
    defaultValue: 'pickup',
    comment: '交付方式：自提、送货、快递、都可以'
  },
  status: {
    type: DataTypes.ENUM('pending', 'published', 'negotiating', 'sold', 'cancelled'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '交易状态'
  },
  viewCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '浏览次数'
  },
  favoriteCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '收藏次数'
  },
  publishedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '发布时间'
  },
  soldAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '售出时间'
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '过期时间'
  },
  isUrgent: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否紧急'
  },
  isTop: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否置顶'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  }
}, {
  tableName: 'equipments',
  timestamps: true,
  paranoid: true, // 软删除
  indexes: [
    {
      fields: ['sellerId']
    },
    {
      fields: ['buyerId']
    },
    {
      fields: ['category']
    },
    {
      fields: ['condition']
    },
    {
      fields: ['tradeType']
    },
    {
      fields: ['status']
    },
    {
      fields: ['publishedAt']
    },
    {
      fields: ['price']
    }
  ]
});

module.exports = Equipment;
