const { DataTypes } = require('sequelize');
const { sequelize } = require('../sequelize');

const Order = sequelize.define('Order', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  orderNo: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '订单号'
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '用户ID'
  },
  stationId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '驿站ID'
  },
  serviceType: {
    type: DataTypes.ENUM('express_pickup', 'express_send', 'print_copy', 'other'),
    allowNull: false,
    defaultValue: 'express_pickup',
    comment: '服务类型：快递代收、快递代发、打印复印、其他'
  },
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: '订单标题'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '订单描述'
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: '订单金额'
  },
  status: {
    type: DataTypes.ENUM('pending', 'paid', 'processing', 'completed', 'cancelled', 'refunded'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '订单状态'
  },
  paymentMethod: {
    type: DataTypes.ENUM('wechat', 'alipay', 'balance'),
    allowNull: true,
    comment: '支付方式'
  },
  paymentTime: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '支付时间'
  },
  completedTime: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '完成时间'
  },
  cancelledTime: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '取消时间'
  },
  cancelReason: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '取消原因'
  },
  refundAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '退款金额'
  },
  refundTime: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '退款时间'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  }
}, {
  tableName: 'orders',
  timestamps: true,
  paranoid: true, // 软删除
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['stationId']
    },
    {
      fields: ['status']
    },
    {
      fields: ['serviceType']
    },
    {
      fields: ['createdAt']
    }
  ]
});

module.exports = Order;
