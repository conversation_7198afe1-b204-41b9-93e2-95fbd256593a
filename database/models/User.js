const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const { sequelize } = require('../sequelize');

// 创建用户模型
const User = sequelize.define('User', {
  // ID字段会自动创建
  
  // 基本信息
  nickname: {
    type: DataTypes.STRING,
    allowNull: true
  },
  avatar: {
    type: DataTypes.STRING,
    allowNull: true,
    defaultValue: ''
  },
  gender: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0, // 0: 未知, 1: 男, 2: 女
    validate: {
      isIn: [[0, 1, 2]]
    }
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  password: {
    type: DataTypes.STRING,
    allowNull: true
  },
  
  // 微信信息
  openid: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  unionid: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  sessionKey: {
    type: DataTypes.STRING,
    allowNull: true
  },
  
  // 用户状态
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  isVerified: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false
  },
  
  // 用户角色 - 存储为JSON数组
  roles: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: ['user'],
    get() {
      const rawValue = this.getDataValue('roles');
      return rawValue ? rawValue : ['user'];
    },
    set(value) {
      this.setDataValue('roles', Array.isArray(value) ? value : ['user']);
    }
  },
  
  // 用户标签 - 存储为JSON数组
  tags: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: []
  },
  
  // 注册信息
  registerIp: {
    type: DataTypes.STRING,
    allowNull: true
  },
  lastLoginIp: {
    type: DataTypes.STRING,
    allowNull: true
  },
  lastLoginTime: {
    type: DataTypes.DATE,
    allowNull: true
  },
  loginCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  registerSource: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'other',
    validate: {
      isIn: [['wechat', 'phone', 'email', 'other']]
    }
  },
  
  // 数据统计
  orderCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  
  // 实名认证 - 存储为JSON对象
  realNameAuth: {
    type: DataTypes.JSON,
    allowNull: false,
    defaultValue: {
      isVerified: false,
      realName: null,
      idCardNumber: null,
      idCardFrontUrl: null,
      idCardBackUrl: null,
      verifyTime: null
    }
  }
}, {
  // 模型选项
  tableName: 'users',
  timestamps: true, // 创建 createdAt 和 updatedAt
  // 使用下划线命名约定
  underscored: true,
  indexes: [
    {
      fields: ['created_at']
    },
    {
      fields: ['updated_at']
    },
    {
      fields: ['phone']
    },
    {
      fields: ['email']
    },
    {
      fields: ['openid']
    }
  ],
  defaultScope: {
    attributes: { exclude: ['password', 'sessionKey'] }
  },
  scopes: {
    withPassword: {
      attributes: { include: ['password'] }
    }
  }
});

// 密码加密的钩子
User.beforeCreate(async (user) => {
  if (user.password) {
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(user.password, salt);
  }
});

User.beforeUpdate(async (user) => {
  if (user.changed('password')) {
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(user.password, salt);
  }
});

// 实例方法
User.prototype.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

module.exports = User; 
