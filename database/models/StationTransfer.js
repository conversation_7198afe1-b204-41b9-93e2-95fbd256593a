const { DataTypes } = require('sequelize');
const { sequelize } = require('../sequelize');

const StationTransfer = sequelize.define('StationTransfer', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  transferNo: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '转让编号'
  },
  sellerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '卖家用户ID'
  },
  buyerId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '买家用户ID'
  },
  stationId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '驿站ID'
  },
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: '转让标题'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '转让描述'
  },
  price: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false,
    comment: '转让价格'
  },
  originalPrice: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: true,
    comment: '原价'
  },
  transferType: {
    type: DataTypes.ENUM('full_transfer', 'share_transfer', 'lease'),
    allowNull: false,
    defaultValue: 'full_transfer',
    comment: '转让类型：完全转让、股份转让、租赁'
  },
  businessInfo: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '经营信息：月收入、客流量等'
  },
  facilities: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '设施设备清单'
  },
  location: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '位置信息'
  },
  images: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '转让图片'
  },
  contactInfo: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '联系方式'
  },
  status: {
    type: DataTypes.ENUM('pending', 'published', 'negotiating', 'completed', 'cancelled'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '转让状态'
  },
  viewCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '浏览次数'
  },
  favoriteCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '收藏次数'
  },
  publishedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '发布时间'
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '完成时间'
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '过期时间'
  },
  isUrgent: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否紧急'
  },
  isTop: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否置顶'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  }
}, {
  tableName: 'station_transfers',
  timestamps: true,
  paranoid: true, // 软删除
  indexes: [
    {
      fields: ['sellerId']
    },
    {
      fields: ['buyerId']
    },
    {
      fields: ['stationId']
    },
    {
      fields: ['status']
    },
    {
      fields: ['transferType']
    },
    {
      fields: ['publishedAt']
    },
    {
      fields: ['price']
    }
  ]
});

module.exports = StationTransfer;
