const { DataTypes } = require('sequelize');
const { sequelize } = require('../sequelize');

const Job = sequelize.define('Job', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  jobNo: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '职位编号'
  },
  publisherId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '发布者用户ID'
  },
  stationId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '关联驿站ID'
  },
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: '职位标题'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '职位描述'
  },
  jobType: {
    type: DataTypes.ENUM('recruitment', 'job_seeking'),
    allowNull: false,
    comment: '类型：招聘、求职'
  },
  position: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '职位名称'
  },
  category: {
    type: DataTypes.ENUM('station_manager', 'clerk', 'delivery', 'customer_service', 'maintenance', 'part_time', 'other'),
    allowNull: false,
    comment: '职位类别：站长、店员、配送员、客服、维修、兼职、其他'
  },
  workType: {
    type: DataTypes.ENUM('full_time', 'part_time', 'temporary', 'internship'),
    allowNull: false,
    comment: '工作类型：全职、兼职、临时、实习'
  },
  salaryType: {
    type: DataTypes.ENUM('monthly', 'daily', 'hourly', 'piece_rate', 'negotiable'),
    allowNull: false,
    comment: '薪资类型：月薪、日薪、时薪、计件、面议'
  },
  salaryMin: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '最低薪资'
  },
  salaryMax: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '最高薪资'
  },
  workLocation: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '工作地点'
  },
  workTime: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '工作时间'
  },
  requirements: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '任职要求'
  },
  benefits: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '福利待遇'
  },
  contactInfo: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '联系方式'
  },
  experienceRequired: {
    type: DataTypes.ENUM('none', 'less_than_1', '1_to_3', '3_to_5', 'more_than_5'),
    allowNull: false,
    defaultValue: 'none',
    comment: '经验要求：无要求、1年以下、1-3年、3-5年、5年以上'
  },
  educationRequired: {
    type: DataTypes.ENUM('none', 'primary', 'junior', 'senior', 'college', 'bachelor', 'master'),
    allowNull: false,
    defaultValue: 'none',
    comment: '学历要求：无要求、小学、初中、高中、大专、本科、硕士'
  },
  ageMin: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '最小年龄'
  },
  ageMax: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '最大年龄'
  },
  genderRequirement: {
    type: DataTypes.ENUM('none', 'male', 'female'),
    allowNull: false,
    defaultValue: 'none',
    comment: '性别要求：无要求、男、女'
  },
  status: {
    type: DataTypes.ENUM('pending', 'published', 'recruiting', 'paused', 'completed', 'cancelled'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '状态'
  },
  viewCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '浏览次数'
  },
  applicationCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '申请次数'
  },
  publishedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '发布时间'
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '过期时间'
  },
  isUrgent: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否紧急'
  },
  isTop: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否置顶'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  }
}, {
  tableName: 'jobs',
  timestamps: true,
  paranoid: true, // 软删除
  indexes: [
    {
      fields: ['publisherId']
    },
    {
      fields: ['stationId']
    },
    {
      fields: ['jobType']
    },
    {
      fields: ['category']
    },
    {
      fields: ['workType']
    },
    {
      fields: ['status']
    },
    {
      fields: ['publishedAt']
    },
    {
      fields: ['salaryMin', 'salaryMax']
    }
  ]
});

module.exports = Job;
