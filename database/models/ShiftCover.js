const { DataTypes } = require('sequelize');
const { sequelize } = require('../sequelize');

const ShiftCover = sequelize.define('ShiftCover', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  coverNo: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    comment: '顶班编号'
  },
  requesterId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '请求者用户ID'
  },
  coverId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '顶班者用户ID'
  },
  stationId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '驿站ID'
  },
  title: {
    type: DataTypes.STRING(200),
    allowNull: false,
    comment: '顶班标题'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '顶班描述'
  },
  shiftType: {
    type: DataTypes.ENUM('request_cover', 'offer_cover'),
    allowNull: false,
    comment: '类型：请求顶班、提供顶班'
  },
  urgencyLevel: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    allowNull: false,
    defaultValue: 'medium',
    comment: '紧急程度：低、中、高、紧急'
  },
  shiftDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    comment: '顶班日期'
  },
  startTime: {
    type: DataTypes.TIME,
    allowNull: false,
    comment: '开始时间'
  },
  endTime: {
    type: DataTypes.TIME,
    allowNull: false,
    comment: '结束时间'
  },
  duration: {
    type: DataTypes.DECIMAL(4, 2),
    allowNull: false,
    comment: '时长（小时）'
  },
  paymentType: {
    type: DataTypes.ENUM('hourly', 'fixed', 'negotiable', 'free'),
    allowNull: false,
    defaultValue: 'hourly',
    comment: '付费类型：按小时、固定金额、面议、免费'
  },
  paymentAmount: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    comment: '付费金额'
  },
  requirements: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '要求条件'
  },
  workContent: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '工作内容'
  },
  contactInfo: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '联系方式'
  },
  location: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '位置信息'
  },
  reason: {
    type: DataTypes.STRING(500),
    allowNull: true,
    comment: '顶班原因'
  },
  specialInstructions: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '特殊说明'
  },
  status: {
    type: DataTypes.ENUM('pending', 'published', 'applied', 'confirmed', 'in_progress', 'completed', 'cancelled'),
    allowNull: false,
    defaultValue: 'pending',
    comment: '状态'
  },
  viewCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '浏览次数'
  },
  applicationCount: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '申请次数'
  },
  publishedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '发布时间'
  },
  confirmedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '确认时间'
  },
  startedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '开始时间'
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '完成时间'
  },
  rating: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    comment: '评分'
  },
  feedback: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '反馈评价'
  },
  isUrgent: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否紧急'
  },
  isRecurring: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否重复'
  },
  recurringPattern: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: '重复模式'
  },
  remark: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '备注'
  }
}, {
  tableName: 'shift_covers',
  timestamps: true,
  paranoid: true, // 软删除
  indexes: [
    {
      fields: ['requesterId']
    },
    {
      fields: ['coverId']
    },
    {
      fields: ['stationId']
    },
    {
      fields: ['shiftType']
    },
    {
      fields: ['status']
    },
    {
      fields: ['shiftDate']
    },
    {
      fields: ['urgencyLevel']
    },
    {
      fields: ['publishedAt']
    }
  ]
});

module.exports = ShiftCover;
