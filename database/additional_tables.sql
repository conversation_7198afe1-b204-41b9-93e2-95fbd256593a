-- 额外的数据库表
USE yizhanbang;

-- 管理员表
CREATE TABLE IF NOT EXISTS admin_users (
  id VARCHAR(32) PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  name VARCHAR(50),
  avatar VARCHAR(255),
  phone VARCHAR(20),
  email VARCHAR(100),
  role VARCHAR(20) NOT NULL DEFAULT 'admin', -- 'super_admin', 'admin', 'editor'
  status VARCHAR(20) NOT NULL DEFAULT 'active', -- 'active', 'inactive', 'locked'
  last_login_time DATETIME,
  create_time DATETIME NOT NULL,
  update_time DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 用户实名认证表
CREATE TABLE IF NOT EXISTS user_verification (
  id VARCHAR(32) PRIMARY KEY,
  user_id VARCHAR(32) NOT NULL,
  real_name VARCHAR(50) NOT NULL,
  id_card_number VARCHAR(20) NOT NULL,
  id_card_front_image VARCHAR(255) NOT NULL,
  id_card_back_image VARCHAR(255) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
  reason VARCHAR(255), -- 拒绝原因
  submit_time DATETIME NOT NULL,
  verify_time DATETIME,
  verify_admin_id VARCHAR(32),
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (verify_admin_id) REFERENCES admin_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 驿站认证表
CREATE TABLE IF NOT EXISTS station_verification (
  id VARCHAR(32) PRIMARY KEY,
  user_id VARCHAR(32) NOT NULL,
  station_name VARCHAR(100) NOT NULL,
  business_license_image VARCHAR(255) NOT NULL,
  station_address VARCHAR(255) NOT NULL,
  contact_name VARCHAR(50) NOT NULL,
  contact_phone VARCHAR(20) NOT NULL,
  other_images JSON,
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
  reason VARCHAR(255), -- 拒绝原因
  submit_time DATETIME NOT NULL,
  verify_time DATETIME,
  verify_admin_id VARCHAR(32),
  FOREIGN KEY (user_id) REFERENCES users(id),
  FOREIGN KEY (verify_admin_id) REFERENCES admin_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 顶班接单表
CREATE TABLE IF NOT EXISTS service_orders (
  id VARCHAR(32) PRIMARY KEY,
  service_id VARCHAR(32) NOT NULL,
  provider_id VARCHAR(32) NOT NULL, -- 服务提供者ID
  requester_id VARCHAR(32) NOT NULL, -- 服务请求者ID
  status VARCHAR(20) NOT NULL DEFAULT 'pending', -- 'pending', 'accepted', 'in_progress', 'completed', 'cancelled'
  price DECIMAL(10,2) NOT NULL,
  start_time DATETIME,
  end_time DATETIME,
  actual_hours DECIMAL(5,2),
  address VARCHAR(255),
  remarks TEXT,
  create_time DATETIME NOT NULL,
  update_time DATETIME,
  order_id VARCHAR(32), -- 关联的支付订单ID
  rating INT, -- 评分
  review TEXT, -- 评价内容
  FOREIGN KEY (service_id) REFERENCES services(id),
  FOREIGN KEY (provider_id) REFERENCES users(id),
  FOREIGN KEY (requester_id) REFERENCES users(id),
  FOREIGN KEY (order_id) REFERENCES orders(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 管理员操作日志表
CREATE TABLE IF NOT EXISTS admin_logs (
  id VARCHAR(32) PRIMARY KEY,
  admin_id VARCHAR(32) NOT NULL,
  action VARCHAR(50) NOT NULL, -- 'login', 'logout', 'create', 'update', 'delete', 'approve', 'reject', etc.
  target_type VARCHAR(50) NOT NULL, -- 'user', 'station', 'device', 'service', 'job', 'order', 'product', etc.
  target_id VARCHAR(32),
  details TEXT,
  ip_address VARCHAR(50),
  user_agent TEXT,
  create_time DATETIME NOT NULL,
  FOREIGN KEY (admin_id) REFERENCES admin_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 商品表
CREATE TABLE IF NOT EXISTS products (
  id VARCHAR(32) PRIMARY KEY,
  category_id VARCHAR(32) NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  original_price DECIMAL(10,2),
  main_image VARCHAR(255) NOT NULL,
  images JSON,
  specifications JSON, -- 商品规格JSON格式
  status VARCHAR(20) NOT NULL DEFAULT 'draft', -- 'draft', 'active', 'inactive', 'out_of_stock'
  is_hot BOOLEAN DEFAULT FALSE, -- 是否热门
  is_new BOOLEAN DEFAULT FALSE, -- 是否新品
  is_recommended BOOLEAN DEFAULT FALSE, -- 是否推荐
  sales_count INT DEFAULT 0, -- 销量
  views INT DEFAULT 0, -- 浏览量
  create_time DATETIME NOT NULL,
  update_time DATETIME,
  FOREIGN KEY (category_id) REFERENCES product_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 商品分类表
CREATE TABLE IF NOT EXISTS product_categories (
  id VARCHAR(32) PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  description VARCHAR(255),
  parent_id VARCHAR(32), -- NULL表示一级分类
  icon VARCHAR(255),
  sort_order INT DEFAULT 0, -- 排序字段
  status VARCHAR(20) NOT NULL DEFAULT 'active', -- 'active', 'inactive'
  create_time DATETIME NOT NULL,
  update_time DATETIME,
  FOREIGN KEY (parent_id) REFERENCES product_categories(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 商品库存表
CREATE TABLE IF NOT EXISTS product_inventory (
  id VARCHAR(32) PRIMARY KEY,
  product_id VARCHAR(32) NOT NULL,
  sku VARCHAR(50) NOT NULL, -- 库存单位
  specification_values JSON, -- 规格值JSON格式
  quantity INT NOT NULL DEFAULT 0,
  warn_quantity INT DEFAULT 10, -- 库存预警数量
  create_time DATETIME NOT NULL,
  update_time DATETIME,
  FOREIGN KEY (product_id) REFERENCES products(id),
  UNIQUE KEY (product_id, sku)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 库存操作记录表
CREATE TABLE IF NOT EXISTS inventory_logs (
  id VARCHAR(32) PRIMARY KEY,
  product_id VARCHAR(32) NOT NULL,
  sku VARCHAR(50) NOT NULL,
  type VARCHAR(20) NOT NULL, -- 'in', 'out', 'adjust'
  quantity INT NOT NULL, -- 正数表示增加，负数表示减少
  before_quantity INT NOT NULL, -- 操作前库存
  after_quantity INT NOT NULL, -- 操作后库存
  reason VARCHAR(50), -- 'purchase', 'sale', 'return', 'adjustment', 'damage'
  order_id VARCHAR(32), -- 关联的订单ID
  operator_id VARCHAR(32) NOT NULL, -- 操作员ID
  remarks TEXT,
  create_time DATETIME NOT NULL,
  FOREIGN KEY (product_id) REFERENCES products(id),
  FOREIGN KEY (operator_id) REFERENCES admin_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 促销活动表
CREATE TABLE IF NOT EXISTS promotions (
  id VARCHAR(32) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  type VARCHAR(20) NOT NULL, -- 'discount', 'sale', 'gift', 'coupon', 'bundle'
  discount_type VARCHAR(20), -- 'percentage', 'fixed_amount'
  discount_value DECIMAL(10,2), -- 折扣值
  start_time DATETIME NOT NULL,
  end_time DATETIME NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'draft', -- 'draft', 'active', 'inactive', 'expired'
  target_type VARCHAR(20) NOT NULL, -- 'product', 'category', 'all'
  target_ids JSON, -- 目标产品或分类ID数组
  rules JSON, -- 促销规则JSON格式
  create_time DATETIME NOT NULL,
  update_time DATETIME
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 为已有表添加字段
ALTER TABLE stations ADD COLUMN views INT DEFAULT 0;
ALTER TABLE devices ADD COLUMN views INT DEFAULT 0;
ALTER TABLE jobs ADD COLUMN views INT DEFAULT 0;
ALTER TABLE services ADD COLUMN views INT DEFAULT 0;

-- 为招聘信息添加额外字段存储
ALTER TABLE jobs ADD COLUMN additional_fields JSON;

-- 为服务信息添加额外字段存储
ALTER TABLE services ADD COLUMN additional_fields JSON;

-- 创建文件上传表
CREATE TABLE IF NOT EXISTS uploads (
  id VARCHAR(32) PRIMARY KEY,
  filename VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL,
  path VARCHAR(255) NOT NULL,
  url VARCHAR(255) NOT NULL,
  thumb_url VARCHAR(255),
  mime_type VARCHAR(100) NOT NULL,
  size INT NOT NULL,
  user_id VARCHAR(32),
  upload_time DATETIME NOT NULL,
  FOREIGN KEY (user_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建审核记录表
CREATE TABLE IF NOT EXISTS review_logs (
  id VARCHAR(32) PRIMARY KEY,
  item_id VARCHAR(32) NOT NULL,
  item_type VARCHAR(20) NOT NULL,
  status VARCHAR(20) NOT NULL,
  reviewer_id VARCHAR(32) NOT NULL,
  review_time DATETIME NOT NULL,
  comment TEXT,
  FOREIGN KEY (reviewer_id) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建索引
CREATE INDEX idx_user_verification_user_id ON user_verification(user_id);
CREATE INDEX idx_user_verification_status ON user_verification(status);
CREATE INDEX idx_station_verification_user_id ON station_verification(user_id);
CREATE INDEX idx_station_verification_status ON station_verification(status);
CREATE INDEX idx_service_orders_service_id ON service_orders(service_id);
CREATE INDEX idx_service_orders_provider_id ON service_orders(provider_id);
CREATE INDEX idx_service_orders_requester_id ON service_orders(requester_id);
CREATE INDEX idx_service_orders_status ON service_orders(status);
CREATE INDEX idx_admin_logs_admin_id ON admin_logs(admin_id);
CREATE INDEX idx_admin_logs_action ON admin_logs(action);
CREATE INDEX idx_admin_logs_target_type ON admin_logs(target_type);
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_product_categories_parent_id ON product_categories(parent_id);
CREATE INDEX idx_product_inventory_product_id ON product_inventory(product_id);
CREATE INDEX idx_promotions_status ON promotions(status);
CREATE INDEX idx_promotions_type ON promotions(type);
CREATE INDEX idx_uploads_user_id ON uploads(user_id);
CREATE INDEX idx_uploads_filename ON uploads(filename);
CREATE INDEX idx_review_logs_item_id ON review_logs(item_id);
CREATE INDEX idx_review_logs_item_type ON review_logs(item_type);
CREATE INDEX idx_review_logs_reviewer_id ON review_logs(reviewer_id);
CREATE INDEX idx_review_logs_status ON review_logs(status); 