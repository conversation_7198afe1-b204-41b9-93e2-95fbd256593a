# 驿站帮Pro统一数据库接口

本项目提供了驿站帮Pro小程序和后台管理系统的统一数据库接口，方便两个系统共享数据，避免数据孤岛。

## 目录结构

```
database/
  ├── config.js          # 统一配置文件
  ├── db-interface.js    # 统一数据库接口
  ├── init-db.js         # 数据库初始化脚本
  ├── unified_schema.sql # 数据库结构SQL
  ├── api-test.js        # 接口测试工具
  ├── api-example.js     # 接口使用示例
  └── .env               # 环境变量文件
```

## 安装依赖

```bash
cd database
npm install
```

## 配置数据库

1. 复制`.env.example`为`.env`并配置数据库连接信息：

```bash
cp .env.example .env
```

2. 编辑`.env`文件，设置数据库连接信息：

```
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=你的数据库用户名
DB_PASSWORD=你的数据库密码
DB_NAME=yizhanbang
```

## 初始化数据库

运行以下命令创建和初始化数据库：

```bash
npm run init
# 或者直接运行
node init-db.js
```

## 测试数据库接口

运行以下命令测试数据库接口是否正常工作：

```bash
npm test
# 或者直接运行
node api-test.js
```

## 使用数据库接口

### 在后台管理系统中使用

1. 在`admin-server/src/db/db.js`中已集成统一数据库接口
2. 使用示例：

```javascript
const query = require('../db/db');

async function getUsers() {
  return await query('SELECT * FROM users LIMIT 10');
}
```

### 在小程序中使用

1. 将`db-interface.js`复制到小程序服务端
2. 使用示例：

```javascript
const db = require('../../database/db-interface');

async function getUserById(id) {
  return await db.findOne('users', { id });
}
```

### 高级用法

可以参考`api-example.js`中的服务封装示例，对数据库操作进行进一步抽象，使代码更加整洁：

```javascript
const { UserService } = require('../../database/api-example');

// 查询用户
const user = await UserService.findByPhone('13800138000');

// 更新用户
await UserService.update(1, { nickname: '新昵称' });
```

## 注意事项

1. 确保MySQL服务已启动
2. 数据库用户需要有创建数据库和表的权限
3. 生产环境使用前请修改数据库密码和JWT密钥 