module.exports = {
  // 聚合数据短信API配置
  apiKey: 'your_api_key_here', // 替换为您的API密钥
  
  // 短信模板ID
  loginTemplateId: 123456,      // 登录验证码模板ID
  registerTemplateId: 123457,   // 注册验证码模板ID
  resetPasswordTemplateId: 123458, // 重置密码验证码模板ID
  
  // 短信签名 (如果需要在vars中指定)
  signName: '驿站帮',
  
  // 验证码有效期（秒）
  expireTime: 600,  // 10分钟
  
  // 发送频率限制
  sendLimit: {
    interval: 60,   // 两次发送间隔（秒）
    hourLimit: 5,   // 每小时最大发送次数
    dayLimit: 20    // 每天最大发送次数
  }
}; 