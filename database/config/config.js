const smsConfig = require('./smsConfig');

module.exports = {
  // 环境配置: development, production, test
  environment: process.env.NODE_ENV || 'development',
  
  // 数据库配置
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'yizhan_db',
    dialect: 'mysql',
    timezone: '+08:00',
    logging: process.env.NODE_ENV === 'development' ? console.log : false
  },
  
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    jwtSecret: process.env.JWT_SECRET || 'your-secret-key',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',
    jwtRefreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d'
  },
  
  // 微信小程序配置
  wechat: {
    appId: process.env.WX_APPID || 'your-appid',
    appSecret: process.env.WX_SECRET || 'your-secret'
  },
  
  // 短信API配置
  sms: smsConfig
}; 