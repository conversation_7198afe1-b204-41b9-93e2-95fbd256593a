/**
 * 数据库初始化脚本
 * 用于创建和初始化数据库
 */

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const config = require('./config');
const readline = require('readline');

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 提示用户确认
async function confirm(message) {
  return new Promise((resolve) => {
    rl.question(`${message} (y/n): `, (answer) => {
      resolve(answer.toLowerCase() === 'y');
    });
  });
}

async function main() {
  let connection;
  try {
    console.log('开始初始化数据库...');
    
    // 检查环境
    if (process.env.NODE_ENV === 'production') {
      const isConfirmed = await confirm('您正在生产环境中运行此脚本，这可能会删除现有数据。确定要继续吗？');
      if (!isConfirmed) {
        console.log('操作已取消');
        rl.close();
        return;
      }
    }
    
    // 创建数据库连接
    console.log('正在连接到MySQL服务器...');
    connection = await mysql.createConnection({
      host: config.database.host,
      port: config.database.port,
      user: config.database.user,
      password: config.database.password
    });
    
    // 读取SQL文件
    console.log('正在读取SQL文件...');
    const sqlFilePath = path.join(__dirname, 'unified_schema.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // 按语句分割SQL
    const sqlStatements = sql.split(';').filter(statement => statement.trim());
    
    // 逐条执行SQL语句
    console.log('开始执行SQL语句...');
    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i].trim();
      if (statement) {
        await connection.query(statement);
        process.stdout.write(`执行进度: ${Math.round((i + 1) / sqlStatements.length * 100)}%\r`);
      }
    }
    
    console.log('\n数据库初始化完成！');
    
    // 创建日志目录
    const logDir = path.join(__dirname, '../logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
      console.log('日志目录已创建');
    }
    
    // 创建上传目录
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
      console.log('上传目录已创建');
    }
    
    console.log('所有操作已完成！');
  } catch (error) {
    console.error('初始化数据库失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
    rl.close();
  }
}

// 执行主函数
main(); 