// 导入必要的模块
const { Sequelize } = require('sequelize');
const path = require('path');
const config = require('../config');

// 确保环境变量已加载
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

console.log('数据库配置:', {
  host: config.database.host,
  port: config.database.port,
  user: config.database.user,
  password: config.database.password ? '******' : 'no password',
  database: config.database.database,
  dialect: config.database.dialect
});

// 创建Sequelize实例
const sequelize = new Sequelize(
  config.database.database,
  config.database.user,
  config.database.password,
  {
    host: config.database.host,
    port: config.database.port,
    dialect: config.database.dialect,
    timezone: config.database.timezone,
    define: {
      charset: config.database.charset,
      collate: config.database.collate,
      timestamps: true,
      underscored: true,
      freezeTableName: false
    },
    // 添加dialectOptions配置，处理严格模式下的日期问题
    dialectOptions: {
      dateStrings: true,
      typeCast: true,
      timezone: config.database.timezone
    },
    logging: config.database.logging,
    pool: config.database.pool
  }
);

// 导出Sequelize实例
module.exports = {
  sequelize,
  Sequelize
}; 