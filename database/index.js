const { Sequelize } = require('sequelize');
const config = require('./config');

// 获取数据库配置
const dbConfig = config.database;

// 创建Sequelize实例
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.user,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    timezone: dbConfig.timezone,
    define: {
      charset: dbConfig.charset,
      collate: dbConfig.collate,
      timestamps: true, // 自动添加 createdAt 和 updatedAt 字段
      underscored: false, // 不使用下划线命名
      freezeTableName: false // 不冻结表名，Sequelize会自动将表名转为复数
    },
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    pool: {
      max: dbConfig.connectionLimit,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  }
);

// 连接数据库
async function connectDB() {
  try {
    await sequelize.authenticate();
    console.log(`MySQL连接成功: ${dbConfig.host}:${dbConfig.port}/${dbConfig.database}`);
    
    // 如果是开发环境，可以自动同步数据库模型（慎用）
    if (process.env.NODE_ENV === 'development' && process.env.SYNC_DB === 'true') {
      console.log('正在同步数据库模型...');
      await sequelize.sync({ alter: true });
      console.log('数据库模型同步完成');
    }
    
    return sequelize;
  } catch (error) {
    console.error('MySQL连接失败:', error);
    // 5秒后重试
    console.log('5秒后尝试重新连接...');
    setTimeout(connectDB, 5000);
  }
}

// 处理应用关闭
process.on('SIGINT', async () => {
  try {
    await sequelize.close();
    console.log('应用终止，MySQL连接关闭');
    process.exit(0);
  } catch (error) {
    console.error('关闭MySQL连接失败:', error);
    process.exit(1);
  }
});

module.exports = {
  connectDB,
  sequelize,
  Sequelize
}; 