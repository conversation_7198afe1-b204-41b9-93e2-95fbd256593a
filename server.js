// 导入必要的模块
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
// const helmet = require('helmet');
// const rateLimit = require('express-rate-limit');
const { sequelize } = require('./database/sequelize');
const authRoutes = require('./database/routes/authRoutes');
const smsRoutes = require('./database/routes/smsRoutes');
const config = require('./config');
// const { errorHandler, notFoundHandler } = require('./middleware/errorHandler');

// 确保环境变量已加载
require('dotenv').config();

// 创建Express应用
const app = express();

// 连接数据库并自动同步模型
(async () => {
  try {
    // 验证数据库连接
    await sequelize.authenticate();
    console.log('MySQL数据库连接成功');

    // 如果需要自动同步数据库模型（由配置控制）
    if (config.database.sync) {
      console.log('正在同步数据库模型...');
      // 导入所有模型确保它们已注册
      require('./database/models/User');
      // 添加其他模型导入...

      // 使用更安全的同步方式：仅创建不存在的表，不修改已有表结构
      await sequelize.sync({ force: false });
      console.log('数据库模型同步完成');
    }
  } catch (error) {
    console.error('数据库连接或同步失败:', error);
    // 只记录错误，不要立即退出进程，让应用继续运行
    console.error('服务器将继续运行，但某些数据库功能可能不可用');
  }
})();

// 安全中间件
// app.use(helmet(config.security.helmet));

// 跨域配置
app.use(cors());

// 请求限流
// const limiter = rateLimit(config.api.rateLimit);
// app.use(limiter);

// 请求解析中间件
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'dist')));

// API路由
app.use(`${config.api.prefix}/auth`, authRoutes);
app.use(`${config.api.prefix}/sms`, smsRoutes);

// 测试路由
app.get(`${config.api.prefix}/test`, (req, res) => {
  res.json({
    code: 0,
    message: '服务器运行正常',
    time: new Date().toISOString(),
    env: config.server.env,
    database: 'MySQL',
    version: '1.0.0'
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: config.IS_DEVELOPMENT ? err.message : undefined
  });
});

// 所有其他请求返回index.html - 用于前端路由
// 注意：确保这个路由放在最后，避免拦截API请求
app.get('*', (req, res) => {
  // API请求返回404
  if (req.path.startsWith(config.api.prefix)) {
    return res.status(404).json({
      code: 404,
      message: `路径 ${req.originalUrl} 不存在`,
      timestamp: new Date().toISOString()
    });
  }
  // 其他请求返回前端页面
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// 启动服务器
const server = app.listen(config.server.port, config.server.host, () => {
  console.log(`服务器运行在端口: ${config.server.port}`);
  console.log(`环境: ${config.server.env}`);
  console.log(`后台管理系统: http://localhost:${config.server.port}`);
  console.log('API接口已启用，小程序与后台管理系统可以进行数据互通');
  console.log(`测试API: http://localhost:${config.server.port}${config.api.prefix}/test`);
});

// 优雅关闭服务器
process.on('SIGTERM', async () => {
  console.log('SIGTERM信号接收到，关闭服务器...');
  server.close(async () => {
    console.log('HTTP服务器已关闭');
    // 关闭数据库连接
    try {
      await sequelize.close();
      console.log('数据库连接已关闭');
      process.exit(0);
    } catch (error) {
      console.error('关闭数据库连接时出错:', error);
      process.exit(1);
    }
  });
}); 