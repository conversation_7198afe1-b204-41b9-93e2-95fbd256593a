// 导入必要的模块
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const { sequelize } = require('./database/sequelize');
const authRoutes = require('./database/routes/authRoutes');
const smsRoutes = require('./database/routes/smsRoutes');
require('dotenv').config();

// 创建Express应用
const app = express();

// 连接数据库并自动同步模型
(async () => {
  try {
    // 验证数据库连接
    await sequelize.authenticate();
    console.log('MySQL数据库连接成功');
    
    // 如果需要自动同步数据库模型（由环境变量控制）
    if (process.env.SYNC_DB === 'true') {
      console.log('正在同步数据库模型...');
      // 导入所有模型确保它们已注册
      require('./database/models/User');
      // 添加其他模型导入...
      
      // 使用更安全的同步方式：仅创建不存在的表，不修改已有表结构
      await sequelize.sync({ force: false });
      console.log('数据库模型同步完成');
    }
  } catch (error) {
    console.error('数据库连接或同步失败:', error);
    // 只记录错误，不要立即退出进程，让应用继续运行
    console.error('服务器将继续运行，但某些数据库功能可能不可用');
  }
})();

// 使用中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'dist')));

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/sms', smsRoutes);

// 测试路由
app.get('/api/test', (req, res) => {
  res.json({
    code: 0,
    message: '服务器运行正常',
    time: new Date().toISOString(),
    env: process.env.NODE_ENV,
    database: 'MySQL'
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 所有其他请求返回index.html - 用于前端路由
// 注意：确保这个路由放在最后，避免拦截API请求
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist', 'index.html'));
});

// 启动服务器
const PORT = process.env.PORT || 3000;
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`服务器运行在端口: ${PORT}`);
  console.log(`后台管理系统: http://localhost:${PORT}`);
  console.log('API接口已启用，小程序与后台管理系统可以进行数据互通');
  console.log(`测试API: http://localhost:${PORT}/api/test`);
});

// 优雅关闭服务器
process.on('SIGTERM', async () => {
  console.log('SIGTERM信号接收到，关闭服务器...');
  server.close(async () => {
    console.log('HTTP服务器已关闭');
    // 关闭数据库连接
    try {
      await sequelize.close();
      console.log('数据库连接已关闭');
      process.exit(0);
    } catch (error) {
      console.error('关闭数据库连接时出错:', error);
      process.exit(1);
    }
  });
}); 