#!/bin/bash

# 安装Node.js和npm
echo "正在安装Node.js和npm..."
if [ -f /etc/debian_version ]; then
  curl -fsSL https://deb.nodesource.com/setup_16.x | bash -
  apt-get install -y nodejs
elif [ -f /etc/redhat-release ]; then
  curl -fsSL https://rpm.nodesource.com/setup_16.x | bash -
  yum install -y nodejs
else
  echo "未知的Linux发行版，请手动安装Node.js"
  exit 1
fi

# 安装必要的工具
echo "正在安装必要的工具..."
if [ -f /etc/debian_version ]; then
  apt-get update
  apt-get install -y zip unzip git nginx
elif [ -f /etc/redhat-release ]; then
  yum install -y zip unzip git
  # 检查是否安装了nginx
  if ! command -v nginx &> /dev/null; then
    yum install -y epel-release
    yum install -y nginx
  fi
fi

# 配置Nginx
echo "正在配置Nginx..."
NGINX_CONF_DIR=""
if [ -d "/etc/nginx/conf.d" ]; then
  NGINX_CONF_DIR="/etc/nginx/conf.d"
elif [ -d "/etc/nginx/sites-available" ]; then
  NGINX_CONF_DIR="/etc/nginx/sites-available"
else
  echo "找不到Nginx配置目录，将创建conf.d目录"
  mkdir -p /etc/nginx/conf.d
  NGINX_CONF_DIR="/etc/nginx/conf.d"
fi

cat > ${NGINX_CONF_DIR}/yizhanbang.conf << 'EOFNGINX'
server {
    listen 80;
    server_name _;
    
    root /var/www/yizhanbang-pro;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
EOFNGINX

# 如果是Ubuntu/Debian系统，启用站点配置
if [ -d "/etc/nginx/sites-enabled" ]; then
  ln -sf ${NGINX_CONF_DIR}/yizhanbang.conf /etc/nginx/sites-enabled/
  rm -f /etc/nginx/sites-enabled/default
fi

# 重启Nginx
if command -v systemctl &> /dev/null; then
  systemctl restart nginx
else
  service nginx restart
fi

# 设置PM2
echo "正在安装PM2..."
npm install -g pm2

echo "服务器初始化完成!"
