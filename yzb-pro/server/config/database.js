require('dotenv').config();
const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: process.env.DB_DIALECT || 'mysql',
    logging: (msg) => logger.debug(msg),
    pool: {
      max: parseInt(process.env.DB_POOL_MAX || 10),
      min: parseInt(process.env.DB_POOL_MIN || 0),
      acquire: 30000,
      idle: 10000
    },
    timezone: '+08:00', // 东八区时间
    define: {
      freezeTableName: true, // 表名不加s
      timestamps: true,      // 启用时间戳
      underscored: true,     // 使用下划线命名法
      paranoid: true         // 软删除
    }
  }
);

const connectDatabase = async () => {
  try {
    await sequelize.authenticate();
    logger.info('数据库连接成功');
    return true;
  } catch (error) {
    logger.error('数据库连接失败:', error);
    return false;
  }
};

module.exports = {
  sequelize,
  connectDatabase
}; 