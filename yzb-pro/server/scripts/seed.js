/**
 * 数据库种子脚本
 * 用于初始化基础数据
 */
require('dotenv').config();
const bcrypt = require('bcryptjs');
const { connectDatabase } = require('../config/database');
const { User, Station, StationTransfer } = require('../models');
const logger = require('../utils/logger');

// 初始化管理员账号
const createAdminUser = async () => {
  try {
    const adminExists = await User.findOne({
      where: { username: 'admin' }
    });

    if (!adminExists) {
      await User.create({
        username: 'admin',
        password: bcrypt.hashSync('admin123', 10),
        nickname: '系统管理员',
        email: '<EMAIL>',
        phone: '13800000000',
        role: 'admin',
        status: 'active'
      });
      logger.info('管理员账号创建成功');
    } else {
      logger.info('管理员账号已存在，跳过创建');
    }
  } catch (error) {
    logger.error('创建管理员账号失败:', error);
    throw error;
  }
};

// 创建测试用户
const createTestUsers = async () => {
  try {
    const testUsers = [
      {
        username: 'station_admin',
        password: bcrypt.hashSync('123456', 10),
        nickname: '驿站管理员',
        email: '<EMAIL>',
        phone: '***********',
        role: 'station_admin',
        status: 'active'
      },
      {
        username: 'user1',
        password: bcrypt.hashSync('123456', 10),
        nickname: '测试用户1',
        email: '<EMAIL>',
        phone: '***********',
        role: 'user',
        status: 'active'
      },
      {
        username: 'user2',
        password: bcrypt.hashSync('123456', 10),
        nickname: '测试用户2',
        email: '<EMAIL>',
        phone: '***********',
        role: 'user',
        status: 'active'
      }
    ];

    for (const userData of testUsers) {
      const userExists = await User.findOne({
        where: { username: userData.username }
      });

      if (!userExists) {
        await User.create(userData);
        logger.info(`测试用户 ${userData.username} 创建成功`);
      } else {
        logger.info(`测试用户 ${userData.username} 已存在，跳过创建`);
      }
    }
  } catch (error) {
    logger.error('创建测试用户失败:', error);
    throw error;
  }
};

// 创建测试驿站
const createTestStations = async () => {
  try {
    // 获取测试用户ID
    const stationAdmin = await User.findOne({
      where: { username: 'station_admin' }
    });

    if (!stationAdmin) {
      logger.warn('未找到驿站管理员账号，跳过创建测试驿站');
      return;
    }

    const testStations = [
      {
        name: '中关村驿站',
        code: 'ZGC001',
        type: 'campus',
        province: '北京市',
        city: '北京市',
        district: '海淀区',
        address: '北京市海淀区中关村大街1号',
        contact_name: '张经理',
        contact_phone: '***********',
        business_hours: '08:00-20:00',
        monthly_packages: 5000,
        area: 120,
        description: '位于中关村科技园区，周边高校和科技企业众多，快递量大',
        verify_status: 'approved',
        status: 'active',
        user_id: stationAdmin.id,
        images: JSON.stringify([
          '/uploads/station1_1.jpg',
          '/uploads/station1_2.jpg'
        ])
      },
      {
        name: '望京驿站',
        code: 'WJ002',
        type: 'community',
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        address: '北京市朝阳区望京街道望京西园四区',
        contact_name: '李站长',
        contact_phone: '***********',
        business_hours: '09:00-21:00',
        monthly_packages: 3500,
        area: 90,
        description: '社区型驿站，服务周边居民，环境舒适',
        verify_status: 'approved',
        status: 'active',
        user_id: stationAdmin.id,
        images: JSON.stringify([
          '/uploads/station2_1.jpg',
          '/uploads/station2_2.jpg'
        ])
      }
    ];

    for (const stationData of testStations) {
      const stationExists = await Station.findOne({
        where: { name: stationData.name }
      });

      if (!stationExists) {
        await Station.create(stationData);
        logger.info(`测试驿站 ${stationData.name} 创建成功`);
      } else {
        logger.info(`测试驿站 ${stationData.name} 已存在，跳过创建`);
      }
    }
  } catch (error) {
    logger.error('创建测试驿站失败:', error);
    throw error;
  }
};

// 创建测试转让信息
const createTestTransfers = async () => {
  try {
    // 获取用户
    const user1 = await User.findOne({ where: { username: 'user1' } });
    const user2 = await User.findOne({ where: { username: 'user2' } });
    
    if (!user1 || !user2) {
      logger.warn('未找到测试用户，跳过创建测试转让信息');
      return;
    }
    
    // 获取驿站
    const station1 = await Station.findOne({ where: { name: '中关村驿站' } });
    
    if (!station1) {
      logger.warn('未找到测试驿站，跳过创建测试转让信息');
      return;
    }

    const testTransfers = [
      {
        station_id: station1.id,
        station_name: station1.name,
        station_type: station1.type,
        province: station1.province,
        city: station1.city,
        district: station1.district,
        address: station1.address,
        transfer_price: 180000,
        monthly_revenue: 20000,
        monthly_packages: station1.monthly_packages,
        area: station1.area,
        monthly_rent: 8000,
        transfer_reason: '因个人原因无法继续经营，寻求有意向的接手者',
        description: '驿站位置优越，客流量大，月收入稳定。设备齐全，接手即可营业。',
        images: station1.images,
        contact_name: '张先生',
        contact_phone: '***********',
        status: 'active',
        user_id: user1.id,
        view_count: 58
      },
      {
        station_name: '大望路快递驿站',
        station_type: 'business',
        province: '北京市',
        city: '北京市',
        district: '朝阳区',
        address: '北京市朝阳区建国路甲92号世茂大厦B1层',
        transfer_price: 230000,
        monthly_revenue: 25000,
        monthly_packages: 6000,
        area: 150,
        monthly_rent: 12000,
        transfer_reason: '计划转行，寻求有经验的接手人',
        description: '商圈驿站，周边写字楼密集，快递量大且稳定。合作快递公司多，有稳定客户群。',
        contact_name: '李女士',
        contact_phone: '***********',
        status: 'pending',
        user_id: user2.id
      }
    ];

    for (const transferData of testTransfers) {
      const transferExists = await StationTransfer.findOne({
        where: { 
          station_name: transferData.station_name,
          user_id: transferData.user_id
        }
      });

      if (!transferExists) {
        await StationTransfer.create(transferData);
        logger.info(`测试转让信息 ${transferData.station_name} 创建成功`);
      } else {
        logger.info(`测试转让信息 ${transferData.station_name} 已存在，跳过创建`);
      }
    }
  } catch (error) {
    logger.error('创建测试转让信息失败:', error);
    throw error;
  }
};

// 执行所有种子数据创建
const seedDatabase = async () => {
  try {
    logger.info('开始初始化种子数据...');
    
    // 连接数据库
    const dbConnected = await connectDatabase();
    if (!dbConnected) {
      logger.error('数据库连接失败，无法初始化种子数据');
      process.exit(1);
    }
    
    // 创建管理员账号
    await createAdminUser();
    
    // 创建测试用户
    await createTestUsers();
    
    // 创建测试驿站
    await createTestStations();
    
    // 创建测试转让信息
    await createTestTransfers();
    
    logger.info('种子数据初始化完成');
    process.exit(0);
  } catch (error) {
    logger.error('种子数据初始化失败:', error);
    process.exit(1);
  }
};

// 执行种子脚本
seedDatabase(); 