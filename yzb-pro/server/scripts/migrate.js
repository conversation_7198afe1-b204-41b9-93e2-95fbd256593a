/**
 * 数据库迁移脚本
 * 用于创建/更新数据库表结构
 */
require('dotenv').config();
const { sequelize } = require('../config/database');
const logger = require('../utils/logger');

// 导入所有模型
const models = require('../models');

const migrate = async () => {
  try {
    logger.info('开始数据库迁移...');

    // 验证数据库连接
    await sequelize.authenticate();
    logger.info('数据库连接成功');

    // 同步所有模型到数据库
    // force: true 会先删除表再重建
    // alter: true 会更新表结构，保留数据
    const forceSync = process.env.DB_FORCE_SYNC === 'true';
    const alterSync = process.env.DB_ALTER_SYNC === 'true';
    
    const syncOptions = {
      force: forceSync,
      alter: !forceSync && alterSync
    };

    if (forceSync) {
      logger.warn('警告: 将删除所有表并重新创建');
    } else if (alterSync) {
      logger.info('将更新表结构，保留数据');
    } else {
      logger.info('将只创建不存在的表');
    }

    await sequelize.sync(syncOptions);
    
    logger.info('数据库迁移完成');
    process.exit(0);
  } catch (error) {
    logger.error('数据库迁移失败:', error);
    process.exit(1);
  }
};

// 执行迁移
migrate(); 