{"name": "yzb-pro-server", "version": "1.0.0", "description": "驿站帮Pro系统后端服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "lint": "eslint .", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.0", "express-validator": "^7.0.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.1", "sequelize": "^6.33.0", "winston": "^3.10.0", "ws": "^8.14.2"}, "devDependencies": {"eslint": "^8.50.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}