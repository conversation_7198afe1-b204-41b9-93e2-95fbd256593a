const express = require('express');
const http = require('http');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// 导入自定义模块
const logger = require('./utils/logger');
const routes = require('./routes');
const { errorHandler } = require('./middlewares/errorHandler');
const { sequelize, connectDatabase } = require('./config/database');
const { initializeAssociations } = require('./models');
const webSocketService = require('./services/websocket');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3000;

// 创建HTTP服务器
const server = http.createServer(app);

// 确保上传目录存在
const uploadDir = path.join(__dirname, process.env.UPLOAD_PATH || 'uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 中间件配置
app.use(helmet()); // 安全头
app.use(cors()); // 跨域
app.use(express.json({ limit: '1mb' })); // JSON请求体解析
app.use(express.urlencoded({ extended: true, limit: '1mb' })); // URL编码请求体解析
app.use(compression()); // 响应压缩

// 静态文件服务
app.use('/uploads', express.static(uploadDir));
app.use('/public', express.static(path.join(__dirname, 'public')));

// 接口限流
const apiLimiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 60 * 1000, // 1分钟
  max: parseInt(process.env.RATE_LIMIT_MAX) || 100, // 限制1分钟内最多100个请求
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    code: 429,
    message: '请求过于频繁，请稍后再试'
  }
});
app.use(apiLimiter);

// 接口路由
app.use(routes);

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
const startServer = async () => {
  try {
    // 连接数据库
    const dbConnected = await connectDatabase();
    if (!dbConnected) {
      logger.error('数据库连接失败，服务启动中止');
      process.exit(1);
    }

    // 初始化模型关联
    initializeAssociations();

    // 启动HTTP服务器
    server.listen(PORT, () => {
      logger.info(`服务器已启动: http://localhost:${PORT}`);
    });

    // 初始化WebSocket服务
    webSocketService.initialize(server);
  } catch (error) {
    logger.error('服务器启动失败', error);
    process.exit(1);
  }
};

// 优雅关闭服务
const shutdownGracefully = async () => {
  logger.info('正在关闭服务器...');
  
  // 关闭HTTP服务器
  server.close(() => {
    logger.info('HTTP服务器已关闭');
    
    // 关闭数据库连接
    sequelize.close().then(() => {
      logger.info('数据库连接已关闭');
      process.exit(0);
    }).catch(err => {
      logger.error('关闭数据库连接失败', err);
      process.exit(1);
    });
  });
};

// 监听进程终止信号
process.on('SIGTERM', shutdownGracefully);
process.on('SIGINT', shutdownGracefully);

// 捕获未处理的异常
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常', error);
  shutdownGracefully();
});

// 捕获未处理的Promise拒绝
process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝', { reason, promise });
});

// 启动服务器
startServer(); 