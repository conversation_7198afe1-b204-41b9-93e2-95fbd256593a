const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

/**
 * WebSocket服务
 * 用于实时消息推送、订单状态更新、审核结果通知等
 */
class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // 用于存储客户端连接，key为用户ID
  }

  /**
   * 初始化WebSocket服务
   * @param {Object} server HTTP服务器实例
   */
  initialize(server) {
    const wsPath = process.env.WS_PATH || '/ws';
    
    // 创建WebSocket服务器
    this.wss = new WebSocket.Server({
      server,
      path: wsPath
    });

    logger.info(`WebSocket服务已启动: ${wsPath}`);

    // 连接事件处理
    this.wss.on('connection', (ws, req) => {
      logger.info('新的WebSocket连接');
      
      // 处理连接认证
      this.handleAuthentication(ws, req);

      // 消息事件
      ws.on('message', (message) => {
        this.handleMessage(ws, message);
      });

      // 关闭事件
      ws.on('close', () => {
        this.handleClose(ws);
      });

      // 错误事件
      ws.on('error', (error) => {
        logger.error('WebSocket错误', error);
      });

      // 发送连接成功消息
      ws.send(JSON.stringify({
        type: 'connection',
        data: { status: 'connected' },
        timestamp: Date.now()
      }));
    });
  }

  /**
   * 处理客户端认证
   * @param {WebSocket} ws WebSocket连接
   * @param {Object} req HTTP请求对象
   */
  handleAuthentication(ws, req) {
    // 获取URL中的token参数
    const url = new URL(req.url, `http://${req.headers.host}`);
    const token = url.searchParams.get('token');

    if (!token) {
      logger.warn('WebSocket连接缺少认证信息');
      ws.send(JSON.stringify({
        type: 'error',
        data: { message: '未提供认证信息' },
        timestamp: Date.now()
      }));
      return ws.close(1008, '未提供认证信息');
    }

    try {
      // 验证token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // 存储用户信息和连接
      ws.userId = decoded.id;
      ws.username = decoded.username;
      ws.role = decoded.role;
      
      // 将连接添加到clients映射表
      if (!this.clients.has(decoded.id)) {
        this.clients.set(decoded.id, []);
      }
      this.clients.get(decoded.id).push(ws);
      
      logger.info(`用户已认证: ${decoded.username} (${decoded.id})`);
    } catch (error) {
      logger.error('WebSocket认证失败', error);
      ws.send(JSON.stringify({
        type: 'error',
        data: { message: '认证失败' },
        timestamp: Date.now()
      }));
      return ws.close(1008, '认证失败');
    }
  }

  /**
   * 处理客户端消息
   * @param {WebSocket} ws WebSocket连接
   * @param {String} message 客户端发送的消息
   */
  handleMessage(ws, message) {
    try {
      const data = JSON.parse(message);
      logger.debug(`收到消息: ${message}`);

      // 根据消息类型处理不同的业务逻辑
      switch (data.type) {
        case 'ping':
          ws.send(JSON.stringify({
            type: 'pong',
            data: {},
            timestamp: Date.now()
          }));
          break;
        
        case 'subscribe':
          // 实现频道订阅功能
          // TODO: 实现频道订阅
          break;
        
        default:
          logger.warn(`未知的消息类型: ${data.type}`);
          break;
      }
    } catch (error) {
      logger.error('处理WebSocket消息失败', error);
    }
  }

  /**
   * 处理连接关闭
   * @param {WebSocket} ws WebSocket连接
   */
  handleClose(ws) {
    logger.info(`WebSocket连接关闭: 用户 ${ws.username || '未知'}`);
    
    // 从clients映射表中移除连接
    if (ws.userId && this.clients.has(ws.userId)) {
      const connections = this.clients.get(ws.userId);
      const index = connections.indexOf(ws);
      if (index !== -1) {
        connections.splice(index, 1);
      }
      
      // 如果用户没有活跃连接了，则移除用户
      if (connections.length === 0) {
        this.clients.delete(ws.userId);
      }
    }
  }

  /**
   * 向指定用户发送消息
   * @param {Number} userId 用户ID
   * @param {String} type 消息类型
   * @param {Object} data 消息数据
   * @returns {Boolean} 发送结果
   */
  sendToUser(userId, type, data) {
    if (!this.clients.has(userId)) {
      logger.debug(`用户未连接: ${userId}`);
      return false;
    }

    const connections = this.clients.get(userId);
    const message = JSON.stringify({
      type,
      data,
      timestamp: Date.now()
    });

    let sentToAny = false;
    connections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(message);
        sentToAny = true;
      }
    });

    logger.debug(`向用户发送消息: ${userId}, 类型: ${type}, 结果: ${sentToAny ? '成功' : '失败'}`);
    return sentToAny;
  }

  /**
   * 向所有连接的客户端广播消息
   * @param {String} type 消息类型
   * @param {Object} data 消息数据
   * @param {Function} filter 过滤函数，返回true的客户端将收到消息
   */
  broadcast(type, data, filter = null) {
    const message = JSON.stringify({
      type,
      data,
      timestamp: Date.now()
    });

    let count = 0;
    this.wss.clients.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN && (!filter || filter(ws))) {
        ws.send(message);
        count++;
      }
    });

    logger.debug(`广播消息: 类型: ${type}, 接收人数: ${count}`);
  }

  /**
   * 向特定角色的用户广播消息
   * @param {String} role 用户角色
   * @param {String} type 消息类型
   * @param {Object} data 消息数据
   */
  broadcastToRole(role, type, data) {
    this.broadcast(type, data, (ws) => ws.role === role);
  }

  /**
   * 发送订单状态更新消息
   * @param {Number} userId 用户ID
   * @param {Number} orderId 订单ID
   * @param {String} status 新状态
   * @param {String} message 附加消息
   */
  sendOrderStatusUpdate(userId, orderId, status, message = '') {
    return this.sendToUser(userId, 'order_status', {
      order_id: orderId,
      status,
      message
    });
  }

  /**
   * 发送审核结果通知
   * @param {Number} userId 用户ID
   * @param {String} contentType 内容类型 (transfer, recruitment, etc.)
   * @param {Number} contentId 内容ID
   * @param {String} status 审核状态 (approved, rejected)
   * @param {String} reason 拒绝原因
   */
  sendAuditNotification(userId, contentType, contentId, status, reason = '') {
    return this.sendToUser(userId, 'audit_result', {
      content_type: contentType,
      content_id: contentId,
      status,
      reason
    });
  }

  /**
   * 发送系统消息
   * @param {Number} userId 用户ID
   * @param {String} title 消息标题
   * @param {String} content 消息内容
   * @param {String} level 消息级别 (info, warning, error)
   */
  sendSystemMessage(userId, title, content, level = 'info') {
    return this.sendToUser(userId, 'system_message', {
      title,
      content,
      level
    });
  }
}

// 创建单例
const wsService = new WebSocketService();

module.exports = wsService; 