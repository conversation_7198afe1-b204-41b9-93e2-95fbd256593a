const jwt = require('jsonwebtoken');
const { unauthorized } = require('../utils/response');
const logger = require('../utils/logger');

/**
 * JWT认证中间件
 * 验证请求头中的Authorization Bearer token
 */
const auth = (req, res, next) => {
  // 从请求头中获取token
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return unauthorized(res, '未提供有效的认证信息');
  }

  const token = authHeader.split(' ')[1];
  if (!token) {
    return unauthorized(res, '未提供有效的认证信息');
  }

  try {
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 将解码后的用户信息存储到请求对象中，供后续处理使用
    req.user = decoded;
    
    next();
  } catch (error) {
    logger.error('Token验证失败', error);
    
    if (error.name === 'TokenExpiredError') {
      return unauthorized(res, '登录已过期，请重新登录');
    }
    
    return unauthorized(res, '无效的认证信息');
  }
};

/**
 * 角色验证中间件
 * @param {Array} roles 允许访问的角色数组
 */
const checkRole = (roles = []) => {
  return (req, res, next) => {
    if (!req.user) {
      return unauthorized(res);
    }

    const userRole = req.user.role;
    
    if (!roles.includes(userRole)) {
      return unauthorized(res, '您没有权限执行此操作');
    }
    
    next();
  };
};

// 验证是否为管理员
const isAdmin = checkRole(['admin', 'super_admin']);

// 验证是否为驿站管理员
const isStationAdmin = checkRole(['admin', 'super_admin', 'station_admin']);

module.exports = {
  auth,
  checkRole,
  isAdmin,
  isStationAdmin
}; 