const logger = require('../utils/logger');
const { error, serverError } = require('../utils/response');
const { ValidationError } = require('sequelize');

/**
 * 全局错误处理中间件
 * 处理各种类型的错误并返回统一的响应格式
 */
const errorHandler = (err, req, res, next) => {
  // 记录错误日志
  logger.error(`${req.method} ${req.originalUrl}`, {
    error: err.message,
    stack: err.stack,
    body: req.body,
    params: req.params,
    query: req.query
  });

  // 根据错误类型返回不同的响应
  if (err.name === 'ValidationError' || err.isJoi) {
    // Joi验证错误
    return error(res, '请求参数验证失败', 400, err.details);
  }

  if (err instanceof ValidationError) {
    // Sequelize验证错误
    const validationErrors = err.errors.map(e => ({
      field: e.path,
      message: e.message
    }));
    return error(res, '数据验证失败', 400, validationErrors);
  }

  if (err.name === 'UnauthorizedError') {
    // JWT认证错误
    return error(res, '未授权或令牌已过期', 401);
  }

  if (err.statusCode === 404 || err.name === 'NotFoundError') {
    // 资源不存在错误
    return error(res, err.message || '请求的资源不存在', 404);
  }

  if (err.statusCode === 403 || err.name === 'ForbiddenError') {
    // 禁止访问错误
    return error(res, err.message || '无权访问该资源', 403);
  }

  if (err.statusCode === 429 || err.name === 'TooManyRequestsError') {
    // 请求过多错误
    return error(res, '请求过于频繁，请稍后再试', 429);
  }

  // 默认为服务器内部错误
  return serverError(res, '服务器内部错误', err);
};

/**
 * 404处理中间件
 * 处理未匹配到的路由请求
 */
const notFoundHandler = (req, res) => {
  logger.warn(`路由未找到: ${req.method} ${req.originalUrl}`);
  return error(res, `找不到路径: ${req.originalUrl}`, 404);
};

module.exports = {
  errorHandler,
  notFoundHandler
}; 