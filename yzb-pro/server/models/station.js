const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Station = sequelize.define('station', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  code: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: true
  },
  type: {
    type: DataTypes.ENUM('express', 'community', 'campus', 'business', 'other'),
    allowNull: false,
    defaultValue: 'express',
    comment: '驿站类型: express-快递驿站, community-社区驿站, campus-校园驿站, business-商业区驿站, other-其他'
  },
  province: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '省份'
  },
  city: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '城市'
  },
  district: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '区县'
  },
  address: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '详细地址'
  },
  longitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true,
    comment: '经度'
  },
  latitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true,
    comment: '纬度'
  },
  contact_name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '联系人姓名'
  },
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '联系电话'
  },
  business_hours: {
    type: DataTypes.STRING(100),
    allowNull: true,
    defaultValue: '09:00-21:00',
    comment: '营业时间'
  },
  monthly_packages: {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '月均包裹量'
  },
  area: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '场地面积(平方米)'
  },
  images: {
    type: DataTypes.TEXT,
    allowNull: true,
    get() {
      const rawValue = this.getDataValue('images');
      return rawValue ? JSON.parse(rawValue) : [];
    },
    set(value) {
      this.setDataValue('images', JSON.stringify(value));
    },
    comment: '驿站图片,JSON数组'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '驿站描述'
  },
  verify_status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected'),
    defaultValue: 'pending',
    allowNull: false,
    comment: '认证状态: pending-待审核, approved-已认证, rejected-已拒绝'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'closed'),
    defaultValue: 'inactive',
    allowNull: false,
    comment: '状态: active-营业中, inactive-未营业, closed-已关闭'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '站长用户ID'
  },
  reject_reason: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '拒绝原因'
  },
  verification_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '审核时间'
  }
}, {
  underscored: true,
  timestamps: true,
  paranoid: true
});

// 模型同步
Station.sync({ alter: process.env.NODE_ENV === 'development' })
  .then(() => console.log('驿站表同步完成'))
  .catch(err => console.error('驿站表同步失败:', err));

module.exports = Station; 