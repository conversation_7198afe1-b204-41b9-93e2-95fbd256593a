const User = require('./user');
const Station = require('./station');
const StationTransfer = require('./stationTransfer');
const logger = require('../utils/logger');

// 用户和驿站的关系 (一对多)
User.hasMany(Station, {
  foreignKey: 'user_id',
  as: 'stations'
});
Station.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// 用户和驿站转让的关系 (一对多)
User.hasMany(StationTransfer, {
  foreignKey: 'user_id',
  as: 'transfers'
});
StationTransfer.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

// 买家与转让关系 (一对多)
User.hasMany(StationTransfer, {
  foreignKey: 'buyer_id',
  as: 'purchased_transfers'
});
StationTransfer.belongsTo(User, {
  foreignKey: 'buyer_id',
  as: 'buyer'
});

// 驿站和转让记录的关系 (一对多)
Station.hasMany(StationTransfer, {
  foreignKey: 'station_id',
  as: 'transfers'
});
StationTransfer.belongsTo(Station, {
  foreignKey: 'station_id',
  as: 'station'
});

// 初始化所有模型之间的关联
const initializeAssociations = () => {
  logger.info('初始化模型关联完成');
};

module.exports = {
  User,
  Station,
  StationTransfer,
  initializeAssociations
}; 