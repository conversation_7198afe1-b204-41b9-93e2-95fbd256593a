const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const StationTransfer = sequelize.define('station_transfer', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  station_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '关联的驿站ID，如果为新驿站则为null'
  },
  station_name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '驿站名称'
  },
  station_type: {
    type: DataTypes.ENUM('express', 'community', 'campus', 'business', 'other'),
    allowNull: false,
    defaultValue: 'express',
    comment: '驿站类型'
  },
  province: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '省份'
  },
  city: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '城市'
  },
  district: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '区县'
  },
  address: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '详细地址'
  },
  transfer_price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: '转让价格'
  },
  monthly_revenue: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '月收入'
  },
  monthly_packages: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '月均包裹量'
  },
  area: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '场地面积(平方米)'
  },
  monthly_rent: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: '月租金'
  },
  transfer_reason: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '转让原因'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '详细描述'
  },
  images: {
    type: DataTypes.TEXT,
    allowNull: true,
    get() {
      const rawValue = this.getDataValue('images');
      return rawValue ? JSON.parse(rawValue) : [];
    },
    set(value) {
      this.setDataValue('images', JSON.stringify(value));
    },
    comment: '图片,JSON数组'
  },
  contact_name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: '联系人姓名'
  },
  contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '联系电话'
  },
  view_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '浏览次数'
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected', 'active', 'traded', 'closed'),
    defaultValue: 'pending',
    allowNull: false,
    comment: '状态: pending-待审核, approved-审核通过, rejected-已拒绝, active-转让中, traded-已交易, closed-已下架'
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '发布者用户ID'
  },
  buyer_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '购买者用户ID'
  },
  traded_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '交易时间'
  },
  verify_time: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '审核时间'
  },
  reject_reason: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '拒绝原因'
  }
}, {
  underscored: true,
  timestamps: true,
  paranoid: true
});

// 模型同步
StationTransfer.sync({ alter: process.env.NODE_ENV === 'development' })
  .then(() => console.log('驿站转让表同步完成'))
  .catch(err => console.error('驿站转让表同步失败:', err));

module.exports = StationTransfer; 