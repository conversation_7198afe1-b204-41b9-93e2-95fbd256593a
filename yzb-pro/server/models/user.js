const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const bcrypt = require('bcryptjs');

const User = sequelize.define('user', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      len: [3, 50]
    }
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
    set(value) {
      // 密码哈希处理
      const hashedPassword = bcrypt.hashSync(value, 10);
      this.setDataValue('password', hashedPassword);
    },
    validate: {
      len: [6, 100]
    }
  },
  nickname: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  avatar: {
    type: DataTypes.STRING,
    allowNull: true
  },
  email: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true,
    validate: {
      isEmail: true
    }
  },
  phone: {
    type: DataTypes.STRING(20),
    allowNull: true,
    unique: true,
    validate: {
      is: /^1[3-9]\d{9}$/
    }
  },
  role: {
    type: DataTypes.ENUM('user', 'station_admin', 'admin', 'super_admin'),
    defaultValue: 'user',
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'blocked'),
    defaultValue: 'active',
    allowNull: false
  },
  wx_openid: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  wx_unionid: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true
  },
  last_login_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  login_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  }
}, {
  underscored: true,
  timestamps: true,
  paranoid: true,  // 软删除
  hooks: {
    beforeCreate: (user) => {
      if (user.password) {
        user.password = bcrypt.hashSync(user.password, 10);
      }
    },
    beforeUpdate: (user) => {
      if (user.changed('password')) {
        user.password = bcrypt.hashSync(user.password, 10);
      }
    }
  }
});

// 实例方法 - 验证密码
User.prototype.validatePassword = function(password) {
  return bcrypt.compareSync(password, this.password);
};

// 模型同步
User.sync({ alter: process.env.NODE_ENV === 'development' })
  .then(() => console.log('用户表同步完成'))
  .catch(err => console.error('用户表同步失败:', err));

module.exports = User; 