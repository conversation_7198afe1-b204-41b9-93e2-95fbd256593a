const express = require('express');
const router = express.Router();
const authRoutes = require('./auth');
const stationTransferRoutes = require('./stationTransfer');
const { notFoundHandler } = require('../middlewares/errorHandler');

// API版本前缀
const API_PREFIX = process.env.API_PREFIX || '/api';

// 认证相关路由
router.use(`${API_PREFIX}/auth`, authRoutes);

// 驿站转让相关路由
router.use(`${API_PREFIX}/station-transfers`, stationTransferRoutes);

// 404处理
router.use('*', notFoundHandler);

module.exports = router; 