const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { auth } = require('../middlewares/auth');
const { loginValidator, registerValidator } = require('../middlewares/validator');

// 用户登录
router.post('/login', loginValidator, authController.login);

// 用户注册
router.post('/register', registerValidator, authController.register);

// 微信小程序登录
router.post('/wx-login', authController.wxLogin);

// 令牌刷新
router.post('/refresh-token', authController.refreshToken);

// 获取当前用户信息
router.get('/me', auth, authController.getCurrentUser);

// 退出登录
router.post('/logout', auth, authController.logout);

module.exports = router; 