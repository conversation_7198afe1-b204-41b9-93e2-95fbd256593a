/**
 * 统一API响应格式工具
 */

// 成功响应
const success = (res, data = null, message = '操作成功', code = 200) => {
  return res.status(code).json({
    code,
    success: true,
    message,
    data
  });
};

// 错误响应
const error = (res, message = '操作失败', code = 400, errors = null) => {
  return res.status(code).json({
    code,
    success: false,
    message,
    errors
  });
};

// 分页数据响应
const paginate = (res, { rows, count, page, pageSize }) => {
  return success(res, {
    list: rows,
    pagination: {
      total: count,
      current: page,
      pageSize,
      totalPages: Math.ceil(count / pageSize)
    }
  });
};

// 创建成功响应
const created = (res, data = null, message = '创建成功') => {
  return success(res, data, message, 201);
};

// 无内容响应
const noContent = (res, message = '操作成功') => {
  return res.status(204).send();
};

// 未授权响应
const unauthorized = (res, message = '未授权，请先登录') => {
  return error(res, message, 401);
};

// 禁止访问响应
const forbidden = (res, message = '无权访问该资源') => {
  return error(res, message, 403);
};

// 资源不存在响应
const notFound = (res, message = '请求的资源不存在') => {
  return error(res, message, 404);
};

// 服务器错误响应
const serverError = (res, message = '服务器内部错误', err = null) => {
  const errMessage = err ? (err.message || String(err)) : '';
  return error(res, `${message}${errMessage ? ': ' + errMessage : ''}`, 500);
};

module.exports = {
  success,
  error,
  paginate,
  created,
  noContent,
  unauthorized,
  forbidden,
  notFound,
  serverError
}; 