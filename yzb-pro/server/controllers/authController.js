const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { User } = require('../models');
const { success, error, unauthorized } = require('../utils/response');
const logger = require('../utils/logger');

/**
 * 用户登录
 */
const login = async (req, res) => {
  try {
    const { username, password } = req.body;

    // 查找用户
    const user = await User.findOne({ 
      where: { username },
      attributes: { include: ['password'] } 
    });

    // 用户不存在或密码错误
    if (!user || !user.validatePassword(password)) {
      return unauthorized(res, '用户名或密码错误');
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return unauthorized(res, '账号已被禁用，请联系管理员');
    }

    // 更新登录信息
    await user.update({
      last_login_at: new Date(),
      login_count: user.login_count + 1
    });

    // 生成 token
    const token = generateToken(user);
    const refreshToken = generateRefreshToken(user);

    // 返回用户信息和token
    return success(res, {
      user: {
        id: user.id,
        username: user.username,
        nickname: user.nickname,
        avatar: user.avatar,
        email: user.email,
        phone: user.phone,
        role: user.role,
        last_login_at: user.last_login_at
      },
      token,
      refreshToken
    }, '登录成功');
  } catch (err) {
    logger.error('用户登录失败', err);
    return error(res, '登录失败，服务器错误', 500);
  }
};

/**
 * 用户注册
 */
const register = async (req, res) => {
  try {
    const { username, password, nickname, email, phone } = req.body;

    // 检查用户名是否已存在
    const existingUser = await User.findOne({ where: { username } });
    if (existingUser) {
      return error(res, '用户名已存在', 400);
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await User.findOne({ where: { email } });
      if (existingEmail) {
        return error(res, '邮箱已被使用', 400);
      }
    }

    // 检查手机号是否已存在
    if (phone) {
      const existingPhone = await User.findOne({ where: { phone } });
      if (existingPhone) {
        return error(res, '手机号已被使用', 400);
      }
    }

    // 创建新用户
    const newUser = await User.create({
      username,
      password, // 密码会在模型的hook中自动哈希
      nickname: nickname || username,
      email,
      phone,
      role: 'user', // 默认为普通用户
      status: 'active'
    });

    // 生成 token
    const token = generateToken(newUser);
    const refreshToken = generateRefreshToken(newUser);

    return success(res, {
      user: {
        id: newUser.id,
        username: newUser.username,
        nickname: newUser.nickname,
        email: newUser.email,
        phone: newUser.phone,
        role: newUser.role
      },
      token,
      refreshToken
    }, '注册成功', 201);
  } catch (err) {
    logger.error('用户注册失败', err);
    return error(res, '注册失败，服务器错误', 500);
  }
};

/**
 * 微信小程序登录
 */
const wxLogin = async (req, res) => {
  try {
    const { code, userInfo } = req.body;
    
    if (!code) {
      return error(res, '缺少参数: code', 400);
    }

    // 调用微信API获取openid和session_key
    // 这里需要一个单独的函数来处理这个请求
    const wxResult = await getWxSession(code);
    
    if (!wxResult.openid) {
      return error(res, '获取微信用户信息失败', 400);
    }

    // 查找或创建用户
    let user = await User.findOne({ where: { wx_openid: wxResult.openid } });

    if (!user) {
      // 创建新用户
      user = await User.create({
        username: `wx_${wxResult.openid.substr(0, 8)}`,
        password: bcrypt.hashSync(Math.random().toString(36).slice(-8), 10), // 随机密码
        nickname: userInfo?.nickName || `用户${Math.floor(Math.random() * 10000)}`,
        avatar: userInfo?.avatarUrl,
        wx_openid: wxResult.openid,
        wx_unionid: wxResult.unionid,
        role: 'user',
        status: 'active'
      });
    } else {
      // 更新用户信息
      await user.update({
        last_login_at: new Date(),
        login_count: user.login_count + 1,
        // 可选更新其他用户信息
        avatar: userInfo?.avatarUrl || user.avatar,
        nickname: userInfo?.nickName || user.nickname
      });
    }

    // 生成 token
    const token = generateToken(user);
    const refreshToken = generateRefreshToken(user);

    return success(res, {
      user: {
        id: user.id,
        username: user.username,
        nickname: user.nickname,
        avatar: user.avatar,
        role: user.role
      },
      token,
      refreshToken
    }, '登录成功');
  } catch (err) {
    logger.error('微信登录失败', err);
    return error(res, '微信登录失败，服务器错误', 500);
  }
};

/**
 * 获取当前用户信息
 */
const getCurrentUser = async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await User.findByPk(userId, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return error(res, '用户不存在', 404);
    }

    return success(res, { user }, '获取用户信息成功');
  } catch (err) {
    logger.error('获取当前用户信息失败', err);
    return error(res, '获取用户信息失败', 500);
  }
};

/**
 * 刷新Token
 */
const refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return error(res, '缺少刷新令牌', 400);
    }

    // 验证刷新令牌
    let decoded;
    try {
      decoded = jwt.verify(refreshToken, process.env.JWT_SECRET);
    } catch (err) {
      return unauthorized(res, '刷新令牌无效或已过期');
    }

    // 查找用户
    const user = await User.findByPk(decoded.id);
    if (!user || user.status !== 'active') {
      return unauthorized(res, '用户不存在或已被禁用');
    }

    // 生成新的令牌
    const newToken = generateToken(user);
    const newRefreshToken = generateRefreshToken(user);

    return success(res, {
      token: newToken,
      refreshToken: newRefreshToken
    }, '令牌刷新成功');
  } catch (err) {
    logger.error('刷新令牌失败', err);
    return error(res, '刷新令牌失败', 500);
  }
};

/**
 * 登出
 * 注意：JWT本身无法实现真正的"登出"，这里只是一个示例方法
 * 实际的登出需要在客户端删除令牌，或者使用令牌黑名单等方式
 */
const logout = (req, res) => {
  // 这里可以实现一些服务端逻辑，比如将token加入黑名单
  return success(res, null, '登出成功');
};

/**
 * 生成JWT令牌
 */
const generateToken = (user) => {
  return jwt.sign(
    { 
      id: user.id,
      username: user.username,
      role: user.role
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
  );
};

/**
 * 生成刷新令牌
 */
const generateRefreshToken = (user) => {
  return jwt.sign(
    { id: user.id },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
  );
};

/**
 * 获取微信session（openid和session_key）
 */
const getWxSession = async (code) => {
  try {
    const appId = process.env.WX_APPID;
    const appSecret = process.env.WX_SECRET;
    const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`;
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (data.errcode) {
      logger.error('微信code2session请求失败', data);
      throw new Error(data.errmsg || '微信登录失败');
    }
    
    return {
      openid: data.openid,
      session_key: data.session_key,
      unionid: data.unionid
    };
  } catch (err) {
    logger.error('获取微信session失败', err);
    throw err;
  }
};

module.exports = {
  login,
  register,
  wxLogin,
  getCurrentUser,
  refreshToken,
  logout
}; 