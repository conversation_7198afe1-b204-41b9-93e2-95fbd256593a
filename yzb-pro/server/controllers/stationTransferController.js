const { Op } = require('sequelize');
const { StationTransfer, User, Station } = require('../models');
const { success, error, paginate, created, notFound } = require('../utils/response');
const logger = require('../utils/logger');

/**
 * 获取转让列表
 * 支持分页和筛选
 */
const getTransfers = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 10,
      keyword,
      status,
      stationType,
      province,
      city,
      district,
      minPrice,
      maxPrice,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    // 构建查询条件
    const where = {};

    // 转让状态筛选
    if (status) {
      where.status = status;
    } else {
      // 默认只显示已审核通过和转让中的
      where.status = {
        [Op.in]: ['approved', 'active']
      };
    }

    // 驿站类型筛选
    if (stationType) {
      where.station_type = stationType;
    }

    // 地区筛选
    if (province) where.province = province;
    if (city) where.city = city;
    if (district) where.district = district;

    // 价格范围筛选
    if (minPrice || maxPrice) {
      where.transfer_price = {};
      if (minPrice) where.transfer_price[Op.gte] = minPrice;
      if (maxPrice) where.transfer_price[Op.lte] = maxPrice;
    }

    // 关键词搜索
    if (keyword) {
      where[Op.or] = [
        { station_name: { [Op.like]: `%${keyword}%` } },
        { address: { [Op.like]: `%${keyword}%` } },
        { description: { [Op.like]: `%${keyword}%` } }
      ];
    }

    // 执行查询
    const { count, rows } = await StationTransfer.findAndCountAll({
      where,
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'nickname', 'avatar']
        }
      ],
      order: [[sortBy, sortOrder]],
      limit: parseInt(pageSize),
      offset: (parseInt(page) - 1) * parseInt(pageSize)
    });

    // 返回分页结果
    return paginate(res, {
      rows,
      count,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });
  } catch (err) {
    logger.error('获取转让列表失败', err);
    return error(res, '获取转让列表失败', 500);
  }
};

/**
 * 获取转让详情
 */
const getTransferById = async (req, res) => {
  try {
    const { id } = req.params;

    const transfer = await StationTransfer.findByPk(id, {
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'nickname', 'avatar']
        },
        {
          model: Station,
          as: 'station',
          attributes: ['id', 'name', 'code', 'monthly_packages', 'area', 'business_hours']
        }
      ]
    });

    if (!transfer) {
      return notFound(res, '转让信息不存在');
    }

    // 更新浏览次数
    await transfer.increment('view_count');

    return success(res, { transfer });
  } catch (err) {
    logger.error(`获取转让详情失败: ID=${req.params.id}`, err);
    return error(res, '获取转让详情失败', 500);
  }
};

/**
 * 创建转让信息
 */
const createTransfer = async (req, res) => {
  try {
    const {
      station_id,
      station_name,
      station_type,
      province,
      city,
      district,
      address,
      transfer_price,
      monthly_revenue,
      monthly_packages,
      area,
      monthly_rent,
      transfer_reason,
      description,
      images,
      contact_name,
      contact_phone
    } = req.body;

    // 用户ID从令牌中获取
    const user_id = req.user.id;

    // 创建转让信息
    const transfer = await StationTransfer.create({
      station_id,
      station_name,
      station_type,
      province,
      city,
      district,
      address,
      transfer_price,
      monthly_revenue,
      monthly_packages,
      area,
      monthly_rent,
      transfer_reason,
      description,
      images,
      contact_name,
      contact_phone,
      user_id,
      status: 'pending' // 默认为待审核状态
    });

    return created(res, { transfer }, '转让信息创建成功，等待审核');
  } catch (err) {
    logger.error('创建转让信息失败', err);
    return error(res, '创建转让信息失败', 500);
  }
};

/**
 * 更新转让信息
 */
const updateTransfer = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // 查找转让信息
    const transfer = await StationTransfer.findByPk(id);

    if (!transfer) {
      return notFound(res, '转让信息不存在');
    }

    // 权限检查：只有发布者和管理员可以修改
    if (transfer.user_id !== userId && !['admin', 'super_admin'].includes(userRole)) {
      return error(res, '无权修改此转让信息', 403);
    }

    // 状态检查：已交易的不能修改
    if (transfer.status === 'traded') {
      return error(res, '已交易的转让信息不能修改', 400);
    }

    // 更新转让信息
    const allowedFields = [
      'station_name', 'station_type', 'province', 'city', 'district',
      'address', 'transfer_price', 'monthly_revenue', 'monthly_packages',
      'area', 'monthly_rent', 'transfer_reason', 'description',
      'images', 'contact_name', 'contact_phone'
    ];

    // 过滤出允许更新的字段
    const updateData = {};
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    // 如果用户修改了重要信息，状态变为待审核
    const importantFields = ['station_name', 'station_type', 'address', 'transfer_price'];
    const hasImportantChanges = importantFields.some(field => 
      updateData[field] !== undefined && updateData[field] !== transfer[field]
    );

    if (hasImportantChanges && !['admin', 'super_admin'].includes(userRole)) {
      updateData.status = 'pending';
    }

    await transfer.update(updateData);

    return success(res, { transfer }, '转让信息更新成功');
  } catch (err) {
    logger.error(`更新转让信息失败: ID=${req.params.id}`, err);
    return error(res, '更新转让信息失败', 500);
  }
};

/**
 * 删除转让信息
 */
const deleteTransfer = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const userRole = req.user.role;

    // 查找转让信息
    const transfer = await StationTransfer.findByPk(id);

    if (!transfer) {
      return notFound(res, '转让信息不存在');
    }

    // 权限检查：只有发布者和管理员可以删除
    if (transfer.user_id !== userId && !['admin', 'super_admin'].includes(userRole)) {
      return error(res, '无权删除此转让信息', 403);
    }

    // 删除转让信息（软删除）
    await transfer.destroy();

    return success(res, null, '转让信息删除成功');
  } catch (err) {
    logger.error(`删除转让信息失败: ID=${req.params.id}`, err);
    return error(res, '删除转让信息失败', 500);
  }
};

/**
 * 更新转让状态
 */
const updateTransferStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, reason } = req.body;
    const userRole = req.user.role;

    // 审核操作只能由管理员执行
    if (['pending', 'approved', 'rejected'].includes(status) && !['admin', 'super_admin'].includes(userRole)) {
      return error(res, '无权执行审核操作', 403);
    }

    // 查找转让信息
    const transfer = await StationTransfer.findByPk(id);

    if (!transfer) {
      return notFound(res, '转让信息不存在');
    }

    // 更新状态
    const updateData = { status };

    // 如果是审核通过，记录审核时间
    if (status === 'approved') {
      updateData.verify_time = new Date();
    }

    // 如果是审核拒绝，记录拒绝原因
    if (status === 'rejected') {
      if (!reason) {
        return error(res, '请提供拒绝原因', 400);
      }
      updateData.reject_reason = reason;
      updateData.verify_time = new Date();
    }

    // 如果是标记为已交易，记录交易时间和买家信息
    if (status === 'traded') {
      const { buyer_id } = req.body;
      if (!buyer_id) {
        return error(res, '请提供买家信息', 400);
      }
      updateData.buyer_id = buyer_id;
      updateData.traded_at = new Date();
    }

    await transfer.update(updateData);

    return success(res, { transfer }, '状态更新成功');
  } catch (err) {
    logger.error(`更新转让状态失败: ID=${req.params.id}`, err);
    return error(res, '更新转让状态失败', 500);
  }
};

/**
 * 获取我发布的转让列表
 */
const getMyTransfers = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      pageSize = 10,
      status,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    // 构建查询条件
    const where = { user_id: userId };

    // 状态筛选
    if (status) {
      where.status = status;
    }

    // 执行查询
    const { count, rows } = await StationTransfer.findAndCountAll({
      where,
      order: [[sortBy, sortOrder]],
      limit: parseInt(pageSize),
      offset: (parseInt(page) - 1) * parseInt(pageSize)
    });

    // 返回分页结果
    return paginate(res, {
      rows,
      count,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });
  } catch (err) {
    logger.error('获取我的转让列表失败', err);
    return error(res, '获取我的转让列表失败', 500);
  }
};

/**
 * 获取待审核的转让列表
 */
const getPendingTransfers = async (req, res) => {
  try {
    // 检查权限：只有管理员可以查看
    if (!['admin', 'super_admin'].includes(req.user.role)) {
      return error(res, '无权访问', 403);
    }

    const {
      page = 1,
      pageSize = 10,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    // 执行查询
    const { count, rows } = await StationTransfer.findAndCountAll({
      where: { status: 'pending' },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username', 'nickname', 'avatar']
        }
      ],
      order: [[sortBy, sortOrder]],
      limit: parseInt(pageSize),
      offset: (parseInt(page) - 1) * parseInt(pageSize)
    });

    // 返回分页结果
    return paginate(res, {
      rows,
      count,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });
  } catch (err) {
    logger.error('获取待审核的转让列表失败', err);
    return error(res, '获取待审核的转让列表失败', 500);
  }
};

module.exports = {
  getTransfers,
  getTransferById,
  createTransfer,
  updateTransfer,
  deleteTransfer,
  updateTransferStatus,
  getMyTransfers,
  getPendingTransfers
}; 