# 服务器配置
NODE_ENV=development
PORT=3000
API_PREFIX=/api

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=yzb_pro
DB_USER=root
DB_PASSWORD=password
DB_POOL_MAX=10
DB_POOL_MIN=0
DB_DIALECT=mysql

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 文件上传
UPLOAD_PATH=uploads
MAX_FILE_SIZE=5242880

# 微信小程序配置
WX_APPID=your_wx_appid
WX_SECRET=your_wx_secret

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Redis配置 (用于缓存和速率限制)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 接口限流配置
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=100

# WebSocket配置
WS_PATH=/ws
WS_PORT=3001 