import { get, post, put, del } from '@/utils/request';

// 获取转让列表
export async function getTransferList(params) {
  return get('/station-transfers', params);
}

// 获取转让详情
export async function getTransferDetail(id) {
  return get(`/station-transfers/${id}`);
}

// 创建转让信息
export async function createTransfer(data) {
  return post('/station-transfers', data);
}

// 更新转让信息
export async function updateTransfer(id, data) {
  return put(`/station-transfers/${id}`, data);
}

// 删除转让信息
export async function deleteTransfer(id) {
  return del(`/station-transfers/${id}`);
}

// 更新转让状态
export async function updateTransferStatus(id, data) {
  return post(`/station-transfers/${id}/status`, data);
}

// 获取我发布的转让列表
export async function getMyTransfers(params) {
  return get('/station-transfers/mine', params);
}

// 获取待审核的转让列表 (管理员)
export async function getPendingTransfers(params) {
  return get('/station-transfers/pending', params);
} 