import { getToken } from './auth';
import { notification } from 'antd';

/**
 * WebSocket客户端封装
 * 处理与服务器的实时通信
 */
class WebSocketClient {
  constructor() {
    this.ws = null;
    this.url = process.env.REACT_APP_WS_URL || 'ws://localhost:3000/ws';
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000; // 5秒
    this.messageHandlers = new Map();
    this.pingInterval = null;
  }

  /**
   * 初始化并连接WebSocket
   */
  connect() {
    if (this.ws && this.isConnected) {
      console.info('WebSocket已连接，无需重复连接');
      return;
    }

    // 获取认证令牌
    const token = getToken();
    if (!token) {
      console.error('WebSocket连接失败：未找到认证令牌');
      return;
    }

    // 构建WebSocket URL，附带token
    const wsUrl = `${this.url}?token=${token}`;

    // 创建WebSocket连接
    this.ws = new WebSocket(wsUrl);

    // 连接打开事件
    this.ws.onopen = this.handleOpen.bind(this);

    // 接收消息事件
    this.ws.onmessage = this.handleMessage.bind(this);

    // 连接关闭事件
    this.ws.onclose = this.handleClose.bind(this);

    // 连接错误事件
    this.ws.onerror = this.handleError.bind(this);
  }

  /**
   * 处理连接打开事件
   */
  handleOpen() {
    console.info('WebSocket连接已建立');
    this.isConnected = true;
    this.reconnectAttempts = 0;

    // 启动心跳检测
    this.startHeartbeat();
  }

  /**
   * 处理接收消息事件
   * @param {MessageEvent} event 消息事件
   */
  handleMessage(event) {
    try {
      const message = JSON.parse(event.data);
      console.debug('收到WebSocket消息:', message);

      // 处理特定类型的消息
      switch (message.type) {
        case 'pong':
          // 心跳响应，无需处理
          break;
        
        case 'connection':
          // 连接状态消息
          console.info('WebSocket连接状态:', message.data.status);
          break;
        
        case 'error':
          // 错误消息
          console.error('WebSocket错误:', message.data.message);
          break;
        
        case 'order_status':
          // 订单状态更新
          this.handleOrderStatusUpdate(message.data);
          break;
        
        case 'audit_result':
          // 审核结果通知
          this.handleAuditNotification(message.data);
          break;
        
        case 'system_message':
          // 系统消息
          this.handleSystemMessage(message.data);
          break;
        
        default:
          // 调用已注册的消息处理函数
          if (this.messageHandlers.has(message.type)) {
            this.messageHandlers.get(message.type)(message.data);
          } else {
            console.warn('未处理的WebSocket消息类型:', message.type);
          }
          break;
      }
    } catch (error) {
      console.error('处理WebSocket消息时出错:', error);
    }
  }

  /**
   * 处理连接关闭事件
   * @param {CloseEvent} event 关闭事件
   */
  handleClose(event) {
    this.isConnected = false;
    this.stopHeartbeat();

    console.warn(`WebSocket连接已关闭: 代码=${event.code}, 原因=${event.reason}`);

    // 如果不是正常关闭，尝试重连
    if (event.code !== 1000 && event.code !== 1001) {
      this.reconnect();
    }
  }

  /**
   * 处理连接错误事件
   * @param {Event} error 错误事件
   */
  handleError(error) {
    console.error('WebSocket连接错误:', error);
    this.isConnected = false;
    this.stopHeartbeat();
  }

  /**
   * 尝试重新连接
   */
  reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error(`WebSocket重连失败: 已达到最大重试次数(${this.maxReconnectAttempts})`);
      return;
    }

    this.reconnectAttempts++;
    console.info(`WebSocket尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

    // 延迟重连
    setTimeout(() => {
      this.connect();
    }, this.reconnectInterval);
  }

  /**
   * 发送消息到服务器
   * @param {string} type 消息类型
   * @param {Object} data 消息数据
   * @returns {boolean} 发送结果
   */
  send(type, data) {
    if (!this.isConnected) {
      console.error('WebSocket未连接，无法发送消息');
      return false;
    }

    try {
      const message = JSON.stringify({
        type,
        data,
        timestamp: Date.now()
      });

      this.ws.send(message);
      return true;
    } catch (error) {
      console.error('发送WebSocket消息失败:', error);
      return false;
    }
  }

  /**
   * 注册消息处理函数
   * @param {string} type 消息类型
   * @param {Function} handler 处理函数
   */
  registerHandler(type, handler) {
    if (typeof handler !== 'function') {
      console.error('消息处理函数必须是一个函数');
      return;
    }

    this.messageHandlers.set(type, handler);
  }

  /**
   * 取消注册消息处理函数
   * @param {string} type 消息类型
   */
  unregisterHandler(type) {
    this.messageHandlers.delete(type);
  }

  /**
   * 启动心跳检测
   */
  startHeartbeat() {
    // 清除可能存在的心跳定时器
    this.stopHeartbeat();

    // 每30秒发送一次心跳
    this.pingInterval = setInterval(() => {
      if (this.isConnected) {
        this.send('ping', {});
      }
    }, 30000);
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  /**
   * 关闭WebSocket连接
   */
  disconnect() {
    if (this.ws) {
      this.stopHeartbeat();
      this.ws.close(1000, '客户端主动关闭');
      this.isConnected = false;
      this.ws = null;
    }
  }

  /**
   * 处理订单状态更新消息
   * @param {Object} data 消息数据
   */
  handleOrderStatusUpdate(data) {
    notification.info({
      message: '订单状态更新',
      description: `订单 #${data.order_id} 状态已更新为 ${data.status}${data.message ? ': ' + data.message : ''}`,
      duration: 5
    });
  }

  /**
   * 处理审核结果通知
   * @param {Object} data 消息数据
   */
  handleAuditNotification(data) {
    const contentTypeMap = {
      transfer: '驿站转让',
      recruitment: '招聘信息',
      equipment: '设备交易',
      substitution: '顶班服务'
    };

    const statusMap = {
      approved: '已通过',
      rejected: '已拒绝'
    };

    const contentType = contentTypeMap[data.content_type] || data.content_type;
    const status = statusMap[data.status] || data.status;

    notification[data.status === 'approved' ? 'success' : 'warning']({
      message: `${contentType}审核${status}`,
      description: data.reason ? `原因: ${data.reason}` : '',
      duration: 5
    });
  }

  /**
   * 处理系统消息
   * @param {Object} data 消息数据
   */
  handleSystemMessage(data) {
    notification[data.level || 'info']({
      message: data.title,
      description: data.content,
      duration: 5
    });
  }
}

// 创建单例
const wsClient = new WebSocketClient();

export default wsClient; 