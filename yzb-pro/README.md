# 驿站帮Pro系统

驿站帮Pro是一个综合性的驿站管理系统，连接微信小程序前端与Node.js后台管理系统，实现实时数据交互。系统支持驿站转让、设备交易、招聘求职、顶班服务等功能，为驿站经营者和相关从业人员提供全方位的服务平台。

## 技术架构

- **前端**：微信小程序(原有)
- **后台管理系统**：React + Ant Design Pro
- **后端API服务**：Node.js + Express
- **数据库**：MySQL
- **实时通信**：WebSocket

## 目录结构

```
yzb-pro/
├── server/            # 后端API服务
│   ├── config/        # 配置文件
│   ├── controllers/   # 控制器
│   ├── middlewares/   # 中间件
│   ├── models/        # 数据模型
│   ├── routes/        # 路由
│   ├── services/      # 服务层
│   ├── utils/         # 工具函数
│   ├── scripts/       # 脚本文件
│   ├── public/        # 静态资源
│   ├── uploads/       # 上传文件目录
│   ├── tests/         # 测试文件
│   ├── app.js         # 入口文件
│   └── package.json   # 依赖配置
│
├── client/            # 后台管理系统前端(React)
│
├── scripts/           # 项目脚本
└── docs/              # 项目文档
```

## 安装与配置

### 环境要求

- Node.js >= 16.0.0
- MySQL >= 8.0
- npm >= 8.0.0

### 服务端安装

1. 克隆项目
```bash
git clone https://github.com/your-repo/yzb-pro.git
cd yzb-pro
```

2. 安装依赖
```bash
cd server
npm install
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，配置数据库连接信息等
```

4. 初始化数据库
```bash
npm run migrate  # 创建数据库表
npm run seed     # 添加初始数据
```

5. 启动服务
```bash
npm run dev  # 开发模式
# 或
npm start    # 生产模式
```

## API文档

系统提供完整的RESTful API，主要包括以下模块：

- 用户认证API(`/api/auth/*`)
- 驿站转让API(`/api/station-transfers/*`)
- 设备交易API(`/api/equipment/*`)
- 招聘求职API(`/api/recruitment/*`)
- 顶班服务API(`/api/substitution/*`)
- 商品管理API(`/api/products/*`)
- 订单管理API(`/api/orders/*`)
- 推广管理API(`/api/promotion/*`)

详细的API文档请参考：`/docs/api.md`

## WebSocket实时通信

系统使用WebSocket实现实时消息推送，包括：

- 订单状态实时更新
- 新消息通知
- 审核结果即时反馈

WebSocket连接地址：`ws://your-domain/ws?token=<JWT_TOKEN>`

## 数据库设计

系统使用MySQL数据库，主要表结构如下：

- `users` - 用户表
- `stations` - 驿站表
- `station_transfers` - 驿站转让表
- `equipment` - 设备表
- `recruitment` - 招聘表
- `substitution` - 顶班服务表
- `orders` - 订单表
- `products` - 商品表
- `promotion` - 推广表

详细的数据库设计请参考：`/docs/database.md`

## 安全与性能

系统实现了多种安全与性能优化措施：

- JWT认证与鉴权
- SQL注入防护
- XSS防护
- 接口限流
- 数据库连接池
- 日志记录与监控

## 开发与部署

### 开发环境

```bash
npm run dev  # 启动开发服务器
```

### 测试

```bash
npm test     # 运行测试
```

### 生产环境部署

```bash
npm run build  # 构建生产环境代码
npm start      # 启动生产服务器
```

## 贡献指南

欢迎提交问题和功能需求，或者提交PR来改进代码。

## 许可证

[MIT](LICENSE) 