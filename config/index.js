/**
 * 统一配置文件
 * 为整个项目提供统一的配置管理
 */

require('dotenv').config();

// 环境配置
const NODE_ENV = process.env.NODE_ENV || 'development';
const IS_PRODUCTION = NODE_ENV === 'production';
const IS_DEVELOPMENT = NODE_ENV === 'development';

// 服务器配置
const server = {
  port: parseInt(process.env.PORT) || 3000,
  host: process.env.HOST || '0.0.0.0',
  env: NODE_ENV,
  isDevelopment: IS_DEVELOPMENT,
  isProduction: IS_PRODUCTION
};

// 数据库配置
const database = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'yizhanbang',
  dialect: process.env.DB_DIALECT || 'mysql',
  charset: 'utf8mb4',
  collate: 'utf8mb4_unicode_ci',
  timezone: '+08:00',
  logging: IS_DEVELOPMENT ? console.log : false,
  pool: {
    max: parseInt(process.env.DB_POOL_MAX) || 10,
    min: parseInt(process.env.DB_POOL_MIN) || 0,
    acquire: 30000,
    idle: 10000
  },
  sync: process.env.SYNC_DB === 'true'
};

// JWT配置
const jwt = {
  secret: process.env.JWT_SECRET || 'yizhanbang-jwt-secret-key',
  expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d'
};

// 微信小程序配置
const wechat = {
  appId: process.env.WECHAT_APPID || '',
  appSecret: process.env.WECHAT_SECRET || ''
};

// 短信配置
const sms = {
  apiKey: process.env.SMS_API_KEY || '',
  loginTemplateId: process.env.SMS_LOGIN_TEMPLATE_ID || '123456',
  registerTemplateId: process.env.SMS_REGISTER_TEMPLATE_ID || '123457',
  resetPasswordTemplateId: process.env.SMS_RESET_PASSWORD_TEMPLATE_ID || '123458',
  signName: '驿站帮',
  expireTime: 600, // 10分钟
  sendLimit: {
    interval: 60,   // 两次发送间隔（秒）
    hourLimit: 5,   // 每小时最大发送次数
    dayLimit: 20    // 每天最大发送次数
  }
};

// 文件上传配置
const upload = {
  path: process.env.UPLOAD_PATH || './uploads',
  maxSize: parseInt(process.env.UPLOAD_MAX_SIZE) || 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
};

// 日志配置
const log = {
  level: process.env.LOG_LEVEL || 'info',
  file: process.env.LOG_FILE || './logs/app.log',
  maxSize: '20m',
  maxFiles: '14d'
};

// API配置
const api = {
  prefix: '/api',
  version: 'v1',
  timeout: 30000,
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 100 // 限制每个IP 15分钟内最多100个请求
  }
};

// 安全配置
const security = {
  cors: {
    origin: IS_PRODUCTION ? process.env.ALLOWED_ORIGINS?.split(',') : true,
    credentials: true
  },
  helmet: {
    contentSecurityPolicy: IS_PRODUCTION,
    crossOriginEmbedderPolicy: false
  }
};

module.exports = {
  server,
  database,
  jwt,
  wechat,
  sms,
  upload,
  log,
  api,
  security,
  
  // 便捷访问
  NODE_ENV,
  IS_PRODUCTION,
  IS_DEVELOPMENT
};
