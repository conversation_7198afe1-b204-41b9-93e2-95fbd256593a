#!/bin/bash

echo "=== MySQL密码问题修复指南 ==="
echo ""
echo "您提到使用密码 'Password123!' 连接MySQL失败，以下是修复步骤："
echo ""
echo "方法1：使用提供的密码创建/更新远程用户"
echo "----------------------------------------"
echo "1. 连接到MySQL (输入您的root密码)："
echo "   mysql -u root -p"
echo ""
echo "2. 在MySQL提示符下运行以下命令创建新的远程用户："
echo "   CREATE USER 'remote_user'@'%' IDENTIFIED BY 'Password123!';"
echo "   GRANT ALL PRIVILEGES ON *.* TO 'remote_user'@'%';"
echo "   FLUSH PRIVILEGES;"
echo ""
echo "3. 如果用户已存在，则可以修改密码："
echo "   ALTER USER 'remote_user'@'%' IDENTIFIED BY 'Password123!';"
echo "   FLUSH PRIVILEGES;"
echo ""
echo "4. 退出MySQL:"
echo "   EXIT;"
echo ""
echo "方法2：重置root密码为Password123!"
echo "-------------------------------"
echo "如果您想把root密码设置为Password123!，按照以下步骤操作："
echo ""
echo "1. 连接到MySQL (输入当前root密码)："
echo "   mysql -u root -p"
echo ""
echo "2. 在MySQL提示符下运行："
echo "   ALTER USER 'root'@'localhost' IDENTIFIED BY 'Password123!';"
echo "   FLUSH PRIVILEGES;"
echo "   EXIT;"
echo ""
echo "方法3：检查远程连接设置"
echo "---------------------"
echo "1. 确认MySQL配置允许远程连接："
echo "   cat /opt/homebrew/etc/my.cnf"
echo ""
echo "2. 如果bind-address不是0.0.0.0，请修改："
echo "   sudo nano /opt/homebrew/etc/my.cnf"
echo "   将 bind-address = 127.0.0.1 修改为 bind-address = 0.0.0.0"
echo "   将 mysqlx-bind-address = 127.0.0.1 修改为 mysqlx-bind-address = 0.0.0.0"
echo ""
echo "3. 重启MySQL服务："
echo "   brew services restart mysql"
echo ""
echo "连接信息"
echo "-------"
echo "使用以下信息进行远程连接："
echo "主机: 您的IP地址"
echo "端口: 3306"
echo "用户名: remote_user (或root，如果您修改了root密码)"
echo "密码: Password123!"
echo ""
echo "远程连接命令示例："
echo "mysql -h <您的IP> -u remote_user -p'Password123!'"
echo ""
echo "注意：密码中包含特殊字符(!)，确保在命令行中正确转义，或者在提示符中输入。" 