<template>
  <div class="app-wrapper">
    <div class="sidebar-container" :class="{'is-collapsed': isCollapse}">
      <!-- 侧边栏 -->
      <div class="logo-container">
        <div class="logo">
          <i class="el-icon-s-platform"></i>
        </div>
        <h1 class="title" v-show="!isCollapse">驿站帮Pro</h1>
      </div>
      <!-- 菜单组件会在这里 -->
      <el-menu
        :default-active="activeMenu"
        background-color="transparent"
        text-color="#bfcbd9"
        active-text-color="#ffffff"
        :collapse="isCollapse"
        :unique-opened="true"
        router
        mode="vertical"
        class="sidebar-menu"
      >
        <el-menu-item index="/">
          <i class="el-icon-s-home"></i>
          <span>首页</span>
        </el-menu-item>
        
        <el-sub-menu index="/system">
          <template #title>
            <i class="el-icon-s-tools"></i>
            <span>系统管理</span>
          </template>
          <el-menu-item index="/system/role">角色管理</el-menu-item>
          <el-menu-item index="/system/menu">菜单管理</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/station">
          <template #title>
            <i class="el-icon-s-shop"></i>
            <span>站点管理</span>
          </template>
          <el-menu-item index="/station/list">站点列表</el-menu-item>
          <el-menu-item index="/station/verify">站点审核</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/order">
          <template #title>
            <i class="el-icon-s-order"></i>
            <span>订单管理</span>
          </template>
          <el-menu-item index="/order/list">订单列表</el-menu-item>
          <el-menu-item index="/order/refund">退款管理</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/service">
          <template #title>
            <i class="el-icon-s-cooperation"></i>
            <span>服务管理</span>
          </template>
          <el-menu-item index="/service/list">服务列表</el-menu-item>
        </el-sub-menu>
        
        <el-menu-item index="/job/list">
          <i class="el-icon-s-claim"></i>
          <span>任务管理</span>
        </el-menu-item>
        
        <el-menu-item index="/device/list">
          <i class="el-icon-s-platform"></i>
          <span>设备管理</span>
        </el-menu-item>
        
        <el-sub-menu index="/transfer">
          <template #title>
            <i class="el-icon-s-shop"></i>
            <span>驿站转让</span>
          </template>
          <el-menu-item index="/transfer/list">转让列表</el-menu-item>
          <el-menu-item index="/transfer/statistics">转让统计</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/equipment">
          <template #title>
            <i class="el-icon-printer"></i>
            <span>设备交易</span>
          </template>
          <el-menu-item index="/equipment/list">设备列表</el-menu-item>
          <el-menu-item index="/equipment/category">设备分类</el-menu-item>
          <el-menu-item index="/equipment/price">价格参考</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/recruitment">
          <template #title>
            <i class="el-icon-suitcase"></i>
            <span>招聘求职</span>
          </template>
          <el-menu-item index="/recruitment/list">招聘列表</el-menu-item>
          <el-menu-item index="/recruitment/category">职位分类</el-menu-item>
          <el-menu-item index="/recruitment/statistics">招聘统计</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/substitution">
          <template #title>
            <i class="el-icon-alarm-clock"></i>
            <span>顶班服务</span>
          </template>
          <el-menu-item index="/substitution/list">顶班列表</el-menu-item>
          <el-menu-item index="/substitution/application">接单申请</el-menu-item>
          <el-menu-item index="/substitution/evaluation">服务评价</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/shop">
          <template #title>
            <i class="el-icon-s-goods"></i>
            <span>商城管理</span>
          </template>
          <el-menu-item index="/shop/decoration">小程序装修</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/promotion">
          <template #title>
            <i class="el-icon-present"></i>
            <span>推广赚钱</span>
          </template>
          <el-menu-item index="/promotion/user">推广员管理</el-menu-item>
          <el-menu-item index="/promotion/code">推广码管理</el-menu-item>
          <el-menu-item index="/promotion/commission">佣金规则</el-menu-item>
          <el-menu-item index="/promotion/withdrawal">提现管理</el-menu-item>
          <el-menu-item index="/promotion/statistics">推广统计</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/statistics">
          <template #title>
            <i class="el-icon-s-data"></i>
            <span>统计分析</span>
          </template>
          <el-menu-item index="/statistics/overview">统计概览</el-menu-item>
          <el-menu-item index="/statistics/user">用户统计</el-menu-item>
          <el-menu-item index="/statistics/order">订单统计</el-menu-item>
        </el-sub-menu>
      </el-menu>
    </div>
    
    <div class="main-container">
      <div class="navbar">
        <!-- 顶部导航栏 -->
        <div class="hamburger-container" @click="toggleSideBar">
          <i :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
        </div>
        
        <div class="right-menu">
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              管理员 <i class="el-icon-arrow-down"></i>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>个人中心</el-dropdown-item>
                <el-dropdown-item divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <div class="app-main">
        <!-- 主内容区 -->
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Layout',
  data() {
    return {
      isCollapse: false,
      activeMenu: '/'
    }
  },
  methods: {
    toggleSideBar() {
      this.isCollapse = !this.isCollapse
    }
  }
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  height: 100vh;
  display: flex;
  overflow: hidden;
}

.sidebar-container {
  width: 240px;
  height: 100vh;
  background: linear-gradient(180deg, #304156 0%, #2c3e50 100%);
  transition: all 0.3s ease;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }

  &.is-collapsed {
    width: 64px;
  }

  .logo-container {
    height: 70px;
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .logo {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
      flex-shrink: 0;
    }

    .title {
      margin-left: 12px;
      color: #fff;
      font-size: 18px;
      font-weight: 600;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  :deep(.sidebar-menu) {
    border: none;

    .el-menu-item {
      height: 50px;
      line-height: 50px;
      margin: 4px 12px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
      }

      &.is-active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: #ffffff !important;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 20px;
          background: #ffffff;
          border-radius: 2px;
        }
      }

      i {
        margin-right: 8px;
        font-size: 16px;
      }
    }

    .el-sub-menu {
      .el-sub-menu__title {
        height: 50px;
        line-height: 50px;
        margin: 4px 12px;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.1) !important;
          color: #ffffff !important;
        }

        i {
          margin-right: 8px;
          font-size: 16px;
        }
      }

      .el-menu {
        background: transparent;

        .el-menu-item {
          margin: 2px 24px;
          padding-left: 40px !important;
          font-size: 14px;

          &:hover {
            background: rgba(255, 255, 255, 0.08) !important;
          }

          &.is-active {
            background: rgba(255, 255, 255, 0.15) !important;
            color: #ffffff !important;
          }
        }
      }
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #f8f9fa;

  .navbar {
    height: 60px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    border-bottom: 1px solid #e9ecef;

    .hamburger-container {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: #495057;

      &:hover {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
      }
    }

    .right-menu {
      display: flex;
      align-items: center;
      gap: 16px;

      .el-dropdown-link {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border-radius: 20px;
        color: #495057;
        cursor: pointer;
        transition: all 0.3s ease;
        background: rgba(102, 126, 234, 0.1);

        &:hover {
          background: rgba(102, 126, 234, 0.2);
          color: #667eea;
        }

        &::before {
          content: '';
          width: 32px;
          height: 32px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 50%;
          margin-right: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 14px;
          content: '管';
        }
      }
    }
  }

  .app-main {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-wrapper {
    .sidebar-container {
      position: fixed;
      z-index: 1000;
      height: 100vh;

      &:not(.is-collapsed) {
        width: 240px;
      }
    }

    .main-container {
      margin-left: 0;

      .navbar {
        padding: 0 16px;

        .hamburger-container {
          z-index: 1001;
        }
      }

      .app-main {
        padding: 16px;
      }
    }
  }
}
</style> 