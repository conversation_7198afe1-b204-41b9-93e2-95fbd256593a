<template>
  <div class="app-wrapper">
    <div class="sidebar-container" :class="{'is-collapsed': isCollapse}">
      <!-- 侧边栏 -->
      <div class="logo-container">
        <div class="logo">
          <i class="el-icon-s-platform"></i>
        </div>
        <h1 class="title" v-show="!isCollapse">驿站帮Pro</h1>
      </div>
      <!-- 菜单组件 -->
      <el-menu
        :default-active="activeMenu"
        background-color="transparent"
        text-color="#bfcbd9"
        active-text-color="#ffffff"
        :collapse="isCollapse"
        :unique-opened="true"
        router
        mode="vertical"
        class="sidebar-menu"
      >
        <!-- 仪表板 -->
        <el-menu-item index="/dashboard">
          <i class="el-icon-data-analysis"></i>
          <span>仪表板</span>
        </el-menu-item>

        <!-- 用户管理 -->
        <el-sub-menu index="user">
          <template #title>
            <i class="el-icon-user-solid"></i>
            <span>用户管理</span>
          </template>
          <el-menu-item index="/user/list">用户列表</el-menu-item>
          <el-menu-item index="/user/profile">用户资料</el-menu-item>
          <el-menu-item index="/user/auth">实名认证</el-menu-item>
        </el-sub-menu>

        <!-- 驿站管理 -->
        <el-sub-menu index="station">
          <template #title>
            <i class="el-icon-s-shop"></i>
            <span>驿站管理</span>
          </template>
          <el-menu-item index="/station/list">驿站列表</el-menu-item>
          <el-menu-item index="/station/audit">驿站审核</el-menu-item>
          <el-menu-item index="/station/transfer">驿站转让</el-menu-item>
        </el-sub-menu>

        <!-- 订单管理 -->
        <el-sub-menu index="order">
          <template #title>
            <i class="el-icon-s-order"></i>
            <span>订单管理</span>
          </template>
          <el-menu-item index="/order/list">订单列表</el-menu-item>
          <el-menu-item index="/order/refund">退款管理</el-menu-item>
          <el-menu-item index="/order/statistics">订单统计</el-menu-item>
        </el-sub-menu>

        <!-- 服务管理 -->
        <el-sub-menu index="service">
          <template #title>
            <i class="el-icon-s-tools"></i>
            <span>服务管理</span>
          </template>
          <el-menu-item index="/service/equipment">设备交易</el-menu-item>
          <el-menu-item index="/service/job">招聘求职</el-menu-item>
          <el-menu-item index="/service/shift">顶班服务</el-menu-item>
          <el-menu-item index="/service/express">快递服务</el-menu-item>
        </el-sub-menu>

        <!-- 财务管理 -->
        <el-sub-menu index="finance">
          <template #title>
            <i class="el-icon-money"></i>
            <span>财务管理</span>
          </template>
          <el-menu-item index="/finance/revenue">收入统计</el-menu-item>
          <el-menu-item index="/finance/settlement">结算管理</el-menu-item>
          <el-menu-item index="/finance/withdraw">提现管理</el-menu-item>
        </el-sub-menu>

        <!-- 营销推广 -->
        <el-sub-menu index="marketing">
          <template #title>
            <i class="el-icon-present"></i>
            <span>营销推广</span>
          </template>
          <el-menu-item index="/marketing/coupon">优惠券</el-menu-item>
          <el-menu-item index="/marketing/activity">活动管理</el-menu-item>
          <el-menu-item index="/marketing/banner">轮播图</el-menu-item>
        </el-sub-menu>

        <!-- 数据统计 -->
        <el-sub-menu index="statistics">
          <template #title>
            <i class="el-icon-s-data"></i>
            <span>数据统计</span>
          </template>
          <el-menu-item index="/statistics/overview">数据概览</el-menu-item>
          <el-menu-item index="/statistics/user">用户分析</el-menu-item>
          <el-menu-item index="/statistics/business">业务分析</el-menu-item>
        </el-sub-menu>

        <!-- 系统设置 -->
        <el-sub-menu index="system">
          <template #title>
            <i class="el-icon-setting"></i>
            <span>系统设置</span>
          </template>
          <el-menu-item index="/system/config">系统配置</el-menu-item>
          <el-menu-item index="/system/admin">管理员</el-menu-item>
          <el-menu-item index="/system/log">操作日志</el-menu-item>
        </el-sub-menu>
      </el-menu>
    </div>
    
    <div class="main-container">
      <div class="navbar">
        <!-- 顶部导航栏 -->
        <div class="hamburger-container" @click="toggleSideBar">
          <i :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
        </div>
        
        <div class="right-menu">
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              管理员 <i class="el-icon-arrow-down"></i>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>个人中心</el-dropdown-item>
                <el-dropdown-item divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <div class="app-main">
        <!-- 主内容区 -->
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Layout',
  data() {
    return {
      isCollapse: false,
      activeMenu: '/'
    }
  },
  methods: {
    toggleSideBar() {
      this.isCollapse = !this.isCollapse
    }
  }
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  height: 100vh;
  display: flex;
  overflow: hidden;
}

.sidebar-container {
  width: 240px;
  height: 100vh;
  background: linear-gradient(180deg, #304156 0%, #2c3e50 100%);
  transition: all 0.3s ease;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }

  &.is-collapsed {
    width: 64px;
  }

  .logo-container {
    height: 70px;
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .logo {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
      flex-shrink: 0;
    }

    .title {
      margin-left: 12px;
      color: #fff;
      font-size: 18px;
      font-weight: 600;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  :deep(.sidebar-menu) {
    border: none;

    .el-menu-item {
      height: 50px;
      line-height: 50px;
      margin: 4px 12px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
      }

      &.is-active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: #ffffff !important;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 20px;
          background: #ffffff;
          border-radius: 2px;
        }
      }

      i {
        margin-right: 8px;
        font-size: 16px;
      }
    }

    .el-sub-menu {
      .el-sub-menu__title {
        height: 50px;
        line-height: 50px;
        margin: 4px 12px;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.1) !important;
          color: #ffffff !important;
        }

        i {
          margin-right: 8px;
          font-size: 16px;
        }
      }

      .el-menu {
        background: transparent;

        .el-menu-item {
          margin: 2px 24px;
          padding-left: 40px !important;
          font-size: 14px;

          &:hover {
            background: rgba(255, 255, 255, 0.08) !important;
          }

          &.is-active {
            background: rgba(255, 255, 255, 0.15) !important;
            color: #ffffff !important;
          }
        }
      }
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #f8f9fa;

  .navbar {
    height: 60px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    border-bottom: 1px solid #e9ecef;

    .hamburger-container {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      cursor: pointer;
      transition: all 0.3s ease;
      color: #495057;

      &:hover {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
      }
    }

    .right-menu {
      display: flex;
      align-items: center;
      gap: 16px;

      .el-dropdown-link {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border-radius: 20px;
        color: #495057;
        cursor: pointer;
        transition: all 0.3s ease;
        background: rgba(102, 126, 234, 0.1);

        &:hover {
          background: rgba(102, 126, 234, 0.2);
          color: #667eea;
        }

        &::before {
          content: '';
          width: 32px;
          height: 32px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border-radius: 50%;
          margin-right: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 14px;
          content: '管';
        }
      }
    }
  }

  .app-main {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-wrapper {
    .sidebar-container {
      position: fixed;
      z-index: 1000;
      height: 100vh;

      &:not(.is-collapsed) {
        width: 240px;
      }
    }

    .main-container {
      margin-left: 0;

      .navbar {
        padding: 0 16px;

        .hamburger-container {
          z-index: 1001;
        }
      }

      .app-main {
        padding: 16px;
      }
    }
  }
}
</style> 