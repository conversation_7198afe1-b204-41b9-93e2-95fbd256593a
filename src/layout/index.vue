<template>
  <div class="app-wrapper">
    <div class="sidebar-container" :class="{'is-collapsed': isCollapse}">
      <!-- 侧边栏 -->
      <div class="logo-container">
        <h1 class="title">驿站帮Pro管理系统</h1>
      </div>
      <!-- 菜单组件会在这里 -->
      <el-menu
        :default-active="activeMenu"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        :collapse="isCollapse"
        :unique-opened="true"
        router
        mode="vertical"
      >
        <el-menu-item index="/">
          <i class="el-icon-s-home"></i>
          <span>首页</span>
        </el-menu-item>
        
        <el-sub-menu index="/system">
          <template #title>
            <i class="el-icon-s-tools"></i>
            <span>系统管理</span>
          </template>
          <el-menu-item index="/system/role">角色管理</el-menu-item>
          <el-menu-item index="/system/menu">菜单管理</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/station">
          <template #title>
            <i class="el-icon-s-shop"></i>
            <span>站点管理</span>
          </template>
          <el-menu-item index="/station/list">站点列表</el-menu-item>
          <el-menu-item index="/station/verify">站点审核</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/order">
          <template #title>
            <i class="el-icon-s-order"></i>
            <span>订单管理</span>
          </template>
          <el-menu-item index="/order/list">订单列表</el-menu-item>
          <el-menu-item index="/order/refund">退款管理</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/service">
          <template #title>
            <i class="el-icon-s-cooperation"></i>
            <span>服务管理</span>
          </template>
          <el-menu-item index="/service/list">服务列表</el-menu-item>
        </el-sub-menu>
        
        <el-menu-item index="/job/list">
          <i class="el-icon-s-claim"></i>
          <span>任务管理</span>
        </el-menu-item>
        
        <el-menu-item index="/device/list">
          <i class="el-icon-s-platform"></i>
          <span>设备管理</span>
        </el-menu-item>
        
        <el-sub-menu index="/transfer">
          <template #title>
            <i class="el-icon-s-shop"></i>
            <span>驿站转让</span>
          </template>
          <el-menu-item index="/transfer/list">转让列表</el-menu-item>
          <el-menu-item index="/transfer/statistics">转让统计</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/equipment">
          <template #title>
            <i class="el-icon-printer"></i>
            <span>设备交易</span>
          </template>
          <el-menu-item index="/equipment/list">设备列表</el-menu-item>
          <el-menu-item index="/equipment/category">设备分类</el-menu-item>
          <el-menu-item index="/equipment/price">价格参考</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/recruitment">
          <template #title>
            <i class="el-icon-suitcase"></i>
            <span>招聘求职</span>
          </template>
          <el-menu-item index="/recruitment/list">招聘列表</el-menu-item>
          <el-menu-item index="/recruitment/category">职位分类</el-menu-item>
          <el-menu-item index="/recruitment/statistics">招聘统计</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/substitution">
          <template #title>
            <i class="el-icon-alarm-clock"></i>
            <span>顶班服务</span>
          </template>
          <el-menu-item index="/substitution/list">顶班列表</el-menu-item>
          <el-menu-item index="/substitution/application">接单申请</el-menu-item>
          <el-menu-item index="/substitution/evaluation">服务评价</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/shop">
          <template #title>
            <i class="el-icon-s-goods"></i>
            <span>商城管理</span>
          </template>
          <el-menu-item index="/shop/decoration">小程序装修</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/promotion">
          <template #title>
            <i class="el-icon-present"></i>
            <span>推广赚钱</span>
          </template>
          <el-menu-item index="/promotion/user">推广员管理</el-menu-item>
          <el-menu-item index="/promotion/code">推广码管理</el-menu-item>
          <el-menu-item index="/promotion/commission">佣金规则</el-menu-item>
          <el-menu-item index="/promotion/withdrawal">提现管理</el-menu-item>
          <el-menu-item index="/promotion/statistics">推广统计</el-menu-item>
        </el-sub-menu>
        
        <el-sub-menu index="/statistics">
          <template #title>
            <i class="el-icon-s-data"></i>
            <span>统计分析</span>
          </template>
          <el-menu-item index="/statistics/overview">统计概览</el-menu-item>
          <el-menu-item index="/statistics/user">用户统计</el-menu-item>
          <el-menu-item index="/statistics/order">订单统计</el-menu-item>
        </el-sub-menu>
      </el-menu>
    </div>
    
    <div class="main-container">
      <div class="navbar">
        <!-- 顶部导航栏 -->
        <div class="hamburger-container" @click="toggleSideBar">
          <i :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
        </div>
        
        <div class="right-menu">
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              管理员 <i class="el-icon-arrow-down"></i>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>个人中心</el-dropdown-item>
                <el-dropdown-item divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <div class="app-main">
        <!-- 主内容区 -->
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Layout',
  data() {
    return {
      isCollapse: false,
      activeMenu: '/'
    }
  },
  methods: {
    toggleSideBar() {
      this.isCollapse = !this.isCollapse
    }
  }
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  height: 100%;
  display: flex;
}

.sidebar-container {
  width: 210px;
  height: 100%;
  background-color: #304156;
  transition: width 0.28s;
  overflow-y: auto;
  
  &.is-collapsed {
    width: 64px;
  }
  
  .logo-container {
    height: 60px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .logo {
      width: 32px;
      height: 32px;
    }
    
    .title {
      margin-left: 10px;
      color: #fff;
      font-size: 16px;
      font-weight: 600;
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .navbar {
    height: 50px;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0,21,41,.08);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    
    .hamburger-container {
      font-size: 20px;
      cursor: pointer;
    }
    
    .right-menu {
      display: flex;
      align-items: center;
      
      .el-dropdown-link {
        color: #606266;
        cursor: pointer;
      }
    }
  }
  
  .app-main {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background-color: #f0f2f5;
  }
}
</style> 