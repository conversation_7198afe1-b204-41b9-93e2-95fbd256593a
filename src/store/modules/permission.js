import { constantRoutes } from '@/router'

const permission = {
  state: {
    routes: [],
    addRoutes: []
  },
  mutations: {
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes
      state.routes = constantRoutes.concat(routes)
    }
  },
  actions: {
    // 生成路由
    generateRoutes({ commit }) {
      return new Promise(resolve => {
        // 向后端请求路由数据
        // 这里暂时使用前端定义的路由
        const accessedRoutes = constantRoutes
        commit('SET_ROUTES', [])
        resolve(accessedRoutes)
      })
    }
  }
}

export default permission 