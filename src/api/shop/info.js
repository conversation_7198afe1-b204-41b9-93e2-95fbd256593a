import request from '@/utils/request'

// 获取商店基本信息
export function getShopInfo() {
  return request({
    url: '/shop/info',
    method: 'get'
  })
}

// 更新商店基本信息
export function updateShopInfo(data) {
  return request({
    url: '/shop/info',
    method: 'put',
    data: data
  })
}

// 获取商店营业统计
export function getShopStatistics() {
  return request({
    url: '/shop/statistics',
    method: 'get'
  })
}

// 获取商店评价统计
export function getShopRating() {
  return request({
    url: '/shop/rating',
    method: 'get'
  })
} 