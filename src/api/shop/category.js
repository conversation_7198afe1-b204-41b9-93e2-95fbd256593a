import request from '@/utils/request'

// 查询商品分类列表
export function listCategory(query) {
  return request({
    url: '/shop/category/list',
    method: 'get',
    params: query
  })
}

// 查询分类详情
export function getCategory(id) {
  return request({
    url: '/shop/category/' + id,
    method: 'get'
  })
}

// 新增分类
export function addCategory(data) {
  return request({
    url: '/shop/category',
    method: 'post',
    data: data
  })
}

// 修改分类
export function updateCategory(data) {
  return request({
    url: '/shop/category',
    method: 'put',
    data: data
  })
}

// 删除分类
export function deleteCategory(id) {
  return request({
    url: '/shop/category/' + id,
    method: 'delete'
  })
}

// 查询分类树结构
export function getCategoryTree() {
  return request({
    url: '/shop/category/tree',
    method: 'get'
  })
} 