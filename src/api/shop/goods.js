import request from '@/utils/request'

// 查询商品列表
export function listGoods(query) {
  return request({
    url: '/shop/goods/list',
    method: 'get',
    params: query
  })
}

// 查询商品详情
export function getGoods(id) {
  return request({
    url: '/shop/goods/' + id,
    method: 'get'
  })
}

// 新增商品
export function addGoods(data) {
  return request({
    url: '/shop/goods',
    method: 'post',
    data: data
  })
}

// 修改商品
export function updateGoods(data) {
  return request({
    url: '/shop/goods',
    method: 'put',
    data: data
  })
}

// 删除商品
export function deleteGoods(id) {
  return request({
    url: '/shop/goods/' + id,
    method: 'delete'
  })
}

// 上架商品
export function onShelf(ids) {
  return request({
    url: '/shop/goods/onShelf',
    method: 'put',
    data: { ids }
  })
}

// 下架商品
export function offShelf(ids) {
  return request({
    url: '/shop/goods/offShelf',
    method: 'put',
    data: { ids }
  })
}

// 查询推荐商品
export function getRecommendGoods() {
  return request({
    url: '/shop/goods/recommend',
    method: 'get'
  })
}

// 查询热销商品
export function getHotGoods() {
  return request({
    url: '/shop/goods/hot',
    method: 'get'
  })
}

// 查询新品商品
export function getNewGoods() {
  return request({
    url: '/shop/goods/new',
    method: 'get'
  })
} 