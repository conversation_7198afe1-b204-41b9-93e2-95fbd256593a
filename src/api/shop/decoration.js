import request from '@/utils/request';

// 获取店铺样式配置
export function getShopStyle() {
  return request({
    url: '/shop/decoration/style',
    method: 'get'
  });
}

// 保存店铺样式配置
export function saveShopStyle(data) {
  return request({
    url: '/shop/decoration/style',
    method: 'put',
    data: data
  });
}

// 应用店铺样式到小程序
export function applyShopStyle(data) {
  return request({
    url: '/shop/decoration/style/apply',
    method: 'post',
    data: data
  });
}

// 获取菜单配置
export function getMenuConfig() {
  return request({
    url: '/shop/decoration/menu',
    method: 'get'
  });
}

// 保存菜单配置
export function saveMenuConfig(data) {
  return request({
    url: '/shop/decoration/menu',
    method: 'put',
    data: data
  });
}

// 应用菜单配置到小程序
export function applyMenuConfig(data) {
  return request({
    url: '/shop/decoration/menu/apply',
    method: 'post',
    data: data
  });
}

// 获取首页轮播图配置
export function getBannerConfig() {
  return request({
    url: '/shop/decoration/banner',
    method: 'get'
  });
}

// 保存首页轮播图配置
export function saveBannerConfig(data) {
  return request({
    url: '/shop/decoration/banner',
    method: 'put',
    data: data
  });
}

// 获取导航配置
export function getNavConfig() {
  return request({
    url: '/shop/decoration/nav',
    method: 'get'
  });
}

// 保存导航配置
export function saveNavConfig(data) {
  return request({
    url: '/shop/decoration/nav',
    method: 'put',
    data: data
  });
}

// 获取首页布局配置
export function getLayoutConfig() {
  return request({
    url: '/shop/decoration/layout',
    method: 'get'
  });
}

// 保存首页布局配置
export function saveLayoutConfig(data) {
  return request({
    url: '/shop/decoration/layout',
    method: 'put',
    data: data
  });
}

// 应用首页配置到小程序
export function applyHomeConfig() {
  return request({
    url: '/shop/decoration/home/<USER>',
    method: 'post'
  });
} 