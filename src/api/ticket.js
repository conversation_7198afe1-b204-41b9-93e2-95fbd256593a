import request from '@/utils/request'

/**
 * 获取工单列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getTicketList(params) {
  return request({
    url: '/ticket/list',
    method: 'get',
    params
  })
}

/**
 * 获取工单详情
 * @param {Number} id 工单ID
 * @returns {Promise}
 */
export function getTicketDetail(id) {
  return request({
    url: `/ticket/${id}`,
    method: 'get'
  })
}

/**
 * 添加工单
 * @param {Object} data 工单数据
 * @returns {Promise}
 */
export function addTicket(data) {
  return request({
    url: '/ticket',
    method: 'post',
    data
  })
}

/**
 * 修改工单
 * @param {Object} data 工单数据
 * @returns {Promise}
 */
export function updateTicket(data) {
  return request({
    url: '/ticket',
    method: 'put',
    data
  })
}

/**
 * 删除工单
 * @param {Number} id 工单ID
 * @returns {Promise}
 */
export function deleteTicket(id) {
  return request({
    url: `/ticket/${id}`,
    method: 'delete'
  })
}

/**
 * 处理工单
 * @param {Object} data 处理数据
 * @returns {Promise}
 */
export function processTicket(data) {
  return request({
    url: '/ticket/process',
    method: 'put',
    data
  })
}

/**
 * 导出工单
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function exportTicket(params) {
  return request({
    url: '/ticket/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取工单统计数据
 * @returns {Promise}
 */
export function getTicketStatistics() {
  return request({
    url: '/ticket/statistics',
    method: 'get'
  })
} 