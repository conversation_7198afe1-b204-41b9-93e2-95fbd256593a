import request from '@/utils/request'

/**
 * 获取统计数据概览
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getStatisticsOverview(params) {
  return request({
    url: '/statistics/overview',
    method: 'get',
    params
  })
}

/**
 * 获取订单趋势数据
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getOrderTrend(params) {
  return request({
    url: '/statistics/orderTrend',
    method: 'get',
    params
  })
}

/**
 * 获取订单类型分布
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getOrderTypeDistribution(params) {
  return request({
    url: '/statistics/orderTypeDistribution',
    method: 'get',
    params
  })
}

/**
 * 获取用户增长趋势
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getUserGrowthTrend(params) {
  return request({
    url: '/statistics/userGrowthTrend',
    method: 'get',
    params
  })
}

/**
 * 获取用户地区分布
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getUserRegionDistribution(params) {
  return request({
    url: '/statistics/userRegionDistribution',
    method: 'get',
    params
  })
}

/**
 * 获取热门驿站排行
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getHotStations(params) {
  return request({
    url: '/statistics/hotStations',
    method: 'get',
    params
  })
}

/**
 * 导出统计数据
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function exportStatisticsData(params) {
  return request({
    url: '/statistics/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
} 