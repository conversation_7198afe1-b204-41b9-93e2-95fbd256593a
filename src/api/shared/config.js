/**
 * 共享API配置文件
 * 用于后台管理系统与小程序之间共享API配置
 */

// API版本
export const API_VERSION = 'v1';

// 响应状态码
export const RESPONSE_CODE = {
  SUCCESS: 0,  // 成功
  ERROR: 1,    // 一般错误
  AUTH_FAIL: 401,  // 认证失败
  PERMISSION_DENIED: 403, // 权限不足
  NOT_FOUND: 404,  // 资源不存在
  SERVER_ERROR: 500 // 服务器错误
};

// API响应格式
export const createSuccessResponse = (data, message = '操作成功') => {
  return {
    code: RESPONSE_CODE.SUCCESS,
    message,
    data,
    timestamp: Date.now()
  };
};

export const createErrorResponse = (code = RESPONSE_CODE.ERROR, message = '操作失败', data = null) => {
  return {
    code,
    message,
    data,
    timestamp: Date.now()
  };
};

// 数据同步设置
export const SYNC_SETTINGS = {
  // 同步间隔 (毫秒)
  interval: 5 * 60 * 1000, // 5分钟
  
  // 需要同步的模块
  modules: [
    'identity',  // 身份认证
    'station',   // 驿站管理
    'promotion', // 推广赚钱
    'order',     // 订单系统
    'user'       // 用户数据
  ],
  
  // 是否启用实时同步
  realtime: true,
  
  // 冲突解决策略: 'admin-first' | 'client-first' | 'latest-win'
  conflictStrategy: 'latest-win'
};

// 是否启用请求日志
export const ENABLE_LOG = process.env.NODE_ENV !== 'production'; 