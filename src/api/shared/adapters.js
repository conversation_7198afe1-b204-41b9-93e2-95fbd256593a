/**
 * API适配器
 * 用于适配小程序和后台管理系统的数据格式
 */
import { createSuccessResponse, createErrorResponse } from './config';

/**
 * 身份认证模块适配器
 */
export const identityAdapter = {
  /**
   * 将后台管理系统数据转换为小程序格式
   * @param {Object} adminData - 后台管理系统数据
   * @returns {Object} - 小程序格式数据
   */
  toMiniprogram(adminData) {
    // 身份认证列表项转换
    if (Array.isArray(adminData)) {
      return adminData.map(item => ({
        id: item.id,
        userId: item.userId,
        realName: item.realName,
        idCardNumber: item.idCardNumber,
        phone: item.phone,
        status: item.status,
        applyTime: item.createTime,
        reviewTime: item.reviewTime || null,
        remark: item.remark || ''
      }));
    }
    
    // 身份认证详情转换
    return {
      id: adminData.id,
      userId: adminData.userId,
      realName: adminData.realName,
      idCardNumber: adminData.idCardNumber,
      phone: adminData.phone,
      status: adminData.status,
      applyTime: adminData.createTime,
      reviewTime: adminData.reviewTime || null,
      remark: adminData.remark || '',
      idCardFrontUrl: adminData.idCardFrontUrl,
      idCardBackUrl: adminData.idCardBackUrl,
      faceImageUrl: adminData.faceImageUrl || null,
      verifyMethod: adminData.verifyMethod || 'manual'
    };
  },
  
  /**
   * 将小程序数据转换为后台管理系统格式
   * @param {Object} miniData - 小程序数据
   * @returns {Object} - 后台管理系统格式数据
   */
  toAdmin(miniData) {
    // 身份认证申请数据转换
    return {
      id: miniData.id,
      userId: miniData.userId,
      realName: miniData.realName,
      idCardNumber: miniData.idCardNumber,
      phone: miniData.phone,
      status: miniData.status || 'pending',
      createTime: miniData.applyTime,
      reviewTime: miniData.reviewTime,
      remark: miniData.remark,
      idCardFrontUrl: miniData.idCardFrontUrl,
      idCardBackUrl: miniData.idCardBackUrl,
      faceImageUrl: miniData.faceImageUrl,
      verifyMethod: miniData.verifyMethod || 'manual'
    };
  }
};

/**
 * 驿站管理模块适配器
 */
export const stationAdapter = {
  /**
   * 将后台管理系统数据转换为小程序格式
   * @param {Object} adminData - 后台管理系统数据
   * @returns {Object} - 小程序格式数据
   */
  toMiniprogram(adminData) {
    // 驿站列表项转换
    if (Array.isArray(adminData)) {
      return adminData.map(item => ({
        id: item.id,
        name: item.name,
        address: item.address,
        type: item.type,
        level: item.level,
        status: item.status,
        contactPerson: item.contactPerson,
        contactPhone: item.contactPhone,
        createdAt: item.createTime,
        imgUrl: item.imgUrl || []
      }));
    }
    
    // 驿站详情转换
    return {
      id: adminData.id,
      name: adminData.name,
      address: adminData.address,
      location: adminData.location || { latitude: 0, longitude: 0 },
      type: adminData.type,
      level: adminData.level,
      status: adminData.status,
      contactPerson: adminData.contactPerson,
      contactPhone: adminData.contactPhone,
      createdAt: adminData.createTime,
      imgUrl: adminData.imgUrl || [],
      businessHours: adminData.businessHours || [],
      services: adminData.services || [],
      description: adminData.description || ''
    };
  },
  
  /**
   * 将小程序数据转换为后台管理系统格式
   * @param {Object} miniData - 小程序数据
   * @returns {Object} - 后台管理系统格式数据
   */
  toAdmin(miniData) {
    // 驿站数据转换
    return {
      id: miniData.id,
      name: miniData.name,
      address: miniData.address,
      location: miniData.location,
      type: miniData.type,
      level: miniData.level,
      status: miniData.status || 'pending',
      contactPerson: miniData.contactPerson,
      contactPhone: miniData.contactPhone,
      createTime: miniData.createdAt,
      imgUrl: miniData.imgUrl,
      businessHours: miniData.businessHours,
      services: miniData.services,
      description: miniData.description
    };
  }
};

/**
 * 推广系统模块适配器
 */
export const promotionAdapter = {
  /**
   * 将后台管理系统数据转换为小程序格式
   * @param {Object} adminData - 后台管理系统数据
   * @returns {Object} - 小程序格式数据
   */
  toMiniprogram(adminData) {
    // 推广码列表项转换
    if (adminData.type === 'codes' && Array.isArray(adminData.data)) {
      return adminData.data.map(item => ({
        id: item.id,
        code: item.code,
        promoterId: item.promoterId,
        promoterName: item.promoterName,
        type: item.type,
        commissionRate: item.commissionRate,
        expireTime: item.expireTime,
        qrcodeUrl: item.qrcodeUrl,
        createdAt: item.createTime,
        status: item.status
      }));
    }
    
    // 佣金记录列表转换
    if (adminData.type === 'commissions' && Array.isArray(adminData.data)) {
      return adminData.data.map(item => ({
        id: item.id,
        promoterId: item.promoterId,
        promoterName: item.promoterName,
        userId: item.userId,
        userName: item.userName,
        amount: item.amount,
        orderAmount: item.orderAmount,
        orderType: item.orderType,
        orderId: item.orderId,
        status: item.status,
        createdAt: item.createTime
      }));
    }
    
    // 用户推广数据转换
    if (adminData.type === 'user') {
      return {
        id: adminData.id,
        userId: adminData.userId,
        name: adminData.name,
        phone: adminData.phone,
        totalCommission: adminData.totalCommission,
        availableCommission: adminData.availableCommission,
        withdrawnCommission: adminData.withdrawnCommission,
        inviteCount: adminData.inviteCount,
        status: adminData.status,
        createdAt: adminData.createTime
      };
    }
    
    // 默认返回原始数据
    return adminData;
  },
  
  /**
   * 将小程序数据转换为后台管理系统格式
   * @param {Object} miniData - 小程序数据
   * @returns {Object} - 后台管理系统格式数据
   */
  toAdmin(miniData) {
    // 根据数据类型进行转换
    if (miniData.type === 'withdrawal') {
      // 提现申请数据转换
      return {
        id: miniData.id,
        userId: miniData.userId,
        userName: miniData.userName,
        amount: miniData.amount,
        accountType: miniData.accountType,
        accountInfo: miniData.accountInfo,
        status: miniData.status || 'pending',
        createTime: miniData.createdAt,
        reviewTime: miniData.reviewTime,
        remark: miniData.remark
      };
    }
    
    // 默认返回原始数据
    return miniData;
  }
};

/**
 * 通用适配器工厂
 * 根据模块名称获取适配器
 */
export const getAdapter = (module) => {
  const adapters = {
    identity: identityAdapter,
    station: stationAdapter,
    promotion: promotionAdapter
  };
  
  return adapters[module] || {
    // 默认适配器（透传数据）
    toMiniprogram: data => data,
    toAdmin: data => data
  };
};

export default {
  identityAdapter,
  stationAdapter,
  promotionAdapter,
  getAdapter
}; 