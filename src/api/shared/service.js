/**
 * 共享API服务
 * 实现小程序与后台管理系统的数据互通
 */
import request from '@/utils/request';
import { API_VERSION, SYNC_SETTINGS, createSuccessResponse, createErrorResponse } from './config';

// 数据同步计数器
let syncCounter = 0;

/**
 * 数据同步服务
 * 用于在小程序和后台管理系统之间同步数据
 */
class DataSyncService {
  constructor() {
    this.syncQueue = [];
    this.isSyncing = false;
    this.lastSyncTime = null;
    this.syncInterval = null;
  }

  /**
   * 初始化同步服务
   */
  init() {
    // 启动定时同步
    if (SYNC_SETTINGS.interval > 0) {
      this.syncInterval = setInterval(() => {
        this.syncAll();
      }, SYNC_SETTINGS.interval);
    }

    // 记录初始化时间
    this.lastSyncTime = new Date();
    console.log('数据同步服务已初始化', this.lastSyncTime);
    
    return this;
  }

  /**
   * 添加数据到同步队列
   * @param {String} module - 模块名称
   * @param {String} action - 动作类型 (create|update|delete)
   * @param {Object} data - 数据内容
   * @param {Boolean} immediate - 是否立即同步
   */
  addToQueue(module, action, data, immediate = false) {
    // 检查模块是否在同步列表中
    if (!SYNC_SETTINGS.modules.includes(module)) {
      console.warn(`模块 ${module} 不在同步列表中`);
      return false;
    }

    // 创建同步项
    const syncItem = {
      id: `sync_${Date.now()}_${syncCounter++}`,
      module,
      action,
      data,
      timestamp: Date.now(),
      retries: 0,
      status: 'pending'
    };

    // 添加到队列
    this.syncQueue.push(syncItem);
    console.log(`添加同步项: ${module}.${action}`, data);

    // 如果设置了立即同步或开启了实时同步
    if (immediate || SYNC_SETTINGS.realtime) {
      this.processSyncQueue();
    }

    return syncItem.id;
  }

  /**
   * 处理同步队列
   */
  async processSyncQueue() {
    // 如果正在同步，则返回
    if (this.isSyncing) {
      return;
    }

    // 如果队列为空，则返回
    if (this.syncQueue.length === 0) {
      return;
    }

    // 设置同步状态
    this.isSyncing = true;

    try {
      // 获取下一个待同步项
      const syncItem = this.syncQueue.find(item => item.status === 'pending');
      
      if (!syncItem) {
        this.isSyncing = false;
        return;
      }

      // 更新状态为处理中
      syncItem.status = 'processing';

      // 发送同步请求
      const result = await this.sendSyncRequest(syncItem);

      // 处理同步结果
      if (result.success) {
        // 同步成功，移除队列项
        syncItem.status = 'completed';
        this.syncQueue = this.syncQueue.filter(item => item.id !== syncItem.id);
        console.log(`同步成功: ${syncItem.module}.${syncItem.action}`);
      } else {
        // 同步失败，标记状态
        syncItem.status = 'failed';
        syncItem.error = result.error;
        syncItem.retries += 1;
        
        // 如果重试次数小于3次，则保留在队列中
        if (syncItem.retries < 3) {
          syncItem.status = 'pending';
          console.warn(`同步失败，将重试: ${syncItem.module}.${syncItem.action}`, result.error);
        } else {
          console.error(`同步失败，已达最大重试次数: ${syncItem.module}.${syncItem.action}`, result.error);
        }
      }
    } catch (error) {
      console.error('处理同步队列出错:', error);
    } finally {
      // 重置同步状态
      this.isSyncing = false;
      
      // 如果队列中还有待处理项，继续处理
      if (this.syncQueue.some(item => item.status === 'pending')) {
        setTimeout(() => this.processSyncQueue(), 1000);
      }
    }
  }

  /**
   * 发送同步请求
   * @param {Object} syncItem - 同步项
   * @returns {Promise<Object>} - 同步结果
   */
  async sendSyncRequest(syncItem) {
    try {
      // 构建请求URL
      const url = `/api/${API_VERSION}/sync/${syncItem.module}/${syncItem.action}`;
      
      // 发送请求
      const response = await request({
        url,
        method: 'POST',
        data: {
          data: syncItem.data,
          timestamp: syncItem.timestamp
        }
      });
      
      // 更新最后同步时间
      this.lastSyncTime = new Date();
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error.message || '同步请求失败'
      };
    }
  }

  /**
   * 同步所有模块数据
   */
  async syncAll() {
    console.log('开始全量同步...');
    
    for (const module of SYNC_SETTINGS.modules) {
      try {
        // 获取最后同步时间
        const lastSync = this.lastSyncTime ? this.lastSyncTime.toISOString() : null;
        
        // 发送全量同步请求
        const response = await request({
          url: `/api/${API_VERSION}/sync/${module}/all`,
          method: 'POST',
          data: {
            lastSync
          }
        });
        
        console.log(`模块 ${module} 同步完成:`, response.data);
      } catch (error) {
        console.error(`模块 ${module} 同步失败:`, error);
      }
    }
    
    // 更新最后同步时间
    this.lastSyncTime = new Date();
    console.log('全量同步完成', this.lastSyncTime);
  }

  /**
   * 停止同步服务
   */
  stop() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    
    console.log('数据同步服务已停止');
  }
}

// 创建实例
const dataSyncService = new DataSyncService();

/**
 * 小程序API代理服务
 * 用于在后台管理系统中调用小程序API
 */
export const miniprogramApiProxy = {
  /**
   * 调用小程序API
   * @param {String} module - 模块名称
   * @param {String} method - 方法名称
   * @param {Object} params - 参数
   * @returns {Promise<Object>} - API响应
   */
  async invoke(module, method, params = {}) {
    try {
      const response = await request({
        url: `/api/${API_VERSION}/miniprogram/${module}/${method}`,
        method: 'POST',
        data: params
      });
      
      return response.data;
    } catch (error) {
      console.error(`调用小程序API出错: ${module}.${method}`, error);
      throw error;
    }
  },
  
  /**
   * 获取小程序数据
   * @param {String} module - 模块名称
   * @param {String} id - 数据ID
   * @returns {Promise<Object>} - API响应
   */
  async getData(module, id) {
    return this.invoke(module, 'getData', { id });
  },
  
  /**
   * 获取小程序数据列表
   * @param {String} module - 模块名称
   * @param {Object} query - 查询条件
   * @returns {Promise<Object>} - API响应
   */
  async getList(module, query = {}) {
    return this.invoke(module, 'getList', query);
  }
};

/**
 * 后台管理系统API代理服务
 * 用于在小程序中调用后台管理系统API
 */
export const adminApiProxy = {
  /**
   * 调用后台API
   * @param {String} module - 模块名称
   * @param {String} method - 方法名称
   * @param {Object} params - 参数
   * @returns {Promise<Object>} - API响应
   */
  async invoke(module, method, params = {}) {
    try {
      const response = await request({
        url: `/api/${API_VERSION}/admin/${module}/${method}`,
        method: 'POST',
        data: params
      });
      
      return response.data;
    } catch (error) {
      console.error(`调用后台API出错: ${module}.${method}`, error);
      throw error;
    }
  },
  
  /**
   * 同步数据到后台
   * @param {String} module - 模块名称
   * @param {String} action - 动作 (create|update|delete)
   * @param {Object} data - 数据
   * @param {Boolean} immediate - 是否立即同步
   * @returns {Promise<String>} - 同步ID
   */
  async syncData(module, action, data, immediate = true) {
    return dataSyncService.addToQueue(module, action, data, immediate);
  }
};

// 导出数据同步服务
export const syncService = dataSyncService;

// 默认导出
export default {
  miniprogramApiProxy,
  adminApiProxy,
  syncService
}; 