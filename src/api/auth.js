import request from './request';

/**
 * 微信登录
 * @param {Object} data 登录参数
 * @param {string} data.code 微信登录返回的code
 * @param {Object} data.userInfo 微信用户信息
 */
export function wxLogin(data) {
  return request({
    url: '/api/auth/wx-login',
    method: 'post',
    data
  });
}

/**
 * 手机号登录
 * @param {Object} data 登录参数
 * @param {string} data.phone 手机号
 * @param {string} data.code 验证码
 */
export function phoneLogin(data) {
  return request({
    url: '/api/auth/phone-login',
    method: 'post',
    data
  });
}

/**
 * 密码登录
 * @param {Object} data 登录参数
 * @param {string} data.username 用户名/手机号/邮箱
 * @param {string} data.password 密码
 */
export function passwordLogin(data) {
  return request({
    url: '/api/auth/password-login',
    method: 'post',
    data
  });
}

/**
 * 用户注册
 * @param {Object} data 注册参数
 */
export function register(data) {
  return request({
    url: '/api/auth/register',
    method: 'post',
    data
  });
}

/**
 * 退出登录
 */
export function logout() {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  });
}

/**
 * 刷新令牌
 */
export function refreshToken() {
  return request({
    url: '/api/auth/refresh-token',
    method: 'post'
  });
}

/**
 * 获取登录状态
 */
export function getLoginStatus() {
  return request({
    url: '/api/auth/status',
    method: 'get'
  });
}

export default {
  wxLogin,
  phoneLogin,
  passwordLogin,
  register,
  logout,
  refreshToken,
  getLoginStatus
}; 