import request from '@/utils/request'

// 查询接单申请列表
export function listApplications(query) {
  return request({
    url: '/substitution/application/list',
    method: 'get',
    params: query
  })
}

// 查询接单申请详情
export function getApplication(id) {
  return request({
    url: '/substitution/application/' + id,
    method: 'get'
  })
}

// 批准申请
export function approveApplication(id) {
  return request({
    url: '/substitution/application/approve/' + id,
    method: 'put'
  })
}

// 拒绝申请
export function rejectApplication(data) {
  return request({
    url: '/substitution/application/reject',
    method: 'put',
    data: data
  })
}

// 取消申请
export function cancelApplication(id) {
  return request({
    url: '/substitution/application/cancel/' + id,
    method: 'put'
  })
}

// 添加评价
export function addEvaluation(data) {
  return request({
    url: '/substitution/evaluation',
    method: 'post',
    data: data
  })
} 