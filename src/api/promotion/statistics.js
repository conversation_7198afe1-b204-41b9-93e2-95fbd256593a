import request from '@/utils/request';

/**
 * 获取推广总览数据
 * @param {Object} params 查询参数
 * @param {String} params.timeRange 时间范围：today, yesterday, week, month
 */
export function getPromotionOverview(params) {
  return request({
    url: '/api/admin/promotion/overview',
    method: 'get',
    params
  });
}

/**
 * 获取推广趋势数据
 * @param {Object} params 查询参数
 * @param {String} params.type 数据类型：users, commissions, withdrawals
 * @param {String} params.timeRange 时间范围：week, month, year
 * @param {String} params.startDate 开始日期
 * @param {String} params.endDate 结束日期
 */
export function getPromotionTrend(params) {
  return request({
    url: '/api/admin/promotion/trend',
    method: 'get',
    params
  });
}

/**
 * 获取推广员等级分布
 */
export function getPromoterLevelDistribution() {
  return request({
    url: '/api/admin/promotion/level-distribution',
    method: 'get'
  });
}

/**
 * 获取推广佣金排行
 * @param {Object} params 查询参数
 * @param {Number} params.limit 获取数量，默认10
 * @param {String} params.timeRange 时间范围：week, month, year, all
 */
export function getCommissionRanking(params) {
  return request({
    url: '/api/admin/promotion/commission-ranking',
    method: 'get',
    params
  });
}

/**
 * 获取推广用户排行
 * @param {Object} params 查询参数
 * @param {Number} params.limit 获取数量，默认10
 * @param {String} params.timeRange 时间范围：week, month, year, all
 */
export function getInvitesRanking(params) {
  return request({
    url: '/api/admin/promotion/invites-ranking',
    method: 'get',
    params
  });
}

/**
 * 获取推广员的推广用户列表
 * @param {String|Number} promoterId 推广员ID
 * @param {Number} level 推广层级：1-一级, 2-二级
 * @param {Object} params 查询参数
 */
export function getPromoterInvites(promoterId, level = 1, params = {}) {
  return request({
    url: `/api/admin/promoters/${promoterId}/invites`,
    method: 'get',
    params: { level, ...params }
  });
}

/**
 * 获取推广员的佣金记录
 * @param {String|Number} promoterId 推广员ID
 * @param {Object} params 查询参数
 */
export function getPromoterCommissions(promoterId, params = {}) {
  return request({
    url: `/api/admin/promoters/${promoterId}/commissions`,
    method: 'get',
    params
  });
}

/**
 * 获取推广员的提现记录
 * @param {String|Number} promoterId 推广员ID
 * @param {Object} params 查询参数
 */
export function getPromoterWithdrawals(promoterId, params = {}) {
  return request({
    url: `/api/admin/promoters/${promoterId}/withdrawals`,
    method: 'get',
    params
  });
}

export default {
  getPromotionOverview,
  getPromotionTrend,
  getPromoterLevelDistribution,
  getCommissionRanking,
  getInvitesRanking,
  getPromoterInvites,
  getPromoterCommissions,
  getPromoterWithdrawals
}; 