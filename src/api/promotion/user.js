import request from '@/utils/request';

/**
 * 获取推广员列表
 * @param {Object} params 查询参数
 * @param {Number} params.page 页码
 * @param {Number} params.limit 每页数量
 * @param {String} params.search 搜索关键词
 * @param {Number} params.status 状态过滤
 * @param {Number} params.level 等级过滤
 */
export function getPromoters(params) {
  return request({
    url: '/api/admin/promoters',
    method: 'get',
    params
  });
}

/**
 * 获取推广员详情
 * @param {String} id 推广员ID
 */
export function getPromoterDetail(id) {
  return request({
    url: `/api/admin/promoters/${id}`,
    method: 'get'
  });
}

/**
 * 更新推广员状态
 * @param {String} id 推广员ID
 * @param {Number} status 状态：0-禁用，1-启用
 */
export function updatePromoterStatus(id, status) {
  return request({
    url: `/api/admin/promoters/${id}/status`,
    method: 'put',
    data: { status }
  });
}

/**
 * 更新推广员等级
 * @param {String} id 推广员ID
 * @param {Number} level 等级：1-初级，2-中级，3-高级，4-资深
 * @param {String} remark 备注
 */
export function updatePromoterLevel(id, level, remark) {
  return request({
    url: `/api/admin/promoters/${id}/level`,
    method: 'put',
    data: { level, remark }
  });
}

/**
 * 更新推广员备注
 * @param {String} id 推广员ID
 * @param {String} remark 备注
 */
export function updatePromoterRemark(id, remark) {
  return request({
    url: `/api/admin/promoters/${id}/remark`,
    method: 'put',
    data: { remark }
  });
}

/**
 * 获取推广员申请列表
 * @param {Object} params 查询参数
 * @param {Number} params.page 页码
 * @param {Number} params.limit 每页数量
 * @param {String} params.search 搜索关键词
 * @param {Number} params.status 状态过滤
 */
export function getPromoterApplications(params) {
  return request({
    url: '/api/admin/promoter-applications',
    method: 'get',
    params
  });
}

/**
 * 审核推广员申请
 * @param {String} id 申请ID
 * @param {Number} status 状态：1-通过，2-拒绝
 * @param {String} remark 备注
 */
export function auditPromoterApplication(id, status, remark) {
  return request({
    url: `/api/admin/promoter-applications/${id}/audit`,
    method: 'put',
    data: { status, remark }
  });
}

export default {
  getPromoters,
  getPromoterDetail,
  updatePromoterStatus,
  updatePromoterLevel,
  updatePromoterRemark,
  getPromoterApplications,
  auditPromoterApplication
}; 