import request from '@/utils/request';

/**
 * 获取提现列表
 * @param {Object} params 查询参数
 * @param {Number} params.page 页码
 * @param {Number} params.limit 每页数量
 * @param {String} params.search 搜索关键词
 * @param {Number} params.status 状态过滤
 * @param {String} params.startDate 开始日期
 * @param {String} params.endDate 结束日期
 */
export function getWithdrawals(params) {
  return request({
    url: '/api/admin/withdrawals',
    method: 'get',
    params
  });
}

/**
 * 获取提现详情
 * @param {String} id 提现申请ID
 */
export function getWithdrawalDetail(id) {
  return request({
    url: `/api/admin/withdrawals/${id}`,
    method: 'get'
  });
}

/**
 * 获取提现统计数据
 */
export function getWithdrawalStatistics() {
  return request({
    url: '/api/admin/withdrawals/statistics',
    method: 'get'
  });
}

/**
 * 更新提现状态
 * @param {String} id 提现申请ID
 * @param {Number} status 状态：1-通过，2-拒绝，3-已打款
 * @param {String} remark 备注
 * @param {String} proof 打款凭证(status=3时需要)
 */
export function updateWithdrawalStatus(id, status, remark, proof) {
  const data = { status, remark };
  if (proof) {
    data.proof = proof;
  }
  
  return request({
    url: `/api/admin/withdrawals/${id}/status`,
    method: 'put',
    data
  });
}

export default {
  getWithdrawals,
  getWithdrawalDetail,
  getWithdrawalStatistics,
  updateWithdrawalStatus
}; 