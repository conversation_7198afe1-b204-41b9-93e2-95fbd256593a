/**
 * 小程序API调用接口
 * 用于在后台管理系统中调用小程序的API
 */
import request from '@/utils/request';
import { miniprogramApiProxy } from './shared/service';

/**
 * 获取小程序用户数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniUsers(params) {
  return miniprogramApiProxy.invoke('user', 'getList', params);
}

/**
 * 获取小程序用户详情
 * @param {String} id - 用户ID
 * @returns {Promise}
 */
export function getMiniUserDetail(id) {
  return miniprogramApiProxy.getData('user', id);
}

/**
 * 获取小程序订单数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniOrders(params) {
  return miniprogramApiProxy.invoke('order', 'getList', params);
}

/**
 * 获取小程序订单详情
 * @param {String} id - 订单ID
 * @returns {Promise}
 */
export function getMiniOrderDetail(id) {
  return miniprogramApiProxy.getData('order', id);
}

/**
 * 获取小程序驿站数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniStations(params) {
  return miniprogramApiProxy.invoke('station', 'getList', params);
}

/**
 * 获取小程序驿站详情
 * @param {String} id - 驿站ID
 * @returns {Promise}
 */
export function getMiniStationDetail(id) {
  return miniprogramApiProxy.getData('station', id);
}

/**
 * 获取小程序推广码数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniPromotionCodes(params) {
  return miniprogramApiProxy.invoke('promotion', 'getCodeList', params);
}

/**
 * 获取小程序推广佣金数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniCommissions(params) {
  return miniprogramApiProxy.invoke('promotion', 'getCommissionList', params);
}

/**
 * 获取小程序数据统计
 * @param {String} module - 模块名称
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniStatistics(module, params) {
  return miniprogramApiProxy.invoke('statistics', 'getStats', { module, ...params });
}

/**
 * 发送小程序消息通知
 * @param {Object} data - 消息数据
 * @returns {Promise}
 */
export function sendMiniNotification(data) {
  return miniprogramApiProxy.invoke('notification', 'send', data);
}

/**
 * 同步数据到小程序
 * @param {String} module - 模块名称
 * @param {Object} data - 数据
 * @returns {Promise}
 */
export function syncToMiniprogram(module, data) {
  return request({
    url: `/api/v1/sync/${module}/push`,
    method: 'post',
    data
  });
}

/**
 * 获取小程序实时日志
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniLogs(params) {
  return miniprogramApiProxy.invoke('system', 'getLogs', params);
}

/**
 * 获取小程序版本信息
 * @returns {Promise}
 */
export function getMiniVersion() {
  return miniprogramApiProxy.invoke('system', 'getVersion');
}

/**
 * 获取小程序用户反馈
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMiniFeedback(params) {
  return miniprogramApiProxy.invoke('feedback', 'getList', params);
}

/**
 * 回复小程序用户反馈
 * @param {String} id - 反馈ID
 * @param {String} content - 回复内容
 * @returns {Promise}
 */
export function replyMiniFeedback(id, content) {
  return miniprogramApiProxy.invoke('feedback', 'reply', { id, content });
} 