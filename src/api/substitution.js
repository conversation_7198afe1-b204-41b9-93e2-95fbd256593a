import request from '@/utils/request'

// 查询顶班列表
export function listSubstitution(query) {
  return request({
    url: '/substitution/list',
    method: 'get',
    params: query
  })
}

// 查询顶班详细信息
export function getSubstitution(id) {
  return request({
    url: `/substitution/${id}`,
    method: 'get'
  })
}

// 新增顶班
export function addSubstitution(data) {
  return request({
    url: '/substitution',
    method: 'post',
    data: data
  })
}

// 修改顶班
export function updateSubstitution(data) {
  return request({
    url: `/substitution/${data.id}`,
    method: 'put',
    data: data
  })
}

// 删除顶班
export function delSubstitution(id) {
  return request({
    url: `/substitution/${id}`,
    method: 'delete'
  })
}

// 审核通过顶班申请
export function approveSubstitution(id) {
  return request({
    url: `/substitution/approve/${id}`,
    method: 'put'
  })
}

// 审核拒绝顶班申请
export function rejectSubstitution(id, data) {
  return request({
    url: `/substitution/reject/${id}`,
    method: 'put',
    data: data
  })
}

// 标记顶班服务完成
export function completeSubstitution(id) {
  return request({
    url: `/substitution/complete/${id}`,
    method: 'put'
  })
}

// 导出顶班数据
export function exportSubstitution(query) {
  return request({
    url: '/substitution/export',
    method: 'get',
    params: query,
    responseType: 'blob'
  })
}

// 分配顶班人员
export function assignStaff(id, data) {
  return request({
    url: `/substitution/assign/${id}`,
    method: 'put',
    data: data
  })
}

// 添加顶班服务评价
export function addEvaluation(id, data) {
  return request({
    url: `/substitution/evaluation/${id}`,
    method: 'post',
    data: data
  })
}

// 回复顶班服务评价
export function replyEvaluation(substitutionId, evaluationId, data) {
  return request({
    url: `/substitution/evaluation/${substitutionId}/${evaluationId}/reply`,
    method: 'put',
    data: data
  })
}

// 获取顶班统计数据
export function getSubstitutionStatistics(params) {
  return request({
    url: '/substitution/statistics',
    method: 'get',
    params: params
  })
}

// 获取顶班人员列表
export function getSubstitutionStaffList(query) {
  return request({
    url: '/substitution/staff/list',
    method: 'get',
    params: query
  })
}

// 获取顶班人员详情
export function getSubstitutionStaff(id) {
  return request({
    url: `/substitution/staff/${id}`,
    method: 'get'
  })
}

// 新增顶班人员
export function addSubstitutionStaff(data) {
  return request({
    url: '/substitution/staff',
    method: 'post',
    data: data
  })
}

// 更新顶班人员
export function updateSubstitutionStaff(data) {
  return request({
    url: `/substitution/staff/${data.id}`,
    method: 'put',
    data: data
  })
}

// 删除顶班人员
export function delSubstitutionStaff(id) {
  return request({
    url: `/substitution/staff/${id}`,
    method: 'delete'
  })
}

// 审核顶班人员资质
export function verifyStaffQualification(id, data) {
  return request({
    url: `/substitution/staff/verify/${id}`,
    method: 'put',
    data: data
  })
}

// 获取顶班服务匹配推荐
export function getSubstitutionMatches(id) {
  return request({
    url: `/substitution/match/${id}`,
    method: 'get'
  })
}

// 配置顶班服务匹配算法参数
export function configureMatchingAlgorithm(data) {
  return request({
    url: '/substitution/match/config',
    method: 'put',
    data: data
  })
} 