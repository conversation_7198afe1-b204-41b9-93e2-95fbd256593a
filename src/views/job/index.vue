<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="filter-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
        <el-form-item label="岗位名称" prop="jobName">
          <el-input v-model="queryParams.jobName" placeholder="请输入岗位名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="驿站" prop="stationId">
          <el-select v-model="queryParams.stationId" placeholder="请选择驿站" clearable>
            <el-option v-for="dict in stationOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="岗位类型" prop="jobType">
          <el-select v-model="queryParams.jobType" placeholder="请选择岗位类型" clearable>
            <el-option v-for="dict in jobTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-container">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
        </el-col>
      </el-row>

      <!-- 岗位列表 -->
      <el-table v-loading="loading" :data="jobList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="岗位ID" align="center" prop="jobId" />
        <el-table-column label="岗位名称" align="center" prop="jobName" min-width="150" show-overflow-tooltip />
        <el-table-column label="所属驿站" align="center" prop="stationName" min-width="150" show-overflow-tooltip />
        <el-table-column label="岗位类型" align="center" prop="jobType">
          <template #default="scope">
            <el-tag :type="getJobTypeTag(scope.row.jobType)">{{ scope.row.jobTypeName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="薪资" align="center" prop="salary" />
        <el-table-column label="工作时间" align="center" prop="workTime" min-width="120" />
        <el-table-column label="招聘人数" align="center" prop="recruitNum" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="发布时间" align="center" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="total > 0"
        :total="total"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 添加或修改岗位对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="700px" append-to-body>
      <el-form ref="jobForm" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="所属驿站" prop="stationId">
          <el-select v-model="form.stationId" placeholder="请选择所属驿站">
            <el-option v-for="dict in stationOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="岗位名称" prop="jobName">
          <el-input v-model="form.jobName" placeholder="请输入岗位名称" />
        </el-form-item>
        <el-form-item label="岗位类型" prop="jobType">
          <el-select v-model="form.jobType" placeholder="请选择岗位类型">
            <el-option v-for="dict in jobTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="薪资" prop="salary">
              <el-input v-model="form.salary" placeholder="请输入薪资" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="招聘人数" prop="recruitNum">
              <el-input-number v-model="form.recruitNum" :min="1" :max="999" placeholder="请输入招聘人数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="工作时间" prop="workTime">
          <el-input v-model="form.workTime" placeholder="请输入工作时间，例如：周一至周五 9:00-18:00" />
        </el-form-item>
        <el-form-item label="工作地点" prop="workPlace">
          <el-input v-model="form.workPlace" placeholder="请输入工作地点" />
        </el-form-item>
        <el-form-item label="岗位要求" prop="requirement">
          <el-input v-model="form.requirement" type="textarea" :rows="3" placeholder="请输入岗位要求" />
        </el-form-item>
        <el-form-item label="岗位福利" prop="benefit">
          <el-input v-model="form.benefit" type="textarea" :rows="3" placeholder="请输入岗位福利" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="form.contact" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">上线</el-radio>
            <el-radio :label="0">下线</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看岗位详情对话框 -->
    <el-dialog title="岗位详情" v-model="viewDialog.visible" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="岗位名称">{{ viewInfo.jobName }}</el-descriptions-item>
        <el-descriptions-item label="所属驿站">{{ viewInfo.stationName }}</el-descriptions-item>
        <el-descriptions-item label="岗位类型">{{ viewInfo.jobTypeName }}</el-descriptions-item>
        <el-descriptions-item label="薪资">{{ viewInfo.salary }}</el-descriptions-item>
        <el-descriptions-item label="招聘人数">{{ viewInfo.recruitNum }}</el-descriptions-item>
        <el-descriptions-item label="工作时间">{{ viewInfo.workTime }}</el-descriptions-item>
        <el-descriptions-item label="工作地点" :span="2">{{ viewInfo.workPlace }}</el-descriptions-item>
        <el-descriptions-item label="岗位要求" :span="2">{{ viewInfo.requirement }}</el-descriptions-item>
        <el-descriptions-item label="岗位福利" :span="2">{{ viewInfo.benefit }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{ viewInfo.contact }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ viewInfo.status === 1 ? '招聘中' : '已下线' }}</el-descriptions-item>
        <el-descriptions-item label="发布时间">{{ viewInfo.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ viewInfo.updateTime }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewInfo.remark }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 后续将导入API
// import { listJob, addJob, updateJob, deleteJob, exportJob, changeJobStatus } from '@/api/job'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  jobName: '',
  stationId: '',
  jobType: '',
  status: '',
  dateRange: []
})

// 表格数据
const loading = ref(false)
const jobList = ref([])
const total = ref(0)
const ids = ref([])
const single = computed(() => ids.value.length !== 1)
const multiple = computed(() => ids.value.length === 0)

// 选项数据
const stationOptions = [
  { value: '1', label: '海淀创业园驿站' },
  { value: '2', label: '朝阳小区驿站' },
  { value: '3', label: '张三个人驿站' }
]
const jobTypeOptions = [
  { value: '1', label: '全职' },
  { value: '2', label: '兼职' },
  { value: '3', label: '实习' },
  { value: '4', label: '临时工' }
]
const statusOptions = [
  { value: '1', label: '招聘中' },
  { value: '0', label: '已下线' }
]

// 弹窗参数
const dialog = reactive({
  visible: false,
  title: '',
  type: ''
})
const viewDialog = reactive({
  visible: false
})

// 表单参数
const jobForm = ref(null)
const form = reactive({
  jobId: null,
  stationId: '',
  stationName: '',
  jobName: '',
  jobType: '',
  salary: '',
  recruitNum: 1,
  workTime: '',
  workPlace: '',
  requirement: '',
  benefit: '',
  contact: '',
  status: 1,
  remark: ''
})
const viewInfo = reactive({})

// 表单校验规则
const rules = reactive({
  stationId: [
    { required: true, message: '请选择所属驿站', trigger: 'change' }
  ],
  jobName: [
    { required: true, message: '岗位名称不能为空', trigger: 'blur' }
  ],
  jobType: [
    { required: true, message: '岗位类型不能为空', trigger: 'change' }
  ],
  salary: [
    { required: true, message: '薪资不能为空', trigger: 'blur' }
  ],
  recruitNum: [
    { required: true, message: '招聘人数不能为空', trigger: 'blur' }
  ],
  workTime: [
    { required: true, message: '工作时间不能为空', trigger: 'blur' }
  ],
  workPlace: [
    { required: true, message: '工作地点不能为空', trigger: 'blur' }
  ],
  requirement: [
    { required: true, message: '岗位要求不能为空', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '联系方式不能为空', trigger: 'blur' }
  ]
})

// 岗位类型标签
const getJobTypeTag = (type) => {
  switch (type) {
    case '1':
      return 'primary'
    case '2':
      return 'success'
    case '3':
      return 'warning'
    case '4':
      return 'info'
    default:
      return ''
  }
}

// 生命周期钩子
onMounted(() => {
  getList()
})

// 获取岗位列表
const getList = () => {
  loading.value = true
  // 测试数据，后续替换为API调用
  // listJob(queryParams).then(response => {
  //   jobList.value = response.rows
  //   total.value = response.total
  //   loading.value = false
  // })
  
  // 模拟数据
  setTimeout(() => {
    jobList.value = [
      {
        jobId: 1,
        stationId: '1',
        stationName: '海淀创业园驿站',
        jobName: '驿站管理员',
        jobType: '1',
        jobTypeName: '全职',
        salary: '5000-7000元/月',
        recruitNum: 2,
        workTime: '周一至周五 9:00-18:00',
        workPlace: '北京市海淀区西二旗大街58号',
        requirement: '1. 高中及以上学历；\n2. 熟悉电脑操作，有相关工作经验者优先；\n3. 做事认真负责，有良好的沟通能力。',
        benefit: '1. 五险一金；\n2. 节假日福利；\n3. 带薪年假；\n4. 员工团建。',
        contact: '张经理 13812345678',
        status: 1,
        createTime: '2023-05-15 10:30:00',
        updateTime: '2023-05-15 10:30:00',
        remark: '长期稳定工作'
      },
      {
        jobId: 2,
        stationId: '2',
        stationName: '朝阳小区驿站',
        jobName: '快递分拣员',
        jobType: '2',
        jobTypeName: '兼职',
        salary: '20元/小时',
        recruitNum: 5,
        workTime: '周一至周日 18:00-22:00',
        workPlace: '北京市朝阳区建国路89号',
        requirement: '1. 身体健康，能够搬运快递；\n2. 责任心强，有耐心；\n3. 工作认真仔细。',
        benefit: '1. 薪资日结；\n2. 工作时间灵活；\n3. 表现优秀可转正。',
        contact: '李经理 13987654321',
        status: 1,
        createTime: '2023-05-10 14:20:00',
        updateTime: '2023-05-10 14:20:00',
        remark: '适合在校大学生'
      },
      {
        jobId: 3,
        stationId: '3',
        stationName: '张三个人驿站',
        jobName: '驿站助理',
        jobType: '4',
        jobTypeName: '临时工',
        salary: '150元/天',
        recruitNum: 1,
        workTime: '周六至周日 9:00-17:00',
        workPlace: '上海市浦东新区张杨路500号',
        requirement: '1. 有责任心；\n2. 能够吃苦耐劳；\n3. 有相关经验优先。',
        benefit: '1. 薪资日结；\n2. 提供工作餐。',
        contact: '张先生 13598765432',
        status: 0,
        createTime: '2023-05-05 09:15:00',
        updateTime: '2023-05-05 09:15:00',
        remark: '临时招聘，周末两天'
      }
    ]
    total.value = 3
    loading.value = false
  }, 500)
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.jobName = ''
  queryParams.stationId = ''
  queryParams.jobType = ''
  queryParams.status = ''
  queryParams.dateRange = []
  handleQuery()
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.jobId)
}

// 新增按钮操作
const handleAdd = () => {
  reset()
  dialog.visible = true
  dialog.title = '添加岗位'
  dialog.type = 'add'
}

// 修改按钮操作
const handleUpdate = (row) => {
  reset()
  const jobId = row.jobId || ids.value[0]
  // 后续替换为API调用获取详情
  // getJob(jobId).then(response => {
  //   Object.assign(form, response.data)
  //   dialog.visible = true
  //   dialog.title = '修改岗位'
  //   dialog.type = 'edit'
  // })
  
  // 模拟数据
  const selectedRow = jobList.value.find(item => item.jobId === jobId)
  if (selectedRow) {
    Object.assign(form, selectedRow)
    dialog.visible = true
    dialog.title = '修改岗位'
    dialog.type = 'edit'
  }
}

// 查看详情操作
const handleView = (row) => {
  Object.assign(viewInfo, row)
  viewDialog.visible = true
}

// 提交表单
const submitForm = () => {
  jobForm.value.validate(valid => {
    if (valid) {
      // 获取驿站名称
      const station = stationOptions.find(item => item.value === form.stationId)
      form.stationName = station ? station.label : ''
      
      if (form.jobId) {
        // updateJob(form).then(response => {
        //   ElMessage.success('修改成功')
        //   dialog.visible = false
        //   getList()
        // })
        ElMessage.success('修改成功')
        dialog.visible = false
        getList()
      } else {
        // addJob(form).then(response => {
        //   ElMessage.success('新增成功')
        //   dialog.visible = false
        //   getList()
        // })
        ElMessage.success('新增成功')
        dialog.visible = false
        getList()
      }
    }
  })
}

// 删除按钮操作
const handleDelete = (row) => {
  const jobIds = row.jobId || ids.value
  ElMessageBox.confirm('是否确认删除所选岗位?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // deleteJob(jobIds).then(() => {
    //   ElMessage.success('删除成功')
    //   getList()
    // })
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {})
}

// 导出按钮操作
const handleExport = () => {
  // exportJob(queryParams).then(response => {
  //   // 导出处理
  // })
  ElMessage.success('导出成功')
}

// 状态修改
const handleStatusChange = (row) => {
  // changeJobStatus({ jobId: row.jobId, status: row.status }).then(() => {
  //   ElMessage.success(`${row.status === 1 ? '上线' : '下线'}成功`)
  // }).catch(() => {
  //   row.status = row.status === 1 ? 0 : 1
  // })
  ElMessage.success(`${row.status === 1 ? '上线' : '下线'}成功`)
}

// 表单重置
const reset = () => {
  form.jobId = null
  form.stationId = ''
  form.stationName = ''
  form.jobName = ''
  form.jobType = ''
  form.salary = ''
  form.recruitNum = 1
  form.workTime = ''
  form.workPlace = ''
  form.requirement = ''
  form.benefit = ''
  form.contact = ''
  form.status = 1
  form.remark = ''
}

// 取消按钮
const cancel = () => {
  dialog.visible = false
  reset()
}

// 分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.mb8 {
  margin-bottom: 8px;
}
</style> 