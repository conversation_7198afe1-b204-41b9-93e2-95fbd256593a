<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-title">驿站帮Pro管理系统</div>
      <div class="form-container">
        <div class="form-item">
          <label>用户名</label>
          <el-input v-model="loginForm.username" placeholder="请输入用户名" prefix-icon="el-icon-user"></el-input>
        </div>
        <div class="form-item">
          <label>密码</label>
          <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" prefix-icon="el-icon-lock"></el-input>
        </div>
        <div class="form-item">
          <el-checkbox v-model="loginForm.rememberMe">记住我</el-checkbox>
        </div>
        <div class="form-item">
          <el-button type="primary" class="login-button" @click="handleLogin">登录</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        username: 'admin',
        password: 'admin123',
        rememberMe: false
      }
    }
  },
  methods: {
    handleLogin() {
      this.$router.push({ path: '/' })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  height: 100%;
  background-color: #2c3e50;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .login-box {
    width: 400px;
    padding: 30px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 25px rgba(0, 0, 0, 0.3);
    
    .login-title {
      text-align: center;
      font-size: 24px;
      color: #303133;
      margin-bottom: 30px;
    }
    
    .form-container {
      .form-item {
        margin-bottom: 20px;
        
        label {
          display: block;
          margin-bottom: 5px;
          color: #606266;
          font-size: 14px;
        }
      }
      
      .login-button {
        width: 100%;
      }
    }
  }
}
</style> 