<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="filter-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
        <el-form-item label="设备名称" prop="equipmentName">
          <el-input v-model="queryParams.equipmentName" placeholder="请输入设备名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="设备类型" prop="equipmentType">
          <el-select v-model="queryParams.equipmentType" placeholder="请选择设备类型" clearable>
            <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="驿站" prop="stationId">
          <el-select v-model="queryParams.stationId" placeholder="请选择驿站" clearable>
            <el-option v-for="dict in stationOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-container">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
        </el-col>
      </el-row>

      <!-- 设备列表 -->
      <el-table v-loading="loading" :data="equipmentList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="设备ID" align="center" prop="equipmentId" />
        <el-table-column label="设备名称" align="center" prop="equipmentName" min-width="150" show-overflow-tooltip />
        <el-table-column label="设备编号" align="center" prop="equipmentCode" min-width="120" show-overflow-tooltip />
        <el-table-column label="设备类型" align="center" prop="equipmentType">
          <template #default="scope">
            <el-tag :type="getEquipTypeTag(scope.row.equipmentType)">{{ scope.row.equipmentTypeName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="所属驿站" align="center" prop="stationName" min-width="150" show-overflow-tooltip />
        <el-table-column label="设备状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="getStatusTag(scope.row.status)">{{ scope.row.statusName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="租赁价格" align="center" prop="rentPrice" />
        <el-table-column label="购入日期" align="center" prop="purchaseDate" width="110" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="total > 0"
        :total="total"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 添加或修改设备对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="700px" append-to-body>
      <el-form ref="equipmentForm" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="equipmentName">
              <el-input v-model="form.equipmentName" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备编号" prop="equipmentCode">
              <el-input v-model="form.equipmentCode" placeholder="请输入设备编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="equipmentType">
              <el-select v-model="form.equipmentType" placeholder="请选择设备类型">
                <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属驿站" prop="stationId">
              <el-select v-model="form.stationId" placeholder="请选择所属驿站">
                <el-option v-for="dict in stationOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择设备状态">
                <el-option v-for="dict in equipStatusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="租赁价格" prop="rentPrice">
              <el-input v-model="form.rentPrice" placeholder="请输入租赁价格" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="购入日期" prop="purchaseDate">
              <el-date-picker
                v-model="form.purchaseDate"
                type="date"
                placeholder="请选择购入日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保修期至" prop="warrantyDate">
              <el-date-picker
                v-model="form.warrantyDate"
                type="date"
                placeholder="请选择保修期截止日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="设备规格" prop="specification">
          <el-input v-model="form.specification" placeholder="请输入设备规格" />
        </el-form-item>
        <el-form-item label="厂商信息" prop="manufacturer">
          <el-input v-model="form.manufacturer" placeholder="请输入厂商信息" />
        </el-form-item>
        <el-form-item label="设备图片" prop="image">
          <el-upload
            class="avatar-uploader"
            action="/api/common/upload"
            :show-file-list="false"
            :on-success="handleImageSuccess"
          >
            <img v-if="form.image" :src="form.image" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="设备描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入设备描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看设备详情对话框 -->
    <el-dialog title="设备详情" v-model="viewDialog.visible" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="设备名称">{{ viewInfo.equipmentName }}</el-descriptions-item>
        <el-descriptions-item label="设备编号">{{ viewInfo.equipmentCode }}</el-descriptions-item>
        <el-descriptions-item label="设备类型">{{ viewInfo.equipmentTypeName }}</el-descriptions-item>
        <el-descriptions-item label="所属驿站">{{ viewInfo.stationName }}</el-descriptions-item>
        <el-descriptions-item label="设备状态">
          <el-tag :type="getStatusTag(viewInfo.status)">{{ viewInfo.statusName }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="租赁价格">{{ viewInfo.rentPrice }}</el-descriptions-item>
        <el-descriptions-item label="购入日期">{{ viewInfo.purchaseDate }}</el-descriptions-item>
        <el-descriptions-item label="保修期至">{{ viewInfo.warrantyDate }}</el-descriptions-item>
        <el-descriptions-item label="设备规格" :span="2">{{ viewInfo.specification }}</el-descriptions-item>
        <el-descriptions-item label="厂商信息" :span="2">{{ viewInfo.manufacturer }}</el-descriptions-item>
        <el-descriptions-item label="设备描述" :span="2">{{ viewInfo.description }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewInfo.remark }}</el-descriptions-item>
        <el-descriptions-item label="设备图片" :span="2">
          <img v-if="viewInfo.image" :src="viewInfo.image" class="preview-image" />
          <span v-else>暂无图片</span>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 后续将导入API
// import { listEquipment, addEquipment, updateEquipment, deleteEquipment, exportEquipment } from '@/api/equipment'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  equipmentName: '',
  equipmentType: '',
  stationId: '',
  status: ''
})

// 表格数据
const loading = ref(false)
const equipmentList = ref([])
const total = ref(0)
const ids = ref([])
const single = computed(() => ids.value.length !== 1)
const multiple = computed(() => ids.value.length === 0)

// 选项数据
const typeOptions = [
  { value: '1', label: '分拣设备' },
  { value: '2', label: '存储设备' },
  { value: '3', label: '运输设备' },
  { value: '4', label: '安全设备' },
  { value: '5', label: '办公设备' }
]
const stationOptions = [
  { value: '1', label: '海淀创业园驿站' },
  { value: '2', label: '朝阳小区驿站' },
  { value: '3', label: '张三个人驿站' }
]
const statusOptions = [
  { value: '1', label: '可用' },
  { value: '2', label: '租赁中' },
  { value: '3', label: '维修中' },
  { value: '4', label: '已报废' }
]
const equipStatusOptions = [
  { value: 1, label: '可用' },
  { value: 2, label: '租赁中' },
  { value: 3, label: '维修中' },
  { value: 4, label: '已报废' }
]

// 弹窗参数
const dialog = reactive({
  visible: false,
  title: '',
  type: ''
})
const viewDialog = reactive({
  visible: false
})

// 表单参数
const equipmentForm = ref(null)
const form = reactive({
  equipmentId: null,
  equipmentName: '',
  equipmentCode: '',
  equipmentType: '',
  stationId: '',
  stationName: '',
  status: 1,
  statusName: '',
  rentPrice: '',
  purchaseDate: '',
  warrantyDate: '',
  specification: '',
  manufacturer: '',
  description: '',
  image: '',
  remark: ''
})
const viewInfo = reactive({})

// 表单校验规则
const rules = reactive({
  equipmentName: [
    { required: true, message: '设备名称不能为空', trigger: 'blur' }
  ],
  equipmentCode: [
    { required: true, message: '设备编号不能为空', trigger: 'blur' }
  ],
  equipmentType: [
    { required: true, message: '设备类型不能为空', trigger: 'change' }
  ],
  stationId: [
    { required: true, message: '所属驿站不能为空', trigger: 'change' }
  ],
  status: [
    { required: true, message: '设备状态不能为空', trigger: 'change' }
  ],
  rentPrice: [
    { required: true, message: '租赁价格不能为空', trigger: 'blur' },
    { pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '租赁价格必须为数字，最多两位小数', trigger: 'blur' }
  ],
  purchaseDate: [
    { required: true, message: '购入日期不能为空', trigger: 'change' }
  ],
  specification: [
    { required: true, message: '设备规格不能为空', trigger: 'blur' }
  ],
  manufacturer: [
    { required: true, message: '厂商信息不能为空', trigger: 'blur' }
  ]
})

// 获取设备类型标签
const getEquipTypeTag = (type) => {
  switch (type) {
    case '1':
      return 'primary'
    case '2':
      return 'success'
    case '3':
      return 'warning'
    case '4':
      return 'danger'
    case '5':
      return 'info'
    default:
      return ''
  }
}

// 获取状态标签
const getStatusTag = (status) => {
  switch (status) {
    case 1:
      return 'success'
    case 2:
      return 'warning'
    case 3:
      return 'danger'
    case 4:
      return 'info'
    default:
      return ''
  }
}

// 生命周期钩子
onMounted(() => {
  getList()
})

// 获取设备列表
const getList = () => {
  loading.value = true
  // 测试数据，后续替换为API调用
  // listEquipment(queryParams).then(response => {
  //   equipmentList.value = response.rows
  //   total.value = response.total
  //   loading.value = false
  // })
  
  // 模拟数据
  setTimeout(() => {
    equipmentList.value = [
      {
        equipmentId: 1,
        equipmentName: '自动分拣机',
        equipmentCode: 'FJ20230515001',
        equipmentType: '1',
        equipmentTypeName: '分拣设备',
        stationId: '1',
        stationName: '海淀创业园驿站',
        status: 1,
        statusName: '可用',
        rentPrice: '200.00',
        purchaseDate: '2023-01-15',
        warrantyDate: '2025-01-15',
        specification: '中型自动分拣设备，处理能力500件/小时',
        manufacturer: '北京快捷科技有限公司',
        description: '专业快递分拣设备，效率高，准确率高',
        image: 'https://example.com/equipment1.jpg',
        remark: '重点维护设备'
      },
      {
        equipmentId: 2,
        equipmentName: '智能存储柜',
        equipmentCode: 'CC20230510002',
        equipmentType: '2',
        equipmentTypeName: '存储设备',
        stationId: '2',
        stationName: '朝阳小区驿站',
        status: 2,
        statusName: '租赁中',
        rentPrice: '150.00',
        purchaseDate: '2023-02-10',
        warrantyDate: '2025-02-10',
        specification: '智能存储柜，容量100格',
        manufacturer: '上海聪明科技有限公司',
        description: '智能化存储设备，支持二维码取件，远程控制',
        image: 'https://example.com/equipment2.jpg',
        remark: '租赁给朝阳小区居委会'
      },
      {
        equipmentId: 3,
        equipmentName: '电动三轮车',
        equipmentCode: 'YS20230505003',
        equipmentType: '3',
        equipmentTypeName: '运输设备',
        stationId: '3',
        stationName: '张三个人驿站',
        status: 3,
        statusName: '维修中',
        rentPrice: '50.00',
        purchaseDate: '2023-03-05',
        warrantyDate: '2024-03-05',
        specification: '电动三轮车，载重200kg',
        manufacturer: '天津运输车辆制造厂',
        description: '适用于短距离快递配送的电动三轮车',
        image: 'https://example.com/equipment3.jpg',
        remark: '电机故障，正在维修'
      }
    ]
    total.value = 3
    loading.value = false
  }, 500)
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.equipmentName = ''
  queryParams.equipmentType = ''
  queryParams.stationId = ''
  queryParams.status = ''
  handleQuery()
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.equipmentId)
}

// 新增按钮操作
const handleAdd = () => {
  reset()
  dialog.visible = true
  dialog.title = '添加设备'
  dialog.type = 'add'
}

// 修改按钮操作
const handleUpdate = (row) => {
  reset()
  const equipmentId = row.equipmentId || ids.value[0]
  // 后续替换为API调用获取详情
  // getEquipment(equipmentId).then(response => {
  //   Object.assign(form, response.data)
  //   dialog.visible = true
  //   dialog.title = '修改设备'
  //   dialog.type = 'edit'
  // })
  
  // 模拟数据
  const selectedRow = equipmentList.value.find(item => item.equipmentId === equipmentId)
  if (selectedRow) {
    Object.assign(form, selectedRow)
    dialog.visible = true
    dialog.title = '修改设备'
    dialog.type = 'edit'
  }
}

// 查看详情操作
const handleView = (row) => {
  Object.assign(viewInfo, row)
  viewDialog.visible = true
}

// 提交表单
const submitForm = () => {
  equipmentForm.value.validate(valid => {
    if (valid) {
      // 获取驿站名称和状态名称
      const station = stationOptions.find(item => item.value === form.stationId)
      form.stationName = station ? station.label : ''
      
      const statusObj = equipStatusOptions.find(item => item.value === form.status)
      form.statusName = statusObj ? statusObj.label : ''
      
      if (form.equipmentId) {
        // updateEquipment(form).then(response => {
        //   ElMessage.success('修改成功')
        //   dialog.visible = false
        //   getList()
        // })
        ElMessage.success('修改成功')
        dialog.visible = false
        getList()
      } else {
        // addEquipment(form).then(response => {
        //   ElMessage.success('新增成功')
        //   dialog.visible = false
        //   getList()
        // })
        ElMessage.success('新增成功')
        dialog.visible = false
        getList()
      }
    }
  })
}

// 删除按钮操作
const handleDelete = (row) => {
  const equipmentIds = row.equipmentId || ids.value
  ElMessageBox.confirm('是否确认删除所选设备?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // deleteEquipment(equipmentIds).then(() => {
    //   ElMessage.success('删除成功')
    //   getList()
    // })
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {})
}

// 导出按钮操作
const handleExport = () => {
  // exportEquipment(queryParams).then(response => {
  //   // 导出处理
  // })
  ElMessage.success('导出成功')
}

// 图片上传成功处理
const handleImageSuccess = (response) => {
  form.image = response.data
}

// 表单重置
const reset = () => {
  form.equipmentId = null
  form.equipmentName = ''
  form.equipmentCode = ''
  form.equipmentType = ''
  form.stationId = ''
  form.stationName = ''
  form.status = 1
  form.statusName = ''
  form.rentPrice = ''
  form.purchaseDate = ''
  form.warrantyDate = ''
  form.specification = ''
  form.manufacturer = ''
  form.description = ''
  form.image = ''
  form.remark = ''
}

// 取消按钮
const cancel = () => {
  dialog.visible = false
  reset()
}

// 分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    
    &:hover {
      border-color: var(--el-color-primary);
    }
  }
  
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
    line-height: 178px;
  }
  
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}

.preview-image {
  max-width: 100%;
  max-height: 300px;
}
</style> 