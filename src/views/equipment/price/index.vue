<template>
  <div class="page-container">
    <el-row :gutter="20">
      <!-- 左侧设备分类树 -->
      <el-col :span="6">
        <el-card class="category-card">
          <template #header>
            <div class="card-header">
              <span>设备分类</span>
            </div>
          </template>
          <div class="filter-container">
            <el-input
              v-model="filterText"
              placeholder="输入关键字过滤"
              clearable
              prefix-icon="Search"
            />
          </div>
          <div class="tree-container">
            <el-tree
              ref="categoryTree"
              :data="categoryTree"
              node-key="id"
              highlight-current
              :props="defaultProps"
              :filter-node-method="filterNode"
              default-expand-all
              @node-click="handleNodeClick"
            />
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧价格参考表 -->
      <el-col :span="18">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>{{ currentCategory ? currentCategory.name + ' - 价格参考' : '价格参考' }}</span>
              <div class="header-right">
                <el-button type="primary" @click="handleAdd">新增价格参考</el-button>
                <el-button type="success" @click="handleExport">导出数据</el-button>
              </div>
            </div>
          </template>
          
          <div class="search-box">
            <el-form :model="queryParams" ref="queryForm" :inline="true">
              <el-form-item label="设备型号" prop="model">
                <el-input v-model="queryParams.model" placeholder="请输入设备型号" clearable />
              </el-form-item>
              <el-form-item label="品牌" prop="brand">
                <el-input v-model="queryParams.brand" placeholder="请输入品牌" clearable />
              </el-form-item>
              <el-form-item label="价格区间">
                <el-input-number v-model="queryParams.minPrice" :min="0" controls-position="right" placeholder="最低价" class="price-input" />
                <span class="el-range-separator">-</span>
                <el-input-number v-model="queryParams.maxPrice" :min="0" controls-position="right" placeholder="最高价" class="price-input" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <el-table v-loading="loading" :data="priceList">
            <el-table-column prop="name" label="设备名称" min-width="150" />
            <el-table-column prop="model" label="规格型号" min-width="150" />
            <el-table-column prop="brand" label="品牌" width="120" />
            <el-table-column label="参考价格(元)" align="center" width="220">
              <template #default="scope">
                <div class="price-range">
                  <span class="price-min">{{ scope.row.minPrice }}</span>
                  <span class="price-separator">~</span>
                  <span class="price-max">{{ scope.row.maxPrice }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="marketPrice" label="市场均价(元)" align="center" width="120">
              <template #default="scope">
                <span class="market-price">{{ scope.row.marketPrice }}</span>
              </template>
            </el-table-column>
            <el-table-column label="价格趋势" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.trend === 'up' ? 'danger' : scope.row.trend === 'down' ? 'success' : 'info'">
                  {{ scope.row.trend === 'up' ? '上涨' : scope.row.trend === 'down' ? '下降' : '平稳' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间" width="160" sortable />
            <el-table-column label="操作" width="180" align="center" fixed="right">
              <template #default="scope">
                <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
                <el-button type="text" @click="handleView(scope.row)">查看详情</el-button>
                <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="queryParams.pageNum"
              v-model:page-size="queryParams.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 价格参考详情 -->
    <el-dialog title="价格参考详情" v-model="detailVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="设备名称">{{ priceDetail.name }}</el-descriptions-item>
        <el-descriptions-item label="设备分类">{{ priceDetail.categoryName }}</el-descriptions-item>
        <el-descriptions-item label="规格型号">{{ priceDetail.model }}</el-descriptions-item>
        <el-descriptions-item label="品牌">{{ priceDetail.brand }}</el-descriptions-item>
        <el-descriptions-item label="参考价格">{{ priceDetail.minPrice }} ~ {{ priceDetail.maxPrice }} 元</el-descriptions-item>
        <el-descriptions-item label="市场均价">{{ priceDetail.marketPrice }} 元</el-descriptions-item>
        <el-descriptions-item label="价格趋势">
          <el-tag :type="priceDetail.trend === 'up' ? 'danger' : priceDetail.trend === 'down' ? 'success' : 'info'">
            {{ priceDetail.trend === 'up' ? '上涨' : priceDetail.trend === 'down' ? '下降' : '平稳' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ priceDetail.updateTime }}</el-descriptions-item>
      </el-descriptions>
      
      <div class="chart-container" v-loading="chartLoading">
        <div class="chart-title">价格走势图</div>
        <div id="priceChart" style="width: 100%; height: 300px;"></div>
      </div>
      
      <div class="remark-container">
        <h4>价格说明</h4>
        <div class="remark-content">{{ priceDetail.remark }}</div>
      </div>
    </el-dialog>
    
    <!-- 表单对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="650px" append-to-body>
      <el-form ref="priceForm" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="设备分类" prop="categoryId">
          <el-cascader
            v-model="form.categoryId"
            :options="categoryOptions"
            :props="{ checkStrictly: true, value: 'id', label: 'name' }"
            placeholder="请选择设备分类"
            clearable
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="规格型号" prop="model">
          <el-input v-model="form.model" placeholder="请输入规格型号" />
        </el-form-item>
        <el-form-item label="品牌" prop="brand">
          <el-input v-model="form.brand" placeholder="请输入品牌" />
        </el-form-item>
        <el-form-item label="最低价格" prop="minPrice">
          <el-input-number v-model="form.minPrice" :min="0" :precision="2" controls-position="right" style="width: 100%" />
        </el-form-item>
        <el-form-item label="最高价格" prop="maxPrice">
          <el-input-number v-model="form.maxPrice" :min="0" :precision="2" controls-position="right" style="width: 100%" />
        </el-form-item>
        <el-form-item label="市场均价" prop="marketPrice">
          <el-input-number v-model="form.marketPrice" :min="0" :precision="2" controls-position="right" style="width: 100%" />
        </el-form-item>
        <el-form-item label="价格趋势" prop="trend">
          <el-radio-group v-model="form.trend">
            <el-radio label="up">上涨</el-radio>
            <el-radio label="down">下降</el-radio>
            <el-radio label="stable">平稳</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="价格说明" prop="remark">
          <el-input v-model="form.remark" type="textarea" rows="4" placeholder="请输入价格说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'EquipmentPrice',
  data() {
    return {
      // 加载状态
      loading: false,
      chartLoading: false,
      // 分类树过滤文本
      filterText: '',
      // 分类树选项
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      // 当前选中的分类
      currentCategory: null,
      // 设备分类树
      categoryTree: [
        {
          id: 1,
          name: '快递设备',
          children: [
            {
              id: 4,
              name: '快递柜',
              children: []
            },
            {
              id: 5,
              name: '分拣设备',
              children: []
            }
          ]
        },
        {
          id: 2,
          name: '物流设备',
          children: [
            {
              id: 6,
              name: '运输车辆',
              children: []
            },
            {
              id: 7,
              name: '装卸设备',
              children: []
            }
          ]
        },
        {
          id: 3,
          name: '办公设备',
          children: [
            {
              id: 8,
              name: '电脑设备',
              children: []
            },
            {
              id: 9,
              name: '打印设备',
              children: []
            }
          ]
        }
      ],
      // 分类级联选项
      categoryOptions: [],
      // 总条数
      total: 0,
      // 价格列表
      priceList: [
        {
          id: 1,
          categoryId: 4,
          categoryName: '快递柜',
          name: '智能快递柜',
          model: 'FC-200',
          brand: '丰巢',
          minPrice: 3000,
          maxPrice: 5000,
          marketPrice: 3800,
          trend: 'down',
          updateTime: '2023-04-10 10:00:00',
          remark: '该型号属于中型快递柜，支持扫码取件，一般配置24个格口。近期因为新款上市，价格有所下降，二手市场价格约为新品的60%。主要适用于社区、写字楼等场所。'
        },
        {
          id: 2,
          categoryId: 4,
          categoryName: '快递柜',
          name: '大型智能快递柜',
          model: 'ZM-300',
          brand: '中邮',
          minPrice: 5000,
          maxPrice: 8000,
          marketPrice: 6500,
          trend: 'stable',
          updateTime: '2023-04-11 15:30:00',
          remark: '该型号属于大型快递柜，支持人脸识别和扫码取件，一般配置36个格口。价格相对稳定，二手市场价格约为新品的70%。主要适用于大型社区、商场等人流密集区域。'
        },
        {
          id: 3,
          categoryId: 5,
          categoryName: '分拣设备',
          name: '小型分拣机',
          model: 'KY-100',
          brand: '快易',
          minPrice: 10000,
          maxPrice: 15000,
          marketPrice: 12500,
          trend: 'up',
          updateTime: '2023-04-12 09:15:00',
          remark: '该型号为小型分拣机，适用于日均处理包裹1000件以内的小型驿站。价格近期有所上涨，主要是因为物流行业需求增加，供不应求。'
        }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        categoryId: null,
        model: '',
        brand: '',
        minPrice: null,
        maxPrice: null
      },
      // 价格详情
      priceDetail: {},
      // 价格走势图实例
      priceChart: null,
      // 是否显示详情对话框
      detailVisible: false,
      // 是否显示表单对话框
      dialogVisible: false,
      // 表单标题
      dialogTitle: '',
      // 表单参数
      form: {
        id: undefined,
        categoryId: null,
        name: '',
        model: '',
        brand: '',
        minPrice: 0,
        maxPrice: 0,
        marketPrice: 0,
        trend: 'stable',
        remark: ''
      },
      // 表单校验规则
      rules: {
        categoryId: [
          { required: true, message: '请选择设备分类', trigger: 'change' }
        ],
        name: [
          { required: true, message: '设备名称不能为空', trigger: 'blur' }
        ],
        model: [
          { required: true, message: '规格型号不能为空', trigger: 'blur' }
        ],
        brand: [
          { required: true, message: '品牌不能为空', trigger: 'blur' }
        ],
        minPrice: [
          { required: true, message: '最低价格不能为空', trigger: 'blur' }
        ],
        maxPrice: [
          { required: true, message: '最高价格不能为空', trigger: 'blur' }
        ],
        marketPrice: [
          { required: true, message: '市场均价不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.categoryTree.filter(val)
    }
  },
  created() {
    this.getCategoryOptions()
  },
  methods: {
    /** 过滤节点 */
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    /** 获取分类选项 */
    getCategoryOptions() {
      // 将categoryTree转换为categoryOptions
      this.categoryOptions = this.formatCategoryOptions(this.categoryTree)
    },
    /** 转换分类选项 */
    formatCategoryOptions(categories) {
      const options = []
      categories.forEach(category => {
        const option = {
          id: category.id,
          name: category.name,
          children: []
        }
        if (category.children && category.children.length > 0) {
          option.children = this.formatCategoryOptions(category.children)
        }
        options.push(option)
      })
      return options
    },
    /** 点击树节点 */
    handleNodeClick(data) {
      this.currentCategory = data
      this.queryParams.categoryId = data.id
      this.handleQuery()
    },
    /** 查询价格列表 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置查询条件 */
    resetQuery() {
      this.$refs.queryForm.resetFields()
      if (this.currentCategory) {
        this.queryParams.categoryId = this.currentCategory.id
      }
      this.handleQuery()
    },
    /** 获取价格列表 */
    getList() {
      this.loading = true
      // 这里应该调用API获取数据
      // 模拟按分类过滤数据
      setTimeout(() => {
        if (this.queryParams.categoryId) {
          this.priceList = this.priceList.filter(item => item.categoryId === this.queryParams.categoryId)
        }
        this.total = this.priceList.length
        this.loading = false
      }, 500)
    },
    /** 新增价格参考 */
    handleAdd() {
      this.reset()
      this.dialogTitle = '添加价格参考'
      if (this.currentCategory) {
        this.form.categoryId = this.currentCategory.id
      }
      this.dialogVisible = true
    },
    /** 编辑价格参考 */
    handleEdit(row) {
      this.reset()
      this.dialogTitle = '编辑价格参考'
      // 这里应该调用API获取详细数据
      this.form = {
        id: row.id,
        categoryId: row.categoryId,
        name: row.name,
        model: row.model,
        brand: row.brand,
        minPrice: row.minPrice,
        maxPrice: row.maxPrice,
        marketPrice: row.marketPrice,
        trend: row.trend,
        remark: row.remark
      }
      this.dialogVisible = true
    },
    /** 查看价格详情 */
    handleView(row) {
      this.priceDetail = row
      this.detailVisible = true
      this.chartLoading = true
      
      // 初始化价格走势图
      this.$nextTick(() => {
        // 模拟获取历史价格数据
        setTimeout(() => {
          this.initPriceChart()
          this.chartLoading = false
        }, 500)
      })
    },
    /** 初始化价格走势图 */
    initPriceChart() {
      this.priceChart = echarts.init(document.getElementById('priceChart'))
      // 模拟历史价格数据
      const dates = ['2023-01', '2023-02', '2023-03', '2023-04', '2023-05', '2023-06']
      const prices = [4200, 4100, 3950, 3800, 3700, 3800]
      
      this.priceChart.setOption({
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value} 元'
          }
        },
        series: [
          {
            name: '市场均价',
            type: 'line',
            data: prices,
            markPoint: {
              data: [
                { type: 'max', name: '最高价' },
                { type: 'min', name: '最低价' }
              ]
            },
            markLine: {
              data: [
                { type: 'average', name: '平均价' }
              ]
            },
            lineStyle: {
              width: 3,
              color: '#5470C6'
            },
            areaStyle: {
              opacity: 0.2,
              color: '#5470C6'
            }
          }
        ]
      })
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        this.priceChart && this.priceChart.resize()
      })
    },
    /** 删除价格参考 */
    handleDelete(row) {
      this.$confirm(`确认要删除"${row.name}"的价格参考数据吗?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用API删除数据
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        this.getList()
      }).catch(() => {})
    },
    /** 导出数据 */
    handleExport() {
      this.$confirm('确认导出所有价格参考数据吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用API导出数据
        this.$message({
          type: 'success',
          message: '导出成功!'
        })
      }).catch(() => {})
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        categoryId: this.currentCategory ? this.currentCategory.id : null,
        name: '',
        model: '',
        brand: '',
        minPrice: 0,
        maxPrice: 0,
        marketPrice: 0,
        trend: 'stable',
        remark: ''
      }
    },
    /** 提交表单 */
    submitForm() {
      this.$refs.priceForm.validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            // 修改
            // 这里应该调用API修改数据
            this.$message({
              type: 'success',
              message: '修改成功!'
            })
          } else {
            // 新增
            // 这里应该调用API新增数据
            this.$message({
              type: 'success',
              message: '新增成功!'
            })
          }
          this.dialogVisible = false
          this.getList()
        }
      })
    },
    /** 分页大小变化 */
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    /** 分页页码变化 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-card {
  height: 100%;
  
  .filter-container {
    margin-bottom: 15px;
  }
  
  .tree-container {
    height: calc(100vh - 270px);
    overflow: auto;
  }
}

.search-box {
  margin-bottom: 20px;
}

.price-input {
  width: 100px;
}

.el-range-separator {
  padding: 0 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.price-range {
  display: flex;
  justify-content: center;
  align-items: center;
  
  .price-min, .price-max {
    color: #F56C6C;
    font-weight: bold;
  }
  
  .price-separator {
    margin: 0 10px;
    color: #909399;
  }
}

.market-price {
  color: #409EFF;
  font-weight: bold;
}

.chart-container {
  margin-top: 20px;
  
  .chart-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
  }
}

.remark-container {
  margin-top: 20px;
  
  h4 {
    margin-top: 0;
    margin-bottom: 10px;
  }
  
  .remark-content {
    line-height: 1.6;
    color: #606266;
    background-color: #F5F7FA;
    padding: 10px;
    border-radius: 4px;
  }
}
</style> 