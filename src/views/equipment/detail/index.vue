<template>
  <div class="page-container">
    <div class="page-header">
      <el-page-header @back="goBack" :content="equipmentData.name || '设备详情'" />
    </div>

    <el-row :gutter="20" v-loading="loading">
      <el-col :span="16">
        <el-card class="main-card">
          <div class="equipment-header">
            <h2>{{ equipmentData.name }}</h2>
            <el-tag :type="equipmentData.status === '1' ? 'success' : equipmentData.status === '2' ? 'info' : 'danger'">
              {{ equipmentData.status === '1' ? '在售' : equipmentData.status === '2' ? '已售' : '已下架' }}
            </el-tag>
          </div>

          <div class="equipment-images">
            <el-carousel height="400px" indicator-position="outside" arrow="always" :autoplay="false">
              <el-carousel-item v-for="(image, index) in equipmentData.images" :key="index">
                <el-image :src="image" fit="contain" style="width: 100%; height: 100%;" :preview-src-list="equipmentData.images" />
              </el-carousel-item>
            </el-carousel>
          </div>

          <div class="equipment-price">
            <span class="price-label">价格：</span>
            <span class="price-value">¥ {{ equipmentData.price }}</span>
          </div>

          <el-divider />

          <el-descriptions title="基本信息" :column="2" border>
            <el-descriptions-item label="设备名称">{{ equipmentData.name }}</el-descriptions-item>
            <el-descriptions-item label="设备类型">{{ equipmentData.typeName }}</el-descriptions-item>
            <el-descriptions-item label="品牌型号">{{ equipmentData.brand }}</el-descriptions-item>
            <el-descriptions-item label="新旧程度">{{ equipmentData.condition }}</el-descriptions-item>
            <el-descriptions-item label="使用年限">{{ equipmentData.useYears }} 年</el-descriptions-item>
            <el-descriptions-item label="交易方式">{{ equipmentData.tradeType }}</el-descriptions-item>
            <el-descriptions-item label="所在区域">{{ equipmentData.region }}</el-descriptions-item>
            <el-descriptions-item label="发布时间">{{ equipmentData.createTime }}</el-descriptions-item>
          </el-descriptions>

          <el-divider />

          <h3>设备描述</h3>
          <div class="equipment-description">
            {{ equipmentData.description }}
          </div>

          <el-divider />

          <h3>卖家信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="卖家">{{ equipmentData.publisher }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ equipmentData.phone }}</el-descriptions-item>
            <el-descriptions-item label="信用评级">
              <el-rate v-model="equipmentData.sellerRate" disabled show-score text-color="#ff9900" />
            </el-descriptions-item>
            <el-descriptions-item label="交易次数">{{ equipmentData.sellerDeals }} 次</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card class="side-card">
          <template #header>
            <div class="card-header">
              <span>联系卖家</span>
            </div>
          </template>
          <div class="contact-info">
            <p><i class="el-icon-user"></i> {{ equipmentData.publisher }}</p>
            <p><i class="el-icon-phone"></i> {{ equipmentData.phone }}</p>
            <div class="contact-buttons">
              <el-button type="primary" icon="el-icon-phone" @click="handleCall">拨打电话</el-button>
              <el-button type="success" icon="el-icon-chat-dot-round" @click="handleChat">在线咨询</el-button>
            </div>
          </div>
        </el-card>

        <el-card class="side-card mt-20">
          <template #header>
            <div class="card-header">
              <span>管理操作</span>
            </div>
          </template>
          <div class="admin-actions">
            <el-button type="primary" icon="Edit" @click="handleEdit" v-if="equipmentData.status !== '2'">编辑信息</el-button>
            <el-button type="warning" icon="RefreshLeft" @click="handleToggleStatus" v-if="equipmentData.status === '1'">下架设备</el-button>
            <el-button type="success" icon="RefreshRight" @click="handleToggleStatus" v-if="equipmentData.status === '0'">上架设备</el-button>
            <el-button type="info" icon="Finished" @click="handleMarkSold" v-if="equipmentData.status === '1'">标记已售</el-button>
            <el-button type="danger" icon="Delete" @click="handleDelete" v-if="equipmentData.status !== '2'">删除设备</el-button>
          </div>
        </el-card>

        <el-card class="side-card mt-20">
          <template #header>
            <div class="card-header">
              <span>浏览记录</span>
            </div>
          </template>
          <div class="view-log">
            <el-timeline>
              <el-timeline-item
                v-for="(log, index) in viewLogs"
                :key="index"
                :timestamp="log.time"
                placement="top"
              >
                {{ log.user }} {{ log.action }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>

        <el-card class="side-card mt-20">
          <template #header>
            <div class="card-header">
              <span>同类设备推荐</span>
            </div>
          </template>
          <div class="similar-items">
            <div v-for="(item, index) in similarItems" :key="index" class="similar-item" @click="handleViewSimilar(item)">
              <el-image :src="item.image" fit="cover" class="similar-image" />
              <div class="similar-info">
                <div class="similar-name">{{ item.name }}</div>
                <div class="similar-price">¥ {{ item.price }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 确认对话框 -->
    <el-dialog :title="confirmTitle" v-model="confirmVisible" width="400px">
      <div class="confirm-content">{{ confirmContent }}</div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="confirmVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmAction">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'EquipmentDetail',
  data() {
    return {
      loading: false,
      equipmentId: null,
      equipmentData: {
        id: 1,
        name: '丰巢快递柜',
        typeName: '快递柜',
        type: '1',
        brand: '丰巢 FC-200',
        price: 3500,
        useYears: 2,
        condition: '8成新',
        tradeType: '自提',
        region: '北京市朝阳区',
        images: [
          'https://img1.baidu.com/it/u=413646961,2312577432&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333',
          'https://img2.baidu.com/it/u=1003272215,1878948666&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800',
          'https://img0.baidu.com/it/u=2277942570,1789375638&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'
        ],
        description: '9成新丰巢快递柜，使用两年，无划痕，功能完好，支持扫码存取件。设备型号FC-200，共24格，大中小格都有。因为店铺整体装修换代，所以低价转让。可以上门看设备，价格可小刀。仅限自提，可协助搬运。',
        publisher: '张三',
        phone: '13800138000',
        sellerRate: 4.5,
        sellerDeals: 12,
        createTime: '2023-04-10 10:30:00',
        status: '1'
      },
      viewLogs: [
        { user: '李先生', action: '查看了该设备', time: '2023-04-15 15:30:00' },
        { user: '王女士', action: '询问了该设备', time: '2023-04-14 10:20:00' },
        { user: '赵先生', action: '查看了该设备', time: '2023-04-13 14:15:00' },
        { user: '钱先生', action: '收藏了该设备', time: '2023-04-12 16:40:00' }
      ],
      similarItems: [
        {
          id: 2,
          name: '中邮智能快递柜',
          image: 'https://img2.baidu.com/it/u=1003272215,1878948666&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800',
          price: 4200
        },
        {
          id: 3,
          name: '云柜快递柜',
          image: 'https://img1.baidu.com/it/u=413646961,2312577432&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333',
          price: 3800
        },
        {
          id: 4,
          name: '格格快递柜',
          image: 'https://img0.baidu.com/it/u=2277942570,1789375638&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333',
          price: 2900
        }
      ],
      // 确认框
      confirmVisible: false,
      confirmTitle: '',
      confirmContent: '',
      confirmCallback: null
    }
  },
  created() {
    this.equipmentId = this.$route.params.id
    this.getEquipmentDetail()
  },
  methods: {
    /** 获取设备详情 */
    getEquipmentDetail() {
      this.loading = true
      // 这里应该调用API获取详情
      setTimeout(() => {
        // 模拟API调用返回数据
        this.loading = false
      }, 500)
    },
    /** 返回上一页 */
    goBack() {
      this.$router.go(-1)
    },
    /** 编辑设备 */
    handleEdit() {
      this.$router.push(`/equipment/edit/${this.equipmentId}`)
    },
    /** 切换设备状态 */
    handleToggleStatus() {
      const statusText = this.equipmentData.status === '1' ? '下架' : '上架'
      this.confirmTitle = `确认${statusText}`
      this.confirmContent = `确定要${statusText}设备"${this.equipmentData.name}"吗？`
      this.confirmCallback = () => {
        // 调用API切换状态
        this.equipmentData.status = this.equipmentData.status === '1' ? '0' : '1'
        this.$message({
          type: 'success',
          message: `${statusText}成功！`
        })
        this.confirmVisible = false
      }
      this.confirmVisible = true
    },
    /** 标记已售 */
    handleMarkSold() {
      this.confirmTitle = '标记已售'
      this.confirmContent = `确定要将设备"${this.equipmentData.name}"标记为已售状态吗？`
      this.confirmCallback = () => {
        // 调用API标记已售
        this.equipmentData.status = '2'
        this.$message({
          type: 'success',
          message: '已成功标记为已售状态！'
        })
        this.confirmVisible = false
      }
      this.confirmVisible = true
    },
    /** 删除设备 */
    handleDelete() {
      this.confirmTitle = '删除设备'
      this.confirmContent = `确定要删除设备"${this.equipmentData.name}"吗？此操作不可恢复！`
      this.confirmCallback = () => {
        // 调用API删除设备
        this.$message({
          type: 'success',
          message: '删除成功！'
        })
        this.confirmVisible = false
        this.$router.push('/equipment/list')
      }
      this.confirmVisible = true
    },
    /** 拨打电话 */
    handleCall() {
      window.location.href = `tel:${this.equipmentData.phone}`
    },
    /** 在线咨询 */
    handleChat() {
      this.$message({
        type: 'info',
        message: '正在连接聊天服务...'
      })
    },
    /** 查看同类设备 */
    handleViewSimilar(item) {
      this.$router.push(`/equipment/detail/${item.id}`)
    },
    /** 确认操作 */
    confirmAction() {
      if (this.confirmCallback) {
        this.confirmCallback()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 20px;
}

.main-card {
  margin-bottom: 20px;
}

.equipment-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    margin-right: 10px;
  }
}

.equipment-images {
  margin-bottom: 20px;
}

.equipment-price {
  margin: 20px 0;
  font-size: 16px;
  
  .price-label {
    font-weight: bold;
  }
  
  .price-value {
    color: #F56C6C;
    font-size: 24px;
    font-weight: bold;
  }
}

.equipment-description {
  line-height: 1.8;
  color: #606266;
  margin-bottom: 20px;
  white-space: pre-line;
}

.side-card {
  margin-bottom: 20px;
  
  .card-header {
    font-weight: bold;
  }
}

.mt-20 {
  margin-top: 20px;
}

.contact-info {
  p {
    margin: 10px 0;
    
    i {
      margin-right: 5px;
    }
  }
  
  .contact-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
  }
}

.admin-actions {
  display: flex;
  flex-direction: column;
  
  .el-button {
    margin-bottom: 10px;
    margin-left: 0;
  }
}

.view-log {
  max-height: 300px;
  overflow-y: auto;
}

.similar-items {
  .similar-item {
    display: flex;
    padding: 10px 0;
    border-bottom: 1px solid #EBEEF5;
    cursor: pointer;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: #F5F7FA;
    }
    
    .similar-image {
      width: 80px;
      height: 60px;
      margin-right: 10px;
      border-radius: 4px;
    }
    
    .similar-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      
      .similar-name {
        font-size: 14px;
        color: #303133;
      }
      
      .similar-price {
        font-size: 14px;
        color: #F56C6C;
        font-weight: bold;
      }
    }
  }
}

.confirm-content {
  padding: 20px 0;
}
</style> 