<template>
  <div class="page-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>设备分类管理</span>
          <el-button type="primary" @click="handleAdd">新增分类</el-button>
        </div>
      </template>
      
      <el-table v-loading="loading" :data="categoryList" row-key="id" border :tree-props="{ children: 'children' }">
        <el-table-column prop="name" label="分类名称" min-width="200" />
        <el-table-column prop="code" label="分类编码" width="150" />
        <el-table-column prop="sort" label="排序" width="100" align="center" />
        <el-table-column prop="createTime" label="创建时间" width="180" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'danger' : 'success'">
              {{ scope.row.status === '0' ? '停用' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="handleAdd(scope.row)" v-if="scope.row.level < 3">添加子分类</el-button>
            <el-button type="text" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button type="text" @click="handleStatus(scope.row)" v-if="scope.row.children && scope.row.children.length === 0">
              {{ scope.row.status === '0' ? '启用' : '停用' }}
            </el-button>
            <el-button type="text" @click="handleDelete(scope.row)" v-if="scope.row.children && scope.row.children.length === 0">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 添加或修改分类对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px" append-to-body>
      <el-form ref="categoryForm" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="上级分类" prop="parentId" v-if="form.parentId !== 0">
          <el-cascader
            v-model="form.parentId"
            :options="categoryOptions"
            :props="{ checkStrictly: true, value: 'id', label: 'name' }"
            placeholder="请选择上级分类"
            clearable
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入分类编码" />
        </el-form-item>
        <el-form-item label="显示顺序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="999" controls-position="right" style="width: 100%" />
        </el-form-item>
        <el-form-item label="分类图标" prop="icon">
          <el-input v-model="form.icon" placeholder="请选择图标" style="width: calc(100% - 45px)" />
          <el-button type="primary" icon="Select" @click="showIconSelector" style="margin-left: 5px"></el-button>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="1">正常</el-radio>
            <el-radio label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 图标选择器对话框 -->
    <el-dialog title="选择图标" v-model="iconSelectorVisible" width="800px" append-to-body>
      <div class="icon-list">
        <div v-for="(icon, index) in iconList" :key="index" class="icon-item" @click="selectIcon(icon)">
          <i :class="icon"></i>
          <span>{{ icon.split('-')[1] }}</span>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="iconSelectorVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'EquipmentCategory',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 分类表格数据
      categoryList: [
        {
          id: 1,
          name: '快递设备',
          code: 'express',
          sort: 1,
          icon: 'el-icon-box',
          status: '1',
          level: 1,
          createTime: '2023-04-01 10:00:00',
          children: [
            {
              id: 4,
              name: '快递柜',
              code: 'express-cabinet',
              sort: 1,
              icon: 'el-icon-goods',
              status: '1',
              level: 2,
              createTime: '2023-04-01 10:10:00',
              children: []
            },
            {
              id: 5,
              name: '分拣设备',
              code: 'express-sorting',
              sort: 2,
              icon: 'el-icon-s-grid',
              status: '1',
              level: 2,
              createTime: '2023-04-01 10:15:00',
              children: []
            }
          ]
        },
        {
          id: 2,
          name: '物流设备',
          code: 'logistics',
          sort: 2,
          icon: 'el-icon-truck',
          status: '1',
          level: 1,
          createTime: '2023-04-01 10:05:00',
          children: [
            {
              id: 6,
              name: '运输车辆',
              code: 'logistics-vehicle',
              sort: 1,
              icon: 'el-icon-van',
              status: '1',
              level: 2,
              createTime: '2023-04-01 10:20:00',
              children: []
            },
            {
              id: 7,
              name: '装卸设备',
              code: 'logistics-loading',
              sort: 2,
              icon: 'el-icon-s-operation',
              status: '1',
              level: 2,
              createTime: '2023-04-01 10:25:00',
              children: []
            }
          ]
        },
        {
          id: 3,
          name: '办公设备',
          code: 'office',
          sort: 3,
          icon: 'el-icon-office-building',
          status: '1',
          level: 1,
          createTime: '2023-04-01 10:30:00',
          children: [
            {
              id: 8,
              name: '电脑设备',
              code: 'office-computer',
              sort: 1,
              icon: 'el-icon-monitor',
              status: '1',
              level: 2,
              createTime: '2023-04-01 10:35:00',
              children: []
            },
            {
              id: 9,
              name: '打印设备',
              code: 'office-printer',
              sort: 2,
              icon: 'el-icon-printer',
              status: '1',
              level: 2,
              createTime: '2023-04-01 10:40:00',
              children: []
            }
          ]
        }
      ],
      // 分类选项
      categoryOptions: [],
      // 弹出层标题
      dialogTitle: '',
      // 是否显示弹出层
      dialogVisible: false,
      // 是否显示图标选择器
      iconSelectorVisible: false,
      // 表单参数
      form: {
        id: undefined,
        parentId: 0,
        name: '',
        code: '',
        sort: 0,
        icon: '',
        status: '1',
        remark: ''
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: '分类名称不能为空', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '分类编码不能为空', trigger: 'blur' },
          { pattern: /^[a-z0-9-]+$/, message: '分类编码只能包含小写字母、数字和连字符', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '显示顺序不能为空', trigger: 'blur' }
        ]
      },
      // 图标列表
      iconList: [
        'el-icon-box', 'el-icon-goods', 'el-icon-s-grid', 'el-icon-truck', 'el-icon-van',
        'el-icon-s-operation', 'el-icon-office-building', 'el-icon-monitor', 'el-icon-printer',
        'el-icon-shopping-cart', 'el-icon-shopping-bag', 'el-icon-s-shop', 'el-icon-s-goods',
        'el-icon-s-marketing', 'el-icon-s-tools', 'el-icon-s-platform', 'el-icon-s-cooperation',
        'el-icon-s-order', 'el-icon-s-data', 'el-icon-s-management', 'el-icon-mobile',
        'el-icon-phone', 'el-icon-camera', 'el-icon-video-camera', 'el-icon-message',
        'el-icon-chat-dot-round', 'el-icon-chat-line-round', 'el-icon-bell', 'el-icon-location',
        'el-icon-place', 'el-icon-map-location', 'el-icon-document', 'el-icon-folder',
        'el-icon-ticket', 'el-icon-basketball', 'el-icon-football', 'el-icon-soccer'
      ]
    }
  },
  created() {
    this.getCategoryList()
  },
  methods: {
    /** 查询分类列表 */
    getCategoryList() {
      this.loading = true
      // 这里应该调用API获取数据
      setTimeout(() => {
        // 将分类列表转为层级结构供选择上级分类
        this.categoryOptions = this.formatCategoryOptions(this.categoryList)
        this.loading = false
      }, 500)
    },
    /** 转换分类选项 */
    formatCategoryOptions(categories) {
      const options = []
      categories.forEach(category => {
        const option = {
          id: category.id,
          name: category.name,
          children: []
        }
        if (category.children && category.children.length > 0) {
          option.children = this.formatCategoryOptions(category.children)
        }
        options.push(option)
      })
      return options
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      if (row && row.id) {
        // 添加子分类
        this.form.parentId = row.id
        this.dialogTitle = `添加"${row.name}"的子分类`
      } else {
        // 添加一级分类
        this.form.parentId = 0
        this.dialogTitle = '添加分类'
      }
      this.dialogVisible = true
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.dialogTitle = '修改分类'
      // 这里应该调用API获取详细数据
      this.form = {
        id: row.id,
        parentId: row.parentId || 0,
        name: row.name,
        code: row.code,
        sort: row.sort,
        icon: row.icon,
        status: row.status,
        remark: row.remark || ''
      }
      this.dialogVisible = true
    },
    /** 修改状态操作 */
    handleStatus(row) {
      const newStatus = row.status === '0' ? '1' : '0'
      const statusText = newStatus === '0' ? '停用' : '启用'
      this.$confirm(`确认要${statusText}分类"${row.name}"吗?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用API修改状态
        row.status = newStatus
        this.$message({
          type: 'success',
          message: `${statusText}成功!`
        })
      }).catch(() => {})
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm(`确认要删除分类"${row.name}"吗?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用API删除数据
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        this.getCategoryList()
      }).catch(() => {})
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        parentId: 0,
        name: '',
        code: '',
        sort: 0,
        icon: '',
        status: '1',
        remark: ''
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.categoryForm.validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            // 修改
            // 这里应该调用API修改数据
            this.$message({
              type: 'success',
              message: '修改成功!'
            })
          } else {
            // 新增
            // 这里应该调用API新增数据
            this.$message({
              type: 'success',
              message: '新增成功!'
            })
          }
          this.dialogVisible = false
          this.getCategoryList()
        }
      })
    },
    /** 显示图标选择器 */
    showIconSelector() {
      this.iconSelectorVisible = true
    },
    /** 选择图标 */
    selectIcon(icon) {
      this.form.icon = icon
      this.iconSelectorVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.icon-list {
  display: flex;
  flex-wrap: wrap;
  max-height: 400px;
  overflow-y: auto;
  
  .icon-item {
    width: 120px;
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 5px;
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    cursor: pointer;
    
    &:hover {
      background-color: #F5F7FA;
    }
    
    i {
      font-size: 30px;
      margin-bottom: 10px;
    }
    
    span {
      font-size: 12px;
    }
  }
}
</style> 