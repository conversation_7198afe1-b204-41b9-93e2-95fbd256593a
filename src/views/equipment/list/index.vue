<template>
  <div class="page-container">
    <!-- 搜索工具栏 -->
    <div class="search-box">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入设备名称" clearable />
        </el-form-item>
        <el-form-item label="设备类型" prop="type">
          <el-select v-model="queryParams.type" placeholder="请选择设备类型" clearable>
            <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="价格区间">
          <el-input-number v-model="queryParams.minPrice" :min="0" controls-position="right" placeholder="最低价" class="price-input" />
          <span class="el-range-separator">-</span>
          <el-input-number v-model="queryParams.maxPrice" :min="0" controls-position="right" placeholder="最高价" class="price-input" />
        </el-form-item>
        <el-form-item label="销售状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择销售状态" clearable>
            <el-option label="在售" value="1" />
            <el-option label="已售" value="2" />
            <el-option label="已下架" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar">
      <el-button type="primary" icon="Plus" @click="handleAdd">新增设备</el-button>
      <el-button type="success" icon="Upload" @click="handleImport">批量导入</el-button>
      <el-button type="warning" icon="Download" @click="handleExport">导出数据</el-button>
    </div>

    <!-- 表格展示 -->
    <el-table v-loading="loading" :data="equipmentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="设备名称" align="center" prop="name" min-width="180" show-overflow-tooltip>
        <template #default="scope">
          <div class="equipment-cell">
            <el-image :src="scope.row.image" fit="cover" class="equipment-image" />
            <span>{{ scope.row.name }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="设备类型" align="center" prop="type" width="120">
        <template #default="scope">
          <el-tag :type="getTypeTag(scope.row.type)">{{ scope.row.typeName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="价格" align="center" prop="price" width="120">
        <template #default="scope">
          <span class="price">¥ {{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column label="新旧程度" align="center" prop="condition" width="100">
        <template #default="scope">
          <el-tag type="info">{{ scope.row.condition }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="所在区域" align="center" prop="region" width="150" />
      <el-table-column label="发布者" align="center" prop="publisher" width="120" />
      <el-table-column label="发布时间" align="center" prop="createTime" width="160" sortable />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === '1' ? 'success' : scope.row.status === '2' ? 'info' : 'danger'">
            {{ scope.row.status === '1' ? '在售' : scope.row.status === '2' ? '已售' : '已下架' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template #default="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)">查看</el-button>
          <el-button type="text" size="small" @click="handleEdit(scope.row)" v-if="scope.row.status !== '2'">编辑</el-button>
          <el-button type="text" size="small" @click="handleStatus(scope.row)" v-if="scope.row.status === '1'">下架</el-button>
          <el-button type="text" size="small" @click="handleStatus(scope.row)" v-if="scope.row.status === '0'">上架</el-button>
          <el-button type="text" size="small" @click="handleDelete(scope.row)" v-if="scope.row.status !== '2'">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 设备表单对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="650px" append-to-body>
      <el-form ref="equipmentForm" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择设备类型" style="width: 100%">
            <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="品牌型号" prop="brand">
          <el-input v-model="form.brand" placeholder="请输入品牌型号" />
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input-number v-model="form.price" :min="0" :precision="2" controls-position="right" style="width: 100%" />
        </el-form-item>
        <el-form-item label="使用年限" prop="useYears">
          <el-input-number v-model="form.useYears" :min="0" :precision="1" controls-position="right" style="width: 100%" />
        </el-form-item>
        <el-form-item label="新旧程度" prop="condition">
          <el-select v-model="form.condition" placeholder="请选择新旧程度" style="width: 100%">
            <el-option label="全新" value="全新" />
            <el-option label="9成新" value="9成新" />
            <el-option label="8成新" value="8成新" />
            <el-option label="7成新" value="7成新" />
            <el-option label="6成新及以下" value="6成新及以下" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易方式" prop="tradeType">
          <el-select v-model="form.tradeType" placeholder="请选择交易方式" style="width: 100%">
            <el-option label="自提" value="自提" />
            <el-option label="配送" value="配送" />
            <el-option label="均可" value="均可" />
          </el-select>
        </el-form-item>
        <el-form-item label="所在区域" prop="region">
          <el-cascader
            v-model="form.region"
            :options="regionOptions"
            placeholder="请选择所在区域"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="设备图片" prop="images">
          <el-upload
            list-type="picture-card"
            :limit="6"
            action="#"
            :auto-upload="false"
            :file-list="fileList"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="el-upload__tip">支持JPG、PNG格式，单张不超过5MB，最多可上传6张</div>
        </el-form-item>
        <el-form-item label="设备描述" prop="description">
          <el-input v-model="form.description" type="textarea" rows="4" placeholder="请输入设备描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog title="批量导入设备" v-model="importVisible" width="500px" append-to-body>
      <el-upload
        class="upload-demo"
        drag
        action="#"
        :auto-upload="false"
        :limit="1"
        :on-change="handleImportFileChange"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip">只支持xlsx格式文件，<el-link type="primary" :underline="false" @click="downloadTemplate">下载模板</el-link></div>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitImport">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'EquipmentList',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      selectedIds: [],
      // 总条数
      total: 0,
      // 设备表格数据
      equipmentList: [
        {
          id: 1,
          name: '丰巢快递柜',
          image: 'https://img1.baidu.com/it/u=413646961,2312577432&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333',
          type: '1',
          typeName: '快递柜',
          price: 3500,
          condition: '8成新',
          region: '北京市朝阳区',
          publisher: '张三',
          createTime: '2023-04-10 10:30:00',
          status: '1'
        },
        {
          id: 2,
          name: '快递分拣机',
          image: 'https://img2.baidu.com/it/u=1003272215,1878948666&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800',
          type: '2',
          typeName: '分拣设备',
          price: 12000,
          condition: '9成新',
          region: '北京市海淀区',
          publisher: '李四',
          createTime: '2023-04-11 14:20:00',
          status: '1'
        },
        {
          id: 3,
          name: '电动三轮车',
          image: 'https://img0.baidu.com/it/u=2277942570,1789375638&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333',
          type: '3',
          typeName: '运输车辆',
          price: 2800,
          condition: '7成新',
          region: '北京市丰台区',
          publisher: '王五',
          createTime: '2023-04-12 09:15:00',
          status: '2'
        }
      ],
      // 设备类型选项
      typeOptions: [
        { value: '1', label: '快递柜' },
        { value: '2', label: '分拣设备' },
        { value: '3', label: '运输车辆' },
        { value: '4', label: '称重设备' },
        { value: '5', label: '打包设备' },
        { value: '6', label: '其他设备' }
      ],
      // 区域选项
      regionOptions: [
        {
          value: '北京市',
          label: '北京市',
          children: [
            {
              value: '北京市',
              label: '北京市',
              children: [
                { value: '东城区', label: '东城区' },
                { value: '西城区', label: '西城区' },
                { value: '朝阳区', label: '朝阳区' },
                { value: '海淀区', label: '海淀区' },
                { value: '丰台区', label: '丰台区' }
              ]
            }
          ]
        }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: '',
        type: '',
        minPrice: null,
        maxPrice: null,
        status: ''
      },
      // 表单参数
      form: {
        id: undefined,
        name: '',
        type: '',
        brand: '',
        price: 0,
        useYears: 0,
        condition: '',
        tradeType: '',
        region: [],
        phone: '',
        images: [],
        description: ''
      },
      // 表单校验规则
      rules: {
        name: [
          { required: true, message: '设备名称不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择设备类型', trigger: 'change' }
        ],
        price: [
          { required: true, message: '价格不能为空', trigger: 'blur' }
        ],
        condition: [
          { required: true, message: '请选择新旧程度', trigger: 'change' }
        ],
        region: [
          { required: true, message: '请选择所在区域', trigger: 'change' }
        ],
        phone: [
          { required: true, message: '联系电话不能为空', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },
      // 文件列表
      fileList: [],
      // 对话框标题
      dialogTitle: '',
      // 是否显示弹出层
      dialogVisible: false,
      // 是否显示导入弹出层
      importVisible: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询设备列表 */
    getList() {
      this.loading = true
      // 这里应该调用API获取数据
      // 模拟API调用
      setTimeout(() => {
        this.total = this.equipmentList.length
        this.loading = false
      }, 500)
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.dialogTitle = '添加设备'
      this.dialogVisible = true
    },
    /** 编辑按钮操作 */
    handleEdit(row) {
      this.reset()
      // 这里应该调用API获取详细数据
      this.form = {
        id: row.id,
        name: row.name,
        type: row.type,
        brand: '品牌名称 XYZ-123',
        price: row.price,
        useYears: 2,
        condition: row.condition,
        tradeType: '自提',
        region: ['北京市', '北京市', row.region.split('市')[1].trim()],
        phone: '13800138000',
        images: [row.image],
        description: '这是一段关于设备的详细描述，包含了使用情况、性能特点等信息。'
      }
      // 处理图片显示
      this.fileList = [{
        name: 'image.jpg',
        url: row.image
      }]
      this.dialogTitle = '编辑设备'
      this.dialogVisible = true
    },
    /** 查看详情操作 */
    handleView(row) {
      this.$router.push(`/equipment/detail/${row.id}`)
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm(`是否确认删除设备"${row.name}"?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用API删除数据
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
        this.getList()
      }).catch(() => {})
    },
    /** 上下架操作 */
    handleStatus(row) {
      let statusText = row.status === '1' ? '下架' : '上架'
      let statusValue = row.status === '1' ? '0' : '1'
      this.$confirm(`是否确认${statusText}设备"${row.name}"?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用API修改状态
        this.$message({
          type: 'success',
          message: `${statusText}成功!`
        })
        row.status = statusValue
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$confirm('是否确认导出所有设备数据?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里应该调用API导出数据
        this.$message({
          type: 'success',
          message: '导出成功!'
        })
      }).catch(() => {})
    },
    /** 导入按钮操作 */
    handleImport() {
      this.importVisible = true
    },
    /** 下载模板 */
    downloadTemplate() {
      // 这里应该下载导入模板
      window.open('/download/equipment_template.xlsx')
    },
    /** 导入文件变化 */
    handleImportFileChange(file) {
      // 处理导入文件
      console.log(file)
    },
    /** 提交导入 */
    submitImport() {
      // 提交导入
      this.$message({
        type: 'success',
        message: '导入成功!'
      })
      this.importVisible = false
    },
    /** 文件上传中处理 */
    handleFileChange(file) {
      // 处理文件上传
      console.log(file)
    },
    /** 文件删除处理 */
    handleFileRemove(file) {
      // 处理文件删除
      console.log(file)
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        name: '',
        type: '',
        brand: '',
        price: 0,
        useYears: 0,
        condition: '',
        tradeType: '',
        region: [],
        phone: '',
        images: [],
        description: ''
      }
      this.fileList = []
    },
    /** 提交表单 */
    submitForm() {
      this.$refs.equipmentForm.validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            // 修改操作
            // 这里应该调用API修改数据
            this.$message({
              type: 'success',
              message: '修改成功!'
            })
          } else {
            // 新增操作
            // 这里应该调用API新增数据
            this.$message({
              type: 'success',
              message: '新增成功!'
            })
          }
          this.dialogVisible = false
          this.getList()
        }
      })
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
    },
    /** 分页大小变化 */
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    /** 分页页码变化 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    },
    /** 获取设备类型标签样式 */
    getTypeTag(type) {
      const typeMap = {
        '1': 'primary',
        '2': 'success',
        '3': 'warning',
        '4': 'danger',
        '5': 'info',
        '6': ''
      }
      return typeMap[type] || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;
}

.search-box {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.toolbar {
  margin-bottom: 20px;
}

.equipment-cell {
  display: flex;
  align-items: center;
  
  .equipment-image {
    width: 50px;
    height: 50px;
    margin-right: 10px;
    border-radius: 4px;
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.price-input {
  width: 100px;
}

.el-range-separator {
  padding: 0 10px;
}

.price {
  color: #F56C6C;
  font-weight: bold;
}
</style> 