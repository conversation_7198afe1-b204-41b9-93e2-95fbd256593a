<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>顶班接单申请</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="refreshList">刷新</el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
        <el-form-item label="申请状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="申请状态" clearable style="width: 150px">
            <el-option
              v-for="dict in statusOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="申请人" prop="applicantName">
          <el-input
            v-model="queryParams.applicantName"
            placeholder="请输入申请人姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="顶班时间" prop="substituteTime">
          <el-date-picker
            v-model="queryParams.substituteTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['substitution:application:approve']"
            type="success"
            plain
            icon="Check"
            :disabled="single"
            @click="handleApprove"
          >批准</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['substitution:application:reject']"
            type="danger"
            plain
            icon="Close"
            :disabled="single"
            @click="handleReject"
          >拒绝</el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @refreshList="getList"></right-toolbar>
      </el-row>

      <!-- 表格数据 -->
      <el-table v-loading="loading" :data="applicationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="申请编号" align="center" prop="id" width="120" />
        <el-table-column label="申请人" align="center" prop="applicantName" min-width="100" :show-overflow-tooltip="true" />
        <el-table-column label="联系电话" align="center" prop="applicantPhone" min-width="120" />
        <el-table-column label="顶班驿站" align="center" prop="stationName" min-width="150" :show-overflow-tooltip="true" />
        <el-table-column label="顶班日期" align="center" prop="substituteDate" width="100" />
        <el-table-column label="顶班时段" align="center" prop="substituteTime" width="180" />
        <el-table-column label="申请时间" align="center" prop="applyTime" width="160" />
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="statusTypeMap[scope.row.status]">{{ statusLabelMap[scope.row.status] }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              v-hasPermi="['substitution:application:view']"
              type="text"
              icon="View"
              @click="handleView(scope.row)"
            >查看</el-button>
            <el-button
              v-if="scope.row.status === 0"
              v-hasPermi="['substitution:application:approve']"
              type="text"
              icon="Check"
              @click="handleApprove(scope.row)"
            >批准</el-button>
            <el-button
              v-if="scope.row.status === 0"
              v-hasPermi="['substitution:application:reject']"
              type="text"
              icon="Close"
              @click="handleReject(scope.row)"
            >拒绝</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 拒绝对话框 -->
    <el-dialog title="拒绝申请" v-model="rejectOpen" width="500px" append-to-body>
      <el-form :model="rejectForm" ref="rejectFormRef" :rules="rejectRules" label-width="80px">
        <el-form-item label="拒绝理由" prop="reason">
          <el-input
            type="textarea"
            v-model="rejectForm.reason"
            placeholder="请输入拒绝理由"
            :rows="4"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelReject">取 消</el-button>
          <el-button type="primary" @click="confirmReject">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { listApplications, approveApplication, rejectApplication } from '@/api/substitution/application';
import Pagination from '@/components/Pagination';
import RightToolbar from '@/components/RightToolbar';

export default {
  name: 'SubstitutionApplication',
  components: {
    Pagination,
    RightToolbar
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 申请列表
      applicationList: [],
      // 弹出层标题
      title: '',
      // 拒绝对话框
      rejectOpen: false,
      // 拒绝表单
      rejectForm: {
        id: null,
        reason: ''
      },
      // 拒绝表单校验规则
      rejectRules: {
        reason: [
          { required: true, message: '拒绝理由不能为空', trigger: 'blur' },
          { min: 5, max: 200, message: '内容长度在5-200个字符之间', trigger: 'blur' }
        ]
      },
      // 状态选项
      statusOptions: [
        { value: 0, label: '待审核' },
        { value: 1, label: '已批准' },
        { value: 2, label: '已拒绝' }
      ],
      // 状态映射
      statusLabelMap: {
        0: '待审核',
        1: '已批准',
        2: '已拒绝'
      },
      // 状态类型映射
      statusTypeMap: {
        0: 'warning',
        1: 'success',
        2: 'danger'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: undefined,
        applicantName: undefined,
        substituteTime: []
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 获取申请列表
    getList() {
      this.loading = true;

      // 处理时间范围
      const queryParams = { ...this.queryParams };
      if (queryParams.substituteTime && queryParams.substituteTime.length === 2) {
        queryParams.startDate = queryParams.substituteTime[0];
        queryParams.endDate = queryParams.substituteTime[1];
      }
      delete queryParams.substituteTime;

      listApplications(queryParams).then(response => {
        this.applicationList = response.data.list || [];
        this.total = response.data.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 刷新列表
    refreshList() {
      this.getList();
    },
    // 表单重置
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        status: undefined,
        applicantName: undefined,
        substituteTime: []
      };
      this.handleQuery();
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 查看详情
    handleView(row) {
      this.$router.push({ path: `/substitution/application/detail/${row.id}` });
    },
    // 批准申请
    handleApprove(row) {
      const applicationId = row.id || this.ids[0];
      this.$confirm('确认要批准该申请吗?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return approveApplication(applicationId);
      }).then(() => {
        this.getList();
        this.$message.success('批准成功');
      }).catch(() => {});
    },
    // 显示拒绝对话框
    handleReject(row) {
      this.rejectForm.id = row.id || this.ids[0];
      this.rejectForm.reason = '';
      this.rejectOpen = true;
    },
    // 取消拒绝
    cancelReject() {
      this.rejectOpen = false;
      this.resetRejectForm();
    },
    // 确认拒绝
    confirmReject() {
      this.$refs.rejectFormRef.validate(valid => {
        if (valid) {
          rejectApplication(this.rejectForm).then(() => {
            this.$message.success('拒绝成功');
            this.rejectOpen = false;
            this.getList();
          });
        }
      });
    },
    // 重置拒绝表单
    resetRejectForm() {
      if (this.$refs.rejectFormRef) {
        this.$refs.rejectFormRef.resetFields();
      }
      this.rejectForm = {
        id: null,
        reason: ''
      };
    }
  }
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}
</style>
