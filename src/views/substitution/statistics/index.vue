<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>顶班服务概览</span>
          <div class="right-options">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </div>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Calendar /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">顶班总数</div>
              <div class="stat-value">{{ overview.totalCount }}</div>
              <div class="stat-compare">
                较上期
                <span :class="overview.totalCountTrend > 0 ? 'up' : 'down'">
                  {{ overview.totalCountTrend > 0 ? '+' : '' }}{{ overview.totalCountTrend }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">已完成顶班</div>
              <div class="stat-value">{{ overview.completedCount }}</div>
              <div class="stat-compare">
                完成率
                <span :class="overview.completionRate >= 90 ? 'up' : overview.completionRate >= 70 ? 'normal' : 'down'">
                  {{ overview.completionRate }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">顶班人员数</div>
              <div class="stat-value">{{ overview.staffCount }}</div>
              <div class="stat-compare">
                人均顶班
                <span class="normal">{{ overview.avgSubstitutionsPerStaff }}次</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-title">总薪资支出</div>
              <div class="stat-value">{{ formatCurrency(overview.totalSalary) }}</div>
              <div class="stat-compare">
                人均薪资
                <span class="normal">{{ formatCurrency(overview.avgSalaryPerStaff) }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-row :gutter="20" class="mt20">
      <el-col :span="16">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>顶班趋势分析</span>
              <el-radio-group v-model="trendTimeUnit" size="small" @change="handleTrendTimeUnitChange">
                <el-radio-button label="day">日</el-radio-button>
                <el-radio-button label="week">周</el-radio-button>
                <el-radio-button label="month">月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div id="trendChart" style="width: 100%; height: 400px"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>顶班类型分布</span>
            </div>
          </template>
          <div id="typeChart" style="width: 100%; height: 400px"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt20">
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>区域分布热图</span>
            </div>
          </template>
          <div id="regionChart" style="width: 100%; height: 400px"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>服务质量分析</span>
            </div>
          </template>
          <div id="qualityChart" style="width: 100%; height: 400px"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-card class="box-card mt20">
      <template #header>
        <div class="card-header">
          <span>顶班人员TOP10</span>
        </div>
      </template>
      <el-table :data="topStaff" style="width: 100%">
        <el-table-column label="排名" width="70" align="center">
          <template #default="scope">
            <div class="ranking">{{ scope.$index + 1 }}</div>
          </template>
        </el-table-column>
        <el-table-column label="顶班人员" prop="name" min-width="120" />
        <el-table-column label="联系电话" prop="phone" width="120" />
        <el-table-column label="顶班次数" prop="count" width="120" align="center" sortable />
        <el-table-column label="完成率" width="150" align="center">
          <template #default="scope">
            <el-progress :percentage="scope.row.completionRate" :color="getCompletionRateColor(scope.row.completionRate)" />
          </template>
        </el-table-column>
        <el-table-column label="服务评分" width="150" align="center">
          <template #default="scope">
            <el-rate v-model="scope.row.rating" disabled text-color="#ff9900" />
          </template>
        </el-table-column>
        <el-table-column label="薪资累计" prop="totalSalary" width="120" align="right">
          <template #default="scope">
            {{ formatCurrency(scope.row.totalSalary) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="viewStaffDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 人员详情对话框 -->
    <el-dialog :title="'顶班人员详情 - ' + staffDetail.name" v-model="staffDetailVisible" width="700px">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="姓名">{{ staffDetail.name }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ staffDetail.phone }}</el-descriptions-item>
        <el-descriptions-item label="身份证号">{{ staffDetail.idCard }}</el-descriptions-item>
        <el-descriptions-item label="工作经验">{{ staffDetail.experience }}年</el-descriptions-item>
        <el-descriptions-item label="顶班次数">{{ staffDetail.count }}次</el-descriptions-item>
        <el-descriptions-item label="完成率">{{ staffDetail.completionRate }}%</el-descriptions-item>
        <el-descriptions-item label="服务评分">
          <el-rate v-model="staffDetail.rating" disabled text-color="#ff9900" />
        </el-descriptions-item>
        <el-descriptions-item label="累计薪资">{{ formatCurrency(staffDetail.totalSalary) }}</el-descriptions-item>
      </el-descriptions>
      
      <div class="mt20">
        <h4>顶班记录</h4>
        <el-table :data="staffDetail.substitutions" style="width: 100%">
          <el-table-column label="驿站名称" prop="stationName" min-width="180" :show-overflow-tooltip="true" />
          <el-table-column label="顶班类型" prop="typeText" width="120" />
          <el-table-column label="顶班日期" prop="dateRange" width="180" />
          <el-table-column label="薪资" prop="salary" width="120" align="right">
            <template #default="scope">
              {{ formatCurrency(scope.row.salary) }}
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="statusText" width="100">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.status)">{{ scope.row.statusText }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="评分" width="120" align="center">
            <template #default="scope">
              <el-rate v-model="scope.row.rating" disabled text-color="#ff9900" />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getSubstitutionStatistics, getSubstitutionStaff } from '@/api/substitution'

export default {
  name: 'SubstitutionStatistics',
  data() {
    return {
      dateRange: [],
      trendTimeUnit: 'day',
      loading: false,
      
      // 数据概览
      overview: {
        totalCount: 0,
        totalCountTrend: 0,
        completedCount: 0,
        completionRate: 0,
        staffCount: 0,
        avgSubstitutionsPerStaff: 0,
        totalSalary: 0,
        avgSalaryPerStaff: 0
      },
      
      // 图表实例
      charts: {
        trend: null,
        type: null,
        region: null,
        quality: null
      },
      
      // TOP顶班人员数据
      topStaff: [],
      
      // 人员详情
      staffDetail: {
        id: '',
        name: '',
        phone: '',
        idCard: '',
        experience: 0,
        count: 0,
        completionRate: 0,
        rating: 0,
        totalSalary: 0,
        substitutions: []
      },
      staffDetailVisible: false
    }
  },
  mounted() {
    // 设置默认日期范围为过去30天
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
    this.dateRange = [
      this.formatDate(start),
      this.formatDate(end)
    ]
    
    this.getStatisticsData()
    
    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', this.resizeCharts)
  },
  beforeUnmount() {
    // 销毁图表实例，避免内存泄漏
    Object.keys(this.charts).forEach(key => {
      if (this.charts[key]) {
        this.charts[key].dispose()
        this.charts[key] = null
      }
    })
    
    // 移除窗口大小变化的监听器
    window.removeEventListener('resize', this.resizeCharts)
  },
  methods: {
    // 获取统计数据
    async getStatisticsData() {
      this.loading = true
      try {
        // 构建查询参数
        const params = {}
        if (this.dateRange && this.dateRange.length === 2) {
          params.startDate = this.dateRange[0]
          params.endDate = this.dateRange[1]
        }
        params.timeUnit = this.trendTimeUnit
        
        // 调用API获取数据
        const res = await getSubstitutionStatistics(params)
        
        if (res.code === 0 && res.data) {
          // 更新概览数据
          this.overview = res.data.overview
          
          // 初始化图表
          this.$nextTick(() => {
            this.initTrendChart(res.data.trend)
            this.initTypeChart(res.data.typeDistribution)
            this.initRegionChart(res.data.regionDistribution)
            this.initQualityChart(res.data.qualityAnalysis)
          })
          
          // 更新TOP人员数据
          this.topStaff = res.data.topStaff
        } else {
          this.$message.error('获取统计数据失败')
        }
      } catch (error) {
        console.error('获取统计数据出错', error)
        this.$message.error('获取统计数据出错')
      } finally {
        this.loading = false
      }
    },
    
    // 日期范围变化处理
    handleDateRangeChange() {
      this.getStatisticsData()
    },
    
    // 趋势时间单位变化处理
    handleTrendTimeUnitChange() {
      this.getStatisticsData()
    },
    
    // 初始化趋势图表
    initTrendChart(data) {
      // 创建或获取图表实例
      if (!this.charts.trend) {
        this.charts.trend = echarts.init(document.getElementById('trendChart'))
      }
      
      // 图表配置
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['申请数', '发布数', '进行中', '已完成']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: data.xAxis
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '申请数',
            type: 'line',
            stack: '总量',
            areaStyle: { opacity: 0.1 },
            emphasis: {
              focus: 'series'
            },
            data: data.applied
          },
          {
            name: '发布数',
            type: 'line',
            stack: '总量',
            areaStyle: { opacity: 0.1 },
            emphasis: {
              focus: 'series'
            },
            data: data.published
          },
          {
            name: '进行中',
            type: 'line',
            stack: '总量',
            areaStyle: { opacity: 0.1 },
            emphasis: {
              focus: 'series'
            },
            data: data.ongoing
          },
          {
            name: '已完成',
            type: 'line',
            stack: '总量',
            areaStyle: { opacity: 0.1 },
            emphasis: {
              focus: 'series'
            },
            data: data.completed
          }
        ]
      }
      
      // 设置图表配置
      this.charts.trend.setOption(option)
    },
    
    // 初始化类型分布图表
    initTypeChart(data) {
      // 创建或获取图表实例
      if (!this.charts.type) {
        this.charts.type = echarts.init(document.getElementById('typeChart'))
      }
      
      // 图表配置
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: data.map(item => item.name)
        },
        series: [
          {
            name: '顶班类型',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      }
      
      // 设置图表配置
      this.charts.type.setOption(option)
    },
    
    // 初始化区域分布图表
    initRegionChart(data) {
      // 创建或获取图表实例
      if (!this.charts.region) {
        this.charts.region = echarts.init(document.getElementById('regionChart'))
      }
      
      // 图表配置
      const option = {
        tooltip: {
          trigger: 'item'
        },
        visualMap: {
          min: 0,
          max: Math.max(...data.map(item => item.value)),
          left: 'left',
          top: 'bottom',
          text: ['高', '低'],
          calculable: true
        },
        series: [
          {
            name: '区域分布',
            type: 'map',
            map: 'china',
            roam: true,
            label: {
              show: true
            },
            emphasis: {
              label: {
                show: true
              }
            },
            data: data
          }
        ]
      }
      
      // 设置图表配置
      this.charts.region.setOption(option)
    },
    
    // 初始化服务质量分析图表
    initQualityChart(data) {
      // 创建或获取图表实例
      if (!this.charts.quality) {
        this.charts.quality = echarts.init(document.getElementById('qualityChart'))
      }
      
      // 图表配置
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['5星', '4星', '3星', '2星', '1星']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: data.categories
        },
        series: [
          {
            name: '5星',
            type: 'bar',
            stack: '总量',
            data: data.fiveStar,
            itemStyle: { color: '#67C23A' }
          },
          {
            name: '4星',
            type: 'bar',
            stack: '总量',
            data: data.fourStar,
            itemStyle: { color: '#95D475' }
          },
          {
            name: '3星',
            type: 'bar',
            stack: '总量',
            data: data.threeStar,
            itemStyle: { color: '#E6A23C' }
          },
          {
            name: '2星',
            type: 'bar',
            stack: '总量',
            data: data.twoStar,
            itemStyle: { color: '#F56C6C' }
          },
          {
            name: '1星',
            type: 'bar',
            stack: '总量',
            data: data.oneStar,
            itemStyle: { color: '#909399' }
          }
        ]
      }
      
      // 设置图表配置
      this.charts.quality.setOption(option)
    },
    
    // 查看顶班人员详情
    async viewStaffDetail(staff) {
      try {
        const res = await getSubstitutionStaff(staff.id)
        if (res.code === 0 && res.data) {
          this.staffDetail = res.data
          this.staffDetailVisible = true
        } else {
          this.$message.error('获取人员详情失败')
        }
      } catch (error) {
        console.error('获取人员详情出错', error)
        this.$message.error('获取人员详情出错')
      }
    },
    
    // 格式化金额
    formatCurrency(value) {
      return '¥' + parseFloat(value).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')
    },
    
    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    
    // 获取完成率颜色
    getCompletionRateColor(rate) {
      if (rate >= 90) {
        return '#67C23A'
      } else if (rate >= 70) {
        return '#E6A23C'
      } else {
        return '#F56C6C'
      }
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        'PENDING': 'info',
        'APPROVED': 'success',
        'REJECTED': 'danger',
        'PUBLISHED': 'primary',
        'ONGOING': 'warning',
        'COMPLETED': 'success',
        'CANCELLED': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    // 调整图表大小
    resizeCharts() {
      Object.keys(this.charts).forEach(key => {
        if (this.charts[key]) {
          this.charts[key].resize()
        }
      })
    }
  }
}
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.mt20 {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-options {
  display: flex;
  align-items: center;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f7f7f7;
  border-radius: 4px;
  height: 100px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #409EFF;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.stat-icon :deep(.el-icon) {
  font-size: 24px;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-compare {
  font-size: 12px;
  color: #909399;
}

.up {
  color: #67C23A;
}

.down {
  color: #F56C6C;
}

.normal {
  color: #409EFF;
}

.ranking {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 50%;
  background: #f0f0f0;
  font-weight: bold;
}

.ranking:nth-child(1) {
  background: #f5c518;
  color: white;
}

.ranking:nth-child(2) {
  background: #c0c0c0;
  color: white;
}

.ranking:nth-child(3) {
  background: #cd7f32;
  color: white;
}
</style> 