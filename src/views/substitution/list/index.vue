<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="form-inline">
        <el-form-item label="顶班站点">
          <el-input v-model="queryParams.stationName" placeholder="请输入站点名称" clearable />
        </el-form-item>
        <el-form-item label="申请状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="(value, key) in statusMap"
              :key="key"
              :label="value"
              :value="key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="顶班类型">
          <el-select v-model="queryParams.type" placeholder="请选择类型" clearable>
            <el-option
              v-for="(value, key) in typeMap"
              :key="key"
              :label="value"
              :value="key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发布时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-tabs v-model="activeTab" @tab-click="handleTabChange">
      <el-tab-pane v-for="(value, key) in tabMap" :key="key" :label="value" :name="key">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['substitution:add']"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="Check"
              :disabled="single"
              @click="handleApprove"
              v-hasPermi="['substitution:approve']"
              v-show="activeTab === 'PENDING'"
            >审核通过</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Close"
              :disabled="single"
              @click="handleReject"
              v-hasPermi="['substitution:reject']"
              v-show="activeTab === 'PENDING'"
            >审核拒绝</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="Download"
              @click="handleExport"
              v-hasPermi="['substitution:export']"
            >导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="substitutionList"
          @selection-change="handleSelectionChange"
          border
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="顶班编号" align="center" prop="id" width="120" />
          <el-table-column label="驿站名称" align="center" prop="stationName" min-width="180" :show-overflow-tooltip="true" />
          <el-table-column label="顶班类型" align="center" prop="type" width="120">
            <template #default="scope">
              <el-tag :type="getTypeTag(scope.row.type)">{{ typeMap[scope.row.type] }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="顶班日期" align="center" width="180">
            <template #default="scope">
              {{ scope.row.startDate }} 至 {{ scope.row.endDate }}
            </template>
          </el-table-column>
          <el-table-column label="顶班时间" align="center" width="180" prop="workHours" />
          <el-table-column label="薪资" align="center" width="120">
            <template #default="scope">
              {{ scope.row.salary }}元/{{ scope.row.salaryUnit }}
            </template>
          </el-table-column>
          <el-table-column label="联系人" align="center" prop="contactName" width="100" />
          <el-table-column label="联系电话" align="center" prop="contactPhone" width="120" />
          <el-table-column label="状态" align="center" width="100">
            <template #default="scope">
              <el-tag :type="getStatusTag(scope.row.status)">{{ statusMap[scope.row.status] }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="申请日期" align="center" prop="createTime" width="180" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220">
            <template #default="scope">
              <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['substitution:view']">查看</el-button>
              <el-button 
                link 
                type="primary" 
                icon="Edit" 
                @click="handleUpdate(scope.row)" 
                v-hasPermi="['substitution:edit']"
                v-if="['PENDING', 'APPROVED', 'PUBLISHED'].includes(scope.row.status)"
              >编辑</el-button>
              
              <el-button 
                link 
                type="primary" 
                icon="Check" 
                @click="handleApprove(scope.row)" 
                v-hasPermi="['substitution:approve']"
                v-if="scope.row.status === 'PENDING'"
              >通过</el-button>
              
              <el-button 
                link 
                type="primary" 
                icon="Close" 
                @click="handleReject(scope.row)" 
                v-hasPermi="['substitution:reject']"
                v-if="scope.row.status === 'PENDING'"
              >拒绝</el-button>
              
              <el-button 
                link 
                type="primary" 
                icon="CircleCheck" 
                @click="handleMarkCompleted(scope.row)" 
                v-hasPermi="['substitution:complete']"
                v-if="scope.row.status === 'ONGOING'"
              >完成</el-button>
              
              <el-button 
                link 
                type="primary" 
                icon="Delete" 
                @click="handleDelete(scope.row)" 
                v-hasPermi="['substitution:delete']"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>

    <!-- 查看顶班信息对话框 -->
    <el-dialog :title="viewTitle" v-model="viewOpen" width="700px" append-to-body>
      <el-descriptions class="margin-top" :column="2" border>
        <el-descriptions-item label="顶班编号">{{ form.id }}</el-descriptions-item>
        <el-descriptions-item label="驿站名称">{{ form.stationName }}</el-descriptions-item>
        <el-descriptions-item label="顶班类型">{{ typeMap[form.type] }}</el-descriptions-item>
        <el-descriptions-item label="顶班日期">{{ form.startDate }} 至 {{ form.endDate }}</el-descriptions-item>
        <el-descriptions-item label="工作时间">{{ form.workHours }}</el-descriptions-item>
        <el-descriptions-item label="薪资">{{ form.salary }}元/{{ form.salaryUnit }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ form.contactName }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ form.contactPhone }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ statusMap[form.status] }}</el-descriptions-item>
        <el-descriptions-item label="申请日期">{{ form.createTime }}</el-descriptions-item>
        <el-descriptions-item label="顶班需求" :span="2">{{ form.requirements }}</el-descriptions-item>
        <el-descriptions-item label="工作内容" :span="2">{{ form.jobDescription }}</el-descriptions-item>
      </el-descriptions>
      
      <div v-if="form.staffInfo" class="margin-top">
        <h4>顶班人员信息</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="姓名">{{ form.staffInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ form.staffInfo.phone }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ form.staffInfo.idCard }}</el-descriptions-item>
          <el-descriptions-item label="工作经验">{{ form.staffInfo.experience }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <div v-if="form.evaluations && form.evaluations.length > 0" class="margin-top">
        <h4>服务评价</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in form.evaluations"
            :key="index"
            :timestamp="activity.createTime"
          >
            <el-card>
              <h4>评分: {{ activity.rating }}/5</h4>
              <p>{{ activity.comment }}</p>
              <p v-if="activity.reply" class="reply">回复: {{ activity.reply }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>

    <!-- 添加/修改顶班信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.open" width="700px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="驿站名称" prop="stationName">
          <el-input v-model="form.stationName" placeholder="请输入驿站名称" />
        </el-form-item>
        <el-form-item label="顶班类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择顶班类型" style="width: 100%">
            <el-option
              v-for="(value, key) in typeMap"
              :key="key"
              :label="value"
              :value="key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="顶班日期" prop="dateRange">
          <el-date-picker
            v-model="form.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="工作时间" prop="workHours">
          <el-input v-model="form.workHours" placeholder="例如：09:00-18:00" />
        </el-form-item>
        <el-form-item label="薪资" prop="salary">
          <el-input-number v-model="form.salary" :min="0" :precision="2" style="width: 70%" />
          <el-select v-model="form.salaryUnit" style="width: 28%; margin-left: 2%">
            <el-option label="天" value="天" />
            <el-option label="小时" value="小时" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系人" prop="contactName">
          <el-input v-model="form.contactName" placeholder="请输入联系人姓名" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="顶班需求" prop="requirements">
          <el-input v-model="form.requirements" type="textarea" rows="3" placeholder="请输入顶班需求，如：经验要求、技能要求等" />
        </el-form-item>
        <el-form-item label="工作内容" prop="jobDescription">
          <el-input v-model="form.jobDescription" type="textarea" rows="4" placeholder="请输入工作内容详情" />
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="dialog.type === 'edit'">
          <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
            <el-option
              v-for="(value, key) in editableStatusMap"
              :key="key"
              :label="value"
              :value="key"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog.open = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核拒绝对话框 -->
    <el-dialog title="审核拒绝" v-model="rejectDialog.open" width="500px" append-to-body>
      <el-form ref="rejectFormRef" :model="rejectForm" :rules="rejectRules">
        <el-form-item label="拒绝原因" prop="reason" label-width="100px">
          <el-input v-model="rejectForm.reason" type="textarea" rows="4" placeholder="请输入拒绝原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="rejectDialog.open = false">取 消</el-button>
          <el-button type="primary" @click="confirmReject">确 认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listSubstitution, 
  getSubstitution, 
  addSubstitution, 
  updateSubstitution, 
  delSubstitution, 
  approveSubstitution, 
  rejectSubstitution,
  completeSubstitution,
  exportSubstitution
} from '@/api/substitution';

export default {
  name: 'SubstitutionList',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 顶班列表数据
      substitutionList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查看对话框标题
      viewTitle: '查看顶班信息',
      // 查看对话框显示状态
      viewOpen: false,
      // 日期范围
      dateRange: [],
      // 当前激活的标签页
      activeTab: 'ALL',
      // 对话框信息
      dialog: {
        open: false,
        title: '',
        type: 'add' // add 或 edit
      },
      // 拒绝对话框
      rejectDialog: {
        open: false
      },
      // 拒绝表单
      rejectForm: {
        id: null,
        reason: ''
      },
      // 拒绝表单校验规则
      rejectRules: {
        reason: [
          { required: true, message: '请输入拒绝原因', trigger: 'blur' }
        ]
      },
      // 状态映射
      statusMap: {
        'PENDING': '待审核',
        'APPROVED': '已审核',
        'REJECTED': '已拒绝',
        'PUBLISHED': '已发布',
        'ONGOING': '进行中',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消'
      },
      // 可编辑的状态映射
      editableStatusMap: {
        'PENDING': '待审核',
        'APPROVED': '已审核',
        'PUBLISHED': '已发布',
        'CANCELLED': '已取消'
      },
      // 类型映射
      typeMap: {
        'FULL_TIME': '全职顶班',
        'PART_TIME': '兼职顶班',
        'TEMPORARY': '临时顶班',
        'EMERGENCY': '紧急顶班'
      },
      // 标签页映射
      tabMap: {
        'ALL': '全部',
        'PENDING': '待审核',
        'APPROVED': '已审核',
        'PUBLISHED': '已发布',
        'ONGOING': '进行中',
        'COMPLETED': '已完成',
        'REJECTED': '已拒绝',
        'CANCELLED': '已取消'
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stationName: undefined,
        status: undefined,
        type: undefined,
        startDate: undefined,
        endDate: undefined
      },
      // 表单参数
      form: {
        id: undefined,
        stationName: undefined,
        type: 'TEMPORARY',
        dateRange: [],
        startDate: undefined,
        endDate: undefined,
        workHours: undefined,
        salary: 0,
        salaryUnit: '天',
        contactName: undefined,
        contactPhone: undefined,
        requirements: undefined,
        jobDescription: undefined,
        status: 'PENDING',
        staffInfo: null,
        evaluations: []
      },
      // 表单校验
      rules: {
        stationName: [
          { required: true, message: '驿站名称不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '顶班类型不能为空', trigger: 'change' }
        ],
        dateRange: [
          { required: true, message: '顶班日期不能为空', trigger: 'change' }
        ],
        workHours: [
          { required: true, message: '工作时间不能为空', trigger: 'blur' }
        ],
        salary: [
          { required: true, message: '薪资不能为空', trigger: 'blur' }
        ],
        contactName: [
          { required: true, message: '联系人不能为空', trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '联系电话不能为空', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        requirements: [
          { required: true, message: '顶班需求不能为空', trigger: 'blur' }
        ],
        jobDescription: [
          { required: true, message: '工作内容不能为空', trigger: 'blur' }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询顶班列表 */
    getList() {
      this.loading = true;
      
      // 处理日期范围
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.startDate = this.dateRange[0];
        this.queryParams.endDate = this.dateRange[1];
      } else {
        this.queryParams.startDate = undefined;
        this.queryParams.endDate = undefined;
      }
      
      // 如果不是"全部"标签页，则添加状态过滤
      if (this.activeTab !== 'ALL') {
        this.queryParams.status = this.activeTab;
      }
      
      listSubstitution(this.queryParams).then(response => {
        this.substitutionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        stationName: undefined,
        status: undefined,
        type: undefined,
        startDate: undefined,
        endDate: undefined
      };
      if (this.activeTab !== 'ALL') {
        this.queryParams.status = this.activeTab;
      }
      this.handleQuery();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 标签页切换 */
    handleTabChange(tab) {
      this.activeTab = tab.props.name;
      if (this.activeTab === 'ALL') {
        this.queryParams.status = undefined;
      } else {
        this.queryParams.status = this.activeTab;
      }
      this.resetQuery();
    },
    /** 选择条目触发操作 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 获取状态标签类型 */
    getStatusTag(status) {
      const statusTagMap = {
        'PENDING': 'info',
        'APPROVED': 'success',
        'REJECTED': 'danger',
        'PUBLISHED': 'primary',
        'ONGOING': 'warning',
        'COMPLETED': 'success',
        'CANCELLED': 'info'
      };
      return statusTagMap[status] || 'info';
    },
    /** 获取类型标签类型 */
    getTypeTag(type) {
      const typeTagMap = {
        'FULL_TIME': 'primary',
        'PART_TIME': 'success',
        'TEMPORARY': 'warning',
        'EMERGENCY': 'danger'
      };
      return typeTagMap[type] || 'info';
    },
    /** 新增操作 */
    handleAdd() {
      this.resetForm();
      this.dialog.open = true;
      this.dialog.title = "添加顶班信息";
      this.dialog.type = "add";
      this.form.status = 'PENDING';
      this.form.type = 'TEMPORARY';
      this.form.salary = 0;
      this.form.salaryUnit = '天';
    },
    /** 修改操作 */
    handleUpdate(row) {
      this.resetForm();
      const id = row.id || this.ids[0];
      getSubstitution(id).then(response => {
        this.form = response.data;
        // 日期范围设置
        this.form.dateRange = [this.form.startDate, this.form.endDate];
        this.dialog.open = true;
        this.dialog.title = "修改顶班信息";
        this.dialog.type = "edit";
      });
    },
    /** 查看操作 */
    handleView(row) {
      getSubstitution(row.id).then(response => {
        this.form = response.data;
        this.viewOpen = true;
      });
    },
    /** 提交表单 */
    submitForm() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          // 处理日期范围
          if (this.form.dateRange && this.form.dateRange.length > 0) {
            this.form.startDate = this.form.dateRange[0];
            this.form.endDate = this.form.dateRange[1];
          }
          
          if (this.form.id) {
            updateSubstitution(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.dialog.open = false;
              this.getList();
            });
          } else {
            addSubstitution(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.dialog.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 审核通过操作 */
    handleApprove(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认审核通过顶班申请编号为"' + ids + '"的数据项？').then(() => {
        return approveSubstitution(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("审核通过成功");
      }).catch(() => {});
    },
    /** 审核拒绝操作 */
    handleReject(row) {
      this.rejectForm.id = row.id || this.ids[0];
      this.rejectForm.reason = '';
      this.rejectDialog.open = true;
    },
    /** 确认拒绝 */
    confirmReject() {
      this.$refs.rejectFormRef.validate(valid => {
        if (valid) {
          rejectSubstitution(this.rejectForm.id, this.rejectForm).then(response => {
            this.$modal.msgSuccess("审核拒绝成功");
            this.rejectDialog.open = false;
            this.getList();
          });
        }
      });
    },
    /** 标记完成操作 */
    handleMarkCompleted(row) {
      const id = row.id;
      this.$modal.confirm('是否确认将顶班申请编号为"' + id + '"标记为已完成？').then(() => {
        return completeSubstitution(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("操作成功");
      }).catch(() => {});
    },
    /** 删除操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除顶班申请编号为"' + ids + '"的数据项？').then(() => {
        return delSubstitution(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有顶班数据项？').then(() => {
        this.exportLoading = true;
        return exportSubstitution(this.queryParams);
      }).then(response => {
        this.$download.excel(response, '顶班数据.xlsx');
        this.exportLoading = false;
      }).catch(() => {});
    },
    // 重置表单
    resetForm() {
      this.form = {
        id: undefined,
        stationName: undefined,
        type: 'TEMPORARY',
        dateRange: [],
        startDate: undefined,
        endDate: undefined,
        workHours: undefined,
        salary: 0,
        salaryUnit: '天',
        contactName: undefined,
        contactPhone: undefined,
        requirements: undefined,
        jobDescription: undefined,
        status: 'PENDING',
        staffInfo: null,
        evaluations: []
      };
    }
  }
};
</script>

<style scoped>
.margin-top {
  margin-top: 15px;
}
.reply {
  color: #666;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px dashed #ddd;
}
</style>
