<template>
  <div class="user-list-container">
    <div class="page-header">
      <h2><i class="el-icon-user-solid"></i> 用户管理</h2>
      <p>管理系统中的所有用户信息</p>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable />
        </el-form-item>

        <el-form-item label="注册时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="userList"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="用户ID" width="80" />
        <el-table-column label="用户信息" min-width="200">
          <template #default="scope">
            <div class="user-info">
              <el-avatar :src="scope.row.avatar" :size="40">
                {{ scope.row.nickname ? scope.row.nickname.charAt(0) : '用' }}
              </el-avatar>
              <div class="user-details">
                <div class="nickname">{{ scope.row.nickname || '未设置' }}</div>
                <div class="phone">{{ scope.row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="is_verified" label="认证状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_verified ? 'success' : 'warning'">
              {{ scope.row.is_verified ? '已认证' : '未认证' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_login_time" label="最后登录" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.last_login_time) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="注册时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              <i class="el-icon-view"></i> 查看
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">
              <i class="el-icon-edit"></i> 编辑
            </el-button>
            <el-button type="text" size="small" class="danger" @click="handleDelete(scope.row)">
              <i class="el-icon-delete"></i> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UserList',
  data() {
    return {
      loading: false,
      userList: [],
      selectedUsers: [],
      searchForm: {
        phone: '',
        dateRange: []
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      }
    }
  },
  mounted() {
    this.loadUserList()
  },
  methods: {
    async loadUserList() {
      this.loading = true
      try {
        const response = await this.$http.get('/user/list', {
          params: this.getSearchParams()
        })

        if (response.data.code === 0) {
          this.userList = response.data.data.list
          this.pagination.total = response.data.data.total
        } else {
          this.$message.error(response.data.message || '获取用户列表失败')
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
        this.$message.error('加载用户列表失败')
      } finally {
        this.loading = false
      }
    },
    
    getSearchParams() {
      const params = {
        page: this.pagination.page,
        limit: this.pagination.limit
      }
      
      if (this.searchForm.phone) {
        params.phone = this.searchForm.phone
      }
      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
        params.startDate = this.searchForm.dateRange[0]
        params.endDate = this.searchForm.dateRange[1]
      }
      
      return params
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadUserList()
    },
    
    handleReset() {
      this.searchForm = {
        phone: '',
        dateRange: []
      }
      this.pagination.page = 1
      this.loadUserList()
    },
    
    handleSelectionChange(selection) {
      this.selectedUsers = selection
    },
    

    
    handleView(user) {
      this.$message.info('查看用户详情功能开发中')
    },
    
    handleEdit(user) {
      this.$message.info('编辑用户功能开发中')
    },
    
    handleDelete(user) {
      this.$confirm('确定要删除这个用户吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      })
    },
    
    handleSizeChange(size) {
      this.pagination.limit = size
      this.loadUserList()
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadUserList()
    },
    

    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.user-list-container {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .filter-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .user-details {
        .nickname {
          font-weight: 500;
          color: #303133;
        }
        
        .phone {
          font-size: 12px;
          color: #909399;
          margin-top: 2px;
        }
      }
    }
    
    .danger {
      color: #F56C6C;
    }
  }
  
  .pagination-container {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #EBEEF5;
  }
}
</style>
