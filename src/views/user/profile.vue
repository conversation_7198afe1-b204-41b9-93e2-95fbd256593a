<template>
  <div class="user-profile-container">
    <div class="page-header">
      <h2><i class="el-icon-user"></i> 用户资料管理</h2>
      <p>查看和管理用户详细资料信息</p>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable />
        </el-form-item>
        <el-form-item label="认证状态">
          <el-select v-model="searchForm.isVerified" placeholder="请选择" clearable>
            <el-option label="已认证" :value="true" />
            <el-option label="未认证" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="userList"
        style="width: 100%"
      >
        <el-table-column prop="id" label="用户ID" width="80" />
        <el-table-column label="用户信息" min-width="200">
          <template #default="scope">
            <div class="user-info">
              <el-avatar :src="scope.row.avatar" :size="40">
                {{ scope.row.nickname ? scope.row.nickname.charAt(0) : '用' }}
              </el-avatar>
              <div class="user-details">
                <div class="nickname">{{ scope.row.nickname || '未设置' }}</div>
                <div class="phone">{{ scope.row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="real_name" label="真实姓名" width="100">
          <template #default="scope">
            {{ scope.row.real_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="id_card" label="身份证号" width="180">
          <template #default="scope">
            {{ scope.row.id_card ? hideIdCard(scope.row.id_card) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="is_verified" label="认证状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_verified ? 'success' : 'warning'">
              {{ scope.row.is_verified ? '已认证' : '未认证' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="注册时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              <i class="el-icon-view"></i> 查看详情
            </el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">
              <i class="el-icon-edit"></i> 编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="用户详情"
      width="600px"
      :before-close="handleClose"
    >
      <div v-if="currentUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ currentUser.id }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ currentUser.phone }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ currentUser.nickname || '-' }}</el-descriptions-item>
          <el-descriptions-item label="真实姓名">{{ currentUser.real_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ currentUser.id_card || '-' }}</el-descriptions-item>
          <el-descriptions-item label="认证状态">
            <el-tag :type="currentUser.is_verified ? 'success' : 'warning'">
              {{ currentUser.is_verified ? '已认证' : '未认证' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ formatTime(currentUser.last_login_time) }}</el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ formatTime(currentUser.created_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'UserProfile',
  data() {
    return {
      loading: false,
      userList: [],
      searchForm: {
        phone: '',
        isVerified: null
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      dialogVisible: false,
      currentUser: null
    }
  },
  mounted() {
    this.loadUserList()
  },
  methods: {
    async loadUserList() {
      this.loading = true
      try {
        const response = await this.$http.get('/user/list', { 
          params: this.getSearchParams() 
        })
        
        if (response.data.code === 0) {
          this.userList = response.data.data.list
          this.pagination.total = response.data.data.total
        } else {
          this.$message.error(response.data.message || '获取用户列表失败')
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
        this.$message.error('加载用户列表失败')
      } finally {
        this.loading = false
      }
    },
    
    getSearchParams() {
      const params = {
        page: this.pagination.page,
        limit: this.pagination.limit
      }
      
      if (this.searchForm.phone) {
        params.phone = this.searchForm.phone
      }
      if (this.searchForm.isVerified !== null) {
        params.isVerified = this.searchForm.isVerified
      }
      
      return params
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadUserList()
    },
    
    handleReset() {
      this.searchForm = {
        phone: '',
        isVerified: null
      }
      this.pagination.page = 1
      this.loadUserList()
    },
    
    handleView(user) {
      this.currentUser = user
      this.dialogVisible = true
    },
    
    handleEdit(user) {
      this.$message.info('编辑用户功能开发中')
    },
    
    handleClose() {
      this.dialogVisible = false
      this.currentUser = null
    },
    
    handleSizeChange(size) {
      this.pagination.limit = size
      this.loadUserList()
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadUserList()
    },
    
    hideIdCard(idCard) {
      if (!idCard) return '-'
      return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.user-profile-container {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .filter-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .user-details {
        .nickname {
          font-weight: 500;
          color: #303133;
        }
        
        .phone {
          font-size: 12px;
          color: #909399;
          margin-top: 2px;
        }
      }
    }
  }
  
  .pagination-container {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #EBEEF5;
  }
  
  .user-detail {
    padding: 20px 0;
  }
}
</style>
