<template>
  <div class="user-auth-container">
    <div class="page-header">
      <h2><i class="el-icon-postcard"></i> 实名认证管理</h2>
      <p>审核和管理用户实名认证申请</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon total">
          <i class="el-icon-user-solid"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total }}</div>
          <div class="stat-label">总申请数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon pending">
          <i class="el-icon-time"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.pending }}</div>
          <div class="stat-label">待审核</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon approved">
          <i class="el-icon-check"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.approved }}</div>
          <div class="stat-label">已通过</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon rejected">
          <i class="el-icon-close"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.rejected }}</div>
          <div class="stat-label">已拒绝</div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable />
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input v-model="searchForm.realName" placeholder="请输入真实姓名" clearable />
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="authList"
        style="width: 100%"
      >
        <el-table-column prop="id" label="申请ID" width="80" />
        <el-table-column label="用户信息" min-width="180">
          <template #default="scope">
            <div class="user-info">
              <div class="phone">{{ scope.row.phone }}</div>
              <div class="nickname">{{ scope.row.nickname || '未设置' }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="real_name" label="真实姓名" width="100" />
        <el-table-column prop="id_card" label="身份证号" width="180">
          <template #default="scope">
            {{ hideIdCard(scope.row.id_card) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="审核状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="申请时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              <i class="el-icon-view"></i> 查看
            </el-button>
            <el-button 
              v-if="scope.row.status === 'pending'" 
              type="text" 
              size="small" 
              @click="handleApprove(scope.row)"
            >
              <i class="el-icon-check"></i> 审核
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="auditDialogVisible"
      title="实名认证审核"
      width="600px"
    >
      <div v-if="currentAuth" class="auth-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户手机号">{{ currentAuth.phone }}</el-descriptions-item>
          <el-descriptions-item label="用户昵称">{{ currentAuth.nickname || '-' }}</el-descriptions-item>
          <el-descriptions-item label="真实姓名">{{ currentAuth.real_name }}</el-descriptions-item>
          <el-descriptions-item label="身份证号">{{ currentAuth.id_card }}</el-descriptions-item>
          <el-descriptions-item label="申请时间" :span="2">{{ formatTime(currentAuth.created_at) }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="audit-form" style="margin-top: 20px;">
          <el-form :model="auditForm" label-width="80px">
            <el-form-item label="审核结果">
              <el-radio-group v-model="auditForm.status">
                <el-radio label="approved">通过</el-radio>
                <el-radio label="rejected">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="审核备注">
              <el-input
                v-model="auditForm.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入审核备注"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAudit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'UserAuth',
  data() {
    return {
      loading: false,
      authList: [],
      searchForm: {
        phone: '',
        realName: '',
        status: ''
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      stats: {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0
      },
      auditDialogVisible: false,
      currentAuth: null,
      auditForm: {
        status: 'approved',
        remark: ''
      }
    }
  },
  mounted() {
    this.loadStats()
    this.loadAuthList()
  },
  methods: {
    async loadStats() {
      // 暂时使用模拟数据
      this.stats = {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0
      }
    },
    
    async loadAuthList() {
      this.loading = true
      try {
        // 这里调用API获取实名认证列表
        // const response = await this.$http.get('/user/auth/list', { params: this.getSearchParams() })
        
        // 暂时使用空数据
        this.authList = []
        this.pagination.total = 0
      } catch (error) {
        console.error('加载认证列表失败:', error)
        this.$message.error('加载认证列表失败')
      } finally {
        this.loading = false
      }
    },
    
    getSearchParams() {
      const params = {
        page: this.pagination.page,
        limit: this.pagination.limit
      }
      
      if (this.searchForm.phone) {
        params.phone = this.searchForm.phone
      }
      if (this.searchForm.realName) {
        params.realName = this.searchForm.realName
      }
      if (this.searchForm.status) {
        params.status = this.searchForm.status
      }
      
      return params
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadAuthList()
    },
    
    handleReset() {
      this.searchForm = {
        phone: '',
        realName: '',
        status: ''
      }
      this.pagination.page = 1
      this.loadAuthList()
    },
    
    handleView(auth) {
      this.currentAuth = auth
      this.auditDialogVisible = true
    },
    
    handleApprove(auth) {
      this.currentAuth = auth
      this.auditForm = {
        status: 'approved',
        remark: ''
      }
      this.auditDialogVisible = true
    },
    
    async submitAudit() {
      try {
        // 这里调用API提交审核结果
        // const response = await this.$http.post(`/user/auth/${this.currentAuth.id}/audit`, this.auditForm)
        
        this.$message.success('审核成功')
        this.auditDialogVisible = false
        this.loadAuthList()
      } catch (error) {
        console.error('审核失败:', error)
        this.$message.error('审核失败')
      }
    },
    
    handleSizeChange(size) {
      this.pagination.limit = size
      this.loadAuthList()
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadAuthList()
    },
    
    hideIdCard(idCard) {
      if (!idCard) return '-'
      return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
    },
    
    getStatusColor(status) {
      const colorMap = {
        pending: 'warning',
        approved: 'success',
        rejected: 'danger'
      }
      return colorMap[status] || 'info'
    },
    
    getStatusText(status) {
      const textMap = {
        pending: '待审核',
        approved: '已通过',
        rejected: '已拒绝'
      }
      return textMap[status] || '未知'
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.user-auth-container {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        
        &.total { background: #909399; }
        &.pending { background: #E6A23C; }
        &.approved { background: #67C23A; }
        &.rejected { background: #F56C6C; }
      }
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .filter-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .user-info {
      .phone {
        font-weight: 500;
        color: #303133;
      }
      
      .nickname {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
  }
  
  .pagination-container {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #EBEEF5;
  }
  
  .auth-detail {
    padding: 20px 0;
  }
}
</style>
