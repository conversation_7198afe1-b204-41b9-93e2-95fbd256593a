<template>
  <div class="app-container">
    <el-card class="filter-container">
      <div class="filter-item">
        <el-input v-model="listQuery.search" placeholder="搜索用户昵称/手机" clearable @keyup.enter.native="handleFilter" style="width: 200px;" />
        <el-select v-model="listQuery.status" placeholder="提现状态" clearable style="width: 130px" @change="handleFilter">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          style="width: 260px;"
          @change="handleDateChange"
        />
        <el-button v-waves type="primary" icon="Search" @click="handleFilter">搜索</el-button>
        <el-button v-waves icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
    </el-card>

    <el-card>
      <div class="table-header">
        <div class="statistics-cards">
          <div class="stat-card primary">
            <div class="stat-value">{{ statistics.totalCount }}</div>
            <div class="stat-label">总申请数</div>
          </div>
          <div class="stat-card warning">
            <div class="stat-value price-text">¥{{ statistics.totalAmount }}</div>
            <div class="stat-label">总提现金额</div>
          </div>
          <div class="stat-card info">
            <div class="stat-value">{{ statistics.pendingCount }}</div>
            <div class="stat-label">待处理申请</div>
          </div>
          <div class="stat-card danger">
            <div class="stat-value price-text">¥{{ statistics.pendingAmount }}</div>
            <div class="stat-label">待处理金额</div>
          </div>
        </div>
        <div>
          <el-button type="success" size="small" @click="exportData">导出数据</el-button>
        </div>
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column label="ID" prop="id" align="center" width="80" />
        <el-table-column label="用户信息" min-width="150">
          <template #default="{row}">
            <div class="user-info">
              <el-avatar :src="row.user_avatar || '/static/default-avatar.png'" :size="30"></el-avatar>
              <div class="user-detail">
                <div>{{ row.user_nickname }}</div>
                <div class="user-phone">{{ row.user_phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请时间" width="150" align="center">
          <template #default="{row}">
            <span>{{ formatDateTime(row.created_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="提现金额" prop="amount" align="center" width="120">
          <template #default="{row}">
            <span class="price-text">¥{{ row.amount.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="账户类型" align="center" width="100">
          <template #default="{row}">
            {{ getAccountTypeText(row.account_type) }}
          </template>
        </el-table-column>
        <el-table-column label="账户信息" min-width="150">
          <template #default="{row}">
            <div>{{ row.real_name }}</div>
            <div v-if="row.account_type === 3">{{ row.bank_name }}</div>
            <div class="account-number">{{ formatAccountNumber(row.account_number, row.account_type) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" align="center" width="100">
          <template #default="{row}">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="处理时间" width="150" align="center">
          <template #default="{row}">
            <span v-if="row.processed_at">{{ formatDateTime(row.processed_at) }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template #default="{row}">
            <el-button v-if="row.status === 0" type="success" size="small" @click="handleApprove(row)">通过</el-button>
            <el-button v-if="row.status === 0" type="danger" size="small" @click="handleReject(row)">拒绝</el-button>
            <el-button v-if="row.status === 1" type="primary" size="small" @click="handleTransfer(row)">已打款</el-button>
            <el-button type="info" size="small" @click="handleDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </el-card>

    <!-- 提现详情对话框 -->
    <el-dialog title="提现详情" v-model="dialogVisible" width="500px">
      <div v-loading="detailLoading" class="withdrawal-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="申请编号">{{ detail.id }}</el-descriptions-item>
          <el-descriptions-item label="申请用户">
            <div class="user-info">
              <el-avatar :src="detail.user_avatar || '/static/default-avatar.png'" :size="30"></el-avatar>
              <div class="user-detail">
                <div>{{ detail.user_nickname }}</div>
                <div class="user-phone">{{ detail.user_phone }}</div>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="提现金额">
            <span class="price-text">¥{{ detail.amount && detail.amount.toFixed(2) }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="收款账户">
            <div>{{ detail.real_name }}</div>
            <div>{{ getAccountTypeText(detail.account_type) }}</div>
            <div v-if="detail.account_type === 3">{{ detail.bank_name }}</div>
            <div>{{ detail.account_number }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ formatDateTime(detail.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="detail.status !== undefined" :type="getStatusType(detail.status)">
              {{ getStatusText(detail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="处理时间">
            {{ detail.processed_at ? formatDateTime(detail.processed_at) : '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="处理备注">
            {{ detail.remark || '--' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <template v-if="detail.status === 0">
            <el-button type="success" @click="handleApprove(detail)">通过</el-button>
            <el-button type="danger" @click="handleReject(detail)">拒绝</el-button>
          </template>
          <el-button v-if="detail.status === 1" type="primary" @click="handleTransfer(detail)">已打款</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog :title="auditAction === 'approve' ? '通过提现申请' : '拒绝提现申请'" v-model="auditDialogVisible" width="400px">
      <el-form :model="auditForm" label-width="80px">
        <el-form-item label="备注">
          <el-input
            v-model="auditForm.remark"
            type="textarea"
            :rows="3"
            :placeholder="auditAction === 'approve' ? '通过原因（选填）' : '拒绝原因（必填）'"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取 消</el-button>
          <el-button :type="auditAction === 'approve' ? 'success' : 'danger'" @click="submitAudit" :loading="auditSubmitting">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 打款确认对话框 -->
    <el-dialog title="确认打款" v-model="transferDialogVisible" width="400px">
      <el-form :model="transferForm" label-width="80px">
        <el-form-item label="收款方">
          {{ transferTarget.real_name }}
        </el-form-item>
        <el-form-item label="账户类型">
          {{ getAccountTypeText(transferTarget.account_type) }}
        </el-form-item>
        <el-form-item v-if="transferTarget.account_type === 3" label="银行名称">
          {{ transferTarget.bank_name }}
        </el-form-item>
        <el-form-item label="账号">
          {{ transferTarget.account_number }}
        </el-form-item>
        <el-form-item label="金额">
          <span class="price-text">¥{{ transferTarget.amount && transferTarget.amount.toFixed(2) }}</span>
        </el-form-item>
        <el-form-item label="打款凭证">
          <el-upload
            class="upload-demo"
            action="/api/upload/image"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :file-list="fileList"
            list-type="picture"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="transferForm.remark" type="textarea" :rows="2"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="transferDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitTransfer" :loading="transferSubmitting">确认已打款</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { formatDateTime } from '@/utils/index'

export default {
  name: 'WithdrawalList',
  components: { Pagination },
  directives: { waves },
  setup() {
    const dateRange = ref([])
    const list = ref([])
    const total = ref(0)
    const listLoading = ref(false)
    const dialogVisible = ref(false)
    const detailLoading = ref(false)
    const detail = ref({})
    const auditDialogVisible = ref(false)
    const auditSubmitting = ref(false)
    const auditAction = ref('approve')
    const currentRow = ref(null)
    const transferDialogVisible = ref(false)
    const transferSubmitting = ref(false)
    const transferTarget = ref({})
    const fileList = ref([])
    
    const listQuery = reactive({
      page: 1,
      limit: 10,
      status: '',
      search: '',
      start_date: '',
      end_date: ''
    })
    
    const statistics = reactive({
      totalCount: 0,
      totalAmount: 0,
      pendingCount: 0,
      pendingAmount: 0
    })
    
    const auditForm = reactive({
      id: null,
      status: 0,
      remark: ''
    })
    
    const transferForm = reactive({
      id: null,
      proof_image: '',
      remark: ''
    })
    
    const statusOptions = [
      { label: '待审核', value: 0 },
      { label: '已通过', value: 1 },
      { label: '已打款', value: 2 },
      { label: '已拒绝', value: 3 }
    ]
    
    // 模拟数据
    const mockList = [
      {
        id: 1,
        user_id: 101,
        user_nickname: '张三',
        user_phone: '138****1234',
        user_avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        amount: 1000.00,
        account_type: 1,
        real_name: '张三',
        account_number: 'zhang_san888',
        bank_name: '',
        created_at: '2023-05-15 09:30:00',
        processed_at: null,
        status: 0,
        remark: ''
      },
      {
        id: 2,
        user_id: 102,
        user_nickname: '李四',
        user_phone: '139****5678',
        user_avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        amount: 2500.00,
        account_type: 2,
        real_name: '李四',
        account_number: '***********',
        bank_name: '',
        created_at: '2023-05-14 14:20:00',
        processed_at: '2023-05-14 15:30:00',
        status: 1,
        remark: '通过审核'
      },
      {
        id: 3,
        user_id: 103,
        user_nickname: '王五',
        user_phone: '137****9012',
        user_avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        amount: 5000.00,
        account_type: 3,
        real_name: '王五',
        account_number: '****************',
        bank_name: '招商银行',
        created_at: '2023-05-13 10:15:00',
        processed_at: '2023-05-13 11:20:00',
        status: 2,
        remark: '已完成打款'
      },
      {
        id: 4,
        user_id: 104,
        user_nickname: '赵六',
        user_phone: '136****3456',
        user_avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        amount: 1200.00,
        account_type: 1,
        real_name: '赵六',
        account_number: 'zhao_liu666',
        bank_name: '',
        created_at: '2023-05-12 16:45:00',
        processed_at: '2023-05-12 17:30:00',
        status: 3,
        remark: '账户信息不正确，请修改后重新申请'
      }
    ]
    
    const getList = () => {
      listLoading.value = true
      
      // 模拟API调用
      setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredList = [...mockList]
        
        if (listQuery.status !== '') {
          filteredList = filteredList.filter(item => item.status === listQuery.status)
        }
        
        if (listQuery.search) {
          filteredList = filteredList.filter(item => 
            item.user_nickname.includes(listQuery.search) || 
            item.user_phone.includes(listQuery.search)
          )
        }
        
        if (listQuery.start_date && listQuery.end_date) {
          // 简化处理，实际项目中应该使用日期比较
          // 这里只是模拟筛选效果
        }
        
        list.value = filteredList
        total.value = filteredList.length
        
        // 更新统计数据
        updateStatistics(filteredList)
        
        listLoading.value = false
      }, 500)
    }
    
    const updateStatistics = (data) => {
      let totalAmount = 0
      let pendingCount = 0
      let pendingAmount = 0
      
      data.forEach(item => {
        totalAmount += item.amount
        
        if (item.status === 0) {
          pendingCount++
          pendingAmount += item.amount
        }
      })
      
      statistics.totalCount = data.length
      statistics.totalAmount = totalAmount.toFixed(2)
      statistics.pendingCount = pendingCount
      statistics.pendingAmount = pendingAmount.toFixed(2)
    }
    
    const handleFilter = () => {
      listQuery.page = 1
      getList()
    }
    
    const resetQuery = () => {
      dateRange.value = []
      Object.assign(listQuery, {
        page: 1,
        limit: 10,
        status: '',
        search: '',
        start_date: '',
        end_date: ''
      })
      getList()
    }
    
    const handleDateChange = (val) => {
      if (val) {
        listQuery.start_date = val[0]
        listQuery.end_date = val[1]
      } else {
        listQuery.start_date = ''
        listQuery.end_date = ''
      }
    }
    
    const getStatusType = (status) => {
      const statusMap = {
        0: 'warning',
        1: 'success',
        2: 'primary',
        3: 'danger'
      }
      return statusMap[status]
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已打款',
        3: '已拒绝'
      }
      return statusMap[status]
    }
    
    const getAccountTypeText = (type) => {
      const typeMap = {
        1: '支付宝',
        2: '微信',
        3: '银行卡'
      }
      return typeMap[type] || '未知'
    }
    
    const formatAccountNumber = (number, type) => {
      if (!number) return ''
      
      // 根据不同账户类型格式化显示
      if (type === 3) {
        // 银行卡号脱敏处理
        return number.replace(/^(\d{4})(\d+)(\d{4})$/, '$1 **** **** $3')
      } else {
        // 支付宝/微信账号
        if (number.includes('@')) {
          // 邮箱格式
          const parts = number.split('@')
          const name = parts[0].length > 3 ? 
            parts[0].substring(0, 3) + '****' : 
            parts[0].substring(0, 1) + '****'
          return name + '@' + parts[1]
        } else if (/^\d+$/.test(number)) {
          // 手机号格式
          return number.replace(/^(\d{3})(\d+)(\d{4})$/, '$1****$3')
        } else {
          // 用户名格式
          return number.substring(0, 3) + '****'
        }
      }
    }
    
    const handleDetail = (row) => {
      detailLoading.value = true
      detail.value = { ...row }
      dialogVisible.value = true
      
      setTimeout(() => {
        detailLoading.value = false
      }, 300)
    }
    
    const handleApprove = (row) => {
      currentRow.value = row
      auditAction.value = 'approve'
      auditForm.id = row.id
      auditForm.status = 1
      auditForm.remark = ''
      auditDialogVisible.value = true
    }
    
    const handleReject = (row) => {
      currentRow.value = row
      auditAction.value = 'reject'
      auditForm.id = row.id
      auditForm.status = 3
      auditForm.remark = ''
      auditDialogVisible.value = true
    }
    
    const submitAudit = () => {
      if (auditAction.value === 'reject' && !auditForm.remark) {
        ElMessage.warning('请填写拒绝原因')
        return
      }
      
      auditSubmitting.value = true
      
      // 模拟API调用
      setTimeout(() => {
        // 更新本地列表数据
        const index = list.value.findIndex(item => item.id === auditForm.id)
        if (index > -1) {
          list.value[index].status = auditForm.status
          list.value[index].processed_at = formatDateTime(new Date())
          list.value[index].remark = auditForm.remark
          
          // 更新详情数据（如果当前正在查看详情）
          if (detail.value.id === auditForm.id) {
            detail.value.status = auditForm.status
            detail.value.processed_at = formatDateTime(new Date())
            detail.value.remark = auditForm.remark
          }
        }
        
        // 更新统计数据
        updateStatistics(list.value)
        
        auditSubmitting.value = false
        auditDialogVisible.value = false
        
        ElMessage({
          type: 'success',
          message: auditAction.value === 'approve' ? '提现申请已通过' : '提现申请已拒绝'
        })
      }, 500)
    }
    
    const handleTransfer = (row) => {
      transferTarget.value = { ...row }
      transferForm.id = row.id
      transferForm.proof_image = ''
      transferForm.remark = ''
      fileList.value = []
      transferDialogVisible.value = true
    }
    
    const handleUploadSuccess = (response) => {
      transferForm.proof_image = response.url
      ElMessage.success('上传成功')
    }
    
    const handleUploadError = () => {
      ElMessage.error('上传失败')
    }
    
    const beforeUpload = (file) => {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isJPG && !isPNG) {
        ElMessage.error('上传图片只能是 JPG 或 PNG 格式!')
      }
      
      if (!isLt2M) {
        ElMessage.error('上传图片大小不能超过 2MB!')
      }
      
      return (isJPG || isPNG) && isLt2M
    }
    
    const submitTransfer = () => {
      if (!transferForm.proof_image) {
        ElMessage.warning('请上传打款凭证')
        return
      }
      
      transferSubmitting.value = true
      
      // 模拟API调用
      setTimeout(() => {
        // 更新本地列表数据
        const index = list.value.findIndex(item => item.id === transferForm.id)
        if (index > -1) {
          list.value[index].status = 2
          list.value[index].processed_at = formatDateTime(new Date())
          list.value[index].remark = transferForm.remark
          
          // 更新详情数据（如果当前正在查看详情）
          if (detail.value.id === transferForm.id) {
            detail.value.status = 2
            detail.value.processed_at = formatDateTime(new Date())
            detail.value.remark = transferForm.remark
          }
        }
        
        // 更新统计数据
        updateStatistics(list.value)
        
        transferSubmitting.value = false
        transferDialogVisible.value = false
        
        ElMessage({
          type: 'success',
          message: '确认打款成功'
        })
      }, 500)
    }
    
    const exportData = () => {
      ElMessage.success('数据导出功能开发中')
    }
    
    onMounted(() => {
      getList()
    })
    
    return {
      dateRange,
      list,
      total,
      listLoading,
      listQuery,
      statistics,
      statusOptions,
      dialogVisible,
      detailLoading,
      detail,
      auditDialogVisible,
      auditSubmitting,
      auditAction,
      auditForm,
      transferDialogVisible,
      transferSubmitting,
      transferTarget,
      transferForm,
      fileList,
      formatDateTime,
      handleFilter,
      resetQuery,
      handleDateChange,
      getStatusType,
      getStatusText,
      getAccountTypeText,
      formatAccountNumber,
      handleDetail,
      handleApprove,
      handleReject,
      submitAudit,
      handleTransfer,
      handleUploadSuccess,
      handleUploadError,
      beforeUpload,
      submitTransfer,
      exportData
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
  
  .filter-item {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .statistics-cards {
    display: flex;
    gap: 15px;
    
    .stat-card {
      padding: 15px;
      border-radius: 4px;
      width: 130px;
      text-align: center;
      
      &.primary {
        background-color: #ecf5ff;
        color: #409eff;
      }
      
      &.warning {
        background-color: #fdf6ec;
        color: #e6a23c;
      }
      
      &.info {
        background-color: #f4f4f5;
        color: #909399;
      }
      
      &.danger {
        background-color: #fef0f0;
        color: #f56c6c;
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
        
        &.price-text {
          color: #f56c6c;
        }
      }
      
      .stat-label {
        font-size: 14px;
      }
    }
  }
}

.user-info {
  display: flex;
  align-items: center;
  
  .user-detail {
    margin-left: 10px;
    
    .user-phone {
      color: #909399;
      font-size: 13px;
    }
  }
}

.price-text {
  color: #f56c6c;
  font-weight: bold;
}

.account-number {
  color: #909399;
  font-size: 13px;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

.withdrawal-detail {
  min-height: 200px;
}
</style>
