<template>
  <div class="app-container">
    <el-card class="qrcode-generator-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>推广二维码生成器</span>
        </div>
      </template>
      
      <div class="generator-content">
        <div class="settings-panel">
          <el-form :model="qrcodeForm" label-width="100px">
            <el-form-item label="推广员">
              <el-select v-model="qrcodeForm.promoter" filterable placeholder="选择推广员" style="width: 100%">
                <el-option
                  v-for="item in promoterOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                  <div class="user-option">
                    <el-avatar :src="item.avatar" :size="30"></el-avatar>
                    <div>
                      <div>{{ item.name }}</div>
                      <div class="user-phone">{{ item.phone }}</div>
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="推广类型">
              <el-radio-group v-model="qrcodeForm.type">
                <el-radio-button label="user">用户推广</el-radio-button>
                <el-radio-button label="station">驿站推广</el-radio-button>
                <el-radio-button label="service">服务推广</el-radio-button>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item v-if="qrcodeForm.type === 'service'" label="推广服务">
              <el-select v-model="qrcodeForm.serviceId" filterable placeholder="选择推广服务" style="width: 100%">
                <el-option
                  v-for="item in serviceOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                  {{ item.name }} (¥{{ item.price }})
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="佣金比例">
              <el-input-number v-model="qrcodeForm.commissionRate" :min="0" :max="30" :step="0.5" :precision="1" style="width: 180px"></el-input-number>
              <span class="commission-rate-suffix">%</span>
            </el-form-item>
            
            <el-form-item label="过期时间">
              <el-date-picker
                v-model="qrcodeForm.expireDate"
                type="date"
                placeholder="选择过期日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                :disabled-date="disabledDate"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
            
            <el-form-item label="背景颜色">
              <el-color-picker v-model="qrcodeForm.bgColor" show-alpha></el-color-picker>
            </el-form-item>
            
            <el-form-item label="前景颜色">
              <el-color-picker v-model="qrcodeForm.fgColor"></el-color-picker>
            </el-form-item>
            
            <el-form-item label="添加Logo">
              <el-switch v-model="qrcodeForm.addLogo"></el-switch>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="generateQrcode" :loading="generating">生成二维码</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="preview-panel">
          <div class="qrcode-preview" v-loading="generating">
            <div v-if="qrcodeUrl" class="qrcode-container">
              <img :src="qrcodeUrl" alt="推广二维码" class="qrcode-image" />
              <div class="qrcode-info">
                <div>推广员: {{ getPromoterName(qrcodeForm.promoter) }}</div>
                <div>类型: {{ getTypeName(qrcodeForm.type) }}</div>
                <div v-if="qrcodeForm.type === 'service'">服务: {{ getServiceName(qrcodeForm.serviceId) }}</div>
                <div>佣金比例: {{ qrcodeForm.commissionRate }}%</div>
                <div>过期时间: {{ qrcodeForm.expireDate || '永久有效' }}</div>
              </div>
              <div class="qrcode-actions">
                <el-button type="primary" size="small" @click="downloadQrcode">
                  <el-icon><Download /></el-icon> 下载
                </el-button>
                <el-button type="success" size="small" @click="saveQrcode">
                  <el-icon><Check /></el-icon> 保存
                </el-button>
              </div>
            </div>
            <div v-else class="empty-preview">
              <el-icon :size="48"><Picture /></el-icon>
              <p>请设置参数生成二维码</p>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    
    <el-card class="qrcode-list-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>已生成的推广二维码</span>
          <div>
            <el-input
              v-model="searchQuery"
              placeholder="搜索推广员"
              clearable
              @clear="getQrcodeList"
              style="width: 200px; margin-right: 10px;"
            >
              <template #suffix>
                <el-icon @click="getQrcodeList"><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="filterType" placeholder="推广类型" clearable @change="getQrcodeList" style="width: 130px; margin-right: 10px;">
              <el-option label="用户推广" value="user" />
              <el-option label="驿站推广" value="station" />
              <el-option label="服务推广" value="service" />
            </el-select>
            <el-button type="primary" @click="getQrcodeList">查询</el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="qrcodeList"
        border
        style="width: 100%"
      >
        <el-table-column label="二维码" width="100">
          <template #default="{row}">
            <div class="qrcode-thumbnail">
              <el-image
                :src="row.url"
                :preview-src-list="[row.url]"
                fit="contain"
                :z-index="3000"
                style="width: 60px; height: 60px;"
              ></el-image>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="promoter_name" label="推广员" width="150"></el-table-column>
        <el-table-column label="推广类型" width="100">
          <template #default="{row}">
            <el-tag :type="getTypeTagType(row.type)">{{ getTypeName(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="target_name" label="推广对象" width="180"></el-table-column>
        <el-table-column prop="commission_rate" label="佣金比例" width="100">
          <template #default="{row}">
            {{ row.commission_rate }}%
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="150"></el-table-column>
        <el-table-column prop="expire_date" label="过期时间" width="150">
          <template #default="{row}">
            {{ row.expire_date || '永久有效' }}
          </template>
        </el-table-column>
        <el-table-column prop="scan_count" label="扫码次数" width="100" sortable></el-table-column>
        <el-table-column prop="conversion_count" label="转化数" width="100" sortable></el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{row}">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
            <el-button type="success" size="small" plain @click="handleDownload(row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-card>
    
    <!-- 二维码详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="二维码详情"
      width="500px"
    >
      <div class="qrcode-detail" v-loading="detailLoading">
        <div class="qrcode-detail-image">
          <el-image :src="currentQrcode.url" fit="contain" style="width: 200px; height: 200px;"></el-image>
        </div>
        <div class="qrcode-detail-info">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="推广员">{{ currentQrcode.promoter_name }}</el-descriptions-item>
            <el-descriptions-item label="推广类型">
              <el-tag :type="getTypeTagType(currentQrcode.type)">{{ getTypeName(currentQrcode.type) }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="推广对象">{{ currentQrcode.target_name }}</el-descriptions-item>
            <el-descriptions-item label="佣金比例">{{ currentQrcode.commission_rate }}%</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ currentQrcode.created_at }}</el-descriptions-item>
            <el-descriptions-item label="过期时间">{{ currentQrcode.expire_date || '永久有效' }}</el-descriptions-item>
            <el-descriptions-item label="扫码次数">{{ currentQrcode.scan_count }}</el-descriptions-item>
            <el-descriptions-item label="转化数">{{ currentQrcode.conversion_count }}</el-descriptions-item>
            <el-descriptions-item label="转化率">{{ calculateConversionRate(currentQrcode) }}%</el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="qrcode-detail-actions">
          <el-button type="primary" @click="handleDetailDownload">下载二维码</el-button>
          <el-button type="danger" @click="handleDetailDelete">删除二维码</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture, Download, Check, Search } from '@element-plus/icons-vue'

export default {
  name: 'PromotionCodeManager',
  components: {
    Picture,
    Download,
    Check,
    Search
  },
  setup() {
    // 表单数据
    const qrcodeForm = reactive({
      promoter: '',
      type: 'user',
      serviceId: '',
      commissionRate: 5.0,
      expireDate: '',
      bgColor: '#FFFFFF',
      fgColor: '#000000',
      addLogo: true
    })
    
    // 状态管理
    const generating = ref(false)
    const qrcodeUrl = ref('')
    const loading = ref(false)
    const dialogVisible = ref(false)
    const detailLoading = ref(false)
    const currentQrcode = ref({})
    const searchQuery = ref('')
    const filterType = ref('')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    
    // 模拟数据
    const promoterOptions = [
      { id: 1, name: '张三', phone: '13811112222', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
      { id: 2, name: '李四', phone: '13922223333', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
      { id: 3, name: '王五', phone: '13933334444', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
      { id: 4, name: '赵六', phone: '13944445555', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' }
    ]
    
    const serviceOptions = [
      { id: 1, name: '快递代取', price: 3.5 },
      { id: 2, name: '外卖代取', price: 5.0 },
      { id: 3, name: '保洁服务', price: 80.0 },
      { id: 4, name: '上门维修', price: 100.0 }
    ]
    
    const mockQrcodeList = [
      {
        id: 1,
        url: 'https://imgse.com/i/p9T2O4s',
        promoter_id: 1,
        promoter_name: '张三',
        type: 'user',
        target_id: null,
        target_name: '用户注册',
        commission_rate: 10.0,
        created_at: '2023-05-01 09:30:00',
        expire_date: '2023-12-31',
        scan_count: 256,
        conversion_count: 43
      },
      {
        id: 2,
        url: 'https://imgse.com/i/p9T2O4s',
        promoter_id: 2,
        promoter_name: '李四',
        type: 'station',
        target_id: 5,
        target_name: '中关村驿站',
        commission_rate: 15.0,
        created_at: '2023-05-05 14:20:00',
        expire_date: null,
        scan_count: 128,
        conversion_count: 25
      },
      {
        id: 3,
        url: 'https://imgse.com/i/p9T2O4s',
        promoter_id: 1,
        promoter_name: '张三',
        type: 'service',
        target_id: 1,
        target_name: '快递代取',
        commission_rate: 8.0,
        created_at: '2023-05-10 11:45:00',
        expire_date: '2023-08-31',
        scan_count: 89,
        conversion_count: 32
      }
    ]
    
    const qrcodeList = ref([...mockQrcodeList])
    
    // 计算属性
    const calculateConversionRate = (qrcode) => {
      if (!qrcode.scan_count) return 0
      return ((qrcode.conversion_count / qrcode.scan_count) * 100).toFixed(2)
    }
    
    // 方法
    const getPromoterName = (id) => {
      const promoter = promoterOptions.find(item => item.id === id)
      return promoter ? promoter.name : '未指定'
    }
    
    const getTypeName = (type) => {
      const typeMap = {
        user: '用户推广',
        station: '驿站推广',
        service: '服务推广'
      }
      return typeMap[type] || '未知'
    }
    
    const getTypeTagType = (type) => {
      const typeMap = {
        user: 'primary',
        station: 'success',
        service: 'warning'
      }
      return typeMap[type] || 'info'
    }
    
    const getServiceName = (id) => {
      const service = serviceOptions.find(item => item.id === parseInt(id))
      return service ? service.name : '未指定'
    }
    
    const disabledDate = (time) => {
      return time.getTime() < Date.now()
    }
    
    const generateQrcode = () => {
      if (!qrcodeForm.promoter) {
        ElMessage.warning('请选择推广员')
        return
      }
      
      if (qrcodeForm.type === 'service' && !qrcodeForm.serviceId) {
        ElMessage.warning('请选择推广服务')
        return
      }
      
      generating.value = true
      
      // 模拟API调用生成二维码
      setTimeout(() => {
        // 实际项目中，这里应该是从后端获取生成的二维码URL
        qrcodeUrl.value = 'https://imgse.com/i/p9T2O4s'
        generating.value = false
        
        ElMessage.success('二维码生成成功')
      }, 1000)
    }
    
    const resetForm = () => {
      Object.assign(qrcodeForm, {
        promoter: '',
        type: 'user',
        serviceId: '',
        commissionRate: 5.0,
        expireDate: '',
        bgColor: '#FFFFFF',
        fgColor: '#000000',
        addLogo: true
      })
      qrcodeUrl.value = ''
    }
    
    const downloadQrcode = () => {
      // 实际项目中，这里应该是下载二维码图片的逻辑
      ElMessage.success('二维码下载成功')
    }
    
    const saveQrcode = () => {
      // 模拟保存二维码到数据库
      const newQrcode = {
        id: qrcodeList.value.length + 1,
        url: qrcodeUrl.value,
        promoter_id: qrcodeForm.promoter,
        promoter_name: getPromoterName(qrcodeForm.promoter),
        type: qrcodeForm.type,
        target_id: qrcodeForm.type === 'service' ? qrcodeForm.serviceId : null,
        target_name: qrcodeForm.type === 'service' ? getServiceName(qrcodeForm.serviceId) : 
                    qrcodeForm.type === 'user' ? '用户注册' : '驿站推广',
        commission_rate: qrcodeForm.commissionRate,
        created_at: new Date().toLocaleString(),
        expire_date: qrcodeForm.expireDate,
        scan_count: 0,
        conversion_count: 0
      }
      
      // 添加到列表
      qrcodeList.value.unshift(newQrcode)
      total.value++
      
      ElMessage.success('二维码保存成功')
      resetForm()
    }
    
    const getQrcodeList = () => {
      loading.value = true
      
      // 模拟API调用
      setTimeout(() => {
        // 过滤数据
        let filteredList = [...mockQrcodeList]
        
        if (searchQuery.value) {
          filteredList = filteredList.filter(item => 
            item.promoter_name.includes(searchQuery.value)
          )
        }
        
        if (filterType.value) {
          filteredList = filteredList.filter(item => 
            item.type === filterType.value
          )
        }
        
        qrcodeList.value = filteredList
        total.value = filteredList.length
        loading.value = false
      }, 500)
    }
    
    const handleView = (row) => {
      detailLoading.value = true
      currentQrcode.value = { ...row }
      dialogVisible.value = true
      
      // 模拟加载详情数据
      setTimeout(() => {
        detailLoading.value = false
      }, 300)
    }
    
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        '确定要删除这个推广二维码吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        // 模拟删除操作
        qrcodeList.value = qrcodeList.value.filter(item => item.id !== row.id)
        total.value--
        
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
      }).catch(() => {})
    }
    
    const handleDownload = (row) => {
      // 实际项目中，这里应该是下载二维码图片的逻辑
      ElMessage.success('二维码下载成功')
    }
    
    const handleDetailDownload = () => {
      // 与handleDownload相同
      ElMessage.success('二维码下载成功')
    }
    
    const handleDetailDelete = () => {
      handleDelete(currentQrcode.value)
      dialogVisible.value = false
    }
    
    const handleSizeChange = (val) => {
      pageSize.value = val
      getQrcodeList()
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
      getQrcodeList()
    }
    
    onMounted(() => {
      getQrcodeList()
    })
    
    return {
      qrcodeForm,
      generating,
      qrcodeUrl,
      promoterOptions,
      serviceOptions,
      loading,
      qrcodeList,
      dialogVisible,
      detailLoading,
      currentQrcode,
      searchQuery,
      filterType,
      currentPage,
      pageSize,
      total,
      getPromoterName,
      getTypeName,
      getTypeTagType,
      getServiceName,
      disabledDate,
      generateQrcode,
      resetForm,
      downloadQrcode,
      saveQrcode,
      getQrcodeList,
      handleView,
      handleDelete,
      handleDownload,
      handleDetailDownload,
      handleDetailDelete,
      handleSizeChange,
      handleCurrentChange,
      calculateConversionRate
    }
  }
}
</script>

<style lang="scss" scoped>
.qrcode-generator-card {
  margin-bottom: 20px;
  
  .generator-content {
    display: flex;
    gap: 30px;
    
    .settings-panel {
      flex: 1;
      
      .commission-rate-suffix {
        margin-left: 5px;
      }
    }
    
    .preview-panel {
      flex: 1;
      
      .qrcode-preview {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 1px dashed #dcdfe6;
        border-radius: 4px;
        padding: 20px;
        min-height: 400px;
        
        .qrcode-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          
          .qrcode-image {
            width: 200px;
            height: 200px;
            margin-bottom: 15px;
          }
          
          .qrcode-info {
            margin-bottom: 15px;
            line-height: 1.8;
            color: #606266;
          }
          
          .qrcode-actions {
            display: flex;
            gap: 10px;
          }
        }
        
        .empty-preview {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          color: #909399;
          
          p {
            margin-top: 15px;
          }
        }
      }
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-option {
  display: flex;
  align-items: center;
  
  div {
    margin-left: 10px;
    
    .user-phone {
      font-size: 12px;
      color: #909399;
    }
  }
}

.qrcode-thumbnail {
  display: flex;
  justify-content: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.qrcode-detail {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .qrcode-detail-image {
    margin-bottom: 20px;
  }
  
  .qrcode-detail-info {
    width: 100%;
    margin-bottom: 20px;
  }
  
  .qrcode-detail-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
  }
}
</style>

