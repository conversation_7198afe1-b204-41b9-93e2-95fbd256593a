<template>
  <div class="app-container">
    <el-card class="commission-rule-card">
      <template #header>
        <div class="card-header">
          <span>佣金规则管理</span>
          <el-button type="primary" @click="handleAddRule">添加规则</el-button>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="用户推广佣金" name="user">
          <el-alert
            type="info"
            title="用户推广佣金说明"
            description="用户推广佣金是指用户邀请其他用户注册并完成相应操作后获得的佣金奖励。"
            :closable="false"
            show-icon
            style="margin-bottom: 20px;"
          />
          
          <div v-loading="loading.user">
            <el-table :data="rules.user" border style="width: 100%">
              <el-table-column label="ID" prop="id" width="80" align="center" />
              <el-table-column label="规则名称" prop="name" min-width="120" />
              <el-table-column label="佣金类型" align="center" width="120">
                <template #default="{row}">
                  {{ getCommissionTypeText(row.type) }}
                </template>
              </el-table-column>
              <el-table-column label="佣金值" align="center" width="120">
                <template #default="{row}">
                  <span v-if="row.type === 1">{{ row.value }}元</span>
                  <span v-else-if="row.type === 2">{{ row.value }}%</span>
                </template>
              </el-table-column>
              <el-table-column label="条件说明" prop="condition" min-width="200" />
              <el-table-column label="状态" align="center" width="100">
                <template #default="{row}">
                  <el-switch
                    v-model="row.status"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleStatusChange(row, 'user')"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="150">
                <template #default="{row}">
                  <el-button type="primary" size="small" @click="handleEditRule(row, 'user')">编辑</el-button>
                  <el-button type="danger" size="small" @click="handleDeleteRule(row, 'user')">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="驿站推广佣金" name="station">
          <el-alert
            type="info"
            title="驿站推广佣金说明"
            description="驿站推广佣金是指为驿站引流、促成驿站认证或其他相关操作后获得的佣金奖励。"
            :closable="false"
            show-icon
            style="margin-bottom: 20px;"
          />
          
          <div v-loading="loading.station">
            <el-table :data="rules.station" border style="width: 100%">
              <el-table-column label="ID" prop="id" width="80" align="center" />
              <el-table-column label="规则名称" prop="name" min-width="120" />
              <el-table-column label="佣金类型" align="center" width="120">
                <template #default="{row}">
                  {{ getCommissionTypeText(row.type) }}
                </template>
              </el-table-column>
              <el-table-column label="佣金值" align="center" width="120">
                <template #default="{row}">
                  <span v-if="row.type === 1">{{ row.value }}元</span>
                  <span v-else-if="row.type === 2">{{ row.value }}%</span>
                </template>
              </el-table-column>
              <el-table-column label="条件说明" prop="condition" min-width="200" />
              <el-table-column label="状态" align="center" width="100">
                <template #default="{row}">
                  <el-switch
                    v-model="row.status"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleStatusChange(row, 'station')"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="150">
                <template #default="{row}">
                  <el-button type="primary" size="small" @click="handleEditRule(row, 'station')">编辑</el-button>
                  <el-button type="danger" size="small" @click="handleDeleteRule(row, 'station')">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="服务推广佣金" name="service">
          <el-alert
            type="info"
            title="服务推广佣金说明"
            description="服务推广佣金是指为特定服务引流、促成服务交易或相关活动后获得的佣金奖励。"
            :closable="false"
            show-icon
            style="margin-bottom: 20px;"
          />
          
          <div v-loading="loading.service">
            <el-table :data="rules.service" border style="width: 100%">
              <el-table-column label="ID" prop="id" width="80" align="center" />
              <el-table-column label="规则名称" prop="name" min-width="120" />
              <el-table-column label="服务类型" align="center" width="120">
                <template #default="{row}">
                  {{ getServiceTypeText(row.service_type) }}
                </template>
              </el-table-column>
              <el-table-column label="佣金类型" align="center" width="120">
                <template #default="{row}">
                  {{ getCommissionTypeText(row.type) }}
                </template>
              </el-table-column>
              <el-table-column label="佣金值" align="center" width="120">
                <template #default="{row}">
                  <span v-if="row.type === 1">{{ row.value }}元</span>
                  <span v-else-if="row.type === 2">{{ row.value }}%</span>
                </template>
              </el-table-column>
              <el-table-column label="条件说明" prop="condition" min-width="200" />
              <el-table-column label="状态" align="center" width="100">
                <template #default="{row}">
                  <el-switch
                    v-model="row.status"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleStatusChange(row, 'service')"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="150">
                <template #default="{row}">
                  <el-button type="primary" size="small" @click="handleEditRule(row, 'service')">编辑</el-button>
                  <el-button type="danger" size="small" @click="handleDeleteRule(row, 'service')">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="全局设置" name="settings">
          <el-alert
            type="info"
            title="全局佣金设置说明"
            description="全局佣金设置将应用于所有类型的佣金计算，包括提现限制、结算周期等。"
            :closable="false"
            show-icon
            style="margin-bottom: 20px;"
          />
          
          <div v-loading="loading.settings">
            <el-form :model="globalSettings" label-width="180px" class="settings-form">
              <el-form-item label="最低提现金额">
                <el-input-number v-model="globalSettings.min_withdrawal" :min="0" :precision="2" :step="10" style="width: 200px" />
                <span class="form-tip">元（0表示无限制）</span>
              </el-form-item>
              
              <el-form-item label="每日最高提现次数">
                <el-input-number v-model="globalSettings.max_daily_withdrawal_count" :min="0" :precision="0" :step="1" style="width: 200px" />
                <span class="form-tip">次（0表示无限制）</span>
              </el-form-item>
              
              <el-form-item label="单次最高提现金额">
                <el-input-number v-model="globalSettings.max_single_withdrawal" :min="0" :precision="2" :step="100" style="width: 200px" />
                <span class="form-tip">元（0表示无限制）</span>
              </el-form-item>
              
              <el-form-item label="佣金结算周期">
                <el-select v-model="globalSettings.settlement_cycle" style="width: 200px">
                  <el-option label="实时结算" value="realtime" />
                  <el-option label="日结（每日24点结算）" value="daily" />
                  <el-option label="周结（每周一0点结算）" value="weekly" />
                  <el-option label="月结（每月1日0点结算）" value="monthly" />
                </el-select>
              </el-form-item>
              
              <el-form-item label="佣金提现手续费">
                <el-input-number v-model="globalSettings.withdrawal_fee" :min="0" :max="100" :precision="2" :step="0.1" style="width: 200px" />
                <span class="form-tip">%</span>
              </el-form-item>
              
              <el-form-item label="同一用户最高返佣层级">
                <el-input-number v-model="globalSettings.max_commission_level" :min="1" :max="10" :precision="0" :step="1" style="width: 200px" />
                <span class="form-tip">层</span>
              </el-form-item>
              
              <el-form-item label="佣金系统总开关">
                <el-switch v-model="globalSettings.system_enabled" />
                <span class="form-tip">关闭后所有佣金将停止计算和发放</span>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="saveGlobalSettings" :loading="submitting">保存设置</el-button>
                <el-button @click="resetGlobalSettings">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <!-- 规则编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="ruleForm"
        :model="currentRule"
        :rules="rules"
        label-width="120px"
        v-loading="dialogLoading"
      >
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="currentRule.name" placeholder="请输入规则名称" />
        </el-form-item>
        
        <el-form-item label="规则类型" prop="rule_type" v-if="isAddForm">
          <el-select v-model="currentRule.rule_type" placeholder="请选择规则类型" style="width: 100%">
            <el-option label="用户推广佣金" value="user" />
            <el-option label="驿站推广佣金" value="station" />
            <el-option label="服务推广佣金" value="service" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="服务类型" prop="service_type" v-if="currentRule.rule_type === 'service'">
          <el-select v-model="currentRule.service_type" placeholder="请选择服务类型" style="width: 100%">
            <el-option label="顶班接单" value="substitute" />
            <el-option label="驿站转让" value="transfer" />
            <el-option label="招聘求职" value="recruitment" />
            <el-option label="其他服务" value="other" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="佣金类型" prop="type">
          <el-radio-group v-model="currentRule.type">
            <el-radio :label="1">固定金额</el-radio>
            <el-radio :label="2">百分比</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="佣金值" prop="value">
          <el-input-number
            v-model="currentRule.value"
            :min="0"
            :precision="currentRule.type === 1 ? 2 : 1"
            :max="currentRule.type === 1 ? 10000 : 100"
            :step="currentRule.type === 1 ? 10 : 0.1"
            style="width: 200px"
          />
          <span class="form-tip">{{ currentRule.type === 1 ? '元' : '%' }}</span>
        </el-form-item>
        
        <el-form-item label="触发条件" prop="condition">
          <el-input 
            v-model="currentRule.condition" 
            type="textarea" 
            :rows="3"
            placeholder="请描述触发该佣金规则的条件"
          />
        </el-form-item>
        
        <el-form-item label="启用状态" prop="status">
          <el-switch v-model="currentRule.status" :active-value="1" :inactive-value="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveRule" :loading="submitting">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, toRefs, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'Commission',
  setup() {
    const activeTab = ref('user')
    const ruleForm = ref(null)
    
    const state = reactive({
      loading: {
        user: false,
        station: false,
        service: false,
        settings: false
      },
      rules: {
        user: [],
        station: [],
        service: []
      },
      globalSettings: {
        min_withdrawal: 50,
        max_daily_withdrawal_count: 3,
        max_single_withdrawal: 5000,
        settlement_cycle: 'daily',
        withdrawal_fee: 1.0,
        max_commission_level: 3,
        system_enabled: true
      },
      dialogVisible: false,
      dialogLoading: false,
      dialogType: 'add', // add 或 edit
      currentRule: {
        id: null,
        name: '',
        rule_type: 'user',
        type: 1,
        value: 0,
        condition: '',
        service_type: '',
        status: 1
      },
      originalGlobalSettings: {},
      submitting: false
    })
    
    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入规则名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
      ],
      rule_type: [
        { required: true, message: '请选择规则类型', trigger: 'change' }
      ],
      service_type: [
        { required: true, message: '请选择服务类型', trigger: 'change' }
      ],
      type: [
        { required: true, message: '请选择佣金类型', trigger: 'change' }
      ],
      value: [
        { required: true, message: '请输入佣金值', trigger: 'blur' }
      ],
      condition: [
        { required: true, message: '请输入触发条件', trigger: 'blur' }
      ]
    }
    
    // 计算属性：对话框标题
    const dialogTitle = computed(() => {
      return state.dialogType === 'add' ? '添加佣金规则' : '编辑佣金规则'
    })
    
    // 计算属性：是否为添加表单
    const isAddForm = computed(() => {
      return state.dialogType === 'add'
    })
    
    // 获取佣金类型文本
    const getCommissionTypeText = (type) => {
      const typeMap = {
        1: '固定金额',
        2: '百分比'
      }
      return typeMap[type] || '未知'
    }
    
    // 获取服务类型文本
    const getServiceTypeText = (type) => {
      const typeMap = {
        'substitute': '顶班接单',
        'transfer': '驿站转让',
        'recruitment': '招聘求职',
        'other': '其他服务'
      }
      return typeMap[type] || '未知'
    }
    
    // 加载规则数据
    const loadRules = (type) => {
      state.loading[type] = true
      
      // 模拟API调用
      setTimeout(() => {
        // 测试数据
        if (type === 'user') {
          state.rules.user = [
            {
              id: 1,
              name: '用户注册奖励',
              type: 1, // 固定金额
              value: 5,
              condition: '邀请用户成功注册并完成手机验证',
              status: 1
            },
            {
              id: 2,
              name: '首单完成奖励',
              type: 1, // 固定金额
              value: 10,
              condition: '被邀请用户完成首单消费',
              status: 1
            },
            {
              id: 3,
              name: '消费佣金',
              type: 2, // 百分比
              value: 5,
              condition: '被邀请用户每笔订单的消费金额',
              status: 0
            }
          ]
        } else if (type === 'station') {
          state.rules.station = [
            {
              id: 4,
              name: '驿站注册认证',
              type: 1, // 固定金额
              value: 50,
              condition: '邀请驿站完成注册并通过认证',
              status: 1
            },
            {
              id: 5,
              name: '驿站首月运营',
              type: 1, // 固定金额
              value: 100,
              condition: '被邀请驿站完成首月运营（接单不少于30单）',
              status: 1
            }
          ]
        } else if (type === 'service') {
          state.rules.service = [
            {
              id: 6,
              name: '顶班服务佣金',
              service_type: 'substitute',
              type: 2, // 百分比
              value: 10,
              condition: '通过推广码达成的顶班服务交易',
              status: 1
            },
            {
              id: 7,
              name: '驿站转让服务',
              service_type: 'transfer',
              type: 2, // 百分比
              value: 5,
              condition: '通过推广达成的驿站转让交易',
              status: 1
            },
            {
              id: 8,
              name: '招聘服务佣金',
              service_type: 'recruitment',
              type: 1, // 固定金额
              value: 20,
              condition: '通过推广达成的招聘交易',
              status: 0
            }
          ]
        }
        
        state.loading[type] = false
      }, 500)
    }
    
    // 加载全局设置
    const loadGlobalSettings = () => {
      state.loading.settings = true
      
      // 模拟API调用
      setTimeout(() => {
        // 已在state中初始化默认值
        state.originalGlobalSettings = { ...state.globalSettings }
        state.loading.settings = false
      }, 500)
    }
    
    // 保存全局设置
    const saveGlobalSettings = () => {
      state.submitting = true
      
      // 模拟API调用
      setTimeout(() => {
        state.originalGlobalSettings = { ...state.globalSettings }
        state.submitting = false
        
        ElMessage({
          type: 'success',
          message: '全局佣金设置保存成功'
        })
      }, 500)
    }
    
    // 重置全局设置
    const resetGlobalSettings = () => {
      state.globalSettings = { ...state.originalGlobalSettings }
    }
    
    // 添加规则
    const handleAddRule = () => {
      state.dialogType = 'add'
      state.currentRule = {
        id: null,
        name: '',
        rule_type: 'user',
        type: 1,
        value: 0,
        condition: '',
        service_type: '',
        status: 1
      }
      state.dialogVisible = true
    }
    
    // 编辑规则
    const handleEditRule = (row, type) => {
      state.dialogType = 'edit'
      state.currentRule = { ...row, rule_type: type }
      
      if (type === 'service' && !state.currentRule.service_type) {
        state.currentRule.service_type = 'other'
      }
      
      state.dialogVisible = true
    }
    
    // 保存规则
    const saveRule = () => {
      if (!ruleForm.value) return
      
      ruleForm.value.validate((valid) => {
        if (valid) {
          state.submitting = true
          
          // 模拟API调用
          setTimeout(() => {
            const { rule_type, ...ruleData } = state.currentRule
            
            if (state.dialogType === 'add') {
              // 添加新规则
              const newId = Math.floor(Math.random() * 1000) + 10 // 生成随机ID
              const newRule = { ...ruleData, id: newId }
              
              // 根据规则类型添加到对应列表
              state.rules[rule_type].push(newRule)
              
              ElMessage({
                type: 'success',
                message: '添加规则成功'
              })
            } else {
              // 编辑现有规则
              const index = state.rules[rule_type].findIndex(item => item.id === ruleData.id)
              if (index !== -1) {
                state.rules[rule_type][index] = { ...ruleData }
                
                ElMessage({
                  type: 'success',
                  message: '更新规则成功'
                })
              }
            }
            
            state.dialogVisible = false
            state.submitting = false
          }, 500)
        }
      })
    }
    
    // 删除规则
    const handleDeleteRule = (row, type) => {
      ElMessageBox.confirm('确定要删除该佣金规则吗？删除后无法恢复。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 模拟API调用
        const index = state.rules[type].findIndex(item => item.id === row.id)
        if (index !== -1) {
          state.rules[type].splice(index, 1)
          
          ElMessage({
            type: 'success',
            message: '删除成功'
          })
        }
      }).catch(() => {})
    }
    
    // 更改规则状态
    const handleStatusChange = (row, type) => {
      // 模拟API调用
      setTimeout(() => {
        ElMessage({
          type: 'success',
          message: row.status === 1 ? '规则已启用' : '规则已禁用'
        })
      }, 300)
    }
    
    onMounted(() => {
      // 加载各类型的佣金规则
      loadRules('user')
      loadRules('station')
      loadRules('service')
      loadGlobalSettings()
    })
    
    return {
      activeTab,
      ruleForm,
      dialogTitle,
      isAddForm,
      rules,
      ...toRefs(state),
      getCommissionTypeText,
      getServiceTypeText,
      handleAddRule,
      handleEditRule,
      handleDeleteRule,
      handleStatusChange,
      saveRule,
      saveGlobalSettings,
      resetGlobalSettings
    }
  }
}
</script>

<style lang="scss" scoped>
.commission-rule-card {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.settings-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 13px;
}

.dialog-footer {
  text-align: right;
}
</style>
