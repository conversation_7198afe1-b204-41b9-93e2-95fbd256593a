<template>
  <div class="app-container">
    <el-card class="filter-container">
      <div class="filter-item">
        <el-input v-model="listQuery.search" placeholder="搜索用户昵称/手机" clearable @keyup.enter.native="handleFilter" style="width: 200px;" />
        <el-select v-model="listQuery.status" placeholder="推广员状态" clearable style="width: 130px" @change="handleFilter">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="listQuery.level" placeholder="推广员等级" clearable style="width: 130px" @change="handleFilter">
          <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
        <el-button v-waves icon="el-icon-refresh" @click="resetQuery">重置</el-button>
      </div>
    </el-card>

    <el-card>
      <div class="table-header">
        <h3>推广员列表</h3>
        <div>
          <el-button type="success" size="small" @click="exportData">导出数据</el-button>
        </div>
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column label="ID" prop="id" align="center" width="80" />
        <el-table-column label="用户信息" min-width="180">
          <template slot-scope="{row}">
            <div class="user-info">
              <el-avatar :src="row.avatar || '/static/default-avatar.png'" :size="40"></el-avatar>
              <div class="user-detail">
                <div>{{ row.nickname }}</div>
                <div class="user-phone">{{ row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="推广等级" prop="level_name" align="center" width="100">
          <template slot-scope="{row}">
            <el-tag :type="getLevelTagType(row.level)">{{ row.level_name }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="推广人数" align="center" width="220">
          <template slot-scope="{row}">
            <div>一级：{{ row.first_level_count }} 人</div>
            <div>二级：{{ row.second_level_count }} 人</div>
          </template>
        </el-table-column>
        <el-table-column label="累计佣金" prop="total_commission" align="center" width="120">
          <template slot-scope="{row}">
            <span class="price-text">¥{{ row.total_commission.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="可提现金额" prop="available_balance" align="center" width="120">
          <template slot-scope="{row}">
            <span class="price-text">¥{{ row.available_balance.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" align="center" width="100">
          <template slot-scope="{row}">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="注册时间" width="150" align="center">
          <template slot-scope="{row}">
            <span>{{ row.created_at | formatDateTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template slot-scope="{row}">
            <el-button type="primary" size="mini" @click="handleDetail(row)">详情</el-button>
            <el-button type="warning" size="mini" @click="handleEditLevel(row)">修改等级</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </el-card>

    <!-- 推广员详情对话框 -->
    <el-dialog title="推广员详情" :visible.sync="dialogVisible" width="700px">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="info">
          <div v-loading="detailLoading" class="promoter-detail">
            <div class="detail-header">
              <el-avatar :src="detail.avatar || '/static/default-avatar.png'" :size="60"></el-avatar>
              <div class="detail-info">
                <h3>{{ detail.nickname }}</h3>
                <p>{{ detail.phone }}</p>
                <el-tag :type="getLevelTagType(detail.level)">{{ detail.level_name }}</el-tag>
              </div>
            </div>
            
            <el-divider content-position="left">账户数据</el-divider>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="data-card">
                  <div class="data-label">累计佣金</div>
                  <div class="data-value price-text">¥{{ detail.total_commission }}</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="data-card">
                  <div class="data-label">可提现金额</div>
                  <div class="data-value price-text">¥{{ detail.available_balance }}</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="data-card">
                  <div class="data-label">冻结金额</div>
                  <div class="data-value price-text">¥{{ detail.frozen_balance }}</div>
                </div>
              </el-col>
            </el-row>
            
            <el-divider content-position="left">推广数据</el-divider>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="data-card">
                  <div class="data-label">推广码</div>
                  <div class="data-value code-text">{{ detail.promotion_code }}</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="data-card">
                  <div class="data-label">一级用户</div>
                  <div class="data-value">{{ detail.first_level_count }} 人</div>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="data-card">
                  <div class="data-label">二级用户</div>
                  <div class="data-value">{{ detail.second_level_count }} 人</div>
                </div>
              </el-col>
            </el-row>
            
            <el-divider content-position="left">账户状态</el-divider>
            
            <el-form label-width="100px">
              <el-form-item label="注册时间">
                {{ detail.created_at | formatDateTime }}
              </el-form-item>
              <el-form-item label="成为推广员">
                {{ detail.promoter_time | formatDateTime }}
              </el-form-item>
              <el-form-item label="账户状态">
                <el-switch
                  v-model="detail.status"
                  :active-value="1"
                  :inactive-value="0"
                  @change="handleDetailStatusChange"
                ></el-switch>
                <span class="status-text">{{ detail.status === 1 ? '正常' : '已禁用' }}</span>
              </el-form-item>
              <el-form-item label="备注">
                <el-input
                  v-model="detail.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注信息"
                ></el-input>
                <div class="form-actions">
                  <el-button type="primary" size="small" @click="saveRemark">保存备注</el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="推广记录" name="invites">
          <div v-loading="invitesLoading">
            <el-radio-group v-model="inviteLevel" style="margin-bottom: 15px" @change="getInvitesList">
              <el-radio-button :label="1">一级推广</el-radio-button>
              <el-radio-button :label="2">二级推广</el-radio-button>
            </el-radio-group>
            
            <el-table
              :data="invitesList"
              border
              fit
              style="width: 100%"
            >
              <el-table-column label="用户信息" min-width="180">
                <template slot-scope="{row}">
                  <div class="user-info">
                    <el-avatar :src="row.avatar || '/static/default-avatar.png'" :size="30"></el-avatar>
                    <div class="user-detail">
                      <div>{{ row.nickname }}</div>
                      <div class="user-phone">{{ row.phone }}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column v-if="inviteLevel === 2" label="上级推广员" prop="inviter_nickname" width="120" />
              <el-table-column label="注册时间" prop="created_at" width="150">
                <template slot-scope="{row}">
                  {{ row.created_at | formatDateTime }}
                </template>
              </el-table-column>
            </el-table>
            
            <pagination
              v-show="invitesTotal>0"
              :total="invitesTotal"
              :page.sync="invitesQuery.page"
              :limit.sync="invitesQuery.limit"
              @pagination="getInvitesList"
            />
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="佣金记录" name="commission">
          <div v-loading="commissionsLoading">
            <el-table
              :data="commissionsList"
              border
              fit
              style="width: 100%"
            >
              <el-table-column label="时间" prop="created_at" width="150">
                <template slot-scope="{row}">
                  {{ row.created_at | formatDateTime }}
                </template>
              </el-table-column>
              <el-table-column label="金额" prop="amount" width="120">
                <template slot-scope="{row}">
                  <span class="price-text">¥{{ row.amount.toFixed(2) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="说明" prop="remark" min-width="180" />
              <el-table-column label="余额" prop="balance" width="120">
                <template slot-scope="{row}">
                  <span class="price-text">¥{{ row.balance.toFixed(2) }}</span>
                </template>
              </el-table-column>
            </el-table>
            
            <pagination
              v-show="commissionsTotal>0"
              :total="commissionsTotal"
              :page.sync="commissionsQuery.page"
              :limit.sync="commissionsQuery.limit"
              @pagination="getCommissionsList"
            />
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="提现记录" name="withdrawals">
          <div v-loading="withdrawalsLoading">
            <el-table
              :data="withdrawalsList"
              border
              fit
              style="width: 100%"
            >
              <el-table-column label="申请时间" prop="created_at" width="150">
                <template slot-scope="{row}">
                  {{ row.created_at | formatDateTime }}
                </template>
              </el-table-column>
              <el-table-column label="金额" prop="amount" width="120">
                <template slot-scope="{row}">
                  <span class="price-text">¥{{ row.amount.toFixed(2) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="账户类型" width="100">
                <template slot-scope="{row}">
                  {{ getAccountTypeText(row.account_type) }}
                </template>
              </el-table-column>
              <el-table-column label="账户信息" prop="account_number" min-width="150" />
              <el-table-column label="状态" prop="status" width="100">
                <template slot-scope="{row}">
                  <el-tag :type="getWithdrawStatusType(row.status)">
                    {{ getWithdrawStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="处理时间" prop="processed_at" width="150">
                <template slot-scope="{row}">
                  {{ row.processed_at ? formatDateTime(row.processed_at) : '未处理' }}
                </template>
              </el-table-column>
            </el-table>
            
            <pagination
              v-show="withdrawalsTotal>0"
              :total="withdrawalsTotal"
              :page.sync="withdrawalsQuery.page"
              :limit.sync="withdrawalsQuery.limit"
              @pagination="getWithdrawalsList"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 修改等级对话框 -->
    <el-dialog title="修改推广员等级" :visible.sync="levelDialogVisible" width="400px">
      <el-form :model="levelForm" label-width="100px">
        <el-form-item label="当前等级">
          <el-tag :type="getLevelTagType(levelForm.currentLevel)">{{ getLevelName(levelForm.currentLevel) }}</el-tag>
        </el-form-item>
        <el-form-item label="新等级">
          <el-select v-model="levelForm.newLevel" placeholder="请选择等级">
            <el-option
              v-for="item in levelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="levelForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入修改原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="levelDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitLevelChange" :loading="levelSubmitting">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPromoters, getPromoterDetail, updatePromoterStatus, updatePromoterLevel, updatePromoterRemark } from '@/api/promotion/user'
import { getPromoterInvites, getPromoterCommissions, getPromoterWithdrawals } from '@/api/promotion/statistics'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { parseTime, formatDateTime } from '@/utils/index'

export default {
  name: 'PromoterList',
  components: { Pagination },
  directives: { waves },
  filters: {
    formatDateTime(time) {
      return formatDateTime(time)
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        search: '',
        status: '',
        level: ''
      },
      
      // 状态和等级选项
      statusOptions: [
        { label: '已启用', value: 1 },
        { label: '已禁用', value: 0 }
      ],
      levelOptions: [
        { label: '初级推广员', value: 1 },
        { label: '中级推广员', value: 2 },
        { label: '高级推广员', value: 3 },
        { label: '资深推广员', value: 4 }
      ],
      
      // 详情对话框
      dialogVisible: false,
      activeTab: 'info',
      detailLoading: false,
      detail: {},
      
      // 推广用户列表
      invitesLoading: false,
      invitesList: [],
      invitesTotal: 0,
      inviteLevel: 1,
      invitesQuery: {
        page: 1,
        limit: 10
      },
      
      // 佣金记录
      commissionsLoading: false,
      commissionsList: [],
      commissionsTotal: 0,
      commissionsQuery: {
        page: 1,
        limit: 10
      },
      
      // 提现记录
      withdrawalsLoading: false,
      withdrawalsList: [],
      withdrawalsTotal: 0,
      withdrawalsQuery: {
        page: 1,
        limit: 10
      },
      
      // 修改等级对话框
      levelDialogVisible: false,
      levelSubmitting: false,
      levelForm: {
        id: null,
        currentLevel: 1,
        newLevel: 1,
        remark: ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      getPromoters(this.listQuery).then(response => {
        this.list = response.data.list
        this.total = response.data.pagination.total
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetQuery() {
      this.listQuery = {
        page: 1,
        limit: 10,
        search: '',
        status: '',
        level: ''
      }
      this.getList()
    },
    exportData() {
      this.$confirm('确认导出推广员数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 导出数据逻辑
        this.$message({
          type: 'success',
          message: '导出成功'
        })
      }).catch(() => {})
    },
    handleStatusChange(row) {
      const statusText = row.status === 1 ? '启用' : '禁用'
      this.$confirm(`确认${statusText}该推广员?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updatePromoterStatus(row.id, row.status).then(() => {
          this.$message({
            type: 'success',
            message: `${statusText}成功`
          })
        }).catch(() => {
          row.status = row.status === 1 ? 0 : 1
        })
      }).catch(() => {
        row.status = row.status === 1 ? 0 : 1
      })
    },
    handleDetail(row) {
      this.dialogVisible = true
      this.activeTab = 'info'
      this.getPromoterDetail(row.id)
    },
    getPromoterDetail(id) {
      this.detailLoading = true
      getPromoterDetail(id).then(response => {
        this.detail = response.data
        this.detailLoading = false
        
        // 重置分页
        this.resetDetailTabs()
      }).catch(() => {
        this.detailLoading = false
      })
    },
    resetDetailTabs() {
      // 重置推广记录查询
      this.inviteLevel = 1
      this.invitesQuery = { page: 1, limit: 10 }
      this.invitesList = []
      this.invitesTotal = 0
      
      // 重置佣金记录查询
      this.commissionsQuery = { page: 1, limit: 10 }
      this.commissionsList = []
      this.commissionsTotal = 0
      
      // 重置提现记录查询
      this.withdrawalsQuery = { page: 1, limit: 10 }
      this.withdrawalsList = []
      this.withdrawalsTotal = 0
    },
    handleDetailStatusChange() {
      const statusText = this.detail.status === 1 ? '启用' : '禁用'
      this.$confirm(`确认${statusText}该推广员?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        updatePromoterStatus(this.detail.id, this.detail.status).then(() => {
          this.$message({
            type: 'success',
            message: `${statusText}成功`
          })
          // 更新列表中对应的状态
          const index = this.list.findIndex(item => item.id === this.detail.id)
          if (index !== -1) {
            this.list[index].status = this.detail.status
          }
        }).catch(() => {
          this.detail.status = this.detail.status === 1 ? 0 : 1
        })
      }).catch(() => {
        this.detail.status = this.detail.status === 1 ? 0 : 1
      })
    },
    saveRemark() {
      updatePromoterRemark(this.detail.id, this.detail.remark).then(() => {
        this.$message({
          type: 'success',
          message: '备注保存成功'
        })
      })
    },
    handleEditLevel(row) {
      this.levelDialogVisible = true
      this.levelForm = {
        id: row.id,
        currentLevel: row.level,
        newLevel: row.level,
        remark: ''
      }
    },
    submitLevelChange() {
      if (this.levelForm.currentLevel === this.levelForm.newLevel) {
        this.$message({
          type: 'warning',
          message: '等级未变更'
        })
        return
      }
      
      this.levelSubmitting = true
      updatePromoterLevel(this.levelForm.id, this.levelForm.newLevel, this.levelForm.remark).then(() => {
        this.$message({
          type: 'success',
          message: '等级修改成功'
        })
        this.levelDialogVisible = false
        this.levelSubmitting = false
        
        // 刷新列表
        this.getList()
        
        // 如果详情对话框打开且是当前用户，刷新详情
        if (this.dialogVisible && this.detail.id === this.levelForm.id) {
          this.getPromoterDetail(this.levelForm.id)
        }
      }).catch(() => {
        this.levelSubmitting = false
      })
    },
    getInvitesList() {
      if (!this.detail.id) return
      
      this.invitesLoading = true
      getPromoterInvites(this.detail.id, this.inviteLevel, this.invitesQuery).then(response => {
        this.invitesList = response.data.list
        this.invitesTotal = response.data.pagination.total
        this.invitesLoading = false
      }).catch(() => {
        this.invitesLoading = false
      })
    },
    getCommissionsList() {
      if (!this.detail.id) return
      
      this.commissionsLoading = true
      getPromoterCommissions(this.detail.id, this.commissionsQuery).then(response => {
        this.commissionsList = response.data.list
        this.commissionsTotal = response.data.pagination.total
        this.commissionsLoading = false
      }).catch(() => {
        this.commissionsLoading = false
      })
    },
    getWithdrawalsList() {
      if (!this.detail.id) return
      
      this.withdrawalsLoading = true
      getPromoterWithdrawals(this.detail.id, this.withdrawalsQuery).then(response => {
        this.withdrawalsList = response.data.list
        this.withdrawalsTotal = response.data.pagination.total
        this.withdrawalsLoading = false
      }).catch(() => {
        this.withdrawalsLoading = false
      })
    },
    getLevelTagType(level) {
      const types = ['', 'info', 'success', 'warning', 'danger']
      return types[level] || 'info'
    },
    getLevelName(level) {
      const option = this.levelOptions.find(item => item.value === level)
      return option ? option.label : '未知等级'
    },
    getAccountTypeText(type) {
      const types = ['未知', '微信', '支付宝', '银行卡']
      return types[type] || '未知'
    },
    getWithdrawStatusText(status) {
      const statusTexts = ['待审核', '已通过', '已拒绝', '已打款']
      return statusTexts[status] || '未知'
    },
    getWithdrawStatusType(status) {
      const types = ['info', 'success', 'danger', 'success']
      return types[status] || 'info'
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
  
  .filter-item {
    display: flex;
    gap: 10px;
  }
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h3 {
    margin: 0;
  }
}

.user-info {
  display: flex;
  align-items: center;
  
  .user-detail {
    margin-left: 10px;
    
    .user-phone {
      font-size: 12px;
      color: #999;
    }
  }
}

.price-text {
  color: #f56c6c;
  font-weight: bold;
}

.code-text {
  font-family: monospace;
  letter-spacing: 1px;
}

.promoter-detail {
  .detail-header {
    display: flex;
    margin-bottom: 20px;
    
    .detail-info {
      margin-left: 15px;
      
      h3 {
        margin: 0 0 5px 0;
      }
      
      p {
        margin: 0 0 10px 0;
        color: #606266;
      }
    }
  }
  
  .data-card {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    text-align: center;
    margin-bottom: 15px;
    
    .data-label {
      color: #909399;
      font-size: 12px;
      margin-bottom: 5px;
    }
    
    .data-value {
      font-size: 18px;
      font-weight: bold;
    }
  }
  
  .status-text {
    margin-left: 10px;
    font-size: 14px;
  }
  
  .form-actions {
    margin-top: 10px;
    text-align: right;
  }
}
</style>

