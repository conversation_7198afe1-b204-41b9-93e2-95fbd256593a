<template>
  <div class="app-container">
    <!-- 数据概览 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-header">
            <div class="stat-title">总推广员数</div>
            <el-select v-model="overviewTimeRange" size="mini" @change="refreshOverview">
              <el-option label="今日" value="today"></el-option>
              <el-option label="昨日" value="yesterday"></el-option>
              <el-option label="本周" value="week"></el-option>
              <el-option label="本月" value="month"></el-option>
            </el-select>
          </div>
          <div class="stat-body">
            <div class="stat-value">{{ overview.totalPromoters }}</div>
            <div class="stat-chart mini-chart">
              <trend-chart 
                :data="overview.promoterTrend" 
                :color="'#409EFF'" 
                :height="30"
                :is-smooth="true"
              />
            </div>
          </div>
          <div class="stat-footer">
            <div class="stat-change" :class="overview.promoterChange >= 0 ? 'up' : 'down'">
              <i :class="overview.promoterChange >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              {{ Math.abs(overview.promoterChange) }}
            </div>
            <div class="stat-period">较上期</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-header">
            <div class="stat-title">新增推广用户</div>
          </div>
          <div class="stat-body">
            <div class="stat-value">{{ overview.newInvites }}</div>
            <div class="stat-chart mini-chart">
              <trend-chart 
                :data="overview.inviteTrend" 
                :color="'#67C23A'" 
                :height="30"
                :is-smooth="true"
              />
            </div>
          </div>
          <div class="stat-footer">
            <div class="stat-change" :class="overview.inviteChange >= 0 ? 'up' : 'down'">
              <i :class="overview.inviteChange >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              {{ Math.abs(overview.inviteChange) }}
            </div>
            <div class="stat-period">较上期</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-header">
            <div class="stat-title">佣金发放</div>
          </div>
          <div class="stat-body">
            <div class="stat-value">¥{{ overview.totalCommission }}</div>
            <div class="stat-chart mini-chart">
              <trend-chart 
                :data="overview.commissionTrend" 
                :color="'#F56C6C'" 
                :height="30"
                :is-smooth="true"
              />
            </div>
          </div>
          <div class="stat-footer">
            <div class="stat-change" :class="overview.commissionChange >= 0 ? 'up' : 'down'">
              <i :class="overview.commissionChange >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              ¥{{ Math.abs(overview.commissionChange) }}
            </div>
            <div class="stat-period">较上期</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-header">
            <div class="stat-title">提现总额</div>
          </div>
          <div class="stat-body">
            <div class="stat-value">¥{{ overview.totalWithdrawal }}</div>
            <div class="stat-chart mini-chart">
              <trend-chart 
                :data="overview.withdrawalTrend" 
                :color="'#E6A23C'" 
                :height="30"
                :is-smooth="true"
              />
            </div>
          </div>
          <div class="stat-footer">
            <div class="stat-change" :class="overview.withdrawalChange >= 0 ? 'up' : 'down'">
              <i :class="overview.withdrawalChange >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              ¥{{ Math.abs(overview.withdrawalChange) }}
            </div>
            <div class="stat-period">较上期</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 趋势图表 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="16">
        <el-card class="chart-card">
          <div class="chart-header">
            <div class="chart-title">推广趋势</div>
            <div class="chart-actions">
              <el-radio-group v-model="trendType" size="mini" @change="refreshTrend">
                <el-radio-button label="users">推广用户</el-radio-button>
                <el-radio-button label="commissions">佣金金额</el-radio-button>
                <el-radio-button label="withdrawals">提现金额</el-radio-button>
              </el-radio-group>
              <el-select v-model="trendTimeRange" size="mini" @change="refreshTrend" style="margin-left: 10px;">
                <el-option label="最近7天" value="week"></el-option>
                <el-option label="最近30天" value="month"></el-option>
                <el-option label="最近12个月" value="year"></el-option>
              </el-select>
            </div>
          </div>
          <div class="chart-body">
            <div v-loading="trendLoading" class="main-chart">
              <trendChart 
                ref="trendChart" 
                :data="trendData" 
                :labels="trendLabels" 
                :height="300"
                :is-smooth="true"
              />
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <div class="chart-header">
            <div class="chart-title">推广员等级分布</div>
          </div>
          <div class="chart-body">
            <div v-loading="distributionLoading" class="pie-chart">
              <pieChart 
                ref="distributionChart" 
                :data="distributionData"
                :height="300"
              />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 排行榜 -->
    <el-row :gutter="20" class="ranking-row">
      <el-col :span="12">
        <el-card class="ranking-card">
          <div class="ranking-header">
            <div class="ranking-title">佣金排行</div>
            <el-select v-model="commissionRankTimeRange" size="mini" @change="getCommissionRanking">
              <el-option label="本周" value="week"></el-option>
              <el-option label="本月" value="month"></el-option>
              <el-option label="今年" value="year"></el-option>
              <el-option label="全部" value="all"></el-option>
            </el-select>
          </div>
          <div v-loading="commissionRankLoading" class="ranking-body">
            <div class="ranking-item" v-for="(item, index) in commissionRanking" :key="item.id">
              <div class="ranking-number" :class="`rank-${index+1}`">{{ index + 1 }}</div>
              <div class="ranking-avatar">
                <el-avatar :src="item.avatar || '/static/default-avatar.png'" :size="40"></el-avatar>
              </div>
              <div class="ranking-info">
                <div class="ranking-name">{{ item.nickname }}</div>
                <div class="ranking-level">{{ getLevelName(item.level) }}</div>
              </div>
              <div class="ranking-value">¥{{ item.total_commission }}</div>
            </div>
            <div class="empty-text" v-if="commissionRanking.length === 0">
              暂无数据
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="ranking-card">
          <div class="ranking-header">
            <div class="ranking-title">推广用户排行</div>
            <el-select v-model="invitesRankTimeRange" size="mini" @change="getInvitesRanking">
              <el-option label="本周" value="week"></el-option>
              <el-option label="本月" value="month"></el-option>
              <el-option label="今年" value="year"></el-option>
              <el-option label="全部" value="all"></el-option>
            </el-select>
          </div>
          <div v-loading="invitesRankLoading" class="ranking-body">
            <div class="ranking-item" v-for="(item, index) in invitesRanking" :key="item.id">
              <div class="ranking-number" :class="`rank-${index+1}`">{{ index + 1 }}</div>
              <div class="ranking-avatar">
                <el-avatar :src="item.avatar || '/static/default-avatar.png'" :size="40"></el-avatar>
              </div>
              <div class="ranking-info">
                <div class="ranking-name">{{ item.nickname }}</div>
                <div class="ranking-level">{{ getLevelName(item.level) }}</div>
              </div>
              <div class="ranking-value">{{ item.total_invites }} 人</div>
            </div>
            <div class="empty-text" v-if="invitesRanking.length === 0">
              暂无数据
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  getPromotionOverview,
  getPromotionTrend,
  getPromoterLevelDistribution,
  getCommissionRanking,
  getInvitesRanking
} from '@/api/promotion/statistics';
import TrendChart from '@/components/Charts/TrendChart';
import PieChart from '@/components/Charts/PieChart';

export default {
  name: 'PromotionStatistics',
  components: {
    TrendChart,
    PieChart
  },
  data() {
    return {
      // 数据概览
      overviewLoading: false,
      overviewTimeRange: 'week',
      overview: {
        totalPromoters: 0,
        newInvites: 0,
        totalCommission: '0.00',
        totalWithdrawal: '0.00',
        promoterChange: 0,
        inviteChange: 0,
        commissionChange: 0,
        withdrawalChange: 0,
        promoterTrend: [],
        inviteTrend: [],
        commissionTrend: [],
        withdrawalTrend: []
      },

      // 趋势图表
      trendLoading: false,
      trendType: 'users',
      trendTimeRange: 'week',
      trendData: [],
      trendLabels: [],

      // 等级分布
      distributionLoading: false,
      distributionData: [],

      // 佣金排行
      commissionRankLoading: false,
      commissionRankTimeRange: 'month',
      commissionRanking: [],

      // 推广用户排行
      invitesRankLoading: false,
      invitesRankTimeRange: 'month',
      invitesRanking: [],

      // 等级名称映射
      levelMap: {
        1: '初级推广员',
        2: '中级推广员',
        3: '高级推广员',
        4: '资深推广员'
      }
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      this.refreshOverview();
      this.refreshTrend();
      this.getPromoterDistribution();
      this.getCommissionRanking();
      this.getInvitesRanking();
    },
    async refreshOverview() {
      this.overviewLoading = true;
      try {
        const res = await getPromotionOverview({
          timeRange: this.overviewTimeRange
        });
        if (res.code === 200) {
          // 处理概览数据，确保所有趋势数据都是有效值
          const data = res.data;
          
          // 确保趋势数据数组不包含undefined或null
          if (Array.isArray(data.promoterTrend)) {
            data.promoterTrend = data.promoterTrend.map(val => val === undefined || val === null ? 0 : val);
          } else {
            data.promoterTrend = [];
          }
          
          if (Array.isArray(data.inviteTrend)) {
            data.inviteTrend = data.inviteTrend.map(val => val === undefined || val === null ? 0 : val);
          } else {
            data.inviteTrend = [];
          }
          
          if (Array.isArray(data.commissionTrend)) {
            data.commissionTrend = data.commissionTrend.map(val => val === undefined || val === null ? 0 : val);
          } else {
            data.commissionTrend = [];
          }
          
          if (Array.isArray(data.withdrawalTrend)) {
            data.withdrawalTrend = data.withdrawalTrend.map(val => val === undefined || val === null ? 0 : val);
          } else {
            data.withdrawalTrend = [];
          }
          
          this.overview = data;
        }
      } catch (error) {
        console.error('获取概览数据失败', error);
        // 确保趋势数据为空数组而不是undefined
        this.overview.promoterTrend = [];
        this.overview.inviteTrend = [];
        this.overview.commissionTrend = [];
        this.overview.withdrawalTrend = [];
      } finally {
        this.overviewLoading = false;
      }
    },
    async refreshTrend() {
      this.trendLoading = true;
      try {
        const res = await getPromotionTrend({
          type: this.trendType,
          timeRange: this.trendTimeRange
        });
        if (res.code === 200) {
          this.trendData = Array.isArray(res.data.values) 
            ? res.data.values.map(val => val === undefined || val === null ? 0 : val) 
            : [];
          this.trendLabels = Array.isArray(res.data.labels) ? res.data.labels : [];
        }
      } catch (error) {
        console.error('获取趋势数据失败', error);
        this.trendData = [];
        this.trendLabels = [];
      } finally {
        this.trendLoading = false;
      }
    },
    async getPromoterDistribution() {
      this.distributionLoading = true;
      try {
        const res = await getPromoterLevelDistribution();
        if (res.code === 200) {
          this.distributionData = res.data.map(item => ({
            name: this.levelMap[item.level],
            value: item.count,
            itemStyle: {
              // 设置不同等级的不同颜色
              color: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C'][item.level - 1]
            }
          }));
        }
      } catch (error) {
        console.error('获取分布数据失败', error);
      } finally {
        this.distributionLoading = false;
      }
    },
    async getCommissionRanking() {
      this.commissionRankLoading = true;
      try {
        const res = await getCommissionRanking({
          timeRange: this.commissionRankTimeRange,
          limit: 10
        });
        if (res.code === 200) {
          this.commissionRanking = res.data;
        }
      } catch (error) {
        console.error('获取佣金排行失败', error);
      } finally {
        this.commissionRankLoading = false;
      }
    },
    async getInvitesRanking() {
      this.invitesRankLoading = true;
      try {
        const res = await getInvitesRanking({
          timeRange: this.invitesRankTimeRange,
          limit: 10
        });
        if (res.code === 200) {
          this.invitesRanking = res.data;
        }
      } catch (error) {
        console.error('获取推广用户排行失败', error);
      } finally {
        this.invitesRankLoading = false;
      }
    },
    getLevelName(level) {
      return this.levelMap[level] || '未知等级';
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  .chart-row, .ranking-row {
    margin-top: 20px;
  }

  .stat-card {
    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .stat-title {
        font-size: 14px;
        color: #606266;
      }
    }

    .stat-body {
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .mini-chart {
        height: 30px;
      }
    }

    .stat-footer {
      display: flex;
      align-items: center;
      margin-top: 10px;

      .stat-change {
        font-size: 14px;
        font-weight: bold;
        margin-right: 5px;

        &.up {
          color: #67C23A;
        }

        &.down {
          color: #F56C6C;
        }

        i {
          margin-right: 2px;
        }
      }

      .stat-period {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .chart-card {
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .chart-title {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .chart-body {
      .main-chart {
        height: 300px;
      }

      .pie-chart {
        height: 300px;
      }
    }
  }

  .ranking-card {
    .ranking-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .ranking-title {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .ranking-body {
      .ranking-item {
        display: flex;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .ranking-number {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background-color: #f0f0f0;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          margin-right: 15px;

          &.rank-1 {
            background-color: #f5c518;
            color: #fff;
          }

          &.rank-2 {
            background-color: #c0c0c0;
            color: #fff;
          }

          &.rank-3 {
            background-color: #cd7f32;
            color: #fff;
          }
        }

        .ranking-avatar {
          margin-right: 15px;
        }

        .ranking-info {
          flex: 1;

          .ranking-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 4px;
          }

          .ranking-level {
            font-size: 12px;
            color: #909399;
          }
        }

        .ranking-value {
          font-size: 16px;
          font-weight: bold;
          color: #F56C6C;
        }
      }

      .empty-text {
        text-align: center;
        color: #909399;
        padding: 30px 0;
      }
    }
  }
}
</style>
