<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="filter-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
        <el-form-item label="订单号" prop="orderNo">
          <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="订单类型" prop="orderType">
          <el-select v-model="queryParams.orderType" placeholder="请选择订单类型" clearable>
            <el-option v-for="dict in orderTypeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="支付状态" prop="payStatus">
          <el-select v-model="queryParams.payStatus" placeholder="请选择支付状态" clearable>
            <el-option v-for="dict in payStatusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="订单状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择订单状态" clearable>
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-container">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
        </el-col>
      </el-row>

      <!-- 订单列表 -->
      <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="订单号" align="center" prop="orderNo" width="180" />
        <el-table-column label="驿站名称" align="center" prop="stationName" min-width="150" show-overflow-tooltip />
        <el-table-column label="订单类型" align="center" prop="orderType">
          <template #default="scope">
            <el-tag :type="getOrderTypeTag(scope.row.orderType)">{{ scope.row.orderTypeName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="用户名" align="center" prop="userName" min-width="100" />
        <el-table-column label="手机号" align="center" prop="phone" min-width="120" />
        <el-table-column label="订单金额" align="center" prop="amount" />
        <el-table-column label="支付状态" align="center" prop="payStatus">
          <template #default="scope">
            <el-tag :type="scope.row.payStatus === 1 ? 'success' : 'info'">
              {{ scope.row.payStatus === 1 ? '已支付' : '未支付' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="getStatusTag(scope.row.status)">{{ scope.row.statusName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="160" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
            <el-button 
              v-if="scope.row.status === 1" 
              link 
              type="primary" 
              icon="Check" 
              @click="handleProcess(scope.row)"
            >处理</el-button>
            <el-button 
              v-if="scope.row.status === 2" 
              link 
              type="primary" 
              icon="Check" 
              @click="handleComplete(scope.row)"
            >完成</el-button>
            <el-button 
              v-if="scope.row.status === 1 && scope.row.payStatus === 0" 
              link 
              type="danger" 
              icon="Close" 
              @click="handleCancel(scope.row)"
            >取消</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="total > 0"
        :total="total"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" v-model="detailDialog.visible" width="800px" append-to-body>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="订单号">{{ detailInfo.orderNo }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ detailInfo.createTime }}</el-descriptions-item>
            <el-descriptions-item label="订单类型">{{ detailInfo.orderTypeName }}</el-descriptions-item>
            <el-descriptions-item label="订单状态">
              <el-tag :type="getStatusTag(detailInfo.status)">{{ detailInfo.statusName }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="订单金额">{{ detailInfo.amount }} 元</el-descriptions-item>
            <el-descriptions-item label="支付状态">
              <el-tag :type="detailInfo.payStatus === 1 ? 'success' : 'info'">
                {{ detailInfo.payStatus === 1 ? '已支付' : '未支付' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="支付时间" v-if="detailInfo.payStatus === 1">{{ detailInfo.payTime }}</el-descriptions-item>
            <el-descriptions-item label="支付方式" v-if="detailInfo.payStatus === 1">{{ detailInfo.payMethod }}</el-descriptions-item>
            <el-descriptions-item label="完成时间" v-if="detailInfo.status === 3">{{ detailInfo.completeTime }}</el-descriptions-item>
            <el-descriptions-item label="取消时间" v-if="detailInfo.status === 4">{{ detailInfo.cancelTime }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="用户信息" name="user">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户名">{{ detailInfo.userName }}</el-descriptions-item>
            <el-descriptions-item label="用户ID">{{ detailInfo.userId }}</el-descriptions-item>
            <el-descriptions-item label="手机号">{{ detailInfo.phone }}</el-descriptions-item>
            <el-descriptions-item label="下单次数">{{ detailInfo.orderCount || '首次下单' }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="驿站信息" name="station">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="驿站名称">{{ detailInfo.stationName }}</el-descriptions-item>
            <el-descriptions-item label="驿站ID">{{ detailInfo.stationId }}</el-descriptions-item>
            <el-descriptions-item label="驿站类型">{{ detailInfo.stationType }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ detailInfo.stationPhone }}</el-descriptions-item>
            <el-descriptions-item label="详细地址" :span="2">{{ detailInfo.stationAddress }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="订单日志" name="log">
          <el-timeline>
            <el-timeline-item
              v-for="(log, index) in detailInfo.logs"
              :key="index"
              :timestamp="log.operateTime"
              :type="getLogTypeIcon(log.operateType)"
            >
              {{ log.operateContent }}
            </el-timeline-item>
          </el-timeline>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 处理订单对话框 -->
    <el-dialog :title="processDialog.title" v-model="processDialog.visible" width="500px" append-to-body>
      <el-form ref="processForm" :model="processForm" :rules="processRules" label-width="80px">
        <el-form-item label="处理结果" prop="result">
          <el-radio-group v-model="processForm.result">
            <el-radio :label="1">同意</el-radio>
            <el-radio :label="0">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="处理备注" prop="remark">
          <el-input v-model="processForm.remark" type="textarea" :rows="3" placeholder="请输入处理备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitProcessForm">确 定</el-button>
          <el-button @click="processDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 后续将导入API
// import { listOrder, getOrderDetail, processOrder, completeOrder, cancelOrder, exportOrder } from '@/api/order'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  orderNo: '',
  orderType: '',
  payStatus: '',
  status: '',
  dateRange: []
})

// 表格数据
const loading = ref(false)
const orderList = ref([])
const total = ref(0)
const ids = ref([])
const multiple = computed(() => ids.value.length === 0)

// 选项数据
const orderTypeOptions = [
  { value: '1', label: '站点租用' },
  { value: '2', label: '服务购买' },
  { value: '3', label: '设备租赁' }
]
const payStatusOptions = [
  { value: '1', label: '已支付' },
  { value: '0', label: '未支付' }
]
const statusOptions = [
  { value: '1', label: '待处理' },
  { value: '2', label: '处理中' },
  { value: '3', label: '已完成' },
  { value: '4', label: '已取消' }
]

// 详情对话框
const detailDialog = reactive({
  visible: false
})
const detailInfo = reactive({})
const activeTab = ref('basic')

// 处理订单对话框
const processDialog = reactive({
  visible: false,
  title: ''
})
const processForm = reactive({
  orderId: null,
  orderNo: '',
  result: 1,
  remark: ''
})
const processRules = reactive({
  result: [
    { required: true, message: '请选择处理结果', trigger: 'change' }
  ],
  remark: [
    { required: true, message: '请输入处理备注', trigger: 'blur' }
  ]
})
const processFormRef = ref(null)

// 获取订单类型标签样式
const getOrderTypeTag = (type) => {
  switch (type) {
    case '1':
      return 'primary'
    case '2':
      return 'success'
    case '3':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取订单状态标签样式
const getStatusTag = (status) => {
  switch (status) {
    case 1:
      return 'info'
    case 2:
      return 'warning'
    case 3:
      return 'success'
    case 4:
      return 'danger'
    default:
      return 'info'
  }
}

// 获取日志图标类型
const getLogTypeIcon = (type) => {
  switch (type) {
    case 'create':
      return 'primary'
    case 'pay':
      return 'success'
    case 'process':
      return 'warning'
    case 'complete':
      return 'success'
    case 'cancel':
      return 'danger'
    default:
      return 'info'
  }
}

// 生命周期钩子
onMounted(() => {
  getList()
})

// 获取订单列表
const getList = () => {
  loading.value = true
  // 测试数据，后续替换为API调用
  // listOrder(queryParams).then(response => {
  //   orderList.value = response.rows
  //   total.value = response.total
  //   loading.value = false
  // })
  
  // 模拟数据
  setTimeout(() => {
    orderList.value = [
      {
        orderId: 1,
        orderNo: 'ORD20230515001',
        stationId: 1,
        stationName: '海淀创业园驿站',
        userId: 1001,
        userName: '张三',
        phone: '13812345678',
        orderType: '1',
        orderTypeName: '站点租用',
        amount: '3000.00',
        payStatus: 1,
        payTime: '2023-05-15 10:35:00',
        payMethod: '微信支付',
        status: 2,
        statusName: '处理中',
        createTime: '2023-05-15 10:30:00',
      },
      {
        orderId: 2,
        orderNo: 'ORD20230510002',
        stationId: 2,
        stationName: '朝阳小区驿站',
        userId: 1002,
        userName: '李四',
        phone: '13987654321',
        orderType: '2',
        orderTypeName: '服务购买',
        amount: '500.00',
        payStatus: 1,
        payTime: '2023-05-10 14:25:00',
        payMethod: '支付宝',
        status: 3,
        statusName: '已完成',
        createTime: '2023-05-10 14:20:00',
        completeTime: '2023-05-12 16:30:00'
      },
      {
        orderId: 3,
        orderNo: 'ORD20230505003',
        stationId: 3,
        stationName: '张三个人驿站',
        userId: 1003,
        userName: '王五',
        phone: '13598765432',
        orderType: '3',
        orderTypeName: '设备租赁',
        amount: '800.00',
        payStatus: 0,
        status: 1,
        statusName: '待处理',
        createTime: '2023-05-05 09:15:00',
      }
    ]
    total.value = 3
    loading.value = false
  }, 500)
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.orderNo = ''
  queryParams.orderType = ''
  queryParams.payStatus = ''
  queryParams.status = ''
  queryParams.dateRange = []
  handleQuery()
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.orderId)
}

// 查看订单详情
const handleDetail = (row) => {
  // getOrderDetail(row.orderId).then(response => {
  //   Object.assign(detailInfo, response.data)
  //   detailDialog.visible = true
  // })
  
  // 模拟数据
  Object.assign(detailInfo, row, {
    stationType: row.stationId === 1 ? '企业' : (row.stationId === 2 ? '社区' : '个人'),
    stationPhone: row.stationId === 1 ? '13812345678' : (row.stationId === 2 ? '13987654321' : '13598765432'),
    stationAddress: row.stationId === 1 ? '北京市海淀区西二旗大街58号' : 
                  (row.stationId === 2 ? '北京市朝阳区建国路89号' : '上海市浦东新区张杨路500号'),
    orderCount: Math.floor(Math.random() * 10),
    logs: [
      {
        operateType: 'create',
        operateContent: '用户创建订单',
        operateTime: row.createTime
      }
    ]
  })
  
  if (row.payStatus === 1) {
    detailInfo.logs.push({
      operateType: 'pay',
      operateContent: `用户完成支付，支付方式：${row.payMethod}`,
      operateTime: row.payTime
    })
  }
  
  if (row.status === 2) {
    detailInfo.logs.push({
      operateType: 'process',
      operateContent: '管理员确认订单，开始处理',
      operateTime: '2023-05-16 09:30:00'
    })
  }
  
  if (row.status === 3) {
    detailInfo.logs.push({
      operateType: 'process',
      operateContent: '管理员确认订单，开始处理',
      operateTime: '2023-05-11 09:30:00'
    })
    detailInfo.logs.push({
      operateType: 'complete',
      operateContent: '管理员确认服务完成，订单完成',
      operateTime: row.completeTime
    })
  }
  
  if (row.status === 4) {
    detailInfo.logs.push({
      operateType: 'cancel',
      operateContent: '订单已取消',
      operateTime: row.cancelTime || row.createTime
    })
  }
  
  detailDialog.visible = true
  activeTab.value = 'basic'
}

// 处理订单
const handleProcess = (row) => {
  processForm.orderId = row.orderId
  processForm.orderNo = row.orderNo
  processForm.result = 1
  processForm.remark = ''
  processDialog.visible = true
  processDialog.title = '处理订单'
}

// 提交处理表单
const submitProcessForm = () => {
  // processFormRef.value.validate(valid => {
  //   if (valid) {
  //     processOrder(processForm).then(response => {
  //       ElMessage.success('处理成功')
  //       processDialog.visible = false
  //       getList()
  //     })
  //   }
  // })
  
  ElMessage.success('处理成功')
  processDialog.visible = false
  getList()
}

// 完成订单
const handleComplete = (row) => {
  ElMessageBox.confirm('是否确认完成该订单?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // completeOrder(row.orderId).then(() => {
    //   ElMessage.success('操作成功')
    //   getList()
    // })
    ElMessage.success('操作成功')
    getList()
  }).catch(() => {})
}

// 取消订单
const handleCancel = (row) => {
  ElMessageBox.confirm('是否确认取消该订单?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // cancelOrder(row.orderId).then(() => {
    //   ElMessage.success('取消成功')
    //   getList()
    // })
    ElMessage.success('取消成功')
    getList()
  }).catch(() => {})
}

// 导出按钮操作
const handleExport = () => {
  // exportOrder(queryParams).then(response => {
  //   // 导出处理
  // })
  ElMessage.success('导出成功')
}

// 分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.mb8 {
  margin-bottom: 8px;
}
</style> 