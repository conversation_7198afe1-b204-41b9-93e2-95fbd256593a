<template>
  <div class="order-list-container">
    <div class="page-header">
      <h2><i class="el-icon-s-order"></i> 订单管理</h2>
      <p>管理系统中的所有订单信息</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon total">
          <i class="el-icon-s-order"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total }}</div>
          <div class="stat-label">总订单数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon pending">
          <i class="el-icon-time"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.pending }}</div>
          <div class="stat-label">待处理</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon completed">
          <i class="el-icon-check"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon revenue">
          <i class="el-icon-money"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ formatMoney(stats.revenue) }}</div>
          <div class="stat-label">总收入</div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="订单编号">
          <el-input v-model="searchForm.orderNo" placeholder="请输入订单编号" clearable />
        </el-form-item>
        <el-form-item label="用户手机号">
          <el-input v-model="searchForm.userPhone" placeholder="请输入用户手机号" clearable />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="处理中" value="processing" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
            <el-option label="已退款" value="refunded" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务类型">
          <el-select v-model="searchForm.serviceType" placeholder="请选择" clearable>
            <el-option label="快递代收" value="express_pickup" />
            <el-option label="快递代发" value="express_send" />
            <el-option label="打印复印" value="print_copy" />
            <el-option label="其他服务" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="orderList"
        style="width: 100%"
      >
        <el-table-column prop="order_no" label="订单编号" width="160" />
        <el-table-column label="用户信息" min-width="150">
          <template #default="scope">
            <div class="user-info">
              <div class="phone">{{ scope.row.user_phone || '-' }}</div>
              <div class="nickname">{{ scope.row.user_nickname || '-' }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="service_type" label="服务类型" width="100">
          <template #default="scope">
            <el-tag :type="getServiceTypeColor(scope.row.service_type)">
              {{ getServiceTypeText(scope.row.service_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="订单金额" width="100">
          <template #default="scope">
            <span class="amount">¥{{ formatMoney(scope.row.amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="station_name" label="服务驿站" width="120" />
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              <i class="el-icon-view"></i> 查看
            </el-button>
            <el-button 
              v-if="scope.row.status === 'paid'" 
              type="text" 
              size="small" 
              @click="handleProcess(scope.row)"
            >
              <i class="el-icon-s-tools"></i> 处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 订单详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="订单详情"
      width="800px"
    >
      <div v-if="currentOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单编号">{{ currentOrder.order_no }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusColor(currentOrder.status)">
              {{ getStatusText(currentOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户手机号">{{ currentOrder.user_phone || '-' }}</el-descriptions-item>
          <el-descriptions-item label="用户昵称">{{ currentOrder.user_nickname || '-' }}</el-descriptions-item>
          <el-descriptions-item label="服务类型">{{ getServiceTypeText(currentOrder.service_type) }}</el-descriptions-item>
          <el-descriptions-item label="订单金额">¥{{ formatMoney(currentOrder.amount) }}</el-descriptions-item>
          <el-descriptions-item label="服务驿站">{{ currentOrder.station_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ currentOrder.payment_method || '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatTime(currentOrder.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ formatTime(currentOrder.payment_time) }}</el-descriptions-item>
          <el-descriptions-item label="服务详情" :span="2">{{ currentOrder.service_details || '-' }}</el-descriptions-item>
          <el-descriptions-item label="备注信息" :span="2">{{ currentOrder.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'OrderList',
  data() {
    return {
      loading: false,
      orderList: [],
      searchForm: {
        orderNo: '',
        userPhone: '',
        status: '',
        serviceType: ''
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      stats: {
        total: 0,
        pending: 0,
        completed: 0,
        revenue: 0
      },
      detailDialogVisible: false,
      currentOrder: null
    }
  },
  mounted() {
    this.loadStats()
    this.loadOrderList()
  },
  methods: {
    async loadStats() {
      // 暂时使用模拟数据
      this.stats = {
        total: 0,
        pending: 0,
        completed: 0,
        revenue: 0
      }
    },
    
    async loadOrderList() {
      this.loading = true
      try {
        // 这里调用API获取订单列表
        // const response = await this.$http.get('/order/list', { params: this.getSearchParams() })
        
        // 暂时使用空数据
        this.orderList = []
        this.pagination.total = 0
      } catch (error) {
        console.error('加载订单列表失败:', error)
        this.$message.error('加载订单列表失败')
      } finally {
        this.loading = false
      }
    },
    
    getSearchParams() {
      const params = {
        page: this.pagination.page,
        limit: this.pagination.limit
      }
      
      if (this.searchForm.orderNo) {
        params.orderNo = this.searchForm.orderNo
      }
      if (this.searchForm.userPhone) {
        params.userPhone = this.searchForm.userPhone
      }
      if (this.searchForm.status) {
        params.status = this.searchForm.status
      }
      if (this.searchForm.serviceType) {
        params.serviceType = this.searchForm.serviceType
      }
      
      return params
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadOrderList()
    },
    
    handleReset() {
      this.searchForm = {
        orderNo: '',
        userPhone: '',
        status: '',
        serviceType: ''
      }
      this.pagination.page = 1
      this.loadOrderList()
    },
    
    handleView(order) {
      this.currentOrder = order
      this.detailDialogVisible = true
    },
    
    handleProcess(order) {
      this.$message.info('订单处理功能开发中')
    },
    
    handleSizeChange(size) {
      this.pagination.limit = size
      this.loadOrderList()
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadOrderList()
    },
    
    getServiceTypeColor(type) {
      const colorMap = {
        express_pickup: 'primary',
        express_send: 'success',
        print_copy: 'warning',
        other: 'info'
      }
      return colorMap[type] || 'info'
    },
    
    getServiceTypeText(type) {
      const textMap = {
        express_pickup: '快递代收',
        express_send: '快递代发',
        print_copy: '打印复印',
        other: '其他服务'
      }
      return textMap[type] || '未知'
    },
    
    getStatusColor(status) {
      const colorMap = {
        pending: 'warning',
        paid: 'primary',
        processing: 'info',
        completed: 'success',
        cancelled: 'danger',
        refunded: 'danger'
      }
      return colorMap[status] || 'info'
    },
    
    getStatusText(status) {
      const textMap = {
        pending: '待支付',
        paid: '已支付',
        processing: '处理中',
        completed: '已完成',
        cancelled: '已取消',
        refunded: '已退款'
      }
      return textMap[status] || '未知'
    },
    
    formatMoney(amount) {
      if (!amount) return '0.00'
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount)
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.order-list-container {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        
        &.total { background: #909399; }
        &.pending { background: #E6A23C; }
        &.completed { background: #67C23A; }
        &.revenue { background: #409EFF; }
      }
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .filter-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .user-info {
      .phone {
        font-weight: 500;
        color: #303133;
      }
      
      .nickname {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
    
    .amount {
      font-weight: bold;
      color: #F56C6C;
    }
  }
  
  .pagination-container {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #EBEEF5;
  }
  
  .order-detail {
    padding: 20px 0;
  }
}
</style>
