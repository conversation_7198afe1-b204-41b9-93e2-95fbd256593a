<template>
  <div class="order-statistics-container">
    <div class="page-header">
      <h2><i class="el-icon-s-data"></i> 订单统计</h2>
      <p>订单数据统计分析</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon total">
          <i class="el-icon-s-order"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalOrders }}</div>
          <div class="stat-label">总订单数</div>
          <div class="stat-growth" :class="{ positive: stats.orderGrowth > 0, negative: stats.orderGrowth < 0 }">
            {{ stats.orderGrowth > 0 ? '+' : '' }}{{ stats.orderGrowth }}
          </div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon revenue">
          <i class="el-icon-money"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ formatMoney(stats.totalRevenue) }}</div>
          <div class="stat-label">总收入</div>
          <div class="stat-growth" :class="{ positive: stats.revenueGrowth > 0, negative: stats.revenueGrowth < 0 }">
            {{ stats.revenueGrowth > 0 ? '+' : '' }}¥{{ formatMoney(stats.revenueGrowth) }}
          </div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon today">
          <i class="el-icon-calendar"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.todayOrders }}</div>
          <div class="stat-label">今日订单</div>
          <div class="stat-growth positive">
            ¥{{ formatMoney(stats.todayRevenue) }}
          </div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon rate">
          <i class="el-icon-pie-chart"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.completionRate }}%</div>
          <div class="stat-label">完成率</div>
          <div class="stat-growth positive">
            +{{ stats.rateGrowth }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-container">
      <div class="chart-card">
        <div class="chart-header">
          <h3>订单趋势</h3>
          <el-radio-group v-model="trendPeriod" @change="updateTrendChart">
            <el-radio-button label="7d">7天</el-radio-button>
            <el-radio-button label="30d">30天</el-radio-button>
            <el-radio-button label="90d">90天</el-radio-button>
          </el-radio-group>
        </div>
        <div id="orderTrendChart" style="height: 400px;"></div>
      </div>
      
      <div class="chart-card">
        <div class="chart-header">
          <h3>服务类型分布</h3>
        </div>
        <div id="serviceTypeChart" style="height: 400px;"></div>
      </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="table-container">
      <div class="table-header">
        <h3>订单详细统计</h3>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="loadDetailStats"
        />
      </div>
      
      <el-table :data="detailStats" style="width: 100%">
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="totalOrders" label="总订单" width="100" />
        <el-table-column prop="completedOrders" label="已完成" width="100" />
        <el-table-column prop="cancelledOrders" label="已取消" width="100" />
        <el-table-column prop="completionRate" label="完成率" width="100">
          <template #default="scope">
            {{ scope.row.completionRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="revenue" label="收入" width="120">
          <template #default="scope">
            ¥{{ formatMoney(scope.row.revenue) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgOrderValue" label="客单价" width="120">
          <template #default="scope">
            ¥{{ formatMoney(scope.row.avgOrderValue) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'OrderStatistics',
  data() {
    return {
      stats: {
        totalOrders: 0,
        totalRevenue: 0,
        todayOrders: 0,
        todayRevenue: 0,
        orderGrowth: 0,
        revenueGrowth: 0,
        completionRate: 0,
        rateGrowth: 0
      },
      trendPeriod: '7d',
      dateRange: [],
      detailStats: [],
      trendChart: null,
      serviceChart: null
    }
  },
  mounted() {
    this.loadStats()
    this.initCharts()
    this.loadDetailStats()
  },
  beforeUnmount() {
    if (this.trendChart) {
      this.trendChart.dispose()
    }
    if (this.serviceChart) {
      this.serviceChart.dispose()
    }
  },
  methods: {
    async loadStats() {
      try {
        // 这里调用API获取统计数据
        // const response = await this.$http.get('/order/stats')
        
        // 暂时使用模拟数据
        this.stats = {
          totalOrders: 0,
          totalRevenue: 0,
          todayOrders: 0,
          todayRevenue: 0,
          orderGrowth: 0,
          revenueGrowth: 0,
          completionRate: 0,
          rateGrowth: 0
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    },
    
    initCharts() {
      this.$nextTick(() => {
        // 初始化订单趋势图表
        this.trendChart = echarts.init(document.getElementById('orderTrendChart'))
        this.updateTrendChart()
        
        // 初始化服务类型分布图表
        this.serviceChart = echarts.init(document.getElementById('serviceTypeChart'))
        this.updateServiceChart()
      })
    },
    
    async updateTrendChart() {
      try {
        // 这里调用API获取趋势数据
        // const response = await this.$http.get(`/statistics/orderTrend?period=${this.trendPeriod}`)
        
        // 暂时使用模拟数据
        const mockData = {
          dates: ['6-27', '6-28', '6-29', '6-30', '7-01', '7-02', '7-03'],
          orders: [0, 0, 0, 0, 0, 0, 0],
          revenue: [0, 0, 0, 0, 0, 0, 0]
        }
        
        this.trendChart.setOption({
          title: {
            text: '订单趋势',
            left: 'center'
          },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['订单数', '收入'],
            top: 30
          },
          xAxis: {
            type: 'category',
            data: mockData.dates
          },
          yAxis: [
            {
              type: 'value',
              name: '订单数',
              position: 'left'
            },
            {
              type: 'value',
              name: '收入(元)',
              position: 'right'
            }
          ],
          series: [
            {
              name: '订单数',
              type: 'line',
              data: mockData.orders,
              smooth: true,
              itemStyle: { color: '#409EFF' }
            },
            {
              name: '收入',
              type: 'bar',
              yAxisIndex: 1,
              data: mockData.revenue,
              itemStyle: { color: '#67C23A' }
            }
          ]
        })
      } catch (error) {
        console.error('更新趋势图表失败:', error)
      }
    },
    
    async updateServiceChart() {
      try {
        // 这里调用API获取服务类型分布数据
        // const response = await this.$http.get('/statistics/orderTypeDistribution')
        
        // 暂时使用模拟数据
        const mockData = [
          { name: '快递代收', value: 0, color: '#409EFF' },
          { name: '快递代发', value: 0, color: '#67C23A' },
          { name: '打印复印', value: 0, color: '#E6A23C' },
          { name: '其他服务', value: 0, color: '#F56C6C' }
        ]
        
        this.serviceChart.setOption({
          title: {
            text: '服务类型分布',
            left: 'center'
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            top: 'middle'
          },
          series: [
            {
              name: '服务类型',
              type: 'pie',
              radius: '50%',
              center: ['60%', '50%'],
              data: mockData.map(item => ({
                name: item.name,
                value: item.value,
                itemStyle: { color: item.color }
              })),
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        })
      } catch (error) {
        console.error('更新服务类型图表失败:', error)
      }
    },
    
    async loadDetailStats() {
      try {
        // 这里调用API获取详细统计数据
        // const response = await this.$http.get('/order/detailStats', { params: { dateRange: this.dateRange } })
        
        // 暂时使用空数据
        this.detailStats = []
      } catch (error) {
        console.error('获取详细统计失败:', error)
      }
    },
    
    formatMoney(amount) {
      if (!amount) return '0.00'
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount)
    }
  }
}
</script>

<style lang="scss" scoped>
.order-statistics-container {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        
        &.total { background: #409EFF; }
        &.revenue { background: #67C23A; }
        &.today { background: #E6A23C; }
        &.rate { background: #F56C6C; }
      }
      
      .stat-content {
        flex: 1;
        
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin: 4px 0;
        }
        
        .stat-growth {
          font-size: 12px;
          
          &.positive {
            color: #67C23A;
          }
          
          &.negative {
            color: #F56C6C;
          }
        }
      }
    }
  }
  
  .charts-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 24px;
    
    .chart-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
          margin: 0;
          color: #303133;
        }
      }
    }
  }
  
  .table-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h3 {
        margin: 0;
        color: #303133;
      }
    }
  }
}
</style>
