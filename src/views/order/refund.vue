<template>
  <div class="refund-container">
    <div class="page-header">
      <h2><i class="el-icon-refresh-left"></i> 退款管理</h2>
      <p>处理订单退款申请和退款记录</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon total">
          <i class="el-icon-document"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total }}</div>
          <div class="stat-label">总退款申请</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon pending">
          <i class="el-icon-time"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.pending }}</div>
          <div class="stat-label">待处理</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon approved">
          <i class="el-icon-check"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.approved }}</div>
          <div class="stat-label">已同意</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon amount">
          <i class="el-icon-money"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ formatMoney(stats.amount) }}</div>
          <div class="stat-label">退款金额</div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="订单编号">
          <el-input v-model="searchForm.orderNo" placeholder="请输入订单编号" clearable />
        </el-form-item>
        <el-form-item label="退款状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="待处理" value="pending" />
            <el-option label="已同意" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="退款中" value="processing" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="refundList"
        style="width: 100%"
      >
        <el-table-column prop="refund_no" label="退款编号" width="160" />
        <el-table-column prop="order_no" label="订单编号" width="160" />
        <el-table-column prop="user_phone" label="用户手机号" width="120" />
        <el-table-column prop="refund_amount" label="退款金额" width="100">
          <template #default="scope">
            <span class="amount">¥{{ formatMoney(scope.row.refund_amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="refund_reason" label="退款原因" min-width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="申请时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              <i class="el-icon-view"></i> 查看
            </el-button>
            <el-button 
              v-if="scope.row.status === 'pending'" 
              type="text" 
              size="small" 
              @click="handleProcess(scope.row)"
            >
              <i class="el-icon-s-tools"></i> 处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OrderRefund',
  data() {
    return {
      loading: false,
      refundList: [],
      searchForm: {
        orderNo: '',
        status: ''
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      stats: {
        total: 0,
        pending: 0,
        approved: 0,
        amount: 0
      }
    }
  },
  mounted() {
    this.loadStats()
    this.loadRefundList()
  },
  methods: {
    async loadStats() {
      this.stats = {
        total: 0,
        pending: 0,
        approved: 0,
        amount: 0
      }
    },
    
    async loadRefundList() {
      this.loading = true
      try {
        this.refundList = []
        this.pagination.total = 0
      } catch (error) {
        console.error('加载退款列表失败:', error)
        this.$message.error('加载退款列表失败')
      } finally {
        this.loading = false
      }
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadRefundList()
    },
    
    handleReset() {
      this.searchForm = {
        orderNo: '',
        status: ''
      }
      this.pagination.page = 1
      this.loadRefundList()
    },
    
    handleView(refund) {
      this.$message.info('查看退款详情功能开发中')
    },
    
    handleProcess(refund) {
      this.$message.info('处理退款功能开发中')
    },
    
    handleSizeChange(size) {
      this.pagination.limit = size
      this.loadRefundList()
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadRefundList()
    },
    
    getStatusColor(status) {
      const colorMap = {
        pending: 'warning',
        approved: 'success',
        rejected: 'danger',
        processing: 'primary',
        completed: 'success'
      }
      return colorMap[status] || 'info'
    },
    
    getStatusText(status) {
      const textMap = {
        pending: '待处理',
        approved: '已同意',
        rejected: '已拒绝',
        processing: '退款中',
        completed: '已完成'
      }
      return textMap[status] || '未知'
    },
    
    formatMoney(amount) {
      if (!amount) return '0.00'
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount)
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.refund-container {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        
        &.total { background: #909399; }
        &.pending { background: #E6A23C; }
        &.approved { background: #67C23A; }
        &.amount { background: #F56C6C; }
      }
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .filter-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .amount {
      font-weight: bold;
      color: #F56C6C;
    }
  }
  
  .pagination-container {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #EBEEF5;
  }
}
</style>
