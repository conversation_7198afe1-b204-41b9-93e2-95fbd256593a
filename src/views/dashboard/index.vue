<template>
  <div class="dashboard-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1>欢迎回来！</h1>
          <p>驿站帮Pro后台管理系统 - 让管理更简单高效</p>
          <div class="current-time">{{ currentTime }}</div>
        </div>
        <div class="welcome-illustration">
          <i class="el-icon-data-analysis welcome-icon"></i>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card users">
        <div class="stat-icon">
          <i class="el-icon-user-solid"></i>
        </div>
        <div class="stat-content">
          <div class="stat-title">总用户数</div>
          <div class="stat-value">{{ dashboardData.totalUsers || 1256 }}</div>
          <div class="stat-compare">较昨日 <span class="up">+{{ dashboardData.userGrowth || 18 }}</span></div>
        </div>
      </div>

      <div class="stat-card stations">
        <div class="stat-icon">
          <i class="el-icon-s-shop"></i>
        </div>
        <div class="stat-content">
          <div class="stat-title">驿站数量</div>
          <div class="stat-value">{{ dashboardData.totalStations || 358 }}</div>
          <div class="stat-compare">较昨日 <span class="up">+{{ dashboardData.stationGrowth || 5 }}</span></div>
        </div>
      </div>

      <div class="stat-card orders">
        <div class="stat-icon">
          <i class="el-icon-s-order"></i>
        </div>
        <div class="stat-content">
          <div class="stat-title">订单总数</div>
          <div class="stat-value">{{ dashboardData.totalOrders || 6832 }}</div>
          <div class="stat-compare">较昨日 <span class="up">+{{ dashboardData.orderGrowth || 43 }}</span></div>
        </div>
      </div>

      <div class="stat-card revenue">
        <div class="stat-icon">
          <i class="el-icon-money"></i>
        </div>
        <div class="stat-content">
          <div class="stat-title">总收入(元)</div>
          <div class="stat-value">{{ formatMoney(dashboardData.totalRevenue || 235698.56) }}</div>
          <div class="stat-compare">较昨日 <span class="up">+{{ formatMoney(dashboardData.revenueGrowth || 1268.32) }}</span></div>
        </div>
      </div>
    </div>
    
    <!-- 图表区域 -->
    <div class="chart-container">
      <div class="chart-card wide">
        <div class="chart-header">
          <h3><i class="el-icon-trend-charts"></i> 订单走势分析</h3>
          <div class="chart-actions">
            <el-button-group>
              <el-button size="small" :type="chartPeriod === '7d' ? 'primary' : ''" @click="changeChartPeriod('7d')">7天</el-button>
              <el-button size="small" :type="chartPeriod === '30d' ? 'primary' : ''" @click="changeChartPeriod('30d')">30天</el-button>
              <el-button size="small" :type="chartPeriod === '90d' ? 'primary' : ''" @click="changeChartPeriod('90d')">90天</el-button>
            </el-button-group>
          </div>
        </div>
        <div id="orderTrendChart" style="height: 350px;"></div>
      </div>

      <div class="chart-card">
        <div class="chart-header">
          <h3><i class="el-icon-pie-chart"></i> 驿站类型分布</h3>
        </div>
        <div id="stationDistChart" style="height: 350px;"></div>
      </div>
    </div>

    <!-- 快捷操作区域 -->
    <div class="quick-actions">
      <div class="action-card">
        <div class="action-icon">
          <i class="el-icon-plus"></i>
        </div>
        <div class="action-content">
          <h4>新增驿站</h4>
          <p>快速添加新的驿站点</p>
        </div>
      </div>

      <div class="action-card">
        <div class="action-icon">
          <i class="el-icon-view"></i>
        </div>
        <div class="action-content">
          <h4>订单管理</h4>
          <p>查看和处理订单信息</p>
        </div>
      </div>

      <div class="action-card">
        <div class="action-icon">
          <i class="el-icon-s-data"></i>
        </div>
        <div class="action-content">
          <h4>数据统计</h4>
          <p>查看详细的数据分析</p>
        </div>
      </div>

      <div class="action-card">
        <div class="action-icon">
          <i class="el-icon-setting"></i>
        </div>
        <div class="action-content">
          <h4>系统设置</h4>
          <p>配置系统参数</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getDashboardData } from '@/api/statistics'

export default {
  name: 'Dashboard',
  data() {
    return {
      orderChart: null,
      stationChart: null,
      currentTime: '',
      chartPeriod: '7d',
      dashboardData: {
        totalUsers: 0,
        totalStations: 0,
        totalOrders: 0,
        totalRevenue: 0,
        userGrowth: 0,
        stationGrowth: 0,
        orderGrowth: 0,
        revenueGrowth: 0
      },
      loading: false
    }
  },
  mounted() {
    this.updateTime()
    this.timeInterval = setInterval(this.updateTime, 1000)
    this.loadDashboardData()
    this.initCharts()
  },
  beforeUnmount() {
    // 清除定时器
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
    // 在组件销毁前销毁图表实例
    if (this.orderChart) {
      this.orderChart.dispose()
    }
    if (this.stationChart) {
      this.stationChart.dispose()
    }
    // 移除窗口大小变化的监听器
    window.removeEventListener('resize', this.resizeCharts)
  },
  methods: {
    // 更新时间
    updateTime() {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },

    // 格式化金额
    formatMoney(amount) {
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount)
    },

    // 加载仪表板数据
    async loadDashboardData() {
      try {
        this.loading = true
        const response = await getDashboardData()
        if (response.code === 0) {
          this.dashboardData = response.data
        }
      } catch (error) {
        console.error('加载仪表板数据失败:', error)
        // 使用模拟数据
        this.dashboardData = {
          totalUsers: 1256,
          totalStations: 358,
          totalOrders: 6832,
          totalRevenue: 235698.56,
          userGrowth: 18,
          stationGrowth: 5,
          orderGrowth: 43,
          revenueGrowth: 1268.32
        }
      } finally {
        this.loading = false
      }
    },

    // 切换图表时间周期
    changeChartPeriod(period) {
      this.chartPeriod = period
      this.updateChartData()
    },

    // 更新图表数据
    async updateChartData() {
      try {
        // 调用API获取真实数据
        const response = await this.$http.get(`/statistics/orderTrend?period=${this.chartPeriod}`)
        if (response.data.code === 0) {
          const trendData = response.data.data
          const dates = trendData.map(item => item.date)
          const orders = trendData.map(item => item.orders)
          const revenue = trendData.map(item => item.revenue)

          this.orderChart.setOption({
            xAxis: {
              data: dates
            },
            series: [
              {
                data: orders
              },
              {
                data: revenue
              }
            ]
          })
        }
      } catch (error) {
        console.error('更新图表数据失败:', error)
        // 如果API调用失败，使用本地模拟数据
        const data = this.getChartDataByPeriod(this.chartPeriod)
        this.orderChart.setOption({
          xAxis: {
            data: data.dates
          },
          series: [
            {
              data: data.orders
            },
            {
              data: data.revenue
            }
          ]
        })
      }
    },

    // 根据时间周期获取图表数据
    getChartDataByPeriod(period) {
      // 这里应该调用API获取真实数据，现在使用模拟数据
      const mockData = {
        '7d': {
          dates: ['6-27', '6-28', '6-29', '6-30', '7-01', '7-02', '7-03'],
          orders: [120, 132, 101, 134, 190, 230, 210],
          revenue: [12000, 13200, 10100, 13400, 19000, 23000, 21000]
        },
        '30d': {
          dates: ['6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          orders: [820, 932, 901, 934, 1290, 1330, 1320],
          revenue: [22000, 25000, 24000, 26000, 32000, 35000, 34000]
        },
        '90d': {
          dates: ['Q1', 'Q2', 'Q3', 'Q4'],
          orders: [2653, 2890, 3120, 3456],
          revenue: [71000, 78000, 85000, 92000]
        }
      }
      return mockData[period] || mockData['7d']
    },

    async initCharts() {
      this.$nextTick(async () => {
        // 初始化订单走势图表
        this.orderChart = echarts.init(document.getElementById('orderTrendChart'))

        // 获取初始图表数据
        let chartData = this.getChartDataByPeriod(this.chartPeriod)
        try {
          const response = await this.$http.get(`/statistics/orderTrend?period=${this.chartPeriod}`)
          if (response.data.code === 0) {
            const trendData = response.data.data
            chartData = {
              dates: trendData.map(item => item.date),
              orders: trendData.map(item => item.orders),
              revenue: trendData.map(item => item.revenue)
            }
          }
        } catch (error) {
          console.error('获取图表数据失败:', error)
        }

        this.orderChart.setOption({
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderColor: '#e6e6e6',
            borderWidth: 1,
            textStyle: {
              color: '#333'
            }
          },
          legend: {
            data: ['订单数量', '销售额'],
            top: 10
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: chartData.dates,
            axisLine: {
              lineStyle: {
                color: '#e6e6e6'
              }
            }
          },
          yAxis: [
            {
              type: 'value',
              name: '订单数量',
              axisLine: {
                lineStyle: {
                  color: '#e6e6e6'
                }
              }
            },
            {
              type: 'value',
              name: '销售额',
              position: 'right',
              axisLabel: {
                formatter: '{value} 元'
              },
              axisLine: {
                lineStyle: {
                  color: '#e6e6e6'
                }
              }
            }
          ],
          series: [
            {
              name: '订单数量',
              type: 'line',
              data: chartData.orders,
              smooth: true,
              lineStyle: {
                width: 3,
                color: '#409EFF'
              },
              areaStyle: {
                opacity: 0.3,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#409EFF' },
                  { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                ])
              }
            },
            {
              name: '销售额',
              type: 'line',
              yAxisIndex: 1,
              data: chartData.revenue,
              smooth: true,
              lineStyle: {
                width: 3,
                color: '#67C23A'
              },
              areaStyle: {
                opacity: 0.3,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#67C23A' },
                  { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
                ])
              }
            }
          ]
        })

        // 初始化驿站分布图表
        this.stationChart = echarts.init(document.getElementById('stationDistChart'))

        // 获取驿站分布数据
        let stationData = [
          { value: 135, name: '社区驿站', itemStyle: { color: '#409EFF' } },
          { value: 86, name: '快递驿站', itemStyle: { color: '#67C23A' } },
          { value: 45, name: '校园驿站', itemStyle: { color: '#E6A23C' } },
          { value: 68, name: '写字楼驿站', itemStyle: { color: '#F56C6C' } },
          { value: 24, name: '商业驿站', itemStyle: { color: '#909399' } }
        ]

        try {
          const response = await this.$http.get('/statistics/orderTypeDistribution')
          if (response.data.code === 0) {
            stationData = response.data.data.map(item => ({
              value: item.value,
              name: item.name,
              itemStyle: { color: item.color }
            }))
          }
        } catch (error) {
          console.error('获取驿站分布数据失败:', error)
        }

        this.stationChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderColor: '#e6e6e6',
            borderWidth: 1,
            textStyle: {
              color: '#333'
            }
          },
          legend: {
            orient: 'vertical',
            left: 10,
            top: 'center',
            data: ['社区驿站', '快递驿站', '校园驿站', '写字楼驿站', '商业驿站']
          },
          series: [
            {
              name: '驿站类型',
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['60%', '50%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '18',
                  fontWeight: 'bold'
                },
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              },
              labelLine: {
                show: false
              },
              data: stationData
            }
          ]
        })

        // 监听窗口大小变化，重新调整图表大小
        window.addEventListener('resize', this.resizeCharts)
      })
    },

    resizeCharts() {
      this.orderChart && this.orderChart.resize()
      this.stationChart && this.stationChart.resize()
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: calc(100vh - 50px);

  // 欢迎横幅
  .welcome-banner {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 30px;
    margin: -20px -20px 30px -20px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      opacity: 0.3;
    }

    .welcome-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      z-index: 1;

      .welcome-text {
        h1 {
          font-size: 32px;
          margin: 0 0 10px 0;
          font-weight: 600;
        }

        p {
          font-size: 16px;
          margin: 0 0 15px 0;
          opacity: 0.9;
        }

        .current-time {
          font-size: 14px;
          opacity: 0.8;
          background: rgba(255, 255, 255, 0.1);
          padding: 8px 16px;
          border-radius: 20px;
          display: inline-block;
        }
      }

      .welcome-illustration {
        .welcome-icon {
          font-size: 80px;
          opacity: 0.3;
        }
      }
    }
  }

  // 统计卡片
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin: 0 30px 30px 30px;

    .stat-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #409EFF, #67C23A);
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
      }

      &.users::before { background: linear-gradient(90deg, #409EFF, #5dade2); }
      &.stations::before { background: linear-gradient(90deg, #67C23A, #58d68d); }
      &.orders::before { background: linear-gradient(90deg, #E6A23C, #f7dc6f); }
      &.revenue::before { background: linear-gradient(90deg, #F56C6C, #ec7063); }

      display: flex;
      align-items: center;
      gap: 20px;

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;

        .users & { background: linear-gradient(135deg, #409EFF, #5dade2); }
        .stations & { background: linear-gradient(135deg, #67C23A, #58d68d); }
        .orders & { background: linear-gradient(135deg, #E6A23C, #f7dc6f); }
        .revenue & { background: linear-gradient(135deg, #F56C6C, #ec7063); }
      }

      .stat-content {
        flex: 1;

        .stat-title {
          font-size: 14px;
          color: #909399;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .stat-value {
          font-size: 28px;
          color: #303133;
          font-weight: 700;
          margin-bottom: 8px;
          line-height: 1;
        }

        .stat-compare {
          font-size: 12px;
          color: #909399;

          .up {
            color: #67C23A;
            font-weight: 600;
          }

          .down {
            color: #F56C6C;
            font-weight: 600;
          }
        }
      }
    }
  }
  
  // 图表容器
  .chart-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin: 0 30px 30px 30px;

    @media (max-width: 1200px) {
      grid-template-columns: 1fr;
    }

    .chart-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
      }

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          margin: 0;
          color: #303133;
          font-size: 18px;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 8px;

          i {
            color: #409EFF;
          }
        }

        .chart-actions {
          .el-button-group {
            .el-button {
              border-radius: 20px;

              &:first-child {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
              }

              &:last-child {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
              }

              &:not(:first-child):not(:last-child) {
                border-radius: 0;
              }
            }
          }
        }
      }
    }
  }

  // 快捷操作
  .quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 0 30px 30px 30px;

    .action-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 16px;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);

        .action-icon {
          transform: scale(1.1);
        }
      }

      .action-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20px;
        transition: all 0.3s ease;
      }

      .action-content {
        flex: 1;

        h4 {
          margin: 0 0 4px 0;
          color: #303133;
          font-size: 16px;
          font-weight: 600;
        }

        p {
          margin: 0;
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    .welcome-banner {
      padding: 30px 20px;
      margin: -20px -20px 20px -20px;

      .welcome-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;

        .welcome-illustration {
          .welcome-icon {
            font-size: 60px;
          }
        }
      }
    }

    .stats-cards {
      grid-template-columns: 1fr;
      margin: 0 20px 20px 20px;
    }

    .chart-container {
      margin: 0 20px 20px 20px;
    }

    .quick-actions {
      grid-template-columns: 1fr;
      margin: 0 20px 20px 20px;
    }
  }
}
</style> 