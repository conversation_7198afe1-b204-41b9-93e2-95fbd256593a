<template>
  <div class="dashboard-container">
    <h2>欢迎使用驿站帮Pro后台管理系统</h2>
    
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-title">总用户数</div>
        <div class="stat-value">1256</div>
        <div class="stat-compare">较昨日 <span class="up">+18</span></div>
      </div>
      
      <div class="stat-card">
        <div class="stat-title">驿站数量</div>
        <div class="stat-value">358</div>
        <div class="stat-compare">较昨日 <span class="up">+5</span></div>
      </div>
      
      <div class="stat-card">
        <div class="stat-title">订单总数</div>
        <div class="stat-value">6832</div>
        <div class="stat-compare">较昨日 <span class="up">+43</span></div>
      </div>
      
      <div class="stat-card">
        <div class="stat-title">总收入(元)</div>
        <div class="stat-value">235698.56</div>
        <div class="stat-compare">较昨日 <span class="up">+1268.32</span></div>
      </div>
    </div>
    
    <div class="chart-container">
      <div class="chart-card wide">
        <h3>订单走势</h3>
        <div id="orderTrendChart" style="height: 300px;"></div>
      </div>
      
      <div class="chart-card">
        <h3>驿站分布</h3>
        <div id="stationDistChart" style="height: 300px;"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'Dashboard',
  data() {
    return {
      orderChart: null,
      stationChart: null
    }
  },
  mounted() {
    this.initCharts()
  },
  beforeUnmount() {
    // 在组件销毁前销毁图表实例
    if (this.orderChart) {
      this.orderChart.dispose()
    }
    if (this.stationChart) {
      this.stationChart.dispose()
    }
    // 移除窗口大小变化的监听器
    window.removeEventListener('resize', this.resizeCharts)
  },
  methods: {
    initCharts() {
      this.$nextTick(() => {
        // 初始化订单走势图表
        this.orderChart = echarts.init(document.getElementById('orderTrendChart'))
        this.orderChart.setOption({
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['订单数量', '销售额']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
          },
          yAxis: [
            {
              type: 'value',
              name: '订单数量'
            },
            {
              type: 'value',
              name: '销售额',
              position: 'right',
              axisLabel: {
                formatter: '{value} 元'
              }
            }
          ],
          series: [
            {
              name: '订单数量',
              type: 'line',
              data: [820, 932, 901, 934, 1290, 1330, 1320],
              smooth: true,
              lineStyle: {
                width: 3,
                color: '#409EFF'
              },
              areaStyle: {
                opacity: 0.2,
                color: '#409EFF'
              }
            },
            {
              name: '销售额',
              type: 'line',
              yAxisIndex: 1,
              data: [22000, 25000, 24000, 26000, 32000, 35000, 34000],
              smooth: true,
              lineStyle: {
                width: 3,
                color: '#67C23A'
              },
              areaStyle: {
                opacity: 0.2,
                color: '#67C23A'
              }
            }
          ]
        })

        // 初始化驿站分布图表
        this.stationChart = echarts.init(document.getElementById('stationDistChart'))
        this.stationChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 10,
            data: ['社区驿站', '快递驿站', '校园驿站', '写字楼驿站', '商业驿站']
          },
          series: [
            {
              name: '驿站类型',
              type: 'pie',
              radius: '70%',
              center: ['50%', '60%'],
              data: [
                { value: 135, name: '社区驿站' },
                { value: 86, name: '快递驿站' },
                { value: 45, name: '校园驿站' },
                { value: 68, name: '写字楼驿站' },
                { value: 24, name: '商业驿站' }
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        })

        // 监听窗口大小变化，重新调整图表大小
        window.addEventListener('resize', this.resizeCharts)
      })
    },
    resizeCharts() {
      this.orderChart && this.orderChart.resize()
      this.stationChart && this.stationChart.resize()
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  
  h2 {
    margin-bottom: 20px;
    color: #303133;
  }
  
  .stats-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    
    .stat-card {
      flex: 1;
      min-width: 200px;
      background-color: #fff;
      border-radius: 4px;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      
      .stat-title {
        font-size: 14px;
        color: #909399;
        margin-bottom: 10px;
      }
      
      .stat-value {
        font-size: 24px;
        color: #409EFF;
        font-weight: bold;
        margin-bottom: 10px;
      }
      
      .stat-compare {
        font-size: 12px;
        color: #909399;
        
        .up {
          color: #67C23A;
        }
        
        .down {
          color: #F56C6C;
        }
      }
    }
  }
  
  .chart-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    
    .chart-card {
      flex: 1;
      min-width: 300px;
      background-color: #fff;
      border-radius: 4px;
      padding: 20px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      
      &.wide {
        flex: 2;
        min-width: 600px;
      }
      
      h3 {
        margin-top: 0;
        margin-bottom: 20px;
        color: #303133;
        font-size: 16px;
      }
      
      .chart-placeholder {
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #909399;
      }
    }
  }
}
</style> 