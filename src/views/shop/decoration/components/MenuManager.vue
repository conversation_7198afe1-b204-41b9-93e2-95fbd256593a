<template>
  <div class="menu-manager">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>底部菜单设置</span>
          <div>
            <el-button type="primary" @click="handleSave" v-hasPermi="['shop:menu:save']">
              <el-icon><Check /></el-icon> 保存设置
            </el-button>
            <el-button type="success" @click="handleApply" v-hasPermi="['shop:menu:apply']">
              <el-icon><Upload /></el-icon> 应用到小程序
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="menu-preview">
        <div class="mock-phone">
          <div class="mock-phone-content"></div>
          <div class="mock-phone-tabbar" :style="{ backgroundColor: menuStyle.backgroundColor }">
            <div v-for="(item, index) in menuItems" :key="index" class="mock-tabbar-item" 
              :class="{ active: currentTab === index }" @click="currentTab = index">
              <div class="mock-tabbar-icon" :style="{ color: currentTab === index ? menuStyle.selectedColor : menuStyle.color }">
                <svg-icon :icon-class="item.icon" />
              </div>
              <div class="mock-tabbar-text" 
                :style="{ color: currentTab === index ? menuStyle.selectedColor : menuStyle.color }">
                {{ item.text }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <el-form :model="menuStyle" :rules="styleRules" ref="styleFormRef" label-width="100px" class="style-form">
        <el-form-item label="背景颜色" prop="backgroundColor">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-color-picker v-model="menuStyle.backgroundColor" show-alpha />
            </el-col>
            <el-col :span="16">
              <el-input v-model="menuStyle.backgroundColor" placeholder="请输入颜色值" />
            </el-col>
          </el-row>
        </el-form-item>
        
        <el-form-item label="文字颜色" prop="color">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-color-picker v-model="menuStyle.color" show-alpha />
            </el-col>
            <el-col :span="16">
              <el-input v-model="menuStyle.color" placeholder="请输入颜色值" />
            </el-col>
          </el-row>
        </el-form-item>
        
        <el-form-item label="选中颜色" prop="selectedColor">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-color-picker v-model="menuStyle.selectedColor" show-alpha />
            </el-col>
            <el-col :span="16">
              <el-input v-model="menuStyle.selectedColor" placeholder="请输入颜色值" />
            </el-col>
          </el-row>
        </el-form-item>
        
        <el-form-item label="边框颜色" prop="borderStyle">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-color-picker v-model="menuStyle.borderStyle" show-alpha />
            </el-col>
            <el-col :span="16">
              <el-input v-model="menuStyle.borderStyle" placeholder="请输入颜色值" />
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      
      <el-divider content-position="left">菜单项设置</el-divider>
      
      <div class="menu-items-container">
        <el-alert type="warning" :closable="false" show-icon>
          <template #title>温馨提示</template>
          <p>小程序底部菜单至少需要2个，最多不超过5个</p>
        </el-alert>
        
        <draggable 
          v-model="menuItems" 
          handle=".drag-handle"
          item-key="id"
          :animation="200"
          ghost-class="ghost"
          class="menu-items-list"
        >
          <template #item="{ element, index }">
            <div class="menu-item-card">
              <div class="menu-item-header">
                <span class="drag-handle"><el-icon><Rank /></el-icon></span>
                <span>{{ `菜单项 ${index + 1}` }}</span>
                <div>
                  <el-button link type="primary" @click="showEditDialog(index)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button link type="danger" @click="handleDeleteItem(index)" :disabled="menuItems.length <= 2">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
              <div class="menu-item-content">
                <div class="item-preview">
                  <div class="item-icon">
                    <svg-icon :icon-class="element.icon" :style="{ color: menuStyle.color }" />
                  </div>
                  <div class="item-text" :style="{ color: menuStyle.color }">{{ element.text }}</div>
                </div>
                <div class="item-info">
                  <el-descriptions :column="1" size="small" border>
                    <el-descriptions-item label="页面路径">{{ element.pagePath }}</el-descriptions-item>
                    <el-descriptions-item label="选中图标">{{ element.selectedIconPath }}</el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
            </div>
          </template>
        </draggable>
        
        <div class="menu-actions">
          <el-button type="primary" @click="handleAddItem" :disabled="menuItems.length >= 5">
            <el-icon><Plus /></el-icon> 添加菜单项
          </el-button>
        </div>
      </div>
    </el-card>
    
    <!-- 菜单项编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加菜单项' : '编辑菜单项'"
      width="500px"
      append-to-body
    >
      <el-form :model="currentItem" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="菜单文本" prop="text">
          <el-input v-model="currentItem.text" placeholder="请输入菜单文本" maxlength="4" show-word-limit />
        </el-form-item>
        
        <el-form-item label="页面路径" prop="pagePath">
          <el-select v-model="currentItem.pagePath" placeholder="请选择页面路径" style="width: 100%">
            <el-option
              v-for="item in pageOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="图标" prop="icon">
          <el-select v-model="currentItem.icon" placeholder="请选择图标" style="width: 100%">
            <el-option-group label="菜单图标">
              <el-option
                v-for="icon in iconOptions"
                :key="icon.value"
                :label="icon.label"
                :value="icon.value"
              >
                <div class="icon-option">
                  <svg-icon :icon-class="icon.value" />
                  <span>{{ icon.label }}</span>
                </div>
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        
        <el-form-item label="选中图标" prop="selectedIconPath">
          <el-select v-model="currentItem.selectedIconPath" placeholder="请选择选中状态图标" style="width: 100%">
            <el-option
              v-for="icon in iconOptions"
              :key="icon.value"
              :label="icon.label"
              :value="icon.value"
            >
              <div class="icon-option">
                <svg-icon :icon-class="icon.value" />
                <span>{{ icon.label }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getMenuConfig, 
  saveMenuConfig, 
  applyMenuConfig 
} from '@/api/shop/decoration';
import draggable from 'vuedraggable';

export default {
  name: 'MenuManager',
  components: {
    draggable
  },
  data() {
    return {
      // 菜单样式
      menuStyle: {
        backgroundColor: '#ffffff',
        color: '#979797',
        selectedColor: '#1890ff',
        borderStyle: '#e6e6e6'
      },
      // 菜单项
      menuItems: [
        {
          id: 1,
          text: '首页',
          pagePath: 'pages/index/index',
          icon: 'dashboard',
          selectedIconPath: 'dashboard'
        },
        {
          id: 2,
          text: '驿站',
          pagePath: 'pages/station/index',
          icon: 'shop',
          selectedIconPath: 'shop'
        },
        {
          id: 3,
          text: '订单',
          pagePath: 'pages/order/index',
          icon: 'shopping',
          selectedIconPath: 'shopping'
        },
        {
          id: 4,
          text: '我的',
          pagePath: 'pages/user/index',
          icon: 'user',
          selectedIconPath: 'user'
        }
      ],
      // 当前激活的选项卡
      currentTab: 0,
      // 对话框相关
      dialogVisible: false,
      dialogType: 'add', // 'add' 或 'edit'
      editIndex: -1,
      currentItem: {
        text: '',
        pagePath: '',
        icon: '',
        selectedIconPath: ''
      },
      // 页面路径选项
      pageOptions: [
        { value: 'pages/index/index', label: '首页' },
        { value: 'pages/station/index', label: '驿站列表' },
        { value: 'pages/station/detail', label: '驿站详情' },
        { value: 'pages/transfer/index', label: '转让市场' },
        { value: 'pages/order/index', label: '订单列表' },
        { value: 'pages/order/detail', label: '订单详情' },
        { value: 'pages/user/index', label: '个人中心' },
        { value: 'pages/user/profile', label: '个人资料' },
        { value: 'pages/user/wallet', label: '我的钱包' },
        { value: 'pages/user/favorite', label: '我的收藏' },
        { value: 'pages/message/index', label: '消息中心' },
      ],
      // 图标选项
      iconOptions: [
        { value: 'dashboard', label: '首页' },
        { value: 'shop', label: '商店' },
        { value: 'shopping', label: '购物' },
        { value: 'user', label: '用户' },
        { value: 'message', label: '消息' },
        { value: 'star', label: '收藏' },
        { value: 'cart', label: '购物车' },
        { value: 'wallet', label: '钱包' },
        { value: 'search', label: '搜索' },
        { value: 'like', label: '喜欢' },
        { value: 'phone', label: '电话' },
        { value: 'location', label: '位置' }
      ],
      // 表单校验规则
      styleRules: {
        backgroundColor: [
          { required: true, message: '请选择背景颜色', trigger: 'change' }
        ],
        color: [
          { required: true, message: '请选择文字颜色', trigger: 'change' }
        ],
        selectedColor: [
          { required: true, message: '请选择选中颜色', trigger: 'change' }
        ],
        borderStyle: [
          { required: true, message: '请选择边框颜色', trigger: 'change' }
        ]
      },
      formRules: {
        text: [
          { required: true, message: '请输入菜单文本', trigger: 'blur' },
          { max: 4, message: '菜单文本不能超过4个字符', trigger: 'blur' }
        ],
        pagePath: [
          { required: true, message: '请选择页面路径', trigger: 'change' }
        ],
        icon: [
          { required: true, message: '请选择图标', trigger: 'change' }
        ],
        selectedIconPath: [
          { required: true, message: '请选择选中图标', trigger: 'change' }
        ]
      },
      loading: false
    };
  },
  created() {
    this.fetchMenuConfig();
  },
  methods: {
    // 获取菜单配置
    fetchMenuConfig() {
      this.loading = true;
      getMenuConfig().then(response => {
        if (response.data) {
          const { style, list } = response.data;
          if (style) {
            this.menuStyle = style;
          }
          if (list && list.length) {
            this.menuItems = list.map((item, index) => ({
              ...item,
              id: index + 1
            }));
          }
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    // 保存菜单配置
    handleSave() {
      this.$refs.styleFormRef.validate(valid => {
        if (valid) {
          this.loading = true;
          const data = {
            style: this.menuStyle,
            list: this.menuItems.map(item => ({
              text: item.text,
              pagePath: item.pagePath,
              icon: item.icon,
              selectedIconPath: item.selectedIconPath
            }))
          };
          
          saveMenuConfig(data).then(() => {
            this.$modal.msgSuccess('保存成功');
            this.loading = false;
          }).catch(() => {
            this.loading = false;
          });
        }
      });
    },
    
    // 应用到小程序
    handleApply() {
      this.$modal.confirm('确认将当前菜单设置应用到小程序吗？').then(() => {
        const data = {
          style: this.menuStyle,
          list: this.menuItems.map(item => ({
            text: item.text,
            pagePath: item.pagePath,
            icon: item.icon,
            selectedIconPath: item.selectedIconPath
          }))
        };
        
        return applyMenuConfig(data);
      }).then(() => {
        this.$modal.msgSuccess('应用成功');
      }).catch(() => {});
    },
    
    // 显示编辑对话框
    showEditDialog(index) {
      this.dialogType = 'edit';
      this.editIndex = index;
      this.currentItem = JSON.parse(JSON.stringify(this.menuItems[index]));
      this.dialogVisible = true;
    },
    
    // 处理提交
    handleSubmit() {
      this.$refs.formRef.validate(valid => {
        if (valid) {
          if (this.dialogType === 'add') {
            // 添加菜单项
            this.menuItems.push({
              ...this.currentItem,
              id: Date.now() // 生成唯一ID
            });
          } else {
            // 更新菜单项
            this.menuItems.splice(this.editIndex, 1, {
              ...this.currentItem,
              id: this.menuItems[this.editIndex].id
            });
          }
          this.dialogVisible = false;
        }
      });
    },
    
    // 添加菜单项
    handleAddItem() {
      if (this.menuItems.length >= 5) {
        this.$modal.msgWarning('小程序底部菜单最多不超过5个');
        return;
      }
      
      this.dialogType = 'add';
      this.currentItem = {
        text: '',
        pagePath: '',
        icon: '',
        selectedIconPath: ''
      };
      this.dialogVisible = true;
    },
    
    // 删除菜单项
    handleDeleteItem(index) {
      if (this.menuItems.length <= 2) {
        this.$modal.msgWarning('小程序底部菜单至少需要2个');
        return;
      }
      
      this.$modal.confirm('确定要删除该菜单项吗？').then(() => {
        this.menuItems.splice(index, 1);
        // 如果当前选中的是被删除的菜单项，重置选中状态
        if (this.currentTab === index) {
          this.currentTab = 0;
        } else if (this.currentTab > index) {
          // 如果当前选中的是被删除菜单项之后的菜单，索引减1
          this.currentTab--;
        }
      }).catch(() => {});
    }
  }
};
</script>

<style scoped lang="scss">
.menu-manager {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .menu-preview {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    
    .mock-phone {
      width: 300px;
      border: 10px solid #333;
      border-radius: 20px;
      overflow: hidden;
      
      .mock-phone-content {
        height: 160px;
        background: #f5f5f5;
      }
      
      .mock-phone-tabbar {
        height: 50px;
        display: flex;
        border-top: 1px solid;
        border-top-color: v-bind('menuStyle.borderStyle');
        
        .mock-tabbar-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          
          .mock-tabbar-icon {
            font-size: 18px;
            margin-bottom: 2px;
          }
          
          .mock-tabbar-text {
            font-size: 12px;
          }
        }
      }
    }
  }
  
  .style-form {
    max-width: 600px;
    margin: 0 auto;
  }
  
  .menu-items-container {
    margin-top: 20px;
    
    .menu-items-list {
      margin-top: 16px;
    }
    
    .menu-item-card {
      border: 1px solid #ebeef5;
      border-radius: 4px;
      margin-bottom: 10px;
      overflow: hidden;
      
      .menu-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background-color: #f5f7fa;
        
        .drag-handle {
          cursor: move;
          margin-right: 10px;
          color: #909399;
        }
      }
      
      .menu-item-content {
        padding: 12px;
        display: flex;
        
        .item-preview {
          width: 80px;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-right: 20px;
          
          .item-icon {
            font-size: 24px;
            margin-bottom: 4px;
          }
          
          .item-text {
            font-size: 12px;
          }
        }
        
        .item-info {
          flex: 1;
        }
      }
    }
    
    .menu-actions {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
  
  .icon-option {
    display: flex;
    align-items: center;
    
    svg {
      margin-right: 8px;
      font-size: 16px;
    }
  }
  
  .ghost {
    opacity: 0.5;
    background: #c8ebfb;
  }
}
</style> 