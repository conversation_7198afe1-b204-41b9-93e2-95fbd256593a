<template>
  <div class="layout-manager">
    <el-form ref="layoutFormRef" :model="layoutForm" :rules="rules" label-width="120px">
      <el-divider content-position="left">基础设置</el-divider>
      
      <el-form-item label="页面背景色" prop="backgroundColor">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-color-picker v-model="layoutForm.backgroundColor" show-alpha @change="updatePreview" />
          </el-col>
          <el-col :span="18">
            <el-input v-model="layoutForm.backgroundColor" placeholder="请输入颜色值" @change="updatePreview" />
          </el-col>
        </el-row>
      </el-form-item>
      
      <el-form-item label="显示搜索框" prop="showSearch">
        <el-switch v-model="layoutForm.showSearch" @change="updatePreview" />
      </el-form-item>
      
      <el-form-item label="搜索框样式" prop="searchStyle" v-if="layoutForm.showSearch">
        <el-radio-group v-model="layoutForm.searchStyle" @change="updatePreview">
          <el-radio-button label="round">圆角</el-radio-button>
          <el-radio-button label="square">方形</el-radio-button>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="搜索框背景" prop="searchBackground" v-if="layoutForm.showSearch">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-color-picker v-model="layoutForm.searchBackground" show-alpha @change="updatePreview" />
          </el-col>
          <el-col :span="18">
            <el-input v-model="layoutForm.searchBackground" placeholder="请输入颜色值" @change="updatePreview" />
          </el-col>
        </el-row>
      </el-form-item>
      
      <el-divider content-position="left">分区设置</el-divider>
      
      <el-form-item label="轮播图高度" prop="bannerHeight">
        <el-input-number v-model="layoutForm.bannerHeight" :min="120" :max="250" :step="10" @change="updatePreview" />
        <span class="form-tip">像素</span>
      </el-form-item>
      
      <el-form-item label="导航样式" prop="navStyle">
        <el-radio-group v-model="layoutForm.navStyle" @change="updatePreview">
          <el-radio-button label="grid">网格</el-radio-button>
          <el-radio-button label="scroll">横向滚动</el-radio-button>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="导航列数" prop="navColumns" v-if="layoutForm.navStyle === 'grid'">
        <el-radio-group v-model="layoutForm.navColumns" @change="updatePreview">
          <el-radio-button :label="4">4列</el-radio-button>
          <el-radio-button :label="5">5列</el-radio-button>
        </el-radio-group>
      </el-form-item>
      
      <el-divider content-position="left">商品展示设置</el-divider>
      
      <el-form-item label="商品展示样式" prop="goodsStyle">
        <el-radio-group v-model="layoutForm.goodsStyle" @change="updatePreview">
          <el-radio-button label="list">列表</el-radio-button>
          <el-radio-button label="grid">网格</el-radio-button>
          <el-radio-button label="waterfall">瀑布流</el-radio-button>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="商品列数" prop="goodsColumns" v-if="layoutForm.goodsStyle !== 'list'">
        <el-radio-group v-model="layoutForm.goodsColumns" @change="updatePreview">
          <el-radio-button :label="2">2列</el-radio-button>
          <el-radio-button :label="3">3列</el-radio-button>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="显示价格" prop="showPrice">
        <el-switch v-model="layoutForm.showPrice" @change="updatePreview" />
      </el-form-item>
      
      <el-form-item label="显示销量" prop="showSales">
        <el-switch v-model="layoutForm.showSales" @change="updatePreview" />
      </el-form-item>
      
      <el-form-item label="显示购买按钮" prop="showBuyButton">
        <el-switch v-model="layoutForm.showBuyButton" @change="updatePreview" />
      </el-form-item>
    </el-form>
    
    <div class="preview-container">
      <div class="preview-title">效果预览</div>
      <div class="preview-phone">
        <div class="phone-header">
          <div class="status-bar"></div>
          <div class="title-bar" v-if="!layoutForm.showSearch">
            驿站帮Pro
          </div>
          <div class="search-bar" v-else :class="layoutForm.searchStyle" :style="{ backgroundColor: layoutForm.searchBackground }">
            <el-icon><Search /></el-icon>
            <span class="search-placeholder">搜索驿站、商品</span>
          </div>
        </div>
        <div class="phone-content" :style="{ backgroundColor: layoutForm.backgroundColor }">
          <!-- 轮播图区域 -->
          <div class="banner-section" :style="{ height: layoutForm.bannerHeight + 'px' }">
            <div class="banner-placeholder">轮播图区域</div>
          </div>
          
          <!-- 导航区域 -->
          <div class="nav-section" :class="layoutForm.navStyle">
            <div v-for="i in layoutForm.navStyle === 'grid' ? layoutForm.navColumns * 2 : 8" :key="i" class="nav-item">
              <div class="nav-icon"></div>
              <div class="nav-text">导航{{ i }}</div>
            </div>
          </div>
          
          <!-- 商品区域 -->
          <div class="goods-section">
            <div class="section-title">
              <span>热门商品</span>
              <span class="more">更多</span>
            </div>
            <div class="goods-list" :class="layoutForm.goodsStyle">
              <div v-for="i in layoutForm.goodsStyle === 'list' ? 3 : layoutForm.goodsColumns * 2" :key="i" 
                class="goods-item" :class="{ 'odd': layoutForm.goodsStyle === 'waterfall' && i % 2 === 1 }">
                <div class="goods-image"></div>
                <div class="goods-info">
                  <div class="goods-title">商品名称示例{{ i }}</div>
                  <div v-if="layoutForm.showPrice" class="goods-price">¥299.00</div>
                  <div class="goods-footer">
                    <div v-if="layoutForm.showSales" class="goods-sales">销量: 128</div>
                    <div v-if="layoutForm.showBuyButton" class="goods-buy">购买</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LayoutManager',
  props: {
    modelValue: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue'],
  data() {
    return {
      layoutForm: {
        backgroundColor: '#f5f5f5',
        showSearch: true,
        searchStyle: 'round',
        searchBackground: '#ffffff',
        bannerHeight: 180,
        navStyle: 'grid',
        navColumns: 5,
        goodsStyle: 'grid',
        goodsColumns: 2,
        showPrice: true,
        showSales: true,
        showBuyButton: true
      },
      rules: {
        backgroundColor: [
          { required: true, message: '请选择页面背景色', trigger: 'change' }
        ],
        searchBackground: [
          { required: true, message: '请选择搜索框背景色', trigger: 'change' }
        ],
        bannerHeight: [
          { required: true, message: '请输入轮播图高度', trigger: 'blur' }
        ],
        navStyle: [
          { required: true, message: '请选择导航样式', trigger: 'change' }
        ],
        goodsStyle: [
          { required: true, message: '请选择商品展示样式', trigger: 'change' }
        ]
      }
    };
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.layoutForm = {...this.layoutForm, ...val};
        }
      },
      immediate: true,
      deep: true
    },
    layoutForm: {
      handler(val) {
        this.$emit('update:modelValue', val);
      },
      deep: true
    }
  },
  methods: {
    // 更新预览
    updatePreview() {
      // 这里可以添加一些额外的预览逻辑
      // 当前已通过响应式数据自动更新了预览效果
    }
  }
};
</script>

<style scoped lang="scss">
.layout-manager {
  display: flex;
  
  .el-form {
    flex: 1;
    max-width: 600px;
    margin-right: 20px;
  }
  
  .form-tip {
    margin-left: 8px;
    color: #909399;
    font-size: 12px;
  }
  
  .preview-container {
    width: 375px;
    position: sticky;
    top: 70px;
    align-self: flex-start;
    
    .preview-title {
      text-align: center;
      margin-bottom: 15px;
      font-weight: bold;
      color: #606266;
    }
    
    .preview-phone {
      width: 100%;
      height: 667px;
      border: 10px solid #333;
      border-radius: 25px;
      overflow: hidden;
      background-color: #fff;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      
      .phone-header {
        .status-bar {
          height: 20px;
          background-color: #000;
        }
        
        .title-bar {
          height: 44px;
          line-height: 44px;
          text-align: center;
          font-size: 16px;
          font-weight: bold;
          background-color: #fff;
          color: #333;
          border-bottom: 1px solid #f0f0f0;
        }
        
        .search-bar {
          margin: 8px 10px;
          height: 36px;
          display: flex;
          align-items: center;
          padding: 0 10px;
          
          &.round {
            border-radius: 18px;
          }
          
          &.square {
            border-radius: 4px;
          }
          
          .el-icon {
            color: #999;
            margin-right: 5px;
          }
          
          .search-placeholder {
            color: #999;
            font-size: 14px;
          }
        }
      }
      
      .phone-content {
        height: calc(100% - 64px);
        overflow-y: auto;
        
        .banner-section {
          width: 100%;
          position: relative;
          
          .banner-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #e6e6e6, #d6d6d6);
            color: #999;
            font-size: 14px;
          }
        }
        
        .nav-section {
          background-color: #fff;
          margin: 10px;
          border-radius: 8px;
          padding: 10px 0;
          
          &.grid {
            display: grid;
            grid-template-columns: repeat(v-bind('layoutForm.navColumns'), 1fr);
            gap: 10px;
            
            .nav-item {
              display: flex;
              flex-direction: column;
              align-items: center;
            }
          }
          
          &.scroll {
            display: flex;
            overflow-x: auto;
            padding: 10px;
            
            .nav-item {
              flex: 0 0 auto;
              display: flex;
              flex-direction: column;
              align-items: center;
              margin-right: 15px;
              width: 60px;
            }
          }
          
          .nav-icon {
            width: 40px;
            height: 40px;
            background-color: #e6e6e6;
            border-radius: 50%;
            margin-bottom: 5px;
          }
          
          .nav-text {
            font-size: 12px;
            color: #333;
            text-align: center;
          }
        }
        
        .goods-section {
          margin: 10px;
          
          .section-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 15px;
            font-weight: bold;
            
            .more {
              color: #999;
              font-size: 12px;
              font-weight: normal;
            }
          }
          
          .goods-list {
            &.list {
              .goods-item {
                display: flex;
                background-color: #fff;
                border-radius: 8px;
                margin-bottom: 10px;
                overflow: hidden;
                
                .goods-image {
                  width: 100px;
                  height: 100px;
                  background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
                  flex-shrink: 0;
                }
                
                .goods-info {
                  flex: 1;
                  padding: 10px;
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                }
              }
            }
            
            &.grid {
              display: grid;
              grid-template-columns: repeat(v-bind('layoutForm.goodsColumns'), 1fr);
              gap: 10px;
              
              .goods-item {
                background-color: #fff;
                border-radius: 8px;
                overflow: hidden;
                
                .goods-image {
                  width: 100%;
                  padding-top: 100%;
                  position: relative;
                  background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
                }
                
                .goods-info {
                  padding: 8px;
                }
              }
            }
            
            &.waterfall {
              display: grid;
              grid-template-columns: repeat(v-bind('layoutForm.goodsColumns'), 1fr);
              gap: 10px;
              
              .goods-item {
                background-color: #fff;
                border-radius: 8px;
                overflow: hidden;
                
                &.odd {
                  margin-top: 40px;
                }
                
                .goods-image {
                  width: 100%;
                  padding-top: 130%;
                  position: relative;
                  background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
                }
                
                .goods-info {
                  padding: 8px;
                }
              }
            }
            
            .goods-title {
              font-size: 14px;
              color: #333;
              margin-bottom: 5px;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
            
            .goods-price {
              color: #ff4d4f;
              font-size: 15px;
              font-weight: bold;
              margin-bottom: 5px;
            }
            
            .goods-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              
              .goods-sales {
                color: #999;
                font-size: 12px;
              }
              
              .goods-buy {
                background-color: #ff4d4f;
                color: #fff;
                padding: 2px 8px;
                border-radius: 10px;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .layout-manager {
    flex-direction: column;
    
    .el-form {
      max-width: 100%;
      margin-right: 0;
      margin-bottom: 20px;
    }
    
    .preview-container {
      width: 100%;
      position: static;
      
      .preview-phone {
        max-width: 375px;
        margin: 0 auto;
      }
    }
  }
}
</style> 