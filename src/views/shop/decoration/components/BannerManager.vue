<template>
  <div class="banner-manager">
    <div class="action-toolbar">
      <el-button type="primary" @click="handleAddBanner" v-hasPermi="['shop:banner:add']">
        <el-icon><Plus /></el-icon> 添加轮播图
      </el-button>
      <el-button type="success" @click="handleSyncToMiniProgram" v-hasPermi="['shop:banner:sync']">
        <el-icon><Upload /></el-icon> 同步到小程序
      </el-button>
    </div>

    <el-card class="banner-previewer" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>轮播图效果预览</span>
          <div>
            <el-switch
              v-model="autoPlay"
              active-text="自动播放"
              inactive-text="手动播放"
              @change="handleAutoPlayChange"
            />
          </div>
        </div>
      </template>
      <div v-if="bannerList.length === 0" class="empty-banner">
        <el-empty description="暂无轮播图，请添加" />
      </div>
      <el-carousel v-else height="200px" :autoplay="autoPlay" :interval="3000" indicator-position="outside">
        <el-carousel-item v-for="(banner, index) in bannerList" :key="banner.id || index">
          <div class="banner-item">
            <img :src="banner.imageUrl" alt="轮播图" class="banner-image" />
            <div class="banner-info" v-if="banner.title">
              <h4>{{ banner.title }}</h4>
              <p v-if="banner.subTitle">{{ banner.subTitle }}</p>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </el-card>

    <el-card class="banner-list" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>轮播图列表</span>
          <div>
            <el-radio-group v-model="listType" size="small">
              <el-radio-button label="grid">
                <el-icon><Grid /></el-icon>
              </el-radio-button>
              <el-radio-button label="list">
                <el-icon><List /></el-icon>
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>
      
      <div v-else-if="bannerList.length === 0" class="empty-container">
        <el-empty description="暂无轮播图数据" />
      </div>
      
      <div v-else :class="['banner-container', listType]">
        <draggable 
          v-model="bannerList" 
          handle=".drag-handle"
          item-key="id"
          :animation="200"
          ghost-class="ghost"
          @end="handleDragSort"
        >
          <template #item="{ element, index }">
            <div class="banner-item-card">
              <div class="banner-item-header">
                <span class="drag-handle"><el-icon><Rank /></el-icon></span>
                <span>{{ element.title || `轮播图 ${index + 1}` }}</span>
                <div class="banner-actions">
                  <el-switch
                    v-model="element.status" 
                    :active-value="1"
                    :inactive-value="0"
                    @change="(val) => handleStatusChange(element, val)"
                  />
                  <el-tooltip content="编辑轮播图" placement="top">
                    <el-button link type="primary" @click="handleEdit(element, index)">
                      <el-icon><Edit /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="删除轮播图" placement="top">
                    <el-button link type="danger" @click="handleDelete(element, index)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </el-tooltip>
                </div>
              </div>
              <div class="banner-item-content">
                <div class="banner-image-container">
                  <img :src="element.imageUrl" alt="轮播图" @click="previewImage(element.imageUrl)" />
                </div>
                <div v-if="listType === 'list'" class="banner-info-container">
                  <div class="banner-info-item">
                    <label>标题：</label>
                    <span>{{ element.title || '无标题' }}</span>
                  </div>
                  <div class="banner-info-item">
                    <label>链接类型：</label>
                    <span>{{ getLinkTypeName(element.linkType) }}</span>
                  </div>
                  <div class="banner-info-item">
                    <label>链接地址：</label>
                    <span>{{ element.linkUrl || '无链接' }}</span>
                  </div>
                  <div class="banner-info-item">
                    <label>排序：</label>
                    <span>{{ element.sort }}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </el-card>

    <!-- 添加/编辑轮播图对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加轮播图' : '编辑轮播图'"
      width="600px"
      append-to-body
    >
      <el-form ref="bannerFormRef" :model="bannerForm" :rules="rules" label-width="90px">
        <el-form-item label="图片" prop="imageUrl">
          <el-upload
            class="banner-uploader"
            :action="uploadUrl"
            :headers="headers"
            :show-file-list="false"
            :on-success="handleUploadSuccess"
            :before-upload="beforeImageUpload"
          >
            <img v-if="bannerForm.imageUrl" :src="bannerForm.imageUrl" class="uploaded-image" />
            <el-icon v-else class="uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="image-tips">建议尺寸：750×340像素，JPG或PNG格式</div>
        </el-form-item>
        
        <el-form-item label="标题" prop="title">
          <el-input v-model="bannerForm.title" placeholder="请输入轮播图标题" />
        </el-form-item>
        
        <el-form-item label="副标题" prop="subTitle">
          <el-input v-model="bannerForm.subTitle" placeholder="请输入轮播图副标题" />
        </el-form-item>
        
        <el-form-item label="链接类型" prop="linkType">
          <el-select v-model="bannerForm.linkType" placeholder="请选择链接类型" style="width: 100%">
            <el-option
              v-for="(name, type) in linkTypeOptions"
              :key="type"
              :label="name"
              :value="type"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="链接地址" prop="linkUrl" v-if="bannerForm.linkType !== 'NONE'">
          <el-input v-model="bannerForm.linkUrl" placeholder="请输入链接地址" />
        </el-form-item>
        
        <el-form-item label="显示时间" prop="timeRange" v-if="showTimeRange">
          <el-date-picker
            v-model="bannerForm.timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="bannerForm.sort" :min="0" :max="999" controls-position="right" style="width: 100%" />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="bannerForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitBannerForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="650px" append-to-body center>
      <div class="preview-image-container">
        <img :src="previewImageUrl" alt="预览图" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
import { saveBannerConfig, getBannerConfig } from '@/api/shop/decoration';
import { getToken } from '@/utils/auth';

export default {
  name: 'BannerManager',
  components: {
    draggable
  },
  props: {
    modelValue: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue'],
  data() {
    return {
      // 轮播图列表
      bannerList: [],
      // 加载状态
      loading: false,
      // 自动播放
      autoPlay: true,
      // 列表展示类型
      listType: 'grid',
      // 对话框显示状态
      dialogVisible: false,
      // 对话框类型（add/edit）
      dialogType: 'add',
      // 当前编辑的轮播图索引
      currentIndex: -1,
      // 表单对象
      bannerForm: {
        id: undefined,
        imageUrl: '',
        title: '',
        subTitle: '',
        linkType: 'NONE',
        linkUrl: '',
        timeRange: [],
        startTime: undefined,
        endTime: undefined,
        sort: 0,
        status: 1
      },
      // 表单校验规则
      rules: {
        imageUrl: [
          { required: true, message: '请上传轮播图片', trigger: 'change' }
        ],
        title: [
          { max: 30, message: '标题长度不能超过30个字符', trigger: 'blur' }
        ],
        subTitle: [
          { max: 50, message: '副标题长度不能超过50个字符', trigger: 'blur' }
        ],
        linkType: [
          { required: true, message: '请选择链接类型', trigger: 'change' }
        ],
        linkUrl: [
          { required: true, message: '请输入链接地址', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入排序号', trigger: 'blur' }
        ]
      },
      // 链接类型选项
      linkTypeOptions: {
        'NONE': '无链接',
        'URL': '外部链接',
        'STATION': '驿站详情',
        'PRODUCT': '商品详情',
        'CATEGORY': '商品分类',
        'PAGE': '小程序页面'
      },
      // 图片预览
      previewVisible: false,
      previewImageUrl: '',
      // 是否显示时间范围
      showTimeRange: false,
      // 上传相关
      uploadUrl: process.env.VUE_APP_BASE_API + '/shop/upload',
      headers: {
        Authorization: 'Bearer ' + getToken()
      }
    };
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val && val.length > 0 && JSON.stringify(val) !== JSON.stringify(this.bannerList)) {
          this.bannerList = JSON.parse(JSON.stringify(val));
        }
      },
      immediate: true
    },
    bannerList: {
      handler(val) {
        this.$emit('update:modelValue', val);
      },
      deep: true
    },
    'bannerForm.linkType': {
      handler(val) {
        // 根据链接类型决定是否需要显示链接地址
        if (val === 'NONE') {
          this.bannerForm.linkUrl = '';
        }
      }
    }
  },
  created() {
    this.loadBannerList();
  },
  methods: {
    // 加载轮播图列表
    async loadBannerList() {
      // 如果props中已有数据，则直接使用
      if (this.modelValue && this.modelValue.length > 0) {
        this.bannerList = JSON.parse(JSON.stringify(this.modelValue));
        return;
      }
      
      this.loading = true;
      try {
        const res = await getBannerConfig();
        if (res.data) {
          this.bannerList = res.data;
        }
      } catch (error) {
        console.error('获取轮播图列表失败', error);
        this.$message.error('获取轮播图列表失败');
      } finally {
        this.loading = false;
      }
    },
    
    // 处理自动播放变更
    handleAutoPlayChange(val) {
      this.autoPlay = val;
    },
    
    // 处理添加轮播图
    handleAddBanner() {
      this.resetForm();
      this.dialogType = 'add';
      this.dialogVisible = true;
      this.currentIndex = -1;
    },
    
    // 处理编辑轮播图
    handleEdit(item, index) {
      this.resetForm();
      this.dialogType = 'edit';
      this.currentIndex = index;
      
      // 深拷贝，避免直接修改列表数据
      this.bannerForm = JSON.parse(JSON.stringify(item));
      
      // 如果有开始时间和结束时间，则设置时间范围
      if (this.bannerForm.startTime && this.bannerForm.endTime) {
        this.bannerForm.timeRange = [this.bannerForm.startTime, this.bannerForm.endTime];
        this.showTimeRange = true;
      }
      
      this.dialogVisible = true;
    },
    
    // 处理删除轮播图
    handleDelete(item, index) {
      this.$confirm(`确定要删除这张轮播图吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.bannerList.splice(index, 1);
        this.$message.success('删除成功');
      }).catch(() => {});
    },
    
    // 处理状态变更
    handleStatusChange(item, status) {
      item.status = status;
    },
    
    // 处理同步到小程序
    handleSyncToMiniProgram() {
      this.$confirm('确定要将当前轮播图配置同步到小程序吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        saveBannerConfig(this.bannerList).then(() => {
          this.$message.success('同步成功');
        }).catch(error => {
          console.error('同步失败', error);
          this.$message.error('同步失败');
        });
      }).catch(() => {});
    },
    
    // 处理拖拽排序
    handleDragSort() {
      // 更新排序字段
      this.bannerList.forEach((item, index) => {
        item.sort = index;
      });
    },
    
    // 图片上传前的校验
    beforeImageUpload(file) {
      const isJPG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;
      
      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是 JPG 或 PNG 格式!');
        return false;
      }
      
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
        return false;
      }
      
      return true;
    },
    
    // 图片上传成功的回调
    handleUploadSuccess(res, file) {
      if (res.code === 0 && res.url) {
        this.bannerForm.imageUrl = res.url;
      } else {
        this.$message.error('图片上传失败');
      }
    },
    
    // 预览图片
    previewImage(url) {
      this.previewImageUrl = url;
      this.previewVisible = true;
    },
    
    // 获取链接类型名称
    getLinkTypeName(type) {
      return this.linkTypeOptions[type] || '无链接';
    },
    
    // 提交表单
    submitBannerForm() {
      this.$refs.bannerFormRef.validate(valid => {
        if (valid) {
          // 处理时间范围
          if (this.showTimeRange && this.bannerForm.timeRange && this.bannerForm.timeRange.length === 2) {
            this.bannerForm.startTime = this.bannerForm.timeRange[0];
            this.bannerForm.endTime = this.bannerForm.timeRange[1];
          } else {
            this.bannerForm.startTime = undefined;
            this.bannerForm.endTime = undefined;
          }
          
          // 移除timeRange字段，它不是后端需要的
          const submitData = { ...this.bannerForm };
          delete submitData.timeRange;
          
          if (this.dialogType === 'add') {
            // 添加到列表最前面
            submitData.id = Date.now(); // 临时ID
            submitData.sort = this.bannerList.length;
            this.bannerList.push(submitData);
          } else {
            // 更新列表中的项
            this.bannerList.splice(this.currentIndex, 1, submitData);
          }
          
          this.dialogVisible = false;
          this.$message.success(this.dialogType === 'add' ? '添加成功' : '编辑成功');
        }
      });
    },
    
    // 重置表单
    resetForm() {
      if (this.$refs.bannerFormRef) {
        this.$refs.bannerFormRef.resetFields();
      }
      
      this.bannerForm = {
        id: undefined,
        imageUrl: '',
        title: '',
        subTitle: '',
        linkType: 'NONE',
        linkUrl: '',
        timeRange: [],
        startTime: undefined,
        endTime: undefined,
        sort: this.bannerList.length,
        status: 1
      };
      
      this.showTimeRange = false;
    }
  }
};
</script>

<style scoped lang="scss">
.banner-manager {
  .action-toolbar {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .banner-previewer {
    margin-bottom: 20px;
    
    .empty-banner {
      height: 200px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .banner-item {
      position: relative;
      width: 100%;
      height: 100%;
      
      .banner-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .banner-info {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 10px 15px;
        background-color: rgba(0, 0, 0, 0.5);
        color: #fff;
        
        h4 {
          margin: 0 0 5px;
          font-size: 16px;
        }
        
        p {
          margin: 0;
          font-size: 12px;
          opacity: 0.8;
        }
      }
    }
  }
  
  .banner-list {
    .loading-container,
    .empty-container {
      padding: 20px;
    }
    
    .banner-container {
      &.grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 16px;
      }
      
      &.list {
        .banner-item-card {
          margin-bottom: 12px;
          
          .banner-item-content {
            display: flex;
            
            .banner-image-container {
              width: 200px;
              height: 100px;
              flex-shrink: 0;
              margin-right: 16px;
            }
            
            .banner-info-container {
              flex: 1;
              
              .banner-info-item {
                margin-bottom: 8px;
                
                label {
                  color: #909399;
                  margin-right: 5px;
                }
              }
            }
          }
        }
      }
    }
    
    .banner-item-card {
      border: 1px solid #ebeef5;
      border-radius: 4px;
      overflow: hidden;
      transition: all 0.3s;
      
      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
      
      .banner-item-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        background-color: #f5f7fa;
        
        .drag-handle {
          cursor: move;
          margin-right: 8px;
          color: #909399;
        }
        
        .banner-actions {
          display: flex;
          align-items: center;
          
          .el-button {
            margin-left: 8px;
          }
        }
      }
      
      .banner-item-content {
        padding: 12px;
        
        .banner-image-container {
          width: 100%;
          height: 120px;
          overflow: hidden;
          border-radius: 4px;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            cursor: pointer;
            transition: transform 0.3s;
            
            &:hover {
              transform: scale(1.05);
            }
          }
        }
      }
    }
  }
  
  .banner-uploader {
    display: flex;
    justify-content: center;
    
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409eff;
      }
      
      .uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 300px;
        height: 150px;
        line-height: 150px;
        text-align: center;
      }
      
      .uploaded-image {
        width: 300px;
        height: 150px;
        display: block;
        object-fit: cover;
      }
    }
  }
  
  .image-tips {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    text-align: center;
  }
  
  .preview-image-container {
    display: flex;
    justify-content: center;
    
    .preview-image {
      max-width: 100%;
      max-height: 500px;
    }
  }
  
  .ghost {
    opacity: 0.5;
    background: #c8ebfb;
  }
}
</style> 