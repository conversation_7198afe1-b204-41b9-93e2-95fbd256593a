<template>
  <div class="nav-manager">
    <div class="action-toolbar">
      <el-button type="primary" @click="handleAddNav" v-hasPermi="['shop:nav:add']">
        <el-icon><Plus /></el-icon> 添加导航
      </el-button>
    </div>
    
    <el-empty v-if="navList.length === 0" description="暂无导航数据，请添加" />
    
    <el-row v-else :gutter="16" class="nav-grid">
      <draggable 
        v-model="navList" 
        :animation="200"
        item-key="id"
        ghost-class="ghost"
        class="nav-draggable"
        handle=".drag-handle"
        @end="handleDragEnd"
      >
        <template #item="{ element, index }">
          <el-col :span="6" :xs="12" :sm="8" :md="6" :lg="4" class="nav-col">
            <el-card shadow="hover" class="nav-item">
              <div class="nav-item-header">
                <span class="drag-handle"><el-icon><Rank /></el-icon></span>
                <el-dropdown trigger="click" @command="(cmd) => handleCommand(cmd, element, index)">
                  <span class="nav-actions">
                    <el-icon><More /></el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="edit">编辑</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
              
              <div class="nav-item-content">
                <div class="nav-icon">
                  <el-image :src="element.iconUrl" fit="contain">
                    <template #error>
                      <div class="image-slot">
                        <el-icon><PictureFilled /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
                <div class="nav-title">{{ element.title }}</div>
              </div>
              
              <div class="nav-item-footer">
                <el-tag size="small" :type="element.status ? 'success' : 'info'">
                  {{ element.status ? '已启用' : '已禁用' }}
                </el-tag>
                <el-switch
                  v-model="element.status"
                  :active-value="1"
                  :inactive-value="0"
                  @change="() => handleStatusChange(element)"
                />
              </div>
            </el-card>
          </el-col>
        </template>
      </draggable>
    </el-row>
    
    <!-- 添加/编辑导航对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加导航' : '编辑导航'"
      width="550px"
      append-to-body
    >
      <el-form ref="navFormRef" :model="navForm" :rules="rules" label-width="100px">
        <el-form-item label="导航标题" prop="title">
          <el-input v-model="navForm.title" placeholder="请输入导航标题" maxlength="8" show-word-limit />
        </el-form-item>
        
        <el-form-item label="导航图标" prop="iconUrl">
          <el-upload
            class="nav-icon-uploader"
            :action="uploadUrl"
            :headers="headers"
            :show-file-list="false"
            :on-success="handleUploadSuccess"
            :before-upload="beforeIconUpload"
          >
            <img v-if="navForm.iconUrl" :src="navForm.iconUrl" class="uploaded-icon" />
            <el-icon v-else class="uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="icon-tips">建议尺寸：60×60像素，PNG格式（支持透明背景）</div>
        </el-form-item>
        
        <el-form-item label="链接类型" prop="linkType">
          <el-select v-model="navForm.linkType" placeholder="请选择链接类型" style="width: 100%">
            <el-option
              v-for="(name, type) in linkTypeOptions"
              :key="type"
              :label="name"
              :value="type"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="链接地址" prop="linkUrl" v-if="navForm.linkType !== 'NONE'">
          <el-input v-model="navForm.linkUrl" placeholder="请输入链接地址" />
        </el-form-item>
        
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="navForm.sort" :min="0" controls-position="right" style="width: 100%" />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="navForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitNavForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
import { getToken } from '@/utils/auth';

export default {
  name: 'NavManager',
  components: {
    draggable
  },
  props: {
    modelValue: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue'],
  data() {
    return {
      navList: [],
      dialogVisible: false,
      dialogType: 'add', // add 或 edit
      currentIndex: -1,
      navForm: {
        id: undefined,
        title: '',
        iconUrl: '',
        linkType: 'PAGE',
        linkUrl: '',
        sort: 0,
        status: 1
      },
      rules: {
        title: [
          { required: true, message: '请输入导航标题', trigger: 'blur' },
          { max: 8, message: '导航标题不能超过8个字符', trigger: 'blur' }
        ],
        iconUrl: [
          { required: true, message: '请上传导航图标', trigger: 'change' }
        ],
        linkType: [
          { required: true, message: '请选择链接类型', trigger: 'change' }
        ],
        linkUrl: [
          { required: true, message: '请输入链接地址', trigger: 'blur' }
        ]
      },
      // 链接类型选项
      linkTypeOptions: {
        'NONE': '无链接',
        'URL': '外部链接',
        'STATION': '驿站详情',
        'PRODUCT': '商品详情',
        'CATEGORY': '商品分类',
        'PAGE': '小程序页面'
      },
      // 上传相关
      uploadUrl: process.env.VUE_APP_BASE_API + '/shop/upload',
      headers: {
        Authorization: 'Bearer ' + getToken()
      }
    };
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val && val.length > 0 && JSON.stringify(val) !== JSON.stringify(this.navList)) {
          this.navList = JSON.parse(JSON.stringify(val));
        }
      },
      immediate: true
    },
    navList: {
      handler(val) {
        this.$emit('update:modelValue', val);
      },
      deep: true
    },
    'navForm.linkType': {
      handler(val) {
        // 根据链接类型决定是否需要显示链接地址
        if (val === 'NONE') {
          this.navForm.linkUrl = '';
        }
      }
    }
  },
  methods: {
    // 处理添加导航
    handleAddNav() {
      this.resetForm();
      this.dialogType = 'add';
      this.dialogVisible = true;
      this.currentIndex = -1;
    },
    
    // 处理图标上传前的校验
    beforeIconUpload(file) {
      const isPNG = file.type === 'image/png';
      const isLt1M = file.size / 1024 / 1024 < 1;
      
      if (!isPNG) {
        this.$message.error('上传图标只能是 PNG 格式!');
        return false;
      }
      
      if (!isLt1M) {
        this.$message.error('上传图标大小不能超过 1MB!');
        return false;
      }
      
      return true;
    },
    
    // 图标上传成功的回调
    handleUploadSuccess(res, file) {
      if (res.code === 0 && res.url) {
        this.navForm.iconUrl = res.url;
      } else {
        this.$message.error('图标上传失败');
      }
    },
    
    // 处理拖拽结束
    handleDragEnd() {
      // 更新排序
      this.navList.forEach((item, index) => {
        item.sort = index;
      });
    },
    
    // 处理下拉菜单命令
    handleCommand(command, item, index) {
      if (command === 'edit') {
        this.handleEdit(item, index);
      } else if (command === 'delete') {
        this.handleDelete(item, index);
      }
    },
    
    // 处理编辑导航
    handleEdit(item, index) {
      this.resetForm();
      this.dialogType = 'edit';
      this.currentIndex = index;
      
      // 深拷贝，避免直接修改列表数据
      this.navForm = JSON.parse(JSON.stringify(item));
      
      this.dialogVisible = true;
    },
    
    // 处理删除导航
    handleDelete(item, index) {
      this.$confirm('确定要删除该导航吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.navList.splice(index, 1);
        this.$message.success('删除成功');
      }).catch(() => {});
    },
    
    // 处理状态变更
    handleStatusChange(item) {
      // 状态已经在v-model中自动更新，这里可以添加额外的逻辑
      const statusText = item.status ? '启用' : '禁用';
      this.$message.success(`已${statusText}`);
    },
    
    // 提交表单
    submitNavForm() {
      this.$refs.navFormRef.validate(valid => {
        if (valid) {
          const submitData = JSON.parse(JSON.stringify(this.navForm));
          
          if (this.dialogType === 'add') {
            // 添加新导航
            submitData.id = Date.now(); // 临时ID
            submitData.sort = this.navList.length;
            this.navList.push(submitData);
          } else {
            // 更新导航
            this.navList.splice(this.currentIndex, 1, submitData);
          }
          
          this.dialogVisible = false;
          this.$message.success(this.dialogType === 'add' ? '添加成功' : '编辑成功');
        }
      });
    },
    
    // 重置表单
    resetForm() {
      if (this.$refs.navFormRef) {
        this.$refs.navFormRef.resetFields();
      }
      
      this.navForm = {
        id: undefined,
        title: '',
        iconUrl: '',
        linkType: 'PAGE',
        linkUrl: '',
        sort: this.navList.length,
        status: 1
      };
    }
  }
};
</script>

<style scoped lang="scss">
.nav-manager {
  .action-toolbar {
    margin-bottom: 20px;
  }
  
  .nav-grid {
    margin-top: 20px;
  }
  
  .nav-draggable {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }
  
  .nav-col {
    margin-bottom: 20px;
  }
  
  .nav-item {
    position: relative;
    
    .nav-item-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      
      .drag-handle {
        cursor: move;
        color: #909399;
        
        &:hover {
          color: #409EFF;
        }
      }
      
      .nav-actions {
        cursor: pointer;
        color: #909399;
        
        &:hover {
          color: #409EFF;
        }
      }
    }
    
    .nav-item-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px 0;
      
      .nav-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 10px;
        
        .el-image {
          width: 100%;
          height: 100%;
          
          .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            color: #909399;
            font-size: 30px;
          }
        }
      }
      
      .nav-title {
        font-size: 14px;
        color: #303133;
        text-align: center;
        margin-top: 5px;
      }
    }
    
    .nav-item-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px solid #ebeef5;
    }
  }
  
  .nav-icon-uploader {
    text-align: center;
    
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409eff;
      }
      
      .uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 100px;
        height: 100px;
        line-height: 100px;
        text-align: center;
      }
      
      .uploaded-icon {
        width: 100px;
        height: 100px;
      }
    }
  }
  
  .icon-tips {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    text-align: center;
  }
  
  .ghost {
    opacity: 0.5;
    background: #c8ebfb;
  }
}
</style> 