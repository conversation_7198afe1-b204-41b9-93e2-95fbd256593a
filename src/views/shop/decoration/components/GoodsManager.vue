<template>
  <div class="goods-manager">
    <div class="action-toolbar">
      <el-button type="primary" @click="handleSelectGoods" v-hasPermi="['shop:goods:select']">
        <el-icon><Select /></el-icon> 选择商品
      </el-button>
      <el-dropdown trigger="click" @command="handleShowType" style="margin-left: 15px;">
        <el-button type="info" plain>
          显示样式<el-icon class="el-icon--right"><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="recommend">推荐商品</el-dropdown-item>
            <el-dropdown-item command="hot">热销商品</el-dropdown-item>
            <el-dropdown-item command="new">新品上架</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    
    <el-empty v-if="goodsList.length === 0" description="暂无选择商品，请添加" />
    
    <el-row v-else :gutter="16" class="goods-grid">
      <draggable 
        v-model="goodsList" 
        :animation="200"
        item-key="id"
        ghost-class="ghost"
        class="goods-draggable"
        handle=".drag-handle"
        @end="handleDragSort"
      >
        <template #item="{ element, index }">
          <el-col :span="8" :xs="24" :sm="12" :md="8" :lg="6" class="goods-col">
            <el-card shadow="hover" class="goods-item">
              <div class="goods-item-header">
                <span class="drag-handle"><el-icon><Rank /></el-icon></span>
                <el-dropdown trigger="click" @command="(cmd) => handleCommand(cmd, element, index)">
                  <span class="goods-actions">
                    <el-icon><More /></el-icon>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="delete">移除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
              
              <div class="goods-item-content">
                <div class="goods-image">
                  <el-image :src="element.imageUrl" fit="cover">
                    <template #error>
                      <div class="image-slot">
                        <el-icon><PictureFilled /></el-icon>
                      </div>
                    </template>
                  </el-image>
                </div>
                <div class="goods-title">{{ element.title }}</div>
                <div class="goods-price">¥{{ element.price.toFixed(2) }}</div>
                <div class="goods-footer">
                  <span class="goods-sales">销量: {{ element.sales }}</span>
                  <span class="goods-stock" v-if="element.stock < 10">库存: {{ element.stock }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </template>
      </draggable>
    </el-row>
    
    <!-- 选择商品对话框 -->
    <el-dialog
      v-model="selectDialogVisible"
      title="选择商品"
      width="800px"
      append-to-body
    >
      <div class="search-bar">
        <el-form :inline="true" :model="queryParams" @submit.prevent>
          <el-form-item>
            <el-input
              v-model="queryParams.keyword"
              placeholder="商品名称/编号"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-select v-model="queryParams.categoryId" placeholder="商品分类" clearable>
              <el-option
                v-for="item in categoryOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              <el-icon><Search /></el-icon> 查询
            </el-button>
            <el-button @click="resetQuery">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <el-table
        v-loading="loading"
        :data="goodsOptions"
        @selection-change="handleSelectionChange"
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="商品图片" width="80" align="center">
          <template #default="scope">
            <el-image
              :src="scope.row.imageUrl"
              style="width: 50px; height: 50px"
              :preview-src-list="[scope.row.imageUrl]"
            />
          </template>
        </el-table-column>
        <el-table-column prop="title" label="商品名称" show-overflow-tooltip />
        <el-table-column prop="price" label="价格" width="80" align="center">
          <template #default="scope">
            {{ scope.row.price.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="sales" label="销量" width="80" align="center" />
        <el-table-column prop="stock" label="库存" width="80" align="center" />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status ? 'success' : 'info'">
              {{ scope.row.status ? '上架' : '下架' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="dialog-pagination">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="selectDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmSelectGoods">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
import { listGoods } from '@/api/shop/goods';
import { listCategory } from '@/api/shop/category';

export default {
  name: 'GoodsManager',
  components: {
    draggable
  },
  props: {
    modelValue: {
      type: Array,
      default: () => []
    },
    maxItems: {
      type: Number,
      default: 10
    },
    showType: {
      type: String,
      default: 'recommend'
    }
  },
  emits: ['update:modelValue', 'update:showType'],
  data() {
    return {
      // 商品列表
      goodsList: [],
      // 选择对话框可见性
      selectDialogVisible: false,
      // 加载状态
      loading: false,
      // 已选择的商品
      selectedGoods: [],
      // 商品选项
      goodsOptions: [],
      // 分类选项
      categoryOptions: [],
      // 总数
      total: 0,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        categoryId: undefined,
        status: 1 // 默认只查询上架商品
      },
      // 当前商品展示类型
      currentShowType: 'recommend'
    };
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val && val.length > 0 && JSON.stringify(val) !== JSON.stringify(this.goodsList)) {
          this.goodsList = JSON.parse(JSON.stringify(val));
        }
      },
      immediate: true,
      deep: true
    },
    goodsList: {
      handler(val) {
        this.$emit('update:modelValue', val);
      },
      deep: true
    },
    showType: {
      handler(val) {
        this.currentShowType = val;
      },
      immediate: true
    }
  },
  created() {
    // 获取商品分类
    this.getCategoryOptions();
  },
  methods: {
    // 获取商品分类选项
    getCategoryOptions() {
      listCategory().then(response => {
        this.categoryOptions = response.data || [];
      });
    },

    // 处理显示类型切换
    handleShowType(type) {
      this.currentShowType = type;
      this.$emit('update:showType', type);
    },

    // 处理选择商品
    handleSelectGoods() {
      this.resetQuery();
      this.getGoodsList();
      this.selectDialogVisible = true;
      this.selectedGoods = [];
    },

    // 获取商品列表
    getGoodsList() {
      this.loading = true;
      listGoods(this.queryParams).then(response => {
        this.goodsOptions = response.data.list || [];
        this.total = response.data.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },

    // 处理查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getGoodsList();
    },

    // 重置查询
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        categoryId: undefined,
        status: 1
      };
    },

    // 处理页码变化
    handleCurrentChange(page) {
      this.queryParams.pageNum = page;
      this.getGoodsList();
    },

    // 处理每页条数变化
    handleSizeChange(size) {
      this.queryParams.pageSize = size;
      this.getGoodsList();
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedGoods = selection;
    },

    // 确认选择商品
    confirmSelectGoods() {
      if (this.selectedGoods.length === 0) {
        this.$message.warning('请至少选择一个商品');
        return;
      }

      // 检查是否超过最大限制
      if (this.goodsList.length + this.selectedGoods.length > this.maxItems) {
        this.$message.warning(`最多只能选择${this.maxItems}个商品`);
        return;
      }

      // 合并已有商品和新选择的商品，避免重复
      const existingIds = this.goodsList.map(item => item.id);
      const newGoods = this.selectedGoods.filter(item => !existingIds.includes(item.id));

      if (newGoods.length > 0) {
        // 为新商品设置排序
        newGoods.forEach((item, index) => {
          item.sort = this.goodsList.length + index;
        });

        this.goodsList = [...this.goodsList, ...newGoods];
        this.$message.success(`成功添加${newGoods.length}个商品`);
      } else {
        this.$message.info('所选商品已存在，未添加新商品');
      }

      this.selectDialogVisible = false;
    },

    // 处理下拉菜单命令
    handleCommand(command, item, index) {
      if (command === 'delete') {
        this.handleRemoveGoods(index);
      }
    },

    // 移除商品
    handleRemoveGoods(index) {
      this.$confirm('确定要移除该商品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.goodsList.splice(index, 1);
        // 更新排序
        this.goodsList.forEach((item, idx) => {
          item.sort = idx;
        });
        this.$message.success('移除成功');
      }).catch(() => {});
    },

    // 处理拖拽排序
    handleDragSort() {
      // 更新排序字段
      this.goodsList.forEach((item, index) => {
        item.sort = index;
      });
    }
  }
};
</script>

<style scoped lang="scss">
.goods-manager {
  .action-toolbar {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
  }
  
  .goods-grid {
    margin-top: 20px;
  }
  
  .goods-draggable {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }
  
  .goods-col {
    margin-bottom: 20px;
  }
  
  .goods-item {
    position: relative;
    
    .goods-item-header {
      display: flex;
      justify-content: space-between;
      padding-bottom: 8px;
      
      .drag-handle {
        cursor: move;
        color: #909399;
        
        &:hover {
          color: #409EFF;
        }
      }
      
      .goods-actions {
        cursor: pointer;
        color: #909399;
        
        &:hover {
          color: #409EFF;
        }
      }
    }
    
    .goods-item-content {
      .goods-image {
        width: 100%;
        height: 0;
        padding-bottom: 100%;
        position: relative;
        overflow: hidden;
        border-radius: 4px;
        
        .el-image {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          
          .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background-color: #f5f7fa;
            color: #909399;
          }
        }
      }
      
      .goods-title {
        margin-top: 8px;
        font-size: 14px;
        color: #303133;
        line-height: 1.5;
        height: 40px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      
      .goods-price {
        margin-top: 5px;
        font-size: 16px;
        color: #f56c6c;
        font-weight: bold;
      }
      
      .goods-footer {
        display: flex;
        justify-content: space-between;
        margin-top: 5px;
        font-size: 12px;
        color: #909399;
        
        .goods-stock {
          color: #e6a23c;
        }
      }
    }
  }
  
  .search-bar {
    margin-bottom: 15px;
  }
  
  .dialog-pagination {
    margin-top: 15px;
    text-align: center;
  }
  
  .ghost {
    opacity: 0.5;
    background: #c8ebfb;
  }
}
</style> 