<template>
  <div class="style-manager">
    <el-row :gutter="20">
      <el-col :span="16">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>风格设置</span>
              <div>
                <el-button type="primary" @click="handleSave" v-hasPermi="['shop:style:save']">
                  <el-icon><Check /></el-icon> 保存设置
                </el-button>
                <el-button type="success" @click="handleApply" v-hasPermi="['shop:style:apply']">
                  <el-icon><Upload /></el-icon> 应用到小程序
                </el-button>
              </div>
            </div>
          </template>
          
          <el-form :model="styleForm" :rules="rules" ref="styleFormRef" label-width="100px">
            <el-divider content-position="left">
              <el-tag>主题颜色</el-tag>
            </el-divider>
            
            <el-form-item label="主题色" prop="primaryColor">
              <el-row :gutter="10" class="color-picker-row">
                <el-col :span="6">
                  <el-color-picker v-model="styleForm.primaryColor" show-alpha @change="updateTheme" />
                </el-col>
                <el-col :span="9">
                  <el-input v-model="styleForm.primaryColor" placeholder="色值" @change="updateTheme" />
                </el-col>
                <el-col :span="9">
                  <div class="preset-colors">
                    <div 
                      v-for="(color, index) in presetColors" 
                      :key="index" 
                      :style="{ backgroundColor: color }" 
                      class="color-block"
                      @click="styleForm.primaryColor = color; updateTheme()"
                    ></div>
                  </div>
                </el-col>
              </el-row>
            </el-form-item>
            
            <el-form-item label="文字颜色" prop="textColor">
              <el-row :gutter="10" class="color-picker-row">
                <el-col :span="6">
                  <el-color-picker v-model="styleForm.textColor" show-alpha @change="updateTheme" />
                </el-col>
                <el-col :span="9">
                  <el-input v-model="styleForm.textColor" placeholder="色值" @change="updateTheme" />
                </el-col>
              </el-row>
            </el-form-item>
            
            <el-form-item label="背景色" prop="backgroundColor">
              <el-row :gutter="10" class="color-picker-row">
                <el-col :span="6">
                  <el-color-picker v-model="styleForm.backgroundColor" show-alpha @change="updateTheme" />
                </el-col>
                <el-col :span="9">
                  <el-input v-model="styleForm.backgroundColor" placeholder="色值" @change="updateTheme" />
                </el-col>
              </el-row>
            </el-form-item>
            
            <el-divider content-position="left">
              <el-tag>导航栏</el-tag>
            </el-divider>
            
            <el-form-item label="导航标题" prop="navTitle">
              <el-input v-model="styleForm.navTitle" placeholder="请输入导航栏标题" maxlength="12" show-word-limit @change="updateTheme" />
            </el-form-item>
            
            <el-form-item label="导航颜色" prop="navColor">
              <el-row :gutter="10" class="color-picker-row">
                <el-col :span="6">
                  <el-color-picker v-model="styleForm.navColor" show-alpha @change="updateTheme" />
                </el-col>
                <el-col :span="9">
                  <el-input v-model="styleForm.navColor" placeholder="色值" @change="updateTheme" />
                </el-col>
              </el-row>
            </el-form-item>
            
            <el-form-item label="文字颜色" prop="navTextColor">
              <el-radio-group v-model="styleForm.navTextColor" @change="updateTheme">
                <el-radio label="white">白色</el-radio>
                <el-radio label="black">黑色</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-divider content-position="left">
              <el-tag>页面设置</el-tag>
            </el-divider>
            
            <el-form-item label="首页布局" prop="layoutType">
              <el-radio-group v-model="styleForm.layoutType" @change="updateTheme">
                <el-radio-button label="card">卡片式</el-radio-button>
                <el-radio-button label="list">列表式</el-radio-button>
                <el-radio-button label="grid">网格式</el-radio-button>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="字体大小" prop="fontSize">
              <el-slider 
                v-model="styleForm.fontSize" 
                :min="12" 
                :max="18" 
                :step="1" 
                show-stops 
                :format-tooltip="formatFontSize"
                @change="updateTheme"
              />
            </el-form-item>
            
            <el-form-item label="圆角大小" prop="borderRadius">
              <el-slider 
                v-model="styleForm.borderRadius" 
                :min="0" 
                :max="16" 
                :step="2" 
                show-stops 
                :format-tooltip="formatRadius"
                @change="updateTheme"
              />
            </el-form-item>
            
            <el-divider content-position="left">
              <el-tag>主题模板</el-tag>
            </el-divider>
            
            <el-form-item label="预设主题">
              <div class="theme-templates">
                <div 
                  v-for="(theme, index) in themeTemplates" 
                  :key="index" 
                  class="theme-template-item"
                  :class="{ active: currentThemeTemplate === index }"
                  @click="applyThemeTemplate(index)"
                >
                  <div class="theme-preview">
                    <div class="theme-nav" :style="{ backgroundColor: theme.navColor }">
                      <div :style="{ color: theme.navTextColor === 'white' ? '#ffffff' : '#000000' }">
                        {{ theme.navTitle || '驿站帮Pro' }}
                      </div>
                    </div>
                    <div class="theme-content" :style="{ backgroundColor: theme.backgroundColor }">
                      <div class="theme-button" :style="{ backgroundColor: theme.primaryColor, borderRadius: theme.borderRadius + 'px' }">
                        <span :style="{ color: '#ffffff', fontSize: theme.fontSize + 'px' }">按钮</span>
                      </div>
                      <div class="theme-text" :style="{ color: theme.textColor, fontSize: theme.fontSize + 'px' }">
                        示例文本
                      </div>
                    </div>
                  </div>
                  <div class="theme-name">{{ theme.name }}</div>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card shadow="hover" class="preview-card">
          <template #header>
            <div class="card-header">
              <span>效果预览</span>
            </div>
          </template>
          
          <div class="phone-container">
            <div class="phone-frame">
              <div class="phone-header" :style="{ backgroundColor: styleForm.navColor }">
                <div class="phone-title" :style="{ color: styleForm.navTextColor === 'white' ? '#ffffff' : '#000000' }">
                  {{ styleForm.navTitle || '驿站帮Pro' }}
                </div>
              </div>
              <div class="phone-content" :style="{ backgroundColor: styleForm.backgroundColor }">
                <!-- 模拟首页内容 -->
                <div class="mock-banner"></div>
                
                <div class="mock-section">
                  <div class="mock-title" :style="{ color: styleForm.textColor, fontSize: (styleForm.fontSize + 2) + 'px' }">
                    热门驿站
                  </div>
                  
                  <div class="mock-cards" :class="styleForm.layoutType">
                    <div class="mock-card" v-for="i in 4" :key="i" 
                      :style="{ borderRadius: styleForm.borderRadius + 'px' }">
                      <div class="mock-card-img"></div>
                      <div class="mock-card-title" :style="{ color: styleForm.textColor, fontSize: styleForm.fontSize + 'px' }">
                        驿站示例 {{ i }}
                      </div>
                      <div class="mock-card-desc" :style="{ color: styleForm.textColor, fontSize: (styleForm.fontSize - 2) + 'px' }">
                        这是一个驿站示例描述
                      </div>
                      <div class="mock-card-footer">
                        <div class="mock-price" :style="{ color: styleForm.primaryColor, fontSize: styleForm.fontSize + 'px' }">
                          ¥3500/月
                        </div>
                        <div class="mock-btn" :style="{ backgroundColor: styleForm.primaryColor, borderRadius: (styleForm.borderRadius / 2) + 'px' }">
                          详情
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="mock-section">
                  <div class="mock-tabs">
                    <div class="mock-tab active" :style="{ color: styleForm.primaryColor, borderBottomColor: styleForm.primaryColor }">
                      全部
                    </div>
                    <div class="mock-tab" :style="{ color: styleForm.textColor }">附近</div>
                    <div class="mock-tab" :style="{ color: styleForm.textColor }">热门</div>
                  </div>
                </div>
                
                <div class="primary-button" :style="{ 
                  backgroundColor: styleForm.primaryColor, 
                  borderRadius: styleForm.borderRadius + 'px',
                  fontSize: styleForm.fontSize + 'px'
                }">
                  主要按钮
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { 
  getShopStyle, 
  saveShopStyle, 
  applyShopStyle 
} from '@/api/shop/decoration';

export default {
  name: 'StyleManager',
  data() {
    return {
      // 表单对象
      styleForm: {
        primaryColor: '#1890ff',
        textColor: '#333333',
        backgroundColor: '#f5f5f5',
        navTitle: '驿站帮Pro',
        navColor: '#1890ff',
        navTextColor: 'white',
        layoutType: 'card',
        fontSize: 14,
        borderRadius: 8
      },
      // 表单校验规则
      rules: {
        primaryColor: [
          { required: true, message: '请选择主题色', trigger: 'change' }
        ],
        textColor: [
          { required: true, message: '请选择文字颜色', trigger: 'change' }
        ],
        backgroundColor: [
          { required: true, message: '请选择背景色', trigger: 'change' }
        ],
        navTitle: [
          { required: true, message: '请输入导航栏标题', trigger: 'blur' },
          { max: 12, message: '导航栏标题不能超过12个字符', trigger: 'blur' }
        ],
        navColor: [
          { required: true, message: '请选择导航颜色', trigger: 'change' }
        ],
        navTextColor: [
          { required: true, message: '请选择导航文字颜色', trigger: 'change' }
        ],
        layoutType: [
          { required: true, message: '请选择首页布局', trigger: 'change' }
        ]
      },
      // 预设颜色
      presetColors: [
        '#1890ff', // 蓝色
        '#f5222d', // 红色
        '#52c41a', // 绿色
        '#faad14', // 黄色
        '#722ed1', // 紫色
        '#13c2c2', // 青色
        '#fa541c', // 橙色
        '#2f54eb'  // 靛蓝
      ],
      // 主题模板
      themeTemplates: [
        {
          name: '默认蓝',
          primaryColor: '#1890ff',
          textColor: '#333333',
          backgroundColor: '#f5f5f5',
          navTitle: '驿站帮Pro',
          navColor: '#1890ff',
          navTextColor: 'white',
          layoutType: 'card',
          fontSize: 14,
          borderRadius: 8
        },
        {
          name: '清新绿',
          primaryColor: '#52c41a',
          textColor: '#333333',
          backgroundColor: '#f7f7f7',
          navTitle: '驿站帮Pro',
          navColor: '#52c41a',
          navTextColor: 'white',
          layoutType: 'card',
          fontSize: 14,
          borderRadius: 8
        },
        {
          name: '活力橙',
          primaryColor: '#fa541c',
          textColor: '#333333',
          backgroundColor: '#fff9f8',
          navTitle: '驿站帮Pro',
          navColor: '#fa541c',
          navTextColor: 'white',
          layoutType: 'grid',
          fontSize: 14,
          borderRadius: 8
        },
        {
          name: '简约黑',
          primaryColor: '#333333',
          textColor: '#333333',
          backgroundColor: '#ffffff',
          navTitle: '驿站帮Pro',
          navColor: '#333333',
          navTextColor: 'white',
          layoutType: 'list',
          fontSize: 14,
          borderRadius: 0
        },
        {
          name: '高贵紫',
          primaryColor: '#722ed1',
          textColor: '#333333',
          backgroundColor: '#f9f0ff',
          navTitle: '驿站帮Pro',
          navColor: '#722ed1',
          navTextColor: 'white',
          layoutType: 'card',
          fontSize: 14,
          borderRadius: 16
        },
        {
          name: '时尚红',
          primaryColor: '#f5222d',
          textColor: '#333333',
          backgroundColor: '#fff1f0',
          navTitle: '驿站帮Pro',
          navColor: '#f5222d',
          navTextColor: 'white',
          layoutType: 'grid',
          fontSize: 14,
          borderRadius: 8
        }
      ],
      // 当前使用的主题模板索引
      currentThemeTemplate: 0,
      // 加载状态
      loading: false
    };
  },
  created() {
    this.fetchShopStyle();
  },
  methods: {
    // 获取店铺风格数据
    fetchShopStyle() {
      this.loading = true;
      getShopStyle().then(response => {
        if (response.data) {
          this.styleForm = {...this.styleForm, ...response.data};
          
          // 尝试匹配当前设置和预设主题
          this.matchCurrentTheme();
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    // 保存店铺风格
    handleSave() {
      this.$refs.styleFormRef.validate(valid => {
        if (valid) {
          this.loading = true;
          saveShopStyle(this.styleForm).then(() => {
            this.$modal.msgSuccess('保存成功');
            this.loading = false;
          }).catch(() => {
            this.loading = false;
          });
        }
      });
    },
    
    // 应用到小程序
    handleApply() {
      this.$modal.confirm('确认将当前样式设置应用到小程序吗？').then(() => {
        return applyShopStyle(this.styleForm);
      }).then(() => {
        this.$modal.msgSuccess('应用成功');
      }).catch(() => {});
    },
    
    // 更新主题（此处仅用于预览效果）
    updateTheme() {
      // 检查当前设置与预设主题的匹配度
      this.matchCurrentTheme();
    },
    
    // 应用主题模板
    applyThemeTemplate(index) {
      const template = this.themeTemplates[index];
      this.styleForm = {...this.styleForm, ...template};
      this.currentThemeTemplate = index;
    },
    
    // 匹配当前设置与预设主题
    matchCurrentTheme() {
      const currentStyle = JSON.stringify({
        primaryColor: this.styleForm.primaryColor,
        textColor: this.styleForm.textColor,
        backgroundColor: this.styleForm.backgroundColor,
        navColor: this.styleForm.navColor,
        navTextColor: this.styleForm.navTextColor,
        layoutType: this.styleForm.layoutType,
        fontSize: this.styleForm.fontSize,
        borderRadius: this.styleForm.borderRadius
      });
      
      for (let i = 0; i < this.themeTemplates.length; i++) {
        const template = this.themeTemplates[i];
        const templateStyle = JSON.stringify({
          primaryColor: template.primaryColor,
          textColor: template.textColor,
          backgroundColor: template.backgroundColor,
          navColor: template.navColor,
          navTextColor: template.navTextColor,
          layoutType: template.layoutType,
          fontSize: template.fontSize,
          borderRadius: template.borderRadius
        });
        
        if (currentStyle === templateStyle) {
          this.currentThemeTemplate = i;
          return;
        }
      }
      
      this.currentThemeTemplate = -1; // 没有匹配的模板
    },
    
    // 格式化字体大小
    formatFontSize(val) {
      return val + 'px';
    },
    
    // 格式化圆角大小
    formatRadius(val) {
      return val + 'px';
    }
  }
};
</script>

<style scoped lang="scss">
.style-manager {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .color-picker-row {
    display: flex;
    align-items: center;
  }
  
  .preset-colors {
    display: flex;
    flex-wrap: wrap;
    
    .color-block {
      width: 24px;
      height: 24px;
      margin: 0 4px 4px 0;
      border-radius: 4px;
      cursor: pointer;
      border: 1px solid #dcdfe6;
      
      &:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
  
  .theme-templates {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    
    .theme-template-item {
      width: 120px;
      height: 160px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
      
      &.active {
        border: 2px solid #1890ff;
      }
      
      .theme-preview {
        height: 120px;
        overflow: hidden;
        
        .theme-nav {
          height: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          text-align: center;
        }
        
        .theme-content {
          height: 90px;
          padding: 8px;
          
          .theme-button {
            height: 24px;
            width: 60px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 8px;
            
            span {
              font-size: 12px;
            }
          }
          
          .theme-text {
            font-size: 12px;
          }
        }
      }
      
      .theme-name {
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        color: #666;
      }
    }
  }
  
  // 电话预览
  .phone-container {
    display: flex;
    justify-content: center;
    height: 600px;
  }
  
  .phone-frame {
    width: 300px;
    height: 580px;
    border: 10px solid #333;
    border-radius: 30px;
    overflow: hidden;
    position: relative;
    background: #fff;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
  
  .phone-header {
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #ddd;
  }
  
  .phone-title {
    font-size: 14px;
    font-weight: bold;
  }
  
  .phone-content {
    height: calc(100% - 40px);
    overflow-y: auto;
    padding: 10px;
  }
  
  // 模拟UI元素
  .mock-banner {
    height: 120px;
    background: linear-gradient(to right, #ddd, #eee);
    border-radius: 8px;
    margin-bottom: 15px;
  }
  
  .mock-section {
    margin-bottom: 15px;
  }
  
  .mock-title {
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .mock-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    
    &.card {
      flex-direction: column;
    }
    
    &.grid {
      flex-direction: row;
      .mock-card {
        width: calc(50% - 5px);
      }
    }
    
    &.list {
      flex-direction: column;
      .mock-card {
        height: 80px;
        display: flex;
        .mock-card-img {
          width: 80px;
          min-width: 80px;
          height: 100%;
          margin-right: 8px;
        }
      }
    }
  }
  
  .mock-card {
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 10px;
    
    .mock-card-img {
      height: 80px;
      background: linear-gradient(135deg, #ddd, #ccc);
    }
    
    .mock-card-title {
      padding: 5px 8px;
      font-weight: bold;
    }
    
    .mock-card-desc {
      padding: 0 8px 5px;
      opacity: 0.8;
    }
    
    .mock-card-footer {
      padding: 5px 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .mock-price {
        font-weight: bold;
      }
      
      .mock-btn {
        padding: 2px 8px;
        color: #fff;
        font-size: 12px;
      }
    }
  }
  
  .mock-tabs {
    display: flex;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;
    
    .mock-tab {
      padding: 8px 15px;
      font-size: 14px;
      cursor: pointer;
      
      &.active {
        font-weight: bold;
        border-bottom: 2px solid;
      }
    }
  }
  
  .primary-button {
    width: 100%;
    height: 40px;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
  }
  
  // 预览卡片
  .preview-card {
    position: sticky;
    top: 70px;
  }
}
</style> 