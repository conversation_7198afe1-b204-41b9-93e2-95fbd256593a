<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>小程序店铺装修</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="previewApp">预览小程序</el-button>
      </div>
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="首页装修" name="home">
          <div class="decoration-container">
            <div class="mobile-preview">
              <div class="mobile-header">
                <div class="status-bar"></div>
                <div class="title-bar">{{shopInfo.name || '我的驿站'}}</div>
              </div>
              <div class="mobile-content">
                <div class="banner-area">
                  <div class="placeholder" v-if="!homeConfig.banner || homeConfig.banner.length === 0">
                    <i class="el-icon-picture"></i>
                    <span>轮播图区域</span>
                  </div>
                  <div class="banner-preview" v-else>
                    <el-carousel height="150px" indicator-position="none">
                      <el-carousel-item v-for="(item, index) in homeConfig.banner" :key="index">
                        <img :src="item.imageUrl" alt="轮播图">
                      </el-carousel-item>
                    </el-carousel>
                  </div>
                </div>
                <div class="nav-area">
                  <div class="placeholder" v-if="!homeConfig.nav || homeConfig.nav.length === 0">
                    <i class="el-icon-s-grid"></i>
                    <span>导航区域</span>
                  </div>
                  <div class="nav-preview" v-else>
                    <div class="nav-item" v-for="(item, index) in homeConfig.nav" :key="index">
                      <div class="nav-icon">
                        <img :src="item.iconUrl" alt="导航图标">
                      </div>
                      <div class="nav-text">{{ item.title }}</div>
                    </div>
                  </div>
                </div>
                <div class="goods-area">
                  <div class="placeholder">
                    <i class="el-icon-shopping-cart-full"></i>
                    <span>商品展示区域</span>
                  </div>
                </div>
              </div>
              <div class="mobile-footer">
                <div class="menu-item" v-for="(item, index) in menuConfig.items" :key="index" :class="{ active: index === 0 }">
                  <i :class="item.icon"></i>
                  <div class="menu-text">{{ item.text }}</div>
                </div>
              </div>
            </div>
            <div class="config-panel">
              <el-collapse v-model="activeConfig">
                <el-collapse-item title="轮播图配置" name="banner">
                  <banner-manager v-model="homeConfig.banner"></banner-manager>
                </el-collapse-item>
                <el-collapse-item title="导航配置" name="nav">
                  <nav-manager v-model="homeConfig.nav"></nav-manager>
                </el-collapse-item>
                <el-collapse-item title="商品配置" name="goods">
                  <goods-manager v-model="homeConfig.goods"></goods-manager>
                </el-collapse-item>
                <el-collapse-item title="布局设置" name="layout">
                  <layout-manager v-model="homeConfig.layout"></layout-manager>
                </el-collapse-item>
              </el-collapse>
              <div class="actions">
                <el-button type="primary" @click="saveHomeConfig">保存设置</el-button>
                <el-button type="success" @click="applyHomeConfig">应用到小程序</el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="底部菜单" name="menu">
          <menu-manager ref="menuManager"></menu-manager>
        </el-tab-pane>
        <el-tab-pane label="主题风格" name="style">
          <style-manager ref="styleManager"></style-manager>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
import BannerManager from './components/BannerManager'
import NavManager from './components/NavManager'
import GoodsManager from './components/GoodsManager'
import LayoutManager from './components/LayoutManager'
import MenuManager from './components/MenuManager'
import StyleManager from './components/StyleManager'
import { getShopInfo } from '@/api/shop/info'
import { getBannerConfig, getNavConfig, getLayoutConfig, saveLayoutConfig, saveBannerConfig, saveNavConfig, applyHomeConfig as applyHome } from '@/api/shop/decoration'

export default {
  name: 'ShopDecoration',
  components: {
    BannerManager,
    NavManager,
    GoodsManager,
    LayoutManager,
    MenuManager,
    StyleManager
  },
  data() {
    return {
      activeTab: 'home',
      activeConfig: ['banner'],
      shopInfo: {},
      homeConfig: {
        banner: [],
        nav: [],
        goods: [],
        layout: {
          backgroundColor: '#f5f5f5',
          showSearch: true,
          searchStyle: 'round',
          navStyle: 'grid',
          goodsStyle: 'card'
        }
      },
      menuConfig: {
        items: [
          { text: '首页', icon: 'el-icon-s-home', pagePath: '/pages/index/index' },
          { text: '分类', icon: 'el-icon-menu', pagePath: '/pages/category/index' },
          { text: '购物车', icon: 'el-icon-shopping-cart-2', pagePath: '/pages/cart/index' },
          { text: '我的', icon: 'el-icon-user', pagePath: '/pages/user/index' }
        ]
      }
    }
  },
  created() {
    this.getShopInfo()
    this.getHomeConfig()
  },
  methods: {
    handleTabClick() {
      // 处理标签页切换
    },
    getShopInfo() {
      getShopInfo().then(response => {
        this.shopInfo = response.data
      })
    },
    getHomeConfig() {
      Promise.all([
        getBannerConfig(),
        getNavConfig(),
        getLayoutConfig()
      ]).then(responses => {
        this.homeConfig.banner = responses[0].data || []
        this.homeConfig.nav = responses[1].data || []
        this.homeConfig.layout = responses[2].data || this.homeConfig.layout
      })
    },
    saveHomeConfig() {
      Promise.all([
        saveBannerConfig(this.homeConfig.banner),
        saveNavConfig(this.homeConfig.nav),
        saveLayoutConfig(this.homeConfig.layout)
      ]).then(() => {
        this.$message.success('保存成功')
      }).catch(() => {
        this.$message.error('保存失败')
      })
    },
    applyHomeConfig() {
      applyHome().then(() => {
        this.$message.success('应用成功')
      }).catch(() => {
        this.$message.error('应用失败')
      })
    },
    previewApp() {
      this.$message({
        message: '请使用微信开发者工具扫描二维码预览',
        type: 'info'
      })
      // 显示小程序预览二维码
    }
  }
}
</script>

<style lang="scss" scoped>
.decoration-container {
  display: flex;
  margin-top: 20px;
  
  .mobile-preview {
    width: 375px;
    height: 667px;
    border: 1px solid #dcdfe6;
    border-radius: 20px;
    background-color: #fff;
    overflow: hidden;
    flex-shrink: 0;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .mobile-header {
      .status-bar {
        height: 20px;
        background-color: #000;
      }
      
      .title-bar {
        height: 44px;
        line-height: 44px;
        text-align: center;
        font-size: 16px;
        font-weight: bold;
        border-bottom: 1px solid #f0f0f0;
      }
    }
    
    .mobile-content {
      height: 548px;
      overflow-y: auto;
      background-color: #f5f5f5;
      
      .placeholder {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 150px;
        background-color: #fafafa;
        color: #909399;
        border: 1px dashed #dcdfe6;
        margin: 10px;
        
        i {
          font-size: 30px;
          margin-bottom: 10px;
        }
      }
      
      .banner-area {
        height: 150px;
      }
      
      .nav-area {
        background-color: #fff;
        margin: 10px;
        padding: 10px 0;
        
        .nav-preview {
          display: flex;
          flex-wrap: wrap;
          
          .nav-item {
            width: 20%;
            text-align: center;
            margin-bottom: 10px;
            
            .nav-icon {
              width: 40px;
              height: 40px;
              margin: 0 auto;
              
              img {
                width: 100%;
                height: 100%;
              }
            }
            
            .nav-text {
              font-size: 12px;
              margin-top: 5px;
              color: #333;
            }
          }
        }
      }
      
      .goods-area {
        margin: 10px;
      }
    }
    
    .mobile-footer {
      height: 54px;
      border-top: 1px solid #f0f0f0;
      display: flex;
      background-color: #fff;
      
      .menu-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #909399;
        
        &.active {
          color: #409eff;
        }
        
        i {
          font-size: 22px;
        }
        
        .menu-text {
          font-size: 12px;
          margin-top: 4px;
        }
      }
    }
  }
  
  .config-panel {
    flex: 1;
    margin-left: 20px;
    
    .el-collapse {
      border: none;
    }
    
    .actions {
      margin-top: 20px;
      text-align: right;
    }
  }
}
</style> 