<template>
  <div class="app-container">
    <!-- 操作按钮 -->
    <el-card class="table-container">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
        </el-col>
      </el-row>

      <!-- 轮播图列表 -->
      <el-table v-loading="loading" :data="bannerList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="bannerId" />
        <el-table-column label="标题" align="center" prop="title" min-width="150" show-overflow-tooltip />
        <el-table-column label="图片" align="center" width="100">
          <template #default="scope">
            <el-image 
              :src="scope.row.imageUrl" 
              :preview-src-list="[scope.row.imageUrl]"
              fit="cover"
              class="banner-image"
            />
          </template>
        </el-table-column>
        <el-table-column label="跳转链接" align="center" prop="linkUrl" min-width="200" show-overflow-tooltip />
        <el-table-column label="类型" align="center" prop="type">
          <template #default="scope">
            <el-tag :type="scope.row.type === 1 ? 'primary' : 'success'">
              {{ scope.row.type === 1 ? '首页轮播' : '驿站轮播' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center" prop="sort" width="80" />
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="total > 0"
        :total="total"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 添加或修改轮播图对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="700px" append-to-body>
      <el-form ref="bannerForm" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入轮播图标题" />
        </el-form-item>
        <el-form-item label="轮播图片" prop="imageUrl">
          <el-upload
            class="banner-uploader"
            action="/api/common/upload"
            :show-file-list="false"
            :on-success="handleImageSuccess"
          >
            <img v-if="form.imageUrl" :src="form.imageUrl" class="banner-img" />
            <el-icon v-else class="banner-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="el-upload__tip">建议上传尺寸比例为16:9的图片，图片大小不超过2MB</div>
        </el-form-item>
        <el-form-item label="跳转链接" prop="linkUrl">
          <el-input v-model="form.linkUrl" placeholder="请输入点击轮播图后的跳转链接" />
        </el-form-item>
        <el-form-item label="轮播类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio :label="1">首页轮播</el-radio>
            <el-radio :label="2">驿站轮播</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="999" placeholder="请输入排序号" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">显示</el-radio>
            <el-radio :label="0">隐藏</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 后续将导入API
// import { listBanner, addBanner, updateBanner, deleteBanner, changeBannerStatus } from '@/api/content/banner'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10
})

// 表格数据
const loading = ref(false)
const bannerList = ref([])
const total = ref(0)
const ids = ref([])
const single = computed(() => ids.value.length !== 1)
const multiple = computed(() => ids.value.length === 0)

// 弹窗参数
const dialog = reactive({
  visible: false,
  title: '',
  type: ''
})

// 表单参数
const bannerForm = ref(null)
const form = reactive({
  bannerId: null,
  title: '',
  imageUrl: '',
  linkUrl: '',
  type: 1,
  sort: 0,
  status: 1,
  remark: ''
})

// 表单校验规则
const rules = reactive({
  title: [
    { required: true, message: '标题不能为空', trigger: 'blur' }
  ],
  imageUrl: [
    { required: true, message: '请上传轮播图片', trigger: 'change' }
  ],
  type: [
    { required: true, message: '轮播类型不能为空', trigger: 'change' }
  ]
})

// 生命周期钩子
onMounted(() => {
  getList()
})

// 获取轮播图列表
const getList = () => {
  loading.value = true
  // 测试数据，后续替换为API调用
  // listBanner(queryParams).then(response => {
  //   bannerList.value = response.rows
  //   total.value = response.total
  //   loading.value = false
  // })
  
  // 模拟数据
  setTimeout(() => {
    bannerList.value = [
      {
        bannerId: 1,
        title: '驿站帮Pro上线啦',
        imageUrl: 'https://example.com/banner1.jpg',
        linkUrl: '/pages/index/index',
        type: 1,
        sort: 1,
        status: 1,
        createTime: '2023-05-15 10:30:00',
        updateTime: '2023-05-15 10:30:00',
        remark: '宣传驿站帮Pro应用上线'
      },
      {
        bannerId: 2,
        title: '快递服务活动',
        imageUrl: 'https://example.com/banner2.jpg',
        linkUrl: '/pages/service/detail?id=1',
        type: 1,
        sort: 2,
        status: 1,
        createTime: '2023-05-10 14:20:00',
        updateTime: '2023-05-10 14:20:00',
        remark: '宣传快递服务优惠活动'
      },
      {
        bannerId: 3,
        title: '海淀创业园驿站介绍',
        imageUrl: 'https://example.com/banner3.jpg',
        linkUrl: '/pages/station/detail?id=1',
        type: 2,
        sort: 1,
        status: 0,
        createTime: '2023-05-05 09:15:00',
        updateTime: '2023-05-05 09:15:00',
        remark: '介绍海淀创业园驿站'
      }
    ]
    total.value = 3
    loading.value = false
  }, 500)
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.bannerId)
}

// 新增按钮操作
const handleAdd = () => {
  reset()
  dialog.visible = true
  dialog.title = '添加轮播图'
  dialog.type = 'add'
}

// 修改按钮操作
const handleUpdate = (row) => {
  reset()
  const bannerId = row.bannerId || ids.value[0]
  // 后续替换为API调用获取详情
  // getBanner(bannerId).then(response => {
  //   Object.assign(form, response.data)
  //   dialog.visible = true
  //   dialog.title = '修改轮播图'
  //   dialog.type = 'edit'
  // })
  
  // 模拟数据
  const selectedRow = bannerList.value.find(item => item.bannerId === bannerId)
  if (selectedRow) {
    Object.assign(form, selectedRow)
    dialog.visible = true
    dialog.title = '修改轮播图'
    dialog.type = 'edit'
  }
}

// 提交表单
const submitForm = () => {
  bannerForm.value.validate(valid => {
    if (valid) {
      if (form.bannerId) {
        // updateBanner(form).then(response => {
        //   ElMessage.success('修改成功')
        //   dialog.visible = false
        //   getList()
        // })
        ElMessage.success('修改成功')
        dialog.visible = false
        getList()
      } else {
        // addBanner(form).then(response => {
        //   ElMessage.success('新增成功')
        //   dialog.visible = false
        //   getList()
        // })
        ElMessage.success('新增成功')
        dialog.visible = false
        getList()
      }
    }
  })
}

// 删除按钮操作
const handleDelete = (row) => {
  const bannerIds = row.bannerId || ids.value
  ElMessageBox.confirm('是否确认删除所选轮播图?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // deleteBanner(bannerIds).then(() => {
    //   ElMessage.success('删除成功')
    //   getList()
    // })
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {})
}

// 状态修改
const handleStatusChange = (row) => {
  // changeBannerStatus({ bannerId: row.bannerId, status: row.status }).then(() => {
  //   ElMessage.success(`${row.status === 1 ? '显示' : '隐藏'}成功`)
  // }).catch(() => {
  //   row.status = row.status === 1 ? 0 : 1
  // })
  ElMessage.success(`${row.status === 1 ? '显示' : '隐藏'}成功`)
}

// 图片上传成功处理
const handleImageSuccess = (response) => {
  form.imageUrl = response.data
}

// 表单重置
const reset = () => {
  form.bannerId = null
  form.title = ''
  form.imageUrl = ''
  form.linkUrl = ''
  form.type = 1
  form.sort = 0
  form.status = 1
  form.remark = ''
}

// 取消按钮
const cancel = () => {
  dialog.visible = false
  reset()
}

// 分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.banner-image {
  width: 80px;
  height: 45px;
  border-radius: 4px;
}

.banner-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    
    &:hover {
      border-color: var(--el-color-primary);
    }
  }
  
  .banner-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 240px;
    height: 135px;
    text-align: center;
    line-height: 135px;
  }
  
  .banner-img {
    width: 240px;
    height: 135px;
    display: block;
  }
}
</style> 