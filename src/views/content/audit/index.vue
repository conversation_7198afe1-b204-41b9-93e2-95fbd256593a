<template>
  <div class="page-container">
    <div class="action-container">
      <div class="search-box">
        <el-input v-model="queryParams.keyword" placeholder="请输入内容标题/发布人" clearable style="width: 200px" class="mr-10" />
        <el-select v-model="queryParams.type" placeholder="内容类型" clearable class="mr-10" style="width: 120px">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="mr-10"
          style="width: 240px"
        />
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </div>
      <div class="button-group">
        <el-button type="success" @click="handleBatchApprove">批量通过</el-button>
        <el-button type="danger" @click="handleBatchReject">批量拒绝</el-button>
      </div>
    </div>
    
    <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane label="待审核内容" name="pending">
        <el-table v-loading="loading" :data="contentList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="title" label="内容标题" min-width="160" show-overflow-tooltip />
          <el-table-column prop="contentType" label="内容类型" width="100">
            <template #default="scope">
              <el-tag :type="getTypeTag(scope.row.contentType)">
                {{ contentTypeMap[scope.row.contentType] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="publisher" label="发布人" width="120" />
          <el-table-column prop="createTime" label="发布时间" width="160" sortable />
          <el-table-column prop="priority" label="优先级" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.priority === 'high' ? 'danger' : scope.row.priority === 'medium' ? 'warning' : 'info'">
                {{ priorityMap[scope.row.priority] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="scope">
              <el-button type="text" @click="handleDetail(scope.row)">查看详情</el-button>
              <el-button type="text" @click="handleApprove(scope.row)">通过</el-button>
              <el-button type="text" @click="handleReject(scope.row)">拒绝</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      
      <el-tab-pane label="审核历史" name="history">
        <el-table v-loading="historyLoading" :data="historyList">
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="title" label="内容标题" min-width="160" show-overflow-tooltip />
          <el-table-column prop="contentType" label="内容类型" width="100">
            <template #default="scope">
              <el-tag :type="getTypeTag(scope.row.contentType)">
                {{ contentTypeMap[scope.row.contentType] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="publisher" label="发布人" width="100" />
          <el-table-column prop="auditStatus" label="审核结果" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.auditStatus === 'approved' ? 'success' : 'danger'">
                {{ scope.row.auditStatus === 'approved' ? '通过' : '拒绝' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="auditTime" label="审核时间" width="160" sortable />
          <el-table-column prop="auditor" label="审核人" width="100" />
          <el-table-column prop="reason" label="拒绝理由" min-width="150" show-overflow-tooltip>
            <template #default="scope">
              {{ scope.row.auditStatus === 'rejected' ? scope.row.reason : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="text" @click="handleDetail(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 拒绝理由选择弹窗 -->
    <el-dialog title="拒绝理由" v-model="rejectDialogVisible" width="500px" append-to-body>
      <el-form ref="rejectForm" :model="rejectForm" label-width="80px">
        <el-form-item label="拒绝理由" prop="reason">
          <el-select v-model="rejectForm.reason" placeholder="请选择拒绝理由" style="width: 100%">
            <el-option v-for="item in reasonOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="补充说明" prop="remark">
          <el-input v-model="rejectForm.remark" type="textarea" placeholder="请输入补充说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitReject">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ContentAudit',
  data() {
    return {
      // 选项卡
      activeName: 'pending',
      // 遮罩层
      loading: false,
      historyLoading: false,
      // 选中数组
      selectedIds: [],
      // 总条数
      total: 0,
      // 日期范围
      dateRange: [],
      // 内容类型映射
      contentTypeMap: {
        'transfer': '驿站转让',
        'equipment': '设备交易',
        'recruitment': '招聘求职',
        'substitution': '顶班服务'
      },
      // 优先级映射
      priorityMap: {
        'high': '高',
        'medium': '中',
        'low': '低'
      },
      // 内容类型选项
      typeOptions: [
        { label: '驿站转让', value: 'transfer' },
        { label: '设备交易', value: 'equipment' },
        { label: '招聘求职', value: 'recruitment' },
        { label: '顶班服务', value: 'substitution' }
      ],
      // 拒绝理由选项
      reasonOptions: [
        { label: '内容不真实', value: '内容不真实' },
        { label: '信息不完整', value: '信息不完整' },
        { label: '违反平台规则', value: '违反平台规则' },
        { label: '图片不清晰', value: '图片不清晰' },
        { label: '联系方式错误', value: '联系方式错误' },
        { label: '其他原因', value: '其他原因' }
      ],
      // 待审核内容列表
      contentList: [
        {
          id: 1,
          title: '北京市海淀区中关村驿站转让',
          contentType: 'transfer',
          publisher: '张三',
          createTime: '2023-04-15 10:30:00',
          priority: 'high'
        },
        {
          id: 2,
          title: '二手快递柜设备出售',
          contentType: 'equipment',
          publisher: '李四',
          createTime: '2023-04-15 15:20:00',
          priority: 'medium'
        },
        {
          id: 3,
          title: '招聘驿站兼职人员',
          contentType: 'recruitment',
          publisher: '王五',
          createTime: '2023-04-16 09:15:00',
          priority: 'low'
        },
        {
          id: 4,
          title: '周末顶班服务',
          contentType: 'substitution',
          publisher: '赵六',
          createTime: '2023-04-16 11:45:00',
          priority: 'medium'
        }
      ],
      // 审核历史列表
      historyList: [
        {
          id: 5,
          title: '朝阳区望京SOHO驿站转让',
          contentType: 'transfer',
          publisher: '张三',
          auditStatus: 'approved',
          auditTime: '2023-04-14 14:25:00',
          auditor: 'admin'
        },
        {
          id: 6,
          title: '快递分拣机低价出售',
          contentType: 'equipment',
          publisher: '李四',
          auditStatus: 'rejected',
          auditTime: '2023-04-14 16:10:00',
          auditor: 'admin',
          reason: '图片不清晰'
        }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        type: '',
        startDate: '',
        endDate: ''
      },
      // 拒绝表单
      rejectForm: {
        id: undefined,
        reason: '',
        remark: ''
      },
      // 拒绝理由弹窗
      rejectDialogVisible: false,
      // 当前操作的内容
      currentContent: {}
    }
  },
  methods: {
    /** 获取内容类型标签样式 */
    getTypeTag(type) {
      const typeMap = {
        'transfer': 'primary',
        'equipment': 'success',
        'recruitment': 'warning',
        'substitution': 'info'
      }
      return typeMap[type] || ''
    },
    /** 查询列表 */
    handleQuery() {
      this.queryParams.pageNum = 1
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.startDate = this.dateRange[0]
        this.queryParams.endDate = this.dateRange[1]
      } else {
        this.queryParams.startDate = ''
        this.queryParams.endDate = ''
      }
      // TODO: 实际应该调用API获取数据
      console.log('查询参数：', this.queryParams)
    },
    /** 重置查询 */
    resetQuery() {
      this.dateRange = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        type: '',
        startDate: '',
        endDate: ''
      }
      this.handleQuery()
    },
    /** 切换选项卡 */
    handleTabClick() {
      if (this.activeName === 'history') {
        this.historyLoading = true
        // TODO: 调用API获取审核历史数据
        setTimeout(() => {
          this.historyLoading = false
        }, 500)
      } else {
        this.loading = true
        // TODO: 调用API获取待审核数据
        setTimeout(() => {
          this.loading = false
        }, 500)
      }
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
    },
    /** 查看详情 */
    handleDetail(row) {
      // 跳转到详情页
      this.$router.push(`/content/audit/detail/${row.id}/${row.contentType}`)
    },
    /** 通过审核 */
    handleApprove(row) {
      this.$confirm(`确认通过内容"${row.title}"的审核?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用审核通过API
        console.log('通过内容ID：', row.id)
        this.$message({
          type: 'success',
          message: '审核通过成功!'
        })
      }).catch(() => {})
    },
    /** 拒绝审核 */
    handleReject(row) {
      this.currentContent = row
      this.rejectForm = {
        id: row.id,
        reason: '',
        remark: ''
      }
      this.rejectDialogVisible = true
    },
    /** 提交拒绝 */
    submitReject() {
      if (!this.rejectForm.reason) {
        this.$message({
          type: 'warning',
          message: '请选择拒绝理由'
        })
        return
      }
      
      // TODO: 调用审核拒绝API
      console.log('拒绝内容ID：', this.rejectForm.id)
      console.log('拒绝理由：', this.rejectForm.reason)
      console.log('补充说明：', this.rejectForm.remark)
      
      this.rejectDialogVisible = false
      this.$message({
        type: 'success',
        message: '审核拒绝成功!'
      })
    },
    /** 批量通过 */
    handleBatchApprove() {
      if (this.selectedIds.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条记录'
        })
        return
      }
      
      this.$confirm(`确认批量通过所选的${this.selectedIds.length}条内容?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用批量审核通过API
        console.log('批量通过内容IDs：', this.selectedIds)
        this.$message({
          type: 'success',
          message: '批量审核通过成功!'
        })
      }).catch(() => {})
    },
    /** 批量拒绝 */
    handleBatchReject() {
      if (this.selectedIds.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条记录'
        })
        return
      }
      
      this.rejectForm = {
        id: this.selectedIds.join(','),
        reason: '',
        remark: ''
      }
      this.rejectDialogVisible = true
    },
    /** 分页大小变化 */
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.handleQuery()
    },
    /** 分页页码变化 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.handleQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
.action-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .search-box {
    display: flex;
    align-items: center;
  }
  
  .button-group {
    display: flex;
    align-items: center;
  }
}

.mr-10 {
  margin-right: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style> 