<template>
  <div class="page-container">
    <div class="page-header">
      <el-page-header @back="goBack" :title="contentTypeMap[contentType]" :content="contentData.title || '内容详情'" />
    </div>
    
    <div class="detail-container" v-loading="loading">
      <!-- 通用信息区域 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <div class="header-status">
              <el-tag type="warning">待审核</el-tag>
            </div>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="发布人">{{ contentData.publisher }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ contentData.phone }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ contentData.createTime }}</el-descriptions-item>
          <el-descriptions-item label="内容类型">{{ contentTypeMap[contentType] }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 驿站转让详情 -->
      <el-card class="info-card" v-if="contentType === 'transfer'">
        <template #header>
          <div class="card-header">
            <span>驿站转让详情</span>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="驿站名称">{{ transferData.stationName }}</el-descriptions-item>
          <el-descriptions-item label="驿站类型">{{ transferData.stationType }}</el-descriptions-item>
          <el-descriptions-item label="所在地区" :span="2">{{ transferData.region }}</el-descriptions-item>
          <el-descriptions-item label="详细地址" :span="2">{{ transferData.address }}</el-descriptions-item>
          <el-descriptions-item label="转让价格">{{ transferData.price }} 元</el-descriptions-item>
          <el-descriptions-item label="月营业额">{{ transferData.monthlyRevenue }} 元</el-descriptions-item>
          <el-descriptions-item label="转让原因" :span="2">{{ transferData.reason }}</el-descriptions-item>
          <el-descriptions-item label="转让说明" :span="2">{{ transferData.description }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 设备交易详情 -->
      <el-card class="info-card" v-if="contentType === 'equipment'">
        <template #header>
          <div class="card-header">
            <span>设备交易详情</span>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备名称">{{ equipmentData.name }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">{{ equipmentData.type }}</el-descriptions-item>
          <el-descriptions-item label="品牌型号">{{ equipmentData.brand }}</el-descriptions-item>
          <el-descriptions-item label="使用年限">{{ equipmentData.useYears }} 年</el-descriptions-item>
          <el-descriptions-item label="新旧程度">{{ equipmentData.condition }}</el-descriptions-item>
          <el-descriptions-item label="销售价格">{{ equipmentData.price }} 元</el-descriptions-item>
          <el-descriptions-item label="交易方式">{{ equipmentData.tradeType }}</el-descriptions-item>
          <el-descriptions-item label="所在地区">{{ equipmentData.region }}</el-descriptions-item>
          <el-descriptions-item label="设备描述" :span="2">{{ equipmentData.description }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 招聘求职详情 -->
      <el-card class="info-card" v-if="contentType === 'recruitment'">
        <template #header>
          <div class="card-header">
            <span>招聘求职详情</span>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="职位名称">{{ recruitmentData.position }}</el-descriptions-item>
          <el-descriptions-item label="工作类型">{{ recruitmentData.jobType }}</el-descriptions-item>
          <el-descriptions-item label="薪资范围">{{ recruitmentData.salary }}</el-descriptions-item>
          <el-descriptions-item label="工作地区">{{ recruitmentData.region }}</el-descriptions-item>
          <el-descriptions-item label="招聘人数">{{ recruitmentData.number }} 人</el-descriptions-item>
          <el-descriptions-item label="工作经验">{{ recruitmentData.experience }}</el-descriptions-item>
          <el-descriptions-item label="学历要求">{{ recruitmentData.education }}</el-descriptions-item>
          <el-descriptions-item label="截止日期">{{ recruitmentData.deadline }}</el-descriptions-item>
          <el-descriptions-item label="职位描述" :span="2">{{ recruitmentData.description }}</el-descriptions-item>
          <el-descriptions-item label="任职要求" :span="2">{{ recruitmentData.requirement }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 顶班服务详情 -->
      <el-card class="info-card" v-if="contentType === 'substitution'">
        <template #header>
          <div class="card-header">
            <span>顶班服务详情</span>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="顶班类型">{{ substitutionData.type }}</el-descriptions-item>
          <el-descriptions-item label="工作地区">{{ substitutionData.region }}</el-descriptions-item>
          <el-descriptions-item label="具体地址">{{ substitutionData.address }}</el-descriptions-item>
          <el-descriptions-item label="报酬金额">{{ substitutionData.payment }} 元/天</el-descriptions-item>
          <el-descriptions-item label="开始日期">{{ substitutionData.startDate }}</el-descriptions-item>
          <el-descriptions-item label="结束日期">{{ substitutionData.endDate }}</el-descriptions-item>
          <el-descriptions-item label="工作时间">{{ substitutionData.workTime }}</el-descriptions-item>
          <el-descriptions-item label="联系方式">{{ substitutionData.contactInfo }}</el-descriptions-item>
          <el-descriptions-item label="工作内容" :span="2">{{ substitutionData.description }}</el-descriptions-item>
          <el-descriptions-item label="其他要求" :span="2">{{ substitutionData.requirement }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
      
      <!-- 图片信息区域 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <span>图片信息</span>
          </div>
        </template>
        
        <div class="image-container">
          <div v-if="images.length > 0" class="image-list">
            <div v-for="(image, index) in images" :key="index" class="image-item">
              <el-image 
                :src="image.url" 
                :preview-src-list="images.map(img => img.url)"
                fit="cover"
              >
                <template #error>
                  <div class="image-error">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </template>
              </el-image>
              <div class="image-desc">{{ image.name }}</div>
            </div>
          </div>
          <div v-else class="no-image">暂无图片</div>
        </div>
      </el-card>
      
      <!-- 审核操作区域 -->
      <div class="action-container">
        <el-button type="primary" @click="handleApprove">通过审核</el-button>
        <el-button type="danger" @click="handleReject">拒绝审核</el-button>
      </div>
    </div>
    
    <!-- 拒绝理由选择弹窗 -->
    <el-dialog title="拒绝理由" v-model="rejectDialogVisible" width="500px" append-to-body>
      <el-form ref="rejectForm" :model="rejectForm" label-width="80px">
        <el-form-item label="拒绝理由" prop="reason">
          <el-select v-model="rejectForm.reason" placeholder="请选择拒绝理由" style="width: 100%">
            <el-option v-for="item in reasonOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="补充说明" prop="remark">
          <el-input v-model="rejectForm.remark" type="textarea" placeholder="请输入补充说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitReject">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'AuditDetail',
  data() {
    return {
      // 内容ID
      contentId: null,
      // 内容类型
      contentType: '',
      // 加载状态
      loading: false,
      // 内容类型映射
      contentTypeMap: {
        'transfer': '驿站转让',
        'equipment': '设备交易',
        'recruitment': '招聘求职',
        'substitution': '顶班服务'
      },
      // 通用内容数据
      contentData: {
        id: '',
        title: '',
        publisher: '',
        phone: '',
        createTime: '',
        content: ''
      },
      // 驿站转让数据
      transferData: {
        stationName: '海淀区中关村社区驿站',
        stationType: '社区驿站',
        region: '北京市海淀区',
        address: '北京市海淀区中关村大街123号',
        price: '25000',
        monthlyRevenue: '18000',
        reason: '个人原因，无法继续经营',
        description: '该驿站位于中关村科技园区内，日均快递量200+，客流量大，接手可立即营业。'
      },
      // 设备交易数据
      equipmentData: {
        name: '二手快递柜',
        type: '快递存取设备',
        brand: '丰巢 FC-200',
        useYears: '2',
        condition: '8成新',
        price: '3500',
        tradeType: '自提',
        region: '北京市朝阳区',
        description: '9成新丰巢快递柜，使用两年，无划痕，功能完好，支持扫码存取件。'
      },
      // 招聘求职数据
      recruitmentData: {
        position: '驿站营业员',
        jobType: '全职',
        salary: '4000-5000元/月',
        region: '北京市海淀区',
        number: '2',
        experience: '经验不限',
        education: '高中及以上',
        deadline: '2023-05-30',
        description: '负责日常快递的收发、分拣、客户服务等工作',
        requirement: '形象气质好，有较强的沟通能力和服务意识，能熟练操作电脑。'
      },
      // 顶班服务数据
      substitutionData: {
        type: '临时顶班',
        region: '北京市朝阳区',
        address: '朝阳区望京SOHO T1',
        payment: '200',
        startDate: '2023-04-20',
        endDate: '2023-04-25',
        workTime: '09:00-18:00',
        contactInfo: '13800138000',
        description: '负责快递的接收、分拣和发放，以及简单的客户服务',
        requirement: '有驿站工作经验优先，需要熟悉常用快递APP的操作'
      },
      // 图片列表
      images: [
        { url: 'https://via.placeholder.com/800x600?text=Image1', name: '店面外观' },
        { url: 'https://via.placeholder.com/800x600?text=Image2', name: '内部环境' },
        { url: 'https://via.placeholder.com/800x600?text=Image3', name: '营业执照' }
      ],
      // 拒绝表单
      rejectForm: {
        reason: '',
        remark: ''
      },
      // 拒绝理由选项
      reasonOptions: [
        { label: '内容不真实', value: '内容不真实' },
        { label: '信息不完整', value: '信息不完整' },
        { label: '违反平台规则', value: '违反平台规则' },
        { label: '图片不清晰', value: '图片不清晰' },
        { label: '联系方式错误', value: '联系方式错误' },
        { label: '其他原因', value: '其他原因' }
      ],
      // 拒绝理由弹窗
      rejectDialogVisible: false
    }
  },
  created() {
    this.contentId = this.$route.params.id
    this.contentType = this.$route.params.type
    this.getDetailData()
  },
  methods: {
    /** 获取详情数据 */
    getDetailData() {
      this.loading = true
      
      // TODO: 根据contentId和contentType从API获取详情数据
      // 模拟API调用
      setTimeout(() => {
        // 根据内容类型填充对应数据
        if (this.contentType === 'transfer') {
          this.contentData = {
            id: this.contentId,
            title: '北京市海淀区中关村驿站转让',
            publisher: '张三',
            phone: '13800138001',
            createTime: '2023-04-15 10:30:00',
            content: '由于个人原因，现将经营良好的中关村驿站转让...'
          }
        } else if (this.contentType === 'equipment') {
          this.contentData = {
            id: this.contentId,
            title: '二手快递柜设备出售',
            publisher: '李四',
            phone: '13800138002',
            createTime: '2023-04-15 15:20:00',
            content: '9成新丰巢快递柜，使用两年，功能完好...'
          }
        } else if (this.contentType === 'recruitment') {
          this.contentData = {
            id: this.contentId,
            title: '招聘驿站兼职人员',
            publisher: '王五',
            phone: '13800138003',
            createTime: '2023-04-16 09:15:00',
            content: '招聘驿站兼职人员，工作时间灵活...'
          }
        } else if (this.contentType === 'substitution') {
          this.contentData = {
            id: this.contentId,
            title: '周末顶班服务',
            publisher: '赵六',
            phone: '13800138004',
            createTime: '2023-04-16 11:45:00',
            content: '本周末需要顶班人员，有经验者优先...'
          }
        }
        
        this.loading = false
      }, 1000)
    },
    /** 返回上一页 */
    goBack() {
      this.$router.go(-1)
    },
    /** 通过审核 */
    handleApprove() {
      this.$confirm(`确认通过内容"${this.contentData.title}"的审核?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用审核通过API
        console.log('通过内容ID：', this.contentId)
        
        this.$message({
          type: 'success',
          message: '审核通过成功!'
        })
        
        // 返回审核列表
        setTimeout(() => {
          this.goBack()
        }, 1000)
      }).catch(() => {})
    },
    /** 拒绝审核 */
    handleReject() {
      this.rejectForm = {
        reason: '',
        remark: ''
      }
      this.rejectDialogVisible = true
    },
    /** 提交拒绝 */
    submitReject() {
      if (!this.rejectForm.reason) {
        this.$message({
          type: 'warning',
          message: '请选择拒绝理由'
        })
        return
      }
      
      // TODO: 调用审核拒绝API
      console.log('拒绝内容ID：', this.contentId)
      console.log('拒绝理由：', this.rejectForm.reason)
      console.log('补充说明：', this.rejectForm.remark)
      
      this.rejectDialogVisible = false
      this.$message({
        type: 'success',
        message: '审核拒绝成功!'
      })
      
      // 返回审核列表
      setTimeout(() => {
        this.goBack()
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 20px;
}

.detail-container {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
}

.info-card {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.image-container {
  .image-list {
    display: flex;
    flex-wrap: wrap;
    
    .image-item {
      width: 200px;
      margin-right: 15px;
      margin-bottom: 15px;
      
      .el-image {
        width: 200px;
        height: 150px;
        border-radius: 4px;
      }
      
      .image-desc {
        text-align: center;
        margin-top: 5px;
        color: #606266;
        font-size: 14px;
      }
      
      .image-error {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f7fa;
        color: #909399;
        
        i {
          font-size: 28px;
        }
      }
    }
  }
  
  .no-image {
    height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f7fa;
    color: #909399;
    border-radius: 4px;
  }
}

.action-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  
  .el-button {
    margin: 0 20px;
    min-width: 120px;
  }
}
</style> 