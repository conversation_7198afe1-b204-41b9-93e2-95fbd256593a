<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="filter-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="queryParams.title" placeholder="请输入公告标题" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="类型" prop="noticeType">
          <el-select v-model="queryParams.noticeType" placeholder="请选择公告类型" clearable>
            <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-container">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
        </el-col>
      </el-row>

      <!-- 公告列表 -->
      <el-table v-loading="loading" :data="noticeList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="noticeId" />
        <el-table-column label="标题" align="center" prop="title" min-width="150" show-overflow-tooltip />
        <el-table-column label="公告类型" align="center" prop="noticeType">
          <template #default="scope">
            <el-tag :type="getNoticeTypeTag(scope.row.noticeType)">{{ scope.row.noticeTypeName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="80">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建者" align="center" prop="createBy" width="100" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="total > 0"
        :total="total"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="800px" append-to-body>
      <el-form ref="noticeForm" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="公告标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入公告标题" />
        </el-form-item>
        <el-form-item label="公告类型" prop="noticeType">
          <el-select v-model="form.noticeType" placeholder="请选择公告类型">
            <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="公告内容" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="10" placeholder="请输入公告内容" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">发布</el-radio>
            <el-radio :label="0">草稿</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看公告详情对话框 -->
    <el-dialog title="公告详情" v-model="viewDialog.visible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="公告标题" :span="2">{{ viewInfo.title }}</el-descriptions-item>
        <el-descriptions-item label="公告类型">
          <el-tag :type="getNoticeTypeTag(viewInfo.noticeType)">{{ viewInfo.noticeTypeName }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="viewInfo.status === 1 ? 'success' : 'info'">
            {{ viewInfo.status === 1 ? '已发布' : '草稿' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建者">{{ viewInfo.createBy }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewInfo.createTime }}</el-descriptions-item>
      </el-descriptions>
      <div class="notice-content-box">
        <div class="notice-title">公告内容</div>
        <div class="notice-content">{{ viewInfo.content }}</div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 后续将导入API
// import { listNotice, addNotice, updateNotice, deleteNotice, changeNoticeStatus } from '@/api/content/notice'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  title: '',
  noticeType: '',
  status: ''
})

// 表格数据
const loading = ref(false)
const noticeList = ref([])
const total = ref(0)
const ids = ref([])
const single = computed(() => ids.value.length !== 1)
const multiple = computed(() => ids.value.length === 0)

// 选项数据
const typeOptions = [
  { value: '1', label: '通知' },
  { value: '2', label: '公告' },
  { value: '3', label: '活动' }
]
const statusOptions = [
  { value: '1', label: '已发布' },
  { value: '0', label: '草稿' }
]

// 弹窗参数
const dialog = reactive({
  visible: false,
  title: '',
  type: ''
})
const viewDialog = reactive({
  visible: false
})

// 表单参数
const noticeForm = ref(null)
const form = reactive({
  noticeId: null,
  title: '',
  noticeType: '',
  content: '',
  status: 1
})
const viewInfo = reactive({})

// 表单校验规则
const rules = reactive({
  title: [
    { required: true, message: '标题不能为空', trigger: 'blur' }
  ],
  noticeType: [
    { required: true, message: '公告类型不能为空', trigger: 'change' }
  ],
  content: [
    { required: true, message: '公告内容不能为空', trigger: 'blur' }
  ]
})

// 获取公告类型标签
const getNoticeTypeTag = (type) => {
  switch (type) {
    case '1':
      return 'primary'
    case '2':
      return 'success'
    case '3':
      return 'warning'
    default:
      return ''
  }
}

// 生命周期钩子
onMounted(() => {
  getList()
})

// 获取公告列表
const getList = () => {
  loading.value = true
  // 测试数据，后续替换为API调用
  // listNotice(queryParams).then(response => {
  //   noticeList.value = response.rows
  //   total.value = response.total
  //   loading.value = false
  // })
  
  // 模拟数据
  setTimeout(() => {
    noticeList.value = [
      {
        noticeId: 1,
        title: '驿站帮Pro系统维护通知',
        noticeType: '1',
        noticeTypeName: '通知',
        content: '尊敬的用户：\n\n为了给您提供更优质的服务，我们计划于2023年5月20日凌晨2:00-4:00对系统进行维护升级。维护期间，驿站帮Pro应用将暂停服务，给您带来的不便敬请谅解。\n\n驿站帮Pro团队\n2023年5月15日',
        status: 1,
        createBy: '管理员',
        createTime: '2023-05-15 10:30:00',
        updateBy: '管理员',
        updateTime: '2023-05-15 10:30:00'
      },
      {
        noticeId: 2,
        title: '驿站帮Pro正式上线公告',
        noticeType: '2',
        noticeTypeName: '公告',
        content: '尊敬的用户：\n\n我们很高兴地宣布，驿站帮Pro平台已于2023年5月10日正式上线！驿站帮Pro致力于为用户提供更便捷、高效的驿站服务体验。在新平台上，您可以轻松查找附近驿站、享受更多样化的服务，还可以参与到驿站的灵活就业中来。\n\n感谢您的支持与关注，我们将不断优化平台功能，提升服务质量，为您带来更好的使用体验！\n\n驿站帮Pro团队\n2023年5月10日',
        status: 1,
        createBy: '管理员',
        createTime: '2023-05-10 14:20:00',
        updateBy: '管理员',
        updateTime: '2023-05-10 14:20:00'
      },
      {
        noticeId: 3,
        title: '五一假期驿站服务活动',
        noticeType: '3',
        noticeTypeName: '活动',
        content: '尊敬的用户：\n\n五一假期将至，驿站帮Pro特推出"假日无忧"系列活动：\n\n1. 驿站代收服务：五一期间下单，享8折优惠\n2. 大件物品搬运：五一期间下单，满200减50\n3. 驿站寄存：五一期间新用户首单立减30元\n\n活动时间：2023年4月29日至2023年5月5日\n\n欢迎广大用户积极参与！\n\n驿站帮Pro团队\n2023年4月25日',
        status: 0,
        createBy: '管理员',
        createTime: '2023-04-25 09:15:00',
        updateBy: '管理员',
        updateTime: '2023-04-25 09:15:00'
      }
    ]
    total.value = 3
    loading.value = false
  }, 500)
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.title = ''
  queryParams.noticeType = ''
  queryParams.status = ''
  handleQuery()
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.noticeId)
}

// 查看详情操作
const handleView = (row) => {
  Object.assign(viewInfo, row)
  viewDialog.visible = true
}

// 新增按钮操作
const handleAdd = () => {
  reset()
  dialog.visible = true
  dialog.title = '添加公告'
  dialog.type = 'add'
}

// 修改按钮操作
const handleUpdate = (row) => {
  reset()
  const noticeId = row.noticeId || ids.value[0]
  // 后续替换为API调用获取详情
  // getNotice(noticeId).then(response => {
  //   Object.assign(form, response.data)
  //   dialog.visible = true
  //   dialog.title = '修改公告'
  //   dialog.type = 'edit'
  // })
  
  // 模拟数据
  const selectedRow = noticeList.value.find(item => item.noticeId === noticeId)
  if (selectedRow) {
    Object.assign(form, selectedRow)
    dialog.visible = true
    dialog.title = '修改公告'
    dialog.type = 'edit'
  }
}

// 提交表单
const submitForm = () => {
  noticeForm.value.validate(valid => {
    if (valid) {
      // 获取公告类型名称
      const type = typeOptions.find(item => item.value === form.noticeType)
      form.noticeTypeName = type ? type.label : ''
      
      if (form.noticeId) {
        // updateNotice(form).then(response => {
        //   ElMessage.success('修改成功')
        //   dialog.visible = false
        //   getList()
        // })
        ElMessage.success('修改成功')
        dialog.visible = false
        getList()
      } else {
        // addNotice(form).then(response => {
        //   ElMessage.success('新增成功')
        //   dialog.visible = false
        //   getList()
        // })
        ElMessage.success('新增成功')
        dialog.visible = false
        getList()
      }
    }
  })
}

// 删除按钮操作
const handleDelete = (row) => {
  const noticeIds = row.noticeId || ids.value
  ElMessageBox.confirm('是否确认删除所选公告?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // deleteNotice(noticeIds).then(() => {
    //   ElMessage.success('删除成功')
    //   getList()
    // })
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {})
}

// 状态修改
const handleStatusChange = (row) => {
  // changeNoticeStatus({ noticeId: row.noticeId, status: row.status }).then(() => {
  //   ElMessage.success(`${row.status === 1 ? '发布' : '设为草稿'}成功`)
  // }).catch(() => {
  //   row.status = row.status === 1 ? 0 : 1
  // })
  ElMessage.success(`${row.status === 1 ? '发布' : '设为草稿'}成功`)
}

// 表单重置
const reset = () => {
  form.noticeId = null
  form.title = ''
  form.noticeType = ''
  form.content = ''
  form.status = 1
}

// 取消按钮
const cancel = () => {
  dialog.visible = false
  reset()
}

// 分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.notice-content-box {
  margin-top: 20px;
  padding: 0 20px;
  
  .notice-title {
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
  }
  
  .notice-content {
    white-space: pre-line;
    line-height: 1.6;
    padding: 10px 0;
  }
}
</style> 