<template>
  <div class="page-container">
    <div class="action-container">
      <div class="search-box">
        <el-input v-model="queryParams.keyword" placeholder="请输入内容标题/发布人" clearable style="width: 200px" class="mr-10" />
        <el-select v-model="queryParams.type" placeholder="内容类型" clearable class="mr-10" style="width: 120px">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="queryParams.status" placeholder="上架状态" clearable class="mr-10" style="width: 120px">
          <el-option label="已上架" value="1" />
          <el-option label="已下架" value="0" />
          <el-option label="违规下架" value="2" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="mr-10"
          style="width: 240px"
        />
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </div>
      <div class="button-group">
        <el-button type="success" @click="handleBatchPublish">批量上架</el-button>
        <el-button type="warning" @click="handleBatchUnpublish">批量下架</el-button>
        <el-button type="danger" @click="handleBatchViolation">批量违规下架</el-button>
      </div>
    </div>
    
    <el-table v-loading="loading" :data="contentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" label="#" width="50" />
      <el-table-column prop="title" label="内容标题" min-width="160" show-overflow-tooltip />
      <el-table-column prop="contentType" label="内容类型" width="100">
        <template #default="scope">
          <el-tag :type="getTypeTag(scope.row.contentType)">
            {{ contentTypeMap[scope.row.contentType] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="publisher" label="发布人" width="100" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === '1' ? 'success' : scope.row.status === '2' ? 'danger' : 'info'">
            {{ statusMap[scope.row.status] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="auditTime" label="审核时间" width="160" sortable />
      <el-table-column prop="publishTime" label="上架时间" width="160" sortable>
        <template #default="scope">
          {{ scope.row.status === '1' ? scope.row.publishTime : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="unpublishReason" label="下架原因" min-width="150" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.status !== '1' ? scope.row.unpublishReason || '-' : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button type="text" @click="handleView(scope.row)">查看</el-button>
          <el-button v-if="scope.row.status !== '1'" type="text" @click="handlePublish(scope.row)">上架</el-button>
          <el-button v-if="scope.row.status === '1'" type="text" @click="handleUnpublish(scope.row)">下架</el-button>
          <el-button v-if="scope.row.status !== '2'" type="text" @click="handleViolation(scope.row)">违规</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 违规原因选择弹窗 -->
    <el-dialog title="违规原因" v-model="violationDialogVisible" width="500px" append-to-body>
      <el-form ref="violationForm" :model="violationForm" label-width="80px">
        <el-form-item label="违规类型" prop="reason">
          <el-select v-model="violationForm.reason" placeholder="请选择违规类型" style="width: 100%">
            <el-option v-for="item in violationOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="补充说明" prop="remark">
          <el-input v-model="violationForm.remark" type="textarea" placeholder="请输入补充说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="violationDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitViolation">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 下架原因选择弹窗 -->
    <el-dialog title="下架原因" v-model="unpublishDialogVisible" width="500px" append-to-body>
      <el-form ref="unpublishForm" :model="unpublishForm" label-width="80px">
        <el-form-item label="下架原因" prop="reason">
          <el-select v-model="unpublishForm.reason" placeholder="请选择下架原因" style="width: 100%">
            <el-option v-for="item in unpublishOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="补充说明" prop="remark">
          <el-input v-model="unpublishForm.remark" type="textarea" placeholder="请输入补充说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="unpublishDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitUnpublish">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 统计信息 -->
    <el-row :gutter="20" class="statistics-container">
      <el-col :span="8">
        <el-card class="statistic-card">
          <div class="statistic-title">内容违规统计</div>
          <el-divider />
          <div class="statistic-chart" id="violationChart"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="statistic-card">
          <div class="statistic-title">内容类型分布</div>
          <el-divider />
          <div class="statistic-chart" id="typeChart"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="statistic-card">
          <div class="statistic-title">日内容发布趋势</div>
          <el-divider />
          <div class="statistic-chart" id="trendChart"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
// 引入 echarts
import * as echarts from 'echarts'

export default {
  name: 'ContentManagement',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      selectedIds: [],
      // 总条数
      total: 0,
      // 日期范围
      dateRange: [],
      // 内容类型映射
      contentTypeMap: {
        'transfer': '驿站转让',
        'equipment': '设备交易',
        'recruitment': '招聘求职',
        'substitution': '顶班服务'
      },
      // 状态映射
      statusMap: {
        '0': '已下架',
        '1': '已上架',
        '2': '违规下架'
      },
      // 内容类型选项
      typeOptions: [
        { label: '驿站转让', value: 'transfer' },
        { label: '设备交易', value: 'equipment' },
        { label: '招聘求职', value: 'recruitment' },
        { label: '顶班服务', value: 'substitution' }
      ],
      // 违规原因选项
      violationOptions: [
        { label: '虚假信息', value: '虚假信息' },
        { label: '重复发布', value: '重复发布' },
        { label: '违法内容', value: '违法内容' },
        { label: '侵犯他人权益', value: '侵犯他人权益' },
        { label: '其他违规', value: '其他违规' }
      ],
      // 下架原因选项
      unpublishOptions: [
        { label: '内容过期', value: '内容过期' },
        { label: '应发布者要求', value: '应发布者要求' },
        { label: '交易已完成', value: '交易已完成' },
        { label: '其他原因', value: '其他原因' }
      ],
      // 内容列表
      contentList: [
        {
          id: 1,
          title: '北京市海淀区中关村驿站转让',
          contentType: 'transfer',
          publisher: '张三',
          status: '1',
          auditTime: '2023-04-15 10:30:00',
          publishTime: '2023-04-15 10:35:00',
          unpublishReason: ''
        },
        {
          id: 2,
          title: '二手快递柜设备出售',
          contentType: 'equipment',
          publisher: '李四',
          status: '1',
          auditTime: '2023-04-15 15:20:00',
          publishTime: '2023-04-15 15:25:00',
          unpublishReason: ''
        },
        {
          id: 3,
          title: '招聘驿站兼职人员',
          contentType: 'recruitment',
          publisher: '王五',
          status: '0',
          auditTime: '2023-04-16 09:15:00',
          publishTime: '2023-04-16 09:20:00',
          unpublishReason: '内容过期'
        },
        {
          id: 4,
          title: '周末顶班服务',
          contentType: 'substitution',
          publisher: '赵六',
          status: '2',
          auditTime: '2023-04-16 11:45:00',
          publishTime: '2023-04-16 11:50:00',
          unpublishReason: '虚假信息'
        }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        type: '',
        status: '',
        startDate: '',
        endDate: ''
      },
      // 违规表单
      violationForm: {
        id: undefined,
        reason: '',
        remark: ''
      },
      // 下架表单
      unpublishForm: {
        id: undefined,
        reason: '',
        remark: ''
      },
      // 违规弹窗
      violationDialogVisible: false,
      // 下架弹窗
      unpublishDialogVisible: false,
      // 当前操作的内容
      currentContent: {},
      // 图表实例
      violationChart: null,
      typeChart: null,
      trendChart: null
    }
  },
  mounted() {
    // 初始化图表
    this.initCharts()
  },
  methods: {
    /** 获取内容类型标签样式 */
    getTypeTag(type) {
      const typeMap = {
        'transfer': 'primary',
        'equipment': 'success',
        'recruitment': 'warning',
        'substitution': 'info'
      }
      return typeMap[type] || ''
    },
    /** 查询列表 */
    handleQuery() {
      this.queryParams.pageNum = 1
      if (this.dateRange && this.dateRange.length > 0) {
        this.queryParams.startDate = this.dateRange[0]
        this.queryParams.endDate = this.dateRange[1]
      } else {
        this.queryParams.startDate = ''
        this.queryParams.endDate = ''
      }
      // TODO: 实际应该调用API获取数据
      console.log('查询参数：', this.queryParams)
    },
    /** 重置查询 */
    resetQuery() {
      this.dateRange = []
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        type: '',
        status: '',
        startDate: '',
        endDate: ''
      }
      this.handleQuery()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
    },
    /** 查看详情 */
    handleView(row) {
      // 跳转到详情页
      this.$router.push(`/content/audit/detail/${row.id}/${row.contentType}`)
    },
    /** 上架 */
    handlePublish(row) {
      this.$confirm(`确认上架内容"${row.title}"?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用上架API
        console.log('上架内容ID：', row.id)
        this.$message({
          type: 'success',
          message: '上架成功!'
        })
      }).catch(() => {})
    },
    /** 下架 */
    handleUnpublish(row) {
      this.currentContent = row
      this.unpublishForm = {
        id: row.id,
        reason: '',
        remark: ''
      }
      this.unpublishDialogVisible = true
    },
    /** 违规下架 */
    handleViolation(row) {
      this.currentContent = row
      this.violationForm = {
        id: row.id,
        reason: '',
        remark: ''
      }
      this.violationDialogVisible = true
    },
    /** 提交违规下架 */
    submitViolation() {
      if (!this.violationForm.reason) {
        this.$message({
          type: 'warning',
          message: '请选择违规类型'
        })
        return
      }
      
      // TODO: 调用违规下架API
      console.log('违规下架内容ID：', this.violationForm.id)
      console.log('违规类型：', this.violationForm.reason)
      console.log('补充说明：', this.violationForm.remark)
      
      this.violationDialogVisible = false
      this.$message({
        type: 'success',
        message: '违规下架成功!'
      })
    },
    /** 提交下架 */
    submitUnpublish() {
      if (!this.unpublishForm.reason) {
        this.$message({
          type: 'warning',
          message: '请选择下架原因'
        })
        return
      }
      
      // TODO: 调用下架API
      console.log('下架内容ID：', this.unpublishForm.id)
      console.log('下架原因：', this.unpublishForm.reason)
      console.log('补充说明：', this.unpublishForm.remark)
      
      this.unpublishDialogVisible = false
      this.$message({
        type: 'success',
        message: '下架成功!'
      })
    },
    /** 批量上架 */
    handleBatchPublish() {
      if (this.selectedIds.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条记录'
        })
        return
      }
      
      this.$confirm(`确认批量上架所选的${this.selectedIds.length}条内容?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用批量上架API
        console.log('批量上架内容IDs：', this.selectedIds)
        this.$message({
          type: 'success',
          message: '批量上架成功!'
        })
      }).catch(() => {})
    },
    /** 批量下架 */
    handleBatchUnpublish() {
      if (this.selectedIds.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条记录'
        })
        return
      }
      
      this.unpublishForm = {
        id: this.selectedIds.join(','),
        reason: '',
        remark: ''
      }
      this.unpublishDialogVisible = true
    },
    /** 批量违规下架 */
    handleBatchViolation() {
      if (this.selectedIds.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条记录'
        })
        return
      }
      
      this.violationForm = {
        id: this.selectedIds.join(','),
        reason: '',
        remark: ''
      }
      this.violationDialogVisible = true
    },
    /** 分页大小变化 */
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.handleQuery()
    },
    /** 分页页码变化 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.handleQuery()
    },
    /** 初始化图表 */
    initCharts() {
      this.$nextTick(() => {
        // 违规类型统计图表
        this.violationChart = echarts.init(document.getElementById('violationChart'))
        this.violationChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            data: ['虚假信息', '重复发布', '违法内容', '侵犯他人权益', '其他违规']
          },
          series: [
            {
              name: '违规类型',
              type: 'pie',
              radius: '50%',
              data: [
                { value: 48, name: '虚假信息' },
                { value: 25, name: '重复发布' },
                { value: 12, name: '违法内容' },
                { value: 8, name: '侵犯他人权益' },
                { value: 15, name: '其他违规' }
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        })
        
        // 内容类型分布图表
        this.typeChart = echarts.init(document.getElementById('typeChart'))
        this.typeChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            data: ['驿站转让', '设备交易', '招聘求职', '顶班服务']
          },
          series: [
            {
              name: '内容类型',
              type: 'pie',
              radius: '50%',
              data: [
                { value: 35, name: '驿站转让' },
                { value: 42, name: '设备交易' },
                { value: 78, name: '招聘求职' },
                { value: 56, name: '顶班服务' }
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        })
        
        // 内容发布趋势图表
        this.trendChart = echarts.init(document.getElementById('trendChart'))
        this.trendChart.setOption({
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              data: [18, 25, 32, 28, 35, 42, 30],
              type: 'line',
              smooth: true
            }
          ]
        })
        
        // 窗口大小变化时，重新调整图表大小
        window.addEventListener('resize', () => {
          this.violationChart.resize()
          this.typeChart.resize()
          this.trendChart.resize()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.action-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .search-box {
    display: flex;
    align-items: center;
  }
  
  .button-group {
    display: flex;
    align-items: center;
  }
}

.mr-10 {
  margin-right: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.statistics-container {
  margin-top: 30px;
  
  .statistic-card {
    margin-bottom: 20px;
    
    .statistic-title {
      font-size: 16px;
      font-weight: bold;
      text-align: center;
    }
    
    .statistic-chart {
      height: 300px;
    }
  }
}
</style> 