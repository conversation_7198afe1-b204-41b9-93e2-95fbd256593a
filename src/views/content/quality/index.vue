<template>
  <div class="page-container">
    <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <!-- 质量评分标准设置 -->
      <el-tab-pane label="质量评分标准" name="standard">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>内容质量评分标准设置</span>
              <el-button type="primary" @click="saveStandard">保存设置</el-button>
            </div>
          </template>
          
          <el-alert
            title="设置不同内容类型的质量评分标准，系统将根据标准对内容进行自动评分"
            type="info"
            show-icon
            :closable="false"
            class="mb-20"
          />
          
          <div v-for="(item, index) in qualityStandards" :key="index" class="standard-item">
            <div class="standard-header">
              <h3>{{ contentTypeMap[item.type] }}质量评分标准</h3>
            </div>
            
            <el-form :model="item" label-width="120px">
              <el-form-item v-for="(factor, fIndex) in item.factors" :key="fIndex" :label="factor.name">
                <el-slider 
                  v-model="factor.weight" 
                  :min="0" 
                  :max="100" 
                  :format-tooltip="formatTooltip"
                  class="weight-slider"
                />
              </el-form-item>
              
              <el-form-item label="其他因素">
                <el-input v-model="item.otherFactors" type="textarea" :rows="2" placeholder="请输入其他考虑因素" />
              </el-form-item>
            </el-form>
            
            <el-divider v-if="index < qualityStandards.length - 1" />
          </div>
        </el-card>
      </el-tab-pane>
      
      <!-- 内容质量评分 -->
      <el-tab-pane label="内容质量评分" name="score">
        <div class="action-container">
          <div class="search-box">
            <el-input v-model="queryParams.keyword" placeholder="请输入内容标题/发布人" clearable style="width: 200px" class="mr-10" />
            <el-select v-model="queryParams.type" placeholder="内容类型" clearable class="mr-10" style="width: 120px">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select v-model="queryParams.score" placeholder="评分等级" clearable class="mr-10" style="width: 120px">
              <el-option label="优质内容" value="high" />
              <el-option label="普通内容" value="medium" />
              <el-option label="低质内容" value="low" />
            </el-select>
            <el-button type="primary" @click="handleQuery">搜索</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </div>
          <div class="button-group">
            <el-button type="primary" @click="handleBatchQuality">批量评分</el-button>
          </div>
        </div>
        
        <el-table v-loading="loading" :data="contentList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="title" label="内容标题" min-width="160" show-overflow-tooltip />
          <el-table-column prop="contentType" label="内容类型" width="100">
            <template #default="scope">
              <el-tag :type="getTypeTag(scope.row.contentType)">
                {{ contentTypeMap[scope.row.contentType] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="publisher" label="发布人" width="100" />
          <el-table-column prop="score" label="质量评分" width="120">
            <template #default="scope">
              <div class="score-cell">
                <el-progress :percentage="scope.row.score" :color="getScoreColor(scope.row.score)" />
                <span>{{ scope.row.score }}分</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="qualityLevel" label="质量等级" width="100">
            <template #default="scope">
              <el-tag :type="getQualityTag(scope.row.score)">
                {{ getQualityLevel(scope.row.score) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="发布时间" width="160" sortable />
          <el-table-column prop="viewCount" label="查看次数" width="100" sortable />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)">查看</el-button>
              <el-button type="text" @click="handleManualScore(scope.row)">评分</el-button>
              <el-button v-if="scope.row.score >= 85" type="text" @click="handleRecommend(scope.row)">推荐</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-tab-pane>
      
      <!-- 优质内容推荐 -->
      <el-tab-pane label="优质内容推荐" name="recommend">
        <div class="action-container">
          <div class="search-box">
            <el-input v-model="recommendQuery.keyword" placeholder="请输入内容标题/发布人" clearable style="width: 200px" class="mr-10" />
            <el-select v-model="recommendQuery.type" placeholder="内容类型" clearable class="mr-10" style="width: 120px">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select v-model="recommendQuery.position" placeholder="推荐位置" clearable class="mr-10" style="width: 120px">
              <el-option label="首页推荐" value="home" />
              <el-option label="分类页推荐" value="category" />
              <el-option label="搜索页推荐" value="search" />
            </el-select>
            <el-button type="primary" @click="handleRecommendQuery">搜索</el-button>
            <el-button @click="resetRecommendQuery">重置</el-button>
          </div>
          <div class="button-group">
            <el-button type="primary" @click="handleBatchRecommend">批量推荐</el-button>
            <el-button type="danger" @click="handleBatchCancelRecommend">批量取消推荐</el-button>
          </div>
        </div>
        
        <el-table v-loading="recommendLoading" :data="recommendList" @selection-change="handleRecommendSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="title" label="内容标题" min-width="160" show-overflow-tooltip />
          <el-table-column prop="contentType" label="内容类型" width="100">
            <template #default="scope">
              <el-tag :type="getTypeTag(scope.row.contentType)">
                {{ contentTypeMap[scope.row.contentType] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="publisher" label="发布人" width="100" />
          <el-table-column prop="score" label="质量评分" width="100">
            <template #default="scope">
              {{ scope.row.score }}分
            </template>
          </el-table-column>
          <el-table-column prop="recommendPosition" label="推荐位置" width="120">
            <template #default="scope">
              <el-tag type="success" v-if="scope.row.recommendPosition.includes('home')">首页</el-tag>
              <el-tag type="info" class="ml-5" v-if="scope.row.recommendPosition.includes('category')">分类页</el-tag>
              <el-tag type="warning" class="ml-5" v-if="scope.row.recommendPosition.includes('search')">搜索页</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="recommendTime" label="推荐时间" width="160" sortable />
          <el-table-column prop="recommendOrder" label="推荐顺序" width="100" sortable />
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)">查看</el-button>
              <el-button type="text" @click="handleRecommendSetting(scope.row)">推荐设置</el-button>
              <el-button type="text" @click="handleCancelRecommend(scope.row)">取消推荐</el-button>
              <el-button type="text" @click="handleMoveUp(scope.row)">上移</el-button>
              <el-button type="text" @click="handleMoveDown(scope.row)">下移</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="recommendQuery.pageNum"
            v-model:page-size="recommendQuery.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="recommendTotal"
            @size-change="handleRecommendSizeChange"
            @current-change="handleRecommendCurrentChange"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 手动评分弹窗 -->
    <el-dialog title="内容质量评分" v-model="scoreDialogVisible" width="550px" append-to-body>
      <div class="score-dialog-header">
        <div class="title">{{ currentContent.title }}</div>
        <div class="sub-title">
          <el-tag :type="getTypeTag(currentContent.contentType)">
            {{ contentTypeMap[currentContent.contentType] }}
          </el-tag>
          <span class="ml-10">发布人：{{ currentContent.publisher }}</span>
        </div>
      </div>
      
      <el-form ref="scoreForm" :model="scoreForm" label-width="120px">
        <el-form-item v-for="(factor, index) in scoreFactors" :key="index" :label="factor.name">
          <el-rate
            v-model="factor.score"
            :colors="['#F56C6C', '#E6A23C', '#67C23A']"
            :max="5"
            show-score
            score-template="{value}"
          />
        </el-form-item>
        
        <el-form-item label="评分说明">
          <el-input v-model="scoreForm.remark" type="textarea" :rows="3" placeholder="请输入评分说明" />
        </el-form-item>
        
        <el-divider />
        
        <div class="score-result">
          <div class="score-title">综合评分</div>
          <div class="score-value">{{ calculateTotalScore() }}分</div>
          <el-progress :percentage="calculateTotalScore()" :color="getScoreColor(calculateTotalScore())" />
          <div class="score-level">
            <el-tag :type="getQualityTag(calculateTotalScore())">
              {{ getQualityLevel(calculateTotalScore()) }}
            </el-tag>
          </div>
        </div>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="scoreDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitScore">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 推荐设置弹窗 -->
    <el-dialog title="推荐设置" v-model="recommendDialogVisible" width="500px" append-to-body>
      <el-form ref="recommendForm" :model="recommendForm" label-width="100px">
        <el-form-item label="推荐位置" prop="positions">
          <el-checkbox-group v-model="recommendForm.positions">
            <el-checkbox label="home">首页推荐</el-checkbox>
            <el-checkbox label="category">分类页推荐</el-checkbox>
            <el-checkbox label="search">搜索页推荐</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="推荐顺序" prop="order">
          <el-input-number v-model="recommendForm.order" :min="1" :max="99" controls-position="right" />
          <span class="form-tip">数字越小排序越靠前</span>
        </el-form-item>
        <el-form-item label="推荐理由" prop="reason">
          <el-input v-model="recommendForm.reason" type="textarea" :rows="3" placeholder="请输入推荐理由" />
        </el-form-item>
        <el-form-item label="展示时间" prop="dateRange">
          <el-date-picker
            v-model="recommendForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="recommendDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitRecommend">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ContentQuality',
  data() {
    return {
      // 激活的选项卡
      activeName: 'standard',
      // 遮罩层
      loading: false,
      recommendLoading: false,
      // 选中数组
      selectedIds: [],
      recommendSelectedIds: [],
      // 总条数
      total: 0,
      recommendTotal: 0,
      // 内容类型映射
      contentTypeMap: {
        'transfer': '驿站转让',
        'equipment': '设备交易',
        'recruitment': '招聘求职',
        'substitution': '顶班服务'
      },
      // 内容类型选项
      typeOptions: [
        { label: '驿站转让', value: 'transfer' },
        { label: '设备交易', value: 'equipment' },
        { label: '招聘求职', value: 'recruitment' },
        { label: '顶班服务', value: 'substitution' }
      ],
      // 质量评分标准设置
      qualityStandards: [
        {
          type: 'transfer',
          factors: [
            { name: '内容完整度', weight: 25 },
            { name: '信息真实性', weight: 30 },
            { name: '图片质量', weight: 15 },
            { name: '价格合理性', weight: 20 },
            { name: '描述详细度', weight: 10 }
          ],
          otherFactors: '交易安全、地理位置、客流量等'
        },
        {
          type: 'equipment',
          factors: [
            { name: '设备描述', weight: 20 },
            { name: '图片质量', weight: 25 },
            { name: '价格合理性', weight: 20 },
            { name: '设备新旧程度', weight: 15 },
            { name: '联系方式有效性', weight: 20 }
          ],
          otherFactors: '品牌信息、配件完整性、保修情况等'
        },
        {
          type: 'recruitment',
          factors: [
            { name: '职位描述', weight: 25 },
            { name: '薪资合理性', weight: 20 },
            { name: '企业信息', weight: 15 },
            { name: '联系方式有效性', weight: 20 },
            { name: '工作要求明确度', weight: 20 }
          ],
          otherFactors: '工作环境、福利待遇、晋升空间等'
        },
        {
          type: 'substitution',
          factors: [
            { name: '服务描述', weight: 25 },
            { name: '时间安排', weight: 20 },
            { name: '报酬合理性', weight: 20 },
            { name: '联系方式有效性', weight: 20 },
            { name: '工作要求明确度', weight: 15 }
          ],
          otherFactors: '服务地点、工作内容、经验要求等'
        }
      ],
      // 内容列表
      contentList: [
        {
          id: 1,
          title: '北京市海淀区中关村驿站转让',
          contentType: 'transfer',
          publisher: '张三',
          score: 92,
          createTime: '2023-04-15 10:30:00',
          viewCount: 156
        },
        {
          id: 2,
          title: '二手快递柜设备出售',
          contentType: 'equipment',
          publisher: '李四',
          score: 85,
          createTime: '2023-04-15 15:20:00',
          viewCount: 98
        },
        {
          id: 3,
          title: '招聘驿站兼职人员',
          contentType: 'recruitment',
          publisher: '王五',
          score: 78,
          createTime: '2023-04-16 09:15:00',
          viewCount: 112
        },
        {
          id: 4,
          title: '周末顶班服务',
          contentType: 'substitution',
          publisher: '赵六',
          score: 62,
          createTime: '2023-04-16 11:45:00',
          viewCount: 45
        }
      ],
      // 推荐内容列表
      recommendList: [
        {
          id: 1,
          title: '北京市海淀区中关村驿站转让',
          contentType: 'transfer',
          publisher: '张三',
          score: 92,
          recommendPosition: ['home', 'category'],
          recommendTime: '2023-04-15 14:30:00',
          recommendOrder: 1
        },
        {
          id: 2,
          title: '二手快递柜设备出售',
          contentType: 'equipment',
          publisher: '李四',
          score: 85,
          recommendPosition: ['category', 'search'],
          recommendTime: '2023-04-15 16:20:00',
          recommendOrder: 2
        }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        type: '',
        score: '',
        startDate: '',
        endDate: ''
      },
      // 推荐查询参数
      recommendQuery: {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        type: '',
        position: ''
      },
      // 评分弹窗
      scoreDialogVisible: false,
      // 推荐弹窗
      recommendDialogVisible: false,
      // 当前操作的内容
      currentContent: {},
      // 评分表单
      scoreForm: {
        id: undefined,
        remark: ''
      },
      // 评分因素
      scoreFactors: [],
      // 推荐表单
      recommendForm: {
        id: undefined,
        positions: [],
        order: 1,
        reason: '',
        dateRange: []
      }
    }
  },
  methods: {
    /** 获取内容类型标签样式 */
    getTypeTag(type) {
      const typeMap = {
        'transfer': 'primary',
        'equipment': 'success',
        'recruitment': 'warning',
        'substitution': 'info'
      }
      return typeMap[type] || ''
    },
    /** 获取分数颜色 */
    getScoreColor(score) {
      if (score >= 85) {
        return '#67C23A' // 绿色，优质
      } else if (score >= 60) {
        return '#E6A23C' // 黄色，普通
      } else {
        return '#F56C6C' // 红色，低质
      }
    },
    /** 获取质量等级标签 */
    getQualityTag(score) {
      if (score >= 85) {
        return 'success' // 优质
      } else if (score >= 60) {
        return 'warning' // 普通
      } else {
        return 'danger' // 低质
      }
    },
    /** 获取质量等级描述 */
    getQualityLevel(score) {
      if (score >= 85) {
        return '优质内容'
      } else if (score >= 60) {
        return '普通内容'
      } else {
        return '低质内容'
      }
    },
    /** 格式化评分标准的提示文本 */
    formatTooltip(val) {
      return val + '%'
    },
    /** 切换选项卡 */
    handleTabClick() {
      if (this.activeName === 'score') {
        this.loading = true
        // TODO: 调用API获取内容质量评分数据
        setTimeout(() => {
          this.loading = false
        }, 500)
      } else if (this.activeName === 'recommend') {
        this.recommendLoading = true
        // TODO: 调用API获取推荐内容数据
        setTimeout(() => {
          this.recommendLoading = false
        }, 500)
      }
    },
    /** 保存评分标准 */
    saveStandard() {
      // TODO: 调用API保存评分标准
      this.$message({
        type: 'success',
        message: '评分标准保存成功!'
      })
    },
    /** 查询内容列表 */
    handleQuery() {
      // TODO: 调用API获取内容列表
      console.log('查询参数：', this.queryParams)
    },
    /** 重置查询 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        type: '',
        score: '',
        startDate: '',
        endDate: ''
      }
      this.handleQuery()
    },
    /** 查询推荐内容列表 */
    handleRecommendQuery() {
      // TODO: 调用API获取推荐内容列表
      console.log('推荐查询参数：', this.recommendQuery)
    },
    /** 重置推荐查询 */
    resetRecommendQuery() {
      this.recommendQuery = {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        type: '',
        position: ''
      }
      this.handleRecommendQuery()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
    },
    /** 推荐多选框选中数据 */
    handleRecommendSelectionChange(selection) {
      this.recommendSelectedIds = selection.map(item => item.id)
    },
    /** 查看详情 */
    handleView(row) {
      // 跳转到详情页
      this.$router.push(`/content/audit/detail/${row.id}/${row.contentType}`)
    },
    /** 手动评分 */
    handleManualScore(row) {
      this.currentContent = row
      
      // 根据内容类型获取评分因素
      const factorConfig = this.qualityStandards.find(item => item.type === row.contentType)
      if (factorConfig) {
        this.scoreFactors = factorConfig.factors.map(factor => ({
          name: factor.name,
          weight: factor.weight,
          score: 3 // 默认3分
        }))
      }
      
      this.scoreForm = {
        id: row.id,
        remark: ''
      }
      this.scoreDialogVisible = true
    },
    /** 计算总评分 */
    calculateTotalScore() {
      if (!this.scoreFactors || this.scoreFactors.length === 0) {
        return 0
      }
      
      let totalWeight = 0
      let weightedScore = 0
      
      this.scoreFactors.forEach(factor => {
        totalWeight += factor.weight
        weightedScore += (factor.score * 20) * (factor.weight / 100) // 5分制转换为100分制
      })
      
      return totalWeight > 0 ? Math.round(weightedScore / (totalWeight / 100)) : 0
    },
    /** 提交评分 */
    submitScore() {
      const totalScore = this.calculateTotalScore()
      
      // TODO: 调用API提交评分
      console.log('内容ID：', this.scoreForm.id)
      console.log('评分因素：', this.scoreFactors)
      console.log('总评分：', totalScore)
      console.log('评分说明：', this.scoreForm.remark)
      
      this.scoreDialogVisible = false
      this.$message({
        type: 'success',
        message: '评分提交成功!'
      })
    },
    /** 批量评分 */
    handleBatchQuality() {
      if (this.selectedIds.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条记录'
        })
        return
      }
      
      this.$confirm(`确认对所选的${this.selectedIds.length}条内容进行自动评分?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用批量评分API
        console.log('批量评分内容IDs：', this.selectedIds)
        this.$message({
          type: 'success',
          message: '批量评分成功!'
        })
      }).catch(() => {})
    },
    /** 推荐内容 */
    handleRecommend(row) {
      this.currentContent = row
      this.recommendForm = {
        id: row.id,
        positions: ['home'],
        order: 1,
        reason: `优质${this.contentTypeMap[row.contentType]}内容，评分${row.score}分`,
        dateRange: [new Date(), new Date(new Date().setDate(new Date().getDate() + 7))] // 默认7天
      }
      this.recommendDialogVisible = true
    },
    /** 提交推荐 */
    submitRecommend() {
      if (this.recommendForm.positions.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一个推荐位置'
        })
        return
      }
      
      // TODO: 调用API提交推荐
      console.log('内容ID：', this.recommendForm.id)
      console.log('推荐位置：', this.recommendForm.positions)
      console.log('推荐顺序：', this.recommendForm.order)
      console.log('推荐理由：', this.recommendForm.reason)
      console.log('展示时间：', this.recommendForm.dateRange)
      
      this.recommendDialogVisible = false
      this.$message({
        type: 'success',
        message: '内容推荐成功!'
      })
    },
    /** 批量推荐 */
    handleBatchRecommend() {
      if (this.selectedIds.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条记录'
        })
        return
      }
      
      this.recommendForm = {
        id: this.selectedIds.join(','),
        positions: ['home'],
        order: 1,
        reason: '批量推荐优质内容',
        dateRange: [new Date(), new Date(new Date().setDate(new Date().getDate() + 7))] // 默认7天
      }
      this.recommendDialogVisible = true
    },
    /** 推荐设置 */
    handleRecommendSetting(row) {
      this.currentContent = row
      this.recommendForm = {
        id: row.id,
        positions: row.recommendPosition,
        order: row.recommendOrder,
        reason: '修改推荐设置',
        dateRange: [new Date(), new Date(new Date().setDate(new Date().getDate() + 7))] // 默认7天
      }
      this.recommendDialogVisible = true
    },
    /** 取消推荐 */
    handleCancelRecommend(row) {
      this.$confirm(`确认取消内容"${row.title}"的推荐?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用取消推荐API
        console.log('取消推荐内容ID：', row.id)
        this.$message({
          type: 'success',
          message: '取消推荐成功!'
        })
      }).catch(() => {})
    },
    /** 批量取消推荐 */
    handleBatchCancelRecommend() {
      if (this.recommendSelectedIds.length === 0) {
        this.$message({
          type: 'warning',
          message: '请至少选择一条记录'
        })
        return
      }
      
      this.$confirm(`确认批量取消所选的${this.recommendSelectedIds.length}条内容的推荐?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用批量取消推荐API
        console.log('批量取消推荐内容IDs：', this.recommendSelectedIds)
        this.$message({
          type: 'success',
          message: '批量取消推荐成功!'
        })
      }).catch(() => {})
    },
    /** 上移推荐顺序 */
    handleMoveUp(row) {
      if (row.recommendOrder <= 1) {
        this.$message({
          type: 'warning',
          message: '已经是第一位，无法上移'
        })
        return
      }
      
      // TODO: 调用上移API
      console.log('上移内容ID：', row.id)
      this.$message({
        type: 'success',
        message: '上移成功!'
      })
    },
    /** 下移推荐顺序 */
    handleMoveDown(row) {
      // TODO: 调用下移API
      console.log('下移内容ID：', row.id)
      this.$message({
        type: 'success',
        message: '下移成功!'
      })
    },
    /** 分页大小变化 */
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.handleQuery()
    },
    /** 分页页码变化 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.handleQuery()
    },
    /** 推荐分页大小变化 */
    handleRecommendSizeChange(val) {
      this.recommendQuery.pageSize = val
      this.handleRecommendQuery()
    },
    /** 推荐分页页码变化 */
    handleRecommendCurrentChange(val) {
      this.recommendQuery.pageNum = val
      this.handleRecommendQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
.action-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .search-box {
    display: flex;
    align-items: center;
  }
  
  .button-group {
    display: flex;
    align-items: center;
  }
}

.mr-10 {
  margin-right: 10px;
}

.ml-5 {
  margin-left: 5px;
}

.ml-10 {
  margin-left: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.standard-item {
  margin-bottom: 20px;
  
  .standard-header {
    margin-bottom: 15px;
    
    h3 {
      margin: 0;
      font-size: 16px;
      color: #303133;
    }
  }
  
  .weight-slider {
    width: 80%;
  }
}

.score-cell {
  display: flex;
  flex-direction: column;
  
  span {
    margin-top: 5px;
    font-size: 14px;
    color: #606266;
  }
}

.score-dialog-header {
  margin-bottom: 20px;
  
  .title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 8px;
  }
  
  .sub-title {
    color: #606266;
  }
}

.score-result {
  text-align: center;
  padding: 20px 0;
  
  .score-title {
    font-size: 16px;
    color: #606266;
    margin-bottom: 10px;
  }
  
  .score-value {
    font-size: 28px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 10px;
  }
  
  .score-level {
    margin-top: 10px;
  }
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style> 