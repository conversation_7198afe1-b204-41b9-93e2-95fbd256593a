<template>
  <div class="page-container">
    <div class="action-container">
      <div class="search-box">
        <el-input v-model="queryParams.keyword" placeholder="请输入角色名称" clearable style="width: 200px" class="mr-10" />
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </div>
      <div class="button-group">
        <el-button type="primary" @click="handleAdd">新增角色</el-button>
      </div>
    </div>
    
    <el-table v-loading="loading" :data="roleList">
      <el-table-column type="index" label="#" width="50" />
      <el-table-column prop="roleName" label="角色名称" show-overflow-tooltip />
      <el-table-column prop="roleKey" label="角色标识" show-overflow-tooltip />
      <el-table-column prop="sort" label="排序" width="80" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
            {{ scope.row.status === '0' ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="160" />
      <el-table-column label="操作" width="260" fixed="right">
        <template #default="scope">
          <el-button type="text" @click="handleEdit(scope.row)">修改</el-button>
          <el-button type="text" @click="handlePermission(scope.row)">权限</el-button>
          <el-button type="text" @click="handleDelete(scope.row)" v-if="scope.row.roleKey !== 'admin'">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    
    <!-- 添加/修改角色弹窗 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px" append-to-body>
      <el-form ref="roleForm" :model="roleForm" :rules="rules" label-width="100px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="roleForm.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色标识" prop="roleKey">
          <el-input v-model="roleForm.roleKey" placeholder="请输入角色标识" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="roleForm.sort" :min="0" :max="999" controls-position="right" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="roleForm.status">
            <el-radio label="0">正常</el-radio>
            <el-radio label="1">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="roleForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 分配权限弹窗 -->
    <el-dialog title="分配权限" v-model="permDialogVisible" width="600px" append-to-body>
      <el-form label-width="80px">
        <el-form-item label="角色名称">
          <el-input v-model="currentRole.roleName" readonly />
        </el-form-item>
        <el-form-item label="权限分配">
          <el-tree
            ref="menuTree"
            :data="menuOptions"
            :props="{ label: 'name', children: 'children' }"
            show-checkbox
            node-key="id"
            empty-text="加载中，请稍候"
            :default-expand-all="true"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="permDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitPermission">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'RoleManagement',
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [
        {
          id: 1,
          roleName: '超级管理员',
          roleKey: 'admin',
          sort: 1,
          status: '0',
          createTime: '2023-01-01 00:00:00',
          remark: '超级管理员拥有所有权限'
        },
        {
          id: 2,
          roleName: '普通管理员',
          roleKey: 'common',
          sort: 2,
          status: '0',
          createTime: '2023-01-01 00:00:00',
          remark: '普通管理员'
        },
        {
          id: 3,
          roleName: '站点管理员',
          roleKey: 'station',
          sort: 3,
          status: '0',
          createTime: '2023-01-02 00:00:00',
          remark: '站点管理员只能管理站点信息'
        }
      ],
      // 弹出层标题
      dialogTitle: '',
      // 是否显示弹出层
      dialogVisible: false,
      // 是否显示权限弹出层
      permDialogVisible: false,
      // 当前操作的角色
      currentRole: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: ''
      },
      // 表单参数
      roleForm: {
        id: undefined,
        roleName: undefined,
        roleKey: undefined,
        sort: 0,
        status: '0',
        remark: undefined
      },
      // 表单校验
      rules: {
        roleName: [
          { required: true, message: '角色名称不能为空', trigger: 'blur' }
        ],
        roleKey: [
          { required: true, message: '角色标识不能为空', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '显示顺序不能为空', trigger: 'blur' }
        ]
      },
      // 菜单树数据
      menuOptions: [
        {
          id: 1,
          name: '系统管理',
          children: [
            { id: 11, name: '用户管理' },
            { id: 12, name: '角色管理' },
            { id: 13, name: '菜单管理' }
          ]
        },
        {
          id: 2,
          name: '站点管理',
          children: [
            { id: 21, name: '站点列表' },
            { id: 22, name: '站点审核' }
          ]
        },
        {
          id: 3,
          name: '订单管理',
          children: [
            { id: 31, name: '订单列表' },
            { id: 32, name: '退款管理' }
          ]
        }
      ]
    }
  },
  methods: {
    /** 查询角色列表 */
    handleQuery() {
      this.queryParams.pageNum = 1
      // TODO: 实际应该调用API获取数据
      console.log('查询参数：', this.queryParams)
    },
    /** 重置查询操作 */
    resetQuery() {
      this.queryParams.keyword = ''
      this.handleQuery()
    },
    /** 新增角色 */
    handleAdd() {
      this.dialogTitle = '添加角色'
      this.dialogVisible = true
      this.resetForm()
    },
    /** 修改角色 */
    handleEdit(row) {
      this.dialogTitle = '修改角色'
      this.dialogVisible = true
      this.roleForm = { ...row }
    },
    /** 分配权限 */
    handlePermission(row) {
      this.currentRole = row
      this.permDialogVisible = true
      // TODO: 根据角色ID获取已分配的菜单权限，并设置选中状态
    },
    /** 删除角色 */
    handleDelete(row) {
      this.$confirm('是否确认删除角色名称为"' + row.roleName + '"的数据项?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用删除API
        console.log('删除角色ID：', row.id)
      }).catch(() => {})
    },
    /** 提交表单 */
    submitForm() {
      this.$refs.roleForm.validate(valid => {
        if (valid) {
          if (this.roleForm.id) {
            // TODO: 调用更新API
            console.log('更新角色：', this.roleForm)
          } else {
            // TODO: 调用新增API
            console.log('新增角色：', this.roleForm)
          }
          this.dialogVisible = false
        }
      })
    },
    /** 提交权限 */
    submitPermission() {
      const checkedKeys = this.$refs.menuTree.getCheckedKeys()
      // TODO: 调用分配权限API
      console.log('角色ID：', this.currentRole.id)
      console.log('分配的权限：', checkedKeys)
      this.permDialogVisible = false
    },
    /** 重置表单 */
    resetForm() {
      this.roleForm = {
        id: undefined,
        roleName: undefined,
        roleKey: undefined,
        sort: 0,
        status: '0',
        remark: undefined
      }
    },
    /** 分页大小变化 */
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.handleQuery()
    },
    /** 分页页码变化 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.handleQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
.action-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .search-box {
    display: flex;
    align-items: center;
  }
  
  .button-group {
    display: flex;
    align-items: center;
  }
}

.mr-10 {
  margin-right: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style> 