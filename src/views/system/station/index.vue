<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="filter-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
        <el-form-item label="驿站名称" prop="stationName">
          <el-input v-model="queryParams.stationName" placeholder="请输入驿站名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="地区" prop="area">
          <el-select v-model="queryParams.area" placeholder="请选择地区" clearable>
            <el-option v-for="dict in areaOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="queryParams.type" placeholder="请选择类型" clearable>
            <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-container">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
        </el-col>
      </el-row>

      <!-- 驿站列表 -->
      <el-table v-loading="loading" :data="stationList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="驿站ID" align="center" prop="stationId" />
        <el-table-column label="驿站名称" align="center" prop="stationName" min-width="150" show-overflow-tooltip />
        <el-table-column label="地区" align="center" prop="area" />
        <el-table-column label="类型" align="center" prop="type">
          <template #default="scope">
            <el-tag :type="scope.row.type === '个人' ? 'success' : 'primary'">{{ scope.row.type }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="空间大小" align="center" prop="size" />
        <el-table-column label="日包裹量" align="center" prop="dailyPackage" />
        <el-table-column label="价格" align="center" prop="price" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="total > 0"
        :total="total"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 添加或修改驿站对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="700px" append-to-body>
      <el-form ref="stationForm" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="驿站名称" prop="stationName">
              <el-input v-model="form.stationName" placeholder="请输入驿站名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择类型">
                <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="地区" prop="area">
              <el-select v-model="form.area" placeholder="请选择地区">
                <el-option v-for="dict in areaOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入详细地址" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="空间大小" prop="size">
              <el-input v-model="form.size" placeholder="请输入空间大小" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日包裹量" prop="dailyPackage">
              <el-input v-model="form.dailyPackage" placeholder="请输入日包裹量" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="价格" prop="price">
          <el-input v-model="form.price" placeholder="请输入价格" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="驿站图片" prop="images">
          <el-upload
            class="avatar-uploader"
            action="/api/common/upload"
            :show-file-list="false"
            :on-success="handleImageSuccess"
          >
            <img v-if="form.image" :src="form.image" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看驿站详情对话框 -->
    <el-dialog title="驿站详情" v-model="viewDialog.visible" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="驿站名称">{{ viewInfo.stationName }}</el-descriptions-item>
        <el-descriptions-item label="驿站类型">{{ viewInfo.type }}</el-descriptions-item>
        <el-descriptions-item label="所在地区">{{ viewInfo.area }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ viewInfo.phone }}</el-descriptions-item>
        <el-descriptions-item label="详细地址" :span="2">{{ viewInfo.address }}</el-descriptions-item>
        <el-descriptions-item label="空间大小">{{ viewInfo.size }}</el-descriptions-item>
        <el-descriptions-item label="日包裹量">{{ viewInfo.dailyPackage }}</el-descriptions-item>
        <el-descriptions-item label="价格">{{ viewInfo.price }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ viewInfo.status === 1 ? '正常' : '停用' }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewInfo.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ viewInfo.updateTime }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewInfo.remark }}</el-descriptions-item>
        <el-descriptions-item label="驿站图片" :span="2">
          <img v-if="viewInfo.image" :src="viewInfo.image" class="preview-image" />
          <span v-else>暂无图片</span>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 后续将导入API
// import { listStation, addStation, updateStation, deleteStation, exportStation, changeStationStatus } from '@/api/system/station'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  stationName: '',
  area: '',
  type: '',
  status: '',
  dateRange: []
})

// 表格数据
const loading = ref(false)
const stationList = ref([])
const total = ref(0)
const ids = ref([])
const single = computed(() => ids.value.length !== 1)
const multiple = computed(() => ids.value.length === 0)

// 选项数据
const typeOptions = [
  { value: '个人', label: '个人' },
  { value: '企业', label: '企业' },
  { value: '社区', label: '社区' },
]
const areaOptions = [
  { value: '北京', label: '北京' },
  { value: '上海', label: '上海' },
  { value: '广州', label: '广州' },
  { value: '深圳', label: '深圳' },
  { value: '杭州', label: '杭州' }
]
const statusOptions = [
  { value: '1', label: '正常' },
  { value: '0', label: '停用' }
]

// 弹窗参数
const dialog = reactive({
  visible: false,
  title: '',
  type: ''
})
const viewDialog = reactive({
  visible: false
})

// 表单参数
const stationForm = ref(null)
const form = reactive({
  stationId: null,
  stationName: '',
  type: '',
  area: '',
  address: '',
  phone: '',
  size: '',
  dailyPackage: '',
  price: '',
  status: 1,
  image: '',
  remark: ''
})
const viewInfo = reactive({})

// 表单校验规则
const rules = reactive({
  stationName: [
    { required: true, message: '驿站名称不能为空', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '驿站类型不能为空', trigger: 'change' }
  ],
  area: [
    { required: true, message: '所在地区不能为空', trigger: 'change' }
  ],
  address: [
    { required: true, message: '详细地址不能为空', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '联系电话不能为空', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  size: [
    { required: true, message: '空间大小不能为空', trigger: 'blur' }
  ],
  dailyPackage: [
    { required: true, message: '日包裹量不能为空', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '价格不能为空', trigger: 'blur' }
  ]
})

// 生命周期钩子
onMounted(() => {
  getList()
})

// 获取驿站列表
const getList = () => {
  loading.value = true
  // 测试数据，后续替换为API调用
  // listStation(queryParams).then(response => {
  //   stationList.value = response.rows
  //   total.value = response.total
  //   loading.value = false
  // })
  
  // 模拟数据
  setTimeout(() => {
    stationList.value = [
      {
        stationId: 1,
        stationName: '海淀创业园驿站',
        type: '企业',
        area: '北京',
        address: '北京市海淀区西二旗大街58号',
        phone: '13812345678',
        size: '100㎡',
        dailyPackage: '500件',
        price: '3000元/月',
        status: 1,
        createTime: '2023-05-15 10:30:00',
        updateTime: '2023-05-15 10:30:00',
        image: 'https://example.com/image1.jpg',
        remark: '位于创业园区内，交通便利'
      },
      {
        stationId: 2,
        stationName: '朝阳小区驿站',
        type: '社区',
        area: '北京',
        address: '北京市朝阳区建国路89号',
        phone: '13987654321',
        size: '50㎡',
        dailyPackage: '200件',
        price: '2000元/月',
        status: 1,
        createTime: '2023-05-10 14:20:00',
        updateTime: '2023-05-10 14:20:00',
        image: 'https://example.com/image2.jpg',
        remark: '社区内配套设施齐全'
      },
      {
        stationId: 3,
        stationName: '张三个人驿站',
        type: '个人',
        area: '上海',
        address: '上海市浦东新区张杨路500号',
        phone: '13598765432',
        size: '30㎡',
        dailyPackage: '100件',
        price: '1500元/月',
        status: 0,
        createTime: '2023-05-05 09:15:00',
        updateTime: '2023-05-05 09:15:00',
        image: 'https://example.com/image3.jpg',
        remark: '个人经营，服务周到'
      }
    ]
    total.value = 3
    loading.value = false
  }, 500)
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.stationName = ''
  queryParams.area = ''
  queryParams.type = ''
  queryParams.status = ''
  queryParams.dateRange = []
  handleQuery()
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.stationId)
}

// 新增按钮操作
const handleAdd = () => {
  reset()
  dialog.visible = true
  dialog.title = '添加驿站'
  dialog.type = 'add'
}

// 修改按钮操作
const handleUpdate = (row) => {
  reset()
  const stationId = row.stationId || ids.value[0]
  // 后续替换为API调用获取详情
  // getStation(stationId).then(response => {
  //   Object.assign(form, response.data)
  //   dialog.visible = true
  //   dialog.title = '修改驿站'
  //   dialog.type = 'edit'
  // })
  
  // 模拟数据
  const selectedRow = stationList.value.find(item => item.stationId === stationId)
  if (selectedRow) {
    Object.assign(form, selectedRow)
    dialog.visible = true
    dialog.title = '修改驿站'
    dialog.type = 'edit'
  }
}

// 查看详情操作
const handleView = (row) => {
  Object.assign(viewInfo, row)
  viewDialog.visible = true
}

// 提交表单
const submitForm = () => {
  stationForm.value.validate(valid => {
    if (valid) {
      if (form.stationId) {
        // updateStation(form).then(response => {
        //   ElMessage.success('修改成功')
        //   dialog.visible = false
        //   getList()
        // })
        ElMessage.success('修改成功')
        dialog.visible = false
        getList()
      } else {
        // addStation(form).then(response => {
        //   ElMessage.success('新增成功')
        //   dialog.visible = false
        //   getList()
        // })
        ElMessage.success('新增成功')
        dialog.visible = false
        getList()
      }
    }
  })
}

// 删除按钮操作
const handleDelete = (row) => {
  const stationIds = row.stationId || ids.value
  ElMessageBox.confirm('是否确认删除所选驿站?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // deleteStation(stationIds).then(() => {
    //   ElMessage.success('删除成功')
    //   getList()
    // })
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {})
}

// 导出按钮操作
const handleExport = () => {
  // exportStation(queryParams).then(response => {
  //   // 导出处理
  // })
  ElMessage.success('导出成功')
}

// 状态修改
const handleStatusChange = (row) => {
  // changeStationStatus({ stationId: row.stationId, status: row.status }).then(() => {
  //   ElMessage.success(`${row.status === 1 ? '启用' : '停用'}成功`)
  // }).catch(() => {
  //   row.status = row.status === 1 ? 0 : 1
  // })
  ElMessage.success(`${row.status === 1 ? '启用' : '停用'}成功`)
}

// 图片上传成功处理
const handleImageSuccess = (response) => {
  form.image = response.data
}

// 表单重置
const reset = () => {
  form.stationId = null
  form.stationName = ''
  form.type = ''
  form.area = ''
  form.address = ''
  form.phone = ''
  form.size = ''
  form.dailyPackage = ''
  form.price = ''
  form.status = 1
  form.image = ''
  form.remark = ''
}

// 取消按钮
const cancel = () => {
  dialog.visible = false
  reset()
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    
    &:hover {
      border-color: var(--el-color-primary);
    }
  }
  
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
    line-height: 178px;
  }
  
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}

.preview-image {
  max-width: 100%;
  max-height: 300px;
}
</style> 