<template>
  <div class="app-container">
    <el-card class="filter-container">
      <div class="filter-item">
        <el-input v-model="listQuery.search" placeholder="驿站名称/联系人" clearable @keyup.enter="handleFilter" style="width: 200px;" />
        <el-select v-model="listQuery.status" placeholder="审核状态" clearable style="width: 130px" @change="handleFilter">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="listQuery.type" placeholder="驿站类型" clearable style="width: 130px" @change="handleFilter">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          style="width: 260px;"
          @change="handleDateChange"
        />
        <el-button type="primary" icon="Search" @click="handleFilter">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
    </el-card>

    <el-card>
      <div class="table-header">
        <div class="statistics-cards">
          <div class="stat-card primary">
            <div class="stat-value">{{ statistics.totalCount }}</div>
            <div class="stat-label">总申请数</div>
          </div>
          <div class="stat-card warning">
            <div class="stat-value">{{ statistics.pendingCount }}</div>
            <div class="stat-label">待审核</div>
          </div>
          <div class="stat-card success">
            <div class="stat-value">{{ statistics.approvedCount }}</div>
            <div class="stat-label">已通过</div>
          </div>
          <div class="stat-card danger">
            <div class="stat-value">{{ statistics.rejectedCount }}</div>
            <div class="stat-label">已拒绝</div>
          </div>
        </div>
        <div>
          <el-button type="success" size="small" @click="exportData">导出数据</el-button>
        </div>
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column label="ID" prop="id" align="center" width="80" />
        <el-table-column label="驿站信息" min-width="200">
          <template #default="{row}">
            <div class="station-info">
              <el-image :src="row.station_image" style="width: 60px; height: 60px; border-radius: 4px;"></el-image>
              <div class="station-detail">
                <div class="station-name">{{ row.station_name }}</div>
                <div class="station-address">{{ row.address }}</div>
                <div>
                  <el-tag size="small" :type="getTypeTagType(row.type)">{{ getTypeText(row.type) }}</el-tag>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="联系人" align="center" width="150">
          <template #default="{row}">
            <div>{{ row.contact_name }}</div>
            <div class="contact-phone">{{ row.contact_phone }}</div>
          </template>
        </el-table-column>
        <el-table-column label="营业执照" align="center" width="120">
          <template #default="{row}">
            <el-button v-if="row.license_image" size="small" type="primary" plain @click="previewImage(row.license_image)">查看</el-button>
            <span v-else>无</span>
          </template>
        </el-table-column>
        <el-table-column label="申请时间" width="150" align="center">
          <template #default="{row}">
            <span>{{ formatDateTime(row.created_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" align="center" width="100">
          <template #default="{row}">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="审核时间" width="150" align="center">
          <template #default="{row}">
            <span v-if="row.verified_at">{{ formatDateTime(row.verified_at) }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template #default="{row}">
            <el-button v-if="row.status === 0" type="success" size="small" @click="handleApprove(row)">通过</el-button>
            <el-button v-if="row.status === 0" type="danger" size="small" @click="handleReject(row)">拒绝</el-button>
            <el-button type="info" size="small" @click="handleDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </el-card>

    <!-- 驿站详情对话框 -->
    <el-dialog title="驿站认证详情" v-model="dialogVisible" width="700px">
      <div v-loading="detailLoading" class="station-detail-dialog">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请编号" :span="2">{{ detail.id }}</el-descriptions-item>
          <el-descriptions-item label="驿站名称" :span="2">{{ detail.station_name }}</el-descriptions-item>
          <el-descriptions-item label="驿站类型">{{ getTypeText(detail.type) }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ detail.contact_name }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ detail.contact_phone }}</el-descriptions-item>
          <el-descriptions-item label="紧急联系人">{{ detail.emergency_contact || '--' }}</el-descriptions-item>
          <el-descriptions-item label="详细地址" :span="2">{{ detail.address }}</el-descriptions-item>
          <el-descriptions-item label="营业时间" :span="2">{{ detail.business_hours || '--' }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime(detail.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="审核时间">{{ detail.verified_at ? formatDateTime(detail.verified_at) : '--' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag v-if="detail.status !== undefined" :type="getStatusType(detail.status)">
              {{ getStatusText(detail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="审核备注">{{ detail.remark || '--' }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="image-container">
          <div class="image-section">
            <div class="image-title">驿站照片</div>
            <div class="image-preview">
              <el-image
                v-if="detail.station_image"
                :src="detail.station_image"
                fit="cover"
                :preview-src-list="[detail.station_image]"
                style="width: 200px; height: 150px; border-radius: 4px;"
              ></el-image>
              <div v-else class="no-image">未上传照片</div>
            </div>
          </div>
          
          <div class="image-section">
            <div class="image-title">营业执照</div>
            <div class="image-preview">
              <el-image
                v-if="detail.license_image"
                :src="detail.license_image"
                fit="cover"
                :preview-src-list="[detail.license_image]"
                style="width: 200px; height: 150px; border-radius: 4px;"
              ></el-image>
              <div v-else class="no-image">未上传执照</div>
            </div>
          </div>
        </div>
        
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <template v-if="detail.status === 0">
            <el-button type="success" @click="handleApprove(detail)">通过</el-button>
            <el-button type="danger" @click="handleReject(detail)">拒绝</el-button>
          </template>
        </div>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog :title="auditAction === 'approve' ? '通过驿站认证' : '拒绝驿站认证'" v-model="auditDialogVisible" width="500px">
      <el-form :model="auditForm" label-width="80px">
        <el-form-item label="审核意见">
          <el-input
            v-model="auditForm.remark"
            type="textarea"
            :rows="4"
            :placeholder="auditAction === 'approve' ? '审核通过说明（选填）' : '拒绝原因（必填）'"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取 消</el-button>
          <el-button :type="auditAction === 'approve' ? 'success' : 'danger'" @click="submitAudit" :loading="auditSubmitting">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" v-model="imagePreviewVisible" width="700px" center>
      <div class="image-preview-container">
        <el-image :src="previewImageUrl" style="max-width: 100%; max-height: 500px;"></el-image>
    </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '@/components/Pagination'
import { formatDateTime } from '@/utils/index'

export default {
  name: 'StationVerify',
  components: { Pagination },
  setup() {
    const dateRange = ref([])
    const list = ref([])
    const total = ref(0)
    const listLoading = ref(false)
    const dialogVisible = ref(false)
    const detailLoading = ref(false)
    const detail = ref({})
    const auditDialogVisible = ref(false)
    const auditSubmitting = ref(false)
    const auditAction = ref('approve')
    const imagePreviewVisible = ref(false)
    const previewImageUrl = ref('')
    
    const listQuery = reactive({
      page: 1,
      limit: 10,
      status: '',
      type: '',
      search: '',
      start_date: '',
      end_date: ''
    })
    
    const statistics = reactive({
      totalCount: 0,
      pendingCount: 0,
      approvedCount: 0,
      rejectedCount: 0
    })
    
    const auditForm = reactive({
      id: null,
      status: 0,
      remark: ''
    })
    
    const statusOptions = [
      { label: '待审核', value: 0 },
      { label: '已通过', value: 1 },
      { label: '已拒绝', value: 2 }
    ]
    
    const typeOptions = [
      { label: '社区驿站', value: 1 },
      { label: '商圈驿站', value: 2 },
      { label: '校园驿站', value: 3 },
      { label: '企业驿站', value: 4 },
      { label: '其他', value: 5 }
    ]
    
    // 模拟数据
    const mockList = [
      {
        id: 1,
        station_name: '阳光社区驿站',
        station_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        address: '北京市朝阳区阳光社区88号',
        type: 1,
        contact_name: '张明',
        contact_phone: '***********',
        emergency_contact: '***********',
        business_hours: '08:00-20:00',
        license_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        created_at: '2023-05-15 09:30:00',
        verified_at: null,
        status: 0,
        remark: ''
      },
      {
        id: 2,
        station_name: '星光商业广场驿站',
        station_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        address: '上海市浦东新区星光广场12号',
        type: 2,
        contact_name: '李强',
        contact_phone: '***********',
        emergency_contact: '***********',
        business_hours: '09:00-21:00',
        license_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        created_at: '2023-05-14 14:20:00',
        verified_at: '2023-05-14 16:30:00',
        status: 1,
        remark: '资料齐全，符合要求'
      },
      {
        id: 3,
        station_name: '清华大学东门驿站',
        station_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        address: '北京市海淀区清华大学东门',
        type: 3,
        contact_name: '王学',
        contact_phone: '***********',
        emergency_contact: '***********',
        business_hours: '07:30-22:00',
        license_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        created_at: '2023-05-13 10:15:00',
        verified_at: '2023-05-13 11:30:00',
        status: 2,
        remark: '证照不清晰，联系方式无法验证'
      },
      {
        id: 4,
        station_name: '科技园创业中心驿站',
        station_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        address: '深圳市南山区科技园创业中心A栋',
        type: 4,
        contact_name: '刘创',
        contact_phone: '***********',
        emergency_contact: '***********',
        business_hours: '08:30-19:30',
        license_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        created_at: '2023-05-12 16:45:00',
        verified_at: null,
        status: 0,
        remark: ''
      }
    ]
    
    const getList = () => {
      listLoading.value = true
      
      // 模拟API调用
      setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredList = [...mockList]
        
        if (listQuery.status !== '') {
          filteredList = filteredList.filter(item => item.status === listQuery.status)
        }
        
        if (listQuery.type !== '') {
          filteredList = filteredList.filter(item => item.type === listQuery.type)
        }
        
        if (listQuery.search) {
          filteredList = filteredList.filter(item => 
            item.station_name.includes(listQuery.search) || 
            item.contact_name.includes(listQuery.search) ||
            item.contact_phone.includes(listQuery.search)
          )
        }
        
        if (listQuery.start_date && listQuery.end_date) {
          // 简化处理，实际项目中应该使用日期比较
          // 这里只是模拟筛选效果
        }
        
        list.value = filteredList
        total.value = filteredList.length
        
        // 更新统计数据
        updateStatistics(filteredList)
        
        listLoading.value = false
      }, 500)
    }
    
    const updateStatistics = (data) => {
      let pendingCount = 0
      let approvedCount = 0
      let rejectedCount = 0
      
      data.forEach(item => {
        if (item.status === 0) {
          pendingCount++
        } else if (item.status === 1) {
          approvedCount++
        } else if (item.status === 2) {
          rejectedCount++
        }
      })
      
      statistics.totalCount = data.length
      statistics.pendingCount = pendingCount
      statistics.approvedCount = approvedCount
      statistics.rejectedCount = rejectedCount
    }
    
    const handleFilter = () => {
      listQuery.page = 1
      getList()
    }
    
    const resetQuery = () => {
      dateRange.value = []
      Object.assign(listQuery, {
        page: 1,
        limit: 10,
        status: '',
        type: '',
        search: '',
        start_date: '',
        end_date: ''
      })
      getList()
    }
    
    const handleDateChange = (val) => {
      if (val) {
        listQuery.start_date = val[0]
        listQuery.end_date = val[1]
      } else {
        listQuery.start_date = ''
        listQuery.end_date = ''
      }
    }
    
    const getStatusType = (status) => {
      const statusMap = {
        0: 'warning',
        1: 'success',
        2: 'danger'
      }
      return statusMap[status]
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已拒绝'
      }
      return statusMap[status]
    }
    
    const getTypeText = (type) => {
      const typeMap = {
        1: '社区驿站',
        2: '商圈驿站',
        3: '校园驿站',
        4: '企业驿站',
        5: '其他'
      }
      return typeMap[type] || '未知'
    }
    
    const getTypeTagType = (type) => {
      const typeMap = {
        1: '',  // 默认灰色
        2: 'success',
        3: 'warning',
        4: 'info',
        5: 'danger'
      }
      return typeMap[type] || ''
    }
    
    const handleDetail = (row) => {
      detailLoading.value = true
      detail.value = { ...row }
      dialogVisible.value = true
      
      setTimeout(() => {
        detailLoading.value = false
      }, 300)
    }
    
    const previewImage = (url) => {
      previewImageUrl.value = url
      imagePreviewVisible.value = true
    }
    
    const handleApprove = (row) => {
      auditAction.value = 'approve'
      auditForm.id = row.id
      auditForm.status = 1
      auditForm.remark = ''
      auditDialogVisible.value = true
    }
    
    const handleReject = (row) => {
      auditAction.value = 'reject'
      auditForm.id = row.id
      auditForm.status = 2
      auditForm.remark = ''
      auditDialogVisible.value = true
    }
    
    const submitAudit = () => {
      if (auditAction.value === 'reject' && !auditForm.remark) {
        ElMessage.warning('请填写拒绝原因')
        return
      }
      
      auditSubmitting.value = true
      
      // 模拟API调用
      setTimeout(() => {
        // 更新本地列表数据
        const index = list.value.findIndex(item => item.id === auditForm.id)
        if (index > -1) {
          list.value[index].status = auditForm.status
          list.value[index].verified_at = formatDateTime(new Date())
          list.value[index].remark = auditForm.remark
          
          // 更新详情数据（如果当前正在查看详情）
          if (detail.value.id === auditForm.id) {
            detail.value.status = auditForm.status
            detail.value.verified_at = formatDateTime(new Date())
            detail.value.remark = auditForm.remark
          }
        }
        
        // 更新统计数据
        updateStatistics(list.value)
        
        auditSubmitting.value = false
        auditDialogVisible.value = false
        
        ElMessage({
          type: 'success',
          message: auditAction.value === 'approve' ? '驿站认证已通过' : '驿站认证已拒绝'
        })
      }, 500)
    }
    
    const exportData = () => {
      ElMessage.success('数据导出功能开发中')
    }
    
    onMounted(() => {
      getList()
    })
    
    return {
      formatDateTime,
      dateRange,
      list,
      total,
      listLoading,
      listQuery,
      statistics,
      statusOptions,
      typeOptions,
      dialogVisible,
      detailLoading,
      detail,
      auditDialogVisible,
      auditSubmitting,
      auditAction,
      auditForm,
      imagePreviewVisible,
      previewImageUrl,
      handleFilter,
      resetQuery,
      handleDateChange,
      getStatusType,
      getStatusText,
      getTypeText,
      getTypeTagType,
      handleDetail,
      previewImage,
      handleApprove,
      handleReject,
      submitAudit,
      exportData
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
  
  .filter-item {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .statistics-cards {
    display: flex;
    gap: 15px;
    
    .stat-card {
      padding: 15px;
  border-radius: 4px;
      width: 130px;
      text-align: center;
      
      &.primary {
        background-color: #ecf5ff;
        color: #409eff;
      }
      
      &.warning {
        background-color: #fdf6ec;
        color: #e6a23c;
      }
      
      &.success {
        background-color: #f0f9eb;
        color: #67c23a;
      }
      
      &.danger {
        background-color: #fef0f0;
        color: #f56c6c;
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .stat-label {
        font-size: 14px;
      }
    }
  }
}

.station-info {
  display: flex;
  align-items: flex-start;
  
  .station-detail {
    margin-left: 10px;
    
    .station-name {
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .station-address {
      color: #606266;
      font-size: 13px;
      margin-bottom: 5px;
    }
  }
}

.contact-phone {
  color: #909399;
  font-size: 13px;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

.station-detail-dialog {
  min-height: 200px;
}

.image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
  
  .image-section {
    flex: 1;
    min-width: 250px;
    
    .image-title {
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .image-preview {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 180px;
      background-color: #f5f7fa;
      
      .no-image {
  color: #909399;
      }
    }
  }
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style> 