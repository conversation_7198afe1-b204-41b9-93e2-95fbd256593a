<template>
  <div class="transfer-container">
    <div class="page-header">
      <h2><i class="el-icon-s-shop"></i> 驿站转让管理</h2>
      <p>管理驿站转让信息，审核转让申请</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon pending">
          <i class="el-icon-time"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.pending }}</div>
          <div class="stat-label">待审核</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon published">
          <i class="el-icon-check"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.published }}</div>
          <div class="stat-label">已发布</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon completed">
          <i class="el-icon-success"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon total">
          <i class="el-icon-s-data"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total }}</div>
          <div class="stat-label">总转让数</div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="转让编号">
          <el-input v-model="searchForm.transferNo" placeholder="请输入转让编号" clearable />
        </el-form-item>
        <el-form-item label="转让类型">
          <el-select v-model="searchForm.transferType" placeholder="请选择" clearable>
            <el-option label="完全转让" value="full_transfer" />
            <el-option label="股份转让" value="share_transfer" />
            <el-option label="租赁" value="lease" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="待审核" value="pending" />
            <el-option label="已发布" value="published" />
            <el-option label="协商中" value="negotiating" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="价格范围">
          <el-input-number v-model="searchForm.minPrice" placeholder="最低价" :min="0" />
          <span style="margin: 0 8px;">-</span>
          <el-input-number v-model="searchForm.maxPrice" placeholder="最高价" :min="0" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="transferList"
        style="width: 100%"
      >
        <el-table-column prop="transferNo" label="转让编号" width="140" />
        <el-table-column label="驿站信息" min-width="200">
          <template #default="scope">
            <div class="station-info">
              <div class="station-name">{{ scope.row.stationName }}</div>
              <div class="station-address">{{ scope.row.stationAddress }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="转让标题" min-width="180" />
        <el-table-column prop="transferType" label="转让类型" width="100">
          <template #default="scope">
            <el-tag :type="getTransferTypeColor(scope.row.transferType)">
              {{ getTransferTypeText(scope.row.transferType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="转让价格" width="120">
          <template #default="scope">
            <span class="price">¥{{ formatMoney(scope.row.price) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sellerName" label="转让方" width="100" />
        <el-table-column prop="viewCount" label="浏览量" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="publishedAt" label="发布时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.publishedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              <i class="el-icon-view"></i> 查看
            </el-button>
            <el-button 
              v-if="scope.row.status === 'pending'" 
              type="text" 
              size="small" 
              @click="handleApprove(scope.row)"
            >
              <i class="el-icon-check"></i> 审核
            </el-button>
            <el-button type="text" size="small" class="danger" @click="handleDelete(scope.row)">
              <i class="el-icon-delete"></i> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StationTransfer',
  data() {
    return {
      loading: false,
      transferList: [],
      searchForm: {
        transferNo: '',
        transferType: '',
        status: '',
        minPrice: null,
        maxPrice: null
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      stats: {
        pending: 0,
        published: 0,
        completed: 0,
        total: 0
      }
    }
  },
  mounted() {
    this.loadStats()
    this.loadTransferList()
  },
  methods: {
    async loadStats() {
      // 模拟统计数据
      this.stats = {
        pending: 5,
        published: 12,
        completed: 8,
        total: 25
      }
    },
    
    async loadTransferList() {
      this.loading = true
      try {
        // 模拟数据
        setTimeout(() => {
          this.transferList = [
            {
              id: 1,
              transferNo: 'TF202401001',
              stationName: '中关村驿站',
              stationAddress: '北京市海淀区中关村大街1号',
              title: '中关村核心地段驿站转让',
              transferType: 'full_transfer',
              price: 150000,
              sellerName: '张三',
              viewCount: 156,
              status: 'published',
              publishedAt: new Date()
            },
            {
              id: 2,
              transferNo: 'TF202401002',
              stationName: '望京驿站',
              stationAddress: '北京市朝阳区望京SOHO',
              title: '望京商务区驿站股份转让',
              transferType: 'share_transfer',
              price: 80000,
              sellerName: '李四',
              viewCount: 89,
              status: 'pending',
              publishedAt: new Date()
            }
          ]
          this.pagination.total = 2
          this.loading = false
        }, 1000)
      } catch (error) {
        this.$message.error('加载转让列表失败')
        this.loading = false
      }
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadTransferList()
    },
    
    handleReset() {
      this.searchForm = {
        transferNo: '',
        transferType: '',
        status: '',
        minPrice: null,
        maxPrice: null
      }
      this.pagination.page = 1
      this.loadTransferList()
    },
    
    handleView(transfer) {
      this.$message.info('查看转让详情功能开发中')
    },
    
    handleApprove(transfer) {
      this.$confirm('确定要审核通过这个转让申请吗？', '审核确认', {
        confirmButtonText: '通过',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        transfer.status = 'published'
        this.$message.success('审核通过')
      })
    },
    
    handleDelete(transfer) {
      this.$confirm('确定要删除这个转让信息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      })
    },
    
    handleSizeChange(size) {
      this.pagination.limit = size
      this.loadTransferList()
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadTransferList()
    },
    
    getTransferTypeColor(type) {
      const colorMap = {
        full_transfer: 'primary',
        share_transfer: 'success',
        lease: 'warning'
      }
      return colorMap[type] || 'info'
    },
    
    getTransferTypeText(type) {
      const textMap = {
        full_transfer: '完全转让',
        share_transfer: '股份转让',
        lease: '租赁'
      }
      return textMap[type] || '未知'
    },
    
    getStatusColor(status) {
      const colorMap = {
        pending: 'warning',
        published: 'primary',
        negotiating: 'info',
        completed: 'success',
        cancelled: 'danger'
      }
      return colorMap[status] || 'info'
    },
    
    getStatusText(status) {
      const textMap = {
        pending: '待审核',
        published: '已发布',
        negotiating: '协商中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return textMap[status] || '未知'
    },
    
    formatMoney(amount) {
      return new Intl.NumberFormat('zh-CN').format(amount)
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.transfer-container {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        
        &.pending { background: #E6A23C; }
        &.published { background: #409EFF; }
        &.completed { background: #67C23A; }
        &.total { background: #909399; }
      }
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .filter-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .station-info {
      .station-name {
        font-weight: 500;
        color: #303133;
      }
      
      .station-address {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
    
    .price {
      font-weight: bold;
      color: #F56C6C;
    }
    
    .danger {
      color: #F56C6C;
    }
  }
  
  .pagination-container {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #EBEEF5;
  }
}
</style>
