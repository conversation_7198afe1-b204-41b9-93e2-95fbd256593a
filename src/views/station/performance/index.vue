<template>
  <div class="app-container">
    <el-card class="filter-container">
      <div class="filter-item">
        <el-input v-model="listQuery.search" placeholder="驿站名称/负责人" clearable @keyup.enter="handleFilter" style="width: 200px;" />
        <el-select v-model="listQuery.level" placeholder="评级等级" clearable style="width: 130px" @change="handleFilter">
          <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select v-model="listQuery.type" placeholder="驿站类型" clearable style="width: 130px" @change="handleFilter">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          style="width: 260px;"
          @change="handleDateChange"
        />
        <el-button type="primary" icon="Search" @click="handleFilter">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
    </el-card>

    <div class="content-container">
      <el-card class="performance-chart-card">
        <template #header>
          <div class="card-header">
            <span>驿站绩效统计</span>
            <div>
              <el-radio-group v-model="chartType" size="small" @change="changeChartType">
                <el-radio-button label="level">等级分布</el-radio-button>
                <el-radio-button label="score">评分分布</el-radio-button>
                <el-radio-button label="trend">趋势变化</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>
        <div class="chart-container" v-loading="chartLoading">
          <div id="performanceChart" style="width: 100%; height: 400px;"></div>
        </div>
      </el-card>

      <el-card>
        <template #header>
          <div class="card-header">
            <span>驿站绩效列表</span>
            <div>
              <el-button type="primary" size="small" @click="startEvaluation">开始评估</el-button>
              <el-button type="success" size="small" @click="exportData">导出数据</el-button>
            </div>
          </div>
        </template>
        
        <el-table
          v-loading="tableLoading"
          :data="list"
          border
          fit
          highlight-current-row
          style="width: 100%;"
        >
          <el-table-column label="ID" prop="id" align="center" width="80" />
          <el-table-column label="驿站信息" min-width="200">
            <template #default="{row}">
              <div class="station-info">
                <el-image :src="row.station_image" style="width: 60px; height: 60px; border-radius: 4px;"></el-image>
                <div class="station-detail">
                  <div class="station-name">{{ row.station_name }}</div>
                  <div class="station-address">{{ row.address }}</div>
                  <div>
                    <el-tag size="small" :type="getTypeTagType(row.type)">{{ getTypeText(row.type) }}</el-tag>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="评级" align="center" width="100">
            <template #default="{row}">
              <el-tag :type="getLevelTagType(row.level)">{{ getLevelText(row.level) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="综合评分" align="center" width="100">
            <template #default="{row}">
              <div class="score-box">
                <span class="score-value">{{ row.total_score }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="订单处理" align="center" width="100">
            <template #default="{row}">
              <el-progress :percentage="row.order_score" :color="getScoreColor(row.order_score)" :format="percentFormat" />
            </template>
          </el-table-column>
          <el-table-column label="用户评价" align="center" width="100">
            <template #default="{row}">
              <el-progress :percentage="row.user_score" :color="getScoreColor(row.user_score)" :format="percentFormat" />
            </template>
          </el-table-column>
          <el-table-column label="服务质量" align="center" width="100">
            <template #default="{row}">
              <el-progress :percentage="row.service_score" :color="getScoreColor(row.service_score)" :format="percentFormat" />
            </template>
          </el-table-column>
          <el-table-column label="上次评估" width="150" align="center">
            <template #default="{row}">
              <span>{{ formatDateTime(row.created_at) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
            <template #default="{row}">
              <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
              <el-button type="success" size="small" @click="handleEvaluate(row)">评估</el-button>
              <el-button type="info" size="small" @click="handleHistory(row)">历史</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          @pagination="getList"
        />
      </el-card>
    </div>

    <!-- 绩效详情对话框 -->
    <el-dialog title="绩效详情" v-model="detailDialogVisible" width="800px">
      <div v-loading="detailLoading" class="performance-detail-dialog">
        <div class="station-header">
          <div class="station-info-large">
            <el-image :src="detail.station_image" style="width: 80px; height: 80px; border-radius: 4px;"></el-image>
            <div class="station-detail-large">
              <div class="station-name-large">{{ detail.station_name }}</div>
              <div class="station-address">{{ detail.address }}</div>
              <div>
                <el-tag size="small" :type="getTypeTagType(detail.type)">{{ getTypeText(detail.type) }}</el-tag>
                <el-tag size="small" :type="getLevelTagType(detail.level)" style="margin-left: 5px;">{{ getLevelText(detail.level) }}</el-tag>
              </div>
            </div>
          </div>
          <div class="score-circle">
            <el-progress type="dashboard" :percentage="detail.total_score" :color="getScoreColor(detail.total_score)" :stroke-width="10">
              <template #default>
                <div class="circle-content">
                  <div class="circle-value">{{ detail.total_score }}</div>
                  <div class="circle-text">综合评分</div>
                </div>
              </template>
            </el-progress>
          </div>
        </div>

        <el-divider content-position="center">评分明细</el-divider>

        <div class="score-details">
          <div class="score-item">
            <div class="score-item-title">订单处理</div>
            <el-progress :percentage="detail.order_score" :color="getScoreColor(detail.order_score)" :format="percentFormat" />
            <div class="score-item-info">
              <div>订单总量：{{ detail.order_count || 0 }}</div>
              <div>准时率：{{ detail.order_on_time_rate || 0 }}%</div>
              <div>差评率：{{ detail.order_bad_rate || 0 }}%</div>
            </div>
          </div>
          <div class="score-item">
            <div class="score-item-title">用户评价</div>
            <el-progress :percentage="detail.user_score" :color="getScoreColor(detail.user_score)" :format="percentFormat" />
            <div class="score-item-info">
              <div>评价总量：{{ detail.review_count || 0 }}</div>
              <div>满意度：{{ detail.satisfaction_rate || 0 }}%</div>
              <div>投诉量：{{ detail.complaint_count || 0 }}</div>
            </div>
          </div>
          <div class="score-item">
            <div class="score-item-title">服务质量</div>
            <el-progress :percentage="detail.service_score" :color="getScoreColor(detail.service_score)" :format="percentFormat" />
            <div class="score-item-info">
              <div>服务时长：{{ detail.service_hours || 0 }}h/天</div>
              <div>响应速度：{{ detail.response_time || 0 }}分钟</div>
              <div>服务态度：{{ detail.attitude_score || 0 }}分</div>
            </div>
          </div>
        </div>

        <el-divider content-position="center">评估报告</el-divider>

        <div class="evaluation-report">
          <div class="report-item">
            <div class="report-title">优势分析</div>
            <div class="report-content">{{ detail.advantages || '暂无数据' }}</div>
          </div>
          <div class="report-item">
            <div class="report-title">改进建议</div>
            <div class="report-content">{{ detail.suggestions || '暂无数据' }}</div>
          </div>
          <div class="report-item">
            <div class="report-title">评估结论</div>
            <div class="report-content">{{ detail.conclusion || '暂无数据' }}</div>
          </div>
        </div>

        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handlePrint">打印报告</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 评估对话框 -->
    <el-dialog title="驿站绩效评估" v-model="evaluateDialogVisible" width="700px">
      <el-form ref="evaluateForm" :model="evaluateForm" label-width="100px" v-loading="evaluateLoading">
        <el-form-item label="驿站名称">
          <span>{{ evaluateForm.station_name }}</span>
        </el-form-item>

        <el-form-item label="订单处理" prop="order_score">
          <el-slider v-model="evaluateForm.order_score" :step="1" :max="100" show-input />
          <div class="score-input-group">
            <el-input-number v-model="evaluateForm.order_count" :min="0" size="small" placeholder="订单量"></el-input-number>
            <el-input-number v-model="evaluateForm.order_on_time_rate" :min="0" :max="100" size="small" placeholder="准时率%"></el-input-number>
            <el-input-number v-model="evaluateForm.order_bad_rate" :min="0" :max="100" size="small" placeholder="差评率%"></el-input-number>
          </div>
        </el-form-item>

        <el-form-item label="用户评价" prop="user_score">
          <el-slider v-model="evaluateForm.user_score" :step="1" :max="100" show-input />
          <div class="score-input-group">
            <el-input-number v-model="evaluateForm.review_count" :min="0" size="small" placeholder="评价数"></el-input-number>
            <el-input-number v-model="evaluateForm.satisfaction_rate" :min="0" :max="100" size="small" placeholder="满意度%"></el-input-number>
            <el-input-number v-model="evaluateForm.complaint_count" :min="0" size="small" placeholder="投诉数"></el-input-number>
          </div>
        </el-form-item>

        <el-form-item label="服务质量" prop="service_score">
          <el-slider v-model="evaluateForm.service_score" :step="1" :max="100" show-input />
          <div class="score-input-group">
            <el-input-number v-model="evaluateForm.service_hours" :min="0" :max="24" size="small" placeholder="服务时长"></el-input-number>
            <el-input-number v-model="evaluateForm.response_time" :min="0" size="small" placeholder="响应时间"></el-input-number>
            <el-input-number v-model="evaluateForm.attitude_score" :min="0" :max="10" size="small" placeholder="态度评分"></el-input-number>
          </div>
        </el-form-item>

        <el-form-item label="优势分析" prop="advantages">
          <el-input v-model="evaluateForm.advantages" type="textarea" :rows="3" placeholder="请输入优势分析"></el-input>
        </el-form-item>

        <el-form-item label="改进建议" prop="suggestions">
          <el-input v-model="evaluateForm.suggestions" type="textarea" :rows="3" placeholder="请输入改进建议"></el-input>
        </el-form-item>

        <el-form-item label="评估结论" prop="conclusion">
          <el-input v-model="evaluateForm.conclusion" type="textarea" :rows="3" placeholder="请输入评估结论"></el-input>
        </el-form-item>

        <el-form-item label="评估等级" prop="level">
          <el-select v-model="evaluateForm.level" placeholder="请选择评估等级" style="width: 100%">
            <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="evaluateDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitEvaluation">提 交</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 历史记录对话框 -->
    <el-dialog title="历史评估记录" v-model="historyDialogVisible" width="900px">
      <div v-loading="historyLoading" class="history-dialog">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in historyList"
            :key="index"
            :timestamp="formatDateTime(item.created_at)"
            :type="getHistoryItemType(item.level)"
          >
            <el-card>
              <div class="history-item-header">
                <span>评估等级：<el-tag :type="getLevelTagType(item.level)">{{ getLevelText(item.level) }}</el-tag></span>
                <span>综合评分：<span class="history-score">{{ item.total_score }}分</span></span>
              </div>
              <div class="history-item-scores">
                <div>订单处理：{{ item.order_score }}分</div>
                <div>用户评价：{{ item.user_score }}分</div>
                <div>服务质量：{{ item.service_score }}分</div>
              </div>
              <div class="history-item-report">
                <div>评估结论：{{ item.conclusion }}</div>
              </div>
              <div class="history-item-footer">
                <span>评估人：{{ item.evaluator }}</span>
                <el-button type="text" @click="viewHistoryDetail(item)">查看详情</el-button>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        
        <div class="empty-data" v-if="historyList.length === 0">
          <el-empty description="暂无历史评估记录"></el-empty>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Pagination from '@/components/Pagination'
import { formatDateTime } from '@/utils/index'
import * as echarts from 'echarts'

export default {
  name: 'StationPerformance',
  components: { Pagination },
  setup() {
    // 图表相关
    let performanceChart = null
    const chartType = ref('level')
    const chartLoading = ref(false)
    
    // 表格和分页相关
    const dateRange = ref([])
    const list = ref([])
    const total = ref(0)
    const tableLoading = ref(false)
    
    // 对话框相关
    const detailDialogVisible = ref(false)
    const detailLoading = ref(false)
    const detail = ref({})
    const evaluateDialogVisible = ref(false)
    const evaluateLoading = ref(false)
    const historyDialogVisible = ref(false)
    const historyLoading = ref(false)
    const historyList = ref([])
    
    // 表单数据
    const listQuery = reactive({
      page: 1,
      limit: 10,
      level: '',
      type: '',
      search: '',
      start_date: '',
      end_date: ''
    })
    
    const evaluateForm = reactive({
      id: null,
      station_id: null,
      station_name: '',
      order_score: 0,
      user_score: 0,
      service_score: 0,
      total_score: 0,
      level: 'C',
      order_count: 0,
      order_on_time_rate: 0,
      order_bad_rate: 0,
      review_count: 0,
      satisfaction_rate: 0,
      complaint_count: 0,
      service_hours: 0,
      response_time: 0,
      attitude_score: 0,
      advantages: '',
      suggestions: '',
      conclusion: ''
    })
    
    // 选项数据
    const levelOptions = [
      { label: 'S级', value: 'S' },
      { label: 'A级', value: 'A' },
      { label: 'B级', value: 'B' },
      { label: 'C级', value: 'C' },
      { label: 'D级', value: 'D' }
    ]
    
    const typeOptions = [
      { label: '社区驿站', value: 1 },
      { label: '商圈驿站', value: 2 },
      { label: '校园驿站', value: 3 },
      { label: '企业驿站', value: 4 },
      { label: '其他', value: 5 }
    ]
    
    // 模拟数据
    const mockList = [
      {
        id: 1,
        station_id: 101,
        station_name: '阳光社区驿站',
        station_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        address: '北京市朝阳区阳光社区88号',
        type: 1,
        level: 'A',
        total_score: 85,
        order_score: 90,
        user_score: 82,
        service_score: 83,
        order_count: 1250,
        order_on_time_rate: 95,
        order_bad_rate: 2,
        review_count: 750,
        satisfaction_rate: 90,
        complaint_count: 5,
        service_hours: 12,
        response_time: 5,
        attitude_score: 8.5,
        advantages: '服务时长充足，订单处理效率高，用户评价良好',
        suggestions: '可以进一步提高服务质量，减少投诉量',
        conclusion: '整体表现优秀，属于A级驿站',
        created_at: '2023-05-15 09:30:00',
        evaluator: '张评估'
      },
      {
        id: 2,
        station_id: 102,
        station_name: '星光商业广场驿站',
        station_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        address: '上海市浦东新区星光广场12号',
        type: 2,
        level: 'S',
        total_score: 95,
        order_score: 98,
        user_score: 93,
        service_score: 94,
        order_count: 2100,
        order_on_time_rate: 99,
        order_bad_rate: 0.5,
        review_count: 1800,
        satisfaction_rate: 98,
        complaint_count: 2,
        service_hours: 16,
        response_time: 3,
        attitude_score: 9.5,
        advantages: '各方面表现卓越，尤其是订单处理准确率高，几乎没有差评',
        suggestions: '继续保持当前优势，适当扩大服务范围',
        conclusion: '表现卓越，是标杆驿站，属于S级',
        created_at: '2023-05-14 14:20:00',
        evaluator: '李评估'
      },
      {
        id: 3,
        station_id: 103,
        station_name: '清华大学东门驿站',
        station_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        address: '北京市海淀区清华大学东门',
        type: 3,
        level: 'B',
        total_score: 75,
        order_score: 73,
        user_score: 78,
        service_score: 74,
        order_count: 980,
        order_on_time_rate: 88,
        order_bad_rate: 5,
        review_count: 520,
        satisfaction_rate: 85,
        complaint_count: 15,
        service_hours: 10,
        response_time: 8,
        attitude_score: 7.8,
        advantages: '用户评价相对较好，服务态度尚可',
        suggestions: '需要提高订单处理效率，减少延误情况',
        conclusion: '整体表现中等，属于B级驿站',
        created_at: '2023-05-13 10:15:00',
        evaluator: '王评估'
      },
      {
        id: 4,
        station_id: 104,
        station_name: '科技园创业中心驿站',
        station_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        address: '深圳市南山区科技园创业中心A栋',
        type: 4,
        level: 'C',
        total_score: 65,
        order_score: 62,
        user_score: 68,
        service_score: 65,
        order_count: 780,
        order_on_time_rate: 80,
        order_bad_rate: 8,
        review_count: 350,
        satisfaction_rate: 75,
        complaint_count: 25,
        service_hours: 8,
        response_time: 12,
        attitude_score: 6.5,
        advantages: '位置较好，服务范围广',
        suggestions: '需要全面提升服务质量，加强员工培训，提高响应速度',
        conclusion: '整体表现一般，属于C级驿站',
        created_at: '2023-05-12 16:45:00',
        evaluator: '刘评估'
      },
      {
        id: 5,
        station_id: 105,
        station_name: '城南物流中心驿站',
        station_image: 'https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png',
        address: '广州市番禺区城南物流中心3号楼',
        type: 5,
        level: 'D',
        total_score: 52,
        order_score: 50,
        user_score: 55,
        service_score: 51,
        order_count: 520,
        order_on_time_rate: 70,
        order_bad_rate: 15,
        review_count: 280,
        satisfaction_rate: 60,
        complaint_count: 40,
        service_hours: 6,
        response_time: 20,
        attitude_score: 5.2,
        advantages: '区域内缺少竞争对手',
        suggestions: '需要彻底改进服务流程，加强管理，提高服务质量',
        conclusion: '表现较差，属于D级驿站，需要重点关注改进',
        created_at: '2023-05-11 11:30:00',
        evaluator: '赵评估'
      }
    ]
    
    // 模拟历史数据
    const mockHistoryList = [
      {
        id: 11,
        station_id: 101,
        level: 'B',
        total_score: 72,
        order_score: 75,
        user_score: 68,
        service_score: 73,
        conclusion: '表现一般，需要改进',
        created_at: '2023-01-15 09:30:00',
        evaluator: '张评估'
      },
      {
        id: 12,
        station_id: 101,
        level: 'B',
        total_score: 78,
        order_score: 80,
        user_score: 75,
        service_score: 79,
        conclusion: '有所改进，继续努力',
        created_at: '2023-03-15 10:15:00',
        evaluator: '王评估'
      },
      {
        id: 13,
        station_id: 101,
        level: 'A',
        total_score: 85,
        order_score: 90,
        user_score: 82,
        service_score: 83,
        conclusion: '整体表现优秀，属于A级驿站',
        created_at: '2023-05-15 09:30:00',
        evaluator: '张评估'
      }
    ]
    
    // 方法
    const getList = () => {
      tableLoading.value = true
      
      // 模拟API调用
      setTimeout(() => {
        // 根据查询条件过滤数据
        let filteredList = [...mockList]
        
        if (listQuery.level !== '') {
          filteredList = filteredList.filter(item => item.level === listQuery.level)
        }
        
        if (listQuery.type !== '') {
          filteredList = filteredList.filter(item => item.type === listQuery.type)
        }
        
        if (listQuery.search) {
          filteredList = filteredList.filter(item => 
            item.station_name.includes(listQuery.search)
          )
        }
        
        if (listQuery.start_date && listQuery.end_date) {
          // 简化处理，实际项目中应该使用日期比较
          // 这里只是模拟筛选效果
        }
        
        list.value = filteredList
        total.value = filteredList.length
        
        tableLoading.value = false
      }, 500)
    }
    
    const handleFilter = () => {
      listQuery.page = 1
      getList()
    }
    
    const resetQuery = () => {
      dateRange.value = []
      Object.assign(listQuery, {
        page: 1,
        limit: 10,
        level: '',
        type: '',
        search: '',
        start_date: '',
        end_date: ''
      })
      getList()
    }
    
    const handleDateChange = (val) => {
      if (val) {
        listQuery.start_date = val[0]
        listQuery.end_date = val[1]
      } else {
        listQuery.start_date = ''
        listQuery.end_date = ''
      }
    }
    
    const getTypeText = (type) => {
      const typeMap = {
        1: '社区驿站',
        2: '商圈驿站',
        3: '校园驿站',
        4: '企业驿站',
        5: '其他'
      }
      return typeMap[type] || '未知'
    }
    
    const getTypeTagType = (type) => {
      const typeMap = {
        1: '',  // 默认灰色
        2: 'success',
        3: 'warning',
        4: 'info',
        5: 'danger'
      }
      return typeMap[type] || ''
    }
    
    const getLevelText = (level) => {
      const levelMap = {
        'S': 'S级',
        'A': 'A级',
        'B': 'B级',
        'C': 'C级',
        'D': 'D级'
      }
      return levelMap[level] || '未知'
    }
    
    const getLevelTagType = (level) => {
      const levelMap = {
        'S': 'success',
        'A': 'primary',
        'B': 'info',
        'C': 'warning',
        'D': 'danger'
      }
      return levelMap[level] || 'info'
    }
    
    const getHistoryItemType = (level) => {
      const levelMap = {
        'S': 'success',
        'A': 'primary',
        'B': 'info',
        'C': 'warning',
        'D': 'danger'
      }
      return levelMap[level] || 'info'
    }
    
    const getScoreColor = (score) => {
      if (score >= 90) return '#67C23A' // 绿色
      if (score >= 80) return '#409EFF' // 蓝色
      if (score >= 70) return '#E6A23C' // 黄色
      if (score >= 60) return '#F56C6C' // 红色
      return '#909399' // 灰色
    }
    
    const percentFormat = (percentage) => {
      return `${percentage}分`
    }
    
    const handleDetail = (row) => {
      detailLoading.value = true
      detail.value = { ...row }
      detailDialogVisible.value = true
      
      setTimeout(() => {
        detailLoading.value = false
      }, 300)
    }
    
    const handlePrint = () => {
      ElMessage.success('打印功能开发中')
    }
    
    const handleEvaluate = (row) => {
      // 重置评估表单
      Object.assign(evaluateForm, {
        id: null,
        station_id: row.station_id,
        station_name: row.station_name,
        order_score: 0,
        user_score: 0,
        service_score: 0,
        total_score: 0,
        level: 'C',
        order_count: 0,
        order_on_time_rate: 0,
        order_bad_rate: 0,
        review_count: 0,
        satisfaction_rate: 0,
        complaint_count: 0,
        service_hours: 0,
        response_time: 0,
        attitude_score: 0,
        advantages: '',
        suggestions: '',
        conclusion: ''
      })
      
      evaluateDialogVisible.value = true
    }
    
    const startEvaluation = () => {
      ElMessageBox.prompt('请输入要评估的驿站ID', '开始评估', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d+$/,
        inputErrorMessage: '请输入有效的驿站ID'
      }).then(({ value }) => {
        // 模拟查找驿站
        const station = mockList.find(item => item.station_id == value)
        if (station) {
          handleEvaluate(station)
        } else {
          ElMessage({
            type: 'warning',
            message: `未找到ID为${value}的驿站`
          })
        }
      }).catch(() => {})
    }
    
    const submitEvaluation = () => {
      // 计算综合得分（加权平均）
      evaluateForm.total_score = Math.round((evaluateForm.order_score * 0.4 + evaluateForm.user_score * 0.3 + evaluateForm.service_score * 0.3))
      
      // 根据综合得分自动判断等级
      if (evaluateForm.total_score >= 90) {
        evaluateForm.level = 'S'
      } else if (evaluateForm.total_score >= 80) {
        evaluateForm.level = 'A'
      } else if (evaluateForm.total_score >= 70) {
        evaluateForm.level = 'B'
      } else if (evaluateForm.total_score >= 60) {
        evaluateForm.level = 'C'
      } else {
        evaluateForm.level = 'D'
      }
      
      evaluateLoading.value = true
      
      // 模拟API调用
      setTimeout(() => {
        // 创建新评估记录
        const newEvaluation = {
          id: Date.now(),
          station_id: evaluateForm.station_id,
          station_name: evaluateForm.station_name,
          station_image: mockList.find(item => item.station_id === evaluateForm.station_id)?.station_image,
          address: mockList.find(item => item.station_id === evaluateForm.station_id)?.address,
          type: mockList.find(item => item.station_id === evaluateForm.station_id)?.type,
          level: evaluateForm.level,
          total_score: evaluateForm.total_score,
          order_score: evaluateForm.order_score,
          user_score: evaluateForm.user_score,
          service_score: evaluateForm.service_score,
          order_count: evaluateForm.order_count,
          order_on_time_rate: evaluateForm.order_on_time_rate,
          order_bad_rate: evaluateForm.order_bad_rate,
          review_count: evaluateForm.review_count,
          satisfaction_rate: evaluateForm.satisfaction_rate,
          complaint_count: evaluateForm.complaint_count,
          service_hours: evaluateForm.service_hours,
          response_time: evaluateForm.response_time,
          attitude_score: evaluateForm.attitude_score,
          advantages: evaluateForm.advantages,
          suggestions: evaluateForm.suggestions,
          conclusion: evaluateForm.conclusion,
          created_at: formatDateTime(new Date()),
          evaluator: '当前用户'
        }
        
        // 将新评估添加到列表
        const index = mockList.findIndex(item => item.station_id === evaluateForm.station_id)
        if (index !== -1) {
          mockList[index] = newEvaluation
        } else {
          mockList.push(newEvaluation)
        }
        
        // 刷新列表
        getList()
        
        // 重绘图表
        initChart()
        
        evaluateLoading.value = false
        evaluateDialogVisible.value = false
        
        ElMessage({
          type: 'success',
          message: '评估提交成功'
        })
      }, 800)
    }
    
    const handleHistory = (row) => {
      historyLoading.value = true
      historyDialogVisible.value = true
      
      // 模拟API调用
      setTimeout(() => {
        // 这里只是示例，假设我们只查询station_id为101的驿站的历史记录
        if (row.station_id === 101) {
          historyList.value = [...mockHistoryList]
        } else {
          historyList.value = []
        }
        
        historyLoading.value = false
      }, 500)
    }
    
    const viewHistoryDetail = (item) => {
      detailLoading.value = true
      detail.value = { ...item, 
        station_image: mockList.find(s => s.station_id === item.station_id)?.station_image,
        address: mockList.find(s => s.station_id === item.station_id)?.address,
        type: mockList.find(s => s.station_id === item.station_id)?.type
      }
      historyDialogVisible.value = false
      detailDialogVisible.value = true
      
      setTimeout(() => {
        detailLoading.value = false
      }, 300)
    }
    
    const exportData = () => {
      ElMessage.success('数据导出功能开发中')
    }
    
    // 图表相关方法
    const initChart = () => {
      chartLoading.value = true
      
      // 确保DOM已经渲染
      nextTick(() => {
        // 初始化图表
        if (performanceChart) {
          performanceChart.dispose()
        }
        
        const chartDom = document.getElementById('performanceChart')
        performanceChart = echarts.init(chartDom)
        
        let option = {}
        
        // 根据不同的图表类型设置不同的选项
        if (chartType.value === 'level') {
          option = getLevelChartOption()
        } else if (chartType.value === 'score') {
          option = getScoreChartOption()
        } else if (chartType.value === 'trend') {
          option = getTrendChartOption()
        }
        
        performanceChart.setOption(option)
        chartLoading.value = false
      })
    }
    
    const changeChartType = (type) => {
      chartType.value = type
      initChart()
    }
    
    const getLevelChartOption = () => {
      // 统计各等级驿站数量
      const levelCount = {
        'S': 0,
        'A': 0,
        'B': 0,
        'C': 0,
        'D': 0
      }
      
      mockList.forEach(item => {
        if (levelCount[item.level] !== undefined) {
          levelCount[item.level]++
        }
      })
      
      return {
        title: {
          text: '驿站等级分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['S级', 'A级', 'B级', 'C级', 'D级']
        },
        series: [
          {
            name: '驿站等级',
            type: 'pie',
            radius: '55%',
            center: ['50%', '60%'],
            data: [
              { value: levelCount['S'], name: 'S级', itemStyle: { color: '#67C23A' } },
              { value: levelCount['A'], name: 'A级', itemStyle: { color: '#409EFF' } },
              { value: levelCount['B'], name: 'B级', itemStyle: { color: '#909399' } },
              { value: levelCount['C'], name: 'C级', itemStyle: { color: '#E6A23C' } },
              { value: levelCount['D'], name: 'D级', itemStyle: { color: '#F56C6C' } }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
    }
    
    const getScoreChartOption = () => {
      const scoreData = mockList.map(item => ({
        value: [item.order_score, item.user_score, item.service_score],
        name: item.station_name,
        symbolSize: Math.sqrt(item.total_score) * 2,
        itemStyle: {
          color: getScoreColor(item.total_score)
        }
      }))
      
      return {
        title: {
          text: '驿站评分分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return params.name + '<br/>' +
              '订单处理: ' + params.value[0] + '<br/>' +
              '用户评价: ' + params.value[1] + '<br/>' +
              '服务质量: ' + params.value[2] + '<br/>' +
              '综合评分: ' + mockList.find(item => item.station_name === params.name).total_score
          }
        },
        xAxis: {
          name: '订单处理',
          nameLocation: 'center',
          nameGap: 30,
          min: 0,
          max: 100
        },
        yAxis: {
          name: '用户评价',
          nameLocation: 'center',
          nameGap: 30,
          min: 0,
          max: 100
        },
        series: [{
          name: '驿站评分',
          type: 'scatter',
          data: scoreData
        }]
      }
    }
    
    const getTrendChartOption = () => {
      // 假设我们有月度数据
      const months = ['1月', '2月', '3月', '4月', '5月']
      
      // 假设的趋势数据
      const sData = [0, 0, 1, 1, 1]
      const aData = [1, 2, 2, 3, 3]
      const bData = [3, 3, 2, 1, 1]
      const cData = [1, 0, 0, 0, 0]
      const dData = [0, 0, 0, 0, 0]
      
      return {
        title: {
          text: '驿站等级趋势变化',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['S级', 'A级', 'B级', 'C级', 'D级'],
          bottom: 10
        },
        xAxis: {
          type: 'category',
          data: months
        },
        yAxis: {
          type: 'value',
          name: '站点数量'
        },
        series: [
          {
            name: 'S级',
            type: 'line',
            data: sData,
            itemStyle: { color: '#67C23A' }
          },
          {
            name: 'A级',
            type: 'line',
            data: aData,
            itemStyle: { color: '#409EFF' }
          },
          {
            name: 'B级',
            type: 'line',
            data: bData,
            itemStyle: { color: '#909399' }
          },
          {
            name: 'C级',
            type: 'line',
            data: cData,
            itemStyle: { color: '#E6A23C' }
          },
          {
            name: 'D级',
            type: 'line',
            data: dData,
            itemStyle: { color: '#F56C6C' }
          }
        ]
      }
    }
    
    // 生命周期钩子
    onMounted(() => {
      getList()
      initChart()
      
      // 监听窗口大小变化
      window.addEventListener('resize', () => {
        if (performanceChart) {
          performanceChart.resize()
        }
      })
    })
    
    return {
      formatDateTime,
      dateRange,
      list,
      total,
      tableLoading,
      listQuery,
      levelOptions,
      typeOptions,
      chartType,
      chartLoading,
      detailDialogVisible,
      detailLoading,
      detail,
      evaluateDialogVisible,
      evaluateLoading,
      evaluateForm,
      historyDialogVisible,
      historyLoading,
      historyList,
      getTypeText,
      getTypeTagType,
      getLevelText,
      getLevelTagType,
      getScoreColor,
      getHistoryItemType,
      percentFormat,
      handleFilter,
      resetQuery,
      handleDateChange,
      handleDetail,
      handlePrint,
      handleEvaluate,
      startEvaluation,
      submitEvaluation,
      handleHistory,
      viewHistoryDetail,
      exportData,
      changeChartType
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
  
  .filter-item {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.performance-chart-card {
  margin-bottom: 0;
  
  .chart-container {
    height: 400px;
  }
}

.station-info {
  display: flex;
  align-items: flex-start;
  
  .station-detail {
    margin-left: 10px;
    
    .station-name {
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .station-address {
      color: #606266;
      font-size: 13px;
      margin-bottom: 5px;
    }
  }
}

.score-box {
  .score-value {
    font-size: 16px;
    font-weight: bold;
    color: #409eff;
  }
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

.performance-detail-dialog {
  min-height: 200px;
  
  .station-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .station-info-large {
      display: flex;
      align-items: flex-start;
      
      .station-detail-large {
        margin-left: 15px;
        
        .station-name-large {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 8px;
        }
        
        .station-address {
          color: #606266;
          font-size: 14px;
          margin-bottom: 8px;
        }
      }
    }
    
    .score-circle {
      .circle-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .circle-value {
          font-size: 24px;
          font-weight: bold;
          color: #409eff;
        }
        
        .circle-text {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
  
  .score-details {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
    
    .score-item {
      flex: 1;
      min-width: 200px;
      
      .score-item-title {
        font-weight: bold;
        margin-bottom: 10px;
      }
      
      .score-item-info {
        margin-top: 10px;
        font-size: 13px;
        color: #606266;
        line-height: 1.6;
      }
    }
  }
  
  .evaluation-report {
    margin-top: 20px;
    
    .report-item {
      margin-bottom: 15px;
      
      .report-title {
        font-weight: bold;
        margin-bottom: 8px;
      }
      
      .report-content {
        padding: 10px;
        background-color: #f5f7fa;
        border-radius: 4px;
        font-size: 14px;
        color: #606266;
        line-height: 1.6;
      }
    }
  }
}

.score-input-group {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.history-dialog {
  .history-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  
  .history-item-scores {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 10px;
    font-size: 13px;
    color: #606266;
  }
  
  .history-item-report {
    margin-bottom: 10px;
    font-size: 14px;
  }
  
  .history-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 13px;
    color: #909399;
  }
  
  .history-score {
    font-weight: bold;
    color: #409eff;
  }
  
  .empty-data {
    padding: 30px 0;
  }
}
</style> 