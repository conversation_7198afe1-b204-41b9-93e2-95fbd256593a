<template>
  <div class="page-container">
    <div class="action-container">
      <div class="search-box">
        <el-input v-model="queryParams.keyword" placeholder="请输入站点名称/负责人" clearable style="width: 200px" class="mr-10" />
        <el-select v-model="queryParams.status" placeholder="站点状态" clearable class="mr-10" style="width: 120px">
          <el-option label="运营中" value="1" />
          <el-option label="已关闭" value="0" />
          <el-option label="待审核" value="2" />
        </el-select>
        <el-select v-model="queryParams.type" placeholder="站点类型" clearable class="mr-10" style="width: 120px">
          <el-option label="快递驿站" value="1" />
          <el-option label="社区驿站" value="2" />
          <el-option label="校园驿站" value="3" />
          <el-option label="写字楼驿站" value="4" />
        </el-select>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </div>
      <div class="button-group">
        <el-button type="primary" @click="handleAdd">新增站点</el-button>
        <el-button type="success" @click="handleExport">导出</el-button>
      </div>
    </div>
    
    <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane label="站点列表" name="list">
        <el-table v-loading="loading" :data="stationList">
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="stationName" label="站点名称" min-width="120" show-overflow-tooltip />
          <el-table-column prop="stationType" label="站点类型" width="100">
            <template #default="scope">
              {{ stationTypeMap[scope.row.stationType] }}
            </template>
          </el-table-column>
          <el-table-column prop="address" label="地址" min-width="180" show-overflow-tooltip />
          <el-table-column prop="managerName" label="负责人" width="100" />
          <el-table-column prop="phone" label="联系电话" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === '1' ? 'success' : scope.row.status === '2' ? 'warning' : 'info'">
                {{ statusMap[scope.row.status] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="orderCount" label="订单数" width="80" sortable />
          <el-table-column prop="createTime" label="创建时间" width="160" sortable />
          <el-table-column label="操作" width="260" fixed="right">
            <template #default="scope">
              <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="text" @click="handleDetail(scope.row)">详情</el-button>
              <el-button v-if="scope.row.status === '2'" type="text" @click="handleApprove(scope.row)">审核</el-button>
              <el-button v-if="scope.row.status === '1'" type="text" @click="handleStatus(scope.row, '0')">关闭</el-button>
              <el-button v-if="scope.row.status === '0'" type="text" @click="handleStatus(scope.row, '1')">开启</el-button>
              <el-button type="text" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="站点监控" name="monitor">
        <div class="monitor-container">
          <div class="monitor-header">
            <h3>站点状态监控</h3>
            <div class="refresh-area">
              <span>上次刷新时间：{{ refreshTime }}</span>
              <el-button type="primary" size="small" @click="refreshMonitor">刷新</el-button>
            </div>
          </div>
          
          <div class="monitor-stats">
            <div class="stat-card primary">
              <div class="stat-icon"><i class="el-icon-office-building"></i></div>
              <div class="stat-info">
                <div class="stat-title">站点总数</div>
                <div class="stat-value">{{ monitorData.total }}</div>
              </div>
            </div>
            
            <div class="stat-card success">
              <div class="stat-icon"><i class="el-icon-circle-check"></i></div>
              <div class="stat-info">
                <div class="stat-title">运营中</div>
                <div class="stat-value">{{ monitorData.active }}</div>
              </div>
            </div>
            
            <div class="stat-card warning">
              <div class="stat-icon"><i class="el-icon-time"></i></div>
              <div class="stat-info">
                <div class="stat-title">待审核</div>
                <div class="stat-value">{{ monitorData.pending }}</div>
              </div>
            </div>
            
            <div class="stat-card danger">
              <div class="stat-icon"><i class="el-icon-circle-close"></i></div>
              <div class="stat-info">
                <div class="stat-title">已关闭</div>
                <div class="stat-value">{{ monitorData.closed }}</div>
              </div>
            </div>
          </div>
          
          <div class="monitor-map">
            <h4>站点地理分布</h4>
            <div class="map-placeholder">地图组件将在此显示 - 需要集成地图组件如高德地图或百度地图</div>
          </div>
          
          <div class="monitor-alert">
            <h4>异常站点列表</h4>
            <el-table :data="alertStations" style="width: 100%">
              <el-table-column type="index" label="#" width="50" />
              <el-table-column prop="stationName" label="站点名称" min-width="120" />
              <el-table-column prop="alertType" label="异常类型">
                <template #default="scope">
                  <el-tag type="danger">{{ scope.row.alertType }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="alertTime" label="异常时间" width="160" />
              <el-table-column prop="duration" label="持续时间" width="120" />
              <el-table-column label="操作" width="150">
                <template #default="scope">
                  <el-button type="text" @click="handleAlertDetail(scope.row)">查看详情</el-button>
                  <el-button type="text" @click="handleResolve(scope.row)">标记解决</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 添加/修改站点弹窗 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="700px" append-to-body>
      <el-form ref="stationForm" :model="stationForm" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="站点名称" prop="stationName">
              <el-input v-model="stationForm.stationName" placeholder="请输入站点名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="站点类型" prop="stationType">
              <el-select v-model="stationForm.stationType" placeholder="请选择站点类型" style="width: 100%">
                <el-option label="快递驿站" value="1" />
                <el-option label="社区驿站" value="2" />
                <el-option label="校园驿站" value="3" />
                <el-option label="写字楼驿站" value="4" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="负责人" prop="managerName">
              <el-input v-model="stationForm.managerName" placeholder="请输入负责人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="stationForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="地址" prop="address">
          <el-input v-model="stationForm.address" placeholder="请输入详细地址" />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input v-model="stationForm.longitude" placeholder="请输入经度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input v-model="stationForm.latitude" placeholder="请输入纬度" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="营业时间" required>
          <el-col :span="11">
            <el-form-item prop="openTime">
              <el-time-picker v-model="stationForm.openTime" placeholder="开始时间" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="text-center">至</el-col>
          <el-col :span="11">
            <el-form-item prop="closeTime">
              <el-time-picker v-model="stationForm.closeTime" placeholder="结束时间" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="stationForm.status">
            <el-radio label="1">运营中</el-radio>
            <el-radio label="0">已关闭</el-radio>
            <el-radio label="2">待审核</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input v-model="stationForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 站点详情弹窗 -->
    <el-dialog title="站点详情" v-model="detailVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="站点名称">{{ currentStation.stationName }}</el-descriptions-item>
        <el-descriptions-item label="站点类型">{{ stationTypeMap[currentStation.stationType] }}</el-descriptions-item>
        <el-descriptions-item label="负责人">{{ currentStation.managerName }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ currentStation.phone }}</el-descriptions-item>
        <el-descriptions-item label="地址" :span="2">{{ currentStation.address }}</el-descriptions-item>
        <el-descriptions-item label="营业时间">{{ currentStation.openTime }} - {{ currentStation.closeTime }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="currentStation.status === '1' ? 'success' : currentStation.status === '2' ? 'warning' : 'info'">
            {{ statusMap[currentStation.status] }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="订单总数">{{ currentStation.orderCount }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentStation.createTime }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ currentStation.remark }}</el-descriptions-item>
      </el-descriptions>
      
      <div class="detail-charts mt-20">
        <el-tabs>
          <el-tab-pane label="订单统计">
            <div class="chart-placeholder">订单统计图表</div>
          </el-tab-pane>
          <el-tab-pane label="收入统计">
            <div class="chart-placeholder">收入统计图表</div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
    
    <!-- 审核弹窗 -->
    <el-dialog title="站点审核" v-model="approveVisible" width="500px" append-to-body>
      <el-form ref="approveForm" :model="approveForm" label-width="100px">
        <el-form-item label="审核结果" prop="approveStatus">
          <el-radio-group v-model="approveForm.approveStatus">
            <el-radio label="1">通过</el-radio>
            <el-radio label="0">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="approveComment">
          <el-input v-model="approveForm.approveComment" type="textarea" placeholder="请输入审核意见" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="approveVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitApprove">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'StationList',
  data() {
    return {
      // 激活的标签页
      activeName: 'list',
      // 遮罩层
      loading: false,
      // 总条数
      total: 0,
      // 刷新时间
      refreshTime: '2023-04-16 12:34:56',
      // 站点类型映射
      stationTypeMap: {
        '1': '快递驿站',
        '2': '社区驿站',
        '3': '校园驿站',
        '4': '写字楼驿站'
      },
      // 状态映射
      statusMap: {
        '0': '已关闭',
        '1': '运营中',
        '2': '待审核'
      },
      // 站点表格数据
      stationList: [
        {
          id: 1,
          stationName: '海淀区中关村社区驿站',
          stationType: '2',
          managerName: '张三',
          phone: '13800138001',
          address: '北京市海淀区中关村大街123号',
          longitude: '116.3252',
          latitude: '39.9841',
          openTime: '08:00',
          closeTime: '22:00',
          status: '1',
          orderCount: 256,
          createTime: '2023-01-01 10:00:00',
          remark: '中关村科技园区内的驿站'
        },
        {
          id: 2,
          stationName: '北京大学校内驿站',
          stationType: '3',
          managerName: '李四',
          phone: '13800138002',
          address: '北京市海淀区颐和园路5号',
          longitude: '116.3045',
          latitude: '39.9869',
          openTime: '08:00',
          closeTime: '21:00',
          status: '1',
          orderCount: 198,
          createTime: '2023-01-02 09:30:00',
          remark: '北京大学校内驿站，主要服务校内师生'
        },
        {
          id: 3,
          stationName: '望京SOHO快递驿站',
          stationType: '1',
          managerName: '王五',
          phone: '13800138003',
          address: '北京市朝阳区望京SOHO T1',
          longitude: '116.4825',
          latitude: '39.9946',
          openTime: '07:30',
          closeTime: '22:30',
          status: '2',
          orderCount: 0,
          createTime: '2023-01-05 15:20:00',
          remark: '望京SOHO写字楼快递服务驿站'
        }
      ],
      // 异常站点列表
      alertStations: [
        {
          id: 1,
          stationName: '海淀区中关村社区驿站',
          alertType: '长时间无订单',
          alertTime: '2023-04-16 10:30:00',
          duration: '2小时30分钟'
        },
        {
          id: 3,
          stationName: '望京SOHO快递驿站',
          alertType: '审核超时',
          alertTime: '2023-04-15 14:20:00',
          duration: '1天2小时'
        }
      ],
      // 监控数据
      monitorData: {
        total: 358,
        active: 312,
        pending: 25,
        closed: 21
      },
      // 弹出层标题
      dialogTitle: '',
      // 是否显示弹出层
      dialogVisible: false,
      // 是否显示详情弹出层
      detailVisible: false,
      // 是否显示审核弹出层
      approveVisible: false,
      // 当前操作的站点
      currentStation: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        status: '',
        type: ''
      },
      // 表单参数
      stationForm: {
        id: undefined,
        stationName: undefined,
        stationType: undefined,
        managerName: undefined,
        phone: undefined,
        address: undefined,
        longitude: undefined,
        latitude: undefined,
        openTime: undefined,
        closeTime: undefined,
        status: '1',
        remark: undefined
      },
      // 审核表单
      approveForm: {
        id: undefined,
        approveStatus: '1',
        approveComment: undefined
      },
      // 表单校验
      rules: {
        stationName: [
          { required: true, message: '站点名称不能为空', trigger: 'blur' }
        ],
        stationType: [
          { required: true, message: '站点类型不能为空', trigger: 'change' }
        ],
        managerName: [
          { required: true, message: '负责人不能为空', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '联系电话不能为空', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        address: [
          { required: true, message: '地址不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /** 获取站点列表数据 */
    getList() {
      this.loading = true;
      import('@/api/station').then(module => {
        module.getStationList(this.queryParams).then(response => {
          if (response.success) {
            this.stationList = response.data.list || [];
            this.total = response.data.total;
          } else {
            this.$message.error(response.message || '获取数据失败');
            // 如果API调用失败，使用模拟数据
            console.warn('使用模拟数据');
          }
        }).catch(error => {
          console.error('获取站点列表失败', error);
          this.$message.error('获取数据异常，请刷新页面重试');
        }).finally(() => {
          this.loading = false;
        });
      });
    },
    /** 查询站点列表 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置查询操作 */
    resetQuery() {
      this.queryParams.keyword = ''
      this.queryParams.status = ''
      this.queryParams.type = ''
      this.handleQuery()
    },
    /** 切换标签页 */
    handleTabClick() {
      if (this.activeName === 'monitor') {
        this.refreshMonitor()
      }
    },
    /** 刷新监控数据 */
    refreshMonitor() {
      // TODO: 调用API获取最新监控数据
      this.refreshTime = new Date().toLocaleString()
      console.log('刷新监控数据')
    },
    /** 新增站点 */
    handleAdd() {
      this.dialogTitle = '添加站点'
      this.dialogVisible = true
      this.resetForm()
    },
    /** 修改站点 */
    handleEdit(row) {
      this.dialogTitle = '修改站点'
      this.dialogVisible = true
      this.stationForm = { ...row }
    },
    /** 查看站点详情 */
    handleDetail(row) {
      this.currentStation = row
      this.detailVisible = true
    },
    /** 站点审核 */
    handleApprove(row) {
      this.currentStation = row
      this.approveForm.id = row.id
      this.approveVisible = true
    },
    /** 更改站点状态 */
    handleStatus(row, status) {
      const statusText = status === '0' ? '关闭' : '开启'
      this.$confirm(`确认要${statusText}站点"${row.stationName}"吗?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用API更改状态
        console.log(`${statusText}站点ID：`, row.id)
      }).catch(() => {})
    },
    /** 删除站点 */
    handleDelete(row) {
      this.$confirm(`是否确认删除站点"${row.stationName}"?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用删除API - 使用测试用的API路径
        import('@/api/station').then(module => {
          // 使用测试删除函数
          module.testDeleteStation(row.id).then(response => {
            if (response.success) {
              this.$message.success('删除成功');
              this.getList();
            } else {
              this.$message.error(response.message || '删除失败');
            }
          }).catch(error => {
            console.error('删除站点失败', error);
            this.$message.error('删除操作异常，请重试');
          });
        });
      }).catch(() => {});
    },
    /** 导出站点数据 */
    handleExport() {
      // TODO: 调用导出API
      console.log('导出站点数据')
    },
    /** 查看告警详情 */
    handleAlertDetail(row) {
      // TODO: 查看告警详情
      console.log('查看告警详情：', row)
    },
    /** 标记告警已解决 */
    handleResolve(row) {
      // TODO: 标记告警已解决
      console.log('标记告警已解决：', row)
    },
    /** 提交表单 */
    submitForm() {
      this.$refs.stationForm.validate(valid => {
        if (valid) {
          if (this.stationForm.id) {
            // TODO: 调用更新API
            console.log('更新站点：', this.stationForm)
          } else {
            // TODO: 调用新增API
            console.log('新增站点：', this.stationForm)
          }
          this.dialogVisible = false
        }
      })
    },
    /** 提交审核 */
    submitApprove() {
      // TODO: 调用审核API
      console.log('审核结果：', this.approveForm)
      this.approveVisible = false
    },
    /** 重置表单 */
    resetForm() {
      this.stationForm = {
        id: undefined,
        stationName: undefined,
        stationType: undefined,
        managerName: undefined,
        phone: undefined,
        address: undefined,
        longitude: undefined,
        latitude: undefined,
        openTime: undefined,
        closeTime: undefined,
        status: '1',
        remark: undefined
      }
    },
    /** 分页大小变化 */
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.handleQuery()
    },
    /** 分页页码变化 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.handleQuery()
    }
  }
}
</script>

<style lang="scss" scoped>
.action-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .search-box {
    display: flex;
    align-items: center;
  }
  
  .button-group {
    display: flex;
    align-items: center;
  }
}

.mr-10 {
  margin-right: 10px;
}

.text-center {
  text-align: center;
  line-height: 32px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.monitor-container {
  .monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
    }
    
    .refresh-area {
      display: flex;
      align-items: center;
      
      span {
        margin-right: 10px;
        color: #909399;
      }
    }
  }
  
  .monitor-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    
    .stat-card {
      flex: 1;
      margin: 0 10px;
      padding: 20px;
      border-radius: 4px;
      background-color: #fff;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      
      &:first-child {
        margin-left: 0;
      }
      
      &:last-child {
        margin-right: 0;
      }
      
      &.primary {
        border-left: 4px solid #409EFF;
        .stat-icon {
          color: #409EFF;
        }
      }
      
      &.success {
        border-left: 4px solid #67C23A;
        .stat-icon {
          color: #67C23A;
        }
      }
      
      &.warning {
        border-left: 4px solid #E6A23C;
        .stat-icon {
          color: #E6A23C;
        }
      }
      
      &.danger {
        border-left: 4px solid #F56C6C;
        .stat-icon {
          color: #F56C6C;
        }
      }
      
      .stat-icon {
        font-size: 36px;
        margin-right: 15px;
      }
      
      .stat-info {
        .stat-title {
          font-size: 14px;
          color: #909399;
          margin-bottom: 5px;
        }
        
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
      }
    }
  }
  
  .monitor-map, .monitor-alert {
    background-color: #fff;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    h4 {
      margin-top: 0;
      margin-bottom: 15px;
      color: #303133;
    }
  }
  
  .map-placeholder {
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #909399;
  }
}

.detail-charts {
  .chart-placeholder {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #909399;
  }
}

.mt-20 {
  margin-top: 20px;
}
</style> 