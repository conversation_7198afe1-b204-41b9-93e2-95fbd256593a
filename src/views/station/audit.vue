<template>
  <div class="station-audit-container">
    <div class="page-header">
      <h2><i class="el-icon-s-check"></i> 驿站审核</h2>
      <p>审核驿站申请，管理驿站认证状态</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon total">
          <i class="el-icon-s-shop"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total }}</div>
          <div class="stat-label">总申请数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon pending">
          <i class="el-icon-time"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.pending }}</div>
          <div class="stat-label">待审核</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon approved">
          <i class="el-icon-check"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.approved }}</div>
          <div class="stat-label">已通过</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon rejected">
          <i class="el-icon-close"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.rejected }}</div>
          <div class="stat-label">已拒绝</div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="驿站名称">
          <el-input v-model="searchForm.name" placeholder="请输入驿站名称" clearable />
        </el-form-item>
        <el-form-item label="负责人">
          <el-input v-model="searchForm.ownerName" placeholder="请输入负责人姓名" clearable />
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="stationList"
        style="width: 100%"
      >
        <el-table-column prop="id" label="驿站ID" width="80" />
        <el-table-column label="驿站信息" min-width="200">
          <template #default="scope">
            <div class="station-info">
              <div class="station-name">{{ scope.row.name }}</div>
              <div class="station-address">{{ scope.row.address }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getTypeColor(scope.row.type)">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="owner_name" label="负责人" width="100" />
        <el-table-column prop="phone" label="联系电话" width="120" />
        <el-table-column prop="status" label="审核状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="申请时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              <i class="el-icon-view"></i> 查看
            </el-button>
            <el-button 
              v-if="scope.row.status === 'pending'" 
              type="text" 
              size="small" 
              @click="handleAudit(scope.row)"
            >
              <i class="el-icon-check"></i> 审核
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="auditDialogVisible"
      title="驿站审核"
      width="800px"
    >
      <div v-if="currentStation" class="station-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="驿站名称">{{ currentStation.name }}</el-descriptions-item>
          <el-descriptions-item label="驿站类型">{{ getTypeText(currentStation.type) }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ currentStation.owner_name }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ currentStation.phone }}</el-descriptions-item>
          <el-descriptions-item label="驿站地址" :span="2">{{ currentStation.address }}</el-descriptions-item>
          <el-descriptions-item label="营业时间" :span="2">{{ currentStation.business_hours || '-' }}</el-descriptions-item>
          <el-descriptions-item label="驿站描述" :span="2">{{ currentStation.description || '-' }}</el-descriptions-item>
          <el-descriptions-item label="申请时间" :span="2">{{ formatTime(currentStation.created_at) }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="audit-form" style="margin-top: 20px;">
          <el-form :model="auditForm" label-width="80px">
            <el-form-item label="审核结果">
              <el-radio-group v-model="auditForm.status">
                <el-radio label="approved">通过</el-radio>
                <el-radio label="rejected">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="审核备注">
              <el-input
                v-model="auditForm.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入审核备注"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAudit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'StationAudit',
  data() {
    return {
      loading: false,
      stationList: [],
      searchForm: {
        name: '',
        ownerName: '',
        status: ''
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      stats: {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0
      },
      auditDialogVisible: false,
      currentStation: null,
      auditForm: {
        status: 'approved',
        remark: ''
      }
    }
  },
  mounted() {
    this.loadStats()
    this.loadStationList()
  },
  methods: {
    async loadStats() {
      try {
        const response = await this.$http.get('/station/stats/overview')
        if (response.data.code === 0) {
          this.stats = response.data.data
        }
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    },
    
    async loadStationList() {
      this.loading = true
      try {
        const response = await this.$http.get('/station/list', { 
          params: this.getSearchParams() 
        })
        
        if (response.data.code === 0) {
          this.stationList = response.data.data.list
          this.pagination.total = response.data.data.total
        } else {
          this.$message.error(response.data.message || '获取驿站列表失败')
        }
      } catch (error) {
        console.error('加载驿站列表失败:', error)
        this.$message.error('加载驿站列表失败')
      } finally {
        this.loading = false
      }
    },
    
    getSearchParams() {
      const params = {
        page: this.pagination.page,
        limit: this.pagination.limit
      }
      
      if (this.searchForm.name) {
        params.keyword = this.searchForm.name
      }
      if (this.searchForm.status) {
        params.status = this.searchForm.status
      }
      
      return params
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadStationList()
    },
    
    handleReset() {
      this.searchForm = {
        name: '',
        ownerName: '',
        status: ''
      }
      this.pagination.page = 1
      this.loadStationList()
    },
    
    handleView(station) {
      this.currentStation = station
      this.auditDialogVisible = true
    },
    
    handleAudit(station) {
      this.currentStation = station
      this.auditForm = {
        status: 'approved',
        remark: ''
      }
      this.auditDialogVisible = true
    },
    
    async submitAudit() {
      try {
        const response = await this.$http.post(`/station/${this.currentStation.id}/approve`, this.auditForm)
        
        if (response.data.code === 0) {
          this.$message.success('审核成功')
          this.auditDialogVisible = false
          this.loadStationList()
          this.loadStats()
        } else {
          this.$message.error(response.data.message || '审核失败')
        }
      } catch (error) {
        console.error('审核失败:', error)
        this.$message.error('审核失败')
      }
    },
    
    handleSizeChange(size) {
      this.pagination.limit = size
      this.loadStationList()
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadStationList()
    },
    
    getTypeColor(type) {
      const colorMap = {
        community: 'primary',
        campus: 'success',
        office: 'warning',
        commercial: 'info'
      }
      return colorMap[type] || 'info'
    },
    
    getTypeText(type) {
      const textMap = {
        community: '社区驿站',
        campus: '校园驿站',
        office: '写字楼驿站',
        commercial: '商业驿站'
      }
      return textMap[type] || '未知'
    },
    
    getStatusColor(status) {
      const colorMap = {
        pending: 'warning',
        approved: 'success',
        rejected: 'danger'
      }
      return colorMap[status] || 'info'
    },
    
    getStatusText(status) {
      const textMap = {
        pending: '待审核',
        approved: '已通过',
        rejected: '已拒绝'
      }
      return textMap[status] || '未知'
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.station-audit-container {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        
        &.total { background: #909399; }
        &.pending { background: #E6A23C; }
        &.approved { background: #67C23A; }
        &.rejected { background: #F56C6C; }
      }
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .filter-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .station-info {
      .station-name {
        font-weight: 500;
        color: #303133;
      }
      
      .station-address {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
  }
  
  .pagination-container {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #EBEEF5;
  }
  
  .station-detail {
    padding: 20px 0;
  }
}
</style>
