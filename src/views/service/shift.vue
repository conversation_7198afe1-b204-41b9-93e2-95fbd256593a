<template>
  <div class="shift-container">
    <div class="page-header">
      <h2><i class="el-icon-alarm-clock"></i> 顶班服务管理</h2>
      <p>管理驿站顶班服务信息</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon total">
          <i class="el-icon-time"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total }}</div>
          <div class="stat-label">总顶班数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon request">
          <i class="el-icon-question"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.request }}</div>
          <div class="stat-label">请求顶班</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon offer">
          <i class="el-icon-check"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.offer }}</div>
          <div class="stat-label">提供顶班</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon completed">
          <i class="el-icon-success"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="顶班标题">
          <el-input v-model="searchForm.title" placeholder="请输入顶班标题" clearable />
        </el-form-item>
        <el-form-item label="顶班类型">
          <el-select v-model="searchForm.shiftType" placeholder="请选择" clearable>
            <el-option label="请求顶班" value="request_cover" />
            <el-option label="提供顶班" value="offer_cover" />
          </el-select>
        </el-form-item>
        <el-form-item label="紧急程度">
          <el-select v-model="searchForm.urgencyLevel" placeholder="请选择" clearable>
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="待审核" value="pending" />
            <el-option label="已发布" value="published" />
            <el-option label="已申请" value="applied" />
            <el-option label="已确认" value="confirmed" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="shiftList"
        style="width: 100%"
      >
        <el-table-column prop="cover_no" label="顶班编号" width="140" />
        <el-table-column prop="title" label="顶班标题" min-width="200" />
        <el-table-column prop="shift_type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getShiftTypeColor(scope.row.shift_type)">
              {{ getShiftTypeText(scope.row.shift_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="urgency_level" label="紧急程度" width="100">
          <template #default="scope">
            <el-tag :type="getUrgencyColor(scope.row.urgency_level)" size="small">
              {{ getUrgencyText(scope.row.urgency_level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="顶班时间" width="180">
          <template #default="scope">
            <div class="shift-time">
              <div>{{ scope.row.shift_date }}</div>
              <div class="time-range">{{ scope.row.start_time }}-{{ scope.row.end_time }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="时长(小时)" width="100" />
        <el-table-column label="报酬" width="100">
          <template #default="scope">
            <div class="payment">
              <span v-if="scope.row.payment_type === 'free'">免费</span>
              <span v-else-if="scope.row.payment_type === 'negotiable'">面议</span>
              <span v-else>¥{{ formatMoney(scope.row.payment_amount) }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="requester_name" label="发布者" width="100" />
        <el-table-column prop="view_count" label="浏览量" width="80" />
        <el-table-column prop="application_count" label="申请数" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="published_at" label="发布时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.published_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              <i class="el-icon-view"></i> 查看
            </el-button>
            <el-button 
              v-if="scope.row.status === 'pending'" 
              type="text" 
              size="small" 
              @click="handleApprove(scope.row)"
            >
              <i class="el-icon-check"></i> 审核
            </el-button>
            <el-button type="text" size="small" class="danger" @click="handleDelete(scope.row)">
              <i class="el-icon-delete"></i> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ServiceShift',
  data() {
    return {
      loading: false,
      shiftList: [],
      searchForm: {
        title: '',
        shiftType: '',
        urgencyLevel: '',
        status: ''
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      stats: {
        total: 0,
        request: 0,
        offer: 0,
        completed: 0
      }
    }
  },
  mounted() {
    this.loadStats()
    this.loadShiftList()
  },
  methods: {
    async loadStats() {
      // 暂时使用模拟数据
      this.stats = {
        total: 0,
        request: 0,
        offer: 0,
        completed: 0
      }
    },
    
    async loadShiftList() {
      this.loading = true
      try {
        // 这里调用API获取顶班列表
        // const response = await this.$http.get('/shift-cover/list', { params: this.getSearchParams() })
        
        // 暂时使用空数据
        this.shiftList = []
        this.pagination.total = 0
      } catch (error) {
        console.error('加载顶班列表失败:', error)
        this.$message.error('加载顶班列表失败')
      } finally {
        this.loading = false
      }
    },
    
    getSearchParams() {
      const params = {
        page: this.pagination.page,
        limit: this.pagination.limit
      }
      
      if (this.searchForm.title) {
        params.title = this.searchForm.title
      }
      if (this.searchForm.shiftType) {
        params.shiftType = this.searchForm.shiftType
      }
      if (this.searchForm.urgencyLevel) {
        params.urgencyLevel = this.searchForm.urgencyLevel
      }
      if (this.searchForm.status) {
        params.status = this.searchForm.status
      }
      
      return params
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadShiftList()
    },
    
    handleReset() {
      this.searchForm = {
        title: '',
        shiftType: '',
        urgencyLevel: '',
        status: ''
      }
      this.pagination.page = 1
      this.loadShiftList()
    },
    
    handleView(shift) {
      this.$message.info('查看顶班详情功能开发中')
    },
    
    handleApprove(shift) {
      this.$confirm('确定要审核通过这个顶班服务吗？', '审核确认', {
        confirmButtonText: '通过',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        shift.status = 'published'
        this.$message.success('审核通过')
      })
    },
    
    handleDelete(shift) {
      this.$confirm('确定要删除这个顶班服务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      })
    },
    
    handleSizeChange(size) {
      this.pagination.limit = size
      this.loadShiftList()
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadShiftList()
    },
    
    getShiftTypeColor(type) {
      const colorMap = {
        request_cover: 'warning',
        offer_cover: 'success'
      }
      return colorMap[type] || ''
    },
    
    getShiftTypeText(type) {
      const textMap = {
        request_cover: '请求顶班',
        offer_cover: '提供顶班'
      }
      return textMap[type] || '未知'
    },
    
    getUrgencyColor(level) {
      const colorMap = {
        low: 'info',
        medium: 'primary',
        high: 'warning',
        urgent: 'danger'
      }
      return colorMap[level] || ''
    },
    
    getUrgencyText(level) {
      const textMap = {
        low: '低',
        medium: '中',
        high: '高',
        urgent: '紧急'
      }
      return textMap[level] || '未知'
    },
    
    getStatusColor(status) {
      const colorMap = {
        pending: 'warning',
        published: 'primary',
        applied: 'info',
        confirmed: 'success',
        in_progress: 'primary',
        completed: 'success',
        cancelled: 'danger'
      }
      return colorMap[status] || 'info'
    },
    
    getStatusText(status) {
      const textMap = {
        pending: '待审核',
        published: '已发布',
        applied: '已申请',
        confirmed: '已确认',
        in_progress: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return textMap[status] || '未知'
    },
    
    formatMoney(amount) {
      if (!amount) return '0.00'
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount)
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.shift-container {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        
        &.total { background: #909399; }
        &.request { background: #E6A23C; }
        &.offer { background: #67C23A; }
        &.completed { background: #409EFF; }
      }
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .filter-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .shift-time {
      .time-range {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
    
    .payment {
      font-weight: 500;
      color: #F56C6C;
    }
    
    .danger {
      color: #F56C6C;
    }
  }
  
  .pagination-container {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #EBEEF5;
  }
}
</style>
