<template>
  <div class="job-container">
    <div class="page-header">
      <h2><i class="el-icon-suitcase"></i> 招聘求职管理</h2>
      <p>管理驿站招聘和求职信息</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon total">
          <i class="el-icon-user-solid"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total }}</div>
          <div class="stat-label">总职位数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon recruitment">
          <i class="el-icon-s-custom"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.recruitment }}</div>
          <div class="stat-label">招聘职位</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon seeking">
          <i class="el-icon-search"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.seeking }}</div>
          <div class="stat-label">求职信息</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon applications">
          <i class="el-icon-document"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.applications }}</div>
          <div class="stat-label">总申请数</div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="职位标题">
          <el-input v-model="searchForm.title" placeholder="请输入职位标题" clearable />
        </el-form-item>
        <el-form-item label="职位类型">
          <el-select v-model="searchForm.jobType" placeholder="请选择" clearable>
            <el-option label="招聘" value="recruitment" />
            <el-option label="求职" value="job_seeking" />
          </el-select>
        </el-form-item>
        <el-form-item label="职位类别">
          <el-select v-model="searchForm.category" placeholder="请选择" clearable>
            <el-option label="站长" value="station_manager" />
            <el-option label="店员" value="clerk" />
            <el-option label="配送员" value="delivery" />
            <el-option label="客服" value="customer_service" />
            <el-option label="维修" value="maintenance" />
            <el-option label="兼职" value="part_time" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="工作类型">
          <el-select v-model="searchForm.workType" placeholder="请选择" clearable>
            <el-option label="全职" value="full_time" />
            <el-option label="兼职" value="part_time" />
            <el-option label="临时" value="temporary" />
            <el-option label="实习" value="internship" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="待审核" value="pending" />
            <el-option label="已发布" value="published" />
            <el-option label="招聘中" value="recruiting" />
            <el-option label="已暂停" value="paused" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="jobList"
        style="width: 100%"
      >
        <el-table-column prop="job_no" label="职位编号" width="140" />
        <el-table-column prop="title" label="职位标题" min-width="200" />
        <el-table-column prop="job_type" label="类型" width="80">
          <template #default="scope">
            <el-tag :type="getJobTypeColor(scope.row.job_type)" size="small">
              {{ getJobTypeText(scope.row.job_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="职位类别" width="100">
          <template #default="scope">
            <el-tag :type="getCategoryColor(scope.row.category)">
              {{ getCategoryText(scope.row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="work_type" label="工作类型" width="80">
          <template #default="scope">
            <el-tag :type="getWorkTypeColor(scope.row.work_type)" size="small">
              {{ getWorkTypeText(scope.row.work_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="薪资" width="120">
          <template #default="scope">
            <div class="salary">
              <span v-if="scope.row.salary_min && scope.row.salary_max">
                ¥{{ formatMoney(scope.row.salary_min) }}-{{ formatMoney(scope.row.salary_max) }}
              </span>
              <span v-else-if="scope.row.salary_min">
                ¥{{ formatMoney(scope.row.salary_min) }}起
              </span>
              <span v-else>面议</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="publisher_name" label="发布者" width="100" />
        <el-table-column prop="view_count" label="浏览量" width="80" />
        <el-table-column prop="application_count" label="申请数" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="published_at" label="发布时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.published_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              <i class="el-icon-view"></i> 查看
            </el-button>
            <el-button 
              v-if="scope.row.status === 'pending'" 
              type="text" 
              size="small" 
              @click="handleApprove(scope.row)"
            >
              <i class="el-icon-check"></i> 审核
            </el-button>
            <el-button type="text" size="small" class="danger" @click="handleDelete(scope.row)">
              <i class="el-icon-delete"></i> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ServiceJob',
  data() {
    return {
      loading: false,
      jobList: [],
      searchForm: {
        title: '',
        jobType: '',
        category: '',
        workType: '',
        status: ''
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      stats: {
        total: 0,
        recruitment: 0,
        seeking: 0,
        applications: 0
      }
    }
  },
  mounted() {
    this.loadStats()
    this.loadJobList()
  },
  methods: {
    async loadStats() {
      // 暂时使用模拟数据
      this.stats = {
        total: 0,
        recruitment: 0,
        seeking: 0,
        applications: 0
      }
    },
    
    async loadJobList() {
      this.loading = true
      try {
        // 这里调用API获取职位列表
        // const response = await this.$http.get('/job/list', { params: this.getSearchParams() })
        
        // 暂时使用空数据
        this.jobList = []
        this.pagination.total = 0
      } catch (error) {
        console.error('加载职位列表失败:', error)
        this.$message.error('加载职位列表失败')
      } finally {
        this.loading = false
      }
    },
    
    getSearchParams() {
      const params = {
        page: this.pagination.page,
        limit: this.pagination.limit
      }
      
      if (this.searchForm.title) {
        params.title = this.searchForm.title
      }
      if (this.searchForm.jobType) {
        params.jobType = this.searchForm.jobType
      }
      if (this.searchForm.category) {
        params.category = this.searchForm.category
      }
      if (this.searchForm.workType) {
        params.workType = this.searchForm.workType
      }
      if (this.searchForm.status) {
        params.status = this.searchForm.status
      }
      
      return params
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadJobList()
    },
    
    handleReset() {
      this.searchForm = {
        title: '',
        jobType: '',
        category: '',
        workType: '',
        status: ''
      }
      this.pagination.page = 1
      this.loadJobList()
    },
    
    handleView(job) {
      this.$message.info('查看职位详情功能开发中')
    },
    
    handleApprove(job) {
      this.$confirm('确定要审核通过这个职位吗？', '审核确认', {
        confirmButtonText: '通过',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        job.status = 'published'
        this.$message.success('审核通过')
      })
    },
    
    handleDelete(job) {
      this.$confirm('确定要删除这个职位吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      })
    },
    
    handleSizeChange(size) {
      this.pagination.limit = size
      this.loadJobList()
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadJobList()
    },
    
    getJobTypeColor(type) {
      const colorMap = {
        recruitment: 'primary',
        job_seeking: 'success'
      }
      return colorMap[type] || ''
    },
    
    getJobTypeText(type) {
      const textMap = {
        recruitment: '招聘',
        job_seeking: '求职'
      }
      return textMap[type] || '未知'
    },
    
    getCategoryColor(category) {
      const colorMap = {
        station_manager: 'danger',
        clerk: 'primary',
        delivery: 'success',
        customer_service: 'warning',
        maintenance: 'info',
        part_time: '',
        other: ''
      }
      return colorMap[category] || ''
    },
    
    getCategoryText(category) {
      const textMap = {
        station_manager: '站长',
        clerk: '店员',
        delivery: '配送员',
        customer_service: '客服',
        maintenance: '维修',
        part_time: '兼职',
        other: '其他'
      }
      return textMap[category] || '未知'
    },
    
    getWorkTypeColor(type) {
      const colorMap = {
        full_time: 'success',
        part_time: 'primary',
        temporary: 'warning',
        internship: 'info'
      }
      return colorMap[type] || ''
    },
    
    getWorkTypeText(type) {
      const textMap = {
        full_time: '全职',
        part_time: '兼职',
        temporary: '临时',
        internship: '实习'
      }
      return textMap[type] || '未知'
    },
    
    getStatusColor(status) {
      const colorMap = {
        pending: 'warning',
        published: 'primary',
        recruiting: 'success',
        paused: 'info',
        completed: 'success',
        cancelled: 'danger'
      }
      return colorMap[status] || 'info'
    },
    
    getStatusText(status) {
      const textMap = {
        pending: '待审核',
        published: '已发布',
        recruiting: '招聘中',
        paused: '已暂停',
        completed: '已完成',
        cancelled: '已取消'
      }
      return textMap[status] || '未知'
    },
    
    formatMoney(amount) {
      if (!amount) return '0'
      return new Intl.NumberFormat('zh-CN').format(amount)
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.job-container {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        
        &.total { background: #909399; }
        &.recruitment { background: #409EFF; }
        &.seeking { background: #67C23A; }
        &.applications { background: #E6A23C; }
      }
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .filter-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .salary {
      font-weight: 500;
      color: #F56C6C;
    }
    
    .danger {
      color: #F56C6C;
    }
  }
  
  .pagination-container {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #EBEEF5;
  }
}
</style>
