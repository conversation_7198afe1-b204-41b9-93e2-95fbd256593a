<template>
  <div class="equipment-container">
    <div class="page-header">
      <h2><i class="el-icon-printer"></i> 设备交易管理</h2>
      <p>管理驿站设备交易信息</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon total">
          <i class="el-icon-s-goods"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total }}</div>
          <div class="stat-label">总设备数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon selling">
          <i class="el-icon-sell"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.selling }}</div>
          <div class="stat-label">在售设备</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon sold">
          <i class="el-icon-check"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.sold }}</div>
          <div class="stat-label">已售出</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon revenue">
          <i class="el-icon-money"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ formatMoney(stats.revenue) }}</div>
          <div class="stat-label">交易金额</div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="设备名称">
          <el-input v-model="searchForm.title" placeholder="请输入设备名称" clearable />
        </el-form-item>
        <el-form-item label="设备类别">
          <el-select v-model="searchForm.category" placeholder="请选择" clearable>
            <el-option label="打印机" value="printer" />
            <el-option label="扫描仪" value="scanner" />
            <el-option label="电脑" value="computer" />
            <el-option label="显示器" value="monitor" />
            <el-option label="货架" value="shelf" />
            <el-option label="柜台" value="counter" />
            <el-option label="安防设备" value="security" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="交易类型">
          <el-select v-model="searchForm.tradeType" placeholder="请选择" clearable>
            <el-option label="出售" value="sell" />
            <el-option label="求购" value="buy" />
            <el-option label="交换" value="exchange" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="待审核" value="pending" />
            <el-option label="已发布" value="published" />
            <el-option label="协商中" value="negotiating" />
            <el-option label="已售出" value="sold" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="el-icon-search"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="el-icon-refresh"></i> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="equipmentList"
        style="width: 100%"
      >
        <el-table-column prop="equipment_no" label="设备编号" width="140" />
        <el-table-column label="设备信息" min-width="200">
          <template #default="scope">
            <div class="equipment-info">
              <div class="title">{{ scope.row.title }}</div>
              <div class="brand-model">{{ scope.row.brand }} {{ scope.row.model }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="类别" width="100">
          <template #default="scope">
            <el-tag :type="getCategoryColor(scope.row.category)">
              {{ getCategoryText(scope.row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="condition" label="成色" width="80">
          <template #default="scope">
            <el-tag :type="getConditionColor(scope.row.condition)" size="small">
              {{ getConditionText(scope.row.condition) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="trade_type" label="交易类型" width="80">
          <template #default="scope">
            <el-tag :type="getTradeTypeColor(scope.row.trade_type)" size="small">
              {{ getTradeTypeText(scope.row.trade_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格" width="100">
          <template #default="scope">
            <span class="price">¥{{ formatMoney(scope.row.price) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="seller_name" label="发布者" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusColor(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="published_at" label="发布时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.published_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">
              <i class="el-icon-view"></i> 查看
            </el-button>
            <el-button 
              v-if="scope.row.status === 'pending'" 
              type="text" 
              size="small" 
              @click="handleApprove(scope.row)"
            >
              <i class="el-icon-check"></i> 审核
            </el-button>
            <el-button type="text" size="small" class="danger" @click="handleDelete(scope.row)">
              <i class="el-icon-delete"></i> 删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ServiceEquipment',
  data() {
    return {
      loading: false,
      equipmentList: [],
      searchForm: {
        title: '',
        category: '',
        tradeType: '',
        status: ''
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      stats: {
        total: 0,
        selling: 0,
        sold: 0,
        revenue: 0
      }
    }
  },
  mounted() {
    this.loadStats()
    this.loadEquipmentList()
  },
  methods: {
    async loadStats() {
      // 暂时使用模拟数据
      this.stats = {
        total: 0,
        selling: 0,
        sold: 0,
        revenue: 0
      }
    },
    
    async loadEquipmentList() {
      this.loading = true
      try {
        // 这里调用API获取设备列表
        // const response = await this.$http.get('/equipment/list', { params: this.getSearchParams() })
        
        // 暂时使用空数据
        this.equipmentList = []
        this.pagination.total = 0
      } catch (error) {
        console.error('加载设备列表失败:', error)
        this.$message.error('加载设备列表失败')
      } finally {
        this.loading = false
      }
    },
    
    getSearchParams() {
      const params = {
        page: this.pagination.page,
        limit: this.pagination.limit
      }
      
      if (this.searchForm.title) {
        params.title = this.searchForm.title
      }
      if (this.searchForm.category) {
        params.category = this.searchForm.category
      }
      if (this.searchForm.tradeType) {
        params.tradeType = this.searchForm.tradeType
      }
      if (this.searchForm.status) {
        params.status = this.searchForm.status
      }
      
      return params
    },
    
    handleSearch() {
      this.pagination.page = 1
      this.loadEquipmentList()
    },
    
    handleReset() {
      this.searchForm = {
        title: '',
        category: '',
        tradeType: '',
        status: ''
      }
      this.pagination.page = 1
      this.loadEquipmentList()
    },
    
    handleView(equipment) {
      this.$message.info('查看设备详情功能开发中')
    },
    
    handleApprove(equipment) {
      this.$confirm('确定要审核通过这个设备交易吗？', '审核确认', {
        confirmButtonText: '通过',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        equipment.status = 'published'
        this.$message.success('审核通过')
      })
    },
    
    handleDelete(equipment) {
      this.$confirm('确定要删除这个设备交易吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      })
    },
    
    handleSizeChange(size) {
      this.pagination.limit = size
      this.loadEquipmentList()
    },
    
    handleCurrentChange(page) {
      this.pagination.page = page
      this.loadEquipmentList()
    },
    
    getCategoryColor(category) {
      const colorMap = {
        printer: 'primary',
        scanner: 'success',
        computer: 'warning',
        monitor: 'info',
        shelf: '',
        counter: '',
        security: 'danger',
        other: ''
      }
      return colorMap[category] || ''
    },
    
    getCategoryText(category) {
      const textMap = {
        printer: '打印机',
        scanner: '扫描仪',
        computer: '电脑',
        monitor: '显示器',
        shelf: '货架',
        counter: '柜台',
        security: '安防',
        other: '其他'
      }
      return textMap[category] || '未知'
    },
    
    getConditionColor(condition) {
      const colorMap = {
        new: 'success',
        like_new: 'primary',
        good: 'warning',
        fair: 'info',
        poor: 'danger'
      }
      return colorMap[condition] || ''
    },
    
    getConditionText(condition) {
      const textMap = {
        new: '全新',
        like_new: '几乎全新',
        good: '良好',
        fair: '一般',
        poor: '较差'
      }
      return textMap[condition] || '未知'
    },
    
    getTradeTypeColor(type) {
      const colorMap = {
        sell: 'success',
        buy: 'primary',
        exchange: 'warning'
      }
      return colorMap[type] || ''
    },
    
    getTradeTypeText(type) {
      const textMap = {
        sell: '出售',
        buy: '求购',
        exchange: '交换'
      }
      return textMap[type] || '未知'
    },
    
    getStatusColor(status) {
      const colorMap = {
        pending: 'warning',
        published: 'primary',
        negotiating: 'info',
        sold: 'success',
        cancelled: 'danger'
      }
      return colorMap[status] || 'info'
    },
    
    getStatusText(status) {
      const textMap = {
        pending: '待审核',
        published: '已发布',
        negotiating: '协商中',
        sold: '已售出',
        cancelled: '已取消'
      }
      return textMap[status] || '未知'
    },
    
    formatMoney(amount) {
      if (!amount) return '0.00'
      return new Intl.NumberFormat('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount)
    },
    
    formatTime(time) {
      if (!time) return '-'
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.equipment-container {
  .page-header {
    margin-bottom: 24px;
    
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      
      i {
        color: #409EFF;
        margin-right: 8px;
      }
    }
    
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }
  
  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        
        &.total { background: #909399; }
        &.selling { background: #409EFF; }
        &.sold { background: #67C23A; }
        &.revenue { background: #E6A23C; }
      }
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .filter-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
  
  .table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    
    .equipment-info {
      .title {
        font-weight: 500;
        color: #303133;
      }
      
      .brand-model {
        font-size: 12px;
        color: #909399;
        margin-top: 2px;
      }
    }
    
    .price {
      font-weight: bold;
      color: #F56C6C;
    }
    
    .danger {
      color: #F56C6C;
    }
  }
  
  .pagination-container {
    padding: 20px;
    text-align: right;
    border-top: 1px solid #EBEEF5;
  }
}
</style>
