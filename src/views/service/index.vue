<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="filter-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
        <el-form-item label="服务名称" prop="serviceName">
          <el-input v-model="queryParams.serviceName" placeholder="请输入服务名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="服务类型" prop="serviceType">
          <el-select v-model="queryParams.serviceType" placeholder="请选择服务类型" clearable>
            <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-container">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
        </el-col>
      </el-row>

      <!-- 服务列表 -->
      <el-table v-loading="loading" :data="serviceList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="服务ID" align="center" prop="serviceId" />
        <el-table-column label="服务名称" align="center" prop="serviceName" min-width="150" show-overflow-tooltip />
        <el-table-column label="服务类型" align="center" prop="serviceType">
          <template #default="scope">
            <el-tag :type="getServiceTypeTag(scope.row.serviceType)">{{ scope.row.serviceTypeName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="服务价格" align="center" prop="price" />
        <el-table-column label="计费方式" align="center" prop="chargeMode" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="total > 0"
        :total="total"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 添加或修改服务对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="700px" append-to-body>
      <el-form ref="serviceForm" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="服务名称" prop="serviceName">
              <el-input v-model="form.serviceName" placeholder="请输入服务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务类型" prop="serviceType">
              <el-select v-model="form.serviceType" placeholder="请选择服务类型">
                <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="服务价格" prop="price">
              <el-input v-model="form.price" placeholder="请输入服务价格" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计费方式" prop="chargeMode">
              <el-select v-model="form.chargeMode" placeholder="请选择计费方式">
                <el-option v-for="dict in chargeModeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="服务详情" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="4" placeholder="请输入服务详情" />
        </el-form-item>
        <el-form-item label="服务图片" prop="image">
          <el-upload
            class="avatar-uploader"
            action="/api/common/upload"
            :show-file-list="false"
            :on-success="handleImageSuccess"
          >
            <img v-if="form.image" :src="form.image" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0" :max="999" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看服务详情对话框 -->
    <el-dialog title="服务详情" v-model="viewDialog.visible" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="服务名称">{{ viewInfo.serviceName }}</el-descriptions-item>
        <el-descriptions-item label="服务类型">{{ viewInfo.serviceTypeName }}</el-descriptions-item>
        <el-descriptions-item label="服务价格">{{ viewInfo.price }}</el-descriptions-item>
        <el-descriptions-item label="计费方式">{{ viewInfo.chargeMode }}</el-descriptions-item>
        <el-descriptions-item label="状态">{{ viewInfo.status === 1 ? '正常' : '停用' }}</el-descriptions-item>
        <el-descriptions-item label="排序">{{ viewInfo.sort }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ viewInfo.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ viewInfo.updateTime }}</el-descriptions-item>
        <el-descriptions-item label="服务详情" :span="2">{{ viewInfo.description }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewInfo.remark }}</el-descriptions-item>
        <el-descriptions-item label="服务图片" :span="2">
          <img v-if="viewInfo.image" :src="viewInfo.image" class="preview-image" />
          <span v-else>暂无图片</span>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewDialog.visible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 后续将导入API
// import { listService, addService, updateService, deleteService, exportService, changeServiceStatus } from '@/api/service'

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  serviceName: '',
  serviceType: '',
  status: ''
})

// 表格数据
const loading = ref(false)
const serviceList = ref([])
const total = ref(0)
const ids = ref([])
const single = computed(() => ids.value.length !== 1)
const multiple = computed(() => ids.value.length === 0)

// 选项数据
const typeOptions = [
  { value: '1', label: '驿站服务' },
  { value: '2', label: '快递服务' },
  { value: '3', label: '装卸搬运' },
  { value: '4', label: '仓储服务' },
  { value: '5', label: '特殊物品' }
]
const statusOptions = [
  { value: '1', label: '正常' },
  { value: '0', label: '停用' }
]
const chargeModeOptions = [
  { value: '按次计费', label: '按次计费' },
  { value: '按小时计费', label: '按小时计费' },
  { value: '按天计费', label: '按天计费' },
  { value: '按月计费', label: '按月计费' }
]

// 弹窗参数
const dialog = reactive({
  visible: false,
  title: '',
  type: ''
})
const viewDialog = reactive({
  visible: false
})

// 表单参数
const serviceForm = ref(null)
const form = reactive({
  serviceId: null,
  serviceName: '',
  serviceType: '',
  price: '',
  chargeMode: '',
  description: '',
  status: 1,
  sort: 0,
  image: '',
  remark: ''
})
const viewInfo = reactive({})

// 表单校验规则
const rules = reactive({
  serviceName: [
    { required: true, message: '服务名称不能为空', trigger: 'blur' }
  ],
  serviceType: [
    { required: true, message: '服务类型不能为空', trigger: 'change' }
  ],
  price: [
    { required: true, message: '价格不能为空', trigger: 'blur' },
    { pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '价格必须为数字，最多两位小数', trigger: 'blur' }
  ],
  chargeMode: [
    { required: true, message: '计费方式不能为空', trigger: 'change' }
  ],
  description: [
    { required: true, message: '服务详情不能为空', trigger: 'blur' }
  ]
})

// 服务类型标签
const getServiceTypeTag = (type) => {
  switch (type) {
    case '1':
      return 'primary'
    case '2':
      return 'success'
    case '3':
      return 'warning'
    case '4':
      return 'danger'
    case '5':
      return 'info'
    default:
      return ''
  }
}

// 生命周期钩子
onMounted(() => {
  getList()
})

// 获取服务列表
const getList = () => {
  loading.value = true
  // 测试数据，后续替换为API调用
  // listService(queryParams).then(response => {
  //   serviceList.value = response.rows
  //   total.value = response.total
  //   loading.value = false
  // })
  
  // 模拟数据
  setTimeout(() => {
    serviceList.value = [
      {
        serviceId: 1,
        serviceName: '包裹代收',
        serviceType: '1',
        serviceTypeName: '驿站服务',
        price: '2.00',
        chargeMode: '按次计费',
        description: '为用户提供包裹代收服务，用户可以在方便的时间来取包裹。',
        status: 1,
        sort: 1,
        createTime: '2023-05-15 10:30:00',
        updateTime: '2023-05-15 10:30:00',
        image: 'https://example.com/service1.jpg',
        remark: '驿站基础服务'
      },
      {
        serviceId: 2,
        serviceName: '大件物品搬运',
        serviceType: '3',
        serviceTypeName: '装卸搬运',
        price: '50.00',
        chargeMode: '按次计费',
        description: '提供大件物品的搬运服务，包括家具、电器等。',
        status: 1,
        sort: 3,
        createTime: '2023-05-10 14:20:00',
        updateTime: '2023-05-10 14:20:00',
        image: 'https://example.com/service2.jpg',
        remark: '需要提前预约'
      },
      {
        serviceId: 3,
        serviceName: '小型仓储空间',
        serviceType: '4',
        serviceTypeName: '仓储服务',
        price: '300.00',
        chargeMode: '按月计费',
        description: '提供小型仓储空间，适合个人或小型企业临时存放物品。',
        status: 0,
        sort: 5,
        createTime: '2023-05-05 09:15:00',
        updateTime: '2023-05-05 09:15:00',
        image: 'https://example.com/service3.jpg',
        remark: '面积约2平方米'
      }
    ]
    total.value = 3
    loading.value = false
  }, 500)
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  queryParams.serviceName = ''
  queryParams.serviceType = ''
  queryParams.status = ''
  handleQuery()
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.serviceId)
}

// 新增按钮操作
const handleAdd = () => {
  reset()
  dialog.visible = true
  dialog.title = '添加服务'
  dialog.type = 'add'
}

// 修改按钮操作
const handleUpdate = (row) => {
  reset()
  const serviceId = row.serviceId || ids.value[0]
  // 后续替换为API调用获取详情
  // getService(serviceId).then(response => {
  //   Object.assign(form, response.data)
  //   dialog.visible = true
  //   dialog.title = '修改服务'
  //   dialog.type = 'edit'
  // })
  
  // 模拟数据
  const selectedRow = serviceList.value.find(item => item.serviceId === serviceId)
  if (selectedRow) {
    Object.assign(form, selectedRow)
    dialog.visible = true
    dialog.title = '修改服务'
    dialog.type = 'edit'
  }
}

// 查看详情操作
const handleView = (row) => {
  Object.assign(viewInfo, row)
  viewDialog.visible = true
}

// 提交表单
const submitForm = () => {
  serviceForm.value.validate(valid => {
    if (valid) {
      if (form.serviceId) {
        // updateService(form).then(response => {
        //   ElMessage.success('修改成功')
        //   dialog.visible = false
        //   getList()
        // })
        ElMessage.success('修改成功')
        dialog.visible = false
        getList()
      } else {
        // addService(form).then(response => {
        //   ElMessage.success('新增成功')
        //   dialog.visible = false
        //   getList()
        // })
        ElMessage.success('新增成功')
        dialog.visible = false
        getList()
      }
    }
  })
}

// 删除按钮操作
const handleDelete = (row) => {
  const serviceIds = row.serviceId || ids.value
  ElMessageBox.confirm('是否确认删除所选服务?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // deleteService(serviceIds).then(() => {
    //   ElMessage.success('删除成功')
    //   getList()
    // })
    ElMessage.success('删除成功')
    getList()
  }).catch(() => {})
}

// 导出按钮操作
const handleExport = () => {
  // exportService(queryParams).then(response => {
  //   // 导出处理
  // })
  ElMessage.success('导出成功')
}

// 状态修改
const handleStatusChange = (row) => {
  // changeServiceStatus({ serviceId: row.serviceId, status: row.status }).then(() => {
  //   ElMessage.success(`${row.status === 1 ? '启用' : '停用'}成功`)
  // }).catch(() => {
  //   row.status = row.status === 1 ? 0 : 1
  // })
  ElMessage.success(`${row.status === 1 ? '启用' : '停用'}成功`)
}

// 图片上传成功处理
const handleImageSuccess = (response) => {
  form.image = response.data
}

// 表单重置
const reset = () => {
  form.serviceId = null
  form.serviceName = ''
  form.serviceType = ''
  form.price = ''
  form.chargeMode = ''
  form.description = ''
  form.status = 1
  form.sort = 0
  form.image = ''
  form.remark = ''
}

// 取消按钮
const cancel = () => {
  dialog.visible = false
  reset()
}

// 分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    
    &:hover {
      border-color: var(--el-color-primary);
    }
  }
  
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
    line-height: 178px;
  }
  
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}

.preview-image {
  max-width: 100%;
  max-height: 300px;
}
</style> 