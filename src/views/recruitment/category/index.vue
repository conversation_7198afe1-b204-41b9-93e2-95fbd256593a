<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <span>职位分类</span>
      </div>
      
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
        </el-col>
      </el-row>
      
      <el-table v-loading="loading" :data="categoryList" row-key="id" default-expand-all>
        <el-table-column prop="name" label="分类名称" width="200"></el-table-column>
        <el-table-column prop="code" label="分类编码" width="200"></el-table-column>
        <el-table-column prop="sort" label="显示顺序" width="100"></el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'danger' : 'success'">
              {{ scope.row.status === '0' ? '禁用' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180"></el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAdd(scope.row)">新增</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 添加或修改分类对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="上级分类" prop="parentId">
            <el-cascader
              v-model="form.parentId"
              :options="categoryOptions"
              :props="{ checkStrictly: true, value: 'id', label: 'name' }"
              placeholder="请选择上级分类"
              clearable
            ></el-cascader>
          </el-form-item>
          <el-form-item label="分类名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入分类名称" />
          </el-form-item>
          <el-form-item label="分类编码" prop="code">
            <el-input v-model="form.code" placeholder="请输入分类编码" />
          </el-form-item>
          <el-form-item label="显示顺序" prop="sort">
            <el-input-number v-model="form.sort" :min="0" :max="999" controls-position="right" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="'0'">禁用</el-radio>
              <el-radio :label="'1'">正常</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "JobCategory",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 显示对话框
      open: false,
      // 弹出层标题
      title: "",
      // 分类列表
      categoryList: [],
      // 分类树选项
      categoryOptions: [],
      // 表单参数
      form: {
        id: undefined,
        parentId: undefined,
        name: undefined,
        code: undefined,
        sort: 0,
        status: "1"
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "分类名称不能为空", trigger: "blur" }
        ],
        code: [
          { required: true, message: "分类编码不能为空", trigger: "blur" }
        ],
        sort: [
          { required: true, message: "显示顺序不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询分类列表 */
    getList() {
      this.loading = true;
      // TODO: 这里应该调用实际的API
      setTimeout(() => {
        this.categoryList = [
          {
            id: 1,
            name: "驿站运营",
            code: "YZ_YY",
            sort: 1,
            status: "1",
            createTime: "2023-01-10 10:00:00",
            children: [
              {
                id: 11,
                name: "驿站店长",
                code: "YZ_DZ",
                sort: 1,
                status: "1",
                createTime: "2023-01-10 10:10:00"
              },
              {
                id: 12,
                name: "驿站营业员",
                code: "YZ_YYY",
                sort: 2,
                status: "1",
                createTime: "2023-01-10 10:20:00"
              }
            ]
          },
          {
            id: 2,
            name: "快递配送",
            code: "KD_PS",
            sort: 2,
            status: "1",
            createTime: "2023-01-10 11:00:00",
            children: [
              {
                id: 21,
                name: "配送员",
                code: "KD_PSY",
                sort: 1,
                status: "1",
                createTime: "2023-01-10 11:10:00"
              },
              {
                id: 22,
                name: "分拣员",
                code: "KD_FJY",
                sort: 2,
                status: "1",
                createTime: "2023-01-10 11:20:00"
              }
            ]
          }
        ];
        this.categoryOptions = this.handleTree(this.categoryList, "id");
        this.loading = false;
      }, 500);
    },
    /** 转换菜单数据结构 */
    handleTree(data, id) {
      // 简单模拟，实际项目中可能需要更复杂的处理
      return data.map(item => {
        if (item.children) {
          item.children = this.handleTree(item.children, id);
        }
        return item;
      });
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      if (row && row.id) {
        this.form.parentId = row.id;
      }
      this.open = true;
      this.title = "添加职位分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      // TODO: 这里应该获取详细数据
      this.form = { ...row };
      this.open = true;
      this.title = "修改职位分类";
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm('是否确认删除名称为"' + row.name + '"的数据项?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // TODO: 调用删除API
        this.$message({
          type: "success",
          message: "删除成功"
        });
        this.getList();
      }).catch(() => {});
    },
    /** 表单重置 */
    reset() {
      this.form = {
        id: undefined,
        parentId: undefined,
        name: undefined,
        code: undefined,
        sort: 0,
        status: "1"
      };
      this.resetForm("form");
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // TODO: 调用保存API
          this.$message({
            type: "success",
            message: "保存成功"
          });
          this.open = false;
          this.getList();
        }
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    }
  }
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}
</style> 