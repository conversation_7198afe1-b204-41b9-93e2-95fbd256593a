<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>今日发布职位</span>
          </div>
          <div class="card-item">
            <div class="card-item-number">15</div>
            <div class="card-item-text">较昨日<span class="up">↑10.5%</span></div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>有效职位</span>
          </div>
          <div class="card-item">
            <div class="card-item-number">125</div>
            <div class="card-item-text">较上周<span class="up">↑5.2%</span></div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>今日投递简历</span>
          </div>
          <div class="card-item">
            <div class="card-item-number">68</div>
            <div class="card-item-text">较昨日<span class="down">↓3.8%</span></div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>本月成功招聘</span>
          </div>
          <div class="card-item">
            <div class="card-item-number">42</div>
            <div class="card-item-text">较上月<span class="up">↑12.7%</span></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt20">
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>职位发布趋势</span>
            <el-radio-group v-model="jobTrendTimeType" size="mini" style="float: right">
              <el-radio-button label="week">本周</el-radio-button>
              <el-radio-button label="month">本月</el-radio-button>
              <el-radio-button label="year">全年</el-radio-button>
            </el-radio-group>
          </div>
          <div class="chart-container">
            <div id="jobTrendChart" style="height: 300px"></div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>职位分类占比</span>
          </div>
          <div class="chart-container">
            <div id="jobCategoryChart" style="height: 300px"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt20">
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>热门职位TOP10</span>
          </div>
          <el-table :data="hotJobsList" style="width: 100%" v-loading="loading">
            <el-table-column prop="rank" label="排名" width="80" align="center" />
            <el-table-column prop="jobTitle" label="职位名称" />
            <el-table-column prop="resumeCount" label="简历数" align="center" />
            <el-table-column prop="viewCount" label="浏览次数" align="center" />
            <el-table-column prop="publishDays" label="发布天数" align="center" />
            <el-table-column label="简历/天" align="center">
              <template #default="scope">
                {{ (scope.row.resumeCount / scope.row.publishDays).toFixed(1) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="120">
              <template #default="scope">
                <el-button type="text" @click="handleViewJob(scope.row)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "RecruitmentStatistics",
  data() {
    return {
      loading: false,
      jobTrendTimeType: 'month',
      hotJobsList: [
        { rank: 1, jobTitle: '驿站店长', resumeCount: 120, viewCount: 860, publishDays: 15 },
        { rank: 2, jobTitle: '快递配送员', resumeCount: 98, viewCount: 750, publishDays: 12 },
        { rank: 3, jobTitle: '驿站营业员', resumeCount: 85, viewCount: 620, publishDays: 10 },
        { rank: 4, jobTitle: '分拣员', resumeCount: 76, viewCount: 560, publishDays: 8 },
        { rank: 5, jobTitle: '站点运营经理', resumeCount: 68, viewCount: 480, publishDays: 20 },
        { rank: 6, jobTitle: '驿站客服', resumeCount: 52, viewCount: 430, publishDays: 7 },
        { rank: 7, jobTitle: '快递驾驶员', resumeCount: 45, viewCount: 380, publishDays: 5 },
        { rank: 8, jobTitle: '仓库管理员', resumeCount: 38, viewCount: 320, publishDays: 6 },
        { rank: 9, jobTitle: '区域配送主管', resumeCount: 32, viewCount: 290, publishDays: 9 },
        { rank: 10, jobTitle: '夜班理货员', resumeCount: 28, viewCount: 250, publishDays: 4 }
      ]
    };
  },
  mounted() {
    this.initCharts();
  },
  methods: {
    initCharts() {
      // 这里需要实际引入echarts并实现图表初始化
      // 示例中省略实际的echarts代码
      console.log('初始化图表');
    },
    handleViewJob(row) {
      this.$router.push(`/recruitment/detail/${row.id || 1}`);
    }
  }
};
</script>

<style scoped>
.mt20 {
  margin-top: 20px;
}

.card-item {
  padding: 10px 0;
  text-align: center;
}

.card-item-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.card-item-text {
  font-size: 14px;
  color: #606266;
}

.up {
  color: #67c23a;
  font-weight: bold;
}

.down {
  color: #f56c6c;
  font-weight: bold;
}

.chart-container {
  position: relative;
  width: 100%;
}
</style> 