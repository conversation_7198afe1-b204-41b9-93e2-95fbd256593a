<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <span>招聘详情</span>
      </div>
      <div v-loading="loading">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="职位名称" prop="jobTitle">
            <el-input v-model="form.jobTitle" placeholder="请输入职位名称" />
          </el-form-item>
          <el-form-item label="薪资范围" prop="salary">
            <el-input v-model="form.salary" placeholder="请输入薪资范围" />
          </el-form-item>
          <el-form-item label="工作地点" prop="workLocation">
            <el-input v-model="form.workLocation" placeholder="请输入工作地点" />
          </el-form-item>
          <el-form-item label="招聘人数" prop="recruitCount">
            <el-input-number v-model="form.recruitCount" :min="1" :max="100" />
          </el-form-item>
          <el-form-item label="工作经验" prop="experience">
            <el-select v-model="form.experience" placeholder="请选择工作经验">
              <el-option label="无经验" value="无经验" />
              <el-option label="1年以下" value="1年以下" />
              <el-option label="1-3年" value="1-3年" />
              <el-option label="3-5年" value="3-5年" />
              <el-option label="5年以上" value="5年以上" />
            </el-select>
          </el-form-item>
          <el-form-item label="学历要求" prop="education">
            <el-select v-model="form.education" placeholder="请选择学历要求">
              <el-option label="不限" value="不限" />
              <el-option label="高中" value="高中" />
              <el-option label="中专" value="中专" />
              <el-option label="大专" value="大专" />
              <el-option label="本科" value="本科" />
              <el-option label="硕士" value="硕士" />
              <el-option label="博士" value="博士" />
            </el-select>
          </el-form-item>
          <el-form-item label="职位描述" prop="description">
            <el-input type="textarea" v-model="form.description" :rows="5" placeholder="请输入职位描述" />
          </el-form-item>
          <el-form-item label="任职要求" prop="requirements">
            <el-input type="textarea" v-model="form.requirements" :rows="5" placeholder="请输入任职要求" />
          </el-form-item>
          <el-form-item label="公司福利" prop="benefits">
            <el-input type="textarea" v-model="form.benefits" :rows="3" placeholder="请输入公司福利" />
          </el-form-item>
          <el-form-item label="发布状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="'0'">草稿</el-radio>
              <el-radio :label="'1'">发布</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">保存</el-button>
            <el-button @click="cancel">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: "RecruitmentDetail",
  data() {
    return {
      loading: false,
      // 是否为新增
      isAdd: false,
      // 表单参数
      form: {
        id: undefined,
        jobTitle: "",
        salary: "",
        workLocation: "",
        recruitCount: 1,
        experience: "",
        education: "",
        description: "",
        requirements: "",
        benefits: "",
        status: "0"
      },
      // 表单校验
      rules: {
        jobTitle: [
          { required: true, message: "职位名称不能为空", trigger: "blur" }
        ],
        salary: [
          { required: true, message: "薪资范围不能为空", trigger: "blur" }
        ],
        workLocation: [
          { required: true, message: "工作地点不能为空", trigger: "blur" }
        ],
        experience: [
          { required: true, message: "请选择工作经验", trigger: "change" }
        ],
        education: [
          { required: true, message: "请选择学历要求", trigger: "change" }
        ],
        description: [
          { required: true, message: "职位描述不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    const id = this.$route.params.id;
    this.isAdd = id === '0' || !id;
    if (!this.isAdd) {
      this.getInfo(id);
    }
  },
  methods: {
    /** 获取详细信息 */
    getInfo(id) {
      this.loading = true;
      // TODO: 这里应该调用实际的API
      setTimeout(() => {
        if (id === '1') {
          this.form = {
            id: 1,
            jobTitle: "快递驿站店长",
            salary: "5000-8000",
            workLocation: "北京市海淀区中关村",
            recruitCount: 2,
            experience: "1-3年",
            education: "大专",
            description: "负责驿站日常运营管理，包括快递接收、派送、客户服务等工作。",
            requirements: "有快递行业经验优先，良好的沟通能力和服务意识，能够承受一定的工作压力。",
            benefits: "五险一金，带薪年假，节日福利，绩效奖金",
            status: "1"
          };
        }
        this.loading = false;
      }, 500);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // TODO: 调用保存API
          this.$message({
            type: "success",
            message: this.isAdd ? "新增成功" : "修改成功"
          });
          this.cancel();
        }
      });
    },
    /** 取消按钮 */
    cancel() {
      this.$router.push("/recruitment/list");
    }
  }
};
</script>

<style scoped>
.el-input-number {
  width: 120px;
}
</style> 