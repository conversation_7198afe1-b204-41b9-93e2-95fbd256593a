<template>
  <div class="app-container">
    <el-card>
      <div slot="header">
        <span>招聘列表</span>
      </div>
      
      <!-- 搜索框 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
        <el-form-item label="职位名称" prop="jobTitle">
          <el-input v-model="queryParams.jobTitle" placeholder="请输入职位名称" clearable size="small" />
        </el-form-item>
        <el-form-item label="发布状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">编辑</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="recruitmentList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" width="80" />
        <el-table-column label="职位名称" align="center" prop="jobTitle" min-width="120" />
        <el-table-column label="薪资范围" align="center" prop="salary" min-width="100" />
        <el-table-column label="工作地点" align="center" prop="workLocation" min-width="150" />
        <el-table-column label="招聘人数" align="center" prop="recruitCount" width="90" />
        <el-table-column label="工作经验" align="center" prop="experience" min-width="100" />
        <el-table-column label="学历要求" align="center" prop="education" min-width="100" />
        <el-table-column label="发布状态" align="center" prop="status" width="90">
          <template #default="scope">
            <el-tag :type="scope.row.status === '1' ? 'success' : 'info'">
              {{ statusFormat(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="发布时间" align="center" prop="publishTime" width="160" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
          <template #default="scope">
            <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script>
export default {
  name: "RecruitmentList",
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 招聘列表数据
      recruitmentList: [],
      // 状态数据字典
      statusOptions: [
        { value: "0", label: "草稿" },
        { value: "1", label: "发布中" },
        { value: "2", label: "已下线" }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        jobTitle: undefined,
        status: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询招聘列表 */
    getList() {
      this.loading = true;
      // TODO: 这里应该调用实际的API
      setTimeout(() => {
        this.recruitmentList = [
          {
            id: 1,
            jobTitle: "快递驿站店长",
            salary: "5000-8000",
            workLocation: "北京市海淀区中关村",
            recruitCount: 2,
            experience: "1-3年",
            education: "大专",
            publishTime: "2023-01-15 10:30:00",
            status: "1"
          },
          {
            id: 2,
            jobTitle: "驿站营业员",
            salary: "3500-5000",
            workLocation: "北京市朝阳区建国路",
            recruitCount: 5,
            experience: "无经验",
            education: "高中",
            publishTime: "2023-01-16 14:20:00",
            status: "1"
          },
          {
            id: 3,
            jobTitle: "配送专员",
            salary: "4000-6000",
            workLocation: "北京市丰台区丽泽桥",
            recruitCount: 10,
            experience: "无经验",
            education: "不限",
            publishTime: "2023-01-14 09:15:00",
            status: "1"
          }
        ];
        this.total = 3;
        this.loading = false;
      }, 500);
    },
    // 状态字典翻译
    statusFormat(row) {
      return this.selectDictLabel(this.statusOptions, row.status);
    },
    // 字典翻译
    selectDictLabel(dict, value) {
      const item = dict.find(item => item.value === value);
      return item ? item.label : '';
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push('/recruitment/detail/0');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const id = row.id || this.ids[0];
      this.$router.push(`/recruitment/detail/${id}`);
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push(`/recruitment/detail/${row.id}`);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除所选招聘信息?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // TODO: 调用删除API
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
        this.getList();
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}
</style> 