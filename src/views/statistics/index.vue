<template>
  <div class="app-container">
    <!-- 时间选择器 -->
    <el-card class="filter-container">
      <el-form :inline="true">
        <el-form-item label="统计时间范围">
          <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
            <el-radio-button label="today">今日</el-radio-button>
            <el-radio-button label="week">本周</el-radio-button>
            <el-radio-button label="month">本月</el-radio-button>
            <el-radio-button label="year">今年</el-radio-button>
            <el-radio-button label="custom">自定义</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="timeRange === 'custom'">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="handleDateRangeChange"
          />
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 数据概览 -->
    <el-card class="data-overview">
      <template #header>
        <div class="card-header">
          <span>数据概览</span>
          <el-button link>刷新</el-button>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="data-card bg-blue">
            <div class="card-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">用户总数</div>
              <div class="card-value">{{ statistics.totalUser }}</div>
              <div class="card-compare">
                <span>较前一{{ timeRangeText }}：</span>
                <span :class="statistics.userGrowth >= 0 ? 'text-success' : 'text-danger'">
                  {{ statistics.userGrowth >= 0 ? '+' : '' }}{{ statistics.userGrowth }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="data-card bg-green">
            <div class="card-icon">
              <el-icon><House /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">驿站总数</div>
              <div class="card-value">{{ statistics.totalStation }}</div>
              <div class="card-compare">
                <span>较前一{{ timeRangeText }}：</span>
                <span :class="statistics.stationGrowth >= 0 ? 'text-success' : 'text-danger'">
                  {{ statistics.stationGrowth >= 0 ? '+' : '' }}{{ statistics.stationGrowth }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="data-card bg-orange">
            <div class="card-icon">
              <el-icon><List /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">订单总数</div>
              <div class="card-value">{{ statistics.totalOrder }}</div>
              <div class="card-compare">
                <span>较前一{{ timeRangeText }}：</span>
                <span :class="statistics.orderGrowth >= 0 ? 'text-success' : 'text-danger'">
                  {{ statistics.orderGrowth >= 0 ? '+' : '' }}{{ statistics.orderGrowth }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="data-card bg-red">
            <div class="card-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-title">销售总额</div>
              <div class="card-value">{{ statistics.totalSales }}</div>
              <div class="card-compare">
                <span>较前一{{ timeRangeText }}：</span>
                <span :class="statistics.salesGrowth >= 0 ? 'text-success' : 'text-danger'">
                  {{ statistics.salesGrowth >= 0 ? '+' : '' }}{{ statistics.salesGrowth }}%
                </span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 订单趋势图 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="16">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>订单趋势</span>
            </div>
          </template>
          <div ref="orderTrendChart" class="chart"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>订单类型分布</span>
            </div>
          </template>
          <div ref="orderTypeChart" class="chart"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 用户分析 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>用户增长趋势</span>
            </div>
          </template>
          <div ref="userGrowthChart" class="chart"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>用户地区分布</span>
            </div>
          </template>
          <div ref="userRegionChart" class="chart"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 热门驿站排行 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <span>热门驿站排行</span>
        </div>
      </template>
      <el-table :data="hotStations" style="width: 100%" stripe>
        <el-table-column prop="rank" label="排名" width="80" align="center" />
        <el-table-column prop="stationName" label="驿站名称" min-width="200" />
        <el-table-column prop="stationType" label="驿站类型" width="120" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.stationType === '个人' ? 'success' : 'primary'">{{ scope.row.stationType }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="area" label="所在地区" width="120" align="center" />
        <el-table-column prop="orderCount" label="订单数量" width="120" align="center" />
        <el-table-column prop="sales" label="销售额" width="150" align="center" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import * as echarts from 'echarts'
import { User, House, List, Money } from '@element-plus/icons-vue'
// 后续将导入API
// import { getStatisticsData, getOrderTrend, getOrderTypes, getUserGrowth, getUserRegion, getHotStations } from '@/api/statistics'

// 时间范围选择
const timeRange = ref('month')
const dateRange = ref([])
const timeRangeText = computed(() => {
  switch (timeRange.value) {
    case 'today':
      return '日'
    case 'week':
      return '周'
    case 'month':
      return '月'
    case 'year':
      return '年'
    case 'custom':
      return '时段'
    default:
      return '时段'
  }
})

// 统计数据
const statistics = reactive({
  totalUser: '2,583',
  userGrowth: 15.8,
  totalStation: '126',
  stationGrowth: 5.2,
  totalOrder: '8,529',
  orderGrowth: 23.7,
  totalSales: '￥236,582',
  salesGrowth: 18.9
})

// 热门驿站数据
const hotStations = ref([
  {
    rank: 1,
    stationName: '海淀创业园驿站',
    stationType: '企业',
    area: '北京',
    orderCount: 1256,
    sales: '￥38,562'
  },
  {
    rank: 2,
    stationName: '朝阳小区驿站',
    stationType: '社区',
    area: '北京',
    orderCount: 985,
    sales: '￥29,354'
  },
  {
    rank: 3,
    stationName: '上海浦东驿站',
    stationType: '企业',
    area: '上海',
    orderCount: 876,
    sales: '￥24,893'
  },
  {
    rank: 4,
    stationName: '张三个人驿站',
    stationType: '个人',
    area: '上海',
    orderCount: 765,
    sales: '￥18,762'
  },
  {
    rank: 5,
    stationName: '广州番禺驿站',
    stationType: '企业',
    area: '广州',
    orderCount: 678,
    sales: '￥15,986'
  }
])

// 图表实例
let orderTrendChart = null
let orderTypeChart = null
let userGrowthChart = null
let userRegionChart = null

// 图表DOM引用
const orderTrendChartRef = ref(null)
const orderTypeChartRef = ref(null)
const userGrowthChartRef = ref(null)
const userRegionChartRef = ref(null)

// 生命周期钩子
onMounted(() => {
  getStatisticsData()
  nextTick(() => {
    initCharts()
  })
})

// 时间范围变化处理
const handleTimeRangeChange = () => {
  if (timeRange.value !== 'custom') {
    getStatisticsData()
    updateCharts()
  }
}

// 日期范围变化处理
const handleDateRangeChange = () => {
  getStatisticsData()
  updateCharts()
}

// 获取统计数据
const getStatisticsData = () => {
  // 后续替换为API调用
  // getStatisticsData({
  //   timeRange: timeRange.value,
  //   startDate: dateRange.value && dateRange.value.length > 0 ? dateRange.value[0] : '',
  //   endDate: dateRange.value && dateRange.value.length > 0 ? dateRange.value[1] : ''
  // }).then(response => {
  //   Object.assign(statistics, response.data)
  // })
  
  // 模拟数据更新
  setTimeout(() => {
    // 这里只是模拟数据变化，实际实现应从后端获取
    statistics.totalUser = `${Math.floor(2000 + Math.random() * 1000)}`
    statistics.userGrowth = +(Math.random() * 30 - 10).toFixed(1)
    statistics.totalStation = `${Math.floor(100 + Math.random() * 50)}`
    statistics.stationGrowth = +(Math.random() * 20 - 5).toFixed(1)
    statistics.totalOrder = `${Math.floor(8000 + Math.random() * 2000)}`
    statistics.orderGrowth = +(Math.random() * 40 - 10).toFixed(1)
    statistics.totalSales = `￥${Math.floor(200000 + Math.random() * 80000)}`
    statistics.salesGrowth = +(Math.random() * 35 - 10).toFixed(1)
  }, 300)
}

// 初始化图表
const initCharts = () => {
  // 订单趋势图
  orderTrendChart = echarts.init(orderTrendChartRef.value)
  const orderTrendOption = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['订单数量', '销售额']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: [
      {
        type: 'value',
        name: '订单数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '销售额',
        position: 'right',
        axisLabel: {
          formatter: '{value} 元'
        }
      }
    ],
    series: [
      {
        name: '订单数量',
        type: 'line',
        data: [820, 932, 901, 934, 1290, 1330, 1320]
      },
      {
        name: '销售额',
        type: 'line',
        yAxisIndex: 1,
        data: [22000, 25000, 24000, 26000, 32000, 35000, 34000]
      }
    ]
  }
  orderTrendChart.setOption(orderTrendOption)
  
  // 订单类型分布图
  orderTypeChart = echarts.init(orderTypeChartRef.value)
  const orderTypeOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: ['站点租用', '服务购买', '设备租赁']
    },
    series: [
      {
        name: '订单类型',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 4350, name: '站点租用' },
          { value: 3264, name: '服务购买' },
          { value: 915, name: '设备租赁' }
        ]
      }
    ]
  }
  orderTypeChart.setOption(orderTypeOption)
  
  // 用户增长趋势图
  userGrowthChart = echarts.init(userGrowthChartRef.value)
  const userGrowthOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['新增用户', '累计用户']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: '新增用户',
        type: 'bar',
        data: [230, 245, 283, 341, 427, 486, 571]
      },
      {
        name: '累计用户',
        type: 'line',
        data: [230, 475, 758, 1099, 1526, 2012, 2583]
      }
    ]
  }
  userGrowthChart.setOption(userGrowthOption)
  
  // 用户地区分布图
  userRegionChart = echarts.init(userRegionChartRef.value)
  const userRegionOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: ['北京', '上海', '广州', '深圳', '杭州', '其他']
    },
    series: [
      {
        name: '用户地区',
        type: 'pie',
        radius: '65%',
        center: ['55%', '50%'],
        data: [
          { value: 895, name: '北京' },
          { value: 782, name: '上海' },
          { value: 432, name: '广州' },
          { value: 285, name: '深圳' },
          { value: 124, name: '杭州' },
          { value: 65, name: '其他' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  userRegionChart.setOption(userRegionOption)
  
  // 监听窗口大小变化，重新渲染图表
  window.addEventListener('resize', () => {
    orderTrendChart && orderTrendChart.resize()
    orderTypeChart && orderTypeChart.resize()
    userGrowthChart && userGrowthChart.resize()
    userRegionChart && userRegionChart.resize()
  })
}

// 更新图表数据
const updateCharts = () => {
  // 后续替换为API调用获取最新数据并更新图表
  // 模拟数据更新，实际应从后端获取
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.data-overview {
  margin-bottom: 20px;
}

.chart-row {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.data-card {
  display: flex;
  padding: 20px;
  border-radius: 8px;
  color: #fff;
  
  .card-icon {
    font-size: 40px;
    margin-right: 15px;
    display: flex;
    align-items: center;
  }
  
  .card-content {
    flex: 1;
    
    .card-title {
      font-size: 14px;
      margin-bottom: 10px;
    }
    
    .card-value {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    
    .card-compare {
      font-size: 12px;
    }
  }
}

.bg-blue {
  background: linear-gradient(135deg, #409eff, #1e88e5);
}

.bg-green {
  background: linear-gradient(135deg, #67c23a, #4caf50);
}

.bg-orange {
  background: linear-gradient(135deg, #e6a23c, #ff9800);
}

.bg-red {
  background: linear-gradient(135deg, #f56c6c, #e53935);
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}

.chart {
  height: 400px;
  width: 100%;
}

.chart-card {
  margin-bottom: 20px;
}
</style> 