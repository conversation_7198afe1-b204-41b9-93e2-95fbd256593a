<template>
  <div class="app-container">
    <el-card class="filter-container">
      <div class="filter-item">
        <el-input v-model="listQuery.search" placeholder="搜索用户昵称/手机" clearable @keyup.enter.native="handleFilter" style="width: 200px;" />
        <el-select v-model="listQuery.status" placeholder="认证状态" clearable style="width: 130px" @change="handleFilter">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          style="width: 260px;"
          @change="handleDateChange"
        />
        <el-button v-waves type="primary" icon="Search" @click="handleFilter">搜索</el-button>
        <el-button v-waves icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
    </el-card>

    <el-card>
      <div class="table-header">
        <div class="statistics-cards">
          <div class="stat-card primary">
            <div class="stat-value">{{ statistics.totalCount }}</div>
            <div class="stat-label">总认证申请</div>
          </div>
          <div class="stat-card warning">
            <div class="stat-value">{{ statistics.pendingCount }}</div>
            <div class="stat-label">待审核</div>
          </div>
          <div class="stat-card success">
            <div class="stat-value">{{ statistics.approvedCount }}</div>
            <div class="stat-label">已通过</div>
          </div>
          <div class="stat-card danger">
            <div class="stat-value">{{ statistics.rejectedCount }}</div>
            <div class="stat-label">已驳回</div>
          </div>
        </div>
        <div>
          <el-button type="success" size="small" @click="exportData">导出数据</el-button>
        </div>
      </div>

      <el-table
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column label="ID" prop="id" align="center" width="80" />
        <el-table-column label="用户信息" min-width="150">
          <template #default="{row}">
            <div class="user-info">
              <el-avatar :src="row.avatar || '/static/default-avatar.png'" :size="30"></el-avatar>
              <div class="user-detail">
                <div>{{ row.nickname }}</div>
                <div class="user-phone">{{ row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="真实姓名" prop="real_name" align="center" width="120" />
        <el-table-column label="身份证号" align="center" width="180">
          <template #default="{row}">
            {{ formatIdCard(row.id_card) }}
          </template>
        </el-table-column>
        <el-table-column label="申请时间" width="150" align="center">
          <template #default="{row}">
            <span>{{ formatDateTime(row.created_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="认证方式" align="center" width="100">
          <template #default="{row}">
            <el-tag type="info">{{ getVerifyTypeText(row.verify_type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" align="center" width="100">
          <template #default="{row}">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="审核时间" width="150" align="center">
          <template #default="{row}">
            <span v-if="row.verified_at">{{ formatDateTime(row.verified_at) }}</span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
          <template #default="{row}">
            <el-button v-if="row.status === 0" type="success" size="small" @click="handleApprove(row)">通过</el-button>
            <el-button v-if="row.status === 0" type="danger" size="small" @click="handleReject(row)">驳回</el-button>
            <el-button type="primary" size="small" @click="handleDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </el-card>

    <!-- 审核对话框 -->
    <el-dialog :title="auditAction === 'approve' ? '通过实名认证' : '驳回实名认证'" v-model="auditDialogVisible" width="400px">
      <el-form :model="auditForm" label-width="80px">
        <el-form-item label="备注">
          <el-input
            v-model="auditForm.remark"
            type="textarea"
            :rows="3"
            :placeholder="auditAction === 'approve' ? '通过原因（选填）' : '驳回原因（必填）'"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="auditDialogVisible = false">取 消</el-button>
          <el-button :type="auditAction === 'approve' ? 'success' : 'danger'" @click="submitAudit" :loading="auditSubmitting">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, toRefs, onMounted } from 'vue'
import waves from '@/directive/waves'
import Pagination from '@/components/Pagination'
import { formatDateTime } from '@/utils/index'

export default {
  name: 'IdentityList',
  components: { Pagination },
  directives: { waves },
  setup() {
    const dateRange = ref([])
    const list = ref([])
    const total = ref(0)
    const listLoading = ref(false)
    const auditDialogVisible = ref(false)
    const auditSubmitting = ref(false)
    const auditAction = ref('approve')
    const currentRow = ref(null)
    
    const listQuery = reactive({
      page: 1,
      limit: 10,
      status: '',
      search: '',
      start_date: '',
      end_date: ''
    })
    
    const statistics = reactive({
      totalCount: 0,
      pendingCount: 0,
      approvedCount: 0,
      rejectedCount: 0
    })
    
    const auditForm = reactive({
      id: null,
      status: 0,
      remark: ''
    })
    
    const statusOptions = [
      { label: '待审核', value: 0 },
      { label: '已通过', value: 1 },
      { label: '已驳回', value: 2 }
    ]
    
    // 模拟数据，实际项目中应从API获取
    const mockList = [
      {
        id: 1,
        nickname: '张三',
        phone: '138****1234',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        real_name: '张三',
        id_card: '******************',
        created_at: '2023-05-20 10:00:00',
        verified_at: null,
        verify_type: 1,
        status: 0
      },
      {
        id: 2,
        nickname: '李四',
        phone: '139****5678',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        real_name: '李四',
        id_card: '******************',
        created_at: '2023-05-19 15:30:00',
        verified_at: '2023-05-19 16:30:00',
        verify_type: 2,
        status: 1
      },
      {
        id: 3,
        nickname: '王五',
        phone: '137****9012',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        real_name: '王五',
        id_card: '******************',
        created_at: '2023-05-18 09:15:00',
        verified_at: '2023-05-18 10:20:00',
        verify_type: 1,
        status: 2
      }
    ]

    const mockStatistics = {
      totalCount: 235,
      pendingCount: 42,
      approvedCount: 180,
      rejectedCount: 13
    }

    const getList = () => {
      listLoading.value = true
      
      // 模拟API调用
      setTimeout(() => {
        list.value = mockList
        total.value = mockList.length
        listLoading.value = false
      }, 500)
      
      // 获取统计数据
      getStatistics()
    }
    
    const getStatistics = () => {
      // 模拟API调用
      setTimeout(() => {
        statistics.totalCount = mockStatistics.totalCount
        statistics.pendingCount = mockStatistics.pendingCount
        statistics.approvedCount = mockStatistics.approvedCount
        statistics.rejectedCount = mockStatistics.rejectedCount
      }, 300)
    }
    
    const handleFilter = () => {
      listQuery.page = 1
      getList()
    }
    
    const resetQuery = () => {
      dateRange.value = []
      Object.assign(listQuery, {
        page: 1,
        limit: 10,
        status: '',
        search: '',
        start_date: '',
        end_date: ''
      })
      getList()
    }
    
    const handleDateChange = (val) => {
      if (val) {
        listQuery.start_date = val[0]
        listQuery.end_date = val[1]
      } else {
        listQuery.start_date = ''
        listQuery.end_date = ''
      }
    }
    
    const getStatusType = (status) => {
      const statusMap = {
        0: 'warning',
        1: 'success',
        2: 'danger'
      }
      return statusMap[status]
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已驳回'
      }
      return statusMap[status]
    }
    
    const getVerifyTypeText = (type) => {
      const typeMap = {
        1: '身份证认证',
        2: '人脸认证'
      }
      return typeMap[type] || '未知'
    }
    
    const formatIdCard = (idCard) => {
      if (!idCard) return ''
      return idCard.replace(/^(.{6})(.*)(.{4})$/, '$1********$3')
    }
    
    const handleApprove = (row) => {
      currentRow.value = row
      auditAction.value = 'approve'
      auditForm.id = row.id
      auditForm.status = 1
      auditForm.remark = ''
      auditDialogVisible.value = true
    }
    
    const handleReject = (row) => {
      currentRow.value = row
      auditAction.value = 'reject'
      auditForm.id = row.id
      auditForm.status = 2
      auditForm.remark = ''
      auditDialogVisible.value = true
    }
    
    const submitAudit = () => {
      if (auditAction.value === 'reject' && !auditForm.remark) {
        ElMessage.warning('请填写驳回原因')
        return
      }
      
      auditSubmitting.value = true
      
      // 模拟API调用
      setTimeout(() => {
        // 更新本地列表数据
        const index = list.value.findIndex(item => item.id === auditForm.id)
        if (index > -1) {
          list.value[index].status = auditForm.status
          list.value[index].verified_at = formatDateTime(new Date())
        }
        
        // 更新统计数据
        if (auditAction.value === 'approve') {
          statistics.pendingCount--
          statistics.approvedCount++
        } else {
          statistics.pendingCount--
          statistics.rejectedCount++
        }
        
        auditSubmitting.value = false
        auditDialogVisible.value = false
        
        ElMessage({
          type: 'success',
          message: auditAction.value === 'approve' ? '认证审核已通过' : '认证审核已驳回'
        })
      }, 500)
    }
    
    const handleDetail = (row) => {
      // 跳转到详情页
      router.push(`/identity/detail/${row.id}`)
    }
    
    const exportData = () => {
      ElMessage.success('数据导出功能开发中')
    }
    
    onMounted(() => {
      getList()
    })
    
    return {
      dateRange,
      list,
      total,
      listLoading,
      listQuery,
      statistics,
      statusOptions,
      auditDialogVisible,
      auditSubmitting,
      auditAction,
      auditForm,
      handleFilter,
      resetQuery,
      handleDateChange,
      getStatusType,
      getStatusText,
      getVerifyTypeText,
      formatIdCard,
      formatDateTime,
      handleApprove,
      handleReject,
      submitAudit,
      handleDetail,
      exportData
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
  
  .filter-item {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .statistics-cards {
    display: flex;
    gap: 15px;
    
    .stat-card {
      padding: 15px;
      border-radius: 4px;
      width: 130px;
      text-align: center;
      
      &.primary {
        background-color: #ecf5ff;
        color: #409eff;
      }
      
      &.warning {
        background-color: #fdf6ec;
        color: #e6a23c;
      }
      
      &.success {
        background-color: #f0f9eb;
        color: #67c23a;
      }
      
      &.danger {
        background-color: #fef0f0;
        color: #f56c6c;
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .stat-label {
        font-size: 14px;
      }
    }
  }
}

.user-info {
  display: flex;
  align-items: center;
  
  .user-detail {
    margin-left: 10px;
    
    .user-phone {
      color: #909399;
      font-size: 13px;
    }
  }
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}
</style> 