<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-title">总认证申请</div>
          <div class="stat-value primary">{{ statistics.totalCount }}</div>
          <div class="stat-trend">
            <span>较上月</span>
            <span class="trend-value" :class="statistics.totalTrend > 0 ? 'up' : 'down'">
              {{ statistics.totalTrend > 0 ? '+' : '' }}{{ statistics.totalTrend }}%
              <el-icon v-if="statistics.totalTrend > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-title">待审核</div>
          <div class="stat-value warning">{{ statistics.pendingCount }}</div>
          <div class="stat-trend">
            <span>较昨日</span>
            <span class="trend-value" :class="statistics.pendingTrend > 0 ? 'up' : 'down'">
              {{ statistics.pendingTrend > 0 ? '+' : '' }}{{ statistics.pendingTrend }}%
              <el-icon v-if="statistics.pendingTrend > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-title">已通过</div>
          <div class="stat-value success">{{ statistics.approvedCount }}</div>
          <div class="stat-trend">
            <span>较上月</span>
            <span class="trend-value" :class="statistics.approvedTrend > 0 ? 'up' : 'down'">
              {{ statistics.approvedTrend > 0 ? '+' : '' }}{{ statistics.approvedTrend }}%
              <el-icon v-if="statistics.approvedTrend > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
            </span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card" shadow="hover">
          <div class="stat-title">已驳回</div>
          <div class="stat-value danger">{{ statistics.rejectedCount }}</div>
          <div class="stat-trend">
            <span>较上月</span>
            <span class="trend-value" :class="statistics.rejectedTrend > 0 ? 'up' : 'down'">
              {{ statistics.rejectedTrend > 0 ? '+' : '' }}{{ statistics.rejectedTrend }}%
              <el-icon v-if="statistics.rejectedTrend > 0"><ArrowUp /></el-icon>
              <el-icon v-else><ArrowDown /></el-icon>
            </span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>认证数量趋势</span>
              <el-radio-group v-model="timeRange" size="small" @change="handleTimeRangeChange">
                <el-radio-button label="week">近7天</el-radio-button>
                <el-radio-button label="month">近30天</el-radio-button>
                <el-radio-button label="year">近12个月</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <div ref="trendChartRef" class="chart"></div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>认证状态分布</span>
            </div>
          </template>
          <div class="chart-container">
            <div ref="statusChartRef" class="chart"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>认证方式分布</span>
            </div>
          </template>
          <div class="chart-container">
            <div ref="verifyTypeChartRef" class="chart"></div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>年龄分布</span>
            </div>
          </template>
          <div class="chart-container">
            <div ref="ageChartRef" class="chart"></div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>地区分布</span>
            </div>
          </template>
          <div class="chart-container">
            <div ref="regionChartRef" class="chart"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <el-col :span="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>认证审核效率分析</span>
              <el-select v-model="auditTimeRange" placeholder="时间范围" size="small" @change="handleAuditTimeChange">
                <el-option label="近7天" value="week" />
                <el-option label="近30天" value="month" />
                <el-option label="近3个月" value="quarter" />
              </el-select>
            </div>
          </template>
          <div class="table-container">
            <el-table :data="auditEfficiencyData" border style="width: 100%">
              <el-table-column prop="date" label="日期" align="center" width="120" />
              <el-table-column prop="total" label="认证申请数" align="center" width="120" />
              <el-table-column prop="approved" label="通过数" align="center" width="120" />
              <el-table-column prop="rejected" label="驳回数" align="center" width="120" />
              <el-table-column prop="pending" label="待处理数" align="center" width="120" />
              <el-table-column prop="avgTime" label="平均审核时间" align="center" width="150">
                <template #default="{row}">
                  {{ row.avgTime }} 小时
                </template>
              </el-table-column>
              <el-table-column prop="approvalRate" label="通过率" align="center" width="120">
                <template #default="{row}">
                  <el-progress :percentage="row.approvalRate" :color="getApprovalRateColor(row.approvalRate)" />
                </template>
              </el-table-column>
              <el-table-column prop="operator" label="审核人员" align="center" min-width="150" />
            </el-table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

export default {
  name: 'IdentityStatistics',
  setup() {
    // 图表引用
    const trendChartRef = ref(null)
    const statusChartRef = ref(null)
    const verifyTypeChartRef = ref(null)
    const ageChartRef = ref(null)
    const regionChartRef = ref(null)
    
    // 图表实例
    let trendChart = null
    let statusChart = null
    let verifyTypeChart = null
    let ageChart = null
    let regionChart = null
    
    // 状态数据
    const timeRange = ref('week')
    const auditTimeRange = ref('week')
    
    // 统计数据
    const statistics = reactive({
      totalCount: 235,
      pendingCount: 42,
      approvedCount: 180,
      rejectedCount: 13,
      totalTrend: 15.8,
      pendingTrend: -5.2,
      approvedTrend: 12.5,
      rejectedTrend: -3.1
    })
    
    // 审核效率数据
    const auditEfficiencyData = ref([
      {
        date: '2023-05-20',
        total: 15,
        approved: 12,
        rejected: 2,
        pending: 1,
        avgTime: 2.5,
        approvalRate: 85.7,
        operator: '张三、李四'
      },
      {
        date: '2023-05-19',
        total: 18,
        approved: 15,
        rejected: 2,
        pending: 1,
        avgTime: 1.8,
        approvalRate: 88.2,
        operator: '张三、王五'
      },
      {
        date: '2023-05-18',
        total: 21,
        approved: 17,
        rejected: 3,
        pending: 1,
        avgTime: 3.2,
        approvalRate: 85.0,
        operator: '李四、王五'
      },
      {
        date: '2023-05-17',
        total: 12,
        approved: 10,
        rejected: 1,
        pending: 1,
        avgTime: 1.5,
        approvalRate: 90.9,
        operator: '张三'
      },
      {
        date: '2023-05-16',
        total: 16,
        approved: 13,
        rejected: 2,
        pending: 1,
        avgTime: 2.0,
        approvalRate: 86.7,
        operator: '李四'
      }
    ])
    
    // 初始化图表
    const initCharts = () => {
      // 初始化认证数量趋势图表
      trendChart = echarts.init(trendChartRef.value)
      const trendOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['总申请', '已通过', '已驳回', '待审核']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['05-14', '05-15', '05-16', '05-17', '05-18', '05-19', '05-20']
        },
        yAxis: {
          type: 'value',
          name: '数量'
        },
        series: [
          {
            name: '总申请',
            type: 'line',
            data: [15, 18, 16, 12, 21, 18, 15],
            lineStyle: {
              color: '#409EFF'
            },
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '已通过',
            type: 'bar',
            stack: 'total',
            data: [12, 15, 13, 10, 17, 15, 12],
            itemStyle: {
              color: '#67C23A'
            }
          },
          {
            name: '已驳回',
            type: 'bar',
            stack: 'total',
            data: [2, 2, 2, 1, 3, 2, 2],
            itemStyle: {
              color: '#F56C6C'
            }
          },
          {
            name: '待审核',
            type: 'bar',
            stack: 'total',
            data: [1, 1, 1, 1, 1, 1, 1],
            itemStyle: {
              color: '#E6A23C'
            }
          }
        ]
      }
      trendChart.setOption(trendOption)
      
      // 初始化认证状态分布图表
      statusChart = echarts.init(statusChartRef.value)
      const statusOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: ['已通过', '已驳回', '待审核']
        },
        series: [
          {
            name: '认证状态',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: statistics.approvedCount, name: '已通过', itemStyle: { color: '#67C23A' } },
              { value: statistics.rejectedCount, name: '已驳回', itemStyle: { color: '#F56C6C' } },
              { value: statistics.pendingCount, name: '待审核', itemStyle: { color: '#E6A23C' } }
            ]
          }
        ]
      }
      statusChart.setOption(statusOption)
      
      // 初始化认证方式分布图表
      verifyTypeChart = echarts.init(verifyTypeChartRef.value)
      const verifyTypeOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          bottom: '0%',
          left: 'center',
          data: ['身份证认证', '人脸认证']
        },
        series: [
          {
            name: '认证方式',
            type: 'pie',
            radius: '70%',
            center: ['50%', '40%'],
            data: [
              { value: 142, name: '身份证认证', itemStyle: { color: '#409EFF' } },
              { value: 93, name: '人脸认证', itemStyle: { color: '#FF9F43' } }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      verifyTypeChart.setOption(verifyTypeOption)
      
      // 初始化年龄分布图表
      ageChart = echarts.init(ageChartRef.value)
      const ageOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['18-24岁', '25-34岁', '35-44岁', '45-54岁', '55岁以上'],
          axisLabel: {
            interval: 0,
            rotate: 30
          }
        },
        yAxis: {
          type: 'value',
          name: '人数'
        },
        series: [
          {
            name: '人数',
            type: 'bar',
            data: [35, 85, 65, 32, 18],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' }
              ])
            }
          }
        ]
      }
      ageChart.setOption(ageOption)
      
      // 初始化地区分布图表
      regionChart = echarts.init(regionChartRef.value)
      const regionOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: ['北京', '上海', '广州', '深圳', '其他']
        },
        series: [
          {
            name: '地区分布',
            type: 'pie',
            radius: '70%',
            center: ['40%', '50%'],
            data: [
              { value: 58, name: '北京' },
              { value: 45, name: '上海' },
              { value: 38, name: '广州' },
              { value: 32, name: '深圳' },
              { value: 62, name: '其他' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      regionChart.setOption(regionOption)
      
      // 监听窗口大小变化，调整图表大小
      window.addEventListener('resize', handleResize)
    }
    
    // 处理窗口大小变化
    const handleResize = () => {
      trendChart && trendChart.resize()
      statusChart && statusChart.resize()
      verifyTypeChart && verifyTypeChart.resize()
      ageChart && ageChart.resize()
      regionChart && regionChart.resize()
    }
    
    // 处理时间范围变化
    const handleTimeRangeChange = (value) => {
      let xAxisData = []
      let totalData = []
      let approvedData = []
      let rejectedData = []
      let pendingData = []
      
      switch(value) {
        case 'week':
          xAxisData = ['05-14', '05-15', '05-16', '05-17', '05-18', '05-19', '05-20']
          totalData = [15, 18, 16, 12, 21, 18, 15]
          approvedData = [12, 15, 13, 10, 17, 15, 12]
          rejectedData = [2, 2, 2, 1, 3, 2, 2]
          pendingData = [1, 1, 1, 1, 1, 1, 1]
          break
        case 'month':
          xAxisData = ['04-21', '04-26', '05-01', '05-06', '05-11', '05-16', '05-20']
          totalData = [60, 75, 85, 70, 95, 80, 65]
          approvedData = [50, 62, 70, 58, 80, 65, 55]
          rejectedData = [8, 10, 12, 8, 10, 10, 7]
          pendingData = [2, 3, 3, 4, 5, 5, 3]
          break
        case 'year':
          xAxisData = ['2022-06', '2022-08', '2022-10', '2022-12', '2023-02', '2023-04', '2023-05']
          totalData = [320, 350, 380, 420, 350, 410, 235]
          approvedData = [260, 285, 310, 350, 290, 330, 180]
          rejectedData = [40, 45, 50, 55, 45, 60, 43]
          pendingData = [20, 20, 20, 15, 15, 20, 12]
          break
      }
      
      trendChart.setOption({
        xAxis: {
          data: xAxisData
        },
        series: [
          {
            name: '总申请',
            data: totalData
          },
          {
            name: '已通过',
            data: approvedData
          },
          {
            name: '已驳回',
            data: rejectedData
          },
          {
            name: '待审核',
            data: pendingData
          }
        ]
      })
    }
    
    // 处理审核时间范围变化
    const handleAuditTimeChange = (value) => {
      // 这里可以根据选择的时间范围更新审核效率数据
      // 在实际项目中应该是从API获取数据
    }
    
    // 获取通过率颜色
    const getApprovalRateColor = (rate) => {
      if (rate >= 90) return '#67C23A'
      if (rate >= 80) return '#409EFF'
      if (rate >= 70) return '#E6A23C'
      return '#F56C6C'
    }
    
    onMounted(() => {
      initCharts()
    })
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', handleResize)
      trendChart && trendChart.dispose()
      statusChart && statusChart.dispose()
      verifyTypeChart && verifyTypeChart.dispose()
      ageChart && ageChart.dispose()
      regionChart && regionChart.dispose()
    })
    
    return {
      trendChartRef,
      statusChartRef,
      verifyTypeChartRef,
      ageChartRef,
      regionChartRef,
      timeRange,
      auditTimeRange,
      statistics,
      auditEfficiencyData,
      handleTimeRangeChange,
      handleAuditTimeChange,
      getApprovalRateColor
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.stat-card {
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  
  .stat-title {
    font-size: 16px;
    color: #606266;
    margin-bottom: 10px;
  }
  
  .stat-value {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 10px;
    
    &.primary {
      color: #409EFF;
    }
    
    &.warning {
      color: #E6A23C;
    }
    
    &.success {
      color: #67C23A;
    }
    
    &.danger {
      color: #F56C6C;
    }
  }
  
  .stat-trend {
    font-size: 13px;
    color: #909399;
    
    .trend-value {
      font-weight: bold;
      margin-left: 5px;
      
      &.up {
        color: #F56C6C;
      }
      
      &.down {
        color: #67C23A;
      }
      
      .el-icon {
        margin-left: 2px;
        font-size: 12px;
      }
    }
  }
}

.chart-row {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  width: 100%;
  height: 350px;
  
  .chart {
    width: 100%;
    height: 100%;
  }
}

.table-container {
  margin-top: 10px;
}
</style> 