<template>
  <div class="app-container">
    <div class="page-header">
      <el-page-header @back="goBack" title="返回认证列表" :content="'认证详情 #' + id" />
    </div>

    <div v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="16">
          <!-- 用户基本信息 -->
          <el-card class="box-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>基本信息</span>
                <div class="status-tag">
                  <el-tag v-if="detail.status !== undefined" :type="getStatusType(detail.status)">
                    {{ getStatusText(detail.status) }}
                  </el-tag>
                </div>
              </div>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="用户ID">{{ detail.user_id }}</el-descriptions-item>
              <el-descriptions-item label="申请时间">{{ formatDateTime(detail.created_at) }}</el-descriptions-item>
              <el-descriptions-item label="真实姓名">{{ detail.real_name }}</el-descriptions-item>
              <el-descriptions-item label="身份证号">{{ detail.id_card }}</el-descriptions-item>
              <el-descriptions-item label="手机号码">{{ detail.phone }}</el-descriptions-item>
              <el-descriptions-item label="认证方式">{{ getVerifyTypeText(detail.verify_type) }}</el-descriptions-item>
              <el-descriptions-item label="审核时间" :span="2">
                {{ detail.verified_at ? formatDateTime(detail.verified_at) : '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="审核备注" :span="2">
                {{ detail.remark || '--' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 证件照片 -->
          <el-card class="box-card id-card-section" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>证件照片</span>
              </div>
            </template>
            <div class="id-card-container">
              <div class="id-card-item">
                <div class="card-title">身份证正面</div>
                <div class="image-container">
                  <el-image 
                    :src="detail.id_card_front" 
                    fit="cover"
                    :preview-src-list="[detail.id_card_front]"
                  >
                    <template #error>
                      <div class="image-placeholder">
                        <el-icon><Picture /></el-icon>
                        <span>暂无图片</span>
                      </div>
                    </template>
                  </el-image>
                </div>
              </div>
              <div class="id-card-item">
                <div class="card-title">身份证反面</div>
                <div class="image-container">
                  <el-image 
                    :src="detail.id_card_back" 
                    fit="cover"
                    :preview-src-list="[detail.id_card_back]"
                  >
                    <template #error>
                      <div class="image-placeholder">
                        <el-icon><Picture /></el-icon>
                        <span>暂无图片</span>
                      </div>
                    </template>
                  </el-image>
                </div>
              </div>
              <div v-if="detail.verify_type === 2" class="id-card-item">
                <div class="card-title">人脸识别照片</div>
                <div class="image-container">
                  <el-image 
                    :src="detail.face_image" 
                    fit="cover"
                    :preview-src-list="[detail.face_image]"
                  >
                    <template #error>
                      <div class="image-placeholder">
                        <el-icon><Picture /></el-icon>
                        <span>暂无图片</span>
                      </div>
                    </template>
                  </el-image>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 操作日志 -->
          <el-card class="box-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>操作日志</span>
              </div>
            </template>
            <el-timeline>
              <el-timeline-item
                v-for="(log, index) in logs"
                :key="index"
                :timestamp="log.created_at"
                :type="getLogType(log.action)"
              >
                {{ log.content }}
                <div class="log-operator">操作人: {{ log.operator }}</div>
              </el-timeline-item>
            </el-timeline>
          </el-card>
        </el-col>

        <el-col :span="8">
          <!-- 用户信息卡片 -->
          <el-card class="box-card user-card" shadow="hover">
            <div class="user-header">
              <el-avatar :size="64" :src="detail.avatar || '/static/default-avatar.png'"></el-avatar>
              <div class="user-info">
                <div class="nickname">{{ detail.nickname || '未知用户' }}</div>
                <div class="user-id">用户ID: {{ detail.user_id }}</div>
              </div>
            </div>
            <div class="user-stats">
              <div class="stat-item">
                <div class="stat-value">{{ userStats.orderCount }}</div>
                <div class="stat-label">订单数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ userStats.commentCount }}</div>
                <div class="stat-label">评价数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ userStats.registerDays }}</div>
                <div class="stat-label">注册天数</div>
              </div>
            </div>
          </el-card>

          <!-- 审核操作 -->
          <el-card v-if="detail.status === 0" class="box-card action-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>审核操作</span>
              </div>
            </template>
            <div class="audit-form">
              <el-form label-position="top">
                <el-form-item label="审核备注">
                  <el-input
                    v-model="auditForm.remark"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入审核备注"
                  ></el-input>
                </el-form-item>
                <div class="audit-buttons">
                  <el-button type="success" :loading="submitting" @click="handleApprove">通过认证</el-button>
                  <el-button type="danger" :loading="submitting" @click="handleReject">驳回认证</el-button>
                </div>
              </el-form>
            </div>
          </el-card>

          <!-- 识别信息 -->
          <el-card v-if="detail.ocr_info" class="box-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>OCR识别信息</span>
              </div>
            </template>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="姓名">{{ detail.ocr_info.name }}</el-descriptions-item>
              <el-descriptions-item label="身份证号">{{ detail.ocr_info.id_number }}</el-descriptions-item>
              <el-descriptions-item label="性别">{{ detail.ocr_info.gender }}</el-descriptions-item>
              <el-descriptions-item label="民族">{{ detail.ocr_info.nationality }}</el-descriptions-item>
              <el-descriptions-item label="出生日期">{{ detail.ocr_info.birth_date }}</el-descriptions-item>
              <el-descriptions-item label="地址">{{ detail.ocr_info.address }}</el-descriptions-item>
              <el-descriptions-item label="签发机关">{{ detail.ocr_info.issuing_authority }}</el-descriptions-item>
              <el-descriptions-item label="有效期">{{ detail.ocr_info.valid_period }}</el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { formatDateTime } from '@/utils/index'

export default {
  name: 'IdentityDetail',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const id = ref(route.params.id)
    const loading = ref(true)
    const submitting = ref(false)
    
    const detail = ref({})
    const logs = ref([])
    const userStats = reactive({
      orderCount: 0,
      commentCount: 0,
      registerDays: 0
    })
    
    const auditForm = reactive({
      remark: ''
    })
    
    // 模拟数据
    const mockDetail = {
      id: route.params.id,
      user_id: 10086,
      nickname: '张三',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      real_name: '张三',
      id_card: '******************',
      phone: '13800001234',
      created_at: '2023-05-20 10:00:00',
      verified_at: null,
      verify_type: 2,
      status: 0,
      remark: '',
      id_card_front: 'https://img2.baidu.com/it/u=373017349,2292638225&fm=253&fmt=auto&app=138&f=JPEG',
      id_card_back: 'https://img0.baidu.com/it/u=1036280137,2256858090&fm=253&fmt=auto&app=138&f=JPEG',
      face_image: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      ocr_info: {
        name: '张三',
        id_number: '******************',
        gender: '男',
        nationality: '汉',
        birth_date: '1990-01-01',
        address: '北京市朝阳区某某街道某某小区',
        issuing_authority: '北京市公安局朝阳分局',
        valid_period: '2020.01.01-2030.01.01'
      }
    }
    
    const mockLogs = [
      {
        id: 1,
        action: 'create',
        content: '用户提交实名认证申请',
        operator: '系统',
        created_at: '2023-05-20 10:00:00'
      },
      {
        id: 2,
        action: 'check',
        content: '系统自动校验身份信息',
        operator: '系统',
        created_at: '2023-05-20 10:01:05'
      }
    ]
    
    const mockUserStats = {
      orderCount: 15,
      commentCount: 8,
      registerDays: 60
    }
    
    // 获取认证详情
    const getDetail = () => {
      loading.value = true
      
      // 模拟API调用
      setTimeout(() => {
        detail.value = mockDetail
        logs.value = mockLogs
        userStats.orderCount = mockUserStats.orderCount
        userStats.commentCount = mockUserStats.commentCount
        userStats.registerDays = mockUserStats.registerDays
        loading.value = false
      }, 500)
    }
    
    const getStatusType = (status) => {
      const statusMap = {
        0: 'warning',
        1: 'success',
        2: 'danger'
      }
      return statusMap[status]
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已驳回'
      }
      return statusMap[status]
    }
    
    const getVerifyTypeText = (type) => {
      const typeMap = {
        1: '身份证认证',
        2: '人脸认证'
      }
      return typeMap[type] || '未知'
    }
    
    const getLogType = (action) => {
      const typeMap = {
        create: 'primary',
        check: 'info',
        approve: 'success',
        reject: 'danger'
      }
      return typeMap[action] || 'info'
    }
    
    const goBack = () => {
      router.push('/identity/list')
    }
    
    const handleApprove = () => {
      submitAudit(1)
    }
    
    const handleReject = () => {
      if (!auditForm.remark) {
        ElMessage.warning('驳回时请填写备注说明原因')
        return
      }
      submitAudit(2)
    }
    
    const submitAudit = (status) => {
      submitting.value = true
      
      // 模拟API调用
      setTimeout(() => {
        detail.value.status = status
        detail.value.verified_at = formatDateTime(new Date())
        detail.value.remark = auditForm.remark
        
        // 添加操作日志
        logs.value.push({
          id: logs.value.length + 1,
          action: status === 1 ? 'approve' : 'reject',
          content: status === 1 ? '审核通过实名认证' : '驳回实名认证：' + auditForm.remark,
          operator: '管理员',
          created_at: formatDateTime(new Date())
        })
        
        submitting.value = false
        
        ElMessage({
          type: 'success',
          message: status === 1 ? '认证已通过' : '认证已驳回'
        })
      }, 500)
    }
    
    onMounted(() => {
      getDetail()
    })
    
    return {
      id,
      loading,
      detail,
      logs,
      userStats,
      auditForm,
      submitting,
      formatDateTime,
      getStatusType,
      getStatusText,
      getVerifyTypeText,
      getLogType,
      goBack,
      handleApprove,
      handleReject
    }
  }
}
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 20px;
}

.box-card {
  margin-bottom: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.id-card-section {
  .id-card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    
    .id-card-item {
      flex: 1;
      min-width: 250px;
      
      .card-title {
        margin-bottom: 10px;
        font-weight: bold;
      }
      
      .image-container {
        width: 100%;
        height: 180px;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        overflow: hidden;
        
        .el-image {
          width: 100%;
          height: 100%;
        }
        
        .image-placeholder {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          color: #909399;
          background: #f5f7fa;
          
          .el-icon {
            font-size: 32px;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
}

.user-card {
  .user-header {
    display: flex;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 20px;
    
    .user-info {
      margin-left: 15px;
      
      .nickname {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .user-id {
        color: #909399;
        font-size: 13px;
      }
    }
  }
  
  .user-stats {
    display: flex;
    justify-content: space-around;
    text-align: center;
    border-top: 1px solid #ebeef5;
    padding-top: 15px;
    
    .stat-item {
      .stat-value {
        font-size: 20px;
        font-weight: bold;
        color: #409eff;
        margin-bottom: 5px;
      }
      
      .stat-label {
        color: #606266;
        font-size: 13px;
      }
    }
  }
}

.action-card {
  .audit-form {
    .audit-buttons {
      display: flex;
      justify-content: space-between;
    }
  }
}

.log-operator {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style> 