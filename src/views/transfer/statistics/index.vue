<template>
  <div class="page-container">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>驿站转让统计分析</span>
          <div class="header-right">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="date-picker"
              @change="handleDateChange"
            />
          </div>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6" v-for="(stat, index) in statistics" :key="index">
          <div class="stat-card">
            <div class="stat-icon" :class="stat.icon">
              <i :class="stat.iconClass"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">{{ stat.title }}</div>
              <div class="stat-number">{{ stat.value }}</div>
              <div class="stat-desc">
                <span :class="stat.isIncrease ? 'up' : 'down'">
                  <i :class="stat.isIncrease ? 'el-icon-top' : 'el-icon-bottom'"></i>
                  {{ stat.rate }}%
                </span>
                较上期
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <div class="chart-wrapper">
            <div class="chart-title">转让数量趋势</div>
            <div class="chart-container" id="trendChart"></div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-wrapper">
            <div class="chart-title">驿站类型分布</div>
            <div class="chart-container" id="typeChart"></div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <div class="chart-wrapper">
            <div class="chart-title">区域分布</div>
            <div class="chart-container" id="regionChart"></div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-wrapper">
            <div class="chart-title">价格区间分布</div>
            <div class="chart-container" id="priceChart"></div>
          </div>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="table-title">热门转让驿站排行</div>
          <el-table :data="hotStations" style="width: 100%" border>
            <el-table-column type="index" label="#" width="50" />
            <el-table-column prop="stationName" label="驿站名称" min-width="160" />
            <el-table-column prop="region" label="所在区域" width="150" />
            <el-table-column prop="type" label="驿站类型" width="120" />
            <el-table-column prop="price" label="转让价格" width="120">
              <template #default="scope">{{ scope.row.price }} 元</template>
            </el-table-column>
            <el-table-column prop="views" label="浏览量" width="100" sortable />
            <el-table-column prop="likes" label="收藏数" width="100" sortable />
            <el-table-column prop="contacts" label="联系数" width="100" sortable />
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'TransferStatistics',
  data() {
    return {
      dateRange: [this.getLastMonthDate(), new Date()],
      statistics: [
        {
          title: '转让总数',
          value: 358,
          rate: 12.5,
          isIncrease: true,
          icon: 'blue',
          iconClass: 'el-icon-s-shop'
        },
        {
          title: '已成交数',
          value: 156,
          rate: 8.3,
          isIncrease: true,
          icon: 'green',
          iconClass: 'el-icon-s-claim'
        },
        {
          title: '平均价格',
          value: '25,630',
          rate: 3.2,
          isIncrease: false,
          icon: 'orange',
          iconClass: 'el-icon-money'
        },
        {
          title: '总浏览量',
          value: '12,568',
          rate: 15.6,
          isIncrease: true,
          icon: 'purple',
          iconClass: 'el-icon-view'
        }
      ],
      hotStations: [
        {
          stationName: '海淀区中关村社区驿站',
          region: '北京市海淀区',
          type: '社区驿站',
          price: 25000,
          views: 1256,
          likes: 45,
          contacts: 32
        },
        {
          stationName: '朝阳区望京SOHO商务驿站',
          region: '北京市朝阳区',
          type: '写字楼驿站',
          price: 35000,
          views: 986,
          likes: 38,
          contacts: 28
        },
        {
          stationName: '西城区西单商业驿站',
          region: '北京市西城区',
          type: '商业驿站',
          price: 40000,
          views: 875,
          likes: 32,
          contacts: 24
        },
        {
          stationName: '丰台区丽泽SOHO驿站',
          region: '北京市丰台区',
          type: '写字楼驿站',
          price: 28000,
          views: 752,
          likes: 28,
          contacts: 21
        },
        {
          stationName: '通州区万达广场驿站',
          region: '北京市通州区',
          type: '商业驿站',
          price: 32000,
          views: 684,
          likes: 26,
          contacts: 19
        }
      ],
      // 图表实例
      trendChart: null,
      typeChart: null,
      regionChart: null,
      priceChart: null
    }
  },
  mounted() {
    this.initCharts()
  },
  methods: {
    /** 获取上个月的日期 */
    getLastMonthDate() {
      const date = new Date()
      date.setMonth(date.getMonth() - 1)
      return date
    },
    /** 日期变化处理 */
    handleDateChange() {
      // TODO: 根据日期范围获取统计数据
      this.initCharts()
    },
    /** 初始化图表 */
    initCharts() {
      this.$nextTick(() => {
        // 转让数量趋势图
        this.trendChart = echarts.init(document.getElementById('trendChart'))
        this.trendChart.setOption({
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['转让数量', '成交数量']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '转让数量',
              type: 'line',
              data: [32, 45, 38, 52, 48, 60, 55],
              smooth: true,
              lineStyle: {
                width: 3,
                color: '#409EFF'
              },
              areaStyle: {
                opacity: 0.2,
                color: '#409EFF'
              }
            },
            {
              name: '成交数量',
              type: 'line',
              data: [15, 20, 18, 25, 22, 30, 26],
              smooth: true,
              lineStyle: {
                width: 3,
                color: '#67C23A'
              },
              areaStyle: {
                opacity: 0.2,
                color: '#67C23A'
              }
            }
          ]
        })
        
        // 驿站类型分布图
        this.typeChart = echarts.init(document.getElementById('typeChart'))
        this.typeChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 10,
            data: ['社区驿站', '快递驿站', '校园驿站', '写字楼驿站', '商业驿站']
          },
          series: [
            {
              name: '驿站类型',
              type: 'pie',
              radius: ['50%', '70%'],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: 'center'
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '18',
                  fontWeight: 'bold'
                }
              },
              labelLine: {
                show: false
              },
              data: [
                { value: 135, name: '社区驿站' },
                { value: 86, name: '快递驿站' },
                { value: 45, name: '校园驿站' },
                { value: 68, name: '写字楼驿站' },
                { value: 24, name: '商业驿站' }
              ]
            }
          ]
        })
        
        // 区域分布图
        this.regionChart = echarts.init(document.getElementById('regionChart'))
        this.regionChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'value'
          },
          yAxis: {
            type: 'category',
            data: ['海淀区', '朝阳区', '西城区', '东城区', '丰台区', '通州区', '昌平区']
          },
          series: [
            {
              name: '转让数量',
              type: 'bar',
              data: [85, 72, 45, 38, 42, 36, 40],
              itemStyle: {
                color: '#E6A23C'
              }
            }
          ]
        })
        
        // 价格区间分布图
        this.priceChart = echarts.init(document.getElementById('priceChart'))
        this.priceChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 10,
            data: ['1万以下', '1-3万', '3-5万', '5-10万', '10万以上']
          },
          series: [
            {
              name: '价格区间',
              type: 'pie',
              radius: '55%',
              center: ['50%', '60%'],
              data: [
                { value: 42, name: '1万以下' },
                { value: 124, name: '1-3万' },
                { value: 98, name: '3-5万' },
                { value: 65, name: '5-10万' },
                { value: 29, name: '10万以上' }
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        })
        
        // 窗口大小变化时，重新调整图表大小
        window.addEventListener('resize', () => {
          this.trendChart.resize()
          this.typeChart.resize()
          this.regionChart.resize()
          this.priceChart.resize()
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .header-right {
    .date-picker {
      width: 320px;
    }
  }
}

.stat-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    
    i {
      font-size: 30px;
      color: #fff;
    }
    
    &.blue {
      background-color: #409EFF;
    }
    
    &.green {
      background-color: #67C23A;
    }
    
    &.orange {
      background-color: #E6A23C;
    }
    
    &.purple {
      background-color: #9966CC;
    }
  }
  
  .stat-info {
    flex: 1;
    
    .stat-title {
      color: #606266;
      font-size: 14px;
      margin-bottom: 5px;
    }
    
    .stat-number {
      color: #303133;
      font-size: 22px;
      font-weight: bold;
      margin-bottom: 5px;
    }
    
    .stat-desc {
      color: #909399;
      font-size: 12px;
      
      .up {
        color: #67C23A;
      }
      
      .down {
        color: #F56C6C;
      }
    }
  }
}

.chart-row {
  margin-top: 20px;
  margin-bottom: 20px;
}

.chart-wrapper {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 15px;
  margin-bottom: 20px;
  
  .chart-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    text-align: center;
  }
  
  .chart-container {
    height: 300px;
  }
}

.table-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
}
</style> 