<template>
  <div class="page-container">
    <div class="action-container">
      <div class="search-box">
        <el-input v-model="queryParams.keyword" placeholder="请输入驿站名称/联系人" clearable style="width: 200px" class="mr-10" />
        <el-select v-model="queryParams.status" placeholder="转让状态" clearable class="mr-10" style="width: 120px">
          <el-option label="待审核" value="0" />
          <el-option label="已上架" value="1" />
          <el-option label="已交易" value="2" />
          <el-option label="已下架" value="3" />
          <el-option label="已关闭" value="4" />
        </el-select>
        <el-select v-model="queryParams.type" placeholder="驿站类型" clearable class="mr-10" style="width: 120px">
          <el-option label="快递驿站" value="1" />
          <el-option label="社区驿站" value="2" />
          <el-option label="校园驿站" value="3" />
          <el-option label="写字楼驿站" value="4" />
        </el-select>
        <el-cascader
          v-model="queryParams.region"
          :options="regionOptions"
          placeholder="选择地区"
          clearable
          class="mr-10"
          style="width: 220px"
        />
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </div>
      <div class="button-group">
        <el-button type="primary" @click="handleAdd">新增转让</el-button>
        <el-button type="success" @click="handleExport">导出</el-button>
      </div>
    </div>
    
    <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane v-for="item in statusTabs" :key="item.name" :label="item.label" :name="item.name">
        <el-table v-loading="loading" :data="transferList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="stationName" label="驿站名称" min-width="120" show-overflow-tooltip />
          <el-table-column prop="stationType" label="驿站类型" width="100">
            <template #default="scope">
              {{ stationTypeMap[scope.row.stationType] }}
            </template>
          </el-table-column>
          <el-table-column prop="region" label="所在地区" min-width="140" show-overflow-tooltip />
          <el-table-column prop="address" label="详细地址" min-width="160" show-overflow-tooltip />
          <el-table-column prop="price" label="转让价格" width="100">
            <template #default="scope">
              {{ scope.row.price }}元
            </template>
          </el-table-column>
          <el-table-column prop="contactName" label="联系人" width="100" />
          <el-table-column prop="contactPhone" label="联系电话" width="120" />
          <el-table-column prop="createTime" label="发布时间" width="160" sortable />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusTag(scope.row.status)">
                {{ statusMap[scope.row.status] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" fixed="right">
            <template #default="scope">
              <el-button type="text" @click="handleView(scope.row)">查看</el-button>
              <el-button type="text" @click="handleEdit(scope.row)" v-if="scope.row.status !== '2'">编辑</el-button>
              <el-button type="text" @click="handleDelete(scope.row)" v-if="scope.row.status !== '2'">删除</el-button>
              <el-button type="text" @click="handleStatus(scope.row, '2')" v-if="scope.row.status === '1'">标记交易</el-button>
              <el-button type="text" @click="handleStatus(scope.row, '1')" v-if="scope.row.status === '3' || scope.row.status === '4'">上架</el-button>
              <el-button type="text" @click="handleStatus(scope.row, '3')" v-if="scope.row.status === '1'">下架</el-button>
              <el-button type="text" @click="handleViolation(scope.row)" v-if="scope.row.status === '1'">违规</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 新增/编辑弹窗 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="700px" append-to-body>
      <el-form ref="transferForm" :model="transferForm" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="驿站名称" prop="stationName">
              <el-input v-model="transferForm.stationName" placeholder="请输入驿站名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="驿站类型" prop="stationType">
              <el-select v-model="transferForm.stationType" placeholder="请选择驿站类型" style="width: 100%">
                <el-option label="快递驿站" value="1" />
                <el-option label="社区驿站" value="2" />
                <el-option label="校园驿站" value="3" />
                <el-option label="写字楼驿站" value="4" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="所在地区" prop="region">
          <el-cascader
            v-model="transferForm.region"
            :options="regionOptions"
            placeholder="请选择所在地区"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="transferForm.address" placeholder="请输入详细地址" />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="转让价格" prop="price">
              <el-input-number v-model="transferForm.price" :min="0" :max="9999999" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="月营业额" prop="monthlyRevenue">
              <el-input-number v-model="transferForm.monthlyRevenue" :min="0" :max="9999999" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactName">
              <el-input v-model="transferForm.contactName" placeholder="请输入联系人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="transferForm.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="转让原因" prop="reason">
          <el-input v-model="transferForm.reason" type="textarea" placeholder="请输入转让原因" />
        </el-form-item>
        
        <el-form-item label="补充说明" prop="description">
          <el-input v-model="transferForm.description" type="textarea" placeholder="请输入补充说明" />
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="transferForm.status">
            <el-radio label="0">待审核</el-radio>
            <el-radio label="1">已上架</el-radio>
            <el-radio label="3">已下架</el-radio>
            <el-radio label="4">已关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="图片上传">
          <el-upload
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :file-list="fileList"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :on-change="handleChange"
          >
            <i class="el-icon-plus"></i>
            <template #tip>
              <div class="el-upload__tip">请上传驿站门店照片、内部环境等相关图片</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 图片预览弹窗 -->
    <el-dialog v-model="previewVisible" title="图片预览">
      <img :src="previewUrl" alt="Preview Image" style="width: 100%;" />
    </el-dialog>
    
    <!-- 违规原因选择弹窗 -->
    <el-dialog title="违规下架" v-model="violationDialogVisible" width="500px" append-to-body>
      <el-form ref="violationForm" :model="violationForm" label-width="80px">
        <el-form-item label="违规类型" prop="reason">
          <el-select v-model="violationForm.reason" placeholder="请选择违规类型" style="width: 100%">
            <el-option v-for="item in violationOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="补充说明" prop="remark">
          <el-input v-model="violationForm.remark" type="textarea" placeholder="请输入补充说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="violationDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitViolation">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'TransferList',
  data() {
    return {
      // 激活的标签页
      activeName: 'all',
      // 状态标签页
      statusTabs: [
        { label: '全部转让', name: 'all' },
        { label: '待审核', name: '0' },
        { label: '已上架', name: '1' },
        { label: '已交易', name: '2' },
        { label: '已下架', name: '3' },
        { label: '已关闭', name: '4' }
      ],
      // 遮罩层
      loading: false,
      // 选中数组
      selectedIds: [],
      // 总条数
      total: 0,
      // 驿站类型映射
      stationTypeMap: {
        '1': '快递驿站',
        '2': '社区驿站',
        '3': '校园驿站',
        '4': '写字楼驿站'
      },
      // 状态映射
      statusMap: {
        '0': '待审核',
        '1': '已上架',
        '2': '已交易',
        '3': '已下架',
        '4': '已关闭'
      },
      // 违规原因选项
      violationOptions: [
        { label: '虚假信息', value: '虚假信息' },
        { label: '信息不完整', value: '信息不完整' },
        { label: '违反平台规则', value: '违反平台规则' },
        { label: '低质内容', value: '低质内容' },
        { label: '其他违规', value: '其他违规' }
      ],
      // 地区选项
      regionOptions: [
        {
          value: '北京市',
          label: '北京市',
          children: [
            {
              value: '北京市',
              label: '北京市',
              children: [
                { value: '朝阳区', label: '朝阳区' },
                { value: '海淀区', label: '海淀区' },
                { value: '东城区', label: '东城区' },
                { value: '西城区', label: '西城区' },
                { value: '丰台区', label: '丰台区' }
              ]
            }
          ]
        },
        {
          value: '上海市',
          label: '上海市',
          children: [
            {
              value: '上海市',
              label: '上海市',
              children: [
                { value: '黄浦区', label: '黄浦区' },
                { value: '徐汇区', label: '徐汇区' },
                { value: '长宁区', label: '长宁区' },
                { value: '静安区', label: '静安区' },
                { value: '浦东新区', label: '浦东新区' }
              ]
            }
          ]
        }
      ],
      // 转让列表
      transferList: [
        {
          id: 1,
          stationName: '海淀区中关村社区驿站',
          stationType: '2',
          region: '北京市 北京市 海淀区',
          address: '北京市海淀区中关村大街123号',
          price: 25000,
          monthlyRevenue: 18000,
          contactName: '张先生',
          contactPhone: '13800138001',
          reason: '个人原因，无法继续经营',
          description: '该驿站位于中关村科技园区内，日均快递量200+，客流量大，接手可立即营业。',
          createTime: '2023-04-15 10:30:00',
          status: '1'
        },
        {
          id: 2,
          stationName: '朝阳区望京SOHO快递驿站',
          stationType: '4',
          region: '北京市 北京市 朝阳区',
          address: '北京市朝阳区望京SOHO T1',
          price: 35000,
          monthlyRevenue: 22000,
          contactName: '李先生',
          contactPhone: '13800138002',
          reason: '另有发展，转让驿站',
          description: '位于写字楼内的快递驿站，周边写字楼多，客流量稳定，月收入2万以上。',
          createTime: '2023-04-16 11:20:00',
          status: '0'
        },
        {
          id: 3,
          stationName: '北京大学校内驿站',
          stationType: '3',
          region: '北京市 北京市 海淀区',
          address: '北京市海淀区颐和园路5号',
          price: 30000,
          monthlyRevenue: 15000,
          contactName: '王先生',
          contactPhone: '13800138003',
          reason: '已找到其他工作，无暇经营',
          description: '校内驿站，寄件、取件和中转业务量大，具有垄断优势，收益稳定。',
          createTime: '2023-04-14 09:45:00',
          status: '2'
        }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        status: '',
        type: '',
        region: null
      },
      // 弹出层标题
      dialogTitle: '',
      // 是否显示弹出层
      dialogVisible: false,
      // 表单参数
      transferForm: {
        id: undefined,
        stationName: '',
        stationType: '',
        region: [],
        address: '',
        price: 0,
        monthlyRevenue: 0,
        contactName: '',
        contactPhone: '',
        reason: '',
        description: '',
        status: '0'
      },
      // 表单校验
      rules: {
        stationName: [
          { required: true, message: '驿站名称不能为空', trigger: 'blur' }
        ],
        stationType: [
          { required: true, message: '驿站类型不能为空', trigger: 'change' }
        ],
        region: [
          { required: true, message: '所在地区不能为空', trigger: 'change' }
        ],
        address: [
          { required: true, message: '详细地址不能为空', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '转让价格不能为空', trigger: 'blur' }
        ],
        contactName: [
          { required: true, message: '联系人不能为空', trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '联系电话不能为空', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        reason: [
          { required: true, message: '转让原因不能为空', trigger: 'blur' }
        ]
      },
      // 图片预览
      previewVisible: false,
      previewUrl: '',
      fileList: [],
      // 违规弹窗
      violationDialogVisible: false,
      // 违规表单
      violationForm: {
        id: undefined,
        reason: '',
        remark: ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 获取状态标签样式 */
    getStatusTag(status) {
      const statusMap = {
        '0': 'warning',
        '1': 'success',
        '2': 'info',
        '3': 'danger',
        '4': 'info'
      }
      return statusMap[status] || ''
    },
    /** 查询列表 */
    getList() {
      this.loading = true
      
      // 根据标签页筛选状态
      const params = { ...this.queryParams }
      if (this.activeName !== 'all') {
        params.status = this.activeName
      }
      
      // TODO: 实际应调用API获取数据
      setTimeout(() => {
        // 模拟API调用
        this.total = this.transferList.length
        this.loading = false
      }, 500)
    },
    /** 切换标签页 */
    handleTabClick() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 查询按钮 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置查询 */
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        keyword: '',
        status: '',
        type: '',
        region: null
      }
      this.handleQuery()
    },
    /** 选择项变化 */
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.dialogTitle = '新增驿站转让'
      this.resetForm()
      this.dialogVisible = true
    },
    /** 编辑按钮操作 */
    handleEdit(row) {
      this.dialogTitle = '编辑驿站转让'
      this.resetForm()
      // 处理地区的级联选择器数据格式
      const regionData = row.region ? row.region.split(' ') : []
      this.transferForm = {
        ...row,
        region: regionData
      }
      // TODO: 获取图片列表，设置fileList
      this.fileList = []
      this.dialogVisible = true
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm(`确认删除驿站转让信息"${row.stationName}"?`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用删除API
        console.log('删除ID：', row.id)
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      }).catch(() => {})
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.$router.push(`/transfer/detail/${row.id}`)
    },
    /** 更改状态 */
    handleStatus(row, status) {
      let statusText = ''
      switch (status) {
        case '1':
          statusText = '上架'
          break
        case '2':
          statusText = '标记为已交易'
          break
        case '3':
          statusText = '下架'
          break
        case '4':
          statusText = '关闭'
          break
      }
      
      this.$confirm(`确认要${statusText}驿站转让"${row.stationName}"?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // TODO: 调用更新状态API
        console.log('更新状态ID：', row.id, '状态：', status)
        this.$message({
          type: 'success',
          message: '操作成功!'
        })
      }).catch(() => {})
    },
    /** 违规下架操作 */
    handleViolation(row) {
      this.violationForm = {
        id: row.id,
        reason: '',
        remark: ''
      }
      this.violationDialogVisible = true
    },
    /** 提交违规下架 */
    submitViolation() {
      if (!this.violationForm.reason) {
        this.$message({
          type: 'warning',
          message: '请选择违规类型'
        })
        return
      }
      
      // TODO: 调用违规下架API
      console.log('违规下架ID：', this.violationForm.id)
      console.log('违规原因：', this.violationForm.reason)
      console.log('补充说明：', this.violationForm.remark)
      
      this.violationDialogVisible = false
      this.$message({
        type: 'success',
        message: '违规下架成功!'
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      // TODO: 调用导出API
      this.$message({
        type: 'success',
        message: '导出成功!'
      })
    },
    /** 图片预览 */
    handlePreview(file) {
      this.previewUrl = file.url || URL.createObjectURL(file.raw)
      this.previewVisible = true
    },
    /** 移除图片 */
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    /** 图片变化 */
    handleChange(file, fileList) {
      this.fileList = fileList
    },
    /** 提交表单 */
    submitForm() {
      this.$refs.transferForm.validate(valid => {
        if (valid) {
          // 处理地区数据，转换为字符串
          const regionStr = this.transferForm.region.join(' ')
          const formData = {
            ...this.transferForm,
            region: regionStr
          }
          
          if (this.transferForm.id) {
            // 编辑更新
            // TODO: 调用更新API
            console.log('更新数据：', formData)
          } else {
            // 新增
            // TODO: 调用新增API
            console.log('新增数据：', formData)
          }
          
          this.dialogVisible = false
          this.$message({
            type: 'success',
            message: this.transferForm.id ? '修改成功!' : '新增成功!'
          })
        }
      })
    },
    /** 重置表单 */
    resetForm() {
      this.fileList = []
      this.transferForm = {
        id: undefined,
        stationName: '',
        stationType: '',
        region: [],
        address: '',
        price: 0,
        monthlyRevenue: 0,
        contactName: '',
        contactPhone: '',
        reason: '',
        description: '',
        status: '0'
      }
    },
    /** 分页大小变化 */
    handleSizeChange(val) {
      this.queryParams.pageSize = val
      this.getList()
    },
    /** 分页页码变化 */
    handleCurrentChange(val) {
      this.queryParams.pageNum = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.action-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .search-box {
    display: flex;
    align-items: center;
  }
  
  .button-group {
    display: flex;
    align-items: center;
  }
}

.mr-10 {
  margin-right: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.el-upload__tip {
  line-height: 1.2;
  margin-top: 8px;
  color: #909399;
}
</style> 