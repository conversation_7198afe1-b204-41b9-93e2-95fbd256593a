<template>
    <div class="page-container">
      <div class="page-header">
        <el-page-header @back="goBack" :content="stationInfo.stationName || '驿站转让详情'" />
      </div>
  
      <el-card class="box-card" v-loading="loading">
        <div class="detail-header">
          <div class="station-title">
            <h2>{{ stationInfo.stationName }}</h2>
            <el-tag :type="getStatusTag(stationInfo.status)" class="ml-10">{{ statusMap[stationInfo.status] }}</el-tag>
          </div>
          <div class="operation-buttons" v-if="stationInfo.status !== '2'">
            <el-button type="primary" @click="handleEdit">编辑</el-button>
            <el-button type="success" v-if="stationInfo.status === '0'" @click="handleStatus('1')">审核通过</el-button>
            <el-button type="danger" v-if="stationInfo.status === '0'" @click="handleReject">审核拒绝</el-button>
            <el-button type="warning" v-if="stationInfo.status === '1'" @click="handleStatus('3')">下架</el-button>
            <el-button type="info" v-if="stationInfo.status === '1'" @click="handleStatus('2')">标记已交易</el-button>
          </div>
        </div>
  
        <el-divider />
  
        <el-row :gutter="20">
          <el-col :span="16">
            <el-descriptions title="基本信息" :column="2" border>
              <el-descriptions-item label="驿站名称">{{ stationInfo.stationName }}</el-descriptions-item>
              <el-descriptions-item label="驿站类型">{{ stationTypeMap[stationInfo.stationType] }}</el-descriptions-item>
              <el-descriptions-item label="所在区域">{{ stationInfo.region }}</el-descriptions-item>
              <el-descriptions-item label="详细地址">{{ stationInfo.address }}</el-descriptions-item>
              <el-descriptions-item label="转让价格">{{ stationInfo.price }} 元</el-descriptions-item>
              <el-descriptions-item label="月营业额">{{ stationInfo.monthlyRevenue }} 元</el-descriptions-item>
              <el-descriptions-item label="联系人">{{ stationInfo.contactName }}</el-descriptions-item>
              <el-descriptions-item label="联系电话">{{ stationInfo.contactPhone }}</el-descriptions-item>
              <el-descriptions-item label="发布时间">{{ stationInfo.createTime }}</el-descriptions-item>
              <el-descriptions-item label="更新时间">{{ stationInfo.updateTime }}</el-descriptions-item>
            </el-descriptions>
  
            <el-divider />
  
            <el-descriptions title="转让信息" :column="1" border>
              <el-descriptions-item label="转让原因">{{ stationInfo.reason }}</el-descriptions-item>
              <el-descriptions-item label="补充说明">{{ stationInfo.description }}</el-descriptions-item>
            </el-descriptions>
  
            <el-divider />
  
            <div class="station-photos">
              <h3>驿站照片</h3>
              <div class="photo-list">
                <el-image 
                  v-for="(url, index) in stationInfo.photos" 
                  :key="index"
                  :src="url" 
                  fit="cover"
                  class="station-photo"
                  :preview-src-list="stationInfo.photos"
                />
                <div class="no-data" v-if="!stationInfo.photos || stationInfo.photos.length === 0">
                  暂无照片
                </div>
              </div>
            </div>
          </el-col>
  
          <el-col :span="8">
            <el-card class="side-card">
              <template #header>
                <div class="card-header">
                  <span>转让日志</span>
                </div>
              </template>
              <div class="log-timeline">
                <el-timeline>
                  <el-timeline-item
                    v-for="(activity, index) in activities"
                    :key="index"
                    :type="activity.type"
                    :color="activity.color"
                    :timestamp="activity.timestamp"
                  >
                    {{ activity.content }}
                  </el-timeline-item>
                </el-timeline>
                <div class="no-data" v-if="activities.length === 0">
                  暂无日志记录
                </div>
              </div>
            </el-card>
  
            <el-card class="side-card mt-20">
              <template #header>
                <div class="card-header">
                  <span>联系记录</span>
                  <el-button type="text" @click="addContact">添加记录</el-button>
                </div>
              </template>
              <div class="contact-list">
                <div v-for="(contact, index) in contactRecords" :key="index" class="contact-item">
                  <div class="contact-header">
                    <span class="contact-user">{{ contact.contactPerson }}</span>
                    <span class="contact-time">{{ contact.contactTime }}</span>
                  </div>
                  <div class="contact-content">{{ contact.contactContent }}</div>
                  <div class="contact-method">联系方式：{{ contact.contactMethod }}</div>
                </div>
                <div class="no-data" v-if="contactRecords.length === 0">
                  暂无联系记录
                </div>
              </div>
            </el-card>
  
            <el-card class="side-card mt-20">
              <template #header>
                <div class="card-header">
                  <span>意向买家</span>
                  <el-button type="text" @click="addBuyer">添加买家</el-button>
                </div>
              </template>
              <div class="buyer-list">
                <el-table :data="buyerList" style="width: 100%">
                  <el-table-column prop="name" label="姓名" min-width="70" />
                  <el-table-column prop="phone" label="联系电话" min-width="110" />
                  <el-table-column prop="level" label="意向度" width="90">
                    <template #default="scope">
                      <el-rate v-model="scope.row.level" disabled text-color="#ff9900" />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80">
                    <template #default="scope">
                      <el-button type="text" size="small" @click="handleBuyerDetail(scope.row)">详情</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="no-data" v-if="buyerList.length === 0">
                  暂无意向买家
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-card>
  
      <!-- 添加联系记录 -->
      <el-dialog title="添加联系记录" v-model="contactDialogVisible" width="500px" append-to-body>
        <el-form ref="contactForm" :model="contactForm" :rules="contactRules" label-width="100px">
          <el-form-item label="联系人" prop="contactPerson">
            <el-input v-model="contactForm.contactPerson" placeholder="请输入联系人" />
          </el-form-item>
          <el-form-item label="联系方式" prop="contactMethod">
            <el-select v-model="contactForm.contactMethod" placeholder="请选择联系方式" style="width: 100%">
              <el-option label="电话" value="电话" />
              <el-option label="微信" value="微信" />
              <el-option label="当面" value="当面" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
          <el-form-item label="联系内容" prop="contactContent">
            <el-input v-model="contactForm.contactContent" type="textarea" rows="4" placeholder="请输入联系内容" />
          </el-form-item>
          <el-form-item label="联系时间" prop="contactTime">
            <el-date-picker v-model="contactForm.contactTime" type="datetime" placeholder="选择联系时间" style="width: 100%" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="contactDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitContactForm">确 定</el-button>
          </div>
        </template>
      </el-dialog>
  
      <!-- 添加意向买家 -->
      <el-dialog title="添加意向买家" v-model="buyerDialogVisible" width="500px" append-to-body>
        <el-form ref="buyerForm" :model="buyerForm" :rules="buyerRules" label-width="100px">
          <el-form-item label="买家姓名" prop="name">
            <el-input v-model="buyerForm.name" placeholder="请输入买家姓名" />
          </el-form-item>
          <el-form-item label="联系电话" prop="phone">
            <el-input v-model="buyerForm.phone" placeholder="请输入联系电话" />
          </el-form-item>
          <el-form-item label="意向度" prop="level">
            <el-rate v-model="buyerForm.level" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="buyerForm.remark" type="textarea" rows="4" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="buyerDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitBuyerForm">确 定</el-button>
          </div>
        </template>
      </el-dialog>
  
      <!-- 审核拒绝 -->
      <el-dialog title="审核拒绝" v-model="rejectDialogVisible" width="500px" append-to-body>
        <el-form ref="rejectForm" :model="rejectForm" :rules="rejectRules" label-width="80px">
          <el-form-item label="拒绝原因" prop="reason">
            <el-select v-model="rejectForm.reason" placeholder="请选择拒绝原因" style="width: 100%">
              <el-option label="信息不完整" value="信息不完整" />
              <el-option label="照片不清晰" value="照片不清晰" />
              <el-option label="违反规定" value="违反规定" />
              <el-option label="信息虚假" value="信息虚假" />
              <el-option label="其他原因" value="其他原因" />
            </el-select>
          </el-form-item>
          <el-form-item label="详细说明" prop="description">
            <el-input v-model="rejectForm.description" type="textarea" rows="4" placeholder="请输入详细说明" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="rejectDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitRejectForm">确 定</el-button>
          </div>
        </template>
      </el-dialog>
  
      <!-- 买家详情 -->
      <el-dialog title="意向买家详情" v-model="buyerDetailVisible" width="500px" append-to-body>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="买家姓名">{{ currentBuyer.name }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ currentBuyer.phone }}</el-descriptions-item>
          <el-descriptions-item label="意向度">
            <el-rate v-model="currentBuyer.level" disabled text-color="#ff9900" />
          </el-descriptions-item>
          <el-descriptions-item label="添加时间">{{ currentBuyer.createTime }}</el-descriptions-item>
          <el-descriptions-item label="备注">{{ currentBuyer.remark }}</el-descriptions-item>
        </el-descriptions>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="danger" @click="deleteBuyer">删除</el-button>
            <el-button @click="buyerDetailVisible = false">关闭</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </template>
  
  <script>
  export default {
    name: 'TransferDetail',
    data() {
      return {
        loading: false,
        stationId: null,
        // 驿站信息
        stationInfo: {
          id: 1,
          stationName: '海淀区中关村社区驿站',
          stationType: '2',
          region: '北京市 北京市 海淀区',
          address: '北京市海淀区中关村大街123号',
          price: 25000,
          monthlyRevenue: 18000,
          contactName: '张先生',
          contactPhone: '13800138001',
          reason: '个人原因，无法继续经营',
          description: '该驿站位于中关村科技园区内，日均快递量200+，客流量大，接手可立即营业。驿站现有固定客户群体，主要为周边小区居民和写字楼白领。店铺面积约30平米，月租金3000元，租约还有2年期限，可以续签。',
          createTime: '2023-04-15 10:30:00',
          updateTime: '2023-04-16 14:15:00',
          status: '1',
          photos: [
            'https://img1.baidu.com/it/u=413646961,2312577432&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333',
            'https://img2.baidu.com/it/u=1003272215,1878948666&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800',
            'https://img0.baidu.com/it/u=2277942570,1789375638&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333'
          ]
        },
        // 驿站类型映射
        stationTypeMap: {
          '1': '快递驿站',
          '2': '社区驿站',
          '3': '校园驿站',
          '4': '写字楼驿站'
        },
        // 状态映射
        statusMap: {
          '0': '待审核',
          '1': '已上架',
          '2': '已交易',
          '3': '已下架',
          '4': '已关闭'
        },
        // 操作日志
        activities: [
          {
            content: '系统自动创建转让信息',
            timestamp: '2023-04-15 10:30:00',
            type: 'primary',
            color: '#0bbd87'
          },
          {
            content: '管理员审核通过',
            timestamp: '2023-04-15 14:20:00',
            type: 'success',
            color: '#0bbd87'
          },
          {
            content: '更新了驿站信息',
            timestamp: '2023-04-16 14:15:00',
            type: 'warning',
            color: '#e6a23c'
          }
        ],
        // 联系记录
        contactRecords: [
          {
            id: 1,
            contactPerson: '刘先生',
            contactTime: '2023-04-17 09:30:00',
            contactMethod: '电话',
            contactContent: '询问驿站日均快递量及收益情况，对价格有一定异议，希望再降低5000元。'
          },
          {
            id: 2,
            contactPerson: '赵女士',
            contactTime: '2023-04-18 15:40:00',
            contactMethod: '微信',
            contactContent: '详细了解了驿站的经营情况，表示对该驿站比较满意，计划近期前往实地考察。'
          }
        ],
        // 意向买家
        buyerList: [
          {
            id: 1,
            name: '刘先生',
            phone: '13912345678',
            level: 3,
            remark: '对价格有异议，希望降价',
            createTime: '2023-04-17 09:30:00'
          },
          {
            id: 2,
            name: '赵女士',
            phone: '13812345678',
            level: 4,
            remark: '计划近期考察，意向较强',
            createTime: '2023-04-18 15:40:00'
          }
        ],
        // 添加联系记录对话框
        contactDialogVisible: false,
        contactForm: {
          contactPerson: '',
          contactMethod: '',
          contactContent: '',
          contactTime: new Date()
        },
        contactRules: {
          contactPerson: [
            { required: true, message: '请输入联系人', trigger: 'blur' }
          ],
          contactMethod: [
            { required: true, message: '请选择联系方式', trigger: 'change' }
          ],
          contactContent: [
            { required: true, message: '请输入联系内容', trigger: 'blur' }
          ],
          contactTime: [
            { required: true, message: '请选择联系时间', trigger: 'change' }
          ]
        },
        // 添加意向买家对话框
        buyerDialogVisible: false,
        buyerForm: {
          name: '',
          phone: '',
          level: 3,
          remark: ''
        },
        buyerRules: {
          name: [
            { required: true, message: '请输入买家姓名', trigger: 'blur' }
          ],
          phone: [
            { required: true, message: '请输入联系电话', trigger: 'blur' },
            { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
          ]
        },
        // 审核拒绝对话框
        rejectDialogVisible: false,
        rejectForm: {
          reason: '',
          description: ''
        },
        rejectRules: {
          reason: [
            { required: true, message: '请选择拒绝原因', trigger: 'change' }
          ],
          description: [
            { required: true, message: '请输入详细说明', trigger: 'blur' }
          ]
        },
        // 买家详情
        buyerDetailVisible: false,
        currentBuyer: {}
      }
    },
    created() {
      this.stationId = this.$route.params.id
      this.getStationDetail()
    },
    methods: {
      /** 获取状态标签样式 */
      getStatusTag(status) {
        const statusMap = {
          '0': 'warning',
          '1': 'success',
          '2': 'info',
          '3': 'danger',
          '4': 'info'
        }
        return statusMap[status] || ''
      },
      /** 获取驿站详情 */
      getStationDetail() {
        this.loading = true
        // TODO: 这里应调用实际API
        setTimeout(() => {
          // 模拟API请求
          this.loading = false
        }, 500)
      },
      /** 返回列表页 */
      goBack() {
        this.$router.push('/transfer/list')
      },
      /** 编辑驿站信息 */
      handleEdit() {
        this.$router.push(`/transfer/edit/${this.stationId}`)
      },
      /** 更改状态 */
      handleStatus(status) {
        let statusText = ''
        switch (status) {
          case '1':
            statusText = '上架'
            break
          case '2':
            statusText = '标记为已交易'
            break
          case '3':
            statusText = '下架'
            break
          case '4':
            statusText = '关闭'
            break
        }
        
        this.$confirm(`确认要${statusText}该驿站转让信息?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // TODO: 调用更新状态API
          console.log('更新状态ID：', this.stationId, '状态：', status)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        }).catch(() => {})
      },
      /** 审核拒绝操作 */
      handleReject() {
        this.rejectDialogVisible = true
      },
      /** 提交拒绝表单 */
      submitRejectForm() {
        this.$refs.rejectForm.validate(valid => {
          if (valid) {
            // TODO: 调用拒绝API
            console.log('拒绝ID：', this.stationId)
            console.log('拒绝原因：', this.rejectForm.reason)
            console.log('详细说明：', this.rejectForm.description)
            
            this.rejectDialogVisible = false
            this.$message({
              type: 'success',
              message: '操作成功!'
            })
          }
        })
      },
      /** 添加联系记录 */
      addContact() {
        this.contactForm = {
          contactPerson: '',
          contactMethod: '',
          contactContent: '',
          contactTime: new Date()
        }
        this.contactDialogVisible = true
      },
      /** 提交联系记录 */
      submitContactForm() {
        this.$refs.contactForm.validate(valid => {
          if (valid) {
            // TODO: 调用添加联系记录API
            console.log('添加联系记录：', this.contactForm)
            
            // 模拟添加成功
            const record = {
              id: new Date().getTime(),
              contactPerson: this.contactForm.contactPerson,
              contactTime: this.formatDateTime(this.contactForm.contactTime),
              contactMethod: this.contactForm.contactMethod,
              contactContent: this.contactForm.contactContent
            }
            this.contactRecords.unshift(record)
            
            this.contactDialogVisible = false
            this.$message({
              type: 'success',
              message: '添加联系记录成功!'
            })
          }
        })
      },
      /** 添加意向买家 */
      addBuyer() {
        this.buyerForm = {
          name: '',
          phone: '',
          level: 3,
          remark: ''
        }
        this.buyerDialogVisible = true
      },
      /** 提交意向买家 */
      submitBuyerForm() {
        this.$refs.buyerForm.validate(valid => {
          if (valid) {
            // TODO: 调用添加意向买家API
            console.log('添加意向买家：', this.buyerForm)
            
            // 模拟添加成功
            const buyer = {
              id: new Date().getTime(),
              name: this.buyerForm.name,
              phone: this.buyerForm.phone,
              level: this.buyerForm.level,
              remark: this.buyerForm.remark,
              createTime: this.formatDateTime(new Date())
            }
            this.buyerList.unshift(buyer)
            
            this.buyerDialogVisible = false
            this.$message({
              type: 'success',
              message: '添加意向买家成功!'
            })
          }
        })
      },
      /** 查看买家详情 */
      handleBuyerDetail(row) {
        this.currentBuyer = row
        this.buyerDetailVisible = true
      },
      /** 删除意向买家 */
      deleteBuyer() {
        this.$confirm('确定删除该意向买家信息?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // TODO: 调用删除API
          const index = this.buyerList.findIndex(item => item.id === this.currentBuyer.id)
          if (index > -1) {
            this.buyerList.splice(index, 1)
          }
          
          this.buyerDetailVisible = false
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        }).catch(() => {})
      },
      /** 格式化日期时间 */
      formatDateTime(date) {
        if (!date) return ''
        date = new Date(date)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .page-header {
    margin-bottom: 20px;
  }
  
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  
    .station-title {
      display: flex;
      align-items: center;
  
      h2 {
        margin: 0;
      }
    }
  }
  
  .ml-10 {
    margin-left: 10px;
  }
  
  .mt-20 {
    margin-top: 20px;
  }
  
  .station-photos {
    margin: 20px 0;
  
    h3 {
      margin-bottom: 15px;
    }
  
    .photo-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
  
    .station-photo {
      width: 150px;
      height: 100px;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;
      border: 1px solid #eee;
    }
  }
  
  .side-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  
    .log-timeline {
      max-height: 300px;
      overflow-y: auto;
      padding-right: 10px;
    }
  
    .contact-list {
      max-height: 300px;
      overflow-y: auto;
    }
  
    .contact-item {
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 10px;
      margin-bottom: 10px;
  
      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }
  
      .contact-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
  
        .contact-user {
          font-weight: bold;
        }
  
        .contact-time {
          color: #909399;
          font-size: 12px;
        }
      }
  
      .contact-content {
        margin-bottom: 5px;
        line-height: 1.5;
      }
  
      .contact-method {
        color: #909399;
        font-size: 13px;
      }
    }
  
    .buyer-list {
      max-height: 300px;
      overflow-y: auto;
    }
  }
  
  .no-data {
    color: #909399;
    text-align: center;
    padding: 20px 0;
  }
  </style>