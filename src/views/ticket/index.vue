<template>
  <div class="app-container">
    <el-card class="filter-container">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="工单编号" prop="ticketNo">
          <el-input v-model="queryParams.ticketNo" placeholder="请输入工单编号" clearable />
        </el-form-item>
        <el-form-item label="工单类型" prop="type">
          <el-select v-model="queryParams.type" placeholder="请选择工单类型" clearable>
            <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="工单状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择工单状态" clearable>
            <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="提交时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-container">
      <div class="action-bar">
        <el-button type="primary" icon="Plus" @click="handleAdd">新增工单</el-button>
        <el-button type="success" icon="Download" @click="handleExport">导出</el-button>
      </div>

      <el-table v-loading="loading" :data="ticketList" border>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="工单编号" align="center" prop="ticketNo" />
        <el-table-column label="工单标题" align="center" prop="title" :show-overflow-tooltip="true" />
        <el-table-column label="工单类型" align="center" prop="typeName" />
        <el-table-column label="提交用户" align="center" prop="userName" />
        <el-table-column label="联系电话" align="center" prop="phone" />
        <el-table-column label="工单状态" align="center" prop="statusName">
          <template #default="scope">
            <el-tag :type="statusTagType(scope.row.status)">
              {{ scope.row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="提交时间" align="center" prop="createTime" width="180" />
        <el-table-column label="操作" align="center" width="200">
          <template #default="scope">
            <el-button type="primary" link icon="View" @click="handleView(scope.row)">查看</el-button>
            <el-button v-if="scope.row.status === 0" type="success" link icon="Check" @click="handleProcess(scope.row)">处理</el-button>
            <el-button v-if="scope.row.status === 1" type="info" link icon="Finished" @click="handleComplete(scope.row)">完成</el-button>
            <el-button type="danger" link icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改工单对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="ticketForm" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="工单类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择工单类型">
            <el-option v-for="dict in typeOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="工单标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入工单标题" />
        </el-form-item>
        <el-form-item label="联系人" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="工单内容" prop="content">
          <el-input v-model="form.content" type="textarea" placeholder="请输入工单内容" :rows="6" />
        </el-form-item>
        <el-form-item label="附件">
          <el-upload
            class="upload-demo"
            action="/api/upload"
            multiple
            :limit="5"
            :on-exceed="handleExceed"
            :on-success="handleUploadSuccess"
            :file-list="fileList"
          >
            <el-button type="primary">上传附件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持上传jpg/png/pdf文件，且不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 工单处理对话框 -->
    <el-dialog title="工单处理" v-model="processOpen" width="600px" append-to-body>
      <div class="ticket-info">
        <div class="ticket-header">
          <h3>{{ processForm.title }}</h3>
          <el-tag :type="statusTagType(processForm.status)">{{ processForm.statusName }}</el-tag>
        </div>
        <div class="ticket-meta">
          <span>工单编号：{{ processForm.ticketNo }}</span>
          <span>提交时间：{{ processForm.createTime }}</span>
          <span>工单类型：{{ processForm.typeName }}</span>
        </div>
        <div class="ticket-content">
          <div class="content-title">工单内容：</div>
          <div class="content-body">{{ processForm.content }}</div>
        </div>
        <div class="ticket-attachments" v-if="processForm.attachments && processForm.attachments.length > 0">
          <div class="content-title">附件：</div>
          <div class="attachment-list">
            <div v-for="(file, index) in processForm.attachments" :key="index" class="attachment-item">
              <el-link :href="file.url" target="_blank" type="primary">{{ file.name }}</el-link>
            </div>
          </div>
        </div>
      </div>

      <el-divider content-position="center">处理信息</el-divider>

      <el-form ref="processTicketForm" :model="processForm" :rules="processRules" label-width="100px">
        <el-form-item label="处理状态" prop="status">
          <el-select v-model="processForm.status">
            <el-option :value="1" label="处理中" />
            <el-option :value="2" label="已完成" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理备注" prop="remark">
          <el-input v-model="processForm.remark" type="textarea" placeholder="请输入处理备注" :rows="4" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="processOpen = false">取 消</el-button>
          <el-button type="primary" @click="submitProcessForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTicketList, addTicket, updateTicket, deleteTicket, processTicket, exportTicket } from '@/api/ticket'

// 定义状态常量
const typeOptions = [
  { label: '账号问题', value: 0 },
  { label: '支付问题', value: 1 },
  { label: '驿站问题', value: 2 },
  { label: '功能建议', value: 3 },
  { label: '其他问题', value: 4 }
]

const statusOptions = [
  { label: '待处理', value: 0 },
  { label: '处理中', value: 1 },
  { label: '已完成', value: 2 },
  { label: '已关闭', value: 3 }
]

// 状态标签类型
const statusTagType = (status) => {
  const map = {
    0: 'danger',
    1: 'warning',
    2: 'success',
    3: 'info'
  }
  return map[status] || 'info'
}

// 数据状态
const state = reactive({
  loading: false,
  // 查询参数
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ticketNo: undefined,
    type: undefined,
    status: undefined
  },
  dateRange: [],
  total: 0,
  ticketList: [],
  title: '',
  open: false,
  // 表单参数
  form: {
    id: undefined,
    ticketNo: undefined,
    type: undefined,
    title: '',
    userName: '',
    phone: '',
    content: '',
    attachments: []
  },
  // 表单校验
  rules: {
    type: [
      { required: true, message: '请选择工单类型', trigger: 'change' }
    ],
    title: [
      { required: true, message: '请输入工单标题', trigger: 'blur' },
      { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
    ],
    userName: [
      { required: true, message: '请输入联系人', trigger: 'blur' }
    ],
    phone: [
      { required: true, message: '请输入联系电话', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ],
    content: [
      { required: true, message: '请输入工单内容', trigger: 'blur' }
    ]
  },
  fileList: [],
  processOpen: false,
  processForm: {
    id: undefined,
    ticketNo: undefined,
    title: '',
    typeName: '',
    status: undefined,
    statusName: '',
    content: '',
    remark: '',
    attachments: [],
    createTime: ''
  },
  processRules: {
    status: [
      { required: true, message: '请选择处理状态', trigger: 'change' }
    ],
    remark: [
      { required: true, message: '请输入处理备注', trigger: 'blur' }
    ]
  }
})

// 解构状态
const { 
  loading, queryParams, dateRange, total, ticketList, title, open, 
  form, rules, fileList, processOpen, processForm, processRules 
} = toRefs(state)

// 表单引用
const queryForm = ref(null)
const ticketForm = ref(null)
const processTicketForm = ref(null)

// 生命周期钩子
onMounted(() => {
  getList()
})

// 获取工单列表
const getList = () => {
  state.loading = true
  const params = { ...state.queryParams }
  // 处理日期范围
  if (state.dateRange && state.dateRange.length > 0) {
    params.beginTime = state.dateRange[0]
    params.endTime = state.dateRange[1]
  }
  
  getTicketList(params).then(response => {
    state.ticketList = response.rows
    state.total = response.total
  }).finally(() => {
    state.loading = false
  })
}

// 搜索按钮操作
const handleQuery = () => {
  state.queryParams.pageNum = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  if (queryForm.value) {
    queryForm.value.resetFields()
  }
  state.dateRange = []
  handleQuery()
}

// 添加工单
const handleAdd = () => {
  resetForm()
  state.open = true
  state.title = '添加工单'
}

// 提交表单
const submitForm = () => {
  if (ticketForm.value) {
    ticketForm.value.validate(valid => {
      if (valid) {
        const formData = { ...state.form }
        if (formData.id) {
          updateTicket(formData).then(response => {
            ElMessage.success('修改成功')
            state.open = false
            getList()
          })
        } else {
          addTicket(formData).then(response => {
            ElMessage.success('新增成功')
            state.open = false
            getList()
          })
        }
      }
    })
  }
}

// 取消按钮
const cancel = () => {
  state.open = false
  resetForm()
}

// 表单重置
const resetForm = () => {
  if (ticketForm.value) {
    ticketForm.value.resetFields()
  }
  state.form = {
    id: undefined,
    ticketNo: undefined,
    type: undefined,
    title: '',
    userName: '',
    phone: '',
    content: '',
    attachments: []
  }
  state.fileList = []
}

// 查看工单
const handleView = (row) => {
  state.processForm = { ...row }
  state.processOpen = true
}

// 处理工单
const handleProcess = (row) => {
  state.processForm = { ...row, status: 1, remark: '' }
  state.processOpen = true
}

// 完成工单
const handleComplete = (row) => {
  state.processForm = { ...row, status: 2, remark: '' }
  state.processOpen = true
}

// 删除工单
const handleDelete = (row) => {
  ElMessageBox.confirm('是否确认删除该工单?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteTicket(row.id).then(() => {
      ElMessage.success('删除成功')
      getList()
    })
  })
}

// 提交处理表单
const submitProcessForm = () => {
  if (processTicketForm.value) {
    processTicketForm.value.validate(valid => {
      if (valid) {
        const data = {
          id: state.processForm.id,
          status: state.processForm.status,
          remark: state.processForm.remark
        }
        processTicket(data).then(response => {
          ElMessage.success('处理成功')
          state.processOpen = false
          getList()
        })
      }
    })
  }
}

// 上传成功回调
const handleUploadSuccess = (response, file, fileList) => {
  state.fileList = fileList
  state.form.attachments.push({
    name: file.name,
    url: response.data
  })
  ElMessage.success('上传成功')
}

// 超出数量限制
const handleExceed = () => {
  ElMessage.warning('最多只能上传5个文件')
}

// 导出工单
const handleExport = () => {
  const params = { ...state.queryParams }
  if (state.dateRange && state.dateRange.length > 0) {
    params.beginTime = state.dateRange[0]
    params.endTime = state.dateRange[1]
  }
  exportTicket(params)
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .filter-container {
    margin-bottom: 20px;
  }
  
  .table-container {
    .action-bar {
      margin-bottom: 20px;
    }
  }
  
  .ticket-info {
    padding: 0 10px;
    
    .ticket-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      h3 {
        margin: 0;
      }
    }
    
    .ticket-meta {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 15px;
      color: #666;
      
      span {
        margin-right: 20px;
        margin-bottom: 5px;
      }
    }
    
    .ticket-content {
      margin-bottom: 15px;
      
      .content-title {
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .content-body {
        background-color: #f8f8f8;
        padding: 10px;
        border-radius: 4px;
        white-space: pre-wrap;
      }
    }
    
    .ticket-attachments {
      .content-title {
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .attachment-list {
        display: flex;
        flex-wrap: wrap;
        
        .attachment-item {
          margin-right: 15px;
          margin-bottom: 5px;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style> 