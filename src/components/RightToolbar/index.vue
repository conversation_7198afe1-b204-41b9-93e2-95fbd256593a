<template>
  <div class="right-toolbar">
    <el-row>
      <el-tooltip content="刷新" placement="top">
        <el-button circle icon="Refresh" @click="handleRefresh" />
      </el-tooltip>
      <el-tooltip content="显示搜索" placement="top" v-if="search">
        <el-button circle icon="Search" :type="showSearch ? 'primary' : ''" @click="toggleSearch" />
      </el-tooltip>
      <el-tooltip content="列设置" placement="top" v-if="columns">
        <el-button circle icon="Menu" />
      </el-tooltip>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "RightToolbar",
  props: {
    showSearch: {
      type: Boolean,
      default: true
    },
    columns: {
      type: Boolean,
      default: false
    },
    search: {
      type: Boolean,
      default: true
    }
  },
  emits: ['update:showSearch', 'refreshList'],
  methods: {
    // 刷新
    handleRefresh() {
      this.$emit('refreshList')
    },
    // 显示/隐藏搜索
    toggleSearch() {
      this.$emit('update:showSearch', !this.showSearch)
    }
  }
}
</script>

<style scoped>
.right-toolbar {
  float: right;
  margin-right: 10px;
}

.right-toolbar .el-button {
  margin-left: 5px;
}
</style> 