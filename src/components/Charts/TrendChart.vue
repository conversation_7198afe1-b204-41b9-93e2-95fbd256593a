<template>
  <div ref="chart" :style="{height: height + 'px'}" class="trend-chart"></div>
</template>

<script>
import * as echarts from 'echarts';
import resize from './mixins/resize';

export default {
  name: 'Trend<PERSON>hart',
  mixins: [resize],
  props: {
    data: {
      type: Array,
      required: true,
      default: () => []
    },
    labels: {
      type: Array,
      default: () => []
    },
    color: {
      type: String,
      default: '#409EFF'
    },
    height: {
      type: Number,
      default: 300
    },
    isSmooth: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    data: {
      handler() {
        this.initChart();
      },
      deep: true
    },
    labels: {
      handler() {
        this.initChart();
      },
      deep: true
    }
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    initChart() {
      if (!this.$refs.chart) return;
      
      this.chart = echarts.init(this.$refs.chart);
      
      // 确保数据点都是有效值，过滤掉undefined或null
      const validData = Array.isArray(this.data) 
        ? this.data.map(item => (item === undefined || item === null) ? 0 : item)
        : [];
      
      const option = {
        grid: {
          top: 10,
          bottom: 10,
          left: 10,
          right: 10,
          containLabel: false
        },
        xAxis: {
          type: 'category',
          data: this.labels.length ? this.labels : [],
          show: this.height > 100,
          axisLine: {
            show: this.height > 100
          },
          axisTick: {
            show: this.height > 100
          },
          axisLabel: {
            show: this.height > 100
          }
        },
        yAxis: {
          type: 'value',
          show: this.height > 100,
          axisLine: {
            show: this.height > 100
          },
          axisTick: {
            show: this.height > 100
          },
          axisLabel: {
            show: this.height > 100
          },
          splitLine: {
            show: this.height > 100
          }
        },
        series: [
          {
            data: validData,
            type: 'line',
            smooth: this.isSmooth,
            symbol: this.height > 100 ? 'circle' : 'none',
            symbolSize: 4,
            lineStyle: {
              width: 2,
              color: this.color
            },
            itemStyle: {
              color: this.color
            },
            areaStyle: this.height > 100 ? {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: this.color + '40' // 40% transparency
                  },
                  {
                    offset: 1,
                    color: this.color + '00' // 0% transparency
                  }
                ]
              }
            } : null
          }
        ],
        tooltip: {
          trigger: 'axis',
          show: this.height > 100,
          formatter: '{b}: {c}'
        }
      };
      
      this.chart.setOption(option);
    }
  }
};
</script>

<style lang="scss" scoped>
.trend-chart {
  width: 100%;
  height: 100%;
}
</style> 