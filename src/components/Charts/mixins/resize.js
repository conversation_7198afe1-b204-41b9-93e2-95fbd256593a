export default {
  data() {
    return {
      $_sidebarElm: null,
      $_resizeHandler: null
    };
  },
  mounted() {
    this.initListener();
  },
  activated() {
    if (!this.$_resizeHandler) {
      // avoid duplication init
      this.initListener();
    }

    // 延迟一下，防止激活时图表未渲染完成
    this.$_resizeHandler && setTimeout(() => {
      this.$_resizeHandler();
    }, 200);
  },
  beforeDestroy() {
    this.destroyListener();
  },
  deactivated() {
    this.destroyListener();
  },
  methods: {
    // 使用事件节流优化resize
    $_debounce(fn, delay) {
      let timer = null;
      return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(function() {
          fn.apply(context, args);
        }, delay);
      };
    },
    // 初始化监听器
    initListener() {
      this.$_resizeHandler = this.$_debounce(() => {
        if (this.chart) {
          this.chart.resize();
        }
      }, 100);

      window.addEventListener('resize', this.$_resizeHandler);

      this.$_sidebarElm = document.getElementsByClassName('sidebar-container')[0];
      if (this.$_sidebarElm) {
        this.$_sidebarElm.addEventListener('transitionend', this.$_resizeHandler);
      }
    },
    // 销毁监听器
    destroyListener() {
      window.removeEventListener('resize', this.$_resizeHandler);
      this.$_resizeHandler = null;

      if (this.$_sidebarElm) {
        this.$_sidebarElm.removeEventListener('transitionend', this.$_resizeHandler);
      }
    }
  }
}; 