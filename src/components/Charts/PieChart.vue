<template>
  <div :class="className" :style="{ height: height, width: width }" ref="chartRef" />
</template>

<script>
import * as echarts from 'echarts';
import { debounce } from 'lodash-es';

export default {
  name: 'Pie<PERSON>hart',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    data: {
      type: Array,
      required: true
    },
    title: {
      type: String,
      default: ''
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    colors: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      resizeHandler: null
    };
  },
  watch: {
    data: {
      deep: true,
      handler(val) {
        // 使用nextTick确保DOM已更新
        this.$nextTick(() => {
          this.setOptions(val);
        });
      }
    }
  },
  mounted() {
    // 创建防抖的resize处理函数
    this.resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize();
      }
    }, 100);
    
    // 使用nextTick确保DOM已经渲染完成
    this.$nextTick(() => {
      this.initChart();
    });
    
    window.addEventListener('resize', this.resizeHandler);
  },
  beforeUnmount() {
    if (!this.chart) {
      return;
    }
    window.removeEventListener('resize', this.resizeHandler);
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      // 防止重复初始化
      if (this.chart) {
        this.chart.dispose();
      }
      
      this.chart = echarts.init(this.$refs.chartRef);
      this.setOptions(this.data);
    },
    setOptions(data) {
      if (!this.chart || !data || data.length === 0) return;
      
      try {
        // 确保数据格式正确
        const formattedData = data.map(item => {
          // 如果数据项没有name或value，使用默认值
          return {
            name: item.name || '未命名',
            value: item.value || 0
          };
        });
        
        const options = {
          title: this.title ? {
            text: this.title,
            left: 'center'
          } : null,
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
          },
          legend: this.showLegend ? {
            type: 'scroll',
            orient: 'vertical',
            right: 10,
            top: 20,
            bottom: 20,
          } : null,
          series: [
            {
              name: this.title,
              type: 'pie',
              radius: '55%',
              center: ['50%', '50%'],
              data: formattedData,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        };
        
        // 如果提供了自定义颜色，则使用它
        if (this.colors && this.colors.length > 0) {
          options.color = this.colors;
        }
        
        // 使用requestAnimationFrame来帮助处理ResizeObserver警告
        requestAnimationFrame(() => {
          this.chart.setOption(options);
        });
      } catch (error) {
        console.error('PieChart 设置选项时出错:', error);
      }
    }
  }
};
</script>

<style scoped>
.chart {
  position: relative;
  width: 100%;
  height: 100%;
}
</style> 