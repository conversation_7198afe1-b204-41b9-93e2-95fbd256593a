<template>
  <div :class="className" :style="{ height: height, width: width }" ref="chartRef" />
</template>

<script>
import * as echarts from 'echarts';
import { debounce } from 'lodash-es';

export default {
  name: 'Bar<PERSON><PERSON>',
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    data: {
      type: Array,
      required: true
    },
    labels: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    xName: {
      type: String,
      default: ''
    },
    yName: {
      type: String,
      default: ''
    },
    colors: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      resizeHandler: null
    };
  },
  watch: {
    data: {
      deep: true,
      handler(val) {
        // 使用nextTick确保DOM已更新
        this.$nextTick(() => {
          this.setOptions(val);
        });
      }
    }
  },
  mounted() {
    // 创建防抖的resize处理函数
    this.resizeHandler = debounce(() => {
      if (this.chart) {
        this.chart.resize();
      }
    }, 100);
    
    // 使用nextTick确保DOM已经渲染完成
    this.$nextTick(() => {
      this.initChart();
    });
    
    window.addEventListener('resize', this.resizeHandler);
  },
  beforeUnmount() {
    if (!this.chart) {
      return;
    }
    window.removeEventListener('resize', this.resizeHandler);
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      // 防止重复初始化
      if (this.chart) {
        this.chart.dispose();
      }
      
      this.chart = echarts.init(this.$refs.chartRef);
      // 确保数据有效后再设置选项
      if (this.data && this.data.length > 0) {
        this.setOptions(this.data);
      }
    },
    setOptions(data) {
      if (!this.chart || !data) return;
      
      try {
        // 确保数据格式正确，防止undefined错误
        const formattedData = Array.isArray(data) ? data : [];
        const labels = this.labels.length > 0 ? this.labels : formattedData.map((_, index) => `项目${index + 1}`);
        
        // 为防止echarts渲染错误，确保数据有正确的type字段
        const series = [];
        
        // 如果数据是二维数组（多组数据）
        if (formattedData.length > 0 && Array.isArray(formattedData[0])) {
          formattedData.forEach((item, index) => {
            series.push({
              name: `数据${index + 1}`,
              type: 'bar',
              data: item.map(val => (val === undefined || val === null) ? 0 : val)
            });
          });
        } else {
          // 单组数据
          series.push({
            name: this.title || '数据',
            type: 'bar',
            data: formattedData.map(val => (val === undefined || val === null) ? 0 : val)
          });
        }
        
        const options = {
          title: this.title ? {
            text: this.title,
            left: 'center'
          } : null,
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            name: this.xName,
            data: labels,
            axisLabel: {
              interval: 0,
              rotate: labels.length > 5 ? 30 : 0
            }
          },
          yAxis: {
            type: 'value',
            name: this.yName
          },
          legend: this.showLegend ? {
            type: 'scroll',
            orient: 'horizontal',
            bottom: 10
          } : null,
          series: series
        };
        
        // 如果提供了自定义颜色，则使用它
        if (this.colors && this.colors.length > 0) {
          options.color = this.colors;
        }
        
        // 使用requestAnimationFrame来帮助处理ResizeObserver警告
        requestAnimationFrame(() => {
          if (this.chart) {
            this.chart.setOption(options);
          }
        });
      } catch (error) {
        console.error('BarChart 设置选项时出错:', error);
      }
    }
  }
};
</script>

<style scoped>
.chart {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
