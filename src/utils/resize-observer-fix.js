/**
 * 修复ResizeObserverLoopError错误
 * 
 * 这个错误通常发生在Vue应用中使用了ResizeObserver时，
 * 当页面缩放或在某些特定条件下观察元素大小变化时可能触发无限循环。
 * 
 * 参考: https://github.com/element-plus/element-plus/issues/2366
 */
export function fixResizeObserverLoopError() {
  // 全局错误处理程序
  const handler = (e) => {
    // 仅拦截ResizeObserver错误
    if (e && e.message && e.message.includes('ResizeObserver loop')) {
      // 阻止错误继续传播，避免错误显示在控制台
      e.stopImmediatePropagation();
    }
  };

  // 添加错误捕获事件监听器
  window.addEventListener('error', handler, true);
  return () => {
    // 返回清理函数，用于移除事件监听器
    window.removeEventListener('error', handler, true);
  };
} 