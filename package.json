{"name": "yzb-admin-system", "version": "1.0.0", "private": true, "description": "驿站帮Pro后台管理系统", "author": "ADMIN", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "db:init": "node database/scripts/initDB.js", "db:migrate": "node database/scripts/migrateDb.js", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.27.2", "bcryptjs": "^3.0.2", "core-js": "^3.25.0", "dotenv": "^16.5.0", "echarts": "^5.3.3", "element-plus": "^2.2.16", "js-cookie": "^3.0.1", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mysql2": "^3.14.1", "node-fetch": "^3.3.2", "nprogress": "^0.2.0", "sequelize": "^6.37.7", "uuid": "^11.1.0", "vue": "^3.2.37", "vue-router": "^4.1.5", "vuedraggable": "^4.1.0", "vuex": "^4.0.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.2.37", "babel-eslint": "^10.1.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "eslint": "^7.32.0", "eslint-plugin-vue": "^9.4.0", "express": "^5.1.0", "miniprogram-ci": "^2.0.10", "nodemon": "^3.1.10", "sass": "^1.54.8", "sass-loader": "^13.0.2"}}