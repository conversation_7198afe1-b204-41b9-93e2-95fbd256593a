/**
 * 认证相关的工具函数
 */

const TokenKey = 'yizhanbang_token';
const UserInfoKey = 'yizhanbang_user_info';

/**
 * 获取token
 * @returns {String} token字符串
 */
export function getToken() {
  return localStorage.getItem(TokenKey);
}

/**
 * 设置token
 * @param {String} token token字符串
 */
export function setToken(token) {
  return localStorage.setItem(TokenKey, token);
}

/**
 * 移除token
 */
export function removeToken() {
  return localStorage.removeItem(TokenKey);
}

/**
 * 获取用户信息
 * @returns {Object} 用户信息对象
 */
export function getUserInfo() {
  const userInfoStr = localStorage.getItem(UserInfoKey);
  return userInfoStr ? JSON.parse(userInfoStr) : null;
}

/**
 * 设置用户信息
 * @param {Object} userInfo 用户信息对象
 */
export function setUserInfo(userInfo) {
  return localStorage.setItem(UserInfoKey, JSON.stringify(userInfo));
}

/**
 * 移除用户信息
 */
export function removeUserInfo() {
  return localStorage.removeItem(UserInfoKey);
}

/**
 * 清除所有认证信息
 */
export function clearAuth() {
  removeToken();
  removeUserInfo();
}

/**
 * 是否已登录
 * @returns {Boolean} 是否已登录
 */
export function isLoggedIn() {
  return !!getToken();
} 