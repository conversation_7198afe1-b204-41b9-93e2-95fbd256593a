# 驿站帮Pro系统

驿站帮Pro是一个为快递驿站提供全方位管理的系统，包含微信小程序和后台管理系统。

## 技术栈

- 前端：Vue.js + Element UI
- 后端：Node.js + Express
- 数据库：MySQL
- 认证：JWT

## 功能模块

- 实名认证管理
- 推广赚钱系统
- 驿站认证管理
- 顶班接单系统
- 驿站转让平台
- 招聘求职中心

## 安装与运行

### 环境要求

- Node.js >= 14.0.0
- MySQL >= 5.7
- npm >= 6.0.0

### 安装依赖

```bash
npm install
```

### 配置环境变量

复制`.env.example`文件并重命名为`.env`，然后根据自己的环境配置相关参数：

```
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=yizhanbang
DB_DIALECT=mysql
SYNC_DB=true  # 是否自动同步数据库模型，生产环境请设为false

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# 微信小程序配置
WECHAT_APPID=your_appid_here
WECHAT_SECRET=your_secret_here
```

### 初始化数据库

首次运行需要初始化数据库，执行以下命令：

```bash
node database/scripts/initDB.js
```

该脚本会自动创建必要的数据表和默认管理员账户。

默认管理员账户：
- 用户名：<EMAIL>
- 密码：admin123

### 从MongoDB迁移到MySQL

如果你之前使用的是MongoDB，可以使用迁移脚本将数据迁移到MySQL：

```bash
# 确保在.env文件中配置了MONGO_URI
node database/scripts/migrateDb.js
```

### 启动服务

开发环境：

```bash
npm run dev
```

生产环境：

```bash
npm run start
```

服务将运行在 http://localhost:3000

## 项目结构

```
├── database            # 数据库相关
│   ├── controllers     # 控制器
│   ├── middlewares     # 中间件
│   ├── models          # 数据模型
│   ├── routes          # 路由
│   └── scripts         # 数据库脚本
├── dist                # 编译后的前端文件
├── public              # 静态资源
├── src                 # 源代码
│   ├── api             # API请求
│   ├── assets          # 静态资源
│   ├── components      # 公共组件
│   ├── layout          # 布局组件
│   ├── router          # 路由配置
│   ├── store           # Vuex存储
│   ├── utils           # 工具函数
│   └── views           # 页面组件
├── server.js           # 服务器入口
└── .env                # 环境配置
```

## API文档

### 认证相关

#### 微信登录

```
POST /api/auth/wx-login
```

请求参数：
- code: 微信登录code
- userInfo: 用户信息（可选）

#### 手机号登录

```
POST /api/auth/phone-login
```

请求参数：
- phone: 手机号
- code: 验证码

#### 密码登录

```
POST /api/auth/password-login
```

请求参数：
- username: 用户名（手机号或邮箱）
- password: 密码

#### 用户注册

```
POST /api/auth/register
```

请求参数：
- phone: 手机号
- password: 密码
- code: 验证码
- nickname: 昵称（可选）
- avatar: 头像URL（可选）

#### 刷新Token

```
POST /api/auth/refresh-token
```

需要授权，在Header中设置Authorization: Bearer {token}

#### 获取登录状态

```
GET /api/auth/status
```

需要授权，在Header中设置Authorization: Bearer {token}

## 许可证

此项目为私有项目，未经授权不得使用、复制或分发。 