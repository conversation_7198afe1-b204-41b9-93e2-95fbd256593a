"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[2520],{2520:(e,l,a)=>{a.r(l),a.d(l,{default:()=>m});var t=a(6768),i=a(4232),n=a(144),r=a(1219),o=a(2933);const u={class:"app-container"},d=["src"],s={class:"dialog-footer"},p={__name:"index",setup(e){const l=(0,n.Kh)({pageNum:1,pageSize:10}),a=(0,n.KR)(!1),p=(0,n.KR)([]),b=(0,n.KR)(0),c=(0,n.KR)([]),m=(0,t.EW)((()=>1!==c.value.length)),g=(0,t.EW)((()=>0===c.value.length)),k=(0,n.Kh)({visible:!1,title:"",type:""}),f=(0,n.KR)(null),v=(0,n.Kh)({bannerId:null,title:"",imageUrl:"",linkUrl:"",type:1,sort:0,status:1,remark:""}),F=(0,n.Kh)({title:[{required:!0,message:"标题不能为空",trigger:"blur"}],imageUrl:[{required:!0,message:"请上传轮播图片",trigger:"change"}],type:[{required:!0,message:"轮播类型不能为空",trigger:"change"}]});(0,t.sV)((()=>{_()}));const _=()=>{a.value=!0,setTimeout((()=>{p.value=[{bannerId:1,title:"驿站帮Pro上线啦",imageUrl:"https://example.com/banner1.jpg",linkUrl:"/pages/index/index",type:1,sort:1,status:1,createTime:"2023-05-15 10:30:00",updateTime:"2023-05-15 10:30:00",remark:"宣传驿站帮Pro应用上线"},{bannerId:2,title:"快递服务活动",imageUrl:"https://example.com/banner2.jpg",linkUrl:"/pages/service/detail?id=1",type:1,sort:2,status:1,createTime:"2023-05-10 14:20:00",updateTime:"2023-05-10 14:20:00",remark:"宣传快递服务优惠活动"},{bannerId:3,title:"海淀创业园驿站介绍",imageUrl:"https://example.com/banner3.jpg",linkUrl:"/pages/station/detail?id=1",type:2,sort:1,status:0,createTime:"2023-05-05 09:15:00",updateTime:"2023-05-05 09:15:00",remark:"介绍海淀创业园驿站"}],b.value=3,a.value=!1}),500)},y=e=>{c.value=e.map((e=>e.bannerId))},h=()=>{x(),k.visible=!0,k.title="添加轮播图",k.type="add"},w=e=>{x();const l=e.bannerId||c.value[0],a=p.value.find((e=>e.bannerId===l));a&&(Object.assign(v,a),k.visible=!0,k.title="修改轮播图",k.type="edit")},U=()=>{f.value.validate((e=>{e&&(v.bannerId?(r.nk.success("修改成功"),k.visible=!1,_()):(r.nk.success("新增成功"),k.visible=!1,_()))}))},V=e=>{e.bannerId||c.value;o.s.confirm("是否确认删除所选轮播图?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{r.nk.success("删除成功"),_()})).catch((()=>{}))},C=e=>{r.nk.success((1===e.status?"显示":"隐藏")+"成功")},W=e=>{v.imageUrl=e.data},x=()=>{v.bannerId=null,v.title="",v.imageUrl="",v.linkUrl="",v.type=1,v.sort=0,v.status=1,v.remark=""},I=()=>{k.visible=!1,x()},z=e=>{l.pageSize=e,_()},T=e=>{l.pageNum=e,_()};return(e,n)=>{const r=(0,t.g2)("el-button"),o=(0,t.g2)("el-col"),c=(0,t.g2)("el-row"),_=(0,t.g2)("el-table-column"),x=(0,t.g2)("el-image"),K=(0,t.g2)("el-tag"),S=(0,t.g2)("el-switch"),E=(0,t.g2)("el-table"),j=(0,t.g2)("el-pagination"),N=(0,t.g2)("el-card"),P=(0,t.g2)("el-input"),R=(0,t.g2)("el-form-item"),X=(0,t.g2)("Plus"),q=(0,t.g2)("el-icon"),B=(0,t.g2)("el-upload"),D=(0,t.g2)("el-radio"),L=(0,t.g2)("el-radio-group"),A=(0,t.g2)("el-input-number"),M=(0,t.g2)("el-form"),O=(0,t.g2)("el-dialog"),Q=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",u,[(0,t.bF)(N,{class:"table-container"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{gutter:10,class:"mb8"},{default:(0,t.k6)((()=>[(0,t.bF)(o,{span:1.5},{default:(0,t.k6)((()=>[(0,t.bF)(r,{type:"primary",plain:"",icon:"Plus",onClick:h},{default:(0,t.k6)((()=>n[9]||(n[9]=[(0,t.eW)("新增")]))),_:1})])),_:1}),(0,t.bF)(o,{span:1.5},{default:(0,t.k6)((()=>[(0,t.bF)(r,{type:"success",plain:"",icon:"Edit",disabled:m.value,onClick:w},{default:(0,t.k6)((()=>n[10]||(n[10]=[(0,t.eW)("修改")]))),_:1},8,["disabled"])])),_:1}),(0,t.bF)(o,{span:1.5},{default:(0,t.k6)((()=>[(0,t.bF)(r,{type:"danger",plain:"",icon:"Delete",disabled:g.value,onClick:V},{default:(0,t.k6)((()=>n[11]||(n[11]=[(0,t.eW)("删除")]))),_:1},8,["disabled"])])),_:1})])),_:1}),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(E,{data:p.value,onSelectionChange:y},{default:(0,t.k6)((()=>[(0,t.bF)(_,{type:"selection",width:"55",align:"center"}),(0,t.bF)(_,{label:"ID",align:"center",prop:"bannerId"}),(0,t.bF)(_,{label:"标题",align:"center",prop:"title","min-width":"150","show-overflow-tooltip":""}),(0,t.bF)(_,{label:"图片",align:"center",width:"100"},{default:(0,t.k6)((e=>[(0,t.bF)(x,{src:e.row.imageUrl,"preview-src-list":[e.row.imageUrl],fit:"cover",class:"banner-image"},null,8,["src","preview-src-list"])])),_:1}),(0,t.bF)(_,{label:"跳转链接",align:"center",prop:"linkUrl","min-width":"200","show-overflow-tooltip":""}),(0,t.bF)(_,{label:"类型",align:"center",prop:"type"},{default:(0,t.k6)((e=>[(0,t.bF)(K,{type:1===e.row.type?"primary":"success"},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)(1===e.row.type?"首页轮播":"驿站轮播"),1)])),_:2},1032,["type"])])),_:1}),(0,t.bF)(_,{label:"排序",align:"center",prop:"sort",width:"80"}),(0,t.bF)(_,{label:"状态",align:"center",prop:"status",width:"80"},{default:(0,t.k6)((e=>[(0,t.bF)(S,{modelValue:e.row.status,"onUpdate:modelValue":l=>e.row.status=l,"active-value":1,"inactive-value":0,onChange:l=>C(e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),(0,t.bF)(_,{label:"创建时间",align:"center",prop:"createTime",width:"180"}),(0,t.bF)(_,{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"200"},{default:(0,t.k6)((e=>[(0,t.bF)(r,{link:"",type:"primary",icon:"Edit",onClick:l=>w(e.row)},{default:(0,t.k6)((()=>n[12]||(n[12]=[(0,t.eW)("修改")]))),_:2},1032,["onClick"]),(0,t.bF)(r,{link:"",type:"primary",icon:"Delete",onClick:l=>V(e.row)},{default:(0,t.k6)((()=>n[13]||(n[13]=[(0,t.eW)("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Q,a.value]]),b.value>0?((0,t.uX)(),(0,t.Wv)(j,{key:0,total:b.value,"current-page":l.pageNum,"onUpdate:currentPage":n[0]||(n[0]=e=>l.pageNum=e),"page-size":l.pageSize,"onUpdate:pageSize":n[1]||(n[1]=e=>l.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:z,onCurrentChange:T},null,8,["total","current-page","page-size"])):(0,t.Q3)("",!0)])),_:1}),(0,t.bF)(O,{title:k.title,modelValue:k.visible,"onUpdate:modelValue":n[8]||(n[8]=e=>k.visible=e),width:"700px","append-to-body":""},{footer:(0,t.k6)((()=>[(0,t.Lk)("div",s,[(0,t.bF)(r,{type:"primary",onClick:U},{default:(0,t.k6)((()=>n[19]||(n[19]=[(0,t.eW)("确 定")]))),_:1}),(0,t.bF)(r,{onClick:I},{default:(0,t.k6)((()=>n[20]||(n[20]=[(0,t.eW)("取 消")]))),_:1})])])),default:(0,t.k6)((()=>[(0,t.bF)(M,{ref_key:"bannerForm",ref:f,model:v,rules:F,"label-width":"100px"},{default:(0,t.k6)((()=>[(0,t.bF)(R,{label:"标题",prop:"title"},{default:(0,t.k6)((()=>[(0,t.bF)(P,{modelValue:v.title,"onUpdate:modelValue":n[2]||(n[2]=e=>v.title=e),placeholder:"请输入轮播图标题"},null,8,["modelValue"])])),_:1}),(0,t.bF)(R,{label:"轮播图片",prop:"imageUrl"},{default:(0,t.k6)((()=>[(0,t.bF)(B,{class:"banner-uploader",action:"/api/common/upload","show-file-list":!1,"on-success":W},{default:(0,t.k6)((()=>[v.imageUrl?((0,t.uX)(),(0,t.CE)("img",{key:0,src:v.imageUrl,class:"banner-img"},null,8,d)):((0,t.uX)(),(0,t.Wv)(q,{key:1,class:"banner-uploader-icon"},{default:(0,t.k6)((()=>[(0,t.bF)(X)])),_:1}))])),_:1}),n[14]||(n[14]=(0,t.Lk)("div",{class:"el-upload__tip"},"建议上传尺寸比例为16:9的图片，图片大小不超过2MB",-1))])),_:1}),(0,t.bF)(R,{label:"跳转链接",prop:"linkUrl"},{default:(0,t.k6)((()=>[(0,t.bF)(P,{modelValue:v.linkUrl,"onUpdate:modelValue":n[3]||(n[3]=e=>v.linkUrl=e),placeholder:"请输入点击轮播图后的跳转链接"},null,8,["modelValue"])])),_:1}),(0,t.bF)(R,{label:"轮播类型",prop:"type"},{default:(0,t.k6)((()=>[(0,t.bF)(L,{modelValue:v.type,"onUpdate:modelValue":n[4]||(n[4]=e=>v.type=e)},{default:(0,t.k6)((()=>[(0,t.bF)(D,{label:1},{default:(0,t.k6)((()=>n[15]||(n[15]=[(0,t.eW)("首页轮播")]))),_:1}),(0,t.bF)(D,{label:2},{default:(0,t.k6)((()=>n[16]||(n[16]=[(0,t.eW)("驿站轮播")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(R,{label:"排序",prop:"sort"},{default:(0,t.k6)((()=>[(0,t.bF)(A,{modelValue:v.sort,"onUpdate:modelValue":n[5]||(n[5]=e=>v.sort=e),min:0,max:999,placeholder:"请输入排序号"},null,8,["modelValue"])])),_:1}),(0,t.bF)(R,{label:"状态",prop:"status"},{default:(0,t.k6)((()=>[(0,t.bF)(L,{modelValue:v.status,"onUpdate:modelValue":n[6]||(n[6]=e=>v.status=e)},{default:(0,t.k6)((()=>[(0,t.bF)(D,{label:1},{default:(0,t.k6)((()=>n[17]||(n[17]=[(0,t.eW)("显示")]))),_:1}),(0,t.bF)(D,{label:0},{default:(0,t.k6)((()=>n[18]||(n[18]=[(0,t.eW)("隐藏")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(R,{label:"备注",prop:"remark"},{default:(0,t.k6)((()=>[(0,t.bF)(P,{modelValue:v.remark,"onUpdate:modelValue":n[7]||(n[7]=e=>v.remark=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"])])}}};var b=a(1241);const c=(0,b.A)(p,[["__scopeId","data-v-d78852f4"]]),m=c}}]);