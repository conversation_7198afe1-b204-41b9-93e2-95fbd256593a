"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[9374],{9374:(e,t,a)=>{a.r(t),a.d(t,{default:()=>x});var l=a(6768),o=a(4232);const n={class:"page-container"},i={class:"page-header"},s={class:"detail-header"},r={class:"station-title"},c={key:0,class:"operation-buttons"},d={class:"station-photos"},u={class:"photo-list"},m={key:0,class:"no-data"},b={class:"log-timeline"},p={key:0,class:"no-data"},k={class:"card-header"},h={class:"contact-list"},f={class:"contact-header"},F={class:"contact-user"},g={class:"contact-time"},y={class:"contact-content"},_={class:"contact-method"},v={key:0,class:"no-data"},V={class:"card-header"},C={class:"buyer-list"},w={key:0,class:"no-data"},D={class:"dialog-footer"},W={class:"dialog-footer"},I={class:"dialog-footer"},T={class:"dialog-footer"};function L(e,t,a,L,B,j){const $=(0,l.g2)("el-page-header"),x=(0,l.g2)("el-tag"),S=(0,l.g2)("el-button"),M=(0,l.g2)("el-divider"),U=(0,l.g2)("el-descriptions-item"),X=(0,l.g2)("el-descriptions"),P=(0,l.g2)("el-image"),R=(0,l.g2)("el-col"),E=(0,l.g2)("el-timeline-item"),Q=(0,l.g2)("el-timeline"),q=(0,l.g2)("el-card"),N=(0,l.g2)("el-table-column"),z=(0,l.g2)("el-rate"),G=(0,l.g2)("el-table"),J=(0,l.g2)("el-row"),K=(0,l.g2)("el-input"),A=(0,l.g2)("el-form-item"),H=(0,l.g2)("el-option"),Y=(0,l.g2)("el-select"),O=(0,l.g2)("el-date-picker"),Z=(0,l.g2)("el-form"),ee=(0,l.g2)("el-dialog"),te=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",n,[(0,l.Lk)("div",i,[(0,l.bF)($,{onBack:j.goBack,content:B.stationInfo.stationName||"驿站转让详情"},null,8,["onBack","content"])]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(q,{class:"box-card"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",s,[(0,l.Lk)("div",r,[(0,l.Lk)("h2",null,(0,o.v_)(B.stationInfo.stationName),1),(0,l.bF)(x,{type:j.getStatusTag(B.stationInfo.status),class:"ml-10"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.statusMap[B.stationInfo.status]),1)])),_:1},8,["type"])]),"2"!==B.stationInfo.status?((0,l.uX)(),(0,l.CE)("div",c,[(0,l.bF)(S,{type:"primary",onClick:j.handleEdit},{default:(0,l.k6)((()=>t[22]||(t[22]=[(0,l.eW)("编辑")]))),_:1},8,["onClick"]),"0"===B.stationInfo.status?((0,l.uX)(),(0,l.Wv)(S,{key:0,type:"success",onClick:t[0]||(t[0]=e=>j.handleStatus("1"))},{default:(0,l.k6)((()=>t[23]||(t[23]=[(0,l.eW)("审核通过")]))),_:1})):(0,l.Q3)("",!0),"0"===B.stationInfo.status?((0,l.uX)(),(0,l.Wv)(S,{key:1,type:"danger",onClick:j.handleReject},{default:(0,l.k6)((()=>t[24]||(t[24]=[(0,l.eW)("审核拒绝")]))),_:1},8,["onClick"])):(0,l.Q3)("",!0),"1"===B.stationInfo.status?((0,l.uX)(),(0,l.Wv)(S,{key:2,type:"warning",onClick:t[1]||(t[1]=e=>j.handleStatus("3"))},{default:(0,l.k6)((()=>t[25]||(t[25]=[(0,l.eW)("下架")]))),_:1})):(0,l.Q3)("",!0),"1"===B.stationInfo.status?((0,l.uX)(),(0,l.Wv)(S,{key:3,type:"info",onClick:t[2]||(t[2]=e=>j.handleStatus("2"))},{default:(0,l.k6)((()=>t[26]||(t[26]=[(0,l.eW)("标记已交易")]))),_:1})):(0,l.Q3)("",!0)])):(0,l.Q3)("",!0)]),(0,l.bF)(M),(0,l.bF)(J,{gutter:20},{default:(0,l.k6)((()=>[(0,l.bF)(R,{span:16},{default:(0,l.k6)((()=>[(0,l.bF)(X,{title:"基本信息",column:2,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(U,{label:"驿站名称"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.stationInfo.stationName),1)])),_:1}),(0,l.bF)(U,{label:"驿站类型"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.stationTypeMap[B.stationInfo.stationType]),1)])),_:1}),(0,l.bF)(U,{label:"所在区域"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.stationInfo.region),1)])),_:1}),(0,l.bF)(U,{label:"详细地址"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.stationInfo.address),1)])),_:1}),(0,l.bF)(U,{label:"转让价格"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.stationInfo.price)+" 元",1)])),_:1}),(0,l.bF)(U,{label:"月营业额"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.stationInfo.monthlyRevenue)+" 元",1)])),_:1}),(0,l.bF)(U,{label:"联系人"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.stationInfo.contactName),1)])),_:1}),(0,l.bF)(U,{label:"联系电话"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.stationInfo.contactPhone),1)])),_:1}),(0,l.bF)(U,{label:"发布时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.stationInfo.createTime),1)])),_:1}),(0,l.bF)(U,{label:"更新时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.stationInfo.updateTime),1)])),_:1})])),_:1}),(0,l.bF)(M),(0,l.bF)(X,{title:"转让信息",column:1,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(U,{label:"转让原因"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.stationInfo.reason),1)])),_:1}),(0,l.bF)(U,{label:"补充说明"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.stationInfo.description),1)])),_:1})])),_:1}),(0,l.bF)(M),(0,l.Lk)("div",d,[t[27]||(t[27]=(0,l.Lk)("h3",null,"驿站照片",-1)),(0,l.Lk)("div",u,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(B.stationInfo.photos,((e,t)=>((0,l.uX)(),(0,l.Wv)(P,{key:t,src:e,fit:"cover",class:"station-photo","preview-src-list":B.stationInfo.photos},null,8,["src","preview-src-list"])))),128)),B.stationInfo.photos&&0!==B.stationInfo.photos.length?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.CE)("div",m," 暂无照片 "))])])])),_:1}),(0,l.bF)(R,{span:8},{default:(0,l.k6)((()=>[(0,l.bF)(q,{class:"side-card"},{header:(0,l.k6)((()=>t[28]||(t[28]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"转让日志")],-1)]))),default:(0,l.k6)((()=>[(0,l.Lk)("div",b,[(0,l.bF)(Q,null,{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(B.activities,((e,t)=>((0,l.uX)(),(0,l.Wv)(E,{key:t,type:e.type,color:e.color,timestamp:e.timestamp},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(e.content),1)])),_:2},1032,["type","color","timestamp"])))),128))])),_:1}),0===B.activities.length?((0,l.uX)(),(0,l.CE)("div",p," 暂无日志记录 ")):(0,l.Q3)("",!0)])])),_:1}),(0,l.bF)(q,{class:"side-card mt-20"},{header:(0,l.k6)((()=>[(0,l.Lk)("div",k,[t[30]||(t[30]=(0,l.Lk)("span",null,"联系记录",-1)),(0,l.bF)(S,{type:"text",onClick:j.addContact},{default:(0,l.k6)((()=>t[29]||(t[29]=[(0,l.eW)("添加记录")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.Lk)("div",h,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(B.contactRecords,((e,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,class:"contact-item"},[(0,l.Lk)("div",f,[(0,l.Lk)("span",F,(0,o.v_)(e.contactPerson),1),(0,l.Lk)("span",g,(0,o.v_)(e.contactTime),1)]),(0,l.Lk)("div",y,(0,o.v_)(e.contactContent),1),(0,l.Lk)("div",_,"联系方式："+(0,o.v_)(e.contactMethod),1)])))),128)),0===B.contactRecords.length?((0,l.uX)(),(0,l.CE)("div",v," 暂无联系记录 ")):(0,l.Q3)("",!0)])])),_:1}),(0,l.bF)(q,{class:"side-card mt-20"},{header:(0,l.k6)((()=>[(0,l.Lk)("div",V,[t[32]||(t[32]=(0,l.Lk)("span",null,"意向买家",-1)),(0,l.bF)(S,{type:"text",onClick:j.addBuyer},{default:(0,l.k6)((()=>t[31]||(t[31]=[(0,l.eW)("添加买家")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.Lk)("div",C,[(0,l.bF)(G,{data:B.buyerList,style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(N,{prop:"name",label:"姓名","min-width":"70"}),(0,l.bF)(N,{prop:"phone",label:"联系电话","min-width":"110"}),(0,l.bF)(N,{prop:"level",label:"意向度",width:"90"},{default:(0,l.k6)((e=>[(0,l.bF)(z,{modelValue:e.row.level,"onUpdate:modelValue":t=>e.row.level=t,disabled:"","text-color":"#ff9900"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),(0,l.bF)(N,{label:"操作",width:"80"},{default:(0,l.k6)((e=>[(0,l.bF)(S,{type:"text",size:"small",onClick:t=>j.handleBuyerDetail(e.row)},{default:(0,l.k6)((()=>t[33]||(t[33]=[(0,l.eW)("详情")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),0===B.buyerList.length?((0,l.uX)(),(0,l.CE)("div",w," 暂无意向买家 ")):(0,l.Q3)("",!0)])])),_:1})])),_:1})])),_:1})])),_:1})),[[te,B.loading]]),(0,l.bF)(ee,{title:"添加联系记录",modelValue:B.contactDialogVisible,"onUpdate:modelValue":t[8]||(t[8]=e=>B.contactDialogVisible=e),width:"500px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",D,[(0,l.bF)(S,{onClick:t[7]||(t[7]=e=>B.contactDialogVisible=!1)},{default:(0,l.k6)((()=>t[34]||(t[34]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(S,{type:"primary",onClick:j.submitContactForm},{default:(0,l.k6)((()=>t[35]||(t[35]=[(0,l.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bF)(Z,{ref:"contactForm",model:B.contactForm,rules:B.contactRules,"label-width":"100px"},{default:(0,l.k6)((()=>[(0,l.bF)(A,{label:"联系人",prop:"contactPerson"},{default:(0,l.k6)((()=>[(0,l.bF)(K,{modelValue:B.contactForm.contactPerson,"onUpdate:modelValue":t[3]||(t[3]=e=>B.contactForm.contactPerson=e),placeholder:"请输入联系人"},null,8,["modelValue"])])),_:1}),(0,l.bF)(A,{label:"联系方式",prop:"contactMethod"},{default:(0,l.k6)((()=>[(0,l.bF)(Y,{modelValue:B.contactForm.contactMethod,"onUpdate:modelValue":t[4]||(t[4]=e=>B.contactForm.contactMethod=e),placeholder:"请选择联系方式",style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(H,{label:"电话",value:"电话"}),(0,l.bF)(H,{label:"微信",value:"微信"}),(0,l.bF)(H,{label:"当面",value:"当面"}),(0,l.bF)(H,{label:"其他",value:"其他"})])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(A,{label:"联系内容",prop:"contactContent"},{default:(0,l.k6)((()=>[(0,l.bF)(K,{modelValue:B.contactForm.contactContent,"onUpdate:modelValue":t[5]||(t[5]=e=>B.contactForm.contactContent=e),type:"textarea",rows:"4",placeholder:"请输入联系内容"},null,8,["modelValue"])])),_:1}),(0,l.bF)(A,{label:"联系时间",prop:"contactTime"},{default:(0,l.k6)((()=>[(0,l.bF)(O,{modelValue:B.contactForm.contactTime,"onUpdate:modelValue":t[6]||(t[6]=e=>B.contactForm.contactTime=e),type:"datetime",placeholder:"选择联系时间",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"]),(0,l.bF)(ee,{title:"添加意向买家",modelValue:B.buyerDialogVisible,"onUpdate:modelValue":t[14]||(t[14]=e=>B.buyerDialogVisible=e),width:"500px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",W,[(0,l.bF)(S,{onClick:t[13]||(t[13]=e=>B.buyerDialogVisible=!1)},{default:(0,l.k6)((()=>t[36]||(t[36]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(S,{type:"primary",onClick:j.submitBuyerForm},{default:(0,l.k6)((()=>t[37]||(t[37]=[(0,l.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bF)(Z,{ref:"buyerForm",model:B.buyerForm,rules:B.buyerRules,"label-width":"100px"},{default:(0,l.k6)((()=>[(0,l.bF)(A,{label:"买家姓名",prop:"name"},{default:(0,l.k6)((()=>[(0,l.bF)(K,{modelValue:B.buyerForm.name,"onUpdate:modelValue":t[9]||(t[9]=e=>B.buyerForm.name=e),placeholder:"请输入买家姓名"},null,8,["modelValue"])])),_:1}),(0,l.bF)(A,{label:"联系电话",prop:"phone"},{default:(0,l.k6)((()=>[(0,l.bF)(K,{modelValue:B.buyerForm.phone,"onUpdate:modelValue":t[10]||(t[10]=e=>B.buyerForm.phone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])])),_:1}),(0,l.bF)(A,{label:"意向度",prop:"level"},{default:(0,l.k6)((()=>[(0,l.bF)(z,{modelValue:B.buyerForm.level,"onUpdate:modelValue":t[11]||(t[11]=e=>B.buyerForm.level=e)},null,8,["modelValue"])])),_:1}),(0,l.bF)(A,{label:"备注",prop:"remark"},{default:(0,l.k6)((()=>[(0,l.bF)(K,{modelValue:B.buyerForm.remark,"onUpdate:modelValue":t[12]||(t[12]=e=>B.buyerForm.remark=e),type:"textarea",rows:"4",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"]),(0,l.bF)(ee,{title:"审核拒绝",modelValue:B.rejectDialogVisible,"onUpdate:modelValue":t[18]||(t[18]=e=>B.rejectDialogVisible=e),width:"500px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",I,[(0,l.bF)(S,{onClick:t[17]||(t[17]=e=>B.rejectDialogVisible=!1)},{default:(0,l.k6)((()=>t[38]||(t[38]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(S,{type:"primary",onClick:j.submitRejectForm},{default:(0,l.k6)((()=>t[39]||(t[39]=[(0,l.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bF)(Z,{ref:"rejectForm",model:B.rejectForm,rules:B.rejectRules,"label-width":"80px"},{default:(0,l.k6)((()=>[(0,l.bF)(A,{label:"拒绝原因",prop:"reason"},{default:(0,l.k6)((()=>[(0,l.bF)(Y,{modelValue:B.rejectForm.reason,"onUpdate:modelValue":t[15]||(t[15]=e=>B.rejectForm.reason=e),placeholder:"请选择拒绝原因",style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(H,{label:"信息不完整",value:"信息不完整"}),(0,l.bF)(H,{label:"照片不清晰",value:"照片不清晰"}),(0,l.bF)(H,{label:"违反规定",value:"违反规定"}),(0,l.bF)(H,{label:"信息虚假",value:"信息虚假"}),(0,l.bF)(H,{label:"其他原因",value:"其他原因"})])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(A,{label:"详细说明",prop:"description"},{default:(0,l.k6)((()=>[(0,l.bF)(K,{modelValue:B.rejectForm.description,"onUpdate:modelValue":t[16]||(t[16]=e=>B.rejectForm.description=e),type:"textarea",rows:"4",placeholder:"请输入详细说明"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"]),(0,l.bF)(ee,{title:"意向买家详情",modelValue:B.buyerDetailVisible,"onUpdate:modelValue":t[21]||(t[21]=e=>B.buyerDetailVisible=e),width:"500px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",T,[(0,l.bF)(S,{type:"danger",onClick:j.deleteBuyer},{default:(0,l.k6)((()=>t[40]||(t[40]=[(0,l.eW)("删除")]))),_:1},8,["onClick"]),(0,l.bF)(S,{onClick:t[20]||(t[20]=e=>B.buyerDetailVisible=!1)},{default:(0,l.k6)((()=>t[41]||(t[41]=[(0,l.eW)("关闭")]))),_:1})])])),default:(0,l.k6)((()=>[(0,l.bF)(X,{column:1,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(U,{label:"买家姓名"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.currentBuyer.name),1)])),_:1}),(0,l.bF)(U,{label:"联系电话"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.currentBuyer.phone),1)])),_:1}),(0,l.bF)(U,{label:"意向度"},{default:(0,l.k6)((()=>[(0,l.bF)(z,{modelValue:B.currentBuyer.level,"onUpdate:modelValue":t[19]||(t[19]=e=>B.currentBuyer.level=e),disabled:"","text-color":"#ff9900"},null,8,["modelValue"])])),_:1}),(0,l.bF)(U,{label:"添加时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.currentBuyer.createTime),1)])),_:1}),(0,l.bF)(U,{label:"备注"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(B.currentBuyer.remark),1)])),_:1})])),_:1})])),_:1},8,["modelValue"])])}const B={name:"TransferDetail",data(){return{loading:!1,stationId:null,stationInfo:{id:1,stationName:"海淀区中关村社区驿站",stationType:"2",region:"北京市 北京市 海淀区",address:"北京市海淀区中关村大街123号",price:25e3,monthlyRevenue:18e3,contactName:"张先生",contactPhone:"13800138001",reason:"个人原因，无法继续经营",description:"该驿站位于中关村科技园区内，日均快递量200+，客流量大，接手可立即营业。驿站现有固定客户群体，主要为周边小区居民和写字楼白领。店铺面积约30平米，月租金3000元，租约还有2年期限，可以续签。",createTime:"2023-04-15 10:30:00",updateTime:"2023-04-16 14:15:00",status:"1",photos:["https://img1.baidu.com/it/u=413646961,2312577432&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333","https://img2.baidu.com/it/u=1003272215,1878948666&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800","https://img0.baidu.com/it/u=2277942570,1789375638&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333"]},stationTypeMap:{1:"快递驿站",2:"社区驿站",3:"校园驿站",4:"写字楼驿站"},statusMap:{0:"待审核",1:"已上架",2:"已交易",3:"已下架",4:"已关闭"},activities:[{content:"系统自动创建转让信息",timestamp:"2023-04-15 10:30:00",type:"primary",color:"#0bbd87"},{content:"管理员审核通过",timestamp:"2023-04-15 14:20:00",type:"success",color:"#0bbd87"},{content:"更新了驿站信息",timestamp:"2023-04-16 14:15:00",type:"warning",color:"#e6a23c"}],contactRecords:[{id:1,contactPerson:"刘先生",contactTime:"2023-04-17 09:30:00",contactMethod:"电话",contactContent:"询问驿站日均快递量及收益情况，对价格有一定异议，希望再降低5000元。"},{id:2,contactPerson:"赵女士",contactTime:"2023-04-18 15:40:00",contactMethod:"微信",contactContent:"详细了解了驿站的经营情况，表示对该驿站比较满意，计划近期前往实地考察。"}],buyerList:[{id:1,name:"刘先生",phone:"13912345678",level:3,remark:"对价格有异议，希望降价",createTime:"2023-04-17 09:30:00"},{id:2,name:"赵女士",phone:"13812345678",level:4,remark:"计划近期考察，意向较强",createTime:"2023-04-18 15:40:00"}],contactDialogVisible:!1,contactForm:{contactPerson:"",contactMethod:"",contactContent:"",contactTime:new Date},contactRules:{contactPerson:[{required:!0,message:"请输入联系人",trigger:"blur"}],contactMethod:[{required:!0,message:"请选择联系方式",trigger:"change"}],contactContent:[{required:!0,message:"请输入联系内容",trigger:"blur"}],contactTime:[{required:!0,message:"请选择联系时间",trigger:"change"}]},buyerDialogVisible:!1,buyerForm:{name:"",phone:"",level:3,remark:""},buyerRules:{name:[{required:!0,message:"请输入买家姓名",trigger:"blur"}],phone:[{required:!0,message:"请输入联系电话",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},rejectDialogVisible:!1,rejectForm:{reason:"",description:""},rejectRules:{reason:[{required:!0,message:"请选择拒绝原因",trigger:"change"}],description:[{required:!0,message:"请输入详细说明",trigger:"blur"}]},buyerDetailVisible:!1,currentBuyer:{}}},created(){this.stationId=this.$route.params.id,this.getStationDetail()},methods:{getStatusTag(e){const t={0:"warning",1:"success",2:"info",3:"danger",4:"info"};return t[e]||""},getStationDetail(){this.loading=!0,setTimeout((()=>{this.loading=!1}),500)},goBack(){this.$router.push("/transfer/list")},handleEdit(){this.$router.push(`/transfer/edit/${this.stationId}`)},handleStatus(e){let t="";switch(e){case"1":t="上架";break;case"2":t="标记为已交易";break;case"3":t="下架";break;case"4":t="关闭";break}this.$confirm(`确认要${t}该驿站转让信息?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{console.log("更新状态ID：",this.stationId,"状态：",e),this.$message({type:"success",message:"操作成功!"})})).catch((()=>{}))},handleReject(){this.rejectDialogVisible=!0},submitRejectForm(){this.$refs.rejectForm.validate((e=>{e&&(console.log("拒绝ID：",this.stationId),console.log("拒绝原因：",this.rejectForm.reason),console.log("详细说明：",this.rejectForm.description),this.rejectDialogVisible=!1,this.$message({type:"success",message:"操作成功!"}))}))},addContact(){this.contactForm={contactPerson:"",contactMethod:"",contactContent:"",contactTime:new Date},this.contactDialogVisible=!0},submitContactForm(){this.$refs.contactForm.validate((e=>{if(e){console.log("添加联系记录：",this.contactForm);const e={id:(new Date).getTime(),contactPerson:this.contactForm.contactPerson,contactTime:this.formatDateTime(this.contactForm.contactTime),contactMethod:this.contactForm.contactMethod,contactContent:this.contactForm.contactContent};this.contactRecords.unshift(e),this.contactDialogVisible=!1,this.$message({type:"success",message:"添加联系记录成功!"})}}))},addBuyer(){this.buyerForm={name:"",phone:"",level:3,remark:""},this.buyerDialogVisible=!0},submitBuyerForm(){this.$refs.buyerForm.validate((e=>{if(e){console.log("添加意向买家：",this.buyerForm);const e={id:(new Date).getTime(),name:this.buyerForm.name,phone:this.buyerForm.phone,level:this.buyerForm.level,remark:this.buyerForm.remark,createTime:this.formatDateTime(new Date)};this.buyerList.unshift(e),this.buyerDialogVisible=!1,this.$message({type:"success",message:"添加意向买家成功!"})}}))},handleBuyerDetail(e){this.currentBuyer=e,this.buyerDetailVisible=!0},deleteBuyer(){this.$confirm("确定删除该意向买家信息?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{const e=this.buyerList.findIndex((e=>e.id===this.currentBuyer.id));e>-1&&this.buyerList.splice(e,1),this.buyerDetailVisible=!1,this.$message({type:"success",message:"删除成功!"})})).catch((()=>{}))},formatDateTime(e){if(!e)return"";e=new Date(e);const t=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),l=String(e.getDate()).padStart(2,"0"),o=String(e.getHours()).padStart(2,"0"),n=String(e.getMinutes()).padStart(2,"0"),i=String(e.getSeconds()).padStart(2,"0");return`${t}-${a}-${l} ${o}:${n}:${i}`}}};var j=a(1241);const $=(0,j.A)(B,[["render",L],["__scopeId","data-v-d1bb6c2e"]]),x=$}}]);