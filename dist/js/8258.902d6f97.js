"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[8258],{8258:(e,t,a)=>{a.r(t),a.d(t,{default:()=>k});var i=a(6768),s=a(4232);const r={class:"page-container"},l={class:"card-header"},n={class:"header-right"},o={class:"stat-card"},c={class:"stat-info"},d={class:"stat-title"},h={class:"stat-number"},p={class:"stat-desc"};function u(e,t,a,u,g,v){const m=(0,i.g2)("el-date-picker"),b=(0,i.g2)("el-col"),k=(0,i.g2)("el-row"),y=(0,i.g2)("el-table-column"),C=(0,i.g2)("el-table"),w=(0,i.g2)("el-card");return(0,i.uX)(),(0,i.CE)("div",r,[(0,i.bF)(w,null,{header:(0,i.k6)((()=>[(0,i.Lk)("div",l,[t[1]||(t[1]=(0,i.Lk)("span",null,"驿站转让统计分析",-1)),(0,i.Lk)("div",n,[(0,i.bF)(m,{modelValue:g.dateRange,"onUpdate:modelValue":t[0]||(t[0]=e=>g.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",class:"date-picker",onChange:v.handleDateChange},null,8,["modelValue","onChange"])])])])),default:(0,i.k6)((()=>[(0,i.bF)(k,{gutter:20},{default:(0,i.k6)((()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(g.statistics,((e,a)=>((0,i.uX)(),(0,i.Wv)(b,{span:6,key:a},{default:(0,i.k6)((()=>[(0,i.Lk)("div",o,[(0,i.Lk)("div",{class:(0,s.C4)(["stat-icon",e.icon])},[(0,i.Lk)("i",{class:(0,s.C4)(e.iconClass)},null,2)],2),(0,i.Lk)("div",c,[(0,i.Lk)("div",d,(0,s.v_)(e.title),1),(0,i.Lk)("div",h,(0,s.v_)(e.value),1),(0,i.Lk)("div",p,[(0,i.Lk)("span",{class:(0,s.C4)(e.isIncrease?"up":"down")},[(0,i.Lk)("i",{class:(0,s.C4)(e.isIncrease?"el-icon-top":"el-icon-bottom")},null,2),(0,i.eW)(" "+(0,s.v_)(e.rate)+"% ",1)],2),t[2]||(t[2]=(0,i.eW)(" 较上期 "))])])])])),_:2},1024)))),128))])),_:1}),(0,i.bF)(k,{gutter:20,class:"chart-row"},{default:(0,i.k6)((()=>[(0,i.bF)(b,{span:12},{default:(0,i.k6)((()=>t[3]||(t[3]=[(0,i.Lk)("div",{class:"chart-wrapper"},[(0,i.Lk)("div",{class:"chart-title"},"转让数量趋势"),(0,i.Lk)("div",{class:"chart-container",id:"trendChart"})],-1)]))),_:1}),(0,i.bF)(b,{span:12},{default:(0,i.k6)((()=>t[4]||(t[4]=[(0,i.Lk)("div",{class:"chart-wrapper"},[(0,i.Lk)("div",{class:"chart-title"},"驿站类型分布"),(0,i.Lk)("div",{class:"chart-container",id:"typeChart"})],-1)]))),_:1})])),_:1}),(0,i.bF)(k,{gutter:20,class:"chart-row"},{default:(0,i.k6)((()=>[(0,i.bF)(b,{span:12},{default:(0,i.k6)((()=>t[5]||(t[5]=[(0,i.Lk)("div",{class:"chart-wrapper"},[(0,i.Lk)("div",{class:"chart-title"},"区域分布"),(0,i.Lk)("div",{class:"chart-container",id:"regionChart"})],-1)]))),_:1}),(0,i.bF)(b,{span:12},{default:(0,i.k6)((()=>t[6]||(t[6]=[(0,i.Lk)("div",{class:"chart-wrapper"},[(0,i.Lk)("div",{class:"chart-title"},"价格区间分布"),(0,i.Lk)("div",{class:"chart-container",id:"priceChart"})],-1)]))),_:1})])),_:1}),(0,i.bF)(k,{gutter:20},{default:(0,i.k6)((()=>[(0,i.bF)(b,{span:24},{default:(0,i.k6)((()=>[t[7]||(t[7]=(0,i.Lk)("div",{class:"table-title"},"热门转让驿站排行",-1)),(0,i.bF)(C,{data:g.hotStations,style:{width:"100%"},border:""},{default:(0,i.k6)((()=>[(0,i.bF)(y,{type:"index",label:"#",width:"50"}),(0,i.bF)(y,{prop:"stationName",label:"驿站名称","min-width":"160"}),(0,i.bF)(y,{prop:"region",label:"所在区域",width:"150"}),(0,i.bF)(y,{prop:"type",label:"驿站类型",width:"120"}),(0,i.bF)(y,{prop:"price",label:"转让价格",width:"120"},{default:(0,i.k6)((e=>[(0,i.eW)((0,s.v_)(e.row.price)+" 元",1)])),_:1}),(0,i.bF)(y,{prop:"views",label:"浏览量",width:"100",sortable:""}),(0,i.bF)(y,{prop:"likes",label:"收藏数",width:"100",sortable:""}),(0,i.bF)(y,{prop:"contacts",label:"联系数",width:"100",sortable:""})])),_:1},8,["data"])])),_:1})])),_:1})])),_:1})])}var g=a(2032);const v={name:"TransferStatistics",data(){return{dateRange:[this.getLastMonthDate(),new Date],statistics:[{title:"转让总数",value:358,rate:12.5,isIncrease:!0,icon:"blue",iconClass:"el-icon-s-shop"},{title:"已成交数",value:156,rate:8.3,isIncrease:!0,icon:"green",iconClass:"el-icon-s-claim"},{title:"平均价格",value:"25,630",rate:3.2,isIncrease:!1,icon:"orange",iconClass:"el-icon-money"},{title:"总浏览量",value:"12,568",rate:15.6,isIncrease:!0,icon:"purple",iconClass:"el-icon-view"}],hotStations:[{stationName:"海淀区中关村社区驿站",region:"北京市海淀区",type:"社区驿站",price:25e3,views:1256,likes:45,contacts:32},{stationName:"朝阳区望京SOHO商务驿站",region:"北京市朝阳区",type:"写字楼驿站",price:35e3,views:986,likes:38,contacts:28},{stationName:"西城区西单商业驿站",region:"北京市西城区",type:"商业驿站",price:4e4,views:875,likes:32,contacts:24},{stationName:"丰台区丽泽SOHO驿站",region:"北京市丰台区",type:"写字楼驿站",price:28e3,views:752,likes:28,contacts:21},{stationName:"通州区万达广场驿站",region:"北京市通州区",type:"商业驿站",price:32e3,views:684,likes:26,contacts:19}],trendChart:null,typeChart:null,regionChart:null,priceChart:null}},mounted(){this.initCharts()},methods:{getLastMonthDate(){const e=new Date;return e.setMonth(e.getMonth()-1),e},handleDateChange(){this.initCharts()},initCharts(){this.$nextTick((()=>{this.trendChart=g.Ts(document.getElementById("trendChart")),this.trendChart.setOption({tooltip:{trigger:"axis"},legend:{data:["转让数量","成交数量"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:["1月","2月","3月","4月","5月","6月","7月"]},yAxis:{type:"value"},series:[{name:"转让数量",type:"line",data:[32,45,38,52,48,60,55],smooth:!0,lineStyle:{width:3,color:"#409EFF"},areaStyle:{opacity:.2,color:"#409EFF"}},{name:"成交数量",type:"line",data:[15,20,18,25,22,30,26],smooth:!0,lineStyle:{width:3,color:"#67C23A"},areaStyle:{opacity:.2,color:"#67C23A"}}]}),this.typeChart=g.Ts(document.getElementById("typeChart")),this.typeChart.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:10,data:["社区驿站","快递驿站","校园驿站","写字楼驿站","商业驿站"]},series:[{name:"驿站类型",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:[{value:135,name:"社区驿站"},{value:86,name:"快递驿站"},{value:45,name:"校园驿站"},{value:68,name:"写字楼驿站"},{value:24,name:"商业驿站"}]}]}),this.regionChart=g.Ts(document.getElementById("regionChart")),this.regionChart.setOption({tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value"},yAxis:{type:"category",data:["海淀区","朝阳区","西城区","东城区","丰台区","通州区","昌平区"]},series:[{name:"转让数量",type:"bar",data:[85,72,45,38,42,36,40],itemStyle:{color:"#E6A23C"}}]}),this.priceChart=g.Ts(document.getElementById("priceChart")),this.priceChart.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:10,data:["1万以下","1-3万","3-5万","5-10万","10万以上"]},series:[{name:"价格区间",type:"pie",radius:"55%",center:["50%","60%"],data:[{value:42,name:"1万以下"},{value:124,name:"1-3万"},{value:98,name:"3-5万"},{value:65,name:"5-10万"},{value:29,name:"10万以上"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}),window.addEventListener("resize",(()=>{this.trendChart.resize(),this.typeChart.resize(),this.regionChart.resize(),this.priceChart.resize()}))}))}}};var m=a(1241);const b=(0,m.A)(v,[["render",u],["__scopeId","data-v-14574785"]]),k=b}}]);