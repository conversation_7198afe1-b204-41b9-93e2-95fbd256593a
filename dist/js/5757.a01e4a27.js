"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[5757],{5757:(e,l,a)=>{a.r(l),a.d(l,{default:()=>y});var t=a(6768),i=a(4232);const o={class:"page-container"},n={class:"search-box"},r={class:"toolbar"},s={class:"equipment-cell"},d={class:"price"},u={class:"pagination-container"},p={class:"dialog-footer"},m={class:"el-upload__tip"},c={class:"dialog-footer"};function g(e,l,a,g,b,h){const f=(0,t.g2)("el-input"),y=(0,t.g2)("el-form-item"),k=(0,t.g2)("el-option"),F=(0,t.g2)("el-select"),_=(0,t.g2)("el-input-number"),v=(0,t.g2)("el-button"),V=(0,t.g2)("el-form"),w=(0,t.g2)("el-table-column"),C=(0,t.g2)("el-image"),q=(0,t.g2)("el-tag"),P=(0,t.g2)("el-table"),L=(0,t.g2)("el-pagination"),x=(0,t.g2)("el-cascader"),T=(0,t.g2)("el-upload"),W=(0,t.g2)("el-dialog"),z=(0,t.g2)("el-link"),U=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",o,[(0,t.Lk)("div",n,[(0,t.bF)(V,{model:b.queryParams,ref:"queryForm",inline:!0},{default:(0,t.k6)((()=>[(0,t.bF)(y,{label:"设备名称",prop:"name"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{modelValue:b.queryParams.name,"onUpdate:modelValue":l[0]||(l[0]=e=>b.queryParams.name=e),placeholder:"请输入设备名称",clearable:""},null,8,["modelValue"])])),_:1}),(0,t.bF)(y,{label:"设备类型",prop:"type"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{modelValue:b.queryParams.type,"onUpdate:modelValue":l[1]||(l[1]=e=>b.queryParams.type=e),placeholder:"请选择设备类型",clearable:""},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(b.typeOptions,(e=>((0,t.uX)(),(0,t.Wv)(k,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(y,{label:"价格区间"},{default:(0,t.k6)((()=>[(0,t.bF)(_,{modelValue:b.queryParams.minPrice,"onUpdate:modelValue":l[2]||(l[2]=e=>b.queryParams.minPrice=e),min:0,"controls-position":"right",placeholder:"最低价",class:"price-input"},null,8,["modelValue"]),l[21]||(l[21]=(0,t.Lk)("span",{class:"el-range-separator"},"-",-1)),(0,t.bF)(_,{modelValue:b.queryParams.maxPrice,"onUpdate:modelValue":l[3]||(l[3]=e=>b.queryParams.maxPrice=e),min:0,"controls-position":"right",placeholder:"最高价",class:"price-input"},null,8,["modelValue"])])),_:1}),(0,t.bF)(y,{label:"销售状态",prop:"status"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{modelValue:b.queryParams.status,"onUpdate:modelValue":l[4]||(l[4]=e=>b.queryParams.status=e),placeholder:"请选择销售状态",clearable:""},{default:(0,t.k6)((()=>[(0,t.bF)(k,{label:"在售",value:"1"}),(0,t.bF)(k,{label:"已售",value:"2"}),(0,t.bF)(k,{label:"已下架",value:"0"})])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(y,null,{default:(0,t.k6)((()=>[(0,t.bF)(v,{type:"primary",icon:"Search",onClick:h.handleQuery},{default:(0,t.k6)((()=>l[22]||(l[22]=[(0,t.eW)("搜索")]))),_:1},8,["onClick"]),(0,t.bF)(v,{icon:"Refresh",onClick:h.resetQuery},{default:(0,t.k6)((()=>l[23]||(l[23]=[(0,t.eW)("重置")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])]),(0,t.Lk)("div",r,[(0,t.bF)(v,{type:"primary",icon:"Plus",onClick:h.handleAdd},{default:(0,t.k6)((()=>l[24]||(l[24]=[(0,t.eW)("新增设备")]))),_:1},8,["onClick"]),(0,t.bF)(v,{type:"success",icon:"Upload",onClick:h.handleImport},{default:(0,t.k6)((()=>l[25]||(l[25]=[(0,t.eW)("批量导入")]))),_:1},8,["onClick"]),(0,t.bF)(v,{type:"warning",icon:"Download",onClick:h.handleExport},{default:(0,t.k6)((()=>l[26]||(l[26]=[(0,t.eW)("导出数据")]))),_:1},8,["onClick"])]),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(P,{data:b.equipmentList,onSelectionChange:h.handleSelectionChange},{default:(0,t.k6)((()=>[(0,t.bF)(w,{type:"selection",width:"50",align:"center"}),(0,t.bF)(w,{label:"设备名称",align:"center",prop:"name","min-width":"180","show-overflow-tooltip":""},{default:(0,t.k6)((e=>[(0,t.Lk)("div",s,[(0,t.bF)(C,{src:e.row.image,fit:"cover",class:"equipment-image"},null,8,["src"]),(0,t.Lk)("span",null,(0,i.v_)(e.row.name),1)])])),_:1}),(0,t.bF)(w,{label:"设备类型",align:"center",prop:"type",width:"120"},{default:(0,t.k6)((e=>[(0,t.bF)(q,{type:h.getTypeTag(e.row.type)},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)(e.row.typeName),1)])),_:2},1032,["type"])])),_:1}),(0,t.bF)(w,{label:"价格",align:"center",prop:"price",width:"120"},{default:(0,t.k6)((e=>[(0,t.Lk)("span",d,"¥ "+(0,i.v_)(e.row.price),1)])),_:1}),(0,t.bF)(w,{label:"新旧程度",align:"center",prop:"condition",width:"100"},{default:(0,t.k6)((e=>[(0,t.bF)(q,{type:"info"},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)(e.row.condition),1)])),_:2},1024)])),_:1}),(0,t.bF)(w,{label:"所在区域",align:"center",prop:"region",width:"150"}),(0,t.bF)(w,{label:"发布者",align:"center",prop:"publisher",width:"120"}),(0,t.bF)(w,{label:"发布时间",align:"center",prop:"createTime",width:"160",sortable:""}),(0,t.bF)(w,{label:"状态",align:"center",prop:"status",width:"100"},{default:(0,t.k6)((e=>[(0,t.bF)(q,{type:"1"===e.row.status?"success":"2"===e.row.status?"info":"danger"},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)("1"===e.row.status?"在售":"2"===e.row.status?"已售":"已下架"),1)])),_:2},1032,["type"])])),_:1}),(0,t.bF)(w,{label:"操作",align:"center",width:"200",fixed:"right"},{default:(0,t.k6)((e=>[(0,t.bF)(v,{type:"text",size:"small",onClick:l=>h.handleView(e.row)},{default:(0,t.k6)((()=>l[27]||(l[27]=[(0,t.eW)("查看")]))),_:2},1032,["onClick"]),"2"!==e.row.status?((0,t.uX)(),(0,t.Wv)(v,{key:0,type:"text",size:"small",onClick:l=>h.handleEdit(e.row)},{default:(0,t.k6)((()=>l[28]||(l[28]=[(0,t.eW)("编辑")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0),"1"===e.row.status?((0,t.uX)(),(0,t.Wv)(v,{key:1,type:"text",size:"small",onClick:l=>h.handleStatus(e.row)},{default:(0,t.k6)((()=>l[29]||(l[29]=[(0,t.eW)("下架")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0),"0"===e.row.status?((0,t.uX)(),(0,t.Wv)(v,{key:2,type:"text",size:"small",onClick:l=>h.handleStatus(e.row)},{default:(0,t.k6)((()=>l[30]||(l[30]=[(0,t.eW)("上架")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0),"2"!==e.row.status?((0,t.uX)(),(0,t.Wv)(v,{key:3,type:"text",size:"small",onClick:l=>h.handleDelete(e.row)},{default:(0,t.k6)((()=>l[31]||(l[31]=[(0,t.eW)("删除")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0)])),_:1})])),_:1},8,["data","onSelectionChange"])),[[U,b.loading]]),(0,t.Lk)("div",u,[(0,t.bF)(L,{"current-page":b.queryParams.pageNum,"onUpdate:currentPage":l[5]||(l[5]=e=>b.queryParams.pageNum=e),"page-size":b.queryParams.pageSize,"onUpdate:pageSize":l[6]||(l[6]=e=>b.queryParams.pageSize=e),"page-sizes":[10,20,50,100],total:b.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:h.handleSizeChange,onCurrentChange:h.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])]),(0,t.bF)(W,{title:b.dialogTitle,modelValue:b.dialogVisible,"onUpdate:modelValue":l[18]||(l[18]=e=>b.dialogVisible=e),width:"650px","append-to-body":""},{footer:(0,t.k6)((()=>[(0,t.Lk)("div",p,[(0,t.bF)(v,{onClick:l[17]||(l[17]=e=>b.dialogVisible=!1)},{default:(0,t.k6)((()=>l[34]||(l[34]=[(0,t.eW)("取 消")]))),_:1}),(0,t.bF)(v,{type:"primary",onClick:h.submitForm},{default:(0,t.k6)((()=>l[35]||(l[35]=[(0,t.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,t.k6)((()=>[(0,t.bF)(V,{ref:"equipmentForm",model:b.form,rules:b.rules,"label-width":"100px"},{default:(0,t.k6)((()=>[(0,t.bF)(y,{label:"设备名称",prop:"name"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{modelValue:b.form.name,"onUpdate:modelValue":l[7]||(l[7]=e=>b.form.name=e),placeholder:"请输入设备名称"},null,8,["modelValue"])])),_:1}),(0,t.bF)(y,{label:"设备类型",prop:"type"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{modelValue:b.form.type,"onUpdate:modelValue":l[8]||(l[8]=e=>b.form.type=e),placeholder:"请选择设备类型",style:{width:"100%"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(b.typeOptions,(e=>((0,t.uX)(),(0,t.Wv)(k,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(y,{label:"品牌型号",prop:"brand"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{modelValue:b.form.brand,"onUpdate:modelValue":l[9]||(l[9]=e=>b.form.brand=e),placeholder:"请输入品牌型号"},null,8,["modelValue"])])),_:1}),(0,t.bF)(y,{label:"价格",prop:"price"},{default:(0,t.k6)((()=>[(0,t.bF)(_,{modelValue:b.form.price,"onUpdate:modelValue":l[10]||(l[10]=e=>b.form.price=e),min:0,precision:2,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),(0,t.bF)(y,{label:"使用年限",prop:"useYears"},{default:(0,t.k6)((()=>[(0,t.bF)(_,{modelValue:b.form.useYears,"onUpdate:modelValue":l[11]||(l[11]=e=>b.form.useYears=e),min:0,precision:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),(0,t.bF)(y,{label:"新旧程度",prop:"condition"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{modelValue:b.form.condition,"onUpdate:modelValue":l[12]||(l[12]=e=>b.form.condition=e),placeholder:"请选择新旧程度",style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(k,{label:"全新",value:"全新"}),(0,t.bF)(k,{label:"9成新",value:"9成新"}),(0,t.bF)(k,{label:"8成新",value:"8成新"}),(0,t.bF)(k,{label:"7成新",value:"7成新"}),(0,t.bF)(k,{label:"6成新及以下",value:"6成新及以下"})])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(y,{label:"交易方式",prop:"tradeType"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{modelValue:b.form.tradeType,"onUpdate:modelValue":l[13]||(l[13]=e=>b.form.tradeType=e),placeholder:"请选择交易方式",style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(k,{label:"自提",value:"自提"}),(0,t.bF)(k,{label:"配送",value:"配送"}),(0,t.bF)(k,{label:"均可",value:"均可"})])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(y,{label:"所在区域",prop:"region"},{default:(0,t.k6)((()=>[(0,t.bF)(x,{modelValue:b.form.region,"onUpdate:modelValue":l[14]||(l[14]=e=>b.form.region=e),options:b.regionOptions,placeholder:"请选择所在区域",style:{width:"100%"}},null,8,["modelValue","options"])])),_:1}),(0,t.bF)(y,{label:"联系电话",prop:"phone"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{modelValue:b.form.phone,"onUpdate:modelValue":l[15]||(l[15]=e=>b.form.phone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])])),_:1}),(0,t.bF)(y,{label:"设备图片",prop:"images"},{default:(0,t.k6)((()=>[(0,t.bF)(T,{"list-type":"picture-card",limit:6,action:"#","auto-upload":!1,"file-list":b.fileList,"on-change":h.handleFileChange,"on-remove":h.handleFileRemove},{default:(0,t.k6)((()=>l[32]||(l[32]=[(0,t.Lk)("i",{class:"el-icon-plus"},null,-1)]))),_:1},8,["file-list","on-change","on-remove"]),l[33]||(l[33]=(0,t.Lk)("div",{class:"el-upload__tip"},"支持JPG、PNG格式，单张不超过5MB，最多可上传6张",-1))])),_:1}),(0,t.bF)(y,{label:"设备描述",prop:"description"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{modelValue:b.form.description,"onUpdate:modelValue":l[16]||(l[16]=e=>b.form.description=e),type:"textarea",rows:"4",placeholder:"请输入设备描述"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"]),(0,t.bF)(W,{title:"批量导入设备",modelValue:b.importVisible,"onUpdate:modelValue":l[20]||(l[20]=e=>b.importVisible=e),width:"500px","append-to-body":""},{footer:(0,t.k6)((()=>[(0,t.Lk)("div",c,[(0,t.bF)(v,{onClick:l[19]||(l[19]=e=>b.importVisible=!1)},{default:(0,t.k6)((()=>l[40]||(l[40]=[(0,t.eW)("取 消")]))),_:1}),(0,t.bF)(v,{type:"primary",onClick:h.submitImport},{default:(0,t.k6)((()=>l[41]||(l[41]=[(0,t.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,t.k6)((()=>[(0,t.bF)(T,{class:"upload-demo",drag:"",action:"#","auto-upload":!1,limit:1,"on-change":h.handleImportFileChange},{default:(0,t.k6)((()=>[l[38]||(l[38]=(0,t.Lk)("i",{class:"el-icon-upload"},null,-1)),l[39]||(l[39]=(0,t.Lk)("div",{class:"el-upload__text"},[(0,t.eW)("将文件拖到此处，或"),(0,t.Lk)("em",null,"点击上传")],-1)),(0,t.Lk)("div",m,[l[37]||(l[37]=(0,t.eW)("只支持xlsx格式文件，")),(0,t.bF)(z,{type:"primary",underline:!1,onClick:h.downloadTemplate},{default:(0,t.k6)((()=>l[36]||(l[36]=[(0,t.eW)("下载模板")]))),_:1},8,["onClick"])])])),_:1},8,["on-change"])])),_:1},8,["modelValue"])])}const b={name:"EquipmentList",data(){return{loading:!1,selectedIds:[],total:0,equipmentList:[{id:1,name:"丰巢快递柜",image:"https://img1.baidu.com/it/u=413646961,2312577432&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333",type:"1",typeName:"快递柜",price:3500,condition:"8成新",region:"北京市朝阳区",publisher:"张三",createTime:"2023-04-10 10:30:00",status:"1"},{id:2,name:"快递分拣机",image:"https://img2.baidu.com/it/u=1003272215,1878948666&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800",type:"2",typeName:"分拣设备",price:12e3,condition:"9成新",region:"北京市海淀区",publisher:"李四",createTime:"2023-04-11 14:20:00",status:"1"},{id:3,name:"电动三轮车",image:"https://img0.baidu.com/it/u=2277942570,1789375638&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333",type:"3",typeName:"运输车辆",price:2800,condition:"7成新",region:"北京市丰台区",publisher:"王五",createTime:"2023-04-12 09:15:00",status:"2"}],typeOptions:[{value:"1",label:"快递柜"},{value:"2",label:"分拣设备"},{value:"3",label:"运输车辆"},{value:"4",label:"称重设备"},{value:"5",label:"打包设备"},{value:"6",label:"其他设备"}],regionOptions:[{value:"北京市",label:"北京市",children:[{value:"北京市",label:"北京市",children:[{value:"东城区",label:"东城区"},{value:"西城区",label:"西城区"},{value:"朝阳区",label:"朝阳区"},{value:"海淀区",label:"海淀区"},{value:"丰台区",label:"丰台区"}]}]}],queryParams:{pageNum:1,pageSize:10,name:"",type:"",minPrice:null,maxPrice:null,status:""},form:{id:void 0,name:"",type:"",brand:"",price:0,useYears:0,condition:"",tradeType:"",region:[],phone:"",images:[],description:""},rules:{name:[{required:!0,message:"设备名称不能为空",trigger:"blur"}],type:[{required:!0,message:"请选择设备类型",trigger:"change"}],price:[{required:!0,message:"价格不能为空",trigger:"blur"}],condition:[{required:!0,message:"请选择新旧程度",trigger:"change"}],region:[{required:!0,message:"请选择所在区域",trigger:"change"}],phone:[{required:!0,message:"联系电话不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},fileList:[],dialogTitle:"",dialogVisible:!1,importVisible:!1}},created(){this.getList()},methods:{getList(){this.loading=!0,setTimeout((()=>{this.total=this.equipmentList.length,this.loading=!1}),500)},handleQuery(){this.queryParams.pageNum=1,this.getList()},resetQuery(){this.$refs.queryForm.resetFields(),this.handleQuery()},handleAdd(){this.reset(),this.dialogTitle="添加设备",this.dialogVisible=!0},handleEdit(e){this.reset(),this.form={id:e.id,name:e.name,type:e.type,brand:"品牌名称 XYZ-123",price:e.price,useYears:2,condition:e.condition,tradeType:"自提",region:["北京市","北京市",e.region.split("市")[1].trim()],phone:"13800138000",images:[e.image],description:"这是一段关于设备的详细描述，包含了使用情况、性能特点等信息。"},this.fileList=[{name:"image.jpg",url:e.image}],this.dialogTitle="编辑设备",this.dialogVisible=!0},handleView(e){this.$router.push(`/equipment/detail/${e.id}`)},handleDelete(e){this.$confirm(`是否确认删除设备"${e.name}"?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$message({type:"success",message:"删除成功!"}),this.getList()})).catch((()=>{}))},handleStatus(e){let l="1"===e.status?"下架":"上架",a="1"===e.status?"0":"1";this.$confirm(`是否确认${l}设备"${e.name}"?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$message({type:"success",message:`${l}成功!`}),e.status=a})).catch((()=>{}))},handleExport(){this.$confirm("是否确认导出所有设备数据?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$message({type:"success",message:"导出成功!"})})).catch((()=>{}))},handleImport(){this.importVisible=!0},downloadTemplate(){window.open("/download/equipment_template.xlsx")},handleImportFileChange(e){console.log(e)},submitImport(){this.$message({type:"success",message:"导入成功!"}),this.importVisible=!1},handleFileChange(e){console.log(e)},handleFileRemove(e){console.log(e)},reset(){this.form={id:void 0,name:"",type:"",brand:"",price:0,useYears:0,condition:"",tradeType:"",region:[],phone:"",images:[],description:""},this.fileList=[]},submitForm(){this.$refs.equipmentForm.validate((e=>{e&&(void 0!==this.form.id?this.$message({type:"success",message:"修改成功!"}):this.$message({type:"success",message:"新增成功!"}),this.dialogVisible=!1,this.getList())}))},handleSelectionChange(e){this.selectedIds=e.map((e=>e.id))},handleSizeChange(e){this.queryParams.pageSize=e,this.getList()},handleCurrentChange(e){this.queryParams.pageNum=e,this.getList()},getTypeTag(e){const l={1:"primary",2:"success",3:"warning",4:"danger",5:"info",6:""};return l[e]||""}}};var h=a(1241);const f=(0,h.A)(b,[["render",g],["__scopeId","data-v-1d543de0"]]),y=f}}]);