"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[4633],{4633:(e,a,t)=>{t.r(a),t.d(a,{default:()=>z});var l=t(6768),s=t(4232);const r={class:"app-container"},d={class:"stat-value primary"},n={class:"stat-trend"},o={class:"stat-value warning"},i={class:"stat-trend"},c={class:"stat-value success"},p={class:"stat-trend"},u={class:"stat-value danger"},v={class:"stat-trend"},k={class:"card-header"},b={class:"chart-container"},h={ref:"trendChartRef",class:"chart"},g={class:"chart-container"},f={ref:"statusChartRef",class:"chart"},m={class:"chart-container"},F={ref:"verifyTypeChartRef",class:"chart"},_={class:"chart-container"},w={ref:"ageChartRef",class:"chart"},y={class:"chart-container"},C={ref:"regionChartRef",class:"chart"},L={class:"card-header"},T={class:"table-container"};function R(e,a,t,R,A,W){const S=(0,l.g2)("ArrowUp"),j=(0,l.g2)("el-icon"),x=(0,l.g2)("ArrowDown"),z=(0,l.g2)("el-card"),E=(0,l.g2)("el-col"),X=(0,l.g2)("el-row"),K=(0,l.g2)("el-radio-button"),O=(0,l.g2)("el-radio-group"),V=(0,l.g2)("el-option"),D=(0,l.g2)("el-select"),U=(0,l.g2)("el-table-column"),B=(0,l.g2)("el-progress"),I=(0,l.g2)("el-table");return(0,l.uX)(),(0,l.CE)("div",r,[(0,l.bF)(X,{gutter:20},{default:(0,l.k6)((()=>[(0,l.bF)(E,{span:6},{default:(0,l.k6)((()=>[(0,l.bF)(z,{class:"stat-card",shadow:"hover"},{default:(0,l.k6)((()=>[a[3]||(a[3]=(0,l.Lk)("div",{class:"stat-title"},"总认证申请",-1)),(0,l.Lk)("div",d,(0,s.v_)(R.statistics.totalCount),1),(0,l.Lk)("div",n,[a[2]||(a[2]=(0,l.Lk)("span",null,"较上月",-1)),(0,l.Lk)("span",{class:(0,s.C4)(["trend-value",R.statistics.totalTrend>0?"up":"down"])},[(0,l.eW)((0,s.v_)(R.statistics.totalTrend>0?"+":"")+(0,s.v_)(R.statistics.totalTrend)+"% ",1),R.statistics.totalTrend>0?((0,l.uX)(),(0,l.Wv)(j,{key:0},{default:(0,l.k6)((()=>[(0,l.bF)(S)])),_:1})):((0,l.uX)(),(0,l.Wv)(j,{key:1},{default:(0,l.k6)((()=>[(0,l.bF)(x)])),_:1}))],2)])])),_:1})])),_:1}),(0,l.bF)(E,{span:6},{default:(0,l.k6)((()=>[(0,l.bF)(z,{class:"stat-card",shadow:"hover"},{default:(0,l.k6)((()=>[a[5]||(a[5]=(0,l.Lk)("div",{class:"stat-title"},"待审核",-1)),(0,l.Lk)("div",o,(0,s.v_)(R.statistics.pendingCount),1),(0,l.Lk)("div",i,[a[4]||(a[4]=(0,l.Lk)("span",null,"较昨日",-1)),(0,l.Lk)("span",{class:(0,s.C4)(["trend-value",R.statistics.pendingTrend>0?"up":"down"])},[(0,l.eW)((0,s.v_)(R.statistics.pendingTrend>0?"+":"")+(0,s.v_)(R.statistics.pendingTrend)+"% ",1),R.statistics.pendingTrend>0?((0,l.uX)(),(0,l.Wv)(j,{key:0},{default:(0,l.k6)((()=>[(0,l.bF)(S)])),_:1})):((0,l.uX)(),(0,l.Wv)(j,{key:1},{default:(0,l.k6)((()=>[(0,l.bF)(x)])),_:1}))],2)])])),_:1})])),_:1}),(0,l.bF)(E,{span:6},{default:(0,l.k6)((()=>[(0,l.bF)(z,{class:"stat-card",shadow:"hover"},{default:(0,l.k6)((()=>[a[7]||(a[7]=(0,l.Lk)("div",{class:"stat-title"},"已通过",-1)),(0,l.Lk)("div",c,(0,s.v_)(R.statistics.approvedCount),1),(0,l.Lk)("div",p,[a[6]||(a[6]=(0,l.Lk)("span",null,"较上月",-1)),(0,l.Lk)("span",{class:(0,s.C4)(["trend-value",R.statistics.approvedTrend>0?"up":"down"])},[(0,l.eW)((0,s.v_)(R.statistics.approvedTrend>0?"+":"")+(0,s.v_)(R.statistics.approvedTrend)+"% ",1),R.statistics.approvedTrend>0?((0,l.uX)(),(0,l.Wv)(j,{key:0},{default:(0,l.k6)((()=>[(0,l.bF)(S)])),_:1})):((0,l.uX)(),(0,l.Wv)(j,{key:1},{default:(0,l.k6)((()=>[(0,l.bF)(x)])),_:1}))],2)])])),_:1})])),_:1}),(0,l.bF)(E,{span:6},{default:(0,l.k6)((()=>[(0,l.bF)(z,{class:"stat-card",shadow:"hover"},{default:(0,l.k6)((()=>[a[9]||(a[9]=(0,l.Lk)("div",{class:"stat-title"},"已驳回",-1)),(0,l.Lk)("div",u,(0,s.v_)(R.statistics.rejectedCount),1),(0,l.Lk)("div",v,[a[8]||(a[8]=(0,l.Lk)("span",null,"较上月",-1)),(0,l.Lk)("span",{class:(0,s.C4)(["trend-value",R.statistics.rejectedTrend>0?"up":"down"])},[(0,l.eW)((0,s.v_)(R.statistics.rejectedTrend>0?"+":"")+(0,s.v_)(R.statistics.rejectedTrend)+"% ",1),R.statistics.rejectedTrend>0?((0,l.uX)(),(0,l.Wv)(j,{key:0},{default:(0,l.k6)((()=>[(0,l.bF)(S)])),_:1})):((0,l.uX)(),(0,l.Wv)(j,{key:1},{default:(0,l.k6)((()=>[(0,l.bF)(x)])),_:1}))],2)])])),_:1})])),_:1})])),_:1}),(0,l.bF)(X,{gutter:20,class:"chart-row"},{default:(0,l.k6)((()=>[(0,l.bF)(E,{span:12},{default:(0,l.k6)((()=>[(0,l.bF)(z,{shadow:"hover"},{header:(0,l.k6)((()=>[(0,l.Lk)("div",k,[a[13]||(a[13]=(0,l.Lk)("span",null,"认证数量趋势",-1)),(0,l.bF)(O,{modelValue:R.timeRange,"onUpdate:modelValue":a[0]||(a[0]=e=>R.timeRange=e),size:"small",onChange:R.handleTimeRangeChange},{default:(0,l.k6)((()=>[(0,l.bF)(K,{label:"week"},{default:(0,l.k6)((()=>a[10]||(a[10]=[(0,l.eW)("近7天")]))),_:1}),(0,l.bF)(K,{label:"month"},{default:(0,l.k6)((()=>a[11]||(a[11]=[(0,l.eW)("近30天")]))),_:1}),(0,l.bF)(K,{label:"year"},{default:(0,l.k6)((()=>a[12]||(a[12]=[(0,l.eW)("近12个月")]))),_:1})])),_:1},8,["modelValue","onChange"])])])),default:(0,l.k6)((()=>[(0,l.Lk)("div",b,[(0,l.Lk)("div",h,null,512)])])),_:1})])),_:1}),(0,l.bF)(E,{span:12},{default:(0,l.k6)((()=>[(0,l.bF)(z,{shadow:"hover"},{header:(0,l.k6)((()=>a[14]||(a[14]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"认证状态分布")],-1)]))),default:(0,l.k6)((()=>[(0,l.Lk)("div",g,[(0,l.Lk)("div",f,null,512)])])),_:1})])),_:1})])),_:1}),(0,l.bF)(X,{gutter:20,class:"chart-row"},{default:(0,l.k6)((()=>[(0,l.bF)(E,{span:8},{default:(0,l.k6)((()=>[(0,l.bF)(z,{shadow:"hover"},{header:(0,l.k6)((()=>a[15]||(a[15]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"认证方式分布")],-1)]))),default:(0,l.k6)((()=>[(0,l.Lk)("div",m,[(0,l.Lk)("div",F,null,512)])])),_:1})])),_:1}),(0,l.bF)(E,{span:8},{default:(0,l.k6)((()=>[(0,l.bF)(z,{shadow:"hover"},{header:(0,l.k6)((()=>a[16]||(a[16]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"年龄分布")],-1)]))),default:(0,l.k6)((()=>[(0,l.Lk)("div",_,[(0,l.Lk)("div",w,null,512)])])),_:1})])),_:1}),(0,l.bF)(E,{span:8},{default:(0,l.k6)((()=>[(0,l.bF)(z,{shadow:"hover"},{header:(0,l.k6)((()=>a[17]||(a[17]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"地区分布")],-1)]))),default:(0,l.k6)((()=>[(0,l.Lk)("div",y,[(0,l.Lk)("div",C,null,512)])])),_:1})])),_:1})])),_:1}),(0,l.bF)(X,{gutter:20,class:"chart-row"},{default:(0,l.k6)((()=>[(0,l.bF)(E,{span:24},{default:(0,l.k6)((()=>[(0,l.bF)(z,{shadow:"hover"},{header:(0,l.k6)((()=>[(0,l.Lk)("div",L,[a[18]||(a[18]=(0,l.Lk)("span",null,"认证审核效率分析",-1)),(0,l.bF)(D,{modelValue:R.auditTimeRange,"onUpdate:modelValue":a[1]||(a[1]=e=>R.auditTimeRange=e),placeholder:"时间范围",size:"small",onChange:R.handleAuditTimeChange},{default:(0,l.k6)((()=>[(0,l.bF)(V,{label:"近7天",value:"week"}),(0,l.bF)(V,{label:"近30天",value:"month"}),(0,l.bF)(V,{label:"近3个月",value:"quarter"})])),_:1},8,["modelValue","onChange"])])])),default:(0,l.k6)((()=>[(0,l.Lk)("div",T,[(0,l.bF)(I,{data:R.auditEfficiencyData,border:"",style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(U,{prop:"date",label:"日期",align:"center",width:"120"}),(0,l.bF)(U,{prop:"total",label:"认证申请数",align:"center",width:"120"}),(0,l.bF)(U,{prop:"approved",label:"通过数",align:"center",width:"120"}),(0,l.bF)(U,{prop:"rejected",label:"驳回数",align:"center",width:"120"}),(0,l.bF)(U,{prop:"pending",label:"待处理数",align:"center",width:"120"}),(0,l.bF)(U,{prop:"avgTime",label:"平均审核时间",align:"center",width:"150"},{default:(0,l.k6)((({row:e})=>[(0,l.eW)((0,s.v_)(e.avgTime)+" 小时 ",1)])),_:1}),(0,l.bF)(U,{prop:"approvalRate",label:"通过率",align:"center",width:"120"},{default:(0,l.k6)((({row:e})=>[(0,l.bF)(B,{percentage:e.approvalRate,color:R.getApprovalRateColor(e.approvalRate)},null,8,["percentage","color"])])),_:1}),(0,l.bF)(U,{prop:"operator",label:"审核人员",align:"center","min-width":"150"})])),_:1},8,["data"])])])),_:1})])),_:1})])),_:1})])}var A=t(144),W=t(2032);const S={name:"IdentityStatistics",setup(){const e=(0,A.KR)(null),a=(0,A.KR)(null),t=(0,A.KR)(null),s=(0,A.KR)(null),r=(0,A.KR)(null);let d=null,n=null,o=null,i=null,c=null;const p=(0,A.KR)("week"),u=(0,A.KR)("week"),v=(0,A.Kh)({totalCount:235,pendingCount:42,approvedCount:180,rejectedCount:13,totalTrend:15.8,pendingTrend:-5.2,approvedTrend:12.5,rejectedTrend:-3.1}),k=(0,A.KR)([{date:"2023-05-20",total:15,approved:12,rejected:2,pending:1,avgTime:2.5,approvalRate:85.7,operator:"张三、李四"},{date:"2023-05-19",total:18,approved:15,rejected:2,pending:1,avgTime:1.8,approvalRate:88.2,operator:"张三、王五"},{date:"2023-05-18",total:21,approved:17,rejected:3,pending:1,avgTime:3.2,approvalRate:85,operator:"李四、王五"},{date:"2023-05-17",total:12,approved:10,rejected:1,pending:1,avgTime:1.5,approvalRate:90.9,operator:"张三"},{date:"2023-05-16",total:16,approved:13,rejected:2,pending:1,avgTime:2,approvalRate:86.7,operator:"李四"}]),b=()=>{d=W.Ts(e.value);const l={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},legend:{data:["总申请","已通过","已驳回","待审核"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:["05-14","05-15","05-16","05-17","05-18","05-19","05-20"]},yAxis:{type:"value",name:"数量"},series:[{name:"总申请",type:"line",data:[15,18,16,12,21,18,15],lineStyle:{color:"#409EFF"},itemStyle:{color:"#409EFF"}},{name:"已通过",type:"bar",stack:"total",data:[12,15,13,10,17,15,12],itemStyle:{color:"#67C23A"}},{name:"已驳回",type:"bar",stack:"total",data:[2,2,2,1,3,2,2],itemStyle:{color:"#F56C6C"}},{name:"待审核",type:"bar",stack:"total",data:[1,1,1,1,1,1,1],itemStyle:{color:"#E6A23C"}}]};d.setOption(l),n=W.Ts(a.value);const p={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:10,data:["已通过","已驳回","待审核"]},series:[{name:"认证状态",type:"pie",radius:["50%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:[{value:v.approvedCount,name:"已通过",itemStyle:{color:"#67C23A"}},{value:v.rejectedCount,name:"已驳回",itemStyle:{color:"#F56C6C"}},{value:v.pendingCount,name:"待审核",itemStyle:{color:"#E6A23C"}}]}]};n.setOption(p),o=W.Ts(t.value);const u={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{bottom:"0%",left:"center",data:["身份证认证","人脸认证"]},series:[{name:"认证方式",type:"pie",radius:"70%",center:["50%","40%"],data:[{value:142,name:"身份证认证",itemStyle:{color:"#409EFF"}},{value:93,name:"人脸认证",itemStyle:{color:"#FF9F43"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};o.setOption(u),i=W.Ts(s.value);const k={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"15%",containLabel:!0},xAxis:{type:"category",data:["18-24岁","25-34岁","35-44岁","45-54岁","55岁以上"],axisLabel:{interval:0,rotate:30}},yAxis:{type:"value",name:"人数"},series:[{name:"人数",type:"bar",data:[35,85,65,32,18],itemStyle:{color:new W.fA.W4(0,0,0,1,[{offset:0,color:"#83bff6"},{offset:.5,color:"#188df0"},{offset:1,color:"#188df0"}])}}]};i.setOption(k),c=W.Ts(r.value);const b={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",right:10,top:"center",data:["北京","上海","广州","深圳","其他"]},series:[{name:"地区分布",type:"pie",radius:"70%",center:["40%","50%"],data:[{value:58,name:"北京"},{value:45,name:"上海"},{value:38,name:"广州"},{value:32,name:"深圳"},{value:62,name:"其他"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};c.setOption(b),window.addEventListener("resize",h)},h=()=>{d&&d.resize(),n&&n.resize(),o&&o.resize(),i&&i.resize(),c&&c.resize()},g=e=>{let a=[],t=[],l=[],s=[],r=[];switch(e){case"week":a=["05-14","05-15","05-16","05-17","05-18","05-19","05-20"],t=[15,18,16,12,21,18,15],l=[12,15,13,10,17,15,12],s=[2,2,2,1,3,2,2],r=[1,1,1,1,1,1,1];break;case"month":a=["04-21","04-26","05-01","05-06","05-11","05-16","05-20"],t=[60,75,85,70,95,80,65],l=[50,62,70,58,80,65,55],s=[8,10,12,8,10,10,7],r=[2,3,3,4,5,5,3];break;case"year":a=["2022-06","2022-08","2022-10","2022-12","2023-02","2023-04","2023-05"],t=[320,350,380,420,350,410,235],l=[260,285,310,350,290,330,180],s=[40,45,50,55,45,60,43],r=[20,20,20,15,15,20,12];break}d.setOption({xAxis:{data:a},series:[{name:"总申请",data:t},{name:"已通过",data:l},{name:"已驳回",data:s},{name:"待审核",data:r}]})},f=e=>{},m=e=>e>=90?"#67C23A":e>=80?"#409EFF":e>=70?"#E6A23C":"#F56C6C";return(0,l.sV)((()=>{b()})),(0,l.xo)((()=>{window.removeEventListener("resize",h),d&&d.dispose(),n&&n.dispose(),o&&o.dispose(),i&&i.dispose(),c&&c.dispose()})),{trendChartRef:e,statusChartRef:a,verifyTypeChartRef:t,ageChartRef:s,regionChartRef:r,timeRange:p,auditTimeRange:u,statistics:v,auditEfficiencyData:k,handleTimeRangeChange:g,handleAuditTimeChange:f,getApprovalRateColor:m}}};var j=t(1241);const x=(0,j.A)(S,[["render",R],["__scopeId","data-v-0d96eb36"]]),z=x}}]);