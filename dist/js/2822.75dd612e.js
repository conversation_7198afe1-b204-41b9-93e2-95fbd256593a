"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[2822],{2822:(a,t,e)=>{e.r(t),e.d(t,{default:()=>l});var d=e(6768);const s={class:"dashboard-container"};function i(a,t,e,i,r,v){return(0,d.uX)(),(0,d.CE)("div",s,t[0]||(t[0]=[(0,d.Fv)('<h2 data-v-0e88d929>欢迎使用驿站帮Pro后台管理系统</h2><div class="stats-cards" data-v-0e88d929><div class="stat-card" data-v-0e88d929><div class="stat-title" data-v-0e88d929>总用户数</div><div class="stat-value" data-v-0e88d929>1256</div><div class="stat-compare" data-v-0e88d929>较昨日 <span class="up" data-v-0e88d929>+18</span></div></div><div class="stat-card" data-v-0e88d929><div class="stat-title" data-v-0e88d929>驿站数量</div><div class="stat-value" data-v-0e88d929>358</div><div class="stat-compare" data-v-0e88d929>较昨日 <span class="up" data-v-0e88d929>+5</span></div></div><div class="stat-card" data-v-0e88d929><div class="stat-title" data-v-0e88d929>订单总数</div><div class="stat-value" data-v-0e88d929>6832</div><div class="stat-compare" data-v-0e88d929>较昨日 <span class="up" data-v-0e88d929>+43</span></div></div><div class="stat-card" data-v-0e88d929><div class="stat-title" data-v-0e88d929>总收入(元)</div><div class="stat-value" data-v-0e88d929>235698.56</div><div class="stat-compare" data-v-0e88d929>较昨日 <span class="up" data-v-0e88d929>+1268.32</span></div></div></div><div class="chart-container" data-v-0e88d929><div class="chart-card wide" data-v-0e88d929><h3 data-v-0e88d929>订单走势</h3><div id="orderTrendChart" style="height:300px;" data-v-0e88d929></div></div><div class="chart-card" data-v-0e88d929><h3 data-v-0e88d929>驿站分布</h3><div id="stationDistChart" style="height:300px;" data-v-0e88d929></div></div></div>',3)]))}var r=e(2032);const v={name:"Dashboard",data(){return{orderChart:null,stationChart:null}},mounted(){this.initCharts()},beforeUnmount(){this.orderChart&&this.orderChart.dispose(),this.stationChart&&this.stationChart.dispose(),window.removeEventListener("resize",this.resizeCharts)},methods:{initCharts(){this.$nextTick((()=>{this.orderChart=r.Ts(document.getElementById("orderTrendChart")),this.orderChart.setOption({tooltip:{trigger:"axis"},legend:{data:["订单数量","销售额"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:["1月","2月","3月","4月","5月","6月","7月"]},yAxis:[{type:"value",name:"订单数量"},{type:"value",name:"销售额",position:"right",axisLabel:{formatter:"{value} 元"}}],series:[{name:"订单数量",type:"line",data:[820,932,901,934,1290,1330,1320],smooth:!0,lineStyle:{width:3,color:"#409EFF"},areaStyle:{opacity:.2,color:"#409EFF"}},{name:"销售额",type:"line",yAxisIndex:1,data:[22e3,25e3,24e3,26e3,32e3,35e3,34e3],smooth:!0,lineStyle:{width:3,color:"#67C23A"},areaStyle:{opacity:.2,color:"#67C23A"}}]}),this.stationChart=r.Ts(document.getElementById("stationDistChart")),this.stationChart.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:10,data:["社区驿站","快递驿站","校园驿站","写字楼驿站","商业驿站"]},series:[{name:"驿站类型",type:"pie",radius:"70%",center:["50%","60%"],data:[{value:135,name:"社区驿站"},{value:86,name:"快递驿站"},{value:45,name:"校园驿站"},{value:68,name:"写字楼驿站"},{value:24,name:"商业驿站"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}),window.addEventListener("resize",this.resizeCharts)}))},resizeCharts(){this.orderChart&&this.orderChart.resize(),this.stationChart&&this.stationChart.resize()}}};var n=e(1241);const o=(0,n.A)(v,[["render",i],["__scopeId","data-v-0e88d929"]]),l=o}}]);