"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[2508],{2508:(e,t,l)=>{l.r(t),l.d(t,{default:()=>u});var a=l(6768),o=l(4232);const i={class:"app-container"},d={slot:"footer",class:"dialog-footer"};function s(e,t,l,s,r,n){const c=(0,a.g2)("el-button"),u=(0,a.g2)("el-col"),m=(0,a.g2)("el-row"),p=(0,a.g2)("el-table-column"),b=(0,a.g2)("el-tag"),h=(0,a.g2)("el-table"),g=(0,a.g2)("el-cascader"),f=(0,a.g2)("el-form-item"),k=(0,a.g2)("el-input"),_=(0,a.g2)("el-input-number"),F=(0,a.g2)("el-radio"),y=(0,a.g2)("el-radio-group"),v=(0,a.g2)("el-form"),w=(0,a.g2)("el-dialog"),C=(0,a.g2)("el-card"),V=(0,a.gN)("loading");return(0,a.uX)(),(0,a.CE)("div",i,[(0,a.bF)(C,null,{default:(0,a.k6)((()=>[t[13]||(t[13]=(0,a.Lk)("div",{slot:"header"},[(0,a.Lk)("span",null,"职位分类")],-1)),(0,a.bF)(m,{gutter:10,class:"mb8"},{default:(0,a.k6)((()=>[(0,a.bF)(u,{span:1.5},{default:(0,a.k6)((()=>[(0,a.bF)(c,{type:"primary",plain:"",icon:"el-icon-plus",size:"mini",onClick:n.handleAdd},{default:(0,a.k6)((()=>t[5]||(t[5]=[(0,a.eW)("新增")]))),_:1},8,["onClick"])])),_:1})])),_:1}),(0,a.bo)(((0,a.uX)(),(0,a.Wv)(h,{data:r.categoryList,"row-key":"id","default-expand-all":""},{default:(0,a.k6)((()=>[(0,a.bF)(p,{prop:"name",label:"分类名称",width:"200"}),(0,a.bF)(p,{prop:"code",label:"分类编码",width:"200"}),(0,a.bF)(p,{prop:"sort",label:"显示顺序",width:"100"}),(0,a.bF)(p,{prop:"status",label:"状态",width:"100"},{default:(0,a.k6)((e=>[(0,a.bF)(b,{type:"0"===e.row.status?"danger":"success"},{default:(0,a.k6)((()=>[(0,a.eW)((0,o.v_)("0"===e.row.status?"禁用":"正常"),1)])),_:2},1032,["type"])])),_:1}),(0,a.bF)(p,{label:"创建时间",align:"center",prop:"createTime",width:"180"}),(0,a.bF)(p,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:(0,a.k6)((e=>[(0,a.bF)(c,{size:"mini",type:"text",icon:"el-icon-edit",onClick:t=>n.handleUpdate(e.row)},{default:(0,a.k6)((()=>t[6]||(t[6]=[(0,a.eW)("修改")]))),_:2},1032,["onClick"]),(0,a.bF)(c,{size:"mini",type:"text",icon:"el-icon-plus",onClick:t=>n.handleAdd(e.row)},{default:(0,a.k6)((()=>t[7]||(t[7]=[(0,a.eW)("新增")]))),_:2},1032,["onClick"]),(0,a.bF)(c,{size:"mini",type:"text",icon:"el-icon-delete",onClick:t=>n.handleDelete(e.row)},{default:(0,a.k6)((()=>t[8]||(t[8]=[(0,a.eW)("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[V,r.loading]]),(0,a.bF)(w,{title:r.title,visible:r.open,width:"500px","append-to-body":""},{default:(0,a.k6)((()=>[(0,a.bF)(v,{ref:"form",model:r.form,rules:r.rules,"label-width":"100px"},{default:(0,a.k6)((()=>[(0,a.bF)(f,{label:"上级分类",prop:"parentId"},{default:(0,a.k6)((()=>[(0,a.bF)(g,{modelValue:r.form.parentId,"onUpdate:modelValue":t[0]||(t[0]=e=>r.form.parentId=e),options:r.categoryOptions,props:{checkStrictly:!0,value:"id",label:"name"},placeholder:"请选择上级分类",clearable:""},null,8,["modelValue","options"])])),_:1}),(0,a.bF)(f,{label:"分类名称",prop:"name"},{default:(0,a.k6)((()=>[(0,a.bF)(k,{modelValue:r.form.name,"onUpdate:modelValue":t[1]||(t[1]=e=>r.form.name=e),placeholder:"请输入分类名称"},null,8,["modelValue"])])),_:1}),(0,a.bF)(f,{label:"分类编码",prop:"code"},{default:(0,a.k6)((()=>[(0,a.bF)(k,{modelValue:r.form.code,"onUpdate:modelValue":t[2]||(t[2]=e=>r.form.code=e),placeholder:"请输入分类编码"},null,8,["modelValue"])])),_:1}),(0,a.bF)(f,{label:"显示顺序",prop:"sort"},{default:(0,a.k6)((()=>[(0,a.bF)(_,{modelValue:r.form.sort,"onUpdate:modelValue":t[3]||(t[3]=e=>r.form.sort=e),min:0,max:999,"controls-position":"right"},null,8,["modelValue"])])),_:1}),(0,a.bF)(f,{label:"状态",prop:"status"},{default:(0,a.k6)((()=>[(0,a.bF)(y,{modelValue:r.form.status,"onUpdate:modelValue":t[4]||(t[4]=e=>r.form.status=e)},{default:(0,a.k6)((()=>[(0,a.bF)(F,{label:"0"},{default:(0,a.k6)((()=>t[9]||(t[9]=[(0,a.eW)("禁用")]))),_:1}),(0,a.bF)(F,{label:"1"},{default:(0,a.k6)((()=>t[10]||(t[10]=[(0,a.eW)("正常")]))),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),(0,a.Lk)("div",d,[(0,a.bF)(c,{onClick:n.cancel},{default:(0,a.k6)((()=>t[11]||(t[11]=[(0,a.eW)("取 消")]))),_:1},8,["onClick"]),(0,a.bF)(c,{type:"primary",onClick:n.submitForm},{default:(0,a.k6)((()=>t[12]||(t[12]=[(0,a.eW)("确 定")]))),_:1},8,["onClick"])])])),_:1},8,["title","visible"])])),_:1})])}const r={name:"JobCategory",data(){return{loading:!1,open:!1,title:"",categoryList:[],categoryOptions:[],form:{id:void 0,parentId:void 0,name:void 0,code:void 0,sort:0,status:"1"},rules:{name:[{required:!0,message:"分类名称不能为空",trigger:"blur"}],code:[{required:!0,message:"分类编码不能为空",trigger:"blur"}],sort:[{required:!0,message:"显示顺序不能为空",trigger:"blur"}]}}},created(){this.getList()},methods:{getList(){this.loading=!0,setTimeout((()=>{this.categoryList=[{id:1,name:"驿站运营",code:"YZ_YY",sort:1,status:"1",createTime:"2023-01-10 10:00:00",children:[{id:11,name:"驿站店长",code:"YZ_DZ",sort:1,status:"1",createTime:"2023-01-10 10:10:00"},{id:12,name:"驿站营业员",code:"YZ_YYY",sort:2,status:"1",createTime:"2023-01-10 10:20:00"}]},{id:2,name:"快递配送",code:"KD_PS",sort:2,status:"1",createTime:"2023-01-10 11:00:00",children:[{id:21,name:"配送员",code:"KD_PSY",sort:1,status:"1",createTime:"2023-01-10 11:10:00"},{id:22,name:"分拣员",code:"KD_FJY",sort:2,status:"1",createTime:"2023-01-10 11:20:00"}]}],this.categoryOptions=this.handleTree(this.categoryList,"id"),this.loading=!1}),500)},handleTree(e,t){return e.map((e=>(e.children&&(e.children=this.handleTree(e.children,t)),e)))},handleAdd(e){this.reset(),e&&e.id&&(this.form.parentId=e.id),this.open=!0,this.title="添加职位分类"},handleUpdate(e){this.reset(),this.form={...e},this.open=!0,this.title="修改职位分类"},handleDelete(e){this.$confirm('是否确认删除名称为"'+e.name+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$message({type:"success",message:"删除成功"}),this.getList()})).catch((()=>{}))},reset(){this.form={id:void 0,parentId:void 0,name:void 0,code:void 0,sort:0,status:"1"},this.resetForm("form")},submitForm(){this.$refs["form"].validate((e=>{e&&(this.$message({type:"success",message:"保存成功"}),this.open=!1,this.getList())}))},cancel(){this.open=!1,this.reset()}}};var n=l(1241);const c=(0,n.A)(r,[["render",s],["__scopeId","data-v-b97b17c4"]]),u=c}}]);