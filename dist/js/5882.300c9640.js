"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[5882],{5882:(e,t,a)=>{a.r(t),a.d(t,{default:()=>b});var l=a(6768),i=a(4232);const r={class:"page-container"},o={class:"action-container"},n={class:"search-box"},s={class:"button-group"},d={class:"pagination-container"},u={class:"dialog-footer"};function p(e,t,a,p,c,h){const m=(0,l.g2)("el-input"),b=(0,l.g2)("el-option"),g=(0,l.g2)("el-select"),y=(0,l.g2)("el-date-picker"),k=(0,l.g2)("el-button"),w=(0,l.g2)("el-table-column"),F=(0,l.g2)("el-tag"),f=(0,l.g2)("el-table"),v=(0,l.g2)("el-tab-pane"),C=(0,l.g2)("el-tabs"),_=(0,l.g2)("el-pagination"),T=(0,l.g2)("el-form-item"),j=(0,l.g2)("el-form"),V=(0,l.g2)("el-dialog"),q=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",r,[(0,l.Lk)("div",o,[(0,l.Lk)("div",n,[(0,l.bF)(m,{modelValue:c.queryParams.keyword,"onUpdate:modelValue":t[0]||(t[0]=e=>c.queryParams.keyword=e),placeholder:"请输入内容标题/发布人",clearable:"",style:{width:"200px"},class:"mr-10"},null,8,["modelValue"]),(0,l.bF)(g,{modelValue:c.queryParams.type,"onUpdate:modelValue":t[1]||(t[1]=e=>c.queryParams.type=e),placeholder:"内容类型",clearable:"",class:"mr-10",style:{width:"120px"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(c.typeOptions,(e=>((0,l.uX)(),(0,l.Wv)(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),(0,l.bF)(y,{modelValue:c.dateRange,"onUpdate:modelValue":t[2]||(t[2]=e=>c.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",class:"mr-10",style:{width:"240px"}},null,8,["modelValue"]),(0,l.bF)(k,{type:"primary",onClick:h.handleQuery},{default:(0,l.k6)((()=>t[10]||(t[10]=[(0,l.eW)("搜索")]))),_:1},8,["onClick"]),(0,l.bF)(k,{onClick:h.resetQuery},{default:(0,l.k6)((()=>t[11]||(t[11]=[(0,l.eW)("重置")]))),_:1},8,["onClick"])]),(0,l.Lk)("div",s,[(0,l.bF)(k,{type:"success",onClick:h.handleBatchApprove},{default:(0,l.k6)((()=>t[12]||(t[12]=[(0,l.eW)("批量通过")]))),_:1},8,["onClick"]),(0,l.bF)(k,{type:"danger",onClick:h.handleBatchReject},{default:(0,l.k6)((()=>t[13]||(t[13]=[(0,l.eW)("批量拒绝")]))),_:1},8,["onClick"])])]),(0,l.bF)(C,{modelValue:c.activeName,"onUpdate:modelValue":t[3]||(t[3]=e=>c.activeName=e),onTabClick:h.handleTabClick},{default:(0,l.k6)((()=>[(0,l.bF)(v,{label:"待审核内容",name:"pending"},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(f,{data:c.contentList,onSelectionChange:h.handleSelectionChange},{default:(0,l.k6)((()=>[(0,l.bF)(w,{type:"selection",width:"55"}),(0,l.bF)(w,{type:"index",label:"#",width:"50"}),(0,l.bF)(w,{prop:"title",label:"内容标题","min-width":"160","show-overflow-tooltip":""}),(0,l.bF)(w,{prop:"contentType",label:"内容类型",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(F,{type:h.getTypeTag(e.row.contentType)},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(c.contentTypeMap[e.row.contentType]),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(w,{prop:"publisher",label:"发布人",width:"120"}),(0,l.bF)(w,{prop:"createTime",label:"发布时间",width:"160",sortable:""}),(0,l.bF)(w,{prop:"priority",label:"优先级",width:"80"},{default:(0,l.k6)((e=>[(0,l.bF)(F,{type:"high"===e.row.priority?"danger":"medium"===e.row.priority?"warning":"info"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(c.priorityMap[e.row.priority]),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(w,{label:"操作",width:"240",fixed:"right"},{default:(0,l.k6)((e=>[(0,l.bF)(k,{type:"text",onClick:t=>h.handleDetail(e.row)},{default:(0,l.k6)((()=>t[14]||(t[14]=[(0,l.eW)("查看详情")]))),_:2},1032,["onClick"]),(0,l.bF)(k,{type:"text",onClick:t=>h.handleApprove(e.row)},{default:(0,l.k6)((()=>t[15]||(t[15]=[(0,l.eW)("通过")]))),_:2},1032,["onClick"]),(0,l.bF)(k,{type:"text",onClick:t=>h.handleReject(e.row)},{default:(0,l.k6)((()=>t[16]||(t[16]=[(0,l.eW)("拒绝")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data","onSelectionChange"])),[[q,c.loading]])])),_:1}),(0,l.bF)(v,{label:"审核历史",name:"history"},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(f,{data:c.historyList},{default:(0,l.k6)((()=>[(0,l.bF)(w,{type:"index",label:"#",width:"50"}),(0,l.bF)(w,{prop:"title",label:"内容标题","min-width":"160","show-overflow-tooltip":""}),(0,l.bF)(w,{prop:"contentType",label:"内容类型",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(F,{type:h.getTypeTag(e.row.contentType)},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(c.contentTypeMap[e.row.contentType]),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(w,{prop:"publisher",label:"发布人",width:"100"}),(0,l.bF)(w,{prop:"auditStatus",label:"审核结果",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(F,{type:"approved"===e.row.auditStatus?"success":"danger"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)("approved"===e.row.auditStatus?"通过":"拒绝"),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(w,{prop:"auditTime",label:"审核时间",width:"160",sortable:""}),(0,l.bF)(w,{prop:"auditor",label:"审核人",width:"100"}),(0,l.bF)(w,{prop:"reason",label:"拒绝理由","min-width":"150","show-overflow-tooltip":""},{default:(0,l.k6)((e=>[(0,l.eW)((0,i.v_)("rejected"===e.row.auditStatus?e.row.reason:"-"),1)])),_:1}),(0,l.bF)(w,{label:"操作",width:"120",fixed:"right"},{default:(0,l.k6)((e=>[(0,l.bF)(k,{type:"text",onClick:t=>h.handleDetail(e.row)},{default:(0,l.k6)((()=>t[17]||(t[17]=[(0,l.eW)("查看详情")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[q,c.historyLoading]])])),_:1})])),_:1},8,["modelValue","onTabClick"]),(0,l.Lk)("div",d,[(0,l.bF)(_,{"current-page":c.queryParams.pageNum,"onUpdate:currentPage":t[4]||(t[4]=e=>c.queryParams.pageNum=e),"page-size":c.queryParams.pageSize,"onUpdate:pageSize":t[5]||(t[5]=e=>c.queryParams.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:c.total,onSizeChange:h.handleSizeChange,onCurrentChange:h.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])]),(0,l.bF)(V,{title:"拒绝理由",modelValue:c.rejectDialogVisible,"onUpdate:modelValue":t[9]||(t[9]=e=>c.rejectDialogVisible=e),width:"500px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",u,[(0,l.bF)(k,{onClick:t[8]||(t[8]=e=>c.rejectDialogVisible=!1)},{default:(0,l.k6)((()=>t[18]||(t[18]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(k,{type:"primary",onClick:h.submitReject},{default:(0,l.k6)((()=>t[19]||(t[19]=[(0,l.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bF)(j,{ref:"rejectForm",model:c.rejectForm,"label-width":"80px"},{default:(0,l.k6)((()=>[(0,l.bF)(T,{label:"拒绝理由",prop:"reason"},{default:(0,l.k6)((()=>[(0,l.bF)(g,{modelValue:c.rejectForm.reason,"onUpdate:modelValue":t[6]||(t[6]=e=>c.rejectForm.reason=e),placeholder:"请选择拒绝理由",style:{width:"100%"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(c.reasonOptions,(e=>((0,l.uX)(),(0,l.Wv)(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(T,{label:"补充说明",prop:"remark"},{default:(0,l.k6)((()=>[(0,l.bF)(m,{modelValue:c.rejectForm.remark,"onUpdate:modelValue":t[7]||(t[7]=e=>c.rejectForm.remark=e),type:"textarea",placeholder:"请输入补充说明"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}const c={name:"ContentAudit",data(){return{activeName:"pending",loading:!1,historyLoading:!1,selectedIds:[],total:0,dateRange:[],contentTypeMap:{transfer:"驿站转让",equipment:"设备交易",recruitment:"招聘求职",substitution:"顶班服务"},priorityMap:{high:"高",medium:"中",low:"低"},typeOptions:[{label:"驿站转让",value:"transfer"},{label:"设备交易",value:"equipment"},{label:"招聘求职",value:"recruitment"},{label:"顶班服务",value:"substitution"}],reasonOptions:[{label:"内容不真实",value:"内容不真实"},{label:"信息不完整",value:"信息不完整"},{label:"违反平台规则",value:"违反平台规则"},{label:"图片不清晰",value:"图片不清晰"},{label:"联系方式错误",value:"联系方式错误"},{label:"其他原因",value:"其他原因"}],contentList:[{id:1,title:"北京市海淀区中关村驿站转让",contentType:"transfer",publisher:"张三",createTime:"2023-04-15 10:30:00",priority:"high"},{id:2,title:"二手快递柜设备出售",contentType:"equipment",publisher:"李四",createTime:"2023-04-15 15:20:00",priority:"medium"},{id:3,title:"招聘驿站兼职人员",contentType:"recruitment",publisher:"王五",createTime:"2023-04-16 09:15:00",priority:"low"},{id:4,title:"周末顶班服务",contentType:"substitution",publisher:"赵六",createTime:"2023-04-16 11:45:00",priority:"medium"}],historyList:[{id:5,title:"朝阳区望京SOHO驿站转让",contentType:"transfer",publisher:"张三",auditStatus:"approved",auditTime:"2023-04-14 14:25:00",auditor:"admin"},{id:6,title:"快递分拣机低价出售",contentType:"equipment",publisher:"李四",auditStatus:"rejected",auditTime:"2023-04-14 16:10:00",auditor:"admin",reason:"图片不清晰"}],queryParams:{pageNum:1,pageSize:10,keyword:"",type:"",startDate:"",endDate:""},rejectForm:{id:void 0,reason:"",remark:""},rejectDialogVisible:!1,currentContent:{}}},methods:{getTypeTag(e){const t={transfer:"primary",equipment:"success",recruitment:"warning",substitution:"info"};return t[e]||""},handleQuery(){this.queryParams.pageNum=1,this.dateRange&&this.dateRange.length>0?(this.queryParams.startDate=this.dateRange[0],this.queryParams.endDate=this.dateRange[1]):(this.queryParams.startDate="",this.queryParams.endDate=""),console.log("查询参数：",this.queryParams)},resetQuery(){this.dateRange=[],this.queryParams={pageNum:1,pageSize:10,keyword:"",type:"",startDate:"",endDate:""},this.handleQuery()},handleTabClick(){"history"===this.activeName?(this.historyLoading=!0,setTimeout((()=>{this.historyLoading=!1}),500)):(this.loading=!0,setTimeout((()=>{this.loading=!1}),500))},handleSelectionChange(e){this.selectedIds=e.map((e=>e.id))},handleDetail(e){this.$router.push(`/content/audit/detail/${e.id}/${e.contentType}`)},handleApprove(e){this.$confirm(`确认通过内容"${e.title}"的审核?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{console.log("通过内容ID：",e.id),this.$message({type:"success",message:"审核通过成功!"})})).catch((()=>{}))},handleReject(e){this.currentContent=e,this.rejectForm={id:e.id,reason:"",remark:""},this.rejectDialogVisible=!0},submitReject(){this.rejectForm.reason?(console.log("拒绝内容ID：",this.rejectForm.id),console.log("拒绝理由：",this.rejectForm.reason),console.log("补充说明：",this.rejectForm.remark),this.rejectDialogVisible=!1,this.$message({type:"success",message:"审核拒绝成功!"})):this.$message({type:"warning",message:"请选择拒绝理由"})},handleBatchApprove(){0!==this.selectedIds.length?this.$confirm(`确认批量通过所选的${this.selectedIds.length}条内容?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{console.log("批量通过内容IDs：",this.selectedIds),this.$message({type:"success",message:"批量审核通过成功!"})})).catch((()=>{})):this.$message({type:"warning",message:"请至少选择一条记录"})},handleBatchReject(){0!==this.selectedIds.length?(this.rejectForm={id:this.selectedIds.join(","),reason:"",remark:""},this.rejectDialogVisible=!0):this.$message({type:"warning",message:"请至少选择一条记录"})},handleSizeChange(e){this.queryParams.pageSize=e,this.handleQuery()},handleCurrentChange(e){this.queryParams.pageNum=e,this.handleQuery()}}};var h=a(1241);const m=(0,h.A)(c,[["render",p],["__scopeId","data-v-040f09b0"]]),b=m}}]);