"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[2089],{2089:(e,a,t)=>{t.r(a),t.d(a,{default:()=>U});var l=t(6768),o=t(4232);const i={class:"page-container"},s={class:"action-container"},r={class:"search-box"},d={class:"button-group"},n={class:"pagination-container"},u={class:"monitor-container"},p={class:"monitor-header"},m={class:"refresh-area"},c={class:"monitor-stats"},b={class:"stat-card primary"},k={class:"stat-info"},h={class:"stat-value"},F={class:"stat-card success"},g={class:"stat-info"},v={class:"stat-value"},f={class:"stat-card warning"},y={class:"stat-info"},_={class:"stat-value"},V={class:"stat-card danger"},w={class:"stat-info"},T={class:"stat-value"},C={class:"monitor-alert"},L={class:"dialog-footer"},S={class:"detail-charts mt-20"},W={class:"dialog-footer"};function N(e,a,t,N,x,q){const P=(0,l.g2)("el-input"),U=(0,l.g2)("el-option"),z=(0,l.g2)("el-select"),D=(0,l.g2)("el-button"),$=(0,l.g2)("el-table-column"),Q=(0,l.g2)("el-tag"),A=(0,l.g2)("el-table"),M=(0,l.g2)("el-pagination"),O=(0,l.g2)("el-tab-pane"),E=(0,l.g2)("el-tabs"),X=(0,l.g2)("el-form-item"),B=(0,l.g2)("el-col"),H=(0,l.g2)("el-row"),I=(0,l.g2)("el-time-picker"),R=(0,l.g2)("el-radio"),j=(0,l.g2)("el-radio-group"),G=(0,l.g2)("el-form"),J=(0,l.g2)("el-dialog"),K=(0,l.g2)("el-descriptions-item"),Y=(0,l.g2)("el-descriptions"),Z=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",i,[(0,l.Lk)("div",s,[(0,l.Lk)("div",r,[(0,l.bF)(P,{modelValue:x.queryParams.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>x.queryParams.keyword=e),placeholder:"请输入站点名称/负责人",clearable:"",style:{width:"200px"},class:"mr-10"},null,8,["modelValue"]),(0,l.bF)(z,{modelValue:x.queryParams.status,"onUpdate:modelValue":a[1]||(a[1]=e=>x.queryParams.status=e),placeholder:"站点状态",clearable:"",class:"mr-10",style:{width:"120px"}},{default:(0,l.k6)((()=>[(0,l.bF)(U,{label:"运营中",value:"1"}),(0,l.bF)(U,{label:"已关闭",value:"0"}),(0,l.bF)(U,{label:"待审核",value:"2"})])),_:1},8,["modelValue"]),(0,l.bF)(z,{modelValue:x.queryParams.type,"onUpdate:modelValue":a[2]||(a[2]=e=>x.queryParams.type=e),placeholder:"站点类型",clearable:"",class:"mr-10",style:{width:"120px"}},{default:(0,l.k6)((()=>[(0,l.bF)(U,{label:"快递驿站",value:"1"}),(0,l.bF)(U,{label:"社区驿站",value:"2"}),(0,l.bF)(U,{label:"校园驿站",value:"3"}),(0,l.bF)(U,{label:"写字楼驿站",value:"4"})])),_:1},8,["modelValue"]),(0,l.bF)(D,{type:"primary",onClick:q.handleQuery},{default:(0,l.k6)((()=>a[24]||(a[24]=[(0,l.eW)("搜索")]))),_:1},8,["onClick"]),(0,l.bF)(D,{onClick:q.resetQuery},{default:(0,l.k6)((()=>a[25]||(a[25]=[(0,l.eW)("重置")]))),_:1},8,["onClick"])]),(0,l.Lk)("div",d,[(0,l.bF)(D,{type:"primary",onClick:q.handleAdd},{default:(0,l.k6)((()=>a[26]||(a[26]=[(0,l.eW)("新增站点")]))),_:1},8,["onClick"]),(0,l.bF)(D,{type:"success",onClick:q.handleExport},{default:(0,l.k6)((()=>a[27]||(a[27]=[(0,l.eW)("导出")]))),_:1},8,["onClick"])])]),(0,l.bF)(E,{modelValue:x.activeName,"onUpdate:modelValue":a[5]||(a[5]=e=>x.activeName=e),onTabClick:q.handleTabClick},{default:(0,l.k6)((()=>[(0,l.bF)(O,{label:"站点列表",name:"list"},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(A,{data:x.stationList},{default:(0,l.k6)((()=>[(0,l.bF)($,{type:"index",label:"#",width:"50"}),(0,l.bF)($,{prop:"stationName",label:"站点名称","min-width":"120","show-overflow-tooltip":""}),(0,l.bF)($,{prop:"stationType",label:"站点类型",width:"100"},{default:(0,l.k6)((e=>[(0,l.eW)((0,o.v_)(x.stationTypeMap[e.row.stationType]),1)])),_:1}),(0,l.bF)($,{prop:"address",label:"地址","min-width":"180","show-overflow-tooltip":""}),(0,l.bF)($,{prop:"managerName",label:"负责人",width:"100"}),(0,l.bF)($,{prop:"phone",label:"联系电话",width:"120"}),(0,l.bF)($,{prop:"status",label:"状态",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(Q,{type:"1"===e.row.status?"success":"2"===e.row.status?"warning":"info"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(x.statusMap[e.row.status]),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)($,{prop:"orderCount",label:"订单数",width:"80",sortable:""}),(0,l.bF)($,{prop:"createTime",label:"创建时间",width:"160",sortable:""}),(0,l.bF)($,{label:"操作",width:"260",fixed:"right"},{default:(0,l.k6)((e=>[(0,l.bF)(D,{type:"text",onClick:a=>q.handleEdit(e.row)},{default:(0,l.k6)((()=>a[28]||(a[28]=[(0,l.eW)("编辑")]))),_:2},1032,["onClick"]),(0,l.bF)(D,{type:"text",onClick:a=>q.handleDetail(e.row)},{default:(0,l.k6)((()=>a[29]||(a[29]=[(0,l.eW)("详情")]))),_:2},1032,["onClick"]),"2"===e.row.status?((0,l.uX)(),(0,l.Wv)(D,{key:0,type:"text",onClick:a=>q.handleApprove(e.row)},{default:(0,l.k6)((()=>a[30]||(a[30]=[(0,l.eW)("审核")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),"1"===e.row.status?((0,l.uX)(),(0,l.Wv)(D,{key:1,type:"text",onClick:a=>q.handleStatus(e.row,"0")},{default:(0,l.k6)((()=>a[31]||(a[31]=[(0,l.eW)("关闭")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),"0"===e.row.status?((0,l.uX)(),(0,l.Wv)(D,{key:2,type:"text",onClick:a=>q.handleStatus(e.row,"1")},{default:(0,l.k6)((()=>a[32]||(a[32]=[(0,l.eW)("开启")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),(0,l.bF)(D,{type:"text",onClick:a=>q.handleDelete(e.row)},{default:(0,l.k6)((()=>a[33]||(a[33]=[(0,l.eW)("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Z,x.loading]]),(0,l.Lk)("div",n,[(0,l.bF)(M,{"current-page":x.queryParams.pageNum,"onUpdate:currentPage":a[3]||(a[3]=e=>x.queryParams.pageNum=e),"page-size":x.queryParams.pageSize,"onUpdate:pageSize":a[4]||(a[4]=e=>x.queryParams.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:x.total,onSizeChange:q.handleSizeChange,onCurrentChange:q.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])])),_:1}),(0,l.bF)(O,{label:"站点监控",name:"monitor"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",u,[(0,l.Lk)("div",p,[a[35]||(a[35]=(0,l.Lk)("h3",null,"站点状态监控",-1)),(0,l.Lk)("div",m,[(0,l.Lk)("span",null,"上次刷新时间："+(0,o.v_)(x.refreshTime),1),(0,l.bF)(D,{type:"primary",size:"small",onClick:q.refreshMonitor},{default:(0,l.k6)((()=>a[34]||(a[34]=[(0,l.eW)("刷新")]))),_:1},8,["onClick"])])]),(0,l.Lk)("div",c,[(0,l.Lk)("div",b,[a[37]||(a[37]=(0,l.Lk)("div",{class:"stat-icon"},[(0,l.Lk)("i",{class:"el-icon-office-building"})],-1)),(0,l.Lk)("div",k,[a[36]||(a[36]=(0,l.Lk)("div",{class:"stat-title"},"站点总数",-1)),(0,l.Lk)("div",h,(0,o.v_)(x.monitorData.total),1)])]),(0,l.Lk)("div",F,[a[39]||(a[39]=(0,l.Lk)("div",{class:"stat-icon"},[(0,l.Lk)("i",{class:"el-icon-circle-check"})],-1)),(0,l.Lk)("div",g,[a[38]||(a[38]=(0,l.Lk)("div",{class:"stat-title"},"运营中",-1)),(0,l.Lk)("div",v,(0,o.v_)(x.monitorData.active),1)])]),(0,l.Lk)("div",f,[a[41]||(a[41]=(0,l.Lk)("div",{class:"stat-icon"},[(0,l.Lk)("i",{class:"el-icon-time"})],-1)),(0,l.Lk)("div",y,[a[40]||(a[40]=(0,l.Lk)("div",{class:"stat-title"},"待审核",-1)),(0,l.Lk)("div",_,(0,o.v_)(x.monitorData.pending),1)])]),(0,l.Lk)("div",V,[a[43]||(a[43]=(0,l.Lk)("div",{class:"stat-icon"},[(0,l.Lk)("i",{class:"el-icon-circle-close"})],-1)),(0,l.Lk)("div",w,[a[42]||(a[42]=(0,l.Lk)("div",{class:"stat-title"},"已关闭",-1)),(0,l.Lk)("div",T,(0,o.v_)(x.monitorData.closed),1)])])]),a[47]||(a[47]=(0,l.Lk)("div",{class:"monitor-map"},[(0,l.Lk)("h4",null,"站点地理分布"),(0,l.Lk)("div",{class:"map-placeholder"},"地图组件将在此显示 - 需要集成地图组件如高德地图或百度地图")],-1)),(0,l.Lk)("div",C,[a[46]||(a[46]=(0,l.Lk)("h4",null,"异常站点列表",-1)),(0,l.bF)(A,{data:x.alertStations,style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)($,{type:"index",label:"#",width:"50"}),(0,l.bF)($,{prop:"stationName",label:"站点名称","min-width":"120"}),(0,l.bF)($,{prop:"alertType",label:"异常类型"},{default:(0,l.k6)((e=>[(0,l.bF)(Q,{type:"danger"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(e.row.alertType),1)])),_:2},1024)])),_:1}),(0,l.bF)($,{prop:"alertTime",label:"异常时间",width:"160"}),(0,l.bF)($,{prop:"duration",label:"持续时间",width:"120"}),(0,l.bF)($,{label:"操作",width:"150"},{default:(0,l.k6)((e=>[(0,l.bF)(D,{type:"text",onClick:a=>q.handleAlertDetail(e.row)},{default:(0,l.k6)((()=>a[44]||(a[44]=[(0,l.eW)("查看详情")]))),_:2},1032,["onClick"]),(0,l.bF)(D,{type:"text",onClick:a=>q.handleResolve(e.row)},{default:(0,l.k6)((()=>a[45]||(a[45]=[(0,l.eW)("标记解决")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])])])),_:1})])),_:1},8,["modelValue","onTabClick"]),(0,l.bF)(J,{title:x.dialogTitle,modelValue:x.dialogVisible,"onUpdate:modelValue":a[18]||(a[18]=e=>x.dialogVisible=e),width:"700px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",L,[(0,l.bF)(D,{onClick:a[17]||(a[17]=e=>x.dialogVisible=!1)},{default:(0,l.k6)((()=>a[52]||(a[52]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(D,{type:"primary",onClick:q.submitForm},{default:(0,l.k6)((()=>a[53]||(a[53]=[(0,l.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bF)(G,{ref:"stationForm",model:x.stationForm,rules:x.rules,"label-width":"100px"},{default:(0,l.k6)((()=>[(0,l.bF)(H,{gutter:20},{default:(0,l.k6)((()=>[(0,l.bF)(B,{span:12},{default:(0,l.k6)((()=>[(0,l.bF)(X,{label:"站点名称",prop:"stationName"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{modelValue:x.stationForm.stationName,"onUpdate:modelValue":a[6]||(a[6]=e=>x.stationForm.stationName=e),placeholder:"请输入站点名称"},null,8,["modelValue"])])),_:1})])),_:1}),(0,l.bF)(B,{span:12},{default:(0,l.k6)((()=>[(0,l.bF)(X,{label:"站点类型",prop:"stationType"},{default:(0,l.k6)((()=>[(0,l.bF)(z,{modelValue:x.stationForm.stationType,"onUpdate:modelValue":a[7]||(a[7]=e=>x.stationForm.stationType=e),placeholder:"请选择站点类型",style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(U,{label:"快递驿站",value:"1"}),(0,l.bF)(U,{label:"社区驿站",value:"2"}),(0,l.bF)(U,{label:"校园驿站",value:"3"}),(0,l.bF)(U,{label:"写字楼驿站",value:"4"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),(0,l.bF)(H,{gutter:20},{default:(0,l.k6)((()=>[(0,l.bF)(B,{span:12},{default:(0,l.k6)((()=>[(0,l.bF)(X,{label:"负责人",prop:"managerName"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{modelValue:x.stationForm.managerName,"onUpdate:modelValue":a[8]||(a[8]=e=>x.stationForm.managerName=e),placeholder:"请输入负责人姓名"},null,8,["modelValue"])])),_:1})])),_:1}),(0,l.bF)(B,{span:12},{default:(0,l.k6)((()=>[(0,l.bF)(X,{label:"联系电话",prop:"phone"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{modelValue:x.stationForm.phone,"onUpdate:modelValue":a[9]||(a[9]=e=>x.stationForm.phone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),(0,l.bF)(X,{label:"地址",prop:"address"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{modelValue:x.stationForm.address,"onUpdate:modelValue":a[10]||(a[10]=e=>x.stationForm.address=e),placeholder:"请输入详细地址"},null,8,["modelValue"])])),_:1}),(0,l.bF)(H,{gutter:20},{default:(0,l.k6)((()=>[(0,l.bF)(B,{span:12},{default:(0,l.k6)((()=>[(0,l.bF)(X,{label:"经度",prop:"longitude"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{modelValue:x.stationForm.longitude,"onUpdate:modelValue":a[11]||(a[11]=e=>x.stationForm.longitude=e),placeholder:"请输入经度"},null,8,["modelValue"])])),_:1})])),_:1}),(0,l.bF)(B,{span:12},{default:(0,l.k6)((()=>[(0,l.bF)(X,{label:"纬度",prop:"latitude"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{modelValue:x.stationForm.latitude,"onUpdate:modelValue":a[12]||(a[12]=e=>x.stationForm.latitude=e),placeholder:"请输入纬度"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),(0,l.bF)(X,{label:"营业时间",required:""},{default:(0,l.k6)((()=>[(0,l.bF)(B,{span:11},{default:(0,l.k6)((()=>[(0,l.bF)(X,{prop:"openTime"},{default:(0,l.k6)((()=>[(0,l.bF)(I,{modelValue:x.stationForm.openTime,"onUpdate:modelValue":a[13]||(a[13]=e=>x.stationForm.openTime=e),placeholder:"开始时间",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),(0,l.bF)(B,{span:2,class:"text-center"},{default:(0,l.k6)((()=>a[48]||(a[48]=[(0,l.eW)("至")]))),_:1}),(0,l.bF)(B,{span:11},{default:(0,l.k6)((()=>[(0,l.bF)(X,{prop:"closeTime"},{default:(0,l.k6)((()=>[(0,l.bF)(I,{modelValue:x.stationForm.closeTime,"onUpdate:modelValue":a[14]||(a[14]=e=>x.stationForm.closeTime=e),placeholder:"结束时间",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),(0,l.bF)(X,{label:"状态",prop:"status"},{default:(0,l.k6)((()=>[(0,l.bF)(j,{modelValue:x.stationForm.status,"onUpdate:modelValue":a[15]||(a[15]=e=>x.stationForm.status=e)},{default:(0,l.k6)((()=>[(0,l.bF)(R,{label:"1"},{default:(0,l.k6)((()=>a[49]||(a[49]=[(0,l.eW)("运营中")]))),_:1}),(0,l.bF)(R,{label:"0"},{default:(0,l.k6)((()=>a[50]||(a[50]=[(0,l.eW)("已关闭")]))),_:1}),(0,l.bF)(R,{label:"2"},{default:(0,l.k6)((()=>a[51]||(a[51]=[(0,l.eW)("待审核")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(X,{label:"备注",prop:"remark"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{modelValue:x.stationForm.remark,"onUpdate:modelValue":a[16]||(a[16]=e=>x.stationForm.remark=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"]),(0,l.bF)(J,{title:"站点详情",modelValue:x.detailVisible,"onUpdate:modelValue":a[19]||(a[19]=e=>x.detailVisible=e),width:"800px","append-to-body":""},{default:(0,l.k6)((()=>[(0,l.bF)(Y,{column:2,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(K,{label:"站点名称"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(x.currentStation.stationName),1)])),_:1}),(0,l.bF)(K,{label:"站点类型"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(x.stationTypeMap[x.currentStation.stationType]),1)])),_:1}),(0,l.bF)(K,{label:"负责人"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(x.currentStation.managerName),1)])),_:1}),(0,l.bF)(K,{label:"联系电话"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(x.currentStation.phone),1)])),_:1}),(0,l.bF)(K,{label:"地址",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(x.currentStation.address),1)])),_:1}),(0,l.bF)(K,{label:"营业时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(x.currentStation.openTime)+" - "+(0,o.v_)(x.currentStation.closeTime),1)])),_:1}),(0,l.bF)(K,{label:"状态"},{default:(0,l.k6)((()=>[(0,l.bF)(Q,{type:"1"===x.currentStation.status?"success":"2"===x.currentStation.status?"warning":"info"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(x.statusMap[x.currentStation.status]),1)])),_:1},8,["type"])])),_:1}),(0,l.bF)(K,{label:"订单总数"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(x.currentStation.orderCount),1)])),_:1}),(0,l.bF)(K,{label:"创建时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(x.currentStation.createTime),1)])),_:1}),(0,l.bF)(K,{label:"备注",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,o.v_)(x.currentStation.remark),1)])),_:1})])),_:1}),(0,l.Lk)("div",S,[(0,l.bF)(E,null,{default:(0,l.k6)((()=>[(0,l.bF)(O,{label:"订单统计"},{default:(0,l.k6)((()=>a[54]||(a[54]=[(0,l.Lk)("div",{class:"chart-placeholder"},"订单统计图表",-1)]))),_:1}),(0,l.bF)(O,{label:"收入统计"},{default:(0,l.k6)((()=>a[55]||(a[55]=[(0,l.Lk)("div",{class:"chart-placeholder"},"收入统计图表",-1)]))),_:1})])),_:1})])])),_:1},8,["modelValue"]),(0,l.bF)(J,{title:"站点审核",modelValue:x.approveVisible,"onUpdate:modelValue":a[23]||(a[23]=e=>x.approveVisible=e),width:"500px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",W,[(0,l.bF)(D,{onClick:a[22]||(a[22]=e=>x.approveVisible=!1)},{default:(0,l.k6)((()=>a[58]||(a[58]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(D,{type:"primary",onClick:q.submitApprove},{default:(0,l.k6)((()=>a[59]||(a[59]=[(0,l.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bF)(G,{ref:"approveForm",model:x.approveForm,"label-width":"100px"},{default:(0,l.k6)((()=>[(0,l.bF)(X,{label:"审核结果",prop:"approveStatus"},{default:(0,l.k6)((()=>[(0,l.bF)(j,{modelValue:x.approveForm.approveStatus,"onUpdate:modelValue":a[20]||(a[20]=e=>x.approveForm.approveStatus=e)},{default:(0,l.k6)((()=>[(0,l.bF)(R,{label:"1"},{default:(0,l.k6)((()=>a[56]||(a[56]=[(0,l.eW)("通过")]))),_:1}),(0,l.bF)(R,{label:"0"},{default:(0,l.k6)((()=>a[57]||(a[57]=[(0,l.eW)("拒绝")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(X,{label:"审核意见",prop:"approveComment"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{modelValue:x.approveForm.approveComment,"onUpdate:modelValue":a[21]||(a[21]=e=>x.approveForm.approveComment=e),type:"textarea",placeholder:"请输入审核意见"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}const x={name:"StationList",data(){return{activeName:"list",loading:!1,total:0,refreshTime:"2023-04-16 12:34:56",stationTypeMap:{1:"快递驿站",2:"社区驿站",3:"校园驿站",4:"写字楼驿站"},statusMap:{0:"已关闭",1:"运营中",2:"待审核"},stationList:[{id:1,stationName:"海淀区中关村社区驿站",stationType:"2",managerName:"张三",phone:"13800138001",address:"北京市海淀区中关村大街123号",longitude:"116.3252",latitude:"39.9841",openTime:"08:00",closeTime:"22:00",status:"1",orderCount:256,createTime:"2023-01-01 10:00:00",remark:"中关村科技园区内的驿站"},{id:2,stationName:"北京大学校内驿站",stationType:"3",managerName:"李四",phone:"13800138002",address:"北京市海淀区颐和园路5号",longitude:"116.3045",latitude:"39.9869",openTime:"08:00",closeTime:"21:00",status:"1",orderCount:198,createTime:"2023-01-02 09:30:00",remark:"北京大学校内驿站，主要服务校内师生"},{id:3,stationName:"望京SOHO快递驿站",stationType:"1",managerName:"王五",phone:"13800138003",address:"北京市朝阳区望京SOHO T1",longitude:"116.4825",latitude:"39.9946",openTime:"07:30",closeTime:"22:30",status:"2",orderCount:0,createTime:"2023-01-05 15:20:00",remark:"望京SOHO写字楼快递服务驿站"}],alertStations:[{id:1,stationName:"海淀区中关村社区驿站",alertType:"长时间无订单",alertTime:"2023-04-16 10:30:00",duration:"2小时30分钟"},{id:3,stationName:"望京SOHO快递驿站",alertType:"审核超时",alertTime:"2023-04-15 14:20:00",duration:"1天2小时"}],monitorData:{total:358,active:312,pending:25,closed:21},dialogTitle:"",dialogVisible:!1,detailVisible:!1,approveVisible:!1,currentStation:{},queryParams:{pageNum:1,pageSize:10,keyword:"",status:"",type:""},stationForm:{id:void 0,stationName:void 0,stationType:void 0,managerName:void 0,phone:void 0,address:void 0,longitude:void 0,latitude:void 0,openTime:void 0,closeTime:void 0,status:"1",remark:void 0},approveForm:{id:void 0,approveStatus:"1",approveComment:void 0},rules:{stationName:[{required:!0,message:"站点名称不能为空",trigger:"blur"}],stationType:[{required:!0,message:"站点类型不能为空",trigger:"change"}],managerName:[{required:!0,message:"负责人不能为空",trigger:"blur"}],phone:[{required:!0,message:"联系电话不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],address:[{required:!0,message:"地址不能为空",trigger:"blur"}]}}},mounted(){this.getList(),this.getStationStats()},methods:{async getList(){this.loading=!0;try{const e={page:this.queryParams.pageNum,limit:this.queryParams.pageSize,keyword:this.queryParams.keyword,status:this.queryParams.status,type:this.queryParams.type},a=await this.$http.get("/station/list",{params:e});0===a.data.code?(this.stationList=a.data.data.list||[],this.total=a.data.data.total):this.$message.error(a.data.message||"获取数据失败")}catch(e){console.error("获取站点列表失败",e),this.$message.error("获取数据异常，请刷新页面重试")}finally{this.loading=!1}},handleQuery(){this.queryParams.pageNum=1,this.getList()},resetQuery(){this.queryParams.keyword="",this.queryParams.status="",this.queryParams.type="",this.handleQuery()},handleTabClick(){"monitor"===this.activeName&&this.refreshMonitor()},async getStationStats(){try{const e=await this.$http.get("/station/stats/overview");0===e.data.code&&(this.monitorData=e.data.data)}catch(e){console.error("获取驿站统计失败:",e)}},refreshMonitor(){this.getStationStats(),this.refreshTime=(new Date).toLocaleString(),console.log("刷新监控数据")},handleAdd(){this.dialogTitle="添加站点",this.dialogVisible=!0,this.resetForm()},handleEdit(e){this.dialogTitle="修改站点",this.dialogVisible=!0,this.stationForm={...e}},handleDetail(e){this.currentStation=e,this.detailVisible=!0},handleApprove(e){this.currentStation=e,this.approveForm.id=e.id,this.approveVisible=!0},handleStatus(e,a){const t="0"===a?"关闭":"开启";this.$confirm(`确认要${t}站点"${e.stationName}"吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{console.log(`${t}站点ID：`,e.id)})).catch((()=>{}))},handleDelete(e){this.$confirm(`是否确认删除站点"${e.stationName}"?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{t.e(2672).then(t.bind(t,2672)).then((a=>{a.testDeleteStation(e.id).then((e=>{e.success?(this.$message.success("删除成功"),this.getList()):this.$message.error(e.message||"删除失败")})).catch((e=>{console.error("删除站点失败",e),this.$message.error("删除操作异常，请重试")}))}))})).catch((()=>{}))},handleExport(){console.log("导出站点数据")},handleAlertDetail(e){console.log("查看告警详情：",e)},handleResolve(e){console.log("标记告警已解决：",e)},submitForm(){this.$refs.stationForm.validate((e=>{e&&(this.stationForm.id?console.log("更新站点：",this.stationForm):console.log("新增站点：",this.stationForm),this.dialogVisible=!1)}))},submitApprove(){console.log("审核结果：",this.approveForm),this.approveVisible=!1},resetForm(){this.stationForm={id:void 0,stationName:void 0,stationType:void 0,managerName:void 0,phone:void 0,address:void 0,longitude:void 0,latitude:void 0,openTime:void 0,closeTime:void 0,status:"1",remark:void 0}},handleSizeChange(e){this.queryParams.pageSize=e,this.handleQuery()},handleCurrentChange(e){this.queryParams.pageNum=e,this.handleQuery()}}};var q=t(1241);const P=(0,q.A)(x,[["render",N],["__scopeId","data-v-6cdcf70c"]]),U=P}}]);