"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[7677],{7677:(e,l,o)=>{o.r(l),o.d(l,{default:()=>b});var a=o(6768);const n={class:"login-container"},r={class:"login-box"},s={class:"form-container"},i={class:"form-item"},m={class:"form-item"},d={class:"form-item"},t={class:"form-item"};function u(e,l,o,u,c,p){const k=(0,a.g2)("el-input"),b=(0,a.g2)("el-checkbox"),g=(0,a.g2)("el-button");return(0,a.uX)(),(0,a.CE)("div",n,[(0,a.Lk)("div",r,[l[7]||(l[7]=(0,a.Lk)("div",{class:"login-title"},"驿站帮Pro管理系统",-1)),(0,a.Lk)("div",s,[(0,a.Lk)("div",i,[l[3]||(l[3]=(0,a.Lk)("label",null,"用户名",-1)),(0,a.bF)(k,{modelValue:c.loginForm.username,"onUpdate:modelValue":l[0]||(l[0]=e=>c.loginForm.username=e),placeholder:"请输入用户名","prefix-icon":"el-icon-user"},null,8,["modelValue"])]),(0,a.Lk)("div",m,[l[4]||(l[4]=(0,a.Lk)("label",null,"密码",-1)),(0,a.bF)(k,{modelValue:c.loginForm.password,"onUpdate:modelValue":l[1]||(l[1]=e=>c.loginForm.password=e),type:"password",placeholder:"请输入密码","prefix-icon":"el-icon-lock"},null,8,["modelValue"])]),(0,a.Lk)("div",d,[(0,a.bF)(b,{modelValue:c.loginForm.rememberMe,"onUpdate:modelValue":l[2]||(l[2]=e=>c.loginForm.rememberMe=e)},{default:(0,a.k6)((()=>l[5]||(l[5]=[(0,a.eW)("记住我")]))),_:1},8,["modelValue"])]),(0,a.Lk)("div",t,[(0,a.bF)(g,{type:"primary",class:"login-button",onClick:p.handleLogin},{default:(0,a.k6)((()=>l[6]||(l[6]=[(0,a.eW)("登录")]))),_:1},8,["onClick"])])])])])}const c={name:"Login",data(){return{loginForm:{username:"admin",password:"admin123",rememberMe:!1}}},methods:{handleLogin(){this.$router.push({path:"/"})}}};var p=o(1241);const k=(0,p.A)(c,[["render",u],["__scopeId","data-v-eefae7a4"]]),b=k}}]);