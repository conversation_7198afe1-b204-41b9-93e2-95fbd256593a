"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[2709],{1981:(e,t,a)=>{function l(e,t="YYYY-MM-DD HH:mm:ss"){if(!e)return"";let a;"object"===typeof e?a=e:("string"===typeof e&&/^[0-9]+$/.test(e)&&(e=parseInt(e)),"number"===typeof e&&10===e.toString().length&&(e*=1e3),a=new Date(e));const l={"M+":a.getMonth()+1,"D+":a.getDate(),"H+":a.getHours(),"m+":a.getMinutes(),"s+":a.getSeconds(),"q+":Math.floor((a.getMonth()+3)/3),S:a.getMilliseconds()};/(Y+)/.test(t)&&(t=t.replace(RegExp.$1,(a.getFullYear()+"").substr(4-RegExp.$1.length)));for(let i in l)new RegExp("("+i+")").test(t)&&(t=t.replace(RegExp.$1,1===RegExp.$1.length?l[i]:("00"+l[i]).substr((""+l[i]).length)));return t}a.d(t,{r6:()=>l})},2709:(e,t,a)=>{a.r(t),a.d(t,{default:()=>Y});var l=a(6768),i=a(5130),s=a(4232);const n={class:"app-container"},d={class:"filter-item"},u={class:"table-header"},r={class:"statistics-cards"},o={class:"stat-card primary"},c={class:"stat-value"},p={class:"stat-card warning"},g={class:"stat-value"},m={class:"stat-card success"},v={class:"stat-value"},_={class:"stat-card danger"},b={class:"stat-value"},k={class:"station-info"},h={class:"station-detail"},f={class:"station-name"},y={class:"station-address"},C={class:"contact-phone"},F={key:1},w={key:0},L={key:1},x={class:"station-detail-dialog"},V={class:"image-container"},W={class:"image-section"},T={class:"image-preview"},R={key:1,class:"no-image"},S={class:"image-section"},z={class:"image-preview"},D={key:1,class:"no-image"},K={class:"dialog-footer"},X={class:"dialog-footer"},E={class:"image-preview-container"};function A(e,t,a,A,Q,j){const P=(0,l.g2)("el-input"),U=(0,l.g2)("el-option"),I=(0,l.g2)("el-select"),M=(0,l.g2)("el-date-picker"),$=(0,l.g2)("el-button"),Y=(0,l.g2)("el-card"),O=(0,l.g2)("el-table-column"),H=(0,l.g2)("el-image"),N=(0,l.g2)("el-tag"),q=(0,l.g2)("el-table"),B=(0,l.g2)("pagination"),G=(0,l.g2)("el-descriptions-item"),J=(0,l.g2)("el-descriptions"),Z=(0,l.g2)("el-dialog"),ee=(0,l.g2)("el-form-item"),te=(0,l.g2)("el-form"),ae=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",n,[(0,l.bF)(Y,{class:"filter-container"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",d,[(0,l.bF)(P,{modelValue:A.listQuery.search,"onUpdate:modelValue":t[0]||(t[0]=e=>A.listQuery.search=e),placeholder:"驿站名称/联系人",clearable:"",onKeyup:(0,i.jR)(A.handleFilter,["enter"]),style:{width:"200px"}},null,8,["modelValue","onKeyup"]),(0,l.bF)(I,{modelValue:A.listQuery.status,"onUpdate:modelValue":t[1]||(t[1]=e=>A.listQuery.status=e),placeholder:"审核状态",clearable:"",style:{width:"130px"},onChange:A.handleFilter},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(A.statusOptions,(e=>((0,l.uX)(),(0,l.Wv)(U,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,l.bF)(I,{modelValue:A.listQuery.type,"onUpdate:modelValue":t[2]||(t[2]=e=>A.listQuery.type=e),placeholder:"驿站类型",clearable:"",style:{width:"130px"},onChange:A.handleFilter},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(A.typeOptions,(e=>((0,l.uX)(),(0,l.Wv)(U,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,l.bF)(M,{modelValue:A.dateRange,"onUpdate:modelValue":t[3]||(t[3]=e=>A.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",style:{width:"260px"},onChange:A.handleDateChange},null,8,["modelValue","onChange"]),(0,l.bF)($,{type:"primary",icon:"Search",onClick:A.handleFilter},{default:(0,l.k6)((()=>t[12]||(t[12]=[(0,l.eW)("搜索")]))),_:1},8,["onClick"]),(0,l.bF)($,{icon:"Refresh",onClick:A.resetQuery},{default:(0,l.k6)((()=>t[13]||(t[13]=[(0,l.eW)("重置")]))),_:1},8,["onClick"])])])),_:1}),(0,l.bF)(Y,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",u,[(0,l.Lk)("div",r,[(0,l.Lk)("div",o,[(0,l.Lk)("div",c,(0,s.v_)(A.statistics.totalCount),1),t[14]||(t[14]=(0,l.Lk)("div",{class:"stat-label"},"总申请数",-1))]),(0,l.Lk)("div",p,[(0,l.Lk)("div",g,(0,s.v_)(A.statistics.pendingCount),1),t[15]||(t[15]=(0,l.Lk)("div",{class:"stat-label"},"待审核",-1))]),(0,l.Lk)("div",m,[(0,l.Lk)("div",v,(0,s.v_)(A.statistics.approvedCount),1),t[16]||(t[16]=(0,l.Lk)("div",{class:"stat-label"},"已通过",-1))]),(0,l.Lk)("div",_,[(0,l.Lk)("div",b,(0,s.v_)(A.statistics.rejectedCount),1),t[17]||(t[17]=(0,l.Lk)("div",{class:"stat-label"},"已拒绝",-1))])]),(0,l.Lk)("div",null,[(0,l.bF)($,{type:"success",size:"small",onClick:A.exportData},{default:(0,l.k6)((()=>t[18]||(t[18]=[(0,l.eW)("导出数据")]))),_:1},8,["onClick"])])]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(q,{data:A.list,border:"",fit:"","highlight-current-row":"",style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(O,{label:"ID",prop:"id",align:"center",width:"80"}),(0,l.bF)(O,{label:"驿站信息","min-width":"200"},{default:(0,l.k6)((({row:e})=>[(0,l.Lk)("div",k,[(0,l.bF)(H,{src:e.station_image,style:{width:"60px",height:"60px","border-radius":"4px"}},null,8,["src"]),(0,l.Lk)("div",h,[(0,l.Lk)("div",f,(0,s.v_)(e.station_name),1),(0,l.Lk)("div",y,(0,s.v_)(e.address),1),(0,l.Lk)("div",null,[(0,l.bF)(N,{size:"small",type:A.getTypeTagType(e.type)},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.getTypeText(e.type)),1)])),_:2},1032,["type"])])])])])),_:1}),(0,l.bF)(O,{label:"联系人",align:"center",width:"150"},{default:(0,l.k6)((({row:e})=>[(0,l.Lk)("div",null,(0,s.v_)(e.contact_name),1),(0,l.Lk)("div",C,(0,s.v_)(e.contact_phone),1)])),_:1}),(0,l.bF)(O,{label:"营业执照",align:"center",width:"120"},{default:(0,l.k6)((({row:e})=>[e.license_image?((0,l.uX)(),(0,l.Wv)($,{key:0,size:"small",type:"primary",plain:"",onClick:t=>A.previewImage(e.license_image)},{default:(0,l.k6)((()=>t[19]||(t[19]=[(0,l.eW)("查看")]))),_:2},1032,["onClick"])):((0,l.uX)(),(0,l.CE)("span",F,"无"))])),_:1}),(0,l.bF)(O,{label:"申请时间",width:"150",align:"center"},{default:(0,l.k6)((({row:e})=>[(0,l.Lk)("span",null,(0,s.v_)(A.formatDateTime(e.created_at)),1)])),_:1}),(0,l.bF)(O,{label:"状态",prop:"status",align:"center",width:"100"},{default:(0,l.k6)((({row:e})=>[(0,l.bF)(N,{type:A.getStatusType(e.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.getStatusText(e.status)),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(O,{label:"审核时间",width:"150",align:"center"},{default:(0,l.k6)((({row:e})=>[e.verified_at?((0,l.uX)(),(0,l.CE)("span",w,(0,s.v_)(A.formatDateTime(e.verified_at)),1)):((0,l.uX)(),(0,l.CE)("span",L,"--"))])),_:1}),(0,l.bF)(O,{label:"操作",align:"center",width:"200","class-name":"small-padding fixed-width"},{default:(0,l.k6)((({row:e})=>[0===e.status?((0,l.uX)(),(0,l.Wv)($,{key:0,type:"success",size:"small",onClick:t=>A.handleApprove(e)},{default:(0,l.k6)((()=>t[20]||(t[20]=[(0,l.eW)("通过")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),0===e.status?((0,l.uX)(),(0,l.Wv)($,{key:1,type:"danger",size:"small",onClick:t=>A.handleReject(e)},{default:(0,l.k6)((()=>t[21]||(t[21]=[(0,l.eW)("拒绝")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),(0,l.bF)($,{type:"info",size:"small",onClick:t=>A.handleDetail(e)},{default:(0,l.k6)((()=>t[22]||(t[22]=[(0,l.eW)("详情")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[ae,A.listLoading]]),(0,l.bo)((0,l.bF)(B,{total:A.total,page:A.listQuery.page,limit:A.listQuery.limit,onPagination:e.getList},null,8,["total","page","limit","onPagination"]),[[i.aG,A.total>0]])])),_:1}),(0,l.bF)(Z,{title:"驿站认证详情",modelValue:A.dialogVisible,"onUpdate:modelValue":t[7]||(t[7]=e=>A.dialogVisible=e),width:"700px"},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",x,[(0,l.bF)(J,{column:2,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(G,{label:"申请编号",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.detail.id),1)])),_:1}),(0,l.bF)(G,{label:"驿站名称",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.detail.station_name),1)])),_:1}),(0,l.bF)(G,{label:"驿站类型"},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.getTypeText(A.detail.type)),1)])),_:1}),(0,l.bF)(G,{label:"负责人"},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.detail.contact_name),1)])),_:1}),(0,l.bF)(G,{label:"联系电话"},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.detail.contact_phone),1)])),_:1}),(0,l.bF)(G,{label:"紧急联系人"},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.detail.emergency_contact||"--"),1)])),_:1}),(0,l.bF)(G,{label:"详细地址",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.detail.address),1)])),_:1}),(0,l.bF)(G,{label:"营业时间",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.detail.business_hours||"--"),1)])),_:1}),(0,l.bF)(G,{label:"申请时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.formatDateTime(A.detail.created_at)),1)])),_:1}),(0,l.bF)(G,{label:"审核时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.detail.verified_at?A.formatDateTime(A.detail.verified_at):"--"),1)])),_:1}),(0,l.bF)(G,{label:"状态"},{default:(0,l.k6)((()=>[void 0!==A.detail.status?((0,l.uX)(),(0,l.Wv)(N,{key:0,type:A.getStatusType(A.detail.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.getStatusText(A.detail.status)),1)])),_:1},8,["type"])):(0,l.Q3)("",!0)])),_:1}),(0,l.bF)(G,{label:"审核备注"},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(A.detail.remark||"--"),1)])),_:1})])),_:1}),(0,l.Lk)("div",V,[(0,l.Lk)("div",W,[t[23]||(t[23]=(0,l.Lk)("div",{class:"image-title"},"驿站照片",-1)),(0,l.Lk)("div",T,[A.detail.station_image?((0,l.uX)(),(0,l.Wv)(H,{key:0,src:A.detail.station_image,fit:"cover","preview-src-list":[A.detail.station_image],style:{width:"200px",height:"150px","border-radius":"4px"}},null,8,["src","preview-src-list"])):((0,l.uX)(),(0,l.CE)("div",R,"未上传照片"))])]),(0,l.Lk)("div",S,[t[24]||(t[24]=(0,l.Lk)("div",{class:"image-title"},"营业执照",-1)),(0,l.Lk)("div",z,[A.detail.license_image?((0,l.uX)(),(0,l.Wv)(H,{key:0,src:A.detail.license_image,fit:"cover","preview-src-list":[A.detail.license_image],style:{width:"200px",height:"150px","border-radius":"4px"}},null,8,["src","preview-src-list"])):((0,l.uX)(),(0,l.CE)("div",D,"未上传执照"))])])]),(0,l.Lk)("div",K,[(0,l.bF)($,{onClick:t[4]||(t[4]=e=>A.dialogVisible=!1)},{default:(0,l.k6)((()=>t[25]||(t[25]=[(0,l.eW)("关闭")]))),_:1}),0===A.detail.status?((0,l.uX)(),(0,l.CE)(l.FK,{key:0},[(0,l.bF)($,{type:"success",onClick:t[5]||(t[5]=e=>A.handleApprove(A.detail))},{default:(0,l.k6)((()=>t[26]||(t[26]=[(0,l.eW)("通过")]))),_:1}),(0,l.bF)($,{type:"danger",onClick:t[6]||(t[6]=e=>A.handleReject(A.detail))},{default:(0,l.k6)((()=>t[27]||(t[27]=[(0,l.eW)("拒绝")]))),_:1})],64)):(0,l.Q3)("",!0)])])),[[ae,A.detailLoading]])])),_:1},8,["modelValue"]),(0,l.bF)(Z,{title:"approve"===A.auditAction?"通过驿站认证":"拒绝驿站认证",modelValue:A.auditDialogVisible,"onUpdate:modelValue":t[10]||(t[10]=e=>A.auditDialogVisible=e),width:"500px"},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",X,[(0,l.bF)($,{onClick:t[9]||(t[9]=e=>A.auditDialogVisible=!1)},{default:(0,l.k6)((()=>t[28]||(t[28]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)($,{type:"approve"===A.auditAction?"success":"danger",onClick:A.submitAudit,loading:A.auditSubmitting},{default:(0,l.k6)((()=>t[29]||(t[29]=[(0,l.eW)("确 定")]))),_:1},8,["type","onClick","loading"])])])),default:(0,l.k6)((()=>[(0,l.bF)(te,{model:A.auditForm,"label-width":"80px"},{default:(0,l.k6)((()=>[(0,l.bF)(ee,{label:"审核意见"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{modelValue:A.auditForm.remark,"onUpdate:modelValue":t[8]||(t[8]=e=>A.auditForm.remark=e),type:"textarea",rows:4,placeholder:"approve"===A.auditAction?"审核通过说明（选填）":"拒绝原因（必填）"},null,8,["modelValue","placeholder"])])),_:1})])),_:1},8,["model"])])),_:1},8,["title","modelValue"]),(0,l.bF)(Z,{title:"图片预览",modelValue:A.imagePreviewVisible,"onUpdate:modelValue":t[11]||(t[11]=e=>A.imagePreviewVisible=e),width:"700px",center:""},{default:(0,l.k6)((()=>[(0,l.Lk)("div",E,[(0,l.bF)(H,{src:A.previewImageUrl,style:{"max-width":"100%","max-height":"500px"}},null,8,["src"])])])),_:1},8,["modelValue"])])}var Q=a(144),j=a(1219),P=a(9190),U=a(1981);const I={name:"StationVerify",components:{Pagination:P.A},setup(){const e=(0,Q.KR)([]),t=(0,Q.KR)([]),a=(0,Q.KR)(0),i=(0,Q.KR)(!1),s=(0,Q.KR)(!1),n=(0,Q.KR)(!1),d=(0,Q.KR)({}),u=(0,Q.KR)(!1),r=(0,Q.KR)(!1),o=(0,Q.KR)("approve"),c=(0,Q.KR)(!1),p=(0,Q.KR)(""),g=(0,Q.Kh)({page:1,limit:10,status:"",type:"",search:"",start_date:"",end_date:""}),m=(0,Q.Kh)({totalCount:0,pendingCount:0,approvedCount:0,rejectedCount:0}),v=(0,Q.Kh)({id:null,status:0,remark:""}),_=[{label:"待审核",value:0},{label:"已通过",value:1},{label:"已拒绝",value:2}],b=[{label:"社区驿站",value:1},{label:"商圈驿站",value:2},{label:"校园驿站",value:3},{label:"企业驿站",value:4},{label:"其他",value:5}],k=[{id:1,station_name:"阳光社区驿站",station_image:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",address:"北京市朝阳区阳光社区88号",type:1,contact_name:"张明",contact_phone:"***********",emergency_contact:"***********",business_hours:"08:00-20:00",license_image:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",created_at:"2023-05-15 09:30:00",verified_at:null,status:0,remark:""},{id:2,station_name:"星光商业广场驿站",station_image:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",address:"上海市浦东新区星光广场12号",type:2,contact_name:"李强",contact_phone:"***********",emergency_contact:"***********",business_hours:"09:00-21:00",license_image:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",created_at:"2023-05-14 14:20:00",verified_at:"2023-05-14 16:30:00",status:1,remark:"资料齐全，符合要求"},{id:3,station_name:"清华大学东门驿站",station_image:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",address:"北京市海淀区清华大学东门",type:3,contact_name:"王学",contact_phone:"***********",emergency_contact:"***********",business_hours:"07:30-22:00",license_image:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",created_at:"2023-05-13 10:15:00",verified_at:"2023-05-13 11:30:00",status:2,remark:"证照不清晰，联系方式无法验证"},{id:4,station_name:"科技园创业中心驿站",station_image:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",address:"深圳市南山区科技园创业中心A栋",type:4,contact_name:"刘创",contact_phone:"***********",emergency_contact:"***********",business_hours:"08:30-19:30",license_image:"https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",created_at:"2023-05-12 16:45:00",verified_at:null,status:0,remark:""}],h=()=>{i.value=!0,setTimeout((()=>{let e=[...k];""!==g.status&&(e=e.filter((e=>e.status===g.status))),""!==g.type&&(e=e.filter((e=>e.type===g.type))),g.search&&(e=e.filter((e=>e.station_name.includes(g.search)||e.contact_name.includes(g.search)||e.contact_phone.includes(g.search)))),g.start_date&&g.end_date,t.value=e,a.value=e.length,f(e),i.value=!1}),500)},f=e=>{let t=0,a=0,l=0;e.forEach((e=>{0===e.status?t++:1===e.status?a++:2===e.status&&l++})),m.totalCount=e.length,m.pendingCount=t,m.approvedCount=a,m.rejectedCount=l},y=()=>{g.page=1,h()},C=()=>{e.value=[],Object.assign(g,{page:1,limit:10,status:"",type:"",search:"",start_date:"",end_date:""}),h()},F=e=>{e?(g.start_date=e[0],g.end_date=e[1]):(g.start_date="",g.end_date="")},w=e=>{const t={0:"warning",1:"success",2:"danger"};return t[e]},L=e=>{const t={0:"待审核",1:"已通过",2:"已拒绝"};return t[e]},x=e=>{const t={1:"社区驿站",2:"商圈驿站",3:"校园驿站",4:"企业驿站",5:"其他"};return t[e]||"未知"},V=e=>{const t={1:"",2:"success",3:"warning",4:"info",5:"danger"};return t[e]||""},W=e=>{n.value=!0,d.value={...e},s.value=!0,setTimeout((()=>{n.value=!1}),300)},T=e=>{p.value=e,c.value=!0},R=e=>{o.value="approve",v.id=e.id,v.status=1,v.remark="",u.value=!0},S=e=>{o.value="reject",v.id=e.id,v.status=2,v.remark="",u.value=!0},z=()=>{"reject"!==o.value||v.remark?(r.value=!0,setTimeout((()=>{const e=t.value.findIndex((e=>e.id===v.id));e>-1&&(t.value[e].status=v.status,t.value[e].verified_at=(0,U.r6)(new Date),t.value[e].remark=v.remark,d.value.id===v.id&&(d.value.status=v.status,d.value.verified_at=(0,U.r6)(new Date),d.value.remark=v.remark)),f(t.value),r.value=!1,u.value=!1,(0,j.nk)({type:"success",message:"approve"===o.value?"驿站认证已通过":"驿站认证已拒绝"})}),500)):j.nk.warning("请填写拒绝原因")},D=()=>{j.nk.success("数据导出功能开发中")};return(0,l.sV)((()=>{h()})),{formatDateTime:U.r6,dateRange:e,list:t,total:a,listLoading:i,listQuery:g,statistics:m,statusOptions:_,typeOptions:b,dialogVisible:s,detailLoading:n,detail:d,auditDialogVisible:u,auditSubmitting:r,auditAction:o,auditForm:v,imagePreviewVisible:c,previewImageUrl:p,handleFilter:y,resetQuery:C,handleDateChange:F,getStatusType:w,getStatusText:L,getTypeText:x,getTypeTagType:V,handleDetail:W,previewImage:T,handleApprove:R,handleReject:S,submitAudit:z,exportData:D}}};var M=a(1241);const $=(0,M.A)(I,[["render",A],["__scopeId","data-v-61503628"]]),Y=$},9190:(e,t,a)=>{a.d(t,{A:()=>r});var l=a(6768),i=a(4232);function s(e,t,a,s,n,d){const u=(0,l.g2)("el-pagination");return(0,l.uX)(),(0,l.CE)("div",{class:(0,i.C4)([{hidden:a.hidden},"pagination-container"])},[(0,l.bF)(u,{background:a.background,"current-page":d.currentPage,"onUpdate:currentPage":t[0]||(t[0]=e=>d.currentPage=e),"page-size":d.pageSize,"onUpdate:pageSize":t[1]||(t[1]=e=>d.pageSize=e),layout:a.layout,"page-sizes":a.pageSizes,total:a.total,onSizeChange:d.handleSizeChange,onCurrentChange:d.handleCurrentChange},null,8,["background","current-page","page-size","layout","page-sizes","total","onSizeChange","onCurrentChange"])],2)}const n={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:10},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},emits:["update:page","update:limit","pagination"],computed:{currentPage:{get(){return this.page},set(e){this.$emit("update:page",e)}},pageSize:{get(){return this.limit},set(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange(e){this.$emit("pagination",{page:this.currentPage,limit:e})},handleCurrentChange(e){this.$emit("pagination",{page:e,limit:this.pageSize})}}};var d=a(1241);const u=(0,d.A)(n,[["render",s],["__scopeId","data-v-5035dd33"]]),r=u}}]);