"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[9954],{1981:(e,t,a)=>{function l(e,t="YYYY-MM-DD HH:mm:ss"){if(!e)return"";let a;"object"===typeof e?a=e:("string"===typeof e&&/^[0-9]+$/.test(e)&&(e=parseInt(e)),"number"===typeof e&&10===e.toString().length&&(e*=1e3),a=new Date(e));const l={"M+":a.getMonth()+1,"D+":a.getDate(),"H+":a.getHours(),"m+":a.getMinutes(),"s+":a.getSeconds(),"q+":Math.floor((a.getMonth()+3)/3),S:a.getMilliseconds()};/(Y+)/.test(t)&&(t=t.replace(RegExp.$1,(a.getFullYear()+"").substr(4-RegExp.$1.length)));for(let i in l)new RegExp("("+i+")").test(t)&&(t=t.replace(RegExp.$1,1===RegExp.$1.length?l[i]:("00"+l[i]).substr((""+l[i]).length)));return t}a.d(t,{r6:()=>l})},2211:(e,t,a)=>{a.d(t,{R7:()=>i,fW:()=>r,fr:()=>u,nU:()=>c,q_:()=>n,tS:()=>d,tb:()=>o,zK:()=>s});var l=a(5720);function i(e){return(0,l.A)({url:"/api/admin/promotion/overview",method:"get",params:e})}function s(e){return(0,l.A)({url:"/api/admin/promotion/trend",method:"get",params:e})}function o(){return(0,l.A)({url:"/api/admin/promotion/level-distribution",method:"get"})}function n(e){return(0,l.A)({url:"/api/admin/promotion/commission-ranking",method:"get",params:e})}function r(e){return(0,l.A)({url:"/api/admin/promotion/invites-ranking",method:"get",params:e})}function d(e,t=1,a={}){return(0,l.A)({url:`/api/admin/promoters/${e}/invites`,method:"get",params:{level:t,...a}})}function u(e,t={}){return(0,l.A)({url:`/api/admin/promoters/${e}/commissions`,method:"get",params:t})}function c(e,t={}){return(0,l.A)({url:`/api/admin/promoters/${e}/withdrawals`,method:"get",params:t})}},7093:(e,t,a)=>{a.d(t,{A:()=>s});const l={mounted(e,t){e.addEventListener("click",(a=>{const l=Object.assign({},t.value),i=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},l),s=i.ele;if(s){s.style.position="relative",s.style.overflow="hidden";const e=s.getBoundingClientRect();let t=s.querySelector(".waves-ripple");switch(t?t.className="waves-ripple":(t=document.createElement("span"),t.className="waves-ripple",t.style.height=t.style.width=Math.max(e.width,e.height)+"px",s.appendChild(t)),i.type){case"center":t.style.top=e.height/2-t.offsetHeight/2+"px",t.style.left=e.width/2-t.offsetWidth/2+"px";break;default:t.style.top=(a.pageY-e.top-t.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",t.style.left=(a.pageX-e.left-t.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return t.style.backgroundColor=i.color,t.className="waves-ripple z-active",!1}}),!1)}},i=l,s=i},9190:(e,t,a)=>{a.d(t,{A:()=>d});var l=a(6768),i=a(4232);function s(e,t,a,s,o,n){const r=(0,l.g2)("el-pagination");return(0,l.uX)(),(0,l.CE)("div",{class:(0,i.C4)([{hidden:a.hidden},"pagination-container"])},[(0,l.bF)(r,{background:a.background,"current-page":n.currentPage,"onUpdate:currentPage":t[0]||(t[0]=e=>n.currentPage=e),"page-size":n.pageSize,"onUpdate:pageSize":t[1]||(t[1]=e=>n.pageSize=e),layout:a.layout,"page-sizes":a.pageSizes,total:a.total,onSizeChange:n.handleSizeChange,onCurrentChange:n.handleCurrentChange},null,8,["background","current-page","page-size","layout","page-sizes","total","onSizeChange","onCurrentChange"])],2)}const o={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:10},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},emits:["update:page","update:limit","pagination"],computed:{currentPage:{get(){return this.page},set(e){this.$emit("update:page",e)}},pageSize:{get(){return this.limit},set(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange(e){this.$emit("pagination",{page:this.currentPage,limit:e})},handleCurrentChange(e){this.$emit("pagination",{page:e,limit:this.pageSize})}}};var n=a(1241);const r=(0,n.A)(o,[["render",s],["__scopeId","data-v-5035dd33"]]),d=r},9954:(e,t,a)=>{a.r(t),a.d(t,{default:()=>ve});var l=a(6768),i=a(5130),s=a(4232);const o={class:"app-container"},n={class:"filter-item"},r={class:"table-header"},d={"slot-scope":"{row}"},u={class:"user-info"},c={class:"user-detail"},p={class:"user-phone"},m={"slot-scope":"{row}"},h={"slot-scope":"{row}"},g={"slot-scope":"{row}"},v={class:"price-text"},b={"slot-scope":"{row}"},k={class:"price-text"},f={"slot-scope":"{row}"},w={"slot-scope":"{row}"},L={"slot-scope":"{row}"},_={class:"promoter-detail"},y={class:"detail-header"},F={class:"detail-info"},C={class:"data-card"},T={class:"data-value price-text"},x={class:"data-card"},W={class:"data-value price-text"},V={class:"data-card"},S={class:"data-value price-text"},D={class:"data-card"},Q={class:"data-value code-text"},$={class:"data-card"},z={class:"data-value"},A={class:"data-card"},E={class:"data-value"},P={class:"status-text"},X={class:"form-actions"},U={"slot-scope":"{row}"},I={class:"user-info"},N={class:"user-detail"},R={class:"user-phone"},B={"slot-scope":"{row}"},M={"slot-scope":"{row}"},O={"slot-scope":"{row}"},Y={class:"price-text"},H={"slot-scope":"{row}"},K={class:"price-text"},j={"slot-scope":"{row}"},q={"slot-scope":"{row}"},G={class:"price-text"},J={"slot-scope":"{row}"},Z={"slot-scope":"{row}"},ee={"slot-scope":"{row}"},te={slot:"footer",class:"dialog-footer"};function ae(e,t,a,ae,le,ie){const se=(0,l.g2)("el-input"),oe=(0,l.g2)("el-option"),ne=(0,l.g2)("el-select"),re=(0,l.g2)("el-button"),de=(0,l.g2)("el-card"),ue=(0,l.g2)("el-table-column"),ce=(0,l.g2)("el-avatar"),pe=(0,l.g2)("el-tag"),me=(0,l.g2)("el-switch"),he=(0,l.g2)("el-table"),ge=(0,l.g2)("pagination"),ve=(0,l.g2)("el-divider"),be=(0,l.g2)("el-col"),ke=(0,l.g2)("el-row"),fe=(0,l.g2)("el-form-item"),we=(0,l.g2)("el-form"),Le=(0,l.g2)("el-tab-pane"),_e=(0,l.g2)("el-radio-button"),ye=(0,l.g2)("el-radio-group"),Fe=(0,l.g2)("el-tabs"),Ce=(0,l.g2)("el-dialog"),Te=(0,l.gN)("waves"),xe=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",o,[(0,l.bF)(de,{class:"filter-container"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",n,[(0,l.bF)(se,{modelValue:le.listQuery.search,"onUpdate:modelValue":t[0]||(t[0]=e=>le.listQuery.search=e),placeholder:"搜索用户昵称/手机",clearable:"",onKeyup:(0,i.jR)(ie.handleFilter,["enter","native"]),style:{width:"200px"}},null,8,["modelValue","onKeyup"]),(0,l.bF)(ne,{modelValue:le.listQuery.status,"onUpdate:modelValue":t[1]||(t[1]=e=>le.listQuery.status=e),placeholder:"推广员状态",clearable:"",style:{width:"130px"},onChange:ie.handleFilter},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(le.statusOptions,(e=>((0,l.uX)(),(0,l.Wv)(oe,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,l.bF)(ne,{modelValue:le.listQuery.level,"onUpdate:modelValue":t[2]||(t[2]=e=>le.listQuery.level=e),placeholder:"推广员等级",clearable:"",style:{width:"130px"},onChange:ie.handleFilter},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(le.levelOptions,(e=>((0,l.uX)(),(0,l.Wv)(oe,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(re,{type:"primary",icon:"el-icon-search",onClick:ie.handleFilter},{default:(0,l.k6)((()=>t[14]||(t[14]=[(0,l.eW)("搜索")]))),_:1},8,["onClick"])),[[Te]]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(re,{icon:"el-icon-refresh",onClick:ie.resetQuery},{default:(0,l.k6)((()=>t[15]||(t[15]=[(0,l.eW)("重置")]))),_:1},8,["onClick"])),[[Te]])])])),_:1}),(0,l.bF)(de,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",r,[t[17]||(t[17]=(0,l.Lk)("h3",null,"推广员列表",-1)),(0,l.Lk)("div",null,[(0,l.bF)(re,{type:"success",size:"small",onClick:ie.exportData},{default:(0,l.k6)((()=>t[16]||(t[16]=[(0,l.eW)("导出数据")]))),_:1},8,["onClick"])])]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(he,{data:le.list,border:"",fit:"","highlight-current-row":"",style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(ue,{label:"ID",prop:"id",align:"center",width:"80"}),(0,l.bF)(ue,{label:"用户信息","min-width":"180"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",d,[(0,l.Lk)("div",u,[(0,l.bF)(ce,{src:e.row.avatar||"/static/default-avatar.png",size:40},null,8,["src"]),(0,l.Lk)("div",c,[(0,l.Lk)("div",null,(0,s.v_)(e.row.nickname),1),(0,l.Lk)("div",p,(0,s.v_)(e.row.phone),1)])])])])),_:1}),(0,l.bF)(ue,{label:"推广等级",prop:"level_name",align:"center",width:"100"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",m,[(0,l.bF)(pe,{type:ie.getLevelTagType(e.row.level)},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(e.row.level_name),1)])),_:1},8,["type"])])])),_:1}),(0,l.bF)(ue,{label:"推广人数",align:"center",width:"220"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",h,[(0,l.Lk)("div",null,"一级："+(0,s.v_)(e.row.first_level_count)+" 人",1),(0,l.Lk)("div",null,"二级："+(0,s.v_)(e.row.second_level_count)+" 人",1)])])),_:1}),(0,l.bF)(ue,{label:"累计佣金",prop:"total_commission",align:"center",width:"120"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",g,[(0,l.Lk)("span",v,"¥"+(0,s.v_)(e.row.total_commission.toFixed(2)),1)])])),_:1}),(0,l.bF)(ue,{label:"可提现金额",prop:"available_balance",align:"center",width:"120"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",b,[(0,l.Lk)("span",k,"¥"+(0,s.v_)(e.row.available_balance.toFixed(2)),1)])])),_:1}),(0,l.bF)(ue,{label:"状态",prop:"status",align:"center",width:"100"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",f,[(0,l.bF)(me,{modelValue:e.row.status,"onUpdate:modelValue":t[3]||(t[3]=t=>e.row.status=t),"active-value":1,"inactive-value":0,onChange:t[4]||(t[4]=t=>ie.handleStatusChange(e.row))},null,8,["modelValue"])])])),_:1}),(0,l.bF)(ue,{label:"注册时间",width:"150",align:"center"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",w,[(0,l.Lk)("span",null,(0,s.v_)(e.row.created_at|e.formatDateTime),1)])])),_:1}),(0,l.bF)(ue,{label:"操作",align:"center",width:"200","class-name":"small-padding fixed-width"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",L,[(0,l.bF)(re,{type:"primary",size:"mini",onClick:t[5]||(t[5]=t=>ie.handleDetail(e.row))},{default:(0,l.k6)((()=>t[18]||(t[18]=[(0,l.eW)("详情")]))),_:1}),(0,l.bF)(re,{type:"warning",size:"mini",onClick:t[6]||(t[6]=t=>ie.handleEditLevel(e.row))},{default:(0,l.k6)((()=>t[19]||(t[19]=[(0,l.eW)("修改等级")]))),_:1})])])),_:1})])),_:1},8,["data"])),[[xe,le.listLoading]]),(0,l.bo)((0,l.bF)(ge,{total:le.total,page:le.listQuery.page,limit:le.listQuery.limit,onPagination:ie.getList},null,8,["total","page","limit","onPagination"]),[[i.aG,le.total>0]])])),_:1}),(0,l.bF)(Ce,{title:"推广员详情",visible:le.dialogVisible,width:"700px"},{default:(0,l.k6)((()=>[(0,l.bF)(Fe,{modelValue:le.activeTab,"onUpdate:modelValue":t[10]||(t[10]=e=>le.activeTab=e)},{default:(0,l.k6)((()=>[(0,l.bF)(Le,{label:"基本信息",name:"info"},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",_,[(0,l.Lk)("div",y,[(0,l.bF)(ce,{src:le.detail.avatar||"/static/default-avatar.png",size:60},null,8,["src"]),(0,l.Lk)("div",F,[(0,l.Lk)("h3",null,(0,s.v_)(le.detail.nickname),1),(0,l.Lk)("p",null,(0,s.v_)(le.detail.phone),1),(0,l.bF)(pe,{type:ie.getLevelTagType(le.detail.level)},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(le.detail.level_name),1)])),_:1},8,["type"])])]),(0,l.bF)(ve,{"content-position":"left"},{default:(0,l.k6)((()=>t[20]||(t[20]=[(0,l.eW)("账户数据")]))),_:1}),(0,l.bF)(ke,{gutter:20},{default:(0,l.k6)((()=>[(0,l.bF)(be,{span:8},{default:(0,l.k6)((()=>[(0,l.Lk)("div",C,[t[21]||(t[21]=(0,l.Lk)("div",{class:"data-label"},"累计佣金",-1)),(0,l.Lk)("div",T,"¥"+(0,s.v_)(le.detail.total_commission),1)])])),_:1}),(0,l.bF)(be,{span:8},{default:(0,l.k6)((()=>[(0,l.Lk)("div",x,[t[22]||(t[22]=(0,l.Lk)("div",{class:"data-label"},"可提现金额",-1)),(0,l.Lk)("div",W,"¥"+(0,s.v_)(le.detail.available_balance),1)])])),_:1}),(0,l.bF)(be,{span:8},{default:(0,l.k6)((()=>[(0,l.Lk)("div",V,[t[23]||(t[23]=(0,l.Lk)("div",{class:"data-label"},"冻结金额",-1)),(0,l.Lk)("div",S,"¥"+(0,s.v_)(le.detail.frozen_balance),1)])])),_:1})])),_:1}),(0,l.bF)(ve,{"content-position":"left"},{default:(0,l.k6)((()=>t[24]||(t[24]=[(0,l.eW)("推广数据")]))),_:1}),(0,l.bF)(ke,{gutter:20},{default:(0,l.k6)((()=>[(0,l.bF)(be,{span:8},{default:(0,l.k6)((()=>[(0,l.Lk)("div",D,[t[25]||(t[25]=(0,l.Lk)("div",{class:"data-label"},"推广码",-1)),(0,l.Lk)("div",Q,(0,s.v_)(le.detail.promotion_code),1)])])),_:1}),(0,l.bF)(be,{span:8},{default:(0,l.k6)((()=>[(0,l.Lk)("div",$,[t[26]||(t[26]=(0,l.Lk)("div",{class:"data-label"},"一级用户",-1)),(0,l.Lk)("div",z,(0,s.v_)(le.detail.first_level_count)+" 人",1)])])),_:1}),(0,l.bF)(be,{span:8},{default:(0,l.k6)((()=>[(0,l.Lk)("div",A,[t[27]||(t[27]=(0,l.Lk)("div",{class:"data-label"},"二级用户",-1)),(0,l.Lk)("div",E,(0,s.v_)(le.detail.second_level_count)+" 人",1)])])),_:1})])),_:1}),(0,l.bF)(ve,{"content-position":"left"},{default:(0,l.k6)((()=>t[28]||(t[28]=[(0,l.eW)("账户状态")]))),_:1}),(0,l.bF)(we,{"label-width":"100px"},{default:(0,l.k6)((()=>[(0,l.bF)(fe,{label:"注册时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(le.detail.created_at|e.formatDateTime),1)])),_:1}),(0,l.bF)(fe,{label:"成为推广员"},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(le.detail.promoter_time|e.formatDateTime),1)])),_:1}),(0,l.bF)(fe,{label:"账户状态"},{default:(0,l.k6)((()=>[(0,l.bF)(me,{modelValue:le.detail.status,"onUpdate:modelValue":t[7]||(t[7]=e=>le.detail.status=e),"active-value":1,"inactive-value":0,onChange:ie.handleDetailStatusChange},null,8,["modelValue","onChange"]),(0,l.Lk)("span",P,(0,s.v_)(1===le.detail.status?"正常":"已禁用"),1)])),_:1}),(0,l.bF)(fe,{label:"备注"},{default:(0,l.k6)((()=>[(0,l.bF)(se,{modelValue:le.detail.remark,"onUpdate:modelValue":t[8]||(t[8]=e=>le.detail.remark=e),type:"textarea",rows:3,placeholder:"请输入备注信息"},null,8,["modelValue"]),(0,l.Lk)("div",X,[(0,l.bF)(re,{type:"primary",size:"small",onClick:ie.saveRemark},{default:(0,l.k6)((()=>t[29]||(t[29]=[(0,l.eW)("保存备注")]))),_:1},8,["onClick"])])])),_:1})])),_:1})])),[[xe,le.detailLoading]])])),_:1}),(0,l.bF)(Le,{label:"推广记录",name:"invites"},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(ye,{modelValue:le.inviteLevel,"onUpdate:modelValue":t[9]||(t[9]=e=>le.inviteLevel=e),style:{"margin-bottom":"15px"},onChange:ie.getInvitesList},{default:(0,l.k6)((()=>[(0,l.bF)(_e,{label:1},{default:(0,l.k6)((()=>t[30]||(t[30]=[(0,l.eW)("一级推广")]))),_:1}),(0,l.bF)(_e,{label:2},{default:(0,l.k6)((()=>t[31]||(t[31]=[(0,l.eW)("二级推广")]))),_:1})])),_:1},8,["modelValue","onChange"]),(0,l.bF)(he,{data:le.invitesList,border:"",fit:"",style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(ue,{label:"用户信息","min-width":"180"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",U,[(0,l.Lk)("div",I,[(0,l.bF)(ce,{src:e.row.avatar||"/static/default-avatar.png",size:30},null,8,["src"]),(0,l.Lk)("div",N,[(0,l.Lk)("div",null,(0,s.v_)(e.row.nickname),1),(0,l.Lk)("div",R,(0,s.v_)(e.row.phone),1)])])])])),_:1}),2===le.inviteLevel?((0,l.uX)(),(0,l.Wv)(ue,{key:0,label:"上级推广员",prop:"inviter_nickname",width:"120"})):(0,l.Q3)("",!0),(0,l.bF)(ue,{label:"注册时间",prop:"created_at",width:"150"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",B,[(0,l.eW)((0,s.v_)(e.row.created_at|e.formatDateTime),1)])])),_:1})])),_:1},8,["data"]),(0,l.bo)((0,l.bF)(ge,{total:le.invitesTotal,page:le.invitesQuery.page,limit:le.invitesQuery.limit,onPagination:ie.getInvitesList},null,8,["total","page","limit","onPagination"]),[[i.aG,le.invitesTotal>0]])])),[[xe,le.invitesLoading]])])),_:1}),(0,l.bF)(Le,{label:"佣金记录",name:"commission"},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(he,{data:le.commissionsList,border:"",fit:"",style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(ue,{label:"时间",prop:"created_at",width:"150"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",M,[(0,l.eW)((0,s.v_)(e.row.created_at|e.formatDateTime),1)])])),_:1}),(0,l.bF)(ue,{label:"金额",prop:"amount",width:"120"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",O,[(0,l.Lk)("span",Y,"¥"+(0,s.v_)(e.row.amount.toFixed(2)),1)])])),_:1}),(0,l.bF)(ue,{label:"说明",prop:"remark","min-width":"180"}),(0,l.bF)(ue,{label:"余额",prop:"balance",width:"120"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",H,[(0,l.Lk)("span",K,"¥"+(0,s.v_)(e.row.balance.toFixed(2)),1)])])),_:1})])),_:1},8,["data"]),(0,l.bo)((0,l.bF)(ge,{total:le.commissionsTotal,page:le.commissionsQuery.page,limit:le.commissionsQuery.limit,onPagination:ie.getCommissionsList},null,8,["total","page","limit","onPagination"]),[[i.aG,le.commissionsTotal>0]])])),[[xe,le.commissionsLoading]])])),_:1}),(0,l.bF)(Le,{label:"提现记录",name:"withdrawals"},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(he,{data:le.withdrawalsList,border:"",fit:"",style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(ue,{label:"申请时间",prop:"created_at",width:"150"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",j,[(0,l.eW)((0,s.v_)(e.row.created_at|e.formatDateTime),1)])])),_:1}),(0,l.bF)(ue,{label:"金额",prop:"amount",width:"120"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",q,[(0,l.Lk)("span",G,"¥"+(0,s.v_)(e.row.amount.toFixed(2)),1)])])),_:1}),(0,l.bF)(ue,{label:"账户类型",width:"100"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",J,[(0,l.eW)((0,s.v_)(ie.getAccountTypeText(e.row.account_type)),1)])])),_:1}),(0,l.bF)(ue,{label:"账户信息",prop:"account_number","min-width":"150"}),(0,l.bF)(ue,{label:"状态",prop:"status",width:"100"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",Z,[(0,l.bF)(pe,{type:ie.getWithdrawStatusType(e.row.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(ie.getWithdrawStatusText(e.row.status)),1)])),_:1},8,["type"])])])),_:1}),(0,l.bF)(ue,{label:"处理时间",prop:"processed_at",width:"150"},{default:(0,l.k6)((()=>[(0,l.Lk)("template",ee,[(0,l.eW)((0,s.v_)(e.row.processed_at?e.formatDateTime(e.row.processed_at):"未处理"),1)])])),_:1})])),_:1},8,["data"]),(0,l.bo)((0,l.bF)(ge,{total:le.withdrawalsTotal,page:le.withdrawalsQuery.page,limit:le.withdrawalsQuery.limit,onPagination:ie.getWithdrawalsList},null,8,["total","page","limit","onPagination"]),[[i.aG,le.withdrawalsTotal>0]])])),[[xe,le.withdrawalsLoading]])])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["visible"]),(0,l.bF)(Ce,{title:"修改推广员等级",visible:le.levelDialogVisible,width:"400px"},{default:(0,l.k6)((()=>[(0,l.bF)(we,{model:le.levelForm,"label-width":"100px"},{default:(0,l.k6)((()=>[(0,l.bF)(fe,{label:"当前等级"},{default:(0,l.k6)((()=>[(0,l.bF)(pe,{type:ie.getLevelTagType(le.levelForm.currentLevel)},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(ie.getLevelName(le.levelForm.currentLevel)),1)])),_:1},8,["type"])])),_:1}),(0,l.bF)(fe,{label:"新等级"},{default:(0,l.k6)((()=>[(0,l.bF)(ne,{modelValue:le.levelForm.newLevel,"onUpdate:modelValue":t[11]||(t[11]=e=>le.levelForm.newLevel=e),placeholder:"请选择等级"},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(le.levelOptions,(e=>((0,l.uX)(),(0,l.Wv)(oe,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(fe,{label:"备注"},{default:(0,l.k6)((()=>[(0,l.bF)(se,{modelValue:le.levelForm.remark,"onUpdate:modelValue":t[12]||(t[12]=e=>le.levelForm.remark=e),type:"textarea",rows:3,placeholder:"请输入修改原因"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),(0,l.Lk)("div",te,[(0,l.bF)(re,{onClick:t[13]||(t[13]=e=>le.levelDialogVisible=!1)},{default:(0,l.k6)((()=>t[32]||(t[32]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(re,{type:"primary",onClick:ie.submitLevelChange,loading:le.levelSubmitting},{default:(0,l.k6)((()=>t[33]||(t[33]=[(0,l.eW)("确 定")]))),_:1},8,["onClick","loading"])])])),_:1},8,["visible"])])}var le=a(5720);function ie(e){return(0,le.A)({url:"/api/admin/promoters",method:"get",params:e})}function se(e){return(0,le.A)({url:`/api/admin/promoters/${e}`,method:"get"})}function oe(e,t){return(0,le.A)({url:`/api/admin/promoters/${e}/status`,method:"put",data:{status:t}})}function ne(e,t,a){return(0,le.A)({url:`/api/admin/promoters/${e}/level`,method:"put",data:{level:t,remark:a}})}function re(e,t){return(0,le.A)({url:`/api/admin/promoters/${e}/remark`,method:"put",data:{remark:t}})}var de=a(2211),ue=a(7093),ce=a(9190),pe=a(1981);const me={name:"PromoterList",components:{Pagination:ce.A},directives:{waves:ue.A},filters:{formatDateTime(e){return(0,pe.r6)(e)}},data(){return{list:[],total:0,listLoading:!0,listQuery:{page:1,limit:10,search:"",status:"",level:""},statusOptions:[{label:"已启用",value:1},{label:"已禁用",value:0}],levelOptions:[{label:"初级推广员",value:1},{label:"中级推广员",value:2},{label:"高级推广员",value:3},{label:"资深推广员",value:4}],dialogVisible:!1,activeTab:"info",detailLoading:!1,detail:{},invitesLoading:!1,invitesList:[],invitesTotal:0,inviteLevel:1,invitesQuery:{page:1,limit:10},commissionsLoading:!1,commissionsList:[],commissionsTotal:0,commissionsQuery:{page:1,limit:10},withdrawalsLoading:!1,withdrawalsList:[],withdrawalsTotal:0,withdrawalsQuery:{page:1,limit:10},levelDialogVisible:!1,levelSubmitting:!1,levelForm:{id:null,currentLevel:1,newLevel:1,remark:""}}},created(){this.getList()},methods:{getList(){this.listLoading=!0,ie(this.listQuery).then((e=>{this.list=e.data.list,this.total=e.data.pagination.total,this.listLoading=!1})).catch((()=>{this.listLoading=!1}))},handleFilter(){this.listQuery.page=1,this.getList()},resetQuery(){this.listQuery={page:1,limit:10,search:"",status:"",level:""},this.getList()},exportData(){this.$confirm("确认导出推广员数据?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$message({type:"success",message:"导出成功"})})).catch((()=>{}))},handleStatusChange(e){const t=1===e.status?"启用":"禁用";this.$confirm(`确认${t}该推广员?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{oe(e.id,e.status).then((()=>{this.$message({type:"success",message:`${t}成功`})})).catch((()=>{e.status=1===e.status?0:1}))})).catch((()=>{e.status=1===e.status?0:1}))},handleDetail(e){this.dialogVisible=!0,this.activeTab="info",this.getPromoterDetail(e.id)},getPromoterDetail(e){this.detailLoading=!0,se(e).then((e=>{this.detail=e.data,this.detailLoading=!1,this.resetDetailTabs()})).catch((()=>{this.detailLoading=!1}))},resetDetailTabs(){this.inviteLevel=1,this.invitesQuery={page:1,limit:10},this.invitesList=[],this.invitesTotal=0,this.commissionsQuery={page:1,limit:10},this.commissionsList=[],this.commissionsTotal=0,this.withdrawalsQuery={page:1,limit:10},this.withdrawalsList=[],this.withdrawalsTotal=0},handleDetailStatusChange(){const e=1===this.detail.status?"启用":"禁用";this.$confirm(`确认${e}该推广员?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{oe(this.detail.id,this.detail.status).then((()=>{this.$message({type:"success",message:`${e}成功`});const t=this.list.findIndex((e=>e.id===this.detail.id));-1!==t&&(this.list[t].status=this.detail.status)})).catch((()=>{this.detail.status=1===this.detail.status?0:1}))})).catch((()=>{this.detail.status=1===this.detail.status?0:1}))},saveRemark(){re(this.detail.id,this.detail.remark).then((()=>{this.$message({type:"success",message:"备注保存成功"})}))},handleEditLevel(e){this.levelDialogVisible=!0,this.levelForm={id:e.id,currentLevel:e.level,newLevel:e.level,remark:""}},submitLevelChange(){this.levelForm.currentLevel!==this.levelForm.newLevel?(this.levelSubmitting=!0,ne(this.levelForm.id,this.levelForm.newLevel,this.levelForm.remark).then((()=>{this.$message({type:"success",message:"等级修改成功"}),this.levelDialogVisible=!1,this.levelSubmitting=!1,this.getList(),this.dialogVisible&&this.detail.id===this.levelForm.id&&this.getPromoterDetail(this.levelForm.id)})).catch((()=>{this.levelSubmitting=!1}))):this.$message({type:"warning",message:"等级未变更"})},getInvitesList(){this.detail.id&&(this.invitesLoading=!0,(0,de.tS)(this.detail.id,this.inviteLevel,this.invitesQuery).then((e=>{this.invitesList=e.data.list,this.invitesTotal=e.data.pagination.total,this.invitesLoading=!1})).catch((()=>{this.invitesLoading=!1})))},getCommissionsList(){this.detail.id&&(this.commissionsLoading=!0,(0,de.fr)(this.detail.id,this.commissionsQuery).then((e=>{this.commissionsList=e.data.list,this.commissionsTotal=e.data.pagination.total,this.commissionsLoading=!1})).catch((()=>{this.commissionsLoading=!1})))},getWithdrawalsList(){this.detail.id&&(this.withdrawalsLoading=!0,(0,de.nU)(this.detail.id,this.withdrawalsQuery).then((e=>{this.withdrawalsList=e.data.list,this.withdrawalsTotal=e.data.pagination.total,this.withdrawalsLoading=!1})).catch((()=>{this.withdrawalsLoading=!1})))},getLevelTagType(e){const t=["","info","success","warning","danger"];return t[e]||"info"},getLevelName(e){const t=this.levelOptions.find((t=>t.value===e));return t?t.label:"未知等级"},getAccountTypeText(e){const t=["未知","微信","支付宝","银行卡"];return t[e]||"未知"},getWithdrawStatusText(e){const t=["待审核","已通过","已拒绝","已打款"];return t[e]||"未知"},getWithdrawStatusType(e){const t=["info","success","danger","success"];return t[e]||"info"}}};var he=a(1241);const ge=(0,he.A)(me,[["render",ae],["__scopeId","data-v-04266e84"]]),ve=ge}}]);