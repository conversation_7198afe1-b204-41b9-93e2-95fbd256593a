"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[2045],{2045:(e,n,s)=>{s.r(n),s.d(n,{default:()=>l});var a=s(6768);const t={class:"page-container"};function c(e,n,s,c,d,r){return(0,a.uX)(),(0,a.CE)("div",t,n[0]||(n[0]=[(0,a.Lk)("h2",null,"订单列表",-1),(0,a.Lk)("div",{class:"placeholder-content"}," 订单列表功能正在开发中... ",-1)]))}const d={name:"OrderList",data(){return{}}};var r=s(1241);const u=(0,r.A)(d,[["render",c],["__scopeId","data-v-c3fd2cd8"]]),l=u}}]);