"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[6955],{6955:(e,l,a)=>{a.r(l),a.d(l,{default:()=>n});var o=a(6768);const r={class:"app-container"};function t(e,l,a,t,u,d){const i=(0,o.g2)("el-input"),n=(0,o.g2)("el-form-item"),s=(0,o.g2)("el-input-number"),m=(0,o.g2)("el-option"),b=(0,o.g2)("el-select"),p=(0,o.g2)("el-radio"),c=(0,o.g2)("el-radio-group"),f=(0,o.g2)("el-button"),g=(0,o.g2)("el-form"),F=(0,o.g2)("el-card"),k=(0,o.gN)("loading");return(0,o.uX)(),(0,o.CE)("div",r,[(0,o.bF)(F,null,{default:(0,o.k6)((()=>[l[14]||(l[14]=(0,o.Lk)("div",{slot:"header"},[(0,o.Lk)("span",null,"招聘详情")],-1)),(0,o.bo)(((0,o.uX)(),(0,o.CE)("div",null,[(0,o.bF)(g,{ref:"form",model:u.form,rules:u.rules,"label-width":"100px"},{default:(0,o.k6)((()=>[(0,o.bF)(n,{label:"职位名称",prop:"jobTitle"},{default:(0,o.k6)((()=>[(0,o.bF)(i,{modelValue:u.form.jobTitle,"onUpdate:modelValue":l[0]||(l[0]=e=>u.form.jobTitle=e),placeholder:"请输入职位名称"},null,8,["modelValue"])])),_:1}),(0,o.bF)(n,{label:"薪资范围",prop:"salary"},{default:(0,o.k6)((()=>[(0,o.bF)(i,{modelValue:u.form.salary,"onUpdate:modelValue":l[1]||(l[1]=e=>u.form.salary=e),placeholder:"请输入薪资范围"},null,8,["modelValue"])])),_:1}),(0,o.bF)(n,{label:"工作地点",prop:"workLocation"},{default:(0,o.k6)((()=>[(0,o.bF)(i,{modelValue:u.form.workLocation,"onUpdate:modelValue":l[2]||(l[2]=e=>u.form.workLocation=e),placeholder:"请输入工作地点"},null,8,["modelValue"])])),_:1}),(0,o.bF)(n,{label:"招聘人数",prop:"recruitCount"},{default:(0,o.k6)((()=>[(0,o.bF)(s,{modelValue:u.form.recruitCount,"onUpdate:modelValue":l[3]||(l[3]=e=>u.form.recruitCount=e),min:1,max:100},null,8,["modelValue"])])),_:1}),(0,o.bF)(n,{label:"工作经验",prop:"experience"},{default:(0,o.k6)((()=>[(0,o.bF)(b,{modelValue:u.form.experience,"onUpdate:modelValue":l[4]||(l[4]=e=>u.form.experience=e),placeholder:"请选择工作经验"},{default:(0,o.k6)((()=>[(0,o.bF)(m,{label:"无经验",value:"无经验"}),(0,o.bF)(m,{label:"1年以下",value:"1年以下"}),(0,o.bF)(m,{label:"1-3年",value:"1-3年"}),(0,o.bF)(m,{label:"3-5年",value:"3-5年"}),(0,o.bF)(m,{label:"5年以上",value:"5年以上"})])),_:1},8,["modelValue"])])),_:1}),(0,o.bF)(n,{label:"学历要求",prop:"education"},{default:(0,o.k6)((()=>[(0,o.bF)(b,{modelValue:u.form.education,"onUpdate:modelValue":l[5]||(l[5]=e=>u.form.education=e),placeholder:"请选择学历要求"},{default:(0,o.k6)((()=>[(0,o.bF)(m,{label:"不限",value:"不限"}),(0,o.bF)(m,{label:"高中",value:"高中"}),(0,o.bF)(m,{label:"中专",value:"中专"}),(0,o.bF)(m,{label:"大专",value:"大专"}),(0,o.bF)(m,{label:"本科",value:"本科"}),(0,o.bF)(m,{label:"硕士",value:"硕士"}),(0,o.bF)(m,{label:"博士",value:"博士"})])),_:1},8,["modelValue"])])),_:1}),(0,o.bF)(n,{label:"职位描述",prop:"description"},{default:(0,o.k6)((()=>[(0,o.bF)(i,{type:"textarea",modelValue:u.form.description,"onUpdate:modelValue":l[6]||(l[6]=e=>u.form.description=e),rows:5,placeholder:"请输入职位描述"},null,8,["modelValue"])])),_:1}),(0,o.bF)(n,{label:"任职要求",prop:"requirements"},{default:(0,o.k6)((()=>[(0,o.bF)(i,{type:"textarea",modelValue:u.form.requirements,"onUpdate:modelValue":l[7]||(l[7]=e=>u.form.requirements=e),rows:5,placeholder:"请输入任职要求"},null,8,["modelValue"])])),_:1}),(0,o.bF)(n,{label:"公司福利",prop:"benefits"},{default:(0,o.k6)((()=>[(0,o.bF)(i,{type:"textarea",modelValue:u.form.benefits,"onUpdate:modelValue":l[8]||(l[8]=e=>u.form.benefits=e),rows:3,placeholder:"请输入公司福利"},null,8,["modelValue"])])),_:1}),(0,o.bF)(n,{label:"发布状态",prop:"status"},{default:(0,o.k6)((()=>[(0,o.bF)(c,{modelValue:u.form.status,"onUpdate:modelValue":l[9]||(l[9]=e=>u.form.status=e)},{default:(0,o.k6)((()=>[(0,o.bF)(p,{label:"0"},{default:(0,o.k6)((()=>l[10]||(l[10]=[(0,o.eW)("草稿")]))),_:1}),(0,o.bF)(p,{label:"1"},{default:(0,o.k6)((()=>l[11]||(l[11]=[(0,o.eW)("发布")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),(0,o.bF)(n,null,{default:(0,o.k6)((()=>[(0,o.bF)(f,{type:"primary",onClick:d.submitForm},{default:(0,o.k6)((()=>l[12]||(l[12]=[(0,o.eW)("保存")]))),_:1},8,["onClick"]),(0,o.bF)(f,{onClick:d.cancel},{default:(0,o.k6)((()=>l[13]||(l[13]=[(0,o.eW)("取消")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model","rules"])])),[[k,u.loading]])])),_:1})])}const u={name:"RecruitmentDetail",data(){return{loading:!1,isAdd:!1,form:{id:void 0,jobTitle:"",salary:"",workLocation:"",recruitCount:1,experience:"",education:"",description:"",requirements:"",benefits:"",status:"0"},rules:{jobTitle:[{required:!0,message:"职位名称不能为空",trigger:"blur"}],salary:[{required:!0,message:"薪资范围不能为空",trigger:"blur"}],workLocation:[{required:!0,message:"工作地点不能为空",trigger:"blur"}],experience:[{required:!0,message:"请选择工作经验",trigger:"change"}],education:[{required:!0,message:"请选择学历要求",trigger:"change"}],description:[{required:!0,message:"职位描述不能为空",trigger:"blur"}]}}},created(){const e=this.$route.params.id;this.isAdd="0"===e||!e,this.isAdd||this.getInfo(e)},methods:{getInfo(e){this.loading=!0,setTimeout((()=>{"1"===e&&(this.form={id:1,jobTitle:"快递驿站店长",salary:"5000-8000",workLocation:"北京市海淀区中关村",recruitCount:2,experience:"1-3年",education:"大专",description:"负责驿站日常运营管理，包括快递接收、派送、客户服务等工作。",requirements:"有快递行业经验优先，良好的沟通能力和服务意识，能够承受一定的工作压力。",benefits:"五险一金，带薪年假，节日福利，绩效奖金",status:"1"}),this.loading=!1}),500)},submitForm(){this.$refs["form"].validate((e=>{e&&(this.$message({type:"success",message:this.isAdd?"新增成功":"修改成功"}),this.cancel())}))},cancel(){this.$router.push("/recruitment/list")}}};var d=a(1241);const i=(0,d.A)(u,[["render",t],["__scopeId","data-v-01da69ed"]]),n=i}}]);