"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[8591],{8591:(e,l,a)=>{a.r(l),a.d(l,{default:()=>h});var o=a(6768),r=a(4232);const t={class:"page-container"},i={class:"action-container"},d={class:"search-box"},s={class:"button-group"},n={class:"pagination-container"},m={class:"dialog-footer"},u={class:"dialog-footer"};function p(e,l,a,p,b,g){const c=(0,o.g2)("el-input"),h=(0,o.g2)("el-button"),k=(0,o.g2)("el-table-column"),y=(0,o.g2)("el-tag"),F=(0,o.g2)("el-table"),f=(0,o.g2)("el-pagination"),V=(0,o.g2)("el-form-item"),_=(0,o.g2)("el-input-number"),C=(0,o.g2)("el-radio"),w=(0,o.g2)("el-radio-group"),v=(0,o.g2)("el-form"),x=(0,o.g2)("el-dialog"),N=(0,o.g2)("el-tree"),P=(0,o.gN)("loading");return(0,o.uX)(),(0,o.CE)("div",t,[(0,o.Lk)("div",i,[(0,o.Lk)("div",d,[(0,o.bF)(c,{modelValue:b.queryParams.keyword,"onUpdate:modelValue":l[0]||(l[0]=e=>b.queryParams.keyword=e),placeholder:"请输入角色名称",clearable:"",style:{width:"200px"},class:"mr-10"},null,8,["modelValue"]),(0,o.bF)(h,{type:"primary",onClick:g.handleQuery},{default:(0,o.k6)((()=>l[13]||(l[13]=[(0,o.eW)("搜索")]))),_:1},8,["onClick"]),(0,o.bF)(h,{onClick:g.resetQuery},{default:(0,o.k6)((()=>l[14]||(l[14]=[(0,o.eW)("重置")]))),_:1},8,["onClick"])]),(0,o.Lk)("div",s,[(0,o.bF)(h,{type:"primary",onClick:g.handleAdd},{default:(0,o.k6)((()=>l[15]||(l[15]=[(0,o.eW)("新增角色")]))),_:1},8,["onClick"])])]),(0,o.bo)(((0,o.uX)(),(0,o.Wv)(F,{data:b.roleList},{default:(0,o.k6)((()=>[(0,o.bF)(k,{type:"index",label:"#",width:"50"}),(0,o.bF)(k,{prop:"roleName",label:"角色名称","show-overflow-tooltip":""}),(0,o.bF)(k,{prop:"roleKey",label:"角色标识","show-overflow-tooltip":""}),(0,o.bF)(k,{prop:"sort",label:"排序",width:"80"}),(0,o.bF)(k,{prop:"status",label:"状态",width:"100"},{default:(0,o.k6)((e=>[(0,o.bF)(y,{type:"0"===e.row.status?"success":"danger"},{default:(0,o.k6)((()=>[(0,o.eW)((0,r.v_)("0"===e.row.status?"正常":"停用"),1)])),_:2},1032,["type"])])),_:1}),(0,o.bF)(k,{prop:"createTime",label:"创建时间",width:"160"}),(0,o.bF)(k,{label:"操作",width:"260",fixed:"right"},{default:(0,o.k6)((e=>[(0,o.bF)(h,{type:"text",onClick:l=>g.handleEdit(e.row)},{default:(0,o.k6)((()=>l[16]||(l[16]=[(0,o.eW)("修改")]))),_:2},1032,["onClick"]),(0,o.bF)(h,{type:"text",onClick:l=>g.handlePermission(e.row)},{default:(0,o.k6)((()=>l[17]||(l[17]=[(0,o.eW)("权限")]))),_:2},1032,["onClick"]),"admin"!==e.row.roleKey?((0,o.uX)(),(0,o.Wv)(h,{key:0,type:"text",onClick:l=>g.handleDelete(e.row)},{default:(0,o.k6)((()=>l[18]||(l[18]=[(0,o.eW)("删除")]))),_:2},1032,["onClick"])):(0,o.Q3)("",!0)])),_:1})])),_:1},8,["data"])),[[P,b.loading]]),(0,o.Lk)("div",n,[(0,o.bF)(f,{"current-page":b.queryParams.pageNum,"onUpdate:currentPage":l[1]||(l[1]=e=>b.queryParams.pageNum=e),"page-size":b.queryParams.pageSize,"onUpdate:pageSize":l[2]||(l[2]=e=>b.queryParams.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:b.total,onSizeChange:g.handleSizeChange,onCurrentChange:g.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])]),(0,o.bF)(x,{title:b.dialogTitle,modelValue:b.dialogVisible,"onUpdate:modelValue":l[9]||(l[9]=e=>b.dialogVisible=e),width:"500px","append-to-body":""},{footer:(0,o.k6)((()=>[(0,o.Lk)("div",m,[(0,o.bF)(h,{onClick:l[8]||(l[8]=e=>b.dialogVisible=!1)},{default:(0,o.k6)((()=>l[21]||(l[21]=[(0,o.eW)("取 消")]))),_:1}),(0,o.bF)(h,{type:"primary",onClick:g.submitForm},{default:(0,o.k6)((()=>l[22]||(l[22]=[(0,o.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,o.k6)((()=>[(0,o.bF)(v,{ref:"roleForm",model:b.roleForm,rules:b.rules,"label-width":"100px"},{default:(0,o.k6)((()=>[(0,o.bF)(V,{label:"角色名称",prop:"roleName"},{default:(0,o.k6)((()=>[(0,o.bF)(c,{modelValue:b.roleForm.roleName,"onUpdate:modelValue":l[3]||(l[3]=e=>b.roleForm.roleName=e),placeholder:"请输入角色名称"},null,8,["modelValue"])])),_:1}),(0,o.bF)(V,{label:"角色标识",prop:"roleKey"},{default:(0,o.k6)((()=>[(0,o.bF)(c,{modelValue:b.roleForm.roleKey,"onUpdate:modelValue":l[4]||(l[4]=e=>b.roleForm.roleKey=e),placeholder:"请输入角色标识"},null,8,["modelValue"])])),_:1}),(0,o.bF)(V,{label:"排序",prop:"sort"},{default:(0,o.k6)((()=>[(0,o.bF)(_,{modelValue:b.roleForm.sort,"onUpdate:modelValue":l[5]||(l[5]=e=>b.roleForm.sort=e),min:0,max:999,"controls-position":"right"},null,8,["modelValue"])])),_:1}),(0,o.bF)(V,{label:"状态",prop:"status"},{default:(0,o.k6)((()=>[(0,o.bF)(w,{modelValue:b.roleForm.status,"onUpdate:modelValue":l[6]||(l[6]=e=>b.roleForm.status=e)},{default:(0,o.k6)((()=>[(0,o.bF)(C,{label:"0"},{default:(0,o.k6)((()=>l[19]||(l[19]=[(0,o.eW)("正常")]))),_:1}),(0,o.bF)(C,{label:"1"},{default:(0,o.k6)((()=>l[20]||(l[20]=[(0,o.eW)("停用")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),(0,o.bF)(V,{label:"备注",prop:"remark"},{default:(0,o.k6)((()=>[(0,o.bF)(c,{modelValue:b.roleForm.remark,"onUpdate:modelValue":l[7]||(l[7]=e=>b.roleForm.remark=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"]),(0,o.bF)(x,{title:"分配权限",modelValue:b.permDialogVisible,"onUpdate:modelValue":l[12]||(l[12]=e=>b.permDialogVisible=e),width:"600px","append-to-body":""},{footer:(0,o.k6)((()=>[(0,o.Lk)("div",u,[(0,o.bF)(h,{onClick:l[11]||(l[11]=e=>b.permDialogVisible=!1)},{default:(0,o.k6)((()=>l[23]||(l[23]=[(0,o.eW)("取 消")]))),_:1}),(0,o.bF)(h,{type:"primary",onClick:g.submitPermission},{default:(0,o.k6)((()=>l[24]||(l[24]=[(0,o.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,o.k6)((()=>[(0,o.bF)(v,{"label-width":"80px"},{default:(0,o.k6)((()=>[(0,o.bF)(V,{label:"角色名称"},{default:(0,o.k6)((()=>[(0,o.bF)(c,{modelValue:b.currentRole.roleName,"onUpdate:modelValue":l[10]||(l[10]=e=>b.currentRole.roleName=e),readonly:""},null,8,["modelValue"])])),_:1}),(0,o.bF)(V,{label:"权限分配"},{default:(0,o.k6)((()=>[(0,o.bF)(N,{ref:"menuTree",data:b.menuOptions,props:{label:"name",children:"children"},"show-checkbox":"","node-key":"id","empty-text":"加载中，请稍候","default-expand-all":!0},null,8,["data"])])),_:1})])),_:1})])),_:1},8,["modelValue"])])}const b={name:"RoleManagement",data(){return{loading:!1,ids:[],total:0,roleList:[{id:1,roleName:"超级管理员",roleKey:"admin",sort:1,status:"0",createTime:"2023-01-01 00:00:00",remark:"超级管理员拥有所有权限"},{id:2,roleName:"普通管理员",roleKey:"common",sort:2,status:"0",createTime:"2023-01-01 00:00:00",remark:"普通管理员"},{id:3,roleName:"站点管理员",roleKey:"station",sort:3,status:"0",createTime:"2023-01-02 00:00:00",remark:"站点管理员只能管理站点信息"}],dialogTitle:"",dialogVisible:!1,permDialogVisible:!1,currentRole:{},queryParams:{pageNum:1,pageSize:10,keyword:""},roleForm:{id:void 0,roleName:void 0,roleKey:void 0,sort:0,status:"0",remark:void 0},rules:{roleName:[{required:!0,message:"角色名称不能为空",trigger:"blur"}],roleKey:[{required:!0,message:"角色标识不能为空",trigger:"blur"}],sort:[{required:!0,message:"显示顺序不能为空",trigger:"blur"}]},menuOptions:[{id:1,name:"系统管理",children:[{id:11,name:"用户管理"},{id:12,name:"角色管理"},{id:13,name:"菜单管理"}]},{id:2,name:"站点管理",children:[{id:21,name:"站点列表"},{id:22,name:"站点审核"}]},{id:3,name:"订单管理",children:[{id:31,name:"订单列表"},{id:32,name:"退款管理"}]}]}},methods:{handleQuery(){this.queryParams.pageNum=1,console.log("查询参数：",this.queryParams)},resetQuery(){this.queryParams.keyword="",this.handleQuery()},handleAdd(){this.dialogTitle="添加角色",this.dialogVisible=!0,this.resetForm()},handleEdit(e){this.dialogTitle="修改角色",this.dialogVisible=!0,this.roleForm={...e}},handlePermission(e){this.currentRole=e,this.permDialogVisible=!0},handleDelete(e){this.$confirm('是否确认删除角色名称为"'+e.roleName+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{console.log("删除角色ID：",e.id)})).catch((()=>{}))},submitForm(){this.$refs.roleForm.validate((e=>{e&&(this.roleForm.id?console.log("更新角色：",this.roleForm):console.log("新增角色：",this.roleForm),this.dialogVisible=!1)}))},submitPermission(){const e=this.$refs.menuTree.getCheckedKeys();console.log("角色ID：",this.currentRole.id),console.log("分配的权限：",e),this.permDialogVisible=!1},resetForm(){this.roleForm={id:void 0,roleName:void 0,roleKey:void 0,sort:0,status:"0",remark:void 0}},handleSizeChange(e){this.queryParams.pageSize=e,this.handleQuery()},handleCurrentChange(e){this.queryParams.pageNum=e,this.handleQuery()}}};var g=a(1241);const c=(0,g.A)(b,[["render",p],["__scopeId","data-v-04c44785"]]),h=c}}]);