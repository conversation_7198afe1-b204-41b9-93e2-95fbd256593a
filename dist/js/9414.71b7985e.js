"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[9414],{9414:(e,a,l)=>{l.r(a),l.d(a,{default:()=>z});var t=l(6768),o=l(4232);const r={class:"app-container"},d={class:"generator-content"},n={class:"settings-panel"},i={class:"user-option"},c={class:"user-phone"},s={class:"preview-panel"},u={class:"qrcode-preview"},p={key:0,class:"qrcode-container"},m=["src"],g={class:"qrcode-info"},b={key:0},v={class:"qrcode-actions"},_={key:1,class:"empty-preview"},k={class:"card-header"},F={class:"qrcode-thumbnail"},h={class:"pagination-container"},f={class:"qrcode-detail"},y={class:"qrcode-detail-image"},C={class:"qrcode-detail-info"},w={class:"qrcode-detail-actions"};function q(e,a,l,q,L,V){const x=(0,t.g2)("el-avatar"),W=(0,t.g2)("el-option"),D=(0,t.g2)("el-select"),Q=(0,t.g2)("el-form-item"),T=(0,t.g2)("el-radio-button"),z=(0,t.g2)("el-radio-group"),R=(0,t.g2)("el-input-number"),U=(0,t.g2)("el-date-picker"),K=(0,t.g2)("el-color-picker"),S=(0,t.g2)("el-switch"),I=(0,t.g2)("el-button"),X=(0,t.g2)("el-form"),P=(0,t.g2)("Download"),N=(0,t.g2)("el-icon"),O=(0,t.g2)("Check"),E=(0,t.g2)("Picture"),Y=(0,t.g2)("el-card"),M=(0,t.g2)("Search"),j=(0,t.g2)("el-input"),B=(0,t.g2)("el-image"),A=(0,t.g2)("el-table-column"),G=(0,t.g2)("el-tag"),H=(0,t.g2)("el-table"),J=(0,t.g2)("el-pagination"),Z=(0,t.g2)("el-descriptions-item"),$=(0,t.g2)("el-descriptions"),ee=(0,t.g2)("el-dialog"),ae=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",r,[(0,t.bF)(Y,{class:"qrcode-generator-card",shadow:"hover"},{header:(0,t.k6)((()=>a[13]||(a[13]=[(0,t.Lk)("div",{class:"card-header"},[(0,t.Lk)("span",null,"推广二维码生成器")],-1)]))),default:(0,t.k6)((()=>[(0,t.Lk)("div",d,[(0,t.Lk)("div",n,[(0,t.bF)(X,{model:q.qrcodeForm,"label-width":"100px"},{default:(0,t.k6)((()=>[(0,t.bF)(Q,{label:"推广员"},{default:(0,t.k6)((()=>[(0,t.bF)(D,{modelValue:q.qrcodeForm.promoter,"onUpdate:modelValue":a[0]||(a[0]=e=>q.qrcodeForm.promoter=e),filterable:"",placeholder:"选择推广员",style:{width:"100%"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(q.promoterOptions,(e=>((0,t.uX)(),(0,t.Wv)(W,{key:e.id,label:e.name,value:e.id},{default:(0,t.k6)((()=>[(0,t.Lk)("div",i,[(0,t.bF)(x,{src:e.avatar,size:30},null,8,["src"]),(0,t.Lk)("div",null,[(0,t.Lk)("div",null,(0,o.v_)(e.name),1),(0,t.Lk)("div",c,(0,o.v_)(e.phone),1)])])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(Q,{label:"推广类型"},{default:(0,t.k6)((()=>[(0,t.bF)(z,{modelValue:q.qrcodeForm.type,"onUpdate:modelValue":a[1]||(a[1]=e=>q.qrcodeForm.type=e)},{default:(0,t.k6)((()=>[(0,t.bF)(T,{label:"user"},{default:(0,t.k6)((()=>a[14]||(a[14]=[(0,t.eW)("用户推广")]))),_:1}),(0,t.bF)(T,{label:"station"},{default:(0,t.k6)((()=>a[15]||(a[15]=[(0,t.eW)("驿站推广")]))),_:1}),(0,t.bF)(T,{label:"service"},{default:(0,t.k6)((()=>a[16]||(a[16]=[(0,t.eW)("服务推广")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),"service"===q.qrcodeForm.type?((0,t.uX)(),(0,t.Wv)(Q,{key:0,label:"推广服务"},{default:(0,t.k6)((()=>[(0,t.bF)(D,{modelValue:q.qrcodeForm.serviceId,"onUpdate:modelValue":a[2]||(a[2]=e=>q.qrcodeForm.serviceId=e),filterable:"",placeholder:"选择推广服务",style:{width:"100%"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(q.serviceOptions,(e=>((0,t.uX)(),(0,t.Wv)(W,{key:e.id,label:e.name,value:e.id},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(e.name)+" (¥"+(0,o.v_)(e.price)+") ",1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})):(0,t.Q3)("",!0),(0,t.bF)(Q,{label:"佣金比例"},{default:(0,t.k6)((()=>[(0,t.bF)(R,{modelValue:q.qrcodeForm.commissionRate,"onUpdate:modelValue":a[3]||(a[3]=e=>q.qrcodeForm.commissionRate=e),min:0,max:30,step:.5,precision:1,style:{width:"180px"}},null,8,["modelValue"]),a[17]||(a[17]=(0,t.Lk)("span",{class:"commission-rate-suffix"},"%",-1))])),_:1}),(0,t.bF)(Q,{label:"过期时间"},{default:(0,t.k6)((()=>[(0,t.bF)(U,{modelValue:q.qrcodeForm.expireDate,"onUpdate:modelValue":a[4]||(a[4]=e=>q.qrcodeForm.expireDate=e),type:"date",placeholder:"选择过期日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD","disabled-date":q.disabledDate,style:{width:"100%"}},null,8,["modelValue","disabled-date"])])),_:1}),(0,t.bF)(Q,{label:"背景颜色"},{default:(0,t.k6)((()=>[(0,t.bF)(K,{modelValue:q.qrcodeForm.bgColor,"onUpdate:modelValue":a[5]||(a[5]=e=>q.qrcodeForm.bgColor=e),"show-alpha":""},null,8,["modelValue"])])),_:1}),(0,t.bF)(Q,{label:"前景颜色"},{default:(0,t.k6)((()=>[(0,t.bF)(K,{modelValue:q.qrcodeForm.fgColor,"onUpdate:modelValue":a[6]||(a[6]=e=>q.qrcodeForm.fgColor=e)},null,8,["modelValue"])])),_:1}),(0,t.bF)(Q,{label:"添加Logo"},{default:(0,t.k6)((()=>[(0,t.bF)(S,{modelValue:q.qrcodeForm.addLogo,"onUpdate:modelValue":a[7]||(a[7]=e=>q.qrcodeForm.addLogo=e)},null,8,["modelValue"])])),_:1}),(0,t.bF)(Q,null,{default:(0,t.k6)((()=>[(0,t.bF)(I,{type:"primary",onClick:q.generateQrcode,loading:q.generating},{default:(0,t.k6)((()=>a[18]||(a[18]=[(0,t.eW)("生成二维码")]))),_:1},8,["onClick","loading"]),(0,t.bF)(I,{onClick:q.resetForm},{default:(0,t.k6)((()=>a[19]||(a[19]=[(0,t.eW)("重置")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])]),(0,t.Lk)("div",s,[(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",u,[q.qrcodeUrl?((0,t.uX)(),(0,t.CE)("div",p,[(0,t.Lk)("img",{src:q.qrcodeUrl,alt:"推广二维码",class:"qrcode-image"},null,8,m),(0,t.Lk)("div",g,[(0,t.Lk)("div",null,"推广员: "+(0,o.v_)(q.getPromoterName(q.qrcodeForm.promoter)),1),(0,t.Lk)("div",null,"类型: "+(0,o.v_)(q.getTypeName(q.qrcodeForm.type)),1),"service"===q.qrcodeForm.type?((0,t.uX)(),(0,t.CE)("div",b,"服务: "+(0,o.v_)(q.getServiceName(q.qrcodeForm.serviceId)),1)):(0,t.Q3)("",!0),(0,t.Lk)("div",null,"佣金比例: "+(0,o.v_)(q.qrcodeForm.commissionRate)+"%",1),(0,t.Lk)("div",null,"过期时间: "+(0,o.v_)(q.qrcodeForm.expireDate||"永久有效"),1)]),(0,t.Lk)("div",v,[(0,t.bF)(I,{type:"primary",size:"small",onClick:q.downloadQrcode},{default:(0,t.k6)((()=>[(0,t.bF)(N,null,{default:(0,t.k6)((()=>[(0,t.bF)(P)])),_:1}),a[20]||(a[20]=(0,t.eW)(" 下载 "))])),_:1},8,["onClick"]),(0,t.bF)(I,{type:"success",size:"small",onClick:q.saveQrcode},{default:(0,t.k6)((()=>[(0,t.bF)(N,null,{default:(0,t.k6)((()=>[(0,t.bF)(O)])),_:1}),a[21]||(a[21]=(0,t.eW)(" 保存 "))])),_:1},8,["onClick"])])])):((0,t.uX)(),(0,t.CE)("div",_,[(0,t.bF)(N,{size:48},{default:(0,t.k6)((()=>[(0,t.bF)(E)])),_:1}),a[22]||(a[22]=(0,t.Lk)("p",null,"请设置参数生成二维码",-1))]))])),[[ae,q.generating]])])])])),_:1}),(0,t.bF)(Y,{class:"qrcode-list-card",shadow:"hover"},{header:(0,t.k6)((()=>[(0,t.Lk)("div",k,[a[24]||(a[24]=(0,t.Lk)("span",null,"已生成的推广二维码",-1)),(0,t.Lk)("div",null,[(0,t.bF)(j,{modelValue:q.searchQuery,"onUpdate:modelValue":a[8]||(a[8]=e=>q.searchQuery=e),placeholder:"搜索推广员",clearable:"",onClear:q.getQrcodeList,style:{width:"200px","margin-right":"10px"}},{suffix:(0,t.k6)((()=>[(0,t.bF)(N,{onClick:q.getQrcodeList},{default:(0,t.k6)((()=>[(0,t.bF)(M)])),_:1},8,["onClick"])])),_:1},8,["modelValue","onClear"]),(0,t.bF)(D,{modelValue:q.filterType,"onUpdate:modelValue":a[9]||(a[9]=e=>q.filterType=e),placeholder:"推广类型",clearable:"",onChange:q.getQrcodeList,style:{width:"130px","margin-right":"10px"}},{default:(0,t.k6)((()=>[(0,t.bF)(W,{label:"用户推广",value:"user"}),(0,t.bF)(W,{label:"驿站推广",value:"station"}),(0,t.bF)(W,{label:"服务推广",value:"service"})])),_:1},8,["modelValue","onChange"]),(0,t.bF)(I,{type:"primary",onClick:q.getQrcodeList},{default:(0,t.k6)((()=>a[23]||(a[23]=[(0,t.eW)("查询")]))),_:1},8,["onClick"])])])])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(H,{data:q.qrcodeList,border:"",style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(A,{label:"二维码",width:"100"},{default:(0,t.k6)((({row:e})=>[(0,t.Lk)("div",F,[(0,t.bF)(B,{src:e.url,"preview-src-list":[e.url],fit:"contain","z-index":3e3,style:{width:"60px",height:"60px"}},null,8,["src","preview-src-list"])])])),_:1}),(0,t.bF)(A,{prop:"promoter_name",label:"推广员",width:"150"}),(0,t.bF)(A,{label:"推广类型",width:"100"},{default:(0,t.k6)((({row:e})=>[(0,t.bF)(G,{type:q.getTypeTagType(e.type)},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(q.getTypeName(e.type)),1)])),_:2},1032,["type"])])),_:1}),(0,t.bF)(A,{prop:"target_name",label:"推广对象",width:"180"}),(0,t.bF)(A,{prop:"commission_rate",label:"佣金比例",width:"100"},{default:(0,t.k6)((({row:e})=>[(0,t.eW)((0,o.v_)(e.commission_rate)+"% ",1)])),_:1}),(0,t.bF)(A,{prop:"created_at",label:"创建时间",width:"150"}),(0,t.bF)(A,{prop:"expire_date",label:"过期时间",width:"150"},{default:(0,t.k6)((({row:e})=>[(0,t.eW)((0,o.v_)(e.expire_date||"永久有效"),1)])),_:1}),(0,t.bF)(A,{prop:"scan_count",label:"扫码次数",width:"100",sortable:""}),(0,t.bF)(A,{prop:"conversion_count",label:"转化数",width:"100",sortable:""}),(0,t.bF)(A,{label:"操作",width:"200",fixed:"right"},{default:(0,t.k6)((({row:e})=>[(0,t.bF)(I,{type:"primary",size:"small",onClick:a=>q.handleView(e)},{default:(0,t.k6)((()=>a[25]||(a[25]=[(0,t.eW)("查看")]))),_:2},1032,["onClick"]),(0,t.bF)(I,{type:"danger",size:"small",onClick:a=>q.handleDelete(e)},{default:(0,t.k6)((()=>a[26]||(a[26]=[(0,t.eW)("删除")]))),_:2},1032,["onClick"]),(0,t.bF)(I,{type:"success",size:"small",plain:"",onClick:a=>q.handleDownload(e)},{default:(0,t.k6)((()=>a[27]||(a[27]=[(0,t.eW)("下载")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[ae,q.loading]]),(0,t.Lk)("div",h,[(0,t.bF)(J,{"current-page":q.currentPage,"onUpdate:currentPage":a[10]||(a[10]=e=>q.currentPage=e),"page-size":q.pageSize,"onUpdate:pageSize":a[11]||(a[11]=e=>q.pageSize=e),"page-sizes":[10,20,50,100],background:"",layout:"total, sizes, prev, pager, next, jumper",total:q.total,onSizeChange:q.handleSizeChange,onCurrentChange:q.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])])),_:1}),(0,t.bF)(ee,{modelValue:q.dialogVisible,"onUpdate:modelValue":a[12]||(a[12]=e=>q.dialogVisible=e),title:"二维码详情",width:"500px"},{default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",f,[(0,t.Lk)("div",y,[(0,t.bF)(B,{src:q.currentQrcode.url,fit:"contain",style:{width:"200px",height:"200px"}},null,8,["src"])]),(0,t.Lk)("div",C,[(0,t.bF)($,{column:1,border:""},{default:(0,t.k6)((()=>[(0,t.bF)(Z,{label:"推广员"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(q.currentQrcode.promoter_name),1)])),_:1}),(0,t.bF)(Z,{label:"推广类型"},{default:(0,t.k6)((()=>[(0,t.bF)(G,{type:q.getTypeTagType(q.currentQrcode.type)},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(q.getTypeName(q.currentQrcode.type)),1)])),_:1},8,["type"])])),_:1}),(0,t.bF)(Z,{label:"推广对象"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(q.currentQrcode.target_name),1)])),_:1}),(0,t.bF)(Z,{label:"佣金比例"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(q.currentQrcode.commission_rate)+"%",1)])),_:1}),(0,t.bF)(Z,{label:"创建时间"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(q.currentQrcode.created_at),1)])),_:1}),(0,t.bF)(Z,{label:"过期时间"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(q.currentQrcode.expire_date||"永久有效"),1)])),_:1}),(0,t.bF)(Z,{label:"扫码次数"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(q.currentQrcode.scan_count),1)])),_:1}),(0,t.bF)(Z,{label:"转化数"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(q.currentQrcode.conversion_count),1)])),_:1}),(0,t.bF)(Z,{label:"转化率"},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(q.calculateConversionRate(q.currentQrcode))+"%",1)])),_:1})])),_:1})]),(0,t.Lk)("div",w,[(0,t.bF)(I,{type:"primary",onClick:q.handleDetailDownload},{default:(0,t.k6)((()=>a[28]||(a[28]=[(0,t.eW)("下载二维码")]))),_:1},8,["onClick"]),(0,t.bF)(I,{type:"danger",onClick:q.handleDetailDelete},{default:(0,t.k6)((()=>a[29]||(a[29]=[(0,t.eW)("删除二维码")]))),_:1},8,["onClick"])])])),[[ae,q.detailLoading]])])),_:1},8,["modelValue"])])}var L=l(144),V=l(1219),x=l(2933),W=l(7477);const D={name:"PromotionCodeManager",components:{Picture:W.Picture,Download:W.Download,Check:W.Check,Search:W.Search},setup(){const e=(0,L.Kh)({promoter:"",type:"user",serviceId:"",commissionRate:5,expireDate:"",bgColor:"#FFFFFF",fgColor:"#000000",addLogo:!0}),a=(0,L.KR)(!1),l=(0,L.KR)(""),o=(0,L.KR)(!1),r=(0,L.KR)(!1),d=(0,L.KR)(!1),n=(0,L.KR)({}),i=(0,L.KR)(""),c=(0,L.KR)(""),s=(0,L.KR)(1),u=(0,L.KR)(10),p=(0,L.KR)(0),m=[{id:1,name:"张三",phone:"13811112222",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{id:2,name:"李四",phone:"13922223333",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{id:3,name:"王五",phone:"13933334444",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"},{id:4,name:"赵六",phone:"13944445555",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"}],g=[{id:1,name:"快递代取",price:3.5},{id:2,name:"外卖代取",price:5},{id:3,name:"保洁服务",price:80},{id:4,name:"上门维修",price:100}],b=[{id:1,url:"https://imgse.com/i/p9T2O4s",promoter_id:1,promoter_name:"张三",type:"user",target_id:null,target_name:"用户注册",commission_rate:10,created_at:"2023-05-01 09:30:00",expire_date:"2023-12-31",scan_count:256,conversion_count:43},{id:2,url:"https://imgse.com/i/p9T2O4s",promoter_id:2,promoter_name:"李四",type:"station",target_id:5,target_name:"中关村驿站",commission_rate:15,created_at:"2023-05-05 14:20:00",expire_date:null,scan_count:128,conversion_count:25},{id:3,url:"https://imgse.com/i/p9T2O4s",promoter_id:1,promoter_name:"张三",type:"service",target_id:1,target_name:"快递代取",commission_rate:8,created_at:"2023-05-10 11:45:00",expire_date:"2023-08-31",scan_count:89,conversion_count:32}],v=(0,L.KR)([...b]),_=e=>e.scan_count?(e.conversion_count/e.scan_count*100).toFixed(2):0,k=e=>{const a=m.find((a=>a.id===e));return a?a.name:"未指定"},F=e=>{const a={user:"用户推广",station:"驿站推广",service:"服务推广"};return a[e]||"未知"},h=e=>{const a={user:"primary",station:"success",service:"warning"};return a[e]||"info"},f=e=>{const a=g.find((a=>a.id===parseInt(e)));return a?a.name:"未指定"},y=e=>e.getTime()<Date.now(),C=()=>{e.promoter?"service"!==e.type||e.serviceId?(a.value=!0,setTimeout((()=>{l.value="https://imgse.com/i/p9T2O4s",a.value=!1,V.nk.success("二维码生成成功")}),1e3)):V.nk.warning("请选择推广服务"):V.nk.warning("请选择推广员")},w=()=>{Object.assign(e,{promoter:"",type:"user",serviceId:"",commissionRate:5,expireDate:"",bgColor:"#FFFFFF",fgColor:"#000000",addLogo:!0}),l.value=""},q=()=>{V.nk.success("二维码下载成功")},W=()=>{const a={id:v.value.length+1,url:l.value,promoter_id:e.promoter,promoter_name:k(e.promoter),type:e.type,target_id:"service"===e.type?e.serviceId:null,target_name:"service"===e.type?f(e.serviceId):"user"===e.type?"用户注册":"驿站推广",commission_rate:e.commissionRate,created_at:(new Date).toLocaleString(),expire_date:e.expireDate,scan_count:0,conversion_count:0};v.value.unshift(a),p.value++,V.nk.success("二维码保存成功"),w()},D=()=>{o.value=!0,setTimeout((()=>{let e=[...b];i.value&&(e=e.filter((e=>e.promoter_name.includes(i.value)))),c.value&&(e=e.filter((e=>e.type===c.value))),v.value=e,p.value=e.length,o.value=!1}),500)},Q=e=>{d.value=!0,n.value={...e},r.value=!0,setTimeout((()=>{d.value=!1}),300)},T=e=>{x.s.confirm("确定要删除这个推广二维码吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{v.value=v.value.filter((a=>a.id!==e.id)),p.value--,(0,V.nk)({type:"success",message:"删除成功"})})).catch((()=>{}))},z=e=>{V.nk.success("二维码下载成功")},R=()=>{V.nk.success("二维码下载成功")},U=()=>{T(n.value),r.value=!1},K=e=>{u.value=e,D()},S=e=>{s.value=e,D()};return(0,t.sV)((()=>{D()})),{qrcodeForm:e,generating:a,qrcodeUrl:l,promoterOptions:m,serviceOptions:g,loading:o,qrcodeList:v,dialogVisible:r,detailLoading:d,currentQrcode:n,searchQuery:i,filterType:c,currentPage:s,pageSize:u,total:p,getPromoterName:k,getTypeName:F,getTypeTagType:h,getServiceName:f,disabledDate:y,generateQrcode:C,resetForm:w,downloadQrcode:q,saveQrcode:W,getQrcodeList:D,handleView:Q,handleDelete:T,handleDownload:z,handleDetailDownload:R,handleDetailDelete:U,handleSizeChange:K,handleCurrentChange:S,calculateConversionRate:_}}};var Q=l(1241);const T=(0,Q.A)(D,[["render",q],["__scopeId","data-v-6c06c1cc"]]),z=T}}]);