"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[2977],{2977:(t,a,e)=>{e.r(a),e.d(a,{default:()=>M});var s=e(6768),i=e(4232);const r={class:"dashboard-container"},o={class:"welcome-banner"},d={class:"welcome-content"},c={class:"welcome-text"},n={class:"current-time"},l={class:"stats-cards"},h={class:"stat-card users"},v={class:"stat-content"},u={class:"stat-value"},m={class:"stat-compare"},p={class:"up"},k={class:"stat-card stations"},y={class:"stat-content"},L={class:"stat-value"},C={class:"stat-compare"},b={class:"up"},g={class:"stat-card orders"},w={class:"stat-content"},f={class:"stat-value"},D={class:"stat-compare"},x={class:"up"},S={class:"stat-card revenue"},_={class:"stat-content"},P={class:"stat-value"},z={class:"stat-compare"},F={class:"up"},T={class:"chart-container"},W={class:"chart-card wide"},A={class:"chart-header"},G={class:"chart-actions"};function I(t,a,e,I,E,O){const B=(0,s.g2)("el-button"),$=(0,s.g2)("el-button-group");return(0,s.uX)(),(0,s.CE)("div",r,[(0,s.Lk)("div",o,[(0,s.Lk)("div",d,[(0,s.Lk)("div",c,[a[3]||(a[3]=(0,s.Lk)("h1",null,"欢迎回来！",-1)),a[4]||(a[4]=(0,s.Lk)("p",null,"驿站帮Pro后台管理系统 - 让管理更简单高效",-1)),(0,s.Lk)("div",n,(0,i.v_)(E.currentTime),1)]),a[5]||(a[5]=(0,s.Lk)("div",{class:"welcome-illustration"},[(0,s.Lk)("i",{class:"el-icon-data-analysis welcome-icon"})],-1))])]),(0,s.Lk)("div",l,[(0,s.Lk)("div",h,[a[8]||(a[8]=(0,s.Lk)("div",{class:"stat-icon"},[(0,s.Lk)("i",{class:"el-icon-user-solid"})],-1)),(0,s.Lk)("div",v,[a[7]||(a[7]=(0,s.Lk)("div",{class:"stat-title"},"总用户数",-1)),(0,s.Lk)("div",u,(0,i.v_)(E.dashboardData.totalUsers||1256),1),(0,s.Lk)("div",m,[a[6]||(a[6]=(0,s.eW)("较昨日 ")),(0,s.Lk)("span",p,"+"+(0,i.v_)(E.dashboardData.userGrowth||18),1)])])]),(0,s.Lk)("div",k,[a[11]||(a[11]=(0,s.Lk)("div",{class:"stat-icon"},[(0,s.Lk)("i",{class:"el-icon-s-shop"})],-1)),(0,s.Lk)("div",y,[a[10]||(a[10]=(0,s.Lk)("div",{class:"stat-title"},"驿站数量",-1)),(0,s.Lk)("div",L,(0,i.v_)(E.dashboardData.totalStations||358),1),(0,s.Lk)("div",C,[a[9]||(a[9]=(0,s.eW)("较昨日 ")),(0,s.Lk)("span",b,"+"+(0,i.v_)(E.dashboardData.stationGrowth||5),1)])])]),(0,s.Lk)("div",g,[a[14]||(a[14]=(0,s.Lk)("div",{class:"stat-icon"},[(0,s.Lk)("i",{class:"el-icon-s-order"})],-1)),(0,s.Lk)("div",w,[a[13]||(a[13]=(0,s.Lk)("div",{class:"stat-title"},"订单总数",-1)),(0,s.Lk)("div",f,(0,i.v_)(E.dashboardData.totalOrders||6832),1),(0,s.Lk)("div",D,[a[12]||(a[12]=(0,s.eW)("较昨日 ")),(0,s.Lk)("span",x,"+"+(0,i.v_)(E.dashboardData.orderGrowth||43),1)])])]),(0,s.Lk)("div",S,[a[17]||(a[17]=(0,s.Lk)("div",{class:"stat-icon"},[(0,s.Lk)("i",{class:"el-icon-money"})],-1)),(0,s.Lk)("div",_,[a[16]||(a[16]=(0,s.Lk)("div",{class:"stat-title"},"总收入(元)",-1)),(0,s.Lk)("div",P,(0,i.v_)(O.formatMoney(E.dashboardData.totalRevenue||235698.56)),1),(0,s.Lk)("div",z,[a[15]||(a[15]=(0,s.eW)("较昨日 ")),(0,s.Lk)("span",F,"+"+(0,i.v_)(O.formatMoney(E.dashboardData.revenueGrowth||1268.32)),1)])])])]),(0,s.Lk)("div",T,[(0,s.Lk)("div",W,[(0,s.Lk)("div",A,[a[21]||(a[21]=(0,s.Lk)("h3",null,[(0,s.Lk)("i",{class:"el-icon-trend-charts"}),(0,s.eW)(" 订单走势分析")],-1)),(0,s.Lk)("div",G,[(0,s.bF)($,null,{default:(0,s.k6)((()=>[(0,s.bF)(B,{size:"small",type:"7d"===E.chartPeriod?"primary":"",onClick:a[0]||(a[0]=t=>O.changeChartPeriod("7d"))},{default:(0,s.k6)((()=>a[18]||(a[18]=[(0,s.eW)("7天")]))),_:1},8,["type"]),(0,s.bF)(B,{size:"small",type:"30d"===E.chartPeriod?"primary":"",onClick:a[1]||(a[1]=t=>O.changeChartPeriod("30d"))},{default:(0,s.k6)((()=>a[19]||(a[19]=[(0,s.eW)("30天")]))),_:1},8,["type"]),(0,s.bF)(B,{size:"small",type:"90d"===E.chartPeriod?"primary":"",onClick:a[2]||(a[2]=t=>O.changeChartPeriod("90d"))},{default:(0,s.k6)((()=>a[20]||(a[20]=[(0,s.eW)("90天")]))),_:1},8,["type"])])),_:1})])]),a[22]||(a[22]=(0,s.Lk)("div",{id:"orderTrendChart",style:{height:"350px"}},null,-1))]),a[23]||(a[23]=(0,s.Lk)("div",{class:"chart-card"},[(0,s.Lk)("div",{class:"chart-header"},[(0,s.Lk)("h3",null,[(0,s.Lk)("i",{class:"el-icon-pie-chart"}),(0,s.eW)(" 驿站类型分布")])]),(0,s.Lk)("div",{id:"stationDistChart",style:{height:"350px"}})],-1))]),a[24]||(a[24]=(0,s.Fv)('<div class="quick-actions" data-v-54c6328a><div class="action-card" data-v-54c6328a><div class="action-icon" data-v-54c6328a><i class="el-icon-plus" data-v-54c6328a></i></div><div class="action-content" data-v-54c6328a><h4 data-v-54c6328a>新增驿站</h4><p data-v-54c6328a>快速添加新的驿站点</p></div></div><div class="action-card" data-v-54c6328a><div class="action-icon" data-v-54c6328a><i class="el-icon-view" data-v-54c6328a></i></div><div class="action-content" data-v-54c6328a><h4 data-v-54c6328a>订单管理</h4><p data-v-54c6328a>查看和处理订单信息</p></div></div><div class="action-card" data-v-54c6328a><div class="action-icon" data-v-54c6328a><i class="el-icon-s-data" data-v-54c6328a></i></div><div class="action-content" data-v-54c6328a><h4 data-v-54c6328a>数据统计</h4><p data-v-54c6328a>查看详细的数据分析</p></div></div><div class="action-card" data-v-54c6328a><div class="action-icon" data-v-54c6328a><i class="el-icon-setting" data-v-54c6328a></i></div><div class="action-content" data-v-54c6328a><h4 data-v-54c6328a>系统设置</h4><p data-v-54c6328a>配置系统参数</p></div></div></div>',1))])}var E=e(2032),O=e(5720);function B(){return(0,O.A)({url:"/statistics/dashboard",method:"get"})}const $={name:"Dashboard",data(){return{orderChart:null,stationChart:null,currentTime:"",chartPeriod:"7d",dashboardData:{totalUsers:0,totalStations:0,totalOrders:0,totalRevenue:0,userGrowth:0,stationGrowth:0,orderGrowth:0,revenueGrowth:0},loading:!1}},mounted(){this.updateTime(),this.timeInterval=setInterval(this.updateTime,1e3),this.loadDashboardData(),this.initCharts()},beforeUnmount(){this.timeInterval&&clearInterval(this.timeInterval),this.orderChart&&this.orderChart.dispose(),this.stationChart&&this.stationChart.dispose(),window.removeEventListener("resize",this.resizeCharts)},methods:{updateTime(){const t=new Date;this.currentTime=t.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})},formatMoney(t){return new Intl.NumberFormat("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}).format(t)},async loadDashboardData(){try{this.loading=!0;const t=await B();0===t.code&&(this.dashboardData=t.data)}catch(t){console.error("加载仪表板数据失败:",t),this.dashboardData={totalUsers:1256,totalStations:358,totalOrders:6832,totalRevenue:235698.56,userGrowth:18,stationGrowth:5,orderGrowth:43,revenueGrowth:1268.32}}finally{this.loading=!1}},changeChartPeriod(t){this.chartPeriod=t,this.updateChartData()},async updateChartData(){try{const t=await this.$http.get(`/statistics/orderTrend?period=${this.chartPeriod}`);if(0===t.data.code){const a=t.data.data,e=a.map((t=>t.date)),s=a.map((t=>t.orders)),i=a.map((t=>t.revenue));this.orderChart.setOption({xAxis:{data:e},series:[{data:s},{data:i}]})}}catch(t){console.error("更新图表数据失败:",t);const a=this.getChartDataByPeriod(this.chartPeriod);this.orderChart.setOption({xAxis:{data:a.dates},series:[{data:a.orders},{data:a.revenue}]})}},getChartDataByPeriod(t){const a={"7d":{dates:["6-27","6-28","6-29","6-30","7-01","7-02","7-03"],orders:[120,132,101,134,190,230,210],revenue:[12e3,13200,10100,13400,19e3,23e3,21e3]},"30d":{dates:["6月","7月","8月","9月","10月","11月","12月"],orders:[820,932,901,934,1290,1330,1320],revenue:[22e3,25e3,24e3,26e3,32e3,35e3,34e3]},"90d":{dates:["Q1","Q2","Q3","Q4"],orders:[2653,2890,3120,3456],revenue:[71e3,78e3,85e3,92e3]}};return a[t]||a["7d"]},async initCharts(){this.$nextTick((async()=>{this.orderChart=E.Ts(document.getElementById("orderTrendChart"));let t=this.getChartDataByPeriod(this.chartPeriod);try{const a=await this.$http.get(`/statistics/orderTrend?period=${this.chartPeriod}`);if(0===a.data.code){const e=a.data.data;t={dates:e.map((t=>t.date)),orders:e.map((t=>t.orders)),revenue:e.map((t=>t.revenue))}}}catch(e){console.error("获取图表数据失败:",e)}this.orderChart.setOption({tooltip:{trigger:"axis",backgroundColor:"rgba(255, 255, 255, 0.95)",borderColor:"#e6e6e6",borderWidth:1,textStyle:{color:"#333"}},legend:{data:["订单数量","销售额"],top:10},grid:{left:"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:t.dates,axisLine:{lineStyle:{color:"#e6e6e6"}}},yAxis:[{type:"value",name:"订单数量",axisLine:{lineStyle:{color:"#e6e6e6"}}},{type:"value",name:"销售额",position:"right",axisLabel:{formatter:"{value} 元"},axisLine:{lineStyle:{color:"#e6e6e6"}}}],series:[{name:"订单数量",type:"line",data:t.orders,smooth:!0,lineStyle:{width:3,color:"#409EFF"},areaStyle:{opacity:.3,color:new E.fA.W4(0,0,0,1,[{offset:0,color:"#409EFF"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}])}},{name:"销售额",type:"line",yAxisIndex:1,data:t.revenue,smooth:!0,lineStyle:{width:3,color:"#67C23A"},areaStyle:{opacity:.3,color:new E.fA.W4(0,0,0,1,[{offset:0,color:"#67C23A"},{offset:1,color:"rgba(103, 194, 58, 0.1)"}])}}]}),this.stationChart=E.Ts(document.getElementById("stationDistChart"));let a=[{value:135,name:"社区驿站",itemStyle:{color:"#409EFF"}},{value:86,name:"快递驿站",itemStyle:{color:"#67C23A"}},{value:45,name:"校园驿站",itemStyle:{color:"#E6A23C"}},{value:68,name:"写字楼驿站",itemStyle:{color:"#F56C6C"}},{value:24,name:"商业驿站",itemStyle:{color:"#909399"}}];try{const t=await this.$http.get("/statistics/orderTypeDistribution");0===t.data.code&&(a=t.data.data.map((t=>({value:t.value,name:t.name,itemStyle:{color:t.color}}))))}catch(e){console.error("获取驿站分布数据失败:",e)}this.stationChart.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)",backgroundColor:"rgba(255, 255, 255, 0.95)",borderColor:"#e6e6e6",borderWidth:1,textStyle:{color:"#333"}},legend:{orient:"vertical",left:10,top:"center",data:["社区驿站","快递驿站","校园驿站","写字楼驿站","商业驿站"]},series:[{name:"驿站类型",type:"pie",radius:["40%","70%"],center:["60%","50%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"},itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},labelLine:{show:!1},data:a}]}),window.addEventListener("resize",this.resizeCharts)}))},resizeCharts(){this.orderChart&&this.orderChart.resize(),this.stationChart&&this.stationChart.resize()}}};var Q=e(1241);const U=(0,Q.A)($,[["render",I],["__scopeId","data-v-54c6328a"]]),M=U}}]);