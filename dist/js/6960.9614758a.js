"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[6960],{6960:(e,s,o)=>{o.r(s),o.d(s,{default:()=>k});var r=o(6768);const t={class:"error-page"},a={class:"error-actions"};function c(e,s,o,c,n,d){const i=(0,r.g2)("el-button");return(0,r.uX)(),(0,r.CE)("div",t,[s[2]||(s[2]=(0,r.Lk)("div",{class:"error-code"},"404",-1)),s[3]||(s[3]=(0,r.Lk)("div",{class:"error-desc"},"抱歉，您访问的页面不存在",-1)),(0,r.Lk)("div",a,[(0,r.bF)(i,{type:"primary",onClick:d.goHome},{default:(0,r.k6)((()=>s[0]||(s[0]=[(0,r.eW)("返回首页")]))),_:1},8,["onClick"]),(0,r.bF)(i,{onClick:d.goBack},{default:(0,r.k6)((()=>s[1]||(s[1]=[(0,r.eW)("返回上一页")]))),_:1},8,["onClick"])])])}const n={name:"Page404",methods:{goHome(){this.$router.push("/")},goBack(){this.$router.go(-1)}}};var d=o(1241);const i=(0,d.A)(n,[["render",c],["__scopeId","data-v-60f21e11"]]),k=i}}]);