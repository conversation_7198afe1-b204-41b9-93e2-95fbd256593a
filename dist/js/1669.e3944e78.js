"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[1669],{1669:(e,n,s)=>{s.r(n),s.d(n,{default:()=>i});var a=s(6768);const t={class:"page-container"};function c(e,n,s,c,r,d){return(0,a.uX)(),(0,a.CE)("div",t,n[0]||(n[0]=[(0,a.Lk)("h2",null,"设备列表",-1),(0,a.Lk)("div",{class:"placeholder-content"}," 设备列表功能正在开发中... ",-1)]))}const r={name:"DeviceList",data(){return{}}};var d=s(1241);const u=(0,d.A)(r,[["render",c],["__scopeId","data-v-177229bc"]]),i=u}}]);