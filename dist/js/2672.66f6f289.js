"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[2672],{2672:(t,a,n)=>{n.r(a),n.d(a,{addStation:()=>s,auditStation:()=>d,deleteStation:()=>r,exportStation:()=>l,getStationDetail:()=>o,getStationList:()=>i,getStationStatistics:()=>m,testDeleteStation:()=>c,updateStation:()=>u,updateStationStatus:()=>p});var e=n(5720);function i(t){return(0,e.A)({url:"/api/stations/stations",method:"get",params:t})}function o(t){return(0,e.A)({url:`/api/stations/stations/${t}`,method:"get"})}function s(t){return(0,e.A)({url:"/api/station",method:"post",data:t})}function u(t){return(0,e.A)({url:"/api/station",method:"put",data:t})}function r(t){return(0,e.A)({url:`/api/stations/admin/stations/${t}`,method:"delete"})}function d(t){return(0,e.A)({url:"/api/station/audit",method:"put",data:t})}function p(t){return(0,e.A)({url:"/api/station/status",method:"put",data:t})}function l(t){return(0,e.A)({url:"/api/station/export",method:"get",params:t,responseType:"blob"})}function m(){return(0,e.A)({url:"/api/station/statistics",method:"get"})}function c(t){return(0,e.A)({url:`/api/stations/delete-station/${t}`,method:"delete"})}}}]);