"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[7773],{7773:(e,a,t)=>{t.r(a),t.d(a,{default:()=>f});var i=t(6768),l=t(4232);const n={class:"user-list-container"},s={class:"filter-container"},o={class:"table-container"},r={class:"user-info"},d={class:"user-details"},h={class:"nickname"},c={class:"phone"},g={class:"pagination-container"};function p(e,a,t,p,u,m){const k=(0,i.g2)("el-input"),f=(0,i.g2)("el-form-item"),b=(0,i.g2)("el-date-picker"),_=(0,i.g2)("el-button"),F=(0,i.g2)("el-form"),C=(0,i.g2)("el-table-column"),w=(0,i.g2)("el-avatar"),v=(0,i.g2)("el-tag"),L=(0,i.g2)("el-table"),y=(0,i.g2)("el-pagination"),z=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)("div",n,[a[9]||(a[9]=(0,i.Lk)("div",{class:"page-header"},[(0,i.Lk)("h2",null,[(0,i.Lk)("i",{class:"el-icon-user-solid"}),(0,i.eW)(" 用户管理")]),(0,i.Lk)("p",null,"管理系统中的所有用户信息")],-1)),(0,i.Lk)("div",s,[(0,i.bF)(F,{inline:!0,model:u.searchForm,class:"search-form"},{default:(0,i.k6)((()=>[(0,i.bF)(f,{label:"用户手机号"},{default:(0,i.k6)((()=>[(0,i.bF)(k,{modelValue:u.searchForm.phone,"onUpdate:modelValue":a[0]||(a[0]=e=>u.searchForm.phone=e),placeholder:"请输入手机号",clearable:""},null,8,["modelValue"])])),_:1}),(0,i.bF)(f,{label:"注册时间"},{default:(0,i.k6)((()=>[(0,i.bF)(b,{modelValue:u.searchForm.dateRange,"onUpdate:modelValue":a[1]||(a[1]=e=>u.searchForm.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),(0,i.bF)(f,null,{default:(0,i.k6)((()=>[(0,i.bF)(_,{type:"primary",onClick:m.handleSearch},{default:(0,i.k6)((()=>a[4]||(a[4]=[(0,i.Lk)("i",{class:"el-icon-search"},null,-1),(0,i.eW)(" 搜索 ")]))),_:1},8,["onClick"]),(0,i.bF)(_,{onClick:m.handleReset},{default:(0,i.k6)((()=>a[5]||(a[5]=[(0,i.Lk)("i",{class:"el-icon-refresh"},null,-1),(0,i.eW)(" 重置 ")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])]),(0,i.Lk)("div",o,[(0,i.bo)(((0,i.uX)(),(0,i.Wv)(L,{data:u.userList,style:{width:"100%"},onSelectionChange:m.handleSelectionChange},{default:(0,i.k6)((()=>[(0,i.bF)(C,{type:"selection",width:"55"}),(0,i.bF)(C,{prop:"id",label:"用户ID",width:"80"}),(0,i.bF)(C,{label:"用户信息","min-width":"200"},{default:(0,i.k6)((e=>[(0,i.Lk)("div",r,[(0,i.bF)(w,{src:e.row.avatar,size:40},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(e.row.nickname?e.row.nickname.charAt(0):"用"),1)])),_:2},1032,["src"]),(0,i.Lk)("div",d,[(0,i.Lk)("div",h,(0,l.v_)(e.row.nickname||"未设置"),1),(0,i.Lk)("div",c,(0,l.v_)(e.row.phone),1)])])])),_:1}),(0,i.bF)(C,{prop:"is_verified",label:"认证状态",width:"100"},{default:(0,i.k6)((e=>[(0,i.bF)(v,{type:e.row.is_verified?"success":"warning"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(e.row.is_verified?"已认证":"未认证"),1)])),_:2},1032,["type"])])),_:1}),(0,i.bF)(C,{prop:"last_login_time",label:"最后登录",width:"160"},{default:(0,i.k6)((e=>[(0,i.eW)((0,l.v_)(m.formatTime(e.row.last_login_time)),1)])),_:1}),(0,i.bF)(C,{prop:"created_at",label:"注册时间",width:"160"},{default:(0,i.k6)((e=>[(0,i.eW)((0,l.v_)(m.formatTime(e.row.created_at)),1)])),_:1}),(0,i.bF)(C,{label:"操作",width:"200",fixed:"right"},{default:(0,i.k6)((e=>[(0,i.bF)(_,{type:"text",size:"small",onClick:a=>m.handleView(e.row)},{default:(0,i.k6)((()=>a[6]||(a[6]=[(0,i.Lk)("i",{class:"el-icon-view"},null,-1),(0,i.eW)(" 查看 ")]))),_:2},1032,["onClick"]),(0,i.bF)(_,{type:"text",size:"small",onClick:a=>m.handleEdit(e.row)},{default:(0,i.k6)((()=>a[7]||(a[7]=[(0,i.Lk)("i",{class:"el-icon-edit"},null,-1),(0,i.eW)(" 编辑 ")]))),_:2},1032,["onClick"]),(0,i.bF)(_,{type:"text",size:"small",class:"danger",onClick:a=>m.handleDelete(e.row)},{default:(0,i.k6)((()=>a[8]||(a[8]=[(0,i.Lk)("i",{class:"el-icon-delete"},null,-1),(0,i.eW)(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data","onSelectionChange"])),[[z,u.loading]]),(0,i.Lk)("div",g,[(0,i.bF)(y,{"current-page":u.pagination.page,"onUpdate:currentPage":a[2]||(a[2]=e=>u.pagination.page=e),"page-size":u.pagination.limit,"onUpdate:pageSize":a[3]||(a[3]=e=>u.pagination.limit=e),"page-sizes":[10,20,50,100],total:u.pagination.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:m.handleSizeChange,onCurrentChange:m.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])])])}const u={name:"UserList",data(){return{loading:!1,userList:[],selectedUsers:[],searchForm:{phone:"",dateRange:[]},pagination:{page:1,limit:20,total:0}}},mounted(){this.loadUserList()},methods:{async loadUserList(){this.loading=!0;try{const e=await this.$http.get("/user/list",{params:this.getSearchParams()});0===e.data.code?(this.userList=e.data.data.list,this.pagination.total=e.data.data.total):this.$message.error(e.data.message||"获取用户列表失败")}catch(e){console.error("加载用户列表失败:",e),this.$message.error("加载用户列表失败")}finally{this.loading=!1}},getSearchParams(){const e={page:this.pagination.page,limit:this.pagination.limit};return this.searchForm.phone&&(e.phone=this.searchForm.phone),this.searchForm.dateRange&&2===this.searchForm.dateRange.length&&(e.startDate=this.searchForm.dateRange[0],e.endDate=this.searchForm.dateRange[1]),e},handleSearch(){this.pagination.page=1,this.loadUserList()},handleReset(){this.searchForm={phone:"",dateRange:[]},this.pagination.page=1,this.loadUserList()},handleSelectionChange(e){this.selectedUsers=e},handleView(e){this.$message.info("查看用户详情功能开发中")},handleEdit(e){this.$message.info("编辑用户功能开发中")},handleDelete(e){this.$confirm("确定要删除这个用户吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$message.success("删除成功")}))},handleSizeChange(e){this.pagination.limit=e,this.loadUserList()},handleCurrentChange(e){this.pagination.page=e,this.loadUserList()},formatTime(e){return e?new Date(e).toLocaleString("zh-CN"):"-"}}};var m=t(1241);const k=(0,m.A)(u,[["render",p],["__scopeId","data-v-10e9857b"]]),f=k}}]);