"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[4076],{1981:(e,a,t)=>{function l(e,a="YYYY-MM-DD HH:mm:ss"){if(!e)return"";let t;"object"===typeof e?t=e:("string"===typeof e&&/^[0-9]+$/.test(e)&&(e=parseInt(e)),"number"===typeof e&&10===e.toString().length&&(e*=1e3),t=new Date(e));const l={"M+":t.getMonth()+1,"D+":t.getDate(),"H+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),S:t.getMilliseconds()};/(Y+)/.test(a)&&(a=a.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));for(let s in l)new RegExp("("+s+")").test(a)&&(a=a.replace(RegExp.$1,1===RegExp.$1.length?l[s]:("00"+l[s]).substr((""+l[s]).length)));return a}t.d(a,{r6:()=>l})},4076:(e,a,t)=>{t.r(a),t.d(a,{default:()=>I});var l=t(6768),s=t(5130),n=t(4232);const i={class:"app-container"},r={class:"filter-item"},u={class:"table-header"},d={class:"statistics-cards"},o={class:"stat-card primary"},c={class:"stat-value"},p={class:"stat-card warning"},g={class:"stat-value price-text"},m={class:"stat-card info"},k={class:"stat-value"},b={class:"stat-card danger"},_={class:"stat-value price-text"},v={class:"user-info"},f={class:"user-detail"},h={class:"user-phone"},y={class:"price-text"},F={key:0},C={class:"account-number"},w={key:0},L={key:1},x={class:"withdrawal-detail"},W={class:"user-info"},T={class:"user-detail"},V={class:"user-phone"},z={class:"price-text"},D={key:0},R={class:"dialog-footer"},S={class:"dialog-footer"},A={class:"price-text"},K={class:"dialog-footer"};function E(e,a,t,E,X,Q){const U=(0,l.g2)("el-input"),$=(0,l.g2)("el-option"),j=(0,l.g2)("el-select"),M=(0,l.g2)("el-date-picker"),N=(0,l.g2)("el-button"),P=(0,l.g2)("el-card"),I=(0,l.g2)("el-table-column"),Y=(0,l.g2)("el-avatar"),H=(0,l.g2)("el-tag"),B=(0,l.g2)("el-table"),O=(0,l.g2)("pagination"),q=(0,l.g2)("el-descriptions-item"),G=(0,l.g2)("el-descriptions"),J=(0,l.g2)("el-dialog"),Z=(0,l.g2)("el-form-item"),ee=(0,l.g2)("el-form"),ae=(0,l.g2)("el-upload"),te=(0,l.gN)("waves"),le=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",i,[(0,l.bF)(P,{class:"filter-container"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",r,[(0,l.bF)(U,{modelValue:E.listQuery.search,"onUpdate:modelValue":a[0]||(a[0]=e=>E.listQuery.search=e),placeholder:"搜索用户昵称/手机",clearable:"",onKeyup:(0,s.jR)(E.handleFilter,["enter","native"]),style:{width:"200px"}},null,8,["modelValue","onKeyup"]),(0,l.bF)(j,{modelValue:E.listQuery.status,"onUpdate:modelValue":a[1]||(a[1]=e=>E.listQuery.status=e),placeholder:"提现状态",clearable:"",style:{width:"130px"},onChange:E.handleFilter},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(E.statusOptions,(e=>((0,l.uX)(),(0,l.Wv)($,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,l.bF)(M,{modelValue:E.dateRange,"onUpdate:modelValue":a[2]||(a[2]=e=>E.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",style:{width:"260px"},onChange:E.handleDateChange},null,8,["modelValue","onChange"]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(N,{type:"primary",icon:"Search",onClick:E.handleFilter},{default:(0,l.k6)((()=>a[14]||(a[14]=[(0,l.eW)("搜索")]))),_:1},8,["onClick"])),[[te]]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(N,{icon:"Refresh",onClick:E.resetQuery},{default:(0,l.k6)((()=>a[15]||(a[15]=[(0,l.eW)("重置")]))),_:1},8,["onClick"])),[[te]])])])),_:1}),(0,l.bF)(P,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",u,[(0,l.Lk)("div",d,[(0,l.Lk)("div",o,[(0,l.Lk)("div",c,(0,n.v_)(E.statistics.totalCount),1),a[16]||(a[16]=(0,l.Lk)("div",{class:"stat-label"},"总申请数",-1))]),(0,l.Lk)("div",p,[(0,l.Lk)("div",g,"¥"+(0,n.v_)(E.statistics.totalAmount),1),a[17]||(a[17]=(0,l.Lk)("div",{class:"stat-label"},"总提现金额",-1))]),(0,l.Lk)("div",m,[(0,l.Lk)("div",k,(0,n.v_)(E.statistics.pendingCount),1),a[18]||(a[18]=(0,l.Lk)("div",{class:"stat-label"},"待处理申请",-1))]),(0,l.Lk)("div",b,[(0,l.Lk)("div",_,"¥"+(0,n.v_)(E.statistics.pendingAmount),1),a[19]||(a[19]=(0,l.Lk)("div",{class:"stat-label"},"待处理金额",-1))])]),(0,l.Lk)("div",null,[(0,l.bF)(N,{type:"success",size:"small",onClick:E.exportData},{default:(0,l.k6)((()=>a[20]||(a[20]=[(0,l.eW)("导出数据")]))),_:1},8,["onClick"])])]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(B,{data:E.list,border:"",fit:"","highlight-current-row":"",style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(I,{label:"ID",prop:"id",align:"center",width:"80"}),(0,l.bF)(I,{label:"用户信息","min-width":"150"},{default:(0,l.k6)((({row:e})=>[(0,l.Lk)("div",v,[(0,l.bF)(Y,{src:e.user_avatar||"/static/default-avatar.png",size:30},null,8,["src"]),(0,l.Lk)("div",f,[(0,l.Lk)("div",null,(0,n.v_)(e.user_nickname),1),(0,l.Lk)("div",h,(0,n.v_)(e.user_phone),1)])])])),_:1}),(0,l.bF)(I,{label:"申请时间",width:"150",align:"center"},{default:(0,l.k6)((({row:e})=>[(0,l.Lk)("span",null,(0,n.v_)(E.formatDateTime(e.created_at)),1)])),_:1}),(0,l.bF)(I,{label:"提现金额",prop:"amount",align:"center",width:"120"},{default:(0,l.k6)((({row:e})=>[(0,l.Lk)("span",y,"¥"+(0,n.v_)(e.amount.toFixed(2)),1)])),_:1}),(0,l.bF)(I,{label:"账户类型",align:"center",width:"100"},{default:(0,l.k6)((({row:e})=>[(0,l.eW)((0,n.v_)(E.getAccountTypeText(e.account_type)),1)])),_:1}),(0,l.bF)(I,{label:"账户信息","min-width":"150"},{default:(0,l.k6)((({row:e})=>[(0,l.Lk)("div",null,(0,n.v_)(e.real_name),1),3===e.account_type?((0,l.uX)(),(0,l.CE)("div",F,(0,n.v_)(e.bank_name),1)):(0,l.Q3)("",!0),(0,l.Lk)("div",C,(0,n.v_)(E.formatAccountNumber(e.account_number,e.account_type)),1)])),_:1}),(0,l.bF)(I,{label:"状态",prop:"status",align:"center",width:"100"},{default:(0,l.k6)((({row:e})=>[(0,l.bF)(H,{type:E.getStatusType(e.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(E.getStatusText(e.status)),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(I,{label:"处理时间",width:"150",align:"center"},{default:(0,l.k6)((({row:e})=>[e.processed_at?((0,l.uX)(),(0,l.CE)("span",w,(0,n.v_)(E.formatDateTime(e.processed_at)),1)):((0,l.uX)(),(0,l.CE)("span",L,"--"))])),_:1}),(0,l.bF)(I,{label:"操作",align:"center",width:"200","class-name":"small-padding fixed-width"},{default:(0,l.k6)((({row:e})=>[0===e.status?((0,l.uX)(),(0,l.Wv)(N,{key:0,type:"success",size:"small",onClick:a=>E.handleApprove(e)},{default:(0,l.k6)((()=>a[21]||(a[21]=[(0,l.eW)("通过")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),0===e.status?((0,l.uX)(),(0,l.Wv)(N,{key:1,type:"danger",size:"small",onClick:a=>E.handleReject(e)},{default:(0,l.k6)((()=>a[22]||(a[22]=[(0,l.eW)("拒绝")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),1===e.status?((0,l.uX)(),(0,l.Wv)(N,{key:2,type:"primary",size:"small",onClick:a=>E.handleTransfer(e)},{default:(0,l.k6)((()=>a[23]||(a[23]=[(0,l.eW)("已打款")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),(0,l.bF)(N,{type:"info",size:"small",onClick:a=>E.handleDetail(e)},{default:(0,l.k6)((()=>a[24]||(a[24]=[(0,l.eW)("详情")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[le,E.listLoading]]),(0,l.bo)((0,l.bF)(O,{total:E.total,page:E.listQuery.page,limit:E.listQuery.limit,onPagination:e.getList},null,8,["total","page","limit","onPagination"]),[[s.aG,E.total>0]])])),_:1}),(0,l.bF)(J,{title:"提现详情",modelValue:E.dialogVisible,"onUpdate:modelValue":a[7]||(a[7]=e=>E.dialogVisible=e),width:"500px"},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",x,[(0,l.bF)(G,{column:1,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(q,{label:"申请编号"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(E.detail.id),1)])),_:1}),(0,l.bF)(q,{label:"申请用户"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",W,[(0,l.bF)(Y,{src:E.detail.user_avatar||"/static/default-avatar.png",size:30},null,8,["src"]),(0,l.Lk)("div",T,[(0,l.Lk)("div",null,(0,n.v_)(E.detail.user_nickname),1),(0,l.Lk)("div",V,(0,n.v_)(E.detail.user_phone),1)])])])),_:1}),(0,l.bF)(q,{label:"提现金额"},{default:(0,l.k6)((()=>[(0,l.Lk)("span",z,"¥"+(0,n.v_)(E.detail.amount&&E.detail.amount.toFixed(2)),1)])),_:1}),(0,l.bF)(q,{label:"收款账户"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",null,(0,n.v_)(E.detail.real_name),1),(0,l.Lk)("div",null,(0,n.v_)(E.getAccountTypeText(E.detail.account_type)),1),3===E.detail.account_type?((0,l.uX)(),(0,l.CE)("div",D,(0,n.v_)(E.detail.bank_name),1)):(0,l.Q3)("",!0),(0,l.Lk)("div",null,(0,n.v_)(E.detail.account_number),1)])),_:1}),(0,l.bF)(q,{label:"申请时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(E.formatDateTime(E.detail.created_at)),1)])),_:1}),(0,l.bF)(q,{label:"状态"},{default:(0,l.k6)((()=>[void 0!==E.detail.status?((0,l.uX)(),(0,l.Wv)(H,{key:0,type:E.getStatusType(E.detail.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(E.getStatusText(E.detail.status)),1)])),_:1},8,["type"])):(0,l.Q3)("",!0)])),_:1}),(0,l.bF)(q,{label:"处理时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(E.detail.processed_at?E.formatDateTime(E.detail.processed_at):"--"),1)])),_:1}),(0,l.bF)(q,{label:"处理备注"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(E.detail.remark||"--"),1)])),_:1})])),_:1}),(0,l.Lk)("div",R,[(0,l.bF)(N,{onClick:a[3]||(a[3]=e=>E.dialogVisible=!1)},{default:(0,l.k6)((()=>a[25]||(a[25]=[(0,l.eW)("关闭")]))),_:1}),0===E.detail.status?((0,l.uX)(),(0,l.CE)(l.FK,{key:0},[(0,l.bF)(N,{type:"success",onClick:a[4]||(a[4]=e=>E.handleApprove(E.detail))},{default:(0,l.k6)((()=>a[26]||(a[26]=[(0,l.eW)("通过")]))),_:1}),(0,l.bF)(N,{type:"danger",onClick:a[5]||(a[5]=e=>E.handleReject(E.detail))},{default:(0,l.k6)((()=>a[27]||(a[27]=[(0,l.eW)("拒绝")]))),_:1})],64)):(0,l.Q3)("",!0),1===E.detail.status?((0,l.uX)(),(0,l.Wv)(N,{key:1,type:"primary",onClick:a[6]||(a[6]=e=>E.handleTransfer(E.detail))},{default:(0,l.k6)((()=>a[28]||(a[28]=[(0,l.eW)("已打款")]))),_:1})):(0,l.Q3)("",!0)])])),[[le,E.detailLoading]])])),_:1},8,["modelValue"]),(0,l.bF)(J,{title:"approve"===E.auditAction?"通过提现申请":"拒绝提现申请",modelValue:E.auditDialogVisible,"onUpdate:modelValue":a[10]||(a[10]=e=>E.auditDialogVisible=e),width:"400px"},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",S,[(0,l.bF)(N,{onClick:a[9]||(a[9]=e=>E.auditDialogVisible=!1)},{default:(0,l.k6)((()=>a[29]||(a[29]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(N,{type:"approve"===E.auditAction?"success":"danger",onClick:E.submitAudit,loading:E.auditSubmitting},{default:(0,l.k6)((()=>a[30]||(a[30]=[(0,l.eW)("确 定")]))),_:1},8,["type","onClick","loading"])])])),default:(0,l.k6)((()=>[(0,l.bF)(ee,{model:E.auditForm,"label-width":"80px"},{default:(0,l.k6)((()=>[(0,l.bF)(Z,{label:"备注"},{default:(0,l.k6)((()=>[(0,l.bF)(U,{modelValue:E.auditForm.remark,"onUpdate:modelValue":a[8]||(a[8]=e=>E.auditForm.remark=e),type:"textarea",rows:3,placeholder:"approve"===E.auditAction?"通过原因（选填）":"拒绝原因（必填）"},null,8,["modelValue","placeholder"])])),_:1})])),_:1},8,["model"])])),_:1},8,["title","modelValue"]),(0,l.bF)(J,{title:"确认打款",modelValue:E.transferDialogVisible,"onUpdate:modelValue":a[13]||(a[13]=e=>E.transferDialogVisible=e),width:"400px"},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",K,[(0,l.bF)(N,{onClick:a[12]||(a[12]=e=>E.transferDialogVisible=!1)},{default:(0,l.k6)((()=>a[33]||(a[33]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(N,{type:"primary",onClick:E.submitTransfer,loading:E.transferSubmitting},{default:(0,l.k6)((()=>a[34]||(a[34]=[(0,l.eW)("确认已打款")]))),_:1},8,["onClick","loading"])])])),default:(0,l.k6)((()=>[(0,l.bF)(ee,{model:E.transferForm,"label-width":"80px"},{default:(0,l.k6)((()=>[(0,l.bF)(Z,{label:"收款方"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(E.transferTarget.real_name),1)])),_:1}),(0,l.bF)(Z,{label:"账户类型"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(E.getAccountTypeText(E.transferTarget.account_type)),1)])),_:1}),3===E.transferTarget.account_type?((0,l.uX)(),(0,l.Wv)(Z,{key:0,label:"银行名称"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(E.transferTarget.bank_name),1)])),_:1})):(0,l.Q3)("",!0),(0,l.bF)(Z,{label:"账号"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(E.transferTarget.account_number),1)])),_:1}),(0,l.bF)(Z,{label:"金额"},{default:(0,l.k6)((()=>[(0,l.Lk)("span",A,"¥"+(0,n.v_)(E.transferTarget.amount&&E.transferTarget.amount.toFixed(2)),1)])),_:1}),(0,l.bF)(Z,{label:"打款凭证"},{default:(0,l.k6)((()=>[(0,l.bF)(ae,{class:"upload-demo",action:"/api/upload/image","on-success":E.handleUploadSuccess,"on-error":E.handleUploadError,"before-upload":E.beforeUpload,"file-list":E.fileList,"list-type":"picture"},{tip:(0,l.k6)((()=>a[32]||(a[32]=[(0,l.Lk)("div",{class:"el-upload__tip"},"只能上传jpg/png文件，且不超过2MB",-1)]))),default:(0,l.k6)((()=>[(0,l.bF)(N,{size:"small",type:"primary"},{default:(0,l.k6)((()=>a[31]||(a[31]=[(0,l.eW)("点击上传")]))),_:1})])),_:1},8,["on-success","on-error","before-upload","file-list"])])),_:1}),(0,l.bF)(Z,{label:"备注"},{default:(0,l.k6)((()=>[(0,l.bF)(U,{modelValue:E.transferForm.remark,"onUpdate:modelValue":a[11]||(a[11]=e=>E.transferForm.remark=e),type:"textarea",rows:2},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}var X=t(144),Q=t(1219),U=t(7093),$=t(9190),j=t(1981);const M={name:"WithdrawalList",components:{Pagination:$.A},directives:{waves:U.A},setup(){const e=(0,X.KR)([]),a=(0,X.KR)([]),t=(0,X.KR)(0),s=(0,X.KR)(!1),n=(0,X.KR)(!1),i=(0,X.KR)(!1),r=(0,X.KR)({}),u=(0,X.KR)(!1),d=(0,X.KR)(!1),o=(0,X.KR)("approve"),c=(0,X.KR)(null),p=(0,X.KR)(!1),g=(0,X.KR)(!1),m=(0,X.KR)({}),k=(0,X.KR)([]),b=(0,X.Kh)({page:1,limit:10,status:"",search:"",start_date:"",end_date:""}),_=(0,X.Kh)({totalCount:0,totalAmount:0,pendingCount:0,pendingAmount:0}),v=(0,X.Kh)({id:null,status:0,remark:""}),f=(0,X.Kh)({id:null,proof_image:"",remark:""}),h=[{label:"待审核",value:0},{label:"已通过",value:1},{label:"已打款",value:2},{label:"已拒绝",value:3}],y=[{id:1,user_id:101,user_nickname:"张三",user_phone:"138****1234",user_avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",amount:1e3,account_type:1,real_name:"张三",account_number:"zhang_san888",bank_name:"",created_at:"2023-05-15 09:30:00",processed_at:null,status:0,remark:""},{id:2,user_id:102,user_nickname:"李四",user_phone:"139****5678",user_avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",amount:2500,account_type:2,real_name:"李四",account_number:"***********",bank_name:"",created_at:"2023-05-14 14:20:00",processed_at:"2023-05-14 15:30:00",status:1,remark:"通过审核"},{id:3,user_id:103,user_nickname:"王五",user_phone:"137****9012",user_avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",amount:5e3,account_type:3,real_name:"王五",account_number:"****************",bank_name:"招商银行",created_at:"2023-05-13 10:15:00",processed_at:"2023-05-13 11:20:00",status:2,remark:"已完成打款"},{id:4,user_id:104,user_nickname:"赵六",user_phone:"136****3456",user_avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",amount:1200,account_type:1,real_name:"赵六",account_number:"zhao_liu666",bank_name:"",created_at:"2023-05-12 16:45:00",processed_at:"2023-05-12 17:30:00",status:3,remark:"账户信息不正确，请修改后重新申请"}],F=()=>{s.value=!0,setTimeout((()=>{let e=[...y];""!==b.status&&(e=e.filter((e=>e.status===b.status))),b.search&&(e=e.filter((e=>e.user_nickname.includes(b.search)||e.user_phone.includes(b.search)))),b.start_date&&b.end_date,a.value=e,t.value=e.length,C(e),s.value=!1}),500)},C=e=>{let a=0,t=0,l=0;e.forEach((e=>{a+=e.amount,0===e.status&&(t++,l+=e.amount)})),_.totalCount=e.length,_.totalAmount=a.toFixed(2),_.pendingCount=t,_.pendingAmount=l.toFixed(2)},w=()=>{b.page=1,F()},L=()=>{e.value=[],Object.assign(b,{page:1,limit:10,status:"",search:"",start_date:"",end_date:""}),F()},x=e=>{e?(b.start_date=e[0],b.end_date=e[1]):(b.start_date="",b.end_date="")},W=e=>{const a={0:"warning",1:"success",2:"primary",3:"danger"};return a[e]},T=e=>{const a={0:"待审核",1:"已通过",2:"已打款",3:"已拒绝"};return a[e]},V=e=>{const a={1:"支付宝",2:"微信",3:"银行卡"};return a[e]||"未知"},z=(e,a)=>{if(!e)return"";if(3===a)return e.replace(/^(\d{4})(\d+)(\d{4})$/,"$1 **** **** $3");if(e.includes("@")){const a=e.split("@"),t=a[0].length>3?a[0].substring(0,3)+"****":a[0].substring(0,1)+"****";return t+"@"+a[1]}return/^\d+$/.test(e)?e.replace(/^(\d{3})(\d+)(\d{4})$/,"$1****$3"):e.substring(0,3)+"****"},D=e=>{i.value=!0,r.value={...e},n.value=!0,setTimeout((()=>{i.value=!1}),300)},R=e=>{c.value=e,o.value="approve",v.id=e.id,v.status=1,v.remark="",u.value=!0},S=e=>{c.value=e,o.value="reject",v.id=e.id,v.status=3,v.remark="",u.value=!0},A=()=>{"reject"!==o.value||v.remark?(d.value=!0,setTimeout((()=>{const e=a.value.findIndex((e=>e.id===v.id));e>-1&&(a.value[e].status=v.status,a.value[e].processed_at=(0,j.r6)(new Date),a.value[e].remark=v.remark,r.value.id===v.id&&(r.value.status=v.status,r.value.processed_at=(0,j.r6)(new Date),r.value.remark=v.remark)),C(a.value),d.value=!1,u.value=!1,(0,Q.nk)({type:"success",message:"approve"===o.value?"提现申请已通过":"提现申请已拒绝"})}),500)):Q.nk.warning("请填写拒绝原因")},K=e=>{m.value={...e},f.id=e.id,f.proof_image="",f.remark="",k.value=[],p.value=!0},E=e=>{f.proof_image=e.url,Q.nk.success("上传成功")},U=()=>{Q.nk.error("上传失败")},$=e=>{const a="image/jpeg"===e.type,t="image/png"===e.type,l=e.size/1024/1024<2;return a||t||Q.nk.error("上传图片只能是 JPG 或 PNG 格式!"),l||Q.nk.error("上传图片大小不能超过 2MB!"),(a||t)&&l},M=()=>{f.proof_image?(g.value=!0,setTimeout((()=>{const e=a.value.findIndex((e=>e.id===f.id));e>-1&&(a.value[e].status=2,a.value[e].processed_at=(0,j.r6)(new Date),a.value[e].remark=f.remark,r.value.id===f.id&&(r.value.status=2,r.value.processed_at=(0,j.r6)(new Date),r.value.remark=f.remark)),C(a.value),g.value=!1,p.value=!1,(0,Q.nk)({type:"success",message:"确认打款成功"})}),500)):Q.nk.warning("请上传打款凭证")},N=()=>{Q.nk.success("数据导出功能开发中")};return(0,l.sV)((()=>{F()})),{dateRange:e,list:a,total:t,listLoading:s,listQuery:b,statistics:_,statusOptions:h,dialogVisible:n,detailLoading:i,detail:r,auditDialogVisible:u,auditSubmitting:d,auditAction:o,auditForm:v,transferDialogVisible:p,transferSubmitting:g,transferTarget:m,transferForm:f,fileList:k,formatDateTime:j.r6,handleFilter:w,resetQuery:L,handleDateChange:x,getStatusType:W,getStatusText:T,getAccountTypeText:V,formatAccountNumber:z,handleDetail:D,handleApprove:R,handleReject:S,submitAudit:A,handleTransfer:K,handleUploadSuccess:E,handleUploadError:U,beforeUpload:$,submitTransfer:M,exportData:N}}};var N=t(1241);const P=(0,N.A)(M,[["render",E],["__scopeId","data-v-871be92a"]]),I=P},7093:(e,a,t)=>{t.d(a,{A:()=>n});const l={mounted(e,a){e.addEventListener("click",(t=>{const l=Object.assign({},a.value),s=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},l),n=s.ele;if(n){n.style.position="relative",n.style.overflow="hidden";const e=n.getBoundingClientRect();let a=n.querySelector(".waves-ripple");switch(a?a.className="waves-ripple":(a=document.createElement("span"),a.className="waves-ripple",a.style.height=a.style.width=Math.max(e.width,e.height)+"px",n.appendChild(a)),s.type){case"center":a.style.top=e.height/2-a.offsetHeight/2+"px",a.style.left=e.width/2-a.offsetWidth/2+"px";break;default:a.style.top=(t.pageY-e.top-a.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",a.style.left=(t.pageX-e.left-a.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return a.style.backgroundColor=s.color,a.className="waves-ripple z-active",!1}}),!1)}},s=l,n=s},9190:(e,a,t)=>{t.d(a,{A:()=>d});var l=t(6768),s=t(4232);function n(e,a,t,n,i,r){const u=(0,l.g2)("el-pagination");return(0,l.uX)(),(0,l.CE)("div",{class:(0,s.C4)([{hidden:t.hidden},"pagination-container"])},[(0,l.bF)(u,{background:t.background,"current-page":r.currentPage,"onUpdate:currentPage":a[0]||(a[0]=e=>r.currentPage=e),"page-size":r.pageSize,"onUpdate:pageSize":a[1]||(a[1]=e=>r.pageSize=e),layout:t.layout,"page-sizes":t.pageSizes,total:t.total,onSizeChange:r.handleSizeChange,onCurrentChange:r.handleCurrentChange},null,8,["background","current-page","page-size","layout","page-sizes","total","onSizeChange","onCurrentChange"])],2)}const i={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:10},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},emits:["update:page","update:limit","pagination"],computed:{currentPage:{get(){return this.page},set(e){this.$emit("update:page",e)}},pageSize:{get(){return this.limit},set(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange(e){this.$emit("pagination",{page:this.currentPage,limit:e})},handleCurrentChange(e){this.$emit("pagination",{page:e,limit:this.pageSize})}}};var r=t(1241);const u=(0,r.A)(i,[["render",n],["__scopeId","data-v-5035dd33"]]),d=u}}]);