"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[8504],{8504:(e,t,a)=>{a.r(t),a.d(t,{default:()=>y});var l=a(6768),i=a(4232);const s={class:"page-container"},o={class:"action-container"},n={class:"search-box"},r={class:"button-group"},u={class:"pagination-container"},d={class:"dialog-footer"},h={class:"dialog-footer"};function p(e,t,a,p,m,b){const c=(0,l.g2)("el-input"),g=(0,l.g2)("el-option"),y=(0,l.g2)("el-select"),k=(0,l.g2)("el-date-picker"),v=(0,l.g2)("el-button"),F=(0,l.g2)("el-table-column"),f=(0,l.g2)("el-tag"),C=(0,l.g2)("el-table"),w=(0,l.g2)("el-pagination"),V=(0,l.g2)("el-form-item"),_=(0,l.g2)("el-form"),T=(0,l.g2)("el-dialog"),x=(0,l.g2)("el-divider"),D=(0,l.g2)("el-card"),P=(0,l.g2)("el-col"),q=(0,l.g2)("el-row"),W=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",s,[(0,l.Lk)("div",o,[(0,l.Lk)("div",n,[(0,l.bF)(c,{modelValue:m.queryParams.keyword,"onUpdate:modelValue":t[0]||(t[0]=e=>m.queryParams.keyword=e),placeholder:"请输入内容标题/发布人",clearable:"",style:{width:"200px"},class:"mr-10"},null,8,["modelValue"]),(0,l.bF)(y,{modelValue:m.queryParams.type,"onUpdate:modelValue":t[1]||(t[1]=e=>m.queryParams.type=e),placeholder:"内容类型",clearable:"",class:"mr-10",style:{width:"120px"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(m.typeOptions,(e=>((0,l.uX)(),(0,l.Wv)(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),(0,l.bF)(y,{modelValue:m.queryParams.status,"onUpdate:modelValue":t[2]||(t[2]=e=>m.queryParams.status=e),placeholder:"上架状态",clearable:"",class:"mr-10",style:{width:"120px"}},{default:(0,l.k6)((()=>[(0,l.bF)(g,{label:"已上架",value:"1"}),(0,l.bF)(g,{label:"已下架",value:"0"}),(0,l.bF)(g,{label:"违规下架",value:"2"})])),_:1},8,["modelValue"]),(0,l.bF)(k,{modelValue:m.dateRange,"onUpdate:modelValue":t[3]||(t[3]=e=>m.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",class:"mr-10",style:{width:"240px"}},null,8,["modelValue"]),(0,l.bF)(v,{type:"primary",onClick:b.handleQuery},{default:(0,l.k6)((()=>t[14]||(t[14]=[(0,l.eW)("搜索")]))),_:1},8,["onClick"]),(0,l.bF)(v,{onClick:b.resetQuery},{default:(0,l.k6)((()=>t[15]||(t[15]=[(0,l.eW)("重置")]))),_:1},8,["onClick"])]),(0,l.Lk)("div",r,[(0,l.bF)(v,{type:"success",onClick:b.handleBatchPublish},{default:(0,l.k6)((()=>t[16]||(t[16]=[(0,l.eW)("批量上架")]))),_:1},8,["onClick"]),(0,l.bF)(v,{type:"warning",onClick:b.handleBatchUnpublish},{default:(0,l.k6)((()=>t[17]||(t[17]=[(0,l.eW)("批量下架")]))),_:1},8,["onClick"]),(0,l.bF)(v,{type:"danger",onClick:b.handleBatchViolation},{default:(0,l.k6)((()=>t[18]||(t[18]=[(0,l.eW)("批量违规下架")]))),_:1},8,["onClick"])])]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(C,{data:m.contentList,onSelectionChange:b.handleSelectionChange},{default:(0,l.k6)((()=>[(0,l.bF)(F,{type:"selection",width:"55"}),(0,l.bF)(F,{type:"index",label:"#",width:"50"}),(0,l.bF)(F,{prop:"title",label:"内容标题","min-width":"160","show-overflow-tooltip":""}),(0,l.bF)(F,{prop:"contentType",label:"内容类型",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(f,{type:b.getTypeTag(e.row.contentType)},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(m.contentTypeMap[e.row.contentType]),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(F,{prop:"publisher",label:"发布人",width:"100"}),(0,l.bF)(F,{prop:"status",label:"状态",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(f,{type:"1"===e.row.status?"success":"2"===e.row.status?"danger":"info"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(m.statusMap[e.row.status]),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(F,{prop:"auditTime",label:"审核时间",width:"160",sortable:""}),(0,l.bF)(F,{prop:"publishTime",label:"上架时间",width:"160",sortable:""},{default:(0,l.k6)((e=>[(0,l.eW)((0,i.v_)("1"===e.row.status?e.row.publishTime:"-"),1)])),_:1}),(0,l.bF)(F,{prop:"unpublishReason",label:"下架原因","min-width":"150","show-overflow-tooltip":""},{default:(0,l.k6)((e=>[(0,l.eW)((0,i.v_)("1"!==e.row.status&&e.row.unpublishReason||"-"),1)])),_:1}),(0,l.bF)(F,{label:"操作",width:"200",fixed:"right"},{default:(0,l.k6)((e=>[(0,l.bF)(v,{type:"text",onClick:t=>b.handleView(e.row)},{default:(0,l.k6)((()=>t[19]||(t[19]=[(0,l.eW)("查看")]))),_:2},1032,["onClick"]),"1"!==e.row.status?((0,l.uX)(),(0,l.Wv)(v,{key:0,type:"text",onClick:t=>b.handlePublish(e.row)},{default:(0,l.k6)((()=>t[20]||(t[20]=[(0,l.eW)("上架")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),"1"===e.row.status?((0,l.uX)(),(0,l.Wv)(v,{key:1,type:"text",onClick:t=>b.handleUnpublish(e.row)},{default:(0,l.k6)((()=>t[21]||(t[21]=[(0,l.eW)("下架")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),"2"!==e.row.status?((0,l.uX)(),(0,l.Wv)(v,{key:2,type:"text",onClick:t=>b.handleViolation(e.row)},{default:(0,l.k6)((()=>t[22]||(t[22]=[(0,l.eW)("违规")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0)])),_:1})])),_:1},8,["data","onSelectionChange"])),[[W,m.loading]]),(0,l.Lk)("div",u,[(0,l.bF)(w,{"current-page":m.queryParams.pageNum,"onUpdate:currentPage":t[4]||(t[4]=e=>m.queryParams.pageNum=e),"page-size":m.queryParams.pageSize,"onUpdate:pageSize":t[5]||(t[5]=e=>m.queryParams.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:m.total,onSizeChange:b.handleSizeChange,onCurrentChange:b.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])]),(0,l.bF)(T,{title:"违规原因",modelValue:m.violationDialogVisible,"onUpdate:modelValue":t[9]||(t[9]=e=>m.violationDialogVisible=e),width:"500px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",d,[(0,l.bF)(v,{onClick:t[8]||(t[8]=e=>m.violationDialogVisible=!1)},{default:(0,l.k6)((()=>t[23]||(t[23]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(v,{type:"primary",onClick:b.submitViolation},{default:(0,l.k6)((()=>t[24]||(t[24]=[(0,l.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bF)(_,{ref:"violationForm",model:m.violationForm,"label-width":"80px"},{default:(0,l.k6)((()=>[(0,l.bF)(V,{label:"违规类型",prop:"reason"},{default:(0,l.k6)((()=>[(0,l.bF)(y,{modelValue:m.violationForm.reason,"onUpdate:modelValue":t[6]||(t[6]=e=>m.violationForm.reason=e),placeholder:"请选择违规类型",style:{width:"100%"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(m.violationOptions,(e=>((0,l.uX)(),(0,l.Wv)(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(V,{label:"补充说明",prop:"remark"},{default:(0,l.k6)((()=>[(0,l.bF)(c,{modelValue:m.violationForm.remark,"onUpdate:modelValue":t[7]||(t[7]=e=>m.violationForm.remark=e),type:"textarea",placeholder:"请输入补充说明"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),(0,l.bF)(T,{title:"下架原因",modelValue:m.unpublishDialogVisible,"onUpdate:modelValue":t[13]||(t[13]=e=>m.unpublishDialogVisible=e),width:"500px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",h,[(0,l.bF)(v,{onClick:t[12]||(t[12]=e=>m.unpublishDialogVisible=!1)},{default:(0,l.k6)((()=>t[25]||(t[25]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(v,{type:"primary",onClick:b.submitUnpublish},{default:(0,l.k6)((()=>t[26]||(t[26]=[(0,l.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bF)(_,{ref:"unpublishForm",model:m.unpublishForm,"label-width":"80px"},{default:(0,l.k6)((()=>[(0,l.bF)(V,{label:"下架原因",prop:"reason"},{default:(0,l.k6)((()=>[(0,l.bF)(y,{modelValue:m.unpublishForm.reason,"onUpdate:modelValue":t[10]||(t[10]=e=>m.unpublishForm.reason=e),placeholder:"请选择下架原因",style:{width:"100%"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(m.unpublishOptions,(e=>((0,l.uX)(),(0,l.Wv)(g,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(V,{label:"补充说明",prop:"remark"},{default:(0,l.k6)((()=>[(0,l.bF)(c,{modelValue:m.unpublishForm.remark,"onUpdate:modelValue":t[11]||(t[11]=e=>m.unpublishForm.remark=e),type:"textarea",placeholder:"请输入补充说明"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),(0,l.bF)(q,{gutter:20,class:"statistics-container"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{span:8},{default:(0,l.k6)((()=>[(0,l.bF)(D,{class:"statistic-card"},{default:(0,l.k6)((()=>[t[27]||(t[27]=(0,l.Lk)("div",{class:"statistic-title"},"内容违规统计",-1)),(0,l.bF)(x),t[28]||(t[28]=(0,l.Lk)("div",{class:"statistic-chart",id:"violationChart"},null,-1))])),_:1})])),_:1}),(0,l.bF)(P,{span:8},{default:(0,l.k6)((()=>[(0,l.bF)(D,{class:"statistic-card"},{default:(0,l.k6)((()=>[t[29]||(t[29]=(0,l.Lk)("div",{class:"statistic-title"},"内容类型分布",-1)),(0,l.bF)(x),t[30]||(t[30]=(0,l.Lk)("div",{class:"statistic-chart",id:"typeChart"},null,-1))])),_:1})])),_:1}),(0,l.bF)(P,{span:8},{default:(0,l.k6)((()=>[(0,l.bF)(D,{class:"statistic-card"},{default:(0,l.k6)((()=>[t[31]||(t[31]=(0,l.Lk)("div",{class:"statistic-title"},"日内容发布趋势",-1)),(0,l.bF)(x),t[32]||(t[32]=(0,l.Lk)("div",{class:"statistic-chart",id:"trendChart"},null,-1))])),_:1})])),_:1})])),_:1})])}var m=a(2032);const b={name:"ContentManagement",data(){return{loading:!1,selectedIds:[],total:0,dateRange:[],contentTypeMap:{transfer:"驿站转让",equipment:"设备交易",recruitment:"招聘求职",substitution:"顶班服务"},statusMap:{0:"已下架",1:"已上架",2:"违规下架"},typeOptions:[{label:"驿站转让",value:"transfer"},{label:"设备交易",value:"equipment"},{label:"招聘求职",value:"recruitment"},{label:"顶班服务",value:"substitution"}],violationOptions:[{label:"虚假信息",value:"虚假信息"},{label:"重复发布",value:"重复发布"},{label:"违法内容",value:"违法内容"},{label:"侵犯他人权益",value:"侵犯他人权益"},{label:"其他违规",value:"其他违规"}],unpublishOptions:[{label:"内容过期",value:"内容过期"},{label:"应发布者要求",value:"应发布者要求"},{label:"交易已完成",value:"交易已完成"},{label:"其他原因",value:"其他原因"}],contentList:[{id:1,title:"北京市海淀区中关村驿站转让",contentType:"transfer",publisher:"张三",status:"1",auditTime:"2023-04-15 10:30:00",publishTime:"2023-04-15 10:35:00",unpublishReason:""},{id:2,title:"二手快递柜设备出售",contentType:"equipment",publisher:"李四",status:"1",auditTime:"2023-04-15 15:20:00",publishTime:"2023-04-15 15:25:00",unpublishReason:""},{id:3,title:"招聘驿站兼职人员",contentType:"recruitment",publisher:"王五",status:"0",auditTime:"2023-04-16 09:15:00",publishTime:"2023-04-16 09:20:00",unpublishReason:"内容过期"},{id:4,title:"周末顶班服务",contentType:"substitution",publisher:"赵六",status:"2",auditTime:"2023-04-16 11:45:00",publishTime:"2023-04-16 11:50:00",unpublishReason:"虚假信息"}],queryParams:{pageNum:1,pageSize:10,keyword:"",type:"",status:"",startDate:"",endDate:""},violationForm:{id:void 0,reason:"",remark:""},unpublishForm:{id:void 0,reason:"",remark:""},violationDialogVisible:!1,unpublishDialogVisible:!1,currentContent:{},violationChart:null,typeChart:null,trendChart:null}},mounted(){this.initCharts()},methods:{getTypeTag(e){const t={transfer:"primary",equipment:"success",recruitment:"warning",substitution:"info"};return t[e]||""},handleQuery(){this.queryParams.pageNum=1,this.dateRange&&this.dateRange.length>0?(this.queryParams.startDate=this.dateRange[0],this.queryParams.endDate=this.dateRange[1]):(this.queryParams.startDate="",this.queryParams.endDate=""),console.log("查询参数：",this.queryParams)},resetQuery(){this.dateRange=[],this.queryParams={pageNum:1,pageSize:10,keyword:"",type:"",status:"",startDate:"",endDate:""},this.handleQuery()},handleSelectionChange(e){this.selectedIds=e.map((e=>e.id))},handleView(e){this.$router.push(`/content/audit/detail/${e.id}/${e.contentType}`)},handlePublish(e){this.$confirm(`确认上架内容"${e.title}"?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{console.log("上架内容ID：",e.id),this.$message({type:"success",message:"上架成功!"})})).catch((()=>{}))},handleUnpublish(e){this.currentContent=e,this.unpublishForm={id:e.id,reason:"",remark:""},this.unpublishDialogVisible=!0},handleViolation(e){this.currentContent=e,this.violationForm={id:e.id,reason:"",remark:""},this.violationDialogVisible=!0},submitViolation(){this.violationForm.reason?(console.log("违规下架内容ID：",this.violationForm.id),console.log("违规类型：",this.violationForm.reason),console.log("补充说明：",this.violationForm.remark),this.violationDialogVisible=!1,this.$message({type:"success",message:"违规下架成功!"})):this.$message({type:"warning",message:"请选择违规类型"})},submitUnpublish(){this.unpublishForm.reason?(console.log("下架内容ID：",this.unpublishForm.id),console.log("下架原因：",this.unpublishForm.reason),console.log("补充说明：",this.unpublishForm.remark),this.unpublishDialogVisible=!1,this.$message({type:"success",message:"下架成功!"})):this.$message({type:"warning",message:"请选择下架原因"})},handleBatchPublish(){0!==this.selectedIds.length?this.$confirm(`确认批量上架所选的${this.selectedIds.length}条内容?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{console.log("批量上架内容IDs：",this.selectedIds),this.$message({type:"success",message:"批量上架成功!"})})).catch((()=>{})):this.$message({type:"warning",message:"请至少选择一条记录"})},handleBatchUnpublish(){0!==this.selectedIds.length?(this.unpublishForm={id:this.selectedIds.join(","),reason:"",remark:""},this.unpublishDialogVisible=!0):this.$message({type:"warning",message:"请至少选择一条记录"})},handleBatchViolation(){0!==this.selectedIds.length?(this.violationForm={id:this.selectedIds.join(","),reason:"",remark:""},this.violationDialogVisible=!0):this.$message({type:"warning",message:"请至少选择一条记录"})},handleSizeChange(e){this.queryParams.pageSize=e,this.handleQuery()},handleCurrentChange(e){this.queryParams.pageNum=e,this.handleQuery()},initCharts(){this.$nextTick((()=>{this.violationChart=m.Ts(document.getElementById("violationChart")),this.violationChart.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:["虚假信息","重复发布","违法内容","侵犯他人权益","其他违规"]},series:[{name:"违规类型",type:"pie",radius:"50%",data:[{value:48,name:"虚假信息"},{value:25,name:"重复发布"},{value:12,name:"违法内容"},{value:8,name:"侵犯他人权益"},{value:15,name:"其他违规"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}),this.typeChart=m.Ts(document.getElementById("typeChart")),this.typeChart.setOption({tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:["驿站转让","设备交易","招聘求职","顶班服务"]},series:[{name:"内容类型",type:"pie",radius:"50%",data:[{value:35,name:"驿站转让"},{value:42,name:"设备交易"},{value:78,name:"招聘求职"},{value:56,name:"顶班服务"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}),this.trendChart=m.Ts(document.getElementById("trendChart")),this.trendChart.setOption({tooltip:{trigger:"axis"},xAxis:{type:"category",data:["周一","周二","周三","周四","周五","周六","周日"]},yAxis:{type:"value"},series:[{data:[18,25,32,28,35,42,30],type:"line",smooth:!0}]}),window.addEventListener("resize",(()=>{this.violationChart.resize(),this.typeChart.resize(),this.trendChart.resize()}))}))}}};var c=a(1241);const g=(0,c.A)(b,[["render",p],["__scopeId","data-v-cf585e1c"]]),y=g}}]);