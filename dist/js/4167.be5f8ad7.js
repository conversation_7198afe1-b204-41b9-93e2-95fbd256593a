"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[4167],{1981:(e,t,a)=>{function l(e,t="YYYY-MM-DD HH:mm:ss"){if(!e)return"";let a;"object"===typeof e?a=e:("string"===typeof e&&/^[0-9]+$/.test(e)&&(e=parseInt(e)),"number"===typeof e&&10===e.toString().length&&(e*=1e3),a=new Date(e));const l={"M+":a.getMonth()+1,"D+":a.getDate(),"H+":a.getHours(),"m+":a.getMinutes(),"s+":a.getSeconds(),"q+":Math.floor((a.getMonth()+3)/3),S:a.getMilliseconds()};/(Y+)/.test(t)&&(t=t.replace(RegExp.$1,(a.getFullYear()+"").substr(4-RegExp.$1.length)));for(let i in l)new RegExp("("+i+")").test(t)&&(t=t.replace(RegExp.$1,1===RegExp.$1.length?l[i]:("00"+l[i]).substr((""+l[i]).length)));return t}a.d(t,{r6:()=>l})},4167:(e,t,a)=>{a.r(t),a.d(t,{default:()=>W});var l=a(6768),i=a(5130),n=a(4232);const s={class:"app-container"},d={class:"filter-item"},r={class:"table-header"},o={class:"statistics-cards"},u={class:"stat-card primary"},p={class:"stat-value"},c={class:"stat-card warning"},g={class:"stat-value"},v={class:"stat-card success"},m={class:"stat-value"},h={class:"stat-card danger"},b={class:"stat-value"},f={class:"user-info"},k={class:"user-detail"},y={class:"user-phone"},_={key:0},C={key:1},w={class:"dialog-footer"};function F(e,t,a,F,L,x){const z=(0,l.g2)("el-input"),S=(0,l.g2)("el-option"),V=(0,l.g2)("el-select"),R=(0,l.g2)("el-date-picker"),D=(0,l.g2)("el-button"),W=(0,l.g2)("el-card"),E=(0,l.g2)("el-table-column"),j=(0,l.g2)("el-avatar"),T=(0,l.g2)("el-tag"),A=(0,l.g2)("el-table"),K=(0,l.g2)("pagination"),M=(0,l.g2)("el-form-item"),$=(0,l.g2)("el-form"),X=(0,l.g2)("el-dialog"),Q=(0,l.gN)("waves"),I=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",s,[(0,l.bF)(W,{class:"filter-container"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",d,[(0,l.bF)(z,{modelValue:F.listQuery.search,"onUpdate:modelValue":t[0]||(t[0]=e=>F.listQuery.search=e),placeholder:"搜索用户昵称/手机",clearable:"",onKeyup:(0,i.jR)(F.handleFilter,["enter","native"]),style:{width:"200px"}},null,8,["modelValue","onKeyup"]),(0,l.bF)(V,{modelValue:F.listQuery.status,"onUpdate:modelValue":t[1]||(t[1]=e=>F.listQuery.status=e),placeholder:"认证状态",clearable:"",style:{width:"130px"},onChange:F.handleFilter},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(F.statusOptions,(e=>((0,l.uX)(),(0,l.Wv)(S,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"]),(0,l.bF)(R,{modelValue:F.dateRange,"onUpdate:modelValue":t[2]||(t[2]=e=>F.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",style:{width:"260px"},onChange:F.handleDateChange},null,8,["modelValue","onChange"]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(D,{type:"primary",icon:"Search",onClick:F.handleFilter},{default:(0,l.k6)((()=>t[6]||(t[6]=[(0,l.eW)("搜索")]))),_:1},8,["onClick"])),[[Q]]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(D,{icon:"Refresh",onClick:F.resetQuery},{default:(0,l.k6)((()=>t[7]||(t[7]=[(0,l.eW)("重置")]))),_:1},8,["onClick"])),[[Q]])])])),_:1}),(0,l.bF)(W,null,{default:(0,l.k6)((()=>[(0,l.Lk)("div",r,[(0,l.Lk)("div",o,[(0,l.Lk)("div",u,[(0,l.Lk)("div",p,(0,n.v_)(F.statistics.totalCount),1),t[8]||(t[8]=(0,l.Lk)("div",{class:"stat-label"},"总认证申请",-1))]),(0,l.Lk)("div",c,[(0,l.Lk)("div",g,(0,n.v_)(F.statistics.pendingCount),1),t[9]||(t[9]=(0,l.Lk)("div",{class:"stat-label"},"待审核",-1))]),(0,l.Lk)("div",v,[(0,l.Lk)("div",m,(0,n.v_)(F.statistics.approvedCount),1),t[10]||(t[10]=(0,l.Lk)("div",{class:"stat-label"},"已通过",-1))]),(0,l.Lk)("div",h,[(0,l.Lk)("div",b,(0,n.v_)(F.statistics.rejectedCount),1),t[11]||(t[11]=(0,l.Lk)("div",{class:"stat-label"},"已驳回",-1))])]),(0,l.Lk)("div",null,[(0,l.bF)(D,{type:"success",size:"small",onClick:F.exportData},{default:(0,l.k6)((()=>t[12]||(t[12]=[(0,l.eW)("导出数据")]))),_:1},8,["onClick"])])]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(A,{data:F.list,border:"",fit:"","highlight-current-row":"",style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(E,{label:"ID",prop:"id",align:"center",width:"80"}),(0,l.bF)(E,{label:"用户信息","min-width":"150"},{default:(0,l.k6)((({row:e})=>[(0,l.Lk)("div",f,[(0,l.bF)(j,{src:e.avatar||"/static/default-avatar.png",size:30},null,8,["src"]),(0,l.Lk)("div",k,[(0,l.Lk)("div",null,(0,n.v_)(e.nickname),1),(0,l.Lk)("div",y,(0,n.v_)(e.phone),1)])])])),_:1}),(0,l.bF)(E,{label:"真实姓名",prop:"real_name",align:"center",width:"120"}),(0,l.bF)(E,{label:"身份证号",align:"center",width:"180"},{default:(0,l.k6)((({row:e})=>[(0,l.eW)((0,n.v_)(F.formatIdCard(e.id_card)),1)])),_:1}),(0,l.bF)(E,{label:"申请时间",width:"150",align:"center"},{default:(0,l.k6)((({row:e})=>[(0,l.Lk)("span",null,(0,n.v_)(F.formatDateTime(e.created_at)),1)])),_:1}),(0,l.bF)(E,{label:"认证方式",align:"center",width:"100"},{default:(0,l.k6)((({row:e})=>[(0,l.bF)(T,{type:"info"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(F.getVerifyTypeText(e.verify_type)),1)])),_:2},1024)])),_:1}),(0,l.bF)(E,{label:"状态",prop:"status",align:"center",width:"100"},{default:(0,l.k6)((({row:e})=>[(0,l.bF)(T,{type:F.getStatusType(e.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(F.getStatusText(e.status)),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(E,{label:"审核时间",width:"150",align:"center"},{default:(0,l.k6)((({row:e})=>[e.verified_at?((0,l.uX)(),(0,l.CE)("span",_,(0,n.v_)(F.formatDateTime(e.verified_at)),1)):((0,l.uX)(),(0,l.CE)("span",C,"--"))])),_:1}),(0,l.bF)(E,{label:"操作",align:"center",width:"200","class-name":"small-padding fixed-width"},{default:(0,l.k6)((({row:e})=>[0===e.status?((0,l.uX)(),(0,l.Wv)(D,{key:0,type:"success",size:"small",onClick:t=>F.handleApprove(e)},{default:(0,l.k6)((()=>t[13]||(t[13]=[(0,l.eW)("通过")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),0===e.status?((0,l.uX)(),(0,l.Wv)(D,{key:1,type:"danger",size:"small",onClick:t=>F.handleReject(e)},{default:(0,l.k6)((()=>t[14]||(t[14]=[(0,l.eW)("驳回")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),(0,l.bF)(D,{type:"primary",size:"small",onClick:t=>F.handleDetail(e)},{default:(0,l.k6)((()=>t[15]||(t[15]=[(0,l.eW)("详情")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[I,F.listLoading]]),(0,l.bo)((0,l.bF)(K,{total:F.total,page:F.listQuery.page,limit:F.listQuery.limit,onPagination:e.getList},null,8,["total","page","limit","onPagination"]),[[i.aG,F.total>0]])])),_:1}),(0,l.bF)(X,{title:"approve"===F.auditAction?"通过实名认证":"驳回实名认证",modelValue:F.auditDialogVisible,"onUpdate:modelValue":t[5]||(t[5]=e=>F.auditDialogVisible=e),width:"400px"},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",w,[(0,l.bF)(D,{onClick:t[4]||(t[4]=e=>F.auditDialogVisible=!1)},{default:(0,l.k6)((()=>t[16]||(t[16]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(D,{type:"approve"===F.auditAction?"success":"danger",onClick:F.submitAudit,loading:F.auditSubmitting},{default:(0,l.k6)((()=>t[17]||(t[17]=[(0,l.eW)("确 定")]))),_:1},8,["type","onClick","loading"])])])),default:(0,l.k6)((()=>[(0,l.bF)($,{model:F.auditForm,"label-width":"80px"},{default:(0,l.k6)((()=>[(0,l.bF)(M,{label:"备注"},{default:(0,l.k6)((()=>[(0,l.bF)(z,{modelValue:F.auditForm.remark,"onUpdate:modelValue":t[3]||(t[3]=e=>F.auditForm.remark=e),type:"textarea",rows:3,placeholder:"approve"===F.auditAction?"通过原因（选填）":"驳回原因（必填）"},null,8,["modelValue","placeholder"])])),_:1})])),_:1},8,["model"])])),_:1},8,["title","modelValue"])])}var L=a(144),x=a(7093),z=a(9190),S=a(1981);const V={name:"IdentityList",components:{Pagination:z.A},directives:{waves:x.A},setup(){const e=(0,L.KR)([]),t=(0,L.KR)([]),a=(0,L.KR)(0),i=(0,L.KR)(!1),n=(0,L.KR)(!1),s=(0,L.KR)(!1),d=(0,L.KR)("approve"),r=(0,L.KR)(null),o=(0,L.Kh)({page:1,limit:10,status:"",search:"",start_date:"",end_date:""}),u=(0,L.Kh)({totalCount:0,pendingCount:0,approvedCount:0,rejectedCount:0}),p=(0,L.Kh)({id:null,status:0,remark:""}),c=[{label:"待审核",value:0},{label:"已通过",value:1},{label:"已驳回",value:2}],g=[{id:1,nickname:"张三",phone:"138****1234",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",real_name:"张三",id_card:"******************",created_at:"2023-05-20 10:00:00",verified_at:null,verify_type:1,status:0},{id:2,nickname:"李四",phone:"139****5678",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",real_name:"李四",id_card:"******************",created_at:"2023-05-19 15:30:00",verified_at:"2023-05-19 16:30:00",verify_type:2,status:1},{id:3,nickname:"王五",phone:"137****9012",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",real_name:"王五",id_card:"******************",created_at:"2023-05-18 09:15:00",verified_at:"2023-05-18 10:20:00",verify_type:1,status:2}],v={totalCount:235,pendingCount:42,approvedCount:180,rejectedCount:13},m=()=>{i.value=!0,setTimeout((()=>{t.value=g,a.value=g.length,i.value=!1}),500),h()},h=()=>{setTimeout((()=>{u.totalCount=v.totalCount,u.pendingCount=v.pendingCount,u.approvedCount=v.approvedCount,u.rejectedCount=v.rejectedCount}),300)},b=()=>{o.page=1,m()},f=()=>{e.value=[],Object.assign(o,{page:1,limit:10,status:"",search:"",start_date:"",end_date:""}),m()},k=e=>{e?(o.start_date=e[0],o.end_date=e[1]):(o.start_date="",o.end_date="")},y=e=>{const t={0:"warning",1:"success",2:"danger"};return t[e]},_=e=>{const t={0:"待审核",1:"已通过",2:"已驳回"};return t[e]},C=e=>{const t={1:"身份证认证",2:"人脸认证"};return t[e]||"未知"},w=e=>e?e.replace(/^(.{6})(.*)(.{4})$/,"$1********$3"):"",F=e=>{r.value=e,d.value="approve",p.id=e.id,p.status=1,p.remark="",n.value=!0},x=e=>{r.value=e,d.value="reject",p.id=e.id,p.status=2,p.remark="",n.value=!0},z=()=>{"reject"!==d.value||p.remark?(s.value=!0,setTimeout((()=>{const e=t.value.findIndex((e=>e.id===p.id));e>-1&&(t.value[e].status=p.status,t.value[e].verified_at=(0,S.r6)(new Date)),"approve"===d.value?(u.pendingCount--,u.approvedCount++):(u.pendingCount--,u.rejectedCount++),s.value=!1,n.value=!1,ElMessage({type:"success",message:"approve"===d.value?"认证审核已通过":"认证审核已驳回"})}),500)):ElMessage.warning("请填写驳回原因")},V=e=>{router.push(`/identity/detail/${e.id}`)},R=()=>{ElMessage.success("数据导出功能开发中")};return(0,l.sV)((()=>{m()})),{dateRange:e,list:t,total:a,listLoading:i,listQuery:o,statistics:u,statusOptions:c,auditDialogVisible:n,auditSubmitting:s,auditAction:d,auditForm:p,handleFilter:b,resetQuery:f,handleDateChange:k,getStatusType:y,getStatusText:_,getVerifyTypeText:C,formatIdCard:w,formatDateTime:S.r6,handleApprove:F,handleReject:x,submitAudit:z,handleDetail:V,exportData:R}}};var R=a(1241);const D=(0,R.A)(V,[["render",F],["__scopeId","data-v-939c0e7e"]]),W=D},7093:(e,t,a)=>{a.d(t,{A:()=>n});const l={mounted(e,t){e.addEventListener("click",(a=>{const l=Object.assign({},t.value),i=Object.assign({ele:e,type:"hit",color:"rgba(0, 0, 0, 0.15)"},l),n=i.ele;if(n){n.style.position="relative",n.style.overflow="hidden";const e=n.getBoundingClientRect();let t=n.querySelector(".waves-ripple");switch(t?t.className="waves-ripple":(t=document.createElement("span"),t.className="waves-ripple",t.style.height=t.style.width=Math.max(e.width,e.height)+"px",n.appendChild(t)),i.type){case"center":t.style.top=e.height/2-t.offsetHeight/2+"px",t.style.left=e.width/2-t.offsetWidth/2+"px";break;default:t.style.top=(a.pageY-e.top-t.offsetHeight/2-document.documentElement.scrollTop||document.body.scrollTop)+"px",t.style.left=(a.pageX-e.left-t.offsetWidth/2-document.documentElement.scrollLeft||document.body.scrollLeft)+"px"}return t.style.backgroundColor=i.color,t.className="waves-ripple z-active",!1}}),!1)}},i=l,n=i},9190:(e,t,a)=>{a.d(t,{A:()=>o});var l=a(6768),i=a(4232);function n(e,t,a,n,s,d){const r=(0,l.g2)("el-pagination");return(0,l.uX)(),(0,l.CE)("div",{class:(0,i.C4)([{hidden:a.hidden},"pagination-container"])},[(0,l.bF)(r,{background:a.background,"current-page":d.currentPage,"onUpdate:currentPage":t[0]||(t[0]=e=>d.currentPage=e),"page-size":d.pageSize,"onUpdate:pageSize":t[1]||(t[1]=e=>d.pageSize=e),layout:a.layout,"page-sizes":a.pageSizes,total:a.total,onSizeChange:d.handleSizeChange,onCurrentChange:d.handleCurrentChange},null,8,["background","current-page","page-size","layout","page-sizes","total","onSizeChange","onCurrentChange"])],2)}const s={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:10},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},emits:["update:page","update:limit","pagination"],computed:{currentPage:{get(){return this.page},set(e){this.$emit("update:page",e)}},pageSize:{get(){return this.limit},set(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange(e){this.$emit("pagination",{page:this.currentPage,limit:e})},handleCurrentChange(e){this.$emit("pagination",{page:e,limit:this.pageSize})}}};var d=a(1241);const r=(0,d.A)(s,[["render",n],["__scopeId","data-v-5035dd33"]]),o=r}}]);