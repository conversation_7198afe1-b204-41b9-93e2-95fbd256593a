"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[2329],{2329:(e,a,t)=>{t.r(a),t.d(a,{default:()=>z});var l=t(6768),s=t(4232);const n={class:"transfer-container"},i={class:"stats-cards"},r={class:"stat-card"},o={class:"stat-content"},d={class:"stat-value"},c={class:"stat-card"},u={class:"stat-content"},p={class:"stat-value"},h={class:"stat-card"},m={class:"stat-content"},b={class:"stat-value"},g={class:"stat-card"},f={class:"stat-content"},k={class:"stat-value"},v={class:"filter-container"},F={class:"table-container"},L={class:"station-info"},_={class:"station-name"},w={class:"station-address"},y={class:"price"},T={class:"pagination-container"};function C(e,a,t,C,x,N){const V=(0,l.g2)("el-input"),z=(0,l.g2)("el-form-item"),S=(0,l.g2)("el-option"),W=(0,l.g2)("el-select"),A=(0,l.g2)("el-input-number"),P=(0,l.g2)("el-button"),U=(0,l.g2)("el-form"),$=(0,l.g2)("el-table-column"),D=(0,l.g2)("el-tag"),B=(0,l.g2)("el-table"),X=(0,l.g2)("el-pagination"),I=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",n,[a[21]||(a[21]=(0,l.Lk)("div",{class:"page-header"},[(0,l.Lk)("h2",null,[(0,l.Lk)("i",{class:"el-icon-s-shop"}),(0,l.eW)(" 驿站转让管理")]),(0,l.Lk)("p",null,"管理驿站转让信息，审核转让申请")],-1)),(0,l.Lk)("div",i,[(0,l.Lk)("div",r,[a[8]||(a[8]=(0,l.Lk)("div",{class:"stat-icon pending"},[(0,l.Lk)("i",{class:"el-icon-time"})],-1)),(0,l.Lk)("div",o,[(0,l.Lk)("div",d,(0,s.v_)(x.stats.pending),1),a[7]||(a[7]=(0,l.Lk)("div",{class:"stat-label"},"待审核",-1))])]),(0,l.Lk)("div",c,[a[10]||(a[10]=(0,l.Lk)("div",{class:"stat-icon published"},[(0,l.Lk)("i",{class:"el-icon-check"})],-1)),(0,l.Lk)("div",u,[(0,l.Lk)("div",p,(0,s.v_)(x.stats.published),1),a[9]||(a[9]=(0,l.Lk)("div",{class:"stat-label"},"已发布",-1))])]),(0,l.Lk)("div",h,[a[12]||(a[12]=(0,l.Lk)("div",{class:"stat-icon completed"},[(0,l.Lk)("i",{class:"el-icon-success"})],-1)),(0,l.Lk)("div",m,[(0,l.Lk)("div",b,(0,s.v_)(x.stats.completed),1),a[11]||(a[11]=(0,l.Lk)("div",{class:"stat-label"},"已完成",-1))])]),(0,l.Lk)("div",g,[a[14]||(a[14]=(0,l.Lk)("div",{class:"stat-icon total"},[(0,l.Lk)("i",{class:"el-icon-s-data"})],-1)),(0,l.Lk)("div",f,[(0,l.Lk)("div",k,(0,s.v_)(x.stats.total),1),a[13]||(a[13]=(0,l.Lk)("div",{class:"stat-label"},"总转让数",-1))])])]),(0,l.Lk)("div",v,[(0,l.bF)(U,{inline:!0,model:x.searchForm,class:"search-form"},{default:(0,l.k6)((()=>[(0,l.bF)(z,{label:"转让编号"},{default:(0,l.k6)((()=>[(0,l.bF)(V,{modelValue:x.searchForm.transferNo,"onUpdate:modelValue":a[0]||(a[0]=e=>x.searchForm.transferNo=e),placeholder:"请输入转让编号",clearable:""},null,8,["modelValue"])])),_:1}),(0,l.bF)(z,{label:"转让类型"},{default:(0,l.k6)((()=>[(0,l.bF)(W,{modelValue:x.searchForm.transferType,"onUpdate:modelValue":a[1]||(a[1]=e=>x.searchForm.transferType=e),placeholder:"请选择",clearable:""},{default:(0,l.k6)((()=>[(0,l.bF)(S,{label:"完全转让",value:"full_transfer"}),(0,l.bF)(S,{label:"股份转让",value:"share_transfer"}),(0,l.bF)(S,{label:"租赁",value:"lease"})])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(z,{label:"状态"},{default:(0,l.k6)((()=>[(0,l.bF)(W,{modelValue:x.searchForm.status,"onUpdate:modelValue":a[2]||(a[2]=e=>x.searchForm.status=e),placeholder:"请选择",clearable:""},{default:(0,l.k6)((()=>[(0,l.bF)(S,{label:"待审核",value:"pending"}),(0,l.bF)(S,{label:"已发布",value:"published"}),(0,l.bF)(S,{label:"协商中",value:"negotiating"}),(0,l.bF)(S,{label:"已完成",value:"completed"}),(0,l.bF)(S,{label:"已取消",value:"cancelled"})])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(z,{label:"价格范围"},{default:(0,l.k6)((()=>[(0,l.bF)(A,{modelValue:x.searchForm.minPrice,"onUpdate:modelValue":a[3]||(a[3]=e=>x.searchForm.minPrice=e),placeholder:"最低价",min:0},null,8,["modelValue"]),a[15]||(a[15]=(0,l.Lk)("span",{style:{margin:"0 8px"}},"-",-1)),(0,l.bF)(A,{modelValue:x.searchForm.maxPrice,"onUpdate:modelValue":a[4]||(a[4]=e=>x.searchForm.maxPrice=e),placeholder:"最高价",min:0},null,8,["modelValue"])])),_:1}),(0,l.bF)(z,null,{default:(0,l.k6)((()=>[(0,l.bF)(P,{type:"primary",onClick:N.handleSearch},{default:(0,l.k6)((()=>a[16]||(a[16]=[(0,l.Lk)("i",{class:"el-icon-search"},null,-1),(0,l.eW)(" 搜索 ")]))),_:1},8,["onClick"]),(0,l.bF)(P,{onClick:N.handleReset},{default:(0,l.k6)((()=>a[17]||(a[17]=[(0,l.Lk)("i",{class:"el-icon-refresh"},null,-1),(0,l.eW)(" 重置 ")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])]),(0,l.Lk)("div",F,[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(B,{data:x.transferList,style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)($,{prop:"transferNo",label:"转让编号",width:"140"}),(0,l.bF)($,{label:"驿站信息","min-width":"200"},{default:(0,l.k6)((e=>[(0,l.Lk)("div",L,[(0,l.Lk)("div",_,(0,s.v_)(e.row.stationName),1),(0,l.Lk)("div",w,(0,s.v_)(e.row.stationAddress),1)])])),_:1}),(0,l.bF)($,{prop:"title",label:"转让标题","min-width":"180"}),(0,l.bF)($,{prop:"transferType",label:"转让类型",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(D,{type:N.getTransferTypeColor(e.row.transferType)},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(N.getTransferTypeText(e.row.transferType)),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)($,{prop:"price",label:"转让价格",width:"120"},{default:(0,l.k6)((e=>[(0,l.Lk)("span",y,"¥"+(0,s.v_)(N.formatMoney(e.row.price)),1)])),_:1}),(0,l.bF)($,{prop:"sellerName",label:"转让方",width:"100"}),(0,l.bF)($,{prop:"viewCount",label:"浏览量",width:"80"}),(0,l.bF)($,{prop:"status",label:"状态",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(D,{type:N.getStatusColor(e.row.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(N.getStatusText(e.row.status)),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)($,{prop:"publishedAt",label:"发布时间",width:"160"},{default:(0,l.k6)((e=>[(0,l.eW)((0,s.v_)(N.formatTime(e.row.publishedAt)),1)])),_:1}),(0,l.bF)($,{label:"操作",width:"200",fixed:"right"},{default:(0,l.k6)((e=>[(0,l.bF)(P,{type:"text",size:"small",onClick:a=>N.handleView(e.row)},{default:(0,l.k6)((()=>a[18]||(a[18]=[(0,l.Lk)("i",{class:"el-icon-view"},null,-1),(0,l.eW)(" 查看 ")]))),_:2},1032,["onClick"]),"pending"===e.row.status?((0,l.uX)(),(0,l.Wv)(P,{key:0,type:"text",size:"small",onClick:a=>N.handleApprove(e.row)},{default:(0,l.k6)((()=>a[19]||(a[19]=[(0,l.Lk)("i",{class:"el-icon-check"},null,-1),(0,l.eW)(" 审核 ")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0),(0,l.bF)(P,{type:"text",size:"small",class:"danger",onClick:a=>N.handleDelete(e.row)},{default:(0,l.k6)((()=>a[20]||(a[20]=[(0,l.Lk)("i",{class:"el-icon-delete"},null,-1),(0,l.eW)(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[I,x.loading]]),(0,l.Lk)("div",T,[(0,l.bF)(X,{"current-page":x.pagination.page,"onUpdate:currentPage":a[5]||(a[5]=e=>x.pagination.page=e),"page-size":x.pagination.limit,"onUpdate:pageSize":a[6]||(a[6]=e=>x.pagination.limit=e),"page-sizes":[10,20,50,100],total:x.pagination.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:N.handleSizeChange,onCurrentChange:N.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])])])}const x={name:"StationTransfer",data(){return{loading:!1,transferList:[],searchForm:{transferNo:"",transferType:"",status:"",minPrice:null,maxPrice:null},pagination:{page:1,limit:20,total:0},stats:{pending:0,published:0,completed:0,total:0}}},mounted(){this.loadStats(),this.loadTransferList()},methods:{async loadStats(){this.stats={pending:5,published:12,completed:8,total:25}},async loadTransferList(){this.loading=!0;try{setTimeout((()=>{this.transferList=[{id:1,transferNo:"TF202401001",stationName:"中关村驿站",stationAddress:"北京市海淀区中关村大街1号",title:"中关村核心地段驿站转让",transferType:"full_transfer",price:15e4,sellerName:"张三",viewCount:156,status:"published",publishedAt:new Date},{id:2,transferNo:"TF202401002",stationName:"望京驿站",stationAddress:"北京市朝阳区望京SOHO",title:"望京商务区驿站股份转让",transferType:"share_transfer",price:8e4,sellerName:"李四",viewCount:89,status:"pending",publishedAt:new Date}],this.pagination.total=2,this.loading=!1}),1e3)}catch(e){this.$message.error("加载转让列表失败"),this.loading=!1}},handleSearch(){this.pagination.page=1,this.loadTransferList()},handleReset(){this.searchForm={transferNo:"",transferType:"",status:"",minPrice:null,maxPrice:null},this.pagination.page=1,this.loadTransferList()},handleView(e){this.$message.info("查看转让详情功能开发中")},handleApprove(e){this.$confirm("确定要审核通过这个转让申请吗？","审核确认",{confirmButtonText:"通过",cancelButtonText:"取消",type:"warning"}).then((()=>{e.status="published",this.$message.success("审核通过")}))},handleDelete(e){this.$confirm("确定要删除这个转让信息吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$message.success("删除成功")}))},handleSizeChange(e){this.pagination.limit=e,this.loadTransferList()},handleCurrentChange(e){this.pagination.page=e,this.loadTransferList()},getTransferTypeColor(e){const a={full_transfer:"primary",share_transfer:"success",lease:"warning"};return a[e]||"info"},getTransferTypeText(e){const a={full_transfer:"完全转让",share_transfer:"股份转让",lease:"租赁"};return a[e]||"未知"},getStatusColor(e){const a={pending:"warning",published:"primary",negotiating:"info",completed:"success",cancelled:"danger"};return a[e]||"info"},getStatusText(e){const a={pending:"待审核",published:"已发布",negotiating:"协商中",completed:"已完成",cancelled:"已取消"};return a[e]||"未知"},formatMoney(e){return new Intl.NumberFormat("zh-CN").format(e)},formatTime(e){return e?new Date(e).toLocaleString("zh-CN"):"-"}}};var N=t(1241);const V=(0,N.A)(x,[["render",C],["__scopeId","data-v-abfbe422"]]),z=V}}]);