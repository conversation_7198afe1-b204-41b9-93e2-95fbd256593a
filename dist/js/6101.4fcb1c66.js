"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[6101],{1981:(e,a,t)=>{function l(e,a="YYYY-MM-DD HH:mm:ss"){if(!e)return"";let t;"object"===typeof e?t=e:("string"===typeof e&&/^[0-9]+$/.test(e)&&(e=parseInt(e)),"number"===typeof e&&10===e.toString().length&&(e*=1e3),t=new Date(e));const l={"M+":t.getMonth()+1,"D+":t.getDate(),"H+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),S:t.getMilliseconds()};/(Y+)/.test(a)&&(a=a.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length)));for(let r in l)new RegExp("("+r+")").test(a)&&(a=a.replace(RegExp.$1,1===RegExp.$1.length?l[r]:("00"+l[r]).substr((""+l[r]).length)));return a}t.d(a,{r6:()=>l})},6101:(e,a,t)=>{t.r(a),t.d(a,{default:()=>B});var l=t(6768),r=t(4232);const s={class:"app-container"},i={class:"page-header"},d={class:"card-header"},n={class:"status-tag"},c={class:"id-card-container"},o={class:"id-card-item"},u={class:"image-container"},k={class:"image-placeholder"},_={class:"id-card-item"},v={class:"image-container"},p={class:"image-placeholder"},b={key:0,class:"id-card-item"},g={class:"image-container"},m={class:"image-placeholder"},f={class:"log-operator"},h={class:"user-header"},F={class:"user-info"},y={class:"nickname"},L={class:"user-id"},W={class:"user-stats"},w={class:"stat-item"},C={class:"stat-value"},D={class:"stat-item"},x={class:"stat-value"},T={class:"stat-item"},R={class:"stat-value"},E={class:"audit-form"},S={class:"audit-buttons"};function K(e,a,t,K,M,X){const I=(0,l.g2)("el-page-header"),V=(0,l.g2)("el-tag"),Y=(0,l.g2)("el-descriptions-item"),j=(0,l.g2)("el-descriptions"),$=(0,l.g2)("el-card"),B=(0,l.g2)("Picture"),H=(0,l.g2)("el-icon"),Q=(0,l.g2)("el-image"),z=(0,l.g2)("el-timeline-item"),A=(0,l.g2)("el-timeline"),P=(0,l.g2)("el-col"),q=(0,l.g2)("el-avatar"),G=(0,l.g2)("el-input"),J=(0,l.g2)("el-form-item"),N=(0,l.g2)("el-button"),O=(0,l.g2)("el-form"),U=(0,l.g2)("el-row"),Z=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",s,[(0,l.Lk)("div",i,[(0,l.bF)(I,{onBack:K.goBack,title:"返回认证列表",content:"认证详情 #"+K.id},null,8,["onBack","content"])]),(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(U,{gutter:20},{default:(0,l.k6)((()=>[(0,l.bF)(P,{span:16},{default:(0,l.k6)((()=>[(0,l.bF)($,{class:"box-card",shadow:"hover"},{header:(0,l.k6)((()=>[(0,l.Lk)("div",d,[a[1]||(a[1]=(0,l.Lk)("span",null,"基本信息",-1)),(0,l.Lk)("div",n,[void 0!==K.detail.status?((0,l.uX)(),(0,l.Wv)(V,{key:0,type:K.getStatusType(K.detail.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.getStatusText(K.detail.status)),1)])),_:1},8,["type"])):(0,l.Q3)("",!0)])])])),default:(0,l.k6)((()=>[(0,l.bF)(j,{column:2,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(Y,{label:"用户ID"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.user_id),1)])),_:1}),(0,l.bF)(Y,{label:"申请时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.formatDateTime(K.detail.created_at)),1)])),_:1}),(0,l.bF)(Y,{label:"真实姓名"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.real_name),1)])),_:1}),(0,l.bF)(Y,{label:"身份证号"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.id_card),1)])),_:1}),(0,l.bF)(Y,{label:"手机号码"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.phone),1)])),_:1}),(0,l.bF)(Y,{label:"认证方式"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.getVerifyTypeText(K.detail.verify_type)),1)])),_:1}),(0,l.bF)(Y,{label:"审核时间",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.verified_at?K.formatDateTime(K.detail.verified_at):"--"),1)])),_:1}),(0,l.bF)(Y,{label:"审核备注",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.remark||"--"),1)])),_:1})])),_:1})])),_:1}),(0,l.bF)($,{class:"box-card id-card-section",shadow:"hover"},{header:(0,l.k6)((()=>a[2]||(a[2]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"证件照片")],-1)]))),default:(0,l.k6)((()=>[(0,l.Lk)("div",c,[(0,l.Lk)("div",o,[a[4]||(a[4]=(0,l.Lk)("div",{class:"card-title"},"身份证正面",-1)),(0,l.Lk)("div",u,[(0,l.bF)(Q,{src:K.detail.id_card_front,fit:"cover","preview-src-list":[K.detail.id_card_front]},{error:(0,l.k6)((()=>[(0,l.Lk)("div",k,[(0,l.bF)(H,null,{default:(0,l.k6)((()=>[(0,l.bF)(B)])),_:1}),a[3]||(a[3]=(0,l.Lk)("span",null,"暂无图片",-1))])])),_:1},8,["src","preview-src-list"])])]),(0,l.Lk)("div",_,[a[6]||(a[6]=(0,l.Lk)("div",{class:"card-title"},"身份证反面",-1)),(0,l.Lk)("div",v,[(0,l.bF)(Q,{src:K.detail.id_card_back,fit:"cover","preview-src-list":[K.detail.id_card_back]},{error:(0,l.k6)((()=>[(0,l.Lk)("div",p,[(0,l.bF)(H,null,{default:(0,l.k6)((()=>[(0,l.bF)(B)])),_:1}),a[5]||(a[5]=(0,l.Lk)("span",null,"暂无图片",-1))])])),_:1},8,["src","preview-src-list"])])]),2===K.detail.verify_type?((0,l.uX)(),(0,l.CE)("div",b,[a[8]||(a[8]=(0,l.Lk)("div",{class:"card-title"},"人脸识别照片",-1)),(0,l.Lk)("div",g,[(0,l.bF)(Q,{src:K.detail.face_image,fit:"cover","preview-src-list":[K.detail.face_image]},{error:(0,l.k6)((()=>[(0,l.Lk)("div",m,[(0,l.bF)(H,null,{default:(0,l.k6)((()=>[(0,l.bF)(B)])),_:1}),a[7]||(a[7]=(0,l.Lk)("span",null,"暂无图片",-1))])])),_:1},8,["src","preview-src-list"])])])):(0,l.Q3)("",!0)])])),_:1}),(0,l.bF)($,{class:"box-card",shadow:"hover"},{header:(0,l.k6)((()=>a[9]||(a[9]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"操作日志")],-1)]))),default:(0,l.k6)((()=>[(0,l.bF)(A,null,{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(K.logs,((e,a)=>((0,l.uX)(),(0,l.Wv)(z,{key:a,timestamp:e.created_at,type:K.getLogType(e.action)},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(e.content)+" ",1),(0,l.Lk)("div",f,"操作人: "+(0,r.v_)(e.operator),1)])),_:2},1032,["timestamp","type"])))),128))])),_:1})])),_:1})])),_:1}),(0,l.bF)(P,{span:8},{default:(0,l.k6)((()=>[(0,l.bF)($,{class:"box-card user-card",shadow:"hover"},{default:(0,l.k6)((()=>[(0,l.Lk)("div",h,[(0,l.bF)(q,{size:64,src:K.detail.avatar||"/static/default-avatar.png"},null,8,["src"]),(0,l.Lk)("div",F,[(0,l.Lk)("div",y,(0,r.v_)(K.detail.nickname||"未知用户"),1),(0,l.Lk)("div",L,"用户ID: "+(0,r.v_)(K.detail.user_id),1)])]),(0,l.Lk)("div",W,[(0,l.Lk)("div",w,[(0,l.Lk)("div",C,(0,r.v_)(K.userStats.orderCount),1),a[10]||(a[10]=(0,l.Lk)("div",{class:"stat-label"},"订单数",-1))]),(0,l.Lk)("div",D,[(0,l.Lk)("div",x,(0,r.v_)(K.userStats.commentCount),1),a[11]||(a[11]=(0,l.Lk)("div",{class:"stat-label"},"评价数",-1))]),(0,l.Lk)("div",T,[(0,l.Lk)("div",R,(0,r.v_)(K.userStats.registerDays),1),a[12]||(a[12]=(0,l.Lk)("div",{class:"stat-label"},"注册天数",-1))])])])),_:1}),0===K.detail.status?((0,l.uX)(),(0,l.Wv)($,{key:0,class:"box-card action-card",shadow:"hover"},{header:(0,l.k6)((()=>a[13]||(a[13]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"审核操作")],-1)]))),default:(0,l.k6)((()=>[(0,l.Lk)("div",E,[(0,l.bF)(O,{"label-position":"top"},{default:(0,l.k6)((()=>[(0,l.bF)(J,{label:"审核备注"},{default:(0,l.k6)((()=>[(0,l.bF)(G,{modelValue:K.auditForm.remark,"onUpdate:modelValue":a[0]||(a[0]=e=>K.auditForm.remark=e),type:"textarea",rows:4,placeholder:"请输入审核备注"},null,8,["modelValue"])])),_:1}),(0,l.Lk)("div",S,[(0,l.bF)(N,{type:"success",loading:K.submitting,onClick:K.handleApprove},{default:(0,l.k6)((()=>a[14]||(a[14]=[(0,l.eW)("通过认证")]))),_:1},8,["loading","onClick"]),(0,l.bF)(N,{type:"danger",loading:K.submitting,onClick:K.handleReject},{default:(0,l.k6)((()=>a[15]||(a[15]=[(0,l.eW)("驳回认证")]))),_:1},8,["loading","onClick"])])])),_:1})])])),_:1})):(0,l.Q3)("",!0),K.detail.ocr_info?((0,l.uX)(),(0,l.Wv)($,{key:1,class:"box-card",shadow:"hover"},{header:(0,l.k6)((()=>a[16]||(a[16]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"OCR识别信息")],-1)]))),default:(0,l.k6)((()=>[(0,l.bF)(j,{column:1,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(Y,{label:"姓名"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.ocr_info.name),1)])),_:1}),(0,l.bF)(Y,{label:"身份证号"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.ocr_info.id_number),1)])),_:1}),(0,l.bF)(Y,{label:"性别"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.ocr_info.gender),1)])),_:1}),(0,l.bF)(Y,{label:"民族"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.ocr_info.nationality),1)])),_:1}),(0,l.bF)(Y,{label:"出生日期"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.ocr_info.birth_date),1)])),_:1}),(0,l.bF)(Y,{label:"地址"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.ocr_info.address),1)])),_:1}),(0,l.bF)(Y,{label:"签发机关"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.ocr_info.issuing_authority),1)])),_:1}),(0,l.bF)(Y,{label:"有效期"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(K.detail.ocr_info.valid_period),1)])),_:1})])),_:1})])),_:1})):(0,l.Q3)("",!0)])),_:1})])),_:1})])),[[Z,K.loading]])])}var M=t(144),X=t(1387),I=t(1219),V=t(1981);const Y={name:"IdentityDetail",setup(){const e=(0,X.lq)(),a=(0,X.rd)(),t=(0,M.KR)(e.params.id),r=(0,M.KR)(!0),s=(0,M.KR)(!1),i=(0,M.KR)({}),d=(0,M.KR)([]),n=(0,M.Kh)({orderCount:0,commentCount:0,registerDays:0}),c=(0,M.Kh)({remark:""}),o={id:e.params.id,user_id:10086,nickname:"张三",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",real_name:"张三",id_card:"******************",phone:"13800001234",created_at:"2023-05-20 10:00:00",verified_at:null,verify_type:2,status:0,remark:"",id_card_front:"https://img2.baidu.com/it/u=373017349,2292638225&fm=253&fmt=auto&app=138&f=JPEG",id_card_back:"https://img0.baidu.com/it/u=1036280137,2256858090&fm=253&fmt=auto&app=138&f=JPEG",face_image:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",ocr_info:{name:"张三",id_number:"******************",gender:"男",nationality:"汉",birth_date:"1990-01-01",address:"北京市朝阳区某某街道某某小区",issuing_authority:"北京市公安局朝阳分局",valid_period:"2020.01.01-2030.01.01"}},u=[{id:1,action:"create",content:"用户提交实名认证申请",operator:"系统",created_at:"2023-05-20 10:00:00"},{id:2,action:"check",content:"系统自动校验身份信息",operator:"系统",created_at:"2023-05-20 10:01:05"}],k={orderCount:15,commentCount:8,registerDays:60},_=()=>{r.value=!0,setTimeout((()=>{i.value=o,d.value=u,n.orderCount=k.orderCount,n.commentCount=k.commentCount,n.registerDays=k.registerDays,r.value=!1}),500)},v=e=>{const a={0:"warning",1:"success",2:"danger"};return a[e]},p=e=>{const a={0:"待审核",1:"已通过",2:"已驳回"};return a[e]},b=e=>{const a={1:"身份证认证",2:"人脸认证"};return a[e]||"未知"},g=e=>{const a={create:"primary",check:"info",approve:"success",reject:"danger"};return a[e]||"info"},m=()=>{a.push("/identity/list")},f=()=>{F(1)},h=()=>{c.remark?F(2):I.nk.warning("驳回时请填写备注说明原因")},F=e=>{s.value=!0,setTimeout((()=>{i.value.status=e,i.value.verified_at=(0,V.r6)(new Date),i.value.remark=c.remark,d.value.push({id:d.value.length+1,action:1===e?"approve":"reject",content:1===e?"审核通过实名认证":"驳回实名认证："+c.remark,operator:"管理员",created_at:(0,V.r6)(new Date)}),s.value=!1,(0,I.nk)({type:"success",message:1===e?"认证已通过":"认证已驳回"})}),500)};return(0,l.sV)((()=>{_()})),{id:t,loading:r,detail:i,logs:d,userStats:n,auditForm:c,submitting:s,formatDateTime:V.r6,getStatusType:v,getStatusText:p,getVerifyTypeText:b,getLogType:g,goBack:m,handleApprove:f,handleReject:h}}};var j=t(1241);const $=(0,j.A)(Y,[["render",K],["__scopeId","data-v-b0954a90"]]),B=$}}]);