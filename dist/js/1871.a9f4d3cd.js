"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[1871],{1871:(e,a,s)=>{s.r(a),s.d(a,{default:()=>u});var t=s(6768);const n={class:"page-container"};function c(e,a,s,c,r,d){return(0,t.uX)(),(0,t.CE)("div",n,a[0]||(a[0]=[(0,t.Lk)("h2",null,"统计概览",-1),(0,t.Lk)("div",{class:"placeholder-content"}," 统计概览功能正在开发中... ",-1)]))}const r={name:"StatisticsOverview",data(){return{}}};var d=s(1241);const i=(0,d.A)(r,[["render",c],["__scopeId","data-v-68adb382"]]),u=i}}]);