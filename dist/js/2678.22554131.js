"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[2678],{2678:(e,a,t)=>{t.r(a),t.d(a,{default:()=>z});var l=t(6768),r=t(4232);const s={class:"order-list-container"},i={class:"stats-cards"},o={class:"stat-card"},n={class:"stat-content"},d={class:"stat-value"},c={class:"stat-card"},u={class:"stat-content"},p={class:"stat-value"},h={class:"stat-card"},m={class:"stat-content"},b={class:"stat-value"},k={class:"stat-card"},g={class:"stat-content"},v={class:"stat-value"},_={class:"filter-container"},F={class:"table-container"},f={class:"user-info"},L={class:"phone"},y={class:"nickname"},C={class:"amount"},w={class:"pagination-container"},O={key:0,class:"order-detail"},W={class:"dialog-footer"};function S(e,a,t,S,T,V){const x=(0,l.g2)("el-input"),z=(0,l.g2)("el-form-item"),N=(0,l.g2)("el-option"),P=(0,l.g2)("el-select"),D=(0,l.g2)("el-button"),U=(0,l.g2)("el-form"),M=(0,l.g2)("el-table-column"),X=(0,l.g2)("el-tag"),E=(0,l.g2)("el-table"),I=(0,l.g2)("el-pagination"),Q=(0,l.g2)("el-descriptions-item"),R=(0,l.g2)("el-descriptions"),$=(0,l.g2)("el-dialog"),j=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",s,[a[21]||(a[21]=(0,l.Lk)("div",{class:"page-header"},[(0,l.Lk)("h2",null,[(0,l.Lk)("i",{class:"el-icon-s-order"}),(0,l.eW)(" 订单管理")]),(0,l.Lk)("p",null,"管理系统中的所有订单信息")],-1)),(0,l.Lk)("div",i,[(0,l.Lk)("div",o,[a[9]||(a[9]=(0,l.Lk)("div",{class:"stat-icon total"},[(0,l.Lk)("i",{class:"el-icon-s-order"})],-1)),(0,l.Lk)("div",n,[(0,l.Lk)("div",d,(0,r.v_)(T.stats.total),1),a[8]||(a[8]=(0,l.Lk)("div",{class:"stat-label"},"总订单数",-1))])]),(0,l.Lk)("div",c,[a[11]||(a[11]=(0,l.Lk)("div",{class:"stat-icon pending"},[(0,l.Lk)("i",{class:"el-icon-time"})],-1)),(0,l.Lk)("div",u,[(0,l.Lk)("div",p,(0,r.v_)(T.stats.pending),1),a[10]||(a[10]=(0,l.Lk)("div",{class:"stat-label"},"待处理",-1))])]),(0,l.Lk)("div",h,[a[13]||(a[13]=(0,l.Lk)("div",{class:"stat-icon completed"},[(0,l.Lk)("i",{class:"el-icon-check"})],-1)),(0,l.Lk)("div",m,[(0,l.Lk)("div",b,(0,r.v_)(T.stats.completed),1),a[12]||(a[12]=(0,l.Lk)("div",{class:"stat-label"},"已完成",-1))])]),(0,l.Lk)("div",k,[a[15]||(a[15]=(0,l.Lk)("div",{class:"stat-icon revenue"},[(0,l.Lk)("i",{class:"el-icon-money"})],-1)),(0,l.Lk)("div",g,[(0,l.Lk)("div",v,"¥"+(0,r.v_)(V.formatMoney(T.stats.revenue)),1),a[14]||(a[14]=(0,l.Lk)("div",{class:"stat-label"},"总收入",-1))])])]),(0,l.Lk)("div",_,[(0,l.bF)(U,{inline:!0,model:T.searchForm,class:"search-form"},{default:(0,l.k6)((()=>[(0,l.bF)(z,{label:"订单编号"},{default:(0,l.k6)((()=>[(0,l.bF)(x,{modelValue:T.searchForm.orderNo,"onUpdate:modelValue":a[0]||(a[0]=e=>T.searchForm.orderNo=e),placeholder:"请输入订单编号",clearable:""},null,8,["modelValue"])])),_:1}),(0,l.bF)(z,{label:"用户手机号"},{default:(0,l.k6)((()=>[(0,l.bF)(x,{modelValue:T.searchForm.userPhone,"onUpdate:modelValue":a[1]||(a[1]=e=>T.searchForm.userPhone=e),placeholder:"请输入用户手机号",clearable:""},null,8,["modelValue"])])),_:1}),(0,l.bF)(z,{label:"订单状态"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{modelValue:T.searchForm.status,"onUpdate:modelValue":a[2]||(a[2]=e=>T.searchForm.status=e),placeholder:"请选择",clearable:""},{default:(0,l.k6)((()=>[(0,l.bF)(N,{label:"待支付",value:"pending"}),(0,l.bF)(N,{label:"已支付",value:"paid"}),(0,l.bF)(N,{label:"处理中",value:"processing"}),(0,l.bF)(N,{label:"已完成",value:"completed"}),(0,l.bF)(N,{label:"已取消",value:"cancelled"}),(0,l.bF)(N,{label:"已退款",value:"refunded"})])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(z,{label:"服务类型"},{default:(0,l.k6)((()=>[(0,l.bF)(P,{modelValue:T.searchForm.serviceType,"onUpdate:modelValue":a[3]||(a[3]=e=>T.searchForm.serviceType=e),placeholder:"请选择",clearable:""},{default:(0,l.k6)((()=>[(0,l.bF)(N,{label:"快递代收",value:"express_pickup"}),(0,l.bF)(N,{label:"快递代发",value:"express_send"}),(0,l.bF)(N,{label:"打印复印",value:"print_copy"}),(0,l.bF)(N,{label:"其他服务",value:"other"})])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(z,null,{default:(0,l.k6)((()=>[(0,l.bF)(D,{type:"primary",onClick:V.handleSearch},{default:(0,l.k6)((()=>a[16]||(a[16]=[(0,l.Lk)("i",{class:"el-icon-search"},null,-1),(0,l.eW)(" 搜索 ")]))),_:1},8,["onClick"]),(0,l.bF)(D,{onClick:V.handleReset},{default:(0,l.k6)((()=>a[17]||(a[17]=[(0,l.Lk)("i",{class:"el-icon-refresh"},null,-1),(0,l.eW)(" 重置 ")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])]),(0,l.Lk)("div",F,[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(E,{data:T.orderList,style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(M,{prop:"order_no",label:"订单编号",width:"160"}),(0,l.bF)(M,{label:"用户信息","min-width":"150"},{default:(0,l.k6)((e=>[(0,l.Lk)("div",f,[(0,l.Lk)("div",L,(0,r.v_)(e.row.user_phone||"-"),1),(0,l.Lk)("div",y,(0,r.v_)(e.row.user_nickname||"-"),1)])])),_:1}),(0,l.bF)(M,{prop:"service_type",label:"服务类型",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(X,{type:V.getServiceTypeColor(e.row.service_type)},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(V.getServiceTypeText(e.row.service_type)),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(M,{prop:"amount",label:"订单金额",width:"100"},{default:(0,l.k6)((e=>[(0,l.Lk)("span",C,"¥"+(0,r.v_)(V.formatMoney(e.row.amount)),1)])),_:1}),(0,l.bF)(M,{prop:"status",label:"订单状态",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(X,{type:V.getStatusColor(e.row.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(V.getStatusText(e.row.status)),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(M,{prop:"station_name",label:"服务驿站",width:"120"}),(0,l.bF)(M,{prop:"created_at",label:"创建时间",width:"160"},{default:(0,l.k6)((e=>[(0,l.eW)((0,r.v_)(V.formatTime(e.row.created_at)),1)])),_:1}),(0,l.bF)(M,{label:"操作",width:"200",fixed:"right"},{default:(0,l.k6)((e=>[(0,l.bF)(D,{type:"text",size:"small",onClick:a=>V.handleView(e.row)},{default:(0,l.k6)((()=>a[18]||(a[18]=[(0,l.Lk)("i",{class:"el-icon-view"},null,-1),(0,l.eW)(" 查看 ")]))),_:2},1032,["onClick"]),"paid"===e.row.status?((0,l.uX)(),(0,l.Wv)(D,{key:0,type:"text",size:"small",onClick:a=>V.handleProcess(e.row)},{default:(0,l.k6)((()=>a[19]||(a[19]=[(0,l.Lk)("i",{class:"el-icon-s-tools"},null,-1),(0,l.eW)(" 处理 ")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0)])),_:1})])),_:1},8,["data"])),[[j,T.loading]]),(0,l.Lk)("div",w,[(0,l.bF)(I,{"current-page":T.pagination.page,"onUpdate:currentPage":a[4]||(a[4]=e=>T.pagination.page=e),"page-size":T.pagination.limit,"onUpdate:pageSize":a[5]||(a[5]=e=>T.pagination.limit=e),"page-sizes":[10,20,50,100],total:T.pagination.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:V.handleSizeChange,onCurrentChange:V.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),(0,l.bF)($,{modelValue:T.detailDialogVisible,"onUpdate:modelValue":a[7]||(a[7]=e=>T.detailDialogVisible=e),title:"订单详情",width:"800px"},{footer:(0,l.k6)((()=>[(0,l.Lk)("span",W,[(0,l.bF)(D,{onClick:a[6]||(a[6]=e=>T.detailDialogVisible=!1)},{default:(0,l.k6)((()=>a[20]||(a[20]=[(0,l.eW)("关闭")]))),_:1})])])),default:(0,l.k6)((()=>[T.currentOrder?((0,l.uX)(),(0,l.CE)("div",O,[(0,l.bF)(R,{column:2,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(Q,{label:"订单编号"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(T.currentOrder.order_no),1)])),_:1}),(0,l.bF)(Q,{label:"订单状态"},{default:(0,l.k6)((()=>[(0,l.bF)(X,{type:V.getStatusColor(T.currentOrder.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(V.getStatusText(T.currentOrder.status)),1)])),_:1},8,["type"])])),_:1}),(0,l.bF)(Q,{label:"用户手机号"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(T.currentOrder.user_phone||"-"),1)])),_:1}),(0,l.bF)(Q,{label:"用户昵称"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(T.currentOrder.user_nickname||"-"),1)])),_:1}),(0,l.bF)(Q,{label:"服务类型"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(V.getServiceTypeText(T.currentOrder.service_type)),1)])),_:1}),(0,l.bF)(Q,{label:"订单金额"},{default:(0,l.k6)((()=>[(0,l.eW)("¥"+(0,r.v_)(V.formatMoney(T.currentOrder.amount)),1)])),_:1}),(0,l.bF)(Q,{label:"服务驿站"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(T.currentOrder.station_name||"-"),1)])),_:1}),(0,l.bF)(Q,{label:"支付方式"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(T.currentOrder.payment_method||"-"),1)])),_:1}),(0,l.bF)(Q,{label:"创建时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(V.formatTime(T.currentOrder.created_at)),1)])),_:1}),(0,l.bF)(Q,{label:"支付时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(V.formatTime(T.currentOrder.payment_time)),1)])),_:1}),(0,l.bF)(Q,{label:"服务详情",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(T.currentOrder.service_details||"-"),1)])),_:1}),(0,l.bF)(Q,{label:"备注信息",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,r.v_)(T.currentOrder.remark||"-"),1)])),_:1})])),_:1})])):(0,l.Q3)("",!0)])),_:1},8,["modelValue"])])}const T={name:"OrderList",data(){return{loading:!1,orderList:[],searchForm:{orderNo:"",userPhone:"",status:"",serviceType:""},pagination:{page:1,limit:20,total:0},stats:{total:0,pending:0,completed:0,revenue:0},detailDialogVisible:!1,currentOrder:null}},mounted(){this.loadStats(),this.loadOrderList()},methods:{async loadStats(){this.stats={total:0,pending:0,completed:0,revenue:0}},async loadOrderList(){this.loading=!0;try{this.orderList=[],this.pagination.total=0}catch(e){console.error("加载订单列表失败:",e),this.$message.error("加载订单列表失败")}finally{this.loading=!1}},getSearchParams(){const e={page:this.pagination.page,limit:this.pagination.limit};return this.searchForm.orderNo&&(e.orderNo=this.searchForm.orderNo),this.searchForm.userPhone&&(e.userPhone=this.searchForm.userPhone),this.searchForm.status&&(e.status=this.searchForm.status),this.searchForm.serviceType&&(e.serviceType=this.searchForm.serviceType),e},handleSearch(){this.pagination.page=1,this.loadOrderList()},handleReset(){this.searchForm={orderNo:"",userPhone:"",status:"",serviceType:""},this.pagination.page=1,this.loadOrderList()},handleView(e){this.currentOrder=e,this.detailDialogVisible=!0},handleProcess(e){this.$message.info("订单处理功能开发中")},handleSizeChange(e){this.pagination.limit=e,this.loadOrderList()},handleCurrentChange(e){this.pagination.page=e,this.loadOrderList()},getServiceTypeColor(e){const a={express_pickup:"primary",express_send:"success",print_copy:"warning",other:"info"};return a[e]||"info"},getServiceTypeText(e){const a={express_pickup:"快递代收",express_send:"快递代发",print_copy:"打印复印",other:"其他服务"};return a[e]||"未知"},getStatusColor(e){const a={pending:"warning",paid:"primary",processing:"info",completed:"success",cancelled:"danger",refunded:"danger"};return a[e]||"info"},getStatusText(e){const a={pending:"待支付",paid:"已支付",processing:"处理中",completed:"已完成",cancelled:"已取消",refunded:"已退款"};return a[e]||"未知"},formatMoney(e){return e?new Intl.NumberFormat("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}).format(e):"0.00"},formatTime(e){return e?new Date(e).toLocaleString("zh-CN"):"-"}}};var V=t(1241);const x=(0,V.A)(T,[["render",S],["__scopeId","data-v-41aaf2e5"]]),z=x}}]);