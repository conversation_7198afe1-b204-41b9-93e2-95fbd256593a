"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[8709],{8709:(e,a,r)=>{r.r(a),r.d(a,{default:()=>x});var t=r(6768),l=r(4232);const i={class:"page-container"},d={class:"filter-container"},o={class:"tree-container"},n={class:"card-header"},s={class:"header-right"},c={class:"search-box"},m={class:"price-range"},u={class:"price-min"},p={class:"price-max"},h={class:"market-price"},g={class:"pagination-container"},b={class:"chart-container"},k={class:"remark-container"},y={class:"remark-content"},f={class:"dialog-footer"};function F(e,a,r,F,P,_){const C=(0,t.g2)("el-input"),V=(0,t.g2)("el-tree"),x=(0,t.g2)("el-card"),v=(0,t.g2)("el-col"),w=(0,t.g2)("el-button"),L=(0,t.g2)("el-form-item"),q=(0,t.g2)("el-input-number"),T=(0,t.g2)("el-form"),W=(0,t.g2)("el-table-column"),I=(0,t.g2)("el-tag"),U=(0,t.g2)("el-table"),z=(0,t.g2)("el-pagination"),D=(0,t.g2)("el-row"),N=(0,t.g2)("el-descriptions-item"),S=(0,t.g2)("el-descriptions"),$=(0,t.g2)("el-dialog"),E=(0,t.g2)("el-cascader"),O=(0,t.g2)("el-radio"),Q=(0,t.g2)("el-radio-group"),A=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",i,[(0,t.bF)(D,{gutter:20},{default:(0,t.k6)((()=>[(0,t.bF)(v,{span:6},{default:(0,t.k6)((()=>[(0,t.bF)(x,{class:"category-card"},{header:(0,t.k6)((()=>a[19]||(a[19]=[(0,t.Lk)("div",{class:"card-header"},[(0,t.Lk)("span",null,"设备分类")],-1)]))),default:(0,t.k6)((()=>[(0,t.Lk)("div",d,[(0,t.bF)(C,{modelValue:P.filterText,"onUpdate:modelValue":a[0]||(a[0]=e=>P.filterText=e),placeholder:"输入关键字过滤",clearable:"","prefix-icon":"Search"},null,8,["modelValue"])]),(0,t.Lk)("div",o,[(0,t.bF)(V,{ref:"categoryTree",data:P.categoryTree,"node-key":"id","highlight-current":"",props:P.defaultProps,"filter-node-method":_.filterNode,"default-expand-all":"",onNodeClick:_.handleNodeClick},null,8,["data","props","filter-node-method","onNodeClick"])])])),_:1})])),_:1}),(0,t.bF)(v,{span:18},{default:(0,t.k6)((()=>[(0,t.bF)(x,null,{header:(0,t.k6)((()=>[(0,t.Lk)("div",n,[(0,t.Lk)("span",null,(0,l.v_)(P.currentCategory?P.currentCategory.name+" - 价格参考":"价格参考"),1),(0,t.Lk)("div",s,[(0,t.bF)(w,{type:"primary",onClick:_.handleAdd},{default:(0,t.k6)((()=>a[20]||(a[20]=[(0,t.eW)("新增价格参考")]))),_:1},8,["onClick"]),(0,t.bF)(w,{type:"success",onClick:_.handleExport},{default:(0,t.k6)((()=>a[21]||(a[21]=[(0,t.eW)("导出数据")]))),_:1},8,["onClick"])])])])),default:(0,t.k6)((()=>[(0,t.Lk)("div",c,[(0,t.bF)(T,{model:P.queryParams,ref:"queryForm",inline:!0},{default:(0,t.k6)((()=>[(0,t.bF)(L,{label:"设备型号",prop:"model"},{default:(0,t.k6)((()=>[(0,t.bF)(C,{modelValue:P.queryParams.model,"onUpdate:modelValue":a[1]||(a[1]=e=>P.queryParams.model=e),placeholder:"请输入设备型号",clearable:""},null,8,["modelValue"])])),_:1}),(0,t.bF)(L,{label:"品牌",prop:"brand"},{default:(0,t.k6)((()=>[(0,t.bF)(C,{modelValue:P.queryParams.brand,"onUpdate:modelValue":a[2]||(a[2]=e=>P.queryParams.brand=e),placeholder:"请输入品牌",clearable:""},null,8,["modelValue"])])),_:1}),(0,t.bF)(L,{label:"价格区间"},{default:(0,t.k6)((()=>[(0,t.bF)(q,{modelValue:P.queryParams.minPrice,"onUpdate:modelValue":a[3]||(a[3]=e=>P.queryParams.minPrice=e),min:0,"controls-position":"right",placeholder:"最低价",class:"price-input"},null,8,["modelValue"]),a[22]||(a[22]=(0,t.Lk)("span",{class:"el-range-separator"},"-",-1)),(0,t.bF)(q,{modelValue:P.queryParams.maxPrice,"onUpdate:modelValue":a[4]||(a[4]=e=>P.queryParams.maxPrice=e),min:0,"controls-position":"right",placeholder:"最高价",class:"price-input"},null,8,["modelValue"])])),_:1}),(0,t.bF)(L,null,{default:(0,t.k6)((()=>[(0,t.bF)(w,{type:"primary",icon:"Search",onClick:_.handleQuery},{default:(0,t.k6)((()=>a[23]||(a[23]=[(0,t.eW)("搜索")]))),_:1},8,["onClick"]),(0,t.bF)(w,{icon:"Refresh",onClick:_.resetQuery},{default:(0,t.k6)((()=>a[24]||(a[24]=[(0,t.eW)("重置")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])]),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(U,{data:P.priceList},{default:(0,t.k6)((()=>[(0,t.bF)(W,{prop:"name",label:"设备名称","min-width":"150"}),(0,t.bF)(W,{prop:"model",label:"规格型号","min-width":"150"}),(0,t.bF)(W,{prop:"brand",label:"品牌",width:"120"}),(0,t.bF)(W,{label:"参考价格(元)",align:"center",width:"220"},{default:(0,t.k6)((e=>[(0,t.Lk)("div",m,[(0,t.Lk)("span",u,(0,l.v_)(e.row.minPrice),1),a[25]||(a[25]=(0,t.Lk)("span",{class:"price-separator"},"~",-1)),(0,t.Lk)("span",p,(0,l.v_)(e.row.maxPrice),1)])])),_:1}),(0,t.bF)(W,{prop:"marketPrice",label:"市场均价(元)",align:"center",width:"120"},{default:(0,t.k6)((e=>[(0,t.Lk)("span",h,(0,l.v_)(e.row.marketPrice),1)])),_:1}),(0,t.bF)(W,{label:"价格趋势",width:"100",align:"center"},{default:(0,t.k6)((e=>[(0,t.bF)(I,{type:"up"===e.row.trend?"danger":"down"===e.row.trend?"success":"info"},{default:(0,t.k6)((()=>[(0,t.eW)((0,l.v_)("up"===e.row.trend?"上涨":"down"===e.row.trend?"下降":"平稳"),1)])),_:2},1032,["type"])])),_:1}),(0,t.bF)(W,{prop:"updateTime",label:"更新时间",width:"160",sortable:""}),(0,t.bF)(W,{label:"操作",width:"180",align:"center",fixed:"right"},{default:(0,t.k6)((e=>[(0,t.bF)(w,{type:"text",onClick:a=>_.handleEdit(e.row)},{default:(0,t.k6)((()=>a[26]||(a[26]=[(0,t.eW)("编辑")]))),_:2},1032,["onClick"]),(0,t.bF)(w,{type:"text",onClick:a=>_.handleView(e.row)},{default:(0,t.k6)((()=>a[27]||(a[27]=[(0,t.eW)("查看详情")]))),_:2},1032,["onClick"]),(0,t.bF)(w,{type:"text",onClick:a=>_.handleDelete(e.row)},{default:(0,t.k6)((()=>a[28]||(a[28]=[(0,t.eW)("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[A,P.loading]]),(0,t.Lk)("div",g,[(0,t.bF)(z,{"current-page":P.queryParams.pageNum,"onUpdate:currentPage":a[5]||(a[5]=e=>P.queryParams.pageNum=e),"page-size":P.queryParams.pageSize,"onUpdate:pageSize":a[6]||(a[6]=e=>P.queryParams.pageSize=e),"page-sizes":[10,20,50,100],total:P.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:_.handleSizeChange,onCurrentChange:_.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])])),_:1})])),_:1})])),_:1}),(0,t.bF)($,{title:"价格参考详情",modelValue:P.detailVisible,"onUpdate:modelValue":a[7]||(a[7]=e=>P.detailVisible=e),width:"800px","append-to-body":""},{default:(0,t.k6)((()=>[(0,t.bF)(S,{column:2,border:""},{default:(0,t.k6)((()=>[(0,t.bF)(N,{label:"设备名称"},{default:(0,t.k6)((()=>[(0,t.eW)((0,l.v_)(P.priceDetail.name),1)])),_:1}),(0,t.bF)(N,{label:"设备分类"},{default:(0,t.k6)((()=>[(0,t.eW)((0,l.v_)(P.priceDetail.categoryName),1)])),_:1}),(0,t.bF)(N,{label:"规格型号"},{default:(0,t.k6)((()=>[(0,t.eW)((0,l.v_)(P.priceDetail.model),1)])),_:1}),(0,t.bF)(N,{label:"品牌"},{default:(0,t.k6)((()=>[(0,t.eW)((0,l.v_)(P.priceDetail.brand),1)])),_:1}),(0,t.bF)(N,{label:"参考价格"},{default:(0,t.k6)((()=>[(0,t.eW)((0,l.v_)(P.priceDetail.minPrice)+" ~ "+(0,l.v_)(P.priceDetail.maxPrice)+" 元",1)])),_:1}),(0,t.bF)(N,{label:"市场均价"},{default:(0,t.k6)((()=>[(0,t.eW)((0,l.v_)(P.priceDetail.marketPrice)+" 元",1)])),_:1}),(0,t.bF)(N,{label:"价格趋势"},{default:(0,t.k6)((()=>[(0,t.bF)(I,{type:"up"===P.priceDetail.trend?"danger":"down"===P.priceDetail.trend?"success":"info"},{default:(0,t.k6)((()=>[(0,t.eW)((0,l.v_)("up"===P.priceDetail.trend?"上涨":"down"===P.priceDetail.trend?"下降":"平稳"),1)])),_:1},8,["type"])])),_:1}),(0,t.bF)(N,{label:"更新时间"},{default:(0,t.k6)((()=>[(0,t.eW)((0,l.v_)(P.priceDetail.updateTime),1)])),_:1})])),_:1}),(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",b,a[29]||(a[29]=[(0,t.Lk)("div",{class:"chart-title"},"价格走势图",-1),(0,t.Lk)("div",{id:"priceChart",style:{width:"100%",height:"300px"}},null,-1)]))),[[A,P.chartLoading]]),(0,t.Lk)("div",k,[a[30]||(a[30]=(0,t.Lk)("h4",null,"价格说明",-1)),(0,t.Lk)("div",y,(0,l.v_)(P.priceDetail.remark),1)])])),_:1},8,["modelValue"]),(0,t.bF)($,{title:P.dialogTitle,modelValue:P.dialogVisible,"onUpdate:modelValue":a[18]||(a[18]=e=>P.dialogVisible=e),width:"650px","append-to-body":""},{footer:(0,t.k6)((()=>[(0,t.Lk)("div",f,[(0,t.bF)(w,{onClick:a[17]||(a[17]=e=>P.dialogVisible=!1)},{default:(0,t.k6)((()=>a[34]||(a[34]=[(0,t.eW)("取 消")]))),_:1}),(0,t.bF)(w,{type:"primary",onClick:_.submitForm},{default:(0,t.k6)((()=>a[35]||(a[35]=[(0,t.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,t.k6)((()=>[(0,t.bF)(T,{ref:"priceForm",model:P.form,rules:P.rules,"label-width":"100px"},{default:(0,t.k6)((()=>[(0,t.bF)(L,{label:"设备分类",prop:"categoryId"},{default:(0,t.k6)((()=>[(0,t.bF)(E,{modelValue:P.form.categoryId,"onUpdate:modelValue":a[8]||(a[8]=e=>P.form.categoryId=e),options:P.categoryOptions,props:{checkStrictly:!0,value:"id",label:"name"},placeholder:"请选择设备分类",clearable:"",style:{width:"100%"}},null,8,["modelValue","options"])])),_:1}),(0,t.bF)(L,{label:"设备名称",prop:"name"},{default:(0,t.k6)((()=>[(0,t.bF)(C,{modelValue:P.form.name,"onUpdate:modelValue":a[9]||(a[9]=e=>P.form.name=e),placeholder:"请输入设备名称"},null,8,["modelValue"])])),_:1}),(0,t.bF)(L,{label:"规格型号",prop:"model"},{default:(0,t.k6)((()=>[(0,t.bF)(C,{modelValue:P.form.model,"onUpdate:modelValue":a[10]||(a[10]=e=>P.form.model=e),placeholder:"请输入规格型号"},null,8,["modelValue"])])),_:1}),(0,t.bF)(L,{label:"品牌",prop:"brand"},{default:(0,t.k6)((()=>[(0,t.bF)(C,{modelValue:P.form.brand,"onUpdate:modelValue":a[11]||(a[11]=e=>P.form.brand=e),placeholder:"请输入品牌"},null,8,["modelValue"])])),_:1}),(0,t.bF)(L,{label:"最低价格",prop:"minPrice"},{default:(0,t.k6)((()=>[(0,t.bF)(q,{modelValue:P.form.minPrice,"onUpdate:modelValue":a[12]||(a[12]=e=>P.form.minPrice=e),min:0,precision:2,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),(0,t.bF)(L,{label:"最高价格",prop:"maxPrice"},{default:(0,t.k6)((()=>[(0,t.bF)(q,{modelValue:P.form.maxPrice,"onUpdate:modelValue":a[13]||(a[13]=e=>P.form.maxPrice=e),min:0,precision:2,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),(0,t.bF)(L,{label:"市场均价",prop:"marketPrice"},{default:(0,t.k6)((()=>[(0,t.bF)(q,{modelValue:P.form.marketPrice,"onUpdate:modelValue":a[14]||(a[14]=e=>P.form.marketPrice=e),min:0,precision:2,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),(0,t.bF)(L,{label:"价格趋势",prop:"trend"},{default:(0,t.k6)((()=>[(0,t.bF)(Q,{modelValue:P.form.trend,"onUpdate:modelValue":a[15]||(a[15]=e=>P.form.trend=e)},{default:(0,t.k6)((()=>[(0,t.bF)(O,{label:"up"},{default:(0,t.k6)((()=>a[31]||(a[31]=[(0,t.eW)("上涨")]))),_:1}),(0,t.bF)(O,{label:"down"},{default:(0,t.k6)((()=>a[32]||(a[32]=[(0,t.eW)("下降")]))),_:1}),(0,t.bF)(O,{label:"stable"},{default:(0,t.k6)((()=>a[33]||(a[33]=[(0,t.eW)("平稳")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(L,{label:"价格说明",prop:"remark"},{default:(0,t.k6)((()=>[(0,t.bF)(C,{modelValue:P.form.remark,"onUpdate:modelValue":a[16]||(a[16]=e=>P.form.remark=e),type:"textarea",rows:"4",placeholder:"请输入价格说明"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"])])}var P=r(2032);const _={name:"EquipmentPrice",data(){return{loading:!1,chartLoading:!1,filterText:"",defaultProps:{children:"children",label:"name"},currentCategory:null,categoryTree:[{id:1,name:"快递设备",children:[{id:4,name:"快递柜",children:[]},{id:5,name:"分拣设备",children:[]}]},{id:2,name:"物流设备",children:[{id:6,name:"运输车辆",children:[]},{id:7,name:"装卸设备",children:[]}]},{id:3,name:"办公设备",children:[{id:8,name:"电脑设备",children:[]},{id:9,name:"打印设备",children:[]}]}],categoryOptions:[],total:0,priceList:[{id:1,categoryId:4,categoryName:"快递柜",name:"智能快递柜",model:"FC-200",brand:"丰巢",minPrice:3e3,maxPrice:5e3,marketPrice:3800,trend:"down",updateTime:"2023-04-10 10:00:00",remark:"该型号属于中型快递柜，支持扫码取件，一般配置24个格口。近期因为新款上市，价格有所下降，二手市场价格约为新品的60%。主要适用于社区、写字楼等场所。"},{id:2,categoryId:4,categoryName:"快递柜",name:"大型智能快递柜",model:"ZM-300",brand:"中邮",minPrice:5e3,maxPrice:8e3,marketPrice:6500,trend:"stable",updateTime:"2023-04-11 15:30:00",remark:"该型号属于大型快递柜，支持人脸识别和扫码取件，一般配置36个格口。价格相对稳定，二手市场价格约为新品的70%。主要适用于大型社区、商场等人流密集区域。"},{id:3,categoryId:5,categoryName:"分拣设备",name:"小型分拣机",model:"KY-100",brand:"快易",minPrice:1e4,maxPrice:15e3,marketPrice:12500,trend:"up",updateTime:"2023-04-12 09:15:00",remark:"该型号为小型分拣机，适用于日均处理包裹1000件以内的小型驿站。价格近期有所上涨，主要是因为物流行业需求增加，供不应求。"}],queryParams:{pageNum:1,pageSize:10,categoryId:null,model:"",brand:"",minPrice:null,maxPrice:null},priceDetail:{},priceChart:null,detailVisible:!1,dialogVisible:!1,dialogTitle:"",form:{id:void 0,categoryId:null,name:"",model:"",brand:"",minPrice:0,maxPrice:0,marketPrice:0,trend:"stable",remark:""},rules:{categoryId:[{required:!0,message:"请选择设备分类",trigger:"change"}],name:[{required:!0,message:"设备名称不能为空",trigger:"blur"}],model:[{required:!0,message:"规格型号不能为空",trigger:"blur"}],brand:[{required:!0,message:"品牌不能为空",trigger:"blur"}],minPrice:[{required:!0,message:"最低价格不能为空",trigger:"blur"}],maxPrice:[{required:!0,message:"最高价格不能为空",trigger:"blur"}],marketPrice:[{required:!0,message:"市场均价不能为空",trigger:"blur"}]}}},watch:{filterText(e){this.$refs.categoryTree.filter(e)}},created(){this.getCategoryOptions()},methods:{filterNode(e,a){return!e||-1!==a.name.indexOf(e)},getCategoryOptions(){this.categoryOptions=this.formatCategoryOptions(this.categoryTree)},formatCategoryOptions(e){const a=[];return e.forEach((e=>{const r={id:e.id,name:e.name,children:[]};e.children&&e.children.length>0&&(r.children=this.formatCategoryOptions(e.children)),a.push(r)})),a},handleNodeClick(e){this.currentCategory=e,this.queryParams.categoryId=e.id,this.handleQuery()},handleQuery(){this.queryParams.pageNum=1,this.getList()},resetQuery(){this.$refs.queryForm.resetFields(),this.currentCategory&&(this.queryParams.categoryId=this.currentCategory.id),this.handleQuery()},getList(){this.loading=!0,setTimeout((()=>{this.queryParams.categoryId&&(this.priceList=this.priceList.filter((e=>e.categoryId===this.queryParams.categoryId))),this.total=this.priceList.length,this.loading=!1}),500)},handleAdd(){this.reset(),this.dialogTitle="添加价格参考",this.currentCategory&&(this.form.categoryId=this.currentCategory.id),this.dialogVisible=!0},handleEdit(e){this.reset(),this.dialogTitle="编辑价格参考",this.form={id:e.id,categoryId:e.categoryId,name:e.name,model:e.model,brand:e.brand,minPrice:e.minPrice,maxPrice:e.maxPrice,marketPrice:e.marketPrice,trend:e.trend,remark:e.remark},this.dialogVisible=!0},handleView(e){this.priceDetail=e,this.detailVisible=!0,this.chartLoading=!0,this.$nextTick((()=>{setTimeout((()=>{this.initPriceChart(),this.chartLoading=!1}),500)}))},initPriceChart(){this.priceChart=P.Ts(document.getElementById("priceChart"));const e=["2023-01","2023-02","2023-03","2023-04","2023-05","2023-06"],a=[4200,4100,3950,3800,3700,3800];this.priceChart.setOption({tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:e},yAxis:{type:"value",axisLabel:{formatter:"{value} 元"}},series:[{name:"市场均价",type:"line",data:a,markPoint:{data:[{type:"max",name:"最高价"},{type:"min",name:"最低价"}]},markLine:{data:[{type:"average",name:"平均价"}]},lineStyle:{width:3,color:"#5470C6"},areaStyle:{opacity:.2,color:"#5470C6"}}]}),window.addEventListener("resize",(()=>{this.priceChart&&this.priceChart.resize()}))},handleDelete(e){this.$confirm(`确认要删除"${e.name}"的价格参考数据吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$message({type:"success",message:"删除成功!"}),this.getList()})).catch((()=>{}))},handleExport(){this.$confirm("确认导出所有价格参考数据吗?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$message({type:"success",message:"导出成功!"})})).catch((()=>{}))},reset(){this.form={id:void 0,categoryId:this.currentCategory?this.currentCategory.id:null,name:"",model:"",brand:"",minPrice:0,maxPrice:0,marketPrice:0,trend:"stable",remark:""}},submitForm(){this.$refs.priceForm.validate((e=>{e&&(void 0!==this.form.id?this.$message({type:"success",message:"修改成功!"}):this.$message({type:"success",message:"新增成功!"}),this.dialogVisible=!1,this.getList())}))},handleSizeChange(e){this.queryParams.pageSize=e,this.getList()},handleCurrentChange(e){this.queryParams.pageNum=e,this.getList()}}};var C=r(1241);const V=(0,C.A)(_,[["render",F],["__scopeId","data-v-7552a14e"]]),x=V}}]);