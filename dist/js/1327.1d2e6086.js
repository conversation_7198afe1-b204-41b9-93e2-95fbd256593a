"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[1327],{1327:(e,l,t)=>{t.r(l),t.d(l,{default:()=>f});var a=t(6768),i=t(4232);const n={class:"app-container"},u={class:"card-header"},o={key:0},s={key:1},d={key:0},r={key:1},b={key:0},c={key:1},m={class:"form-tip"},p={class:"dialog-footer"};function g(e,l,t,g,y,_){const v=(0,a.g2)("el-button"),k=(0,a.g2)("el-alert"),h=(0,a.g2)("el-table-column"),F=(0,a.g2)("el-switch"),f=(0,a.g2)("el-table"),w=(0,a.g2)("el-tab-pane"),V=(0,a.g2)("el-input-number"),C=(0,a.g2)("el-form-item"),R=(0,a.g2)("el-option"),x=(0,a.g2)("el-select"),S=(0,a.g2)("el-form"),T=(0,a.g2)("el-tabs"),W=(0,a.g2)("el-card"),U=(0,a.g2)("el-input"),E=(0,a.g2)("el-radio"),X=(0,a.g2)("el-radio-group"),L=(0,a.g2)("el-dialog"),z=(0,a.gN)("loading");return(0,a.uX)(),(0,a.CE)("div",n,[(0,a.bF)(W,{class:"commission-rule-card"},{header:(0,a.k6)((()=>[(0,a.Lk)("div",u,[l[18]||(l[18]=(0,a.Lk)("span",null,"佣金规则管理",-1)),(0,a.bF)(v,{type:"primary",onClick:g.handleAddRule},{default:(0,a.k6)((()=>l[17]||(l[17]=[(0,a.eW)("添加规则")]))),_:1},8,["onClick"])])])),default:(0,a.k6)((()=>[(0,a.bF)(T,{modelValue:g.activeTab,"onUpdate:modelValue":l[7]||(l[7]=e=>g.activeTab=e),type:"card"},{default:(0,a.k6)((()=>[(0,a.bF)(w,{label:"用户推广佣金",name:"user"},{default:(0,a.k6)((()=>[(0,a.bF)(k,{type:"info",title:"用户推广佣金说明",description:"用户推广佣金是指用户邀请其他用户注册并完成相应操作后获得的佣金奖励。",closable:!1,"show-icon":"",style:{"margin-bottom":"20px"}}),(0,a.bo)(((0,a.uX)(),(0,a.CE)("div",null,[(0,a.bF)(f,{data:g.rules.user,border:"",style:{width:"100%"}},{default:(0,a.k6)((()=>[(0,a.bF)(h,{label:"ID",prop:"id",width:"80",align:"center"}),(0,a.bF)(h,{label:"规则名称",prop:"name","min-width":"120"}),(0,a.bF)(h,{label:"佣金类型",align:"center",width:"120"},{default:(0,a.k6)((({row:e})=>[(0,a.eW)((0,i.v_)(g.getCommissionTypeText(e.type)),1)])),_:1}),(0,a.bF)(h,{label:"佣金值",align:"center",width:"120"},{default:(0,a.k6)((({row:e})=>[1===e.type?((0,a.uX)(),(0,a.CE)("span",o,(0,i.v_)(e.value)+"元",1)):2===e.type?((0,a.uX)(),(0,a.CE)("span",s,(0,i.v_)(e.value)+"%",1)):(0,a.Q3)("",!0)])),_:1}),(0,a.bF)(h,{label:"条件说明",prop:"condition","min-width":"200"}),(0,a.bF)(h,{label:"状态",align:"center",width:"100"},{default:(0,a.k6)((({row:e})=>[(0,a.bF)(F,{modelValue:e.status,"onUpdate:modelValue":l=>e.status=l,"active-value":1,"inactive-value":0,onChange:l=>g.handleStatusChange(e,"user")},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),(0,a.bF)(h,{label:"操作",align:"center",width:"150"},{default:(0,a.k6)((({row:e})=>[(0,a.bF)(v,{type:"primary",size:"small",onClick:l=>g.handleEditRule(e,"user")},{default:(0,a.k6)((()=>l[19]||(l[19]=[(0,a.eW)("编辑")]))),_:2},1032,["onClick"]),(0,a.bF)(v,{type:"danger",size:"small",onClick:l=>g.handleDeleteRule(e,"user")},{default:(0,a.k6)((()=>l[20]||(l[20]=[(0,a.eW)("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])),[[z,e.loading.user]])])),_:1}),(0,a.bF)(w,{label:"驿站推广佣金",name:"station"},{default:(0,a.k6)((()=>[(0,a.bF)(k,{type:"info",title:"驿站推广佣金说明",description:"驿站推广佣金是指为驿站引流、促成驿站认证或其他相关操作后获得的佣金奖励。",closable:!1,"show-icon":"",style:{"margin-bottom":"20px"}}),(0,a.bo)(((0,a.uX)(),(0,a.CE)("div",null,[(0,a.bF)(f,{data:g.rules.station,border:"",style:{width:"100%"}},{default:(0,a.k6)((()=>[(0,a.bF)(h,{label:"ID",prop:"id",width:"80",align:"center"}),(0,a.bF)(h,{label:"规则名称",prop:"name","min-width":"120"}),(0,a.bF)(h,{label:"佣金类型",align:"center",width:"120"},{default:(0,a.k6)((({row:e})=>[(0,a.eW)((0,i.v_)(g.getCommissionTypeText(e.type)),1)])),_:1}),(0,a.bF)(h,{label:"佣金值",align:"center",width:"120"},{default:(0,a.k6)((({row:e})=>[1===e.type?((0,a.uX)(),(0,a.CE)("span",d,(0,i.v_)(e.value)+"元",1)):2===e.type?((0,a.uX)(),(0,a.CE)("span",r,(0,i.v_)(e.value)+"%",1)):(0,a.Q3)("",!0)])),_:1}),(0,a.bF)(h,{label:"条件说明",prop:"condition","min-width":"200"}),(0,a.bF)(h,{label:"状态",align:"center",width:"100"},{default:(0,a.k6)((({row:e})=>[(0,a.bF)(F,{modelValue:e.status,"onUpdate:modelValue":l=>e.status=l,"active-value":1,"inactive-value":0,onChange:l=>g.handleStatusChange(e,"station")},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),(0,a.bF)(h,{label:"操作",align:"center",width:"150"},{default:(0,a.k6)((({row:e})=>[(0,a.bF)(v,{type:"primary",size:"small",onClick:l=>g.handleEditRule(e,"station")},{default:(0,a.k6)((()=>l[21]||(l[21]=[(0,a.eW)("编辑")]))),_:2},1032,["onClick"]),(0,a.bF)(v,{type:"danger",size:"small",onClick:l=>g.handleDeleteRule(e,"station")},{default:(0,a.k6)((()=>l[22]||(l[22]=[(0,a.eW)("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])),[[z,e.loading.station]])])),_:1}),(0,a.bF)(w,{label:"服务推广佣金",name:"service"},{default:(0,a.k6)((()=>[(0,a.bF)(k,{type:"info",title:"服务推广佣金说明",description:"服务推广佣金是指为特定服务引流、促成服务交易或相关活动后获得的佣金奖励。",closable:!1,"show-icon":"",style:{"margin-bottom":"20px"}}),(0,a.bo)(((0,a.uX)(),(0,a.CE)("div",null,[(0,a.bF)(f,{data:g.rules.service,border:"",style:{width:"100%"}},{default:(0,a.k6)((()=>[(0,a.bF)(h,{label:"ID",prop:"id",width:"80",align:"center"}),(0,a.bF)(h,{label:"规则名称",prop:"name","min-width":"120"}),(0,a.bF)(h,{label:"服务类型",align:"center",width:"120"},{default:(0,a.k6)((({row:e})=>[(0,a.eW)((0,i.v_)(g.getServiceTypeText(e.service_type)),1)])),_:1}),(0,a.bF)(h,{label:"佣金类型",align:"center",width:"120"},{default:(0,a.k6)((({row:e})=>[(0,a.eW)((0,i.v_)(g.getCommissionTypeText(e.type)),1)])),_:1}),(0,a.bF)(h,{label:"佣金值",align:"center",width:"120"},{default:(0,a.k6)((({row:e})=>[1===e.type?((0,a.uX)(),(0,a.CE)("span",b,(0,i.v_)(e.value)+"元",1)):2===e.type?((0,a.uX)(),(0,a.CE)("span",c,(0,i.v_)(e.value)+"%",1)):(0,a.Q3)("",!0)])),_:1}),(0,a.bF)(h,{label:"条件说明",prop:"condition","min-width":"200"}),(0,a.bF)(h,{label:"状态",align:"center",width:"100"},{default:(0,a.k6)((({row:e})=>[(0,a.bF)(F,{modelValue:e.status,"onUpdate:modelValue":l=>e.status=l,"active-value":1,"inactive-value":0,onChange:l=>g.handleStatusChange(e,"service")},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),(0,a.bF)(h,{label:"操作",align:"center",width:"150"},{default:(0,a.k6)((({row:e})=>[(0,a.bF)(v,{type:"primary",size:"small",onClick:l=>g.handleEditRule(e,"service")},{default:(0,a.k6)((()=>l[23]||(l[23]=[(0,a.eW)("编辑")]))),_:2},1032,["onClick"]),(0,a.bF)(v,{type:"danger",size:"small",onClick:l=>g.handleDeleteRule(e,"service")},{default:(0,a.k6)((()=>l[24]||(l[24]=[(0,a.eW)("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])),[[z,e.loading.service]])])),_:1}),(0,a.bF)(w,{label:"全局设置",name:"settings"},{default:(0,a.k6)((()=>[(0,a.bF)(k,{type:"info",title:"全局佣金设置说明",description:"全局佣金设置将应用于所有类型的佣金计算，包括提现限制、结算周期等。",closable:!1,"show-icon":"",style:{"margin-bottom":"20px"}}),(0,a.bo)(((0,a.uX)(),(0,a.CE)("div",null,[(0,a.bF)(S,{model:e.globalSettings,"label-width":"180px",class:"settings-form"},{default:(0,a.k6)((()=>[(0,a.bF)(C,{label:"最低提现金额"},{default:(0,a.k6)((()=>[(0,a.bF)(V,{modelValue:e.globalSettings.min_withdrawal,"onUpdate:modelValue":l[0]||(l[0]=l=>e.globalSettings.min_withdrawal=l),min:0,precision:2,step:10,style:{width:"200px"}},null,8,["modelValue"]),l[25]||(l[25]=(0,a.Lk)("span",{class:"form-tip"},"元（0表示无限制）",-1))])),_:1}),(0,a.bF)(C,{label:"每日最高提现次数"},{default:(0,a.k6)((()=>[(0,a.bF)(V,{modelValue:e.globalSettings.max_daily_withdrawal_count,"onUpdate:modelValue":l[1]||(l[1]=l=>e.globalSettings.max_daily_withdrawal_count=l),min:0,precision:0,step:1,style:{width:"200px"}},null,8,["modelValue"]),l[26]||(l[26]=(0,a.Lk)("span",{class:"form-tip"},"次（0表示无限制）",-1))])),_:1}),(0,a.bF)(C,{label:"单次最高提现金额"},{default:(0,a.k6)((()=>[(0,a.bF)(V,{modelValue:e.globalSettings.max_single_withdrawal,"onUpdate:modelValue":l[2]||(l[2]=l=>e.globalSettings.max_single_withdrawal=l),min:0,precision:2,step:100,style:{width:"200px"}},null,8,["modelValue"]),l[27]||(l[27]=(0,a.Lk)("span",{class:"form-tip"},"元（0表示无限制）",-1))])),_:1}),(0,a.bF)(C,{label:"佣金结算周期"},{default:(0,a.k6)((()=>[(0,a.bF)(x,{modelValue:e.globalSettings.settlement_cycle,"onUpdate:modelValue":l[3]||(l[3]=l=>e.globalSettings.settlement_cycle=l),style:{width:"200px"}},{default:(0,a.k6)((()=>[(0,a.bF)(R,{label:"实时结算",value:"realtime"}),(0,a.bF)(R,{label:"日结（每日24点结算）",value:"daily"}),(0,a.bF)(R,{label:"周结（每周一0点结算）",value:"weekly"}),(0,a.bF)(R,{label:"月结（每月1日0点结算）",value:"monthly"})])),_:1},8,["modelValue"])])),_:1}),(0,a.bF)(C,{label:"佣金提现手续费"},{default:(0,a.k6)((()=>[(0,a.bF)(V,{modelValue:e.globalSettings.withdrawal_fee,"onUpdate:modelValue":l[4]||(l[4]=l=>e.globalSettings.withdrawal_fee=l),min:0,max:100,precision:2,step:.1,style:{width:"200px"}},null,8,["modelValue"]),l[28]||(l[28]=(0,a.Lk)("span",{class:"form-tip"},"%",-1))])),_:1}),(0,a.bF)(C,{label:"同一用户最高返佣层级"},{default:(0,a.k6)((()=>[(0,a.bF)(V,{modelValue:e.globalSettings.max_commission_level,"onUpdate:modelValue":l[5]||(l[5]=l=>e.globalSettings.max_commission_level=l),min:1,max:10,precision:0,step:1,style:{width:"200px"}},null,8,["modelValue"]),l[29]||(l[29]=(0,a.Lk)("span",{class:"form-tip"},"层",-1))])),_:1}),(0,a.bF)(C,{label:"佣金系统总开关"},{default:(0,a.k6)((()=>[(0,a.bF)(F,{modelValue:e.globalSettings.system_enabled,"onUpdate:modelValue":l[6]||(l[6]=l=>e.globalSettings.system_enabled=l)},null,8,["modelValue"]),l[30]||(l[30]=(0,a.Lk)("span",{class:"form-tip"},"关闭后所有佣金将停止计算和发放",-1))])),_:1}),(0,a.bF)(C,null,{default:(0,a.k6)((()=>[(0,a.bF)(v,{type:"primary",onClick:g.saveGlobalSettings,loading:e.submitting},{default:(0,a.k6)((()=>l[31]||(l[31]=[(0,a.eW)("保存设置")]))),_:1},8,["onClick","loading"]),(0,a.bF)(v,{onClick:g.resetGlobalSettings},{default:(0,a.k6)((()=>l[32]||(l[32]=[(0,a.eW)("重置")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])])),[[z,e.loading.settings]])])),_:1})])),_:1},8,["modelValue"])])),_:1}),(0,a.bF)(L,{title:g.dialogTitle,modelValue:e.dialogVisible,"onUpdate:modelValue":l[16]||(l[16]=l=>e.dialogVisible=l),width:"600px","close-on-click-modal":!1},{footer:(0,a.k6)((()=>[(0,a.Lk)("div",p,[(0,a.bF)(v,{onClick:l[15]||(l[15]=l=>e.dialogVisible=!1)},{default:(0,a.k6)((()=>l[35]||(l[35]=[(0,a.eW)("取 消")]))),_:1}),(0,a.bF)(v,{type:"primary",onClick:g.saveRule,loading:e.submitting},{default:(0,a.k6)((()=>l[36]||(l[36]=[(0,a.eW)("确 定")]))),_:1},8,["onClick","loading"])])])),default:(0,a.k6)((()=>[(0,a.bo)(((0,a.uX)(),(0,a.Wv)(S,{ref:"ruleForm",model:e.currentRule,rules:g.rules,"label-width":"120px"},{default:(0,a.k6)((()=>[(0,a.bF)(C,{label:"规则名称",prop:"name"},{default:(0,a.k6)((()=>[(0,a.bF)(U,{modelValue:e.currentRule.name,"onUpdate:modelValue":l[8]||(l[8]=l=>e.currentRule.name=l),placeholder:"请输入规则名称"},null,8,["modelValue"])])),_:1}),g.isAddForm?((0,a.uX)(),(0,a.Wv)(C,{key:0,label:"规则类型",prop:"rule_type"},{default:(0,a.k6)((()=>[(0,a.bF)(x,{modelValue:e.currentRule.rule_type,"onUpdate:modelValue":l[9]||(l[9]=l=>e.currentRule.rule_type=l),placeholder:"请选择规则类型",style:{width:"100%"}},{default:(0,a.k6)((()=>[(0,a.bF)(R,{label:"用户推广佣金",value:"user"}),(0,a.bF)(R,{label:"驿站推广佣金",value:"station"}),(0,a.bF)(R,{label:"服务推广佣金",value:"service"})])),_:1},8,["modelValue"])])),_:1})):(0,a.Q3)("",!0),"service"===e.currentRule.rule_type?((0,a.uX)(),(0,a.Wv)(C,{key:1,label:"服务类型",prop:"service_type"},{default:(0,a.k6)((()=>[(0,a.bF)(x,{modelValue:e.currentRule.service_type,"onUpdate:modelValue":l[10]||(l[10]=l=>e.currentRule.service_type=l),placeholder:"请选择服务类型",style:{width:"100%"}},{default:(0,a.k6)((()=>[(0,a.bF)(R,{label:"顶班接单",value:"substitute"}),(0,a.bF)(R,{label:"驿站转让",value:"transfer"}),(0,a.bF)(R,{label:"招聘求职",value:"recruitment"}),(0,a.bF)(R,{label:"其他服务",value:"other"})])),_:1},8,["modelValue"])])),_:1})):(0,a.Q3)("",!0),(0,a.bF)(C,{label:"佣金类型",prop:"type"},{default:(0,a.k6)((()=>[(0,a.bF)(X,{modelValue:e.currentRule.type,"onUpdate:modelValue":l[11]||(l[11]=l=>e.currentRule.type=l)},{default:(0,a.k6)((()=>[(0,a.bF)(E,{label:1},{default:(0,a.k6)((()=>l[33]||(l[33]=[(0,a.eW)("固定金额")]))),_:1}),(0,a.bF)(E,{label:2},{default:(0,a.k6)((()=>l[34]||(l[34]=[(0,a.eW)("百分比")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),(0,a.bF)(C,{label:"佣金值",prop:"value"},{default:(0,a.k6)((()=>[(0,a.bF)(V,{modelValue:e.currentRule.value,"onUpdate:modelValue":l[12]||(l[12]=l=>e.currentRule.value=l),min:0,precision:1===e.currentRule.type?2:1,max:1===e.currentRule.type?1e4:100,step:1===e.currentRule.type?10:.1,style:{width:"200px"}},null,8,["modelValue","precision","max","step"]),(0,a.Lk)("span",m,(0,i.v_)(1===e.currentRule.type?"元":"%"),1)])),_:1}),(0,a.bF)(C,{label:"触发条件",prop:"condition"},{default:(0,a.k6)((()=>[(0,a.bF)(U,{modelValue:e.currentRule.condition,"onUpdate:modelValue":l[13]||(l[13]=l=>e.currentRule.condition=l),type:"textarea",rows:3,placeholder:"请描述触发该佣金规则的条件"},null,8,["modelValue"])])),_:1}),(0,a.bF)(C,{label:"启用状态",prop:"status"},{default:(0,a.k6)((()=>[(0,a.bF)(F,{modelValue:e.currentRule.status,"onUpdate:modelValue":l[14]||(l[14]=l=>e.currentRule.status=l),"active-value":1,"inactive-value":0},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])),[[z,e.dialogLoading]])])),_:1},8,["title","modelValue"])])}var y=t(144),_=t(1219),v=t(2933);const k={name:"Commission",setup(){const e=(0,y.KR)("user"),l=(0,y.KR)(null),t=(0,y.Kh)({loading:{user:!1,station:!1,service:!1,settings:!1},rules:{user:[],station:[],service:[]},globalSettings:{min_withdrawal:50,max_daily_withdrawal_count:3,max_single_withdrawal:5e3,settlement_cycle:"daily",withdrawal_fee:1,max_commission_level:3,system_enabled:!0},dialogVisible:!1,dialogLoading:!1,dialogType:"add",currentRule:{id:null,name:"",rule_type:"user",type:1,value:0,condition:"",service_type:"",status:1},originalGlobalSettings:{},submitting:!1}),i={name:[{required:!0,message:"请输入规则名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],rule_type:[{required:!0,message:"请选择规则类型",trigger:"change"}],service_type:[{required:!0,message:"请选择服务类型",trigger:"change"}],type:[{required:!0,message:"请选择佣金类型",trigger:"change"}],value:[{required:!0,message:"请输入佣金值",trigger:"blur"}],condition:[{required:!0,message:"请输入触发条件",trigger:"blur"}]},n=(0,a.EW)((()=>"add"===t.dialogType?"添加佣金规则":"编辑佣金规则")),u=(0,a.EW)((()=>"add"===t.dialogType)),o=e=>{const l={1:"固定金额",2:"百分比"};return l[e]||"未知"},s=e=>{const l={substitute:"顶班接单",transfer:"驿站转让",recruitment:"招聘求职",other:"其他服务"};return l[e]||"未知"},d=e=>{t.loading[e]=!0,setTimeout((()=>{"user"===e?t.rules.user=[{id:1,name:"用户注册奖励",type:1,value:5,condition:"邀请用户成功注册并完成手机验证",status:1},{id:2,name:"首单完成奖励",type:1,value:10,condition:"被邀请用户完成首单消费",status:1},{id:3,name:"消费佣金",type:2,value:5,condition:"被邀请用户每笔订单的消费金额",status:0}]:"station"===e?t.rules.station=[{id:4,name:"驿站注册认证",type:1,value:50,condition:"邀请驿站完成注册并通过认证",status:1},{id:5,name:"驿站首月运营",type:1,value:100,condition:"被邀请驿站完成首月运营（接单不少于30单）",status:1}]:"service"===e&&(t.rules.service=[{id:6,name:"顶班服务佣金",service_type:"substitute",type:2,value:10,condition:"通过推广码达成的顶班服务交易",status:1},{id:7,name:"驿站转让服务",service_type:"transfer",type:2,value:5,condition:"通过推广达成的驿站转让交易",status:1},{id:8,name:"招聘服务佣金",service_type:"recruitment",type:1,value:20,condition:"通过推广达成的招聘交易",status:0}]),t.loading[e]=!1}),500)},r=()=>{t.loading.settings=!0,setTimeout((()=>{t.originalGlobalSettings={...t.globalSettings},t.loading.settings=!1}),500)},b=()=>{t.submitting=!0,setTimeout((()=>{t.originalGlobalSettings={...t.globalSettings},t.submitting=!1,(0,_.nk)({type:"success",message:"全局佣金设置保存成功"})}),500)},c=()=>{t.globalSettings={...t.originalGlobalSettings}},m=()=>{t.dialogType="add",t.currentRule={id:null,name:"",rule_type:"user",type:1,value:0,condition:"",service_type:"",status:1},t.dialogVisible=!0},p=(e,l)=>{t.dialogType="edit",t.currentRule={...e,rule_type:l},"service"!==l||t.currentRule.service_type||(t.currentRule.service_type="other"),t.dialogVisible=!0},g=()=>{l.value&&l.value.validate((e=>{e&&(t.submitting=!0,setTimeout((()=>{const{rule_type:e,...l}=t.currentRule;if("add"===t.dialogType){const a=Math.floor(1e3*Math.random())+10,i={...l,id:a};t.rules[e].push(i),(0,_.nk)({type:"success",message:"添加规则成功"})}else{const a=t.rules[e].findIndex((e=>e.id===l.id));-1!==a&&(t.rules[e][a]={...l},(0,_.nk)({type:"success",message:"更新规则成功"}))}t.dialogVisible=!1,t.submitting=!1}),500))}))},k=(e,l)=>{v.s.confirm("确定要删除该佣金规则吗？删除后无法恢复。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{const a=t.rules[l].findIndex((l=>l.id===e.id));-1!==a&&(t.rules[l].splice(a,1),(0,_.nk)({type:"success",message:"删除成功"}))})).catch((()=>{}))},h=(e,l)=>{setTimeout((()=>{(0,_.nk)({type:"success",message:1===e.status?"规则已启用":"规则已禁用"})}),300)};return(0,a.sV)((()=>{d("user"),d("station"),d("service"),r()})),{activeTab:e,ruleForm:l,dialogTitle:n,isAddForm:u,rules:i,...(0,y.QW)(t),getCommissionTypeText:o,getServiceTypeText:s,handleAddRule:m,handleEditRule:p,handleDeleteRule:k,handleStatusChange:h,saveRule:g,saveGlobalSettings:b,resetGlobalSettings:c}}};var h=t(1241);const F=(0,h.A)(k,[["render",g],["__scopeId","data-v-1a8d9246"]]),f=F}}]);