"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[5382],{5382:(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var l=a(6768),n=a(4232);const i={class:"page-container"},r={class:"page-header"},s={class:"detail-container"},o={class:"card-header"},u={class:"header-status"},c={class:"image-container"},d={key:0,class:"image-list"},b={class:"image-desc"},p={key:1,class:"no-image"},m={class:"action-container"},k={class:"dialog-footer"};function _(e,t,a,_,f,v){const h=(0,l.g2)("el-page-header"),g=(0,l.g2)("el-tag"),F=(0,l.g2)("el-descriptions-item"),D=(0,l.g2)("el-descriptions"),y=(0,l.g2)("el-card"),W=(0,l.g2)("el-image"),T=(0,l.g2)("el-button"),j=(0,l.g2)("el-option"),L=(0,l.g2)("el-select"),I=(0,l.g2)("el-form-item"),q=(0,l.g2)("el-input"),C=(0,l.g2)("el-form"),V=(0,l.g2)("el-dialog"),x=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",i,[(0,l.Lk)("div",r,[(0,l.bF)(h,{onBack:v.goBack,title:f.contentTypeMap[f.contentType],content:f.contentData.title||"内容详情"},null,8,["onBack","title","content"])]),(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",s,[(0,l.bF)(y,{class:"info-card"},{header:(0,l.k6)((()=>[(0,l.Lk)("div",o,[t[5]||(t[5]=(0,l.Lk)("span",null,"基本信息",-1)),(0,l.Lk)("div",u,[(0,l.bF)(g,{type:"warning"},{default:(0,l.k6)((()=>t[4]||(t[4]=[(0,l.eW)("待审核")]))),_:1})])])])),default:(0,l.k6)((()=>[(0,l.bF)(D,{column:2,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(F,{label:"发布人"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.contentData.publisher),1)])),_:1}),(0,l.bF)(F,{label:"联系电话"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.contentData.phone),1)])),_:1}),(0,l.bF)(F,{label:"发布时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.contentData.createTime),1)])),_:1}),(0,l.bF)(F,{label:"内容类型"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.contentTypeMap[f.contentType]),1)])),_:1})])),_:1})])),_:1}),"transfer"===f.contentType?((0,l.uX)(),(0,l.Wv)(y,{key:0,class:"info-card"},{header:(0,l.k6)((()=>t[6]||(t[6]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"驿站转让详情")],-1)]))),default:(0,l.k6)((()=>[(0,l.bF)(D,{column:2,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(F,{label:"驿站名称"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.transferData.stationName),1)])),_:1}),(0,l.bF)(F,{label:"驿站类型"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.transferData.stationType),1)])),_:1}),(0,l.bF)(F,{label:"所在地区",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.transferData.region),1)])),_:1}),(0,l.bF)(F,{label:"详细地址",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.transferData.address),1)])),_:1}),(0,l.bF)(F,{label:"转让价格"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.transferData.price)+" 元",1)])),_:1}),(0,l.bF)(F,{label:"月营业额"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.transferData.monthlyRevenue)+" 元",1)])),_:1}),(0,l.bF)(F,{label:"转让原因",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.transferData.reason),1)])),_:1}),(0,l.bF)(F,{label:"转让说明",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.transferData.description),1)])),_:1})])),_:1})])),_:1})):(0,l.Q3)("",!0),"equipment"===f.contentType?((0,l.uX)(),(0,l.Wv)(y,{key:1,class:"info-card"},{header:(0,l.k6)((()=>t[7]||(t[7]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"设备交易详情")],-1)]))),default:(0,l.k6)((()=>[(0,l.bF)(D,{column:2,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(F,{label:"设备名称"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.equipmentData.name),1)])),_:1}),(0,l.bF)(F,{label:"设备类型"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.equipmentData.type),1)])),_:1}),(0,l.bF)(F,{label:"品牌型号"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.equipmentData.brand),1)])),_:1}),(0,l.bF)(F,{label:"使用年限"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.equipmentData.useYears)+" 年",1)])),_:1}),(0,l.bF)(F,{label:"新旧程度"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.equipmentData.condition),1)])),_:1}),(0,l.bF)(F,{label:"销售价格"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.equipmentData.price)+" 元",1)])),_:1}),(0,l.bF)(F,{label:"交易方式"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.equipmentData.tradeType),1)])),_:1}),(0,l.bF)(F,{label:"所在地区"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.equipmentData.region),1)])),_:1}),(0,l.bF)(F,{label:"设备描述",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.equipmentData.description),1)])),_:1})])),_:1})])),_:1})):(0,l.Q3)("",!0),"recruitment"===f.contentType?((0,l.uX)(),(0,l.Wv)(y,{key:2,class:"info-card"},{header:(0,l.k6)((()=>t[8]||(t[8]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"招聘求职详情")],-1)]))),default:(0,l.k6)((()=>[(0,l.bF)(D,{column:2,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(F,{label:"职位名称"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.recruitmentData.position),1)])),_:1}),(0,l.bF)(F,{label:"工作类型"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.recruitmentData.jobType),1)])),_:1}),(0,l.bF)(F,{label:"薪资范围"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.recruitmentData.salary),1)])),_:1}),(0,l.bF)(F,{label:"工作地区"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.recruitmentData.region),1)])),_:1}),(0,l.bF)(F,{label:"招聘人数"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.recruitmentData.number)+" 人",1)])),_:1}),(0,l.bF)(F,{label:"工作经验"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.recruitmentData.experience),1)])),_:1}),(0,l.bF)(F,{label:"学历要求"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.recruitmentData.education),1)])),_:1}),(0,l.bF)(F,{label:"截止日期"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.recruitmentData.deadline),1)])),_:1}),(0,l.bF)(F,{label:"职位描述",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.recruitmentData.description),1)])),_:1}),(0,l.bF)(F,{label:"任职要求",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.recruitmentData.requirement),1)])),_:1})])),_:1})])),_:1})):(0,l.Q3)("",!0),"substitution"===f.contentType?((0,l.uX)(),(0,l.Wv)(y,{key:3,class:"info-card"},{header:(0,l.k6)((()=>t[9]||(t[9]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"顶班服务详情")],-1)]))),default:(0,l.k6)((()=>[(0,l.bF)(D,{column:2,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(F,{label:"顶班类型"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.substitutionData.type),1)])),_:1}),(0,l.bF)(F,{label:"工作地区"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.substitutionData.region),1)])),_:1}),(0,l.bF)(F,{label:"具体地址"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.substitutionData.address),1)])),_:1}),(0,l.bF)(F,{label:"报酬金额"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.substitutionData.payment)+" 元/天",1)])),_:1}),(0,l.bF)(F,{label:"开始日期"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.substitutionData.startDate),1)])),_:1}),(0,l.bF)(F,{label:"结束日期"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.substitutionData.endDate),1)])),_:1}),(0,l.bF)(F,{label:"工作时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.substitutionData.workTime),1)])),_:1}),(0,l.bF)(F,{label:"联系方式"},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.substitutionData.contactInfo),1)])),_:1}),(0,l.bF)(F,{label:"工作内容",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.substitutionData.description),1)])),_:1}),(0,l.bF)(F,{label:"其他要求",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(f.substitutionData.requirement),1)])),_:1})])),_:1})])),_:1})):(0,l.Q3)("",!0),(0,l.bF)(y,{class:"info-card"},{header:(0,l.k6)((()=>t[10]||(t[10]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"图片信息")],-1)]))),default:(0,l.k6)((()=>[(0,l.Lk)("div",c,[f.images.length>0?((0,l.uX)(),(0,l.CE)("div",d,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(f.images,((e,a)=>((0,l.uX)(),(0,l.CE)("div",{key:a,class:"image-item"},[(0,l.bF)(W,{src:e.url,"preview-src-list":f.images.map((e=>e.url)),fit:"cover"},{error:(0,l.k6)((()=>t[11]||(t[11]=[(0,l.Lk)("div",{class:"image-error"},[(0,l.Lk)("i",{class:"el-icon-picture-outline"})],-1)]))),_:2},1032,["src","preview-src-list"]),(0,l.Lk)("div",b,(0,n.v_)(e.name),1)])))),128))])):((0,l.uX)(),(0,l.CE)("div",p,"暂无图片"))])])),_:1}),(0,l.Lk)("div",m,[(0,l.bF)(T,{type:"primary",onClick:v.handleApprove},{default:(0,l.k6)((()=>t[12]||(t[12]=[(0,l.eW)("通过审核")]))),_:1},8,["onClick"]),(0,l.bF)(T,{type:"danger",onClick:v.handleReject},{default:(0,l.k6)((()=>t[13]||(t[13]=[(0,l.eW)("拒绝审核")]))),_:1},8,["onClick"])])])),[[x,f.loading]]),(0,l.bF)(V,{title:"拒绝理由",modelValue:f.rejectDialogVisible,"onUpdate:modelValue":t[3]||(t[3]=e=>f.rejectDialogVisible=e),width:"500px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",k,[(0,l.bF)(T,{onClick:t[2]||(t[2]=e=>f.rejectDialogVisible=!1)},{default:(0,l.k6)((()=>t[14]||(t[14]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(T,{type:"primary",onClick:v.submitReject},{default:(0,l.k6)((()=>t[15]||(t[15]=[(0,l.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bF)(C,{ref:"rejectForm",model:f.rejectForm,"label-width":"80px"},{default:(0,l.k6)((()=>[(0,l.bF)(I,{label:"拒绝理由",prop:"reason"},{default:(0,l.k6)((()=>[(0,l.bF)(L,{modelValue:f.rejectForm.reason,"onUpdate:modelValue":t[0]||(t[0]=e=>f.rejectForm.reason=e),placeholder:"请选择拒绝理由",style:{width:"100%"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(f.reasonOptions,(e=>((0,l.uX)(),(0,l.Wv)(j,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(I,{label:"补充说明",prop:"remark"},{default:(0,l.k6)((()=>[(0,l.bF)(q,{modelValue:f.rejectForm.remark,"onUpdate:modelValue":t[1]||(t[1]=e=>f.rejectForm.remark=e),type:"textarea",placeholder:"请输入补充说明"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}const f={name:"AuditDetail",data(){return{contentId:null,contentType:"",loading:!1,contentTypeMap:{transfer:"驿站转让",equipment:"设备交易",recruitment:"招聘求职",substitution:"顶班服务"},contentData:{id:"",title:"",publisher:"",phone:"",createTime:"",content:""},transferData:{stationName:"海淀区中关村社区驿站",stationType:"社区驿站",region:"北京市海淀区",address:"北京市海淀区中关村大街123号",price:"25000",monthlyRevenue:"18000",reason:"个人原因，无法继续经营",description:"该驿站位于中关村科技园区内，日均快递量200+，客流量大，接手可立即营业。"},equipmentData:{name:"二手快递柜",type:"快递存取设备",brand:"丰巢 FC-200",useYears:"2",condition:"8成新",price:"3500",tradeType:"自提",region:"北京市朝阳区",description:"9成新丰巢快递柜，使用两年，无划痕，功能完好，支持扫码存取件。"},recruitmentData:{position:"驿站营业员",jobType:"全职",salary:"4000-5000元/月",region:"北京市海淀区",number:"2",experience:"经验不限",education:"高中及以上",deadline:"2023-05-30",description:"负责日常快递的收发、分拣、客户服务等工作",requirement:"形象气质好，有较强的沟通能力和服务意识，能熟练操作电脑。"},substitutionData:{type:"临时顶班",region:"北京市朝阳区",address:"朝阳区望京SOHO T1",payment:"200",startDate:"2023-04-20",endDate:"2023-04-25",workTime:"09:00-18:00",contactInfo:"13800138000",description:"负责快递的接收、分拣和发放，以及简单的客户服务",requirement:"有驿站工作经验优先，需要熟悉常用快递APP的操作"},images:[{url:"https://via.placeholder.com/800x600?text=Image1",name:"店面外观"},{url:"https://via.placeholder.com/800x600?text=Image2",name:"内部环境"},{url:"https://via.placeholder.com/800x600?text=Image3",name:"营业执照"}],rejectForm:{reason:"",remark:""},reasonOptions:[{label:"内容不真实",value:"内容不真实"},{label:"信息不完整",value:"信息不完整"},{label:"违反平台规则",value:"违反平台规则"},{label:"图片不清晰",value:"图片不清晰"},{label:"联系方式错误",value:"联系方式错误"},{label:"其他原因",value:"其他原因"}],rejectDialogVisible:!1}},created(){this.contentId=this.$route.params.id,this.contentType=this.$route.params.type,this.getDetailData()},methods:{getDetailData(){this.loading=!0,setTimeout((()=>{"transfer"===this.contentType?this.contentData={id:this.contentId,title:"北京市海淀区中关村驿站转让",publisher:"张三",phone:"13800138001",createTime:"2023-04-15 10:30:00",content:"由于个人原因，现将经营良好的中关村驿站转让..."}:"equipment"===this.contentType?this.contentData={id:this.contentId,title:"二手快递柜设备出售",publisher:"李四",phone:"13800138002",createTime:"2023-04-15 15:20:00",content:"9成新丰巢快递柜，使用两年，功能完好..."}:"recruitment"===this.contentType?this.contentData={id:this.contentId,title:"招聘驿站兼职人员",publisher:"王五",phone:"13800138003",createTime:"2023-04-16 09:15:00",content:"招聘驿站兼职人员，工作时间灵活..."}:"substitution"===this.contentType&&(this.contentData={id:this.contentId,title:"周末顶班服务",publisher:"赵六",phone:"13800138004",createTime:"2023-04-16 11:45:00",content:"本周末需要顶班人员，有经验者优先..."}),this.loading=!1}),1e3)},goBack(){this.$router.go(-1)},handleApprove(){this.$confirm(`确认通过内容"${this.contentData.title}"的审核?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{console.log("通过内容ID：",this.contentId),this.$message({type:"success",message:"审核通过成功!"}),setTimeout((()=>{this.goBack()}),1e3)})).catch((()=>{}))},handleReject(){this.rejectForm={reason:"",remark:""},this.rejectDialogVisible=!0},submitReject(){this.rejectForm.reason?(console.log("拒绝内容ID：",this.contentId),console.log("拒绝理由：",this.rejectForm.reason),console.log("补充说明：",this.rejectForm.remark),this.rejectDialogVisible=!1,this.$message({type:"success",message:"审核拒绝成功!"}),setTimeout((()=>{this.goBack()}),1e3)):this.$message({type:"warning",message:"请选择拒绝理由"})}}};var v=a(1241);const h=(0,v.A)(f,[["render",_],["__scopeId","data-v-37720c81"]]),g=h}}]);