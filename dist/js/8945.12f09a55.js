"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[8945],{8945:(e,t,l)=>{l.r(t),l.d(t,{default:()=>c});var i=l(6768),a=l(5130),n=l(4232);const s={class:"app-container"};function o(e,t,l,o,r,u){const d=(0,i.g2)("el-input"),c=(0,i.g2)("el-form-item"),p=(0,i.g2)("el-option"),b=(0,i.g2)("el-select"),h=(0,i.g2)("el-button"),m=(0,i.g2)("el-form"),g=(0,i.g2)("el-col"),k=(0,i.g2)("right-toolbar"),y=(0,i.g2)("el-row"),F=(0,i.g2)("el-table-column"),f=(0,i.g2)("el-tag"),w=(0,i.g2)("el-table"),_=(0,i.g2)("pagination"),C=(0,i.g2)("el-card"),v=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)("div",s,[(0,i.bF)(C,null,{default:(0,i.k6)((()=>[t[10]||(t[10]=(0,i.Lk)("div",{slot:"header"},[(0,i.Lk)("span",null,"招聘列表")],-1)),(0,i.bo)((0,i.bF)(m,{model:r.queryParams,ref:"queryForm",inline:!0},{default:(0,i.k6)((()=>[(0,i.bF)(c,{label:"职位名称",prop:"jobTitle"},{default:(0,i.k6)((()=>[(0,i.bF)(d,{modelValue:r.queryParams.jobTitle,"onUpdate:modelValue":t[0]||(t[0]=e=>r.queryParams.jobTitle=e),placeholder:"请输入职位名称",clearable:"",size:"small"},null,8,["modelValue"])])),_:1}),(0,i.bF)(c,{label:"发布状态",prop:"status"},{default:(0,i.k6)((()=>[(0,i.bF)(b,{modelValue:r.queryParams.status,"onUpdate:modelValue":t[1]||(t[1]=e=>r.queryParams.status=e),placeholder:"请选择状态",clearable:"",size:"small"},{default:(0,i.k6)((()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(r.statusOptions,(e=>((0,i.uX)(),(0,i.Wv)(p,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,i.bF)(c,null,{default:(0,i.k6)((()=>[(0,i.bF)(h,{type:"primary",icon:"el-icon-search",size:"mini",onClick:u.handleQuery},{default:(0,i.k6)((()=>t[2]||(t[2]=[(0,i.eW)("搜索")]))),_:1},8,["onClick"]),(0,i.bF)(h,{icon:"el-icon-refresh",size:"mini",onClick:u.resetQuery},{default:(0,i.k6)((()=>t[3]||(t[3]=[(0,i.eW)("重置")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"]),[[a.aG,r.showSearch]]),(0,i.bF)(y,{gutter:10,class:"mb8"},{default:(0,i.k6)((()=>[(0,i.bF)(g,{span:1.5},{default:(0,i.k6)((()=>[(0,i.bF)(h,{type:"primary",plain:"",icon:"el-icon-plus",size:"mini",onClick:u.handleAdd},{default:(0,i.k6)((()=>t[4]||(t[4]=[(0,i.eW)("新增")]))),_:1},8,["onClick"])])),_:1}),(0,i.bF)(g,{span:1.5},{default:(0,i.k6)((()=>[(0,i.bF)(h,{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:r.single,onClick:u.handleUpdate},{default:(0,i.k6)((()=>t[5]||(t[5]=[(0,i.eW)("编辑")]))),_:1},8,["disabled","onClick"])])),_:1}),(0,i.bF)(g,{span:1.5},{default:(0,i.k6)((()=>[(0,i.bF)(h,{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:r.multiple,onClick:u.handleDelete},{default:(0,i.k6)((()=>t[6]||(t[6]=[(0,i.eW)("删除")]))),_:1},8,["disabled","onClick"])])),_:1}),(0,i.bF)(k,{showSearch:r.showSearch,onQueryTable:u.getList},null,8,["showSearch","onQueryTable"])])),_:1}),(0,i.bo)(((0,i.uX)(),(0,i.Wv)(w,{data:r.recruitmentList,onSelectionChange:u.handleSelectionChange},{default:(0,i.k6)((()=>[(0,i.bF)(F,{type:"selection",width:"55",align:"center"}),(0,i.bF)(F,{label:"ID",align:"center",prop:"id",width:"80"}),(0,i.bF)(F,{label:"职位名称",align:"center",prop:"jobTitle","min-width":"120"}),(0,i.bF)(F,{label:"薪资范围",align:"center",prop:"salary","min-width":"100"}),(0,i.bF)(F,{label:"工作地点",align:"center",prop:"workLocation","min-width":"150"}),(0,i.bF)(F,{label:"招聘人数",align:"center",prop:"recruitCount",width:"90"}),(0,i.bF)(F,{label:"工作经验",align:"center",prop:"experience","min-width":"100"}),(0,i.bF)(F,{label:"学历要求",align:"center",prop:"education","min-width":"100"}),(0,i.bF)(F,{label:"发布状态",align:"center",prop:"status",width:"90"},{default:(0,i.k6)((e=>[(0,i.bF)(f,{type:"1"===e.row.status?"success":"info"},{default:(0,i.k6)((()=>[(0,i.eW)((0,n.v_)(u.statusFormat(e.row)),1)])),_:2},1032,["type"])])),_:1}),(0,i.bF)(F,{label:"发布时间",align:"center",prop:"publishTime",width:"160"}),(0,i.bF)(F,{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"200"},{default:(0,i.k6)((e=>[(0,i.bF)(h,{size:"mini",type:"text",icon:"el-icon-view",onClick:t=>u.handleView(e.row)},{default:(0,i.k6)((()=>t[7]||(t[7]=[(0,i.eW)("查看")]))),_:2},1032,["onClick"]),(0,i.bF)(h,{size:"mini",type:"text",icon:"el-icon-edit",onClick:t=>u.handleUpdate(e.row)},{default:(0,i.k6)((()=>t[8]||(t[8]=[(0,i.eW)("编辑")]))),_:2},1032,["onClick"]),(0,i.bF)(h,{size:"mini",type:"text",icon:"el-icon-delete",onClick:t=>u.handleDelete(e.row)},{default:(0,i.k6)((()=>t[9]||(t[9]=[(0,i.eW)("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data","onSelectionChange"])),[[v,r.loading]]),(0,i.bo)((0,i.bF)(_,{total:r.total,page:r.queryParams.pageNum,limit:r.queryParams.pageSize,onPagination:u.getList},null,8,["total","page","limit","onPagination"]),[[a.aG,r.total>0]])])),_:1})])}const r={name:"RecruitmentList",data(){return{loading:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,recruitmentList:[],statusOptions:[{value:"0",label:"草稿"},{value:"1",label:"发布中"},{value:"2",label:"已下线"}],queryParams:{pageNum:1,pageSize:10,jobTitle:void 0,status:void 0}}},created(){this.getList()},methods:{getList(){this.loading=!0,setTimeout((()=>{this.recruitmentList=[{id:1,jobTitle:"快递驿站店长",salary:"5000-8000",workLocation:"北京市海淀区中关村",recruitCount:2,experience:"1-3年",education:"大专",publishTime:"2023-01-15 10:30:00",status:"1"},{id:2,jobTitle:"驿站营业员",salary:"3500-5000",workLocation:"北京市朝阳区建国路",recruitCount:5,experience:"无经验",education:"高中",publishTime:"2023-01-16 14:20:00",status:"1"},{id:3,jobTitle:"配送专员",salary:"4000-6000",workLocation:"北京市丰台区丽泽桥",recruitCount:10,experience:"无经验",education:"不限",publishTime:"2023-01-14 09:15:00",status:"1"}],this.total=3,this.loading=!1}),500)},statusFormat(e){return this.selectDictLabel(this.statusOptions,e.status)},selectDictLabel(e,t){const l=e.find((e=>e.value===t));return l?l.label:""},handleQuery(){this.queryParams.pageNum=1,this.getList()},resetQuery(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange(e){this.ids=e.map((e=>e.id)),this.single=1!==e.length,this.multiple=!e.length},handleAdd(){this.$router.push("/recruitment/detail/0")},handleUpdate(e){const t=e.id||this.ids[0];this.$router.push(`/recruitment/detail/${t}`)},handleView(e){this.$router.push(`/recruitment/detail/${e.id}`)},handleDelete(e){e.id||this.ids;this.$confirm("是否确认删除所选招聘信息?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$message({type:"success",message:"删除成功!"}),this.getList()})).catch((()=>{}))}}};var u=l(1241);const d=(0,u.A)(r,[["render",o],["__scopeId","data-v-8f3dc212"]]),c=d}}]);