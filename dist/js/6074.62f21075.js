"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[6074],{6074:(e,a,n)=>{n.r(a),n.d(a,{default:()=>i});var s=n(6768);const t={class:"page-container"};function c(e,a,n,c,r,d){return(0,s.uX)(),(0,s.CE)("div",t,a[0]||(a[0]=[(0,s.Lk)("h2",null,"服务列表",-1),(0,s.Lk)("div",{class:"placeholder-content"}," 服务列表功能正在开发中... ",-1)]))}const r={name:"ServiceList",data(){return{}}};var d=n(1241);const u=(0,d.A)(r,[["render",c],["__scopeId","data-v-b2ad1578"]]),i=u}}]);