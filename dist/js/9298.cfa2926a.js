"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[9298],{9190:(e,t,a)=>{a.d(t,{A:()=>u});var l=a(6768),i=a(4232);function n(e,t,a,n,o,s){const r=(0,l.g2)("el-pagination");return(0,l.uX)(),(0,l.CE)("div",{class:(0,i.C4)([{hidden:a.hidden},"pagination-container"])},[(0,l.bF)(r,{background:a.background,"current-page":s.currentPage,"onUpdate:currentPage":t[0]||(t[0]=e=>s.currentPage=e),"page-size":s.pageSize,"onUpdate:pageSize":t[1]||(t[1]=e=>s.pageSize=e),layout:a.layout,"page-sizes":a.pageSizes,total:a.total,onSizeChange:s.handleSizeChange,onCurrentChange:s.handleCurrentChange},null,8,["background","current-page","page-size","layout","page-sizes","total","onSizeChange","onCurrentChange"])],2)}const o={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:10},pageSizes:{type:Array,default(){return[10,20,30,50]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},emits:["update:page","update:limit","pagination"],computed:{currentPage:{get(){return this.page},set(e){this.$emit("update:page",e)}},pageSize:{get(){return this.limit},set(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange(e){this.$emit("pagination",{page:this.currentPage,limit:e})},handleCurrentChange(e){this.$emit("pagination",{page:e,limit:this.pageSize})}}};var s=a(1241);const r=(0,s.A)(o,[["render",n],["__scopeId","data-v-5035dd33"]]),u=r},9298:(e,t,a)=>{a.r(t),a.d(t,{default:()=>v});var l=a(6768),i=a(5130),n=a(4232);const o={class:"app-container"},s={class:"card-header"},r={class:"dialog-footer"};function u(e,t,a,u,p,c){const d=(0,l.g2)("el-button"),h=(0,l.g2)("el-option"),g=(0,l.g2)("el-select"),m=(0,l.g2)("el-form-item"),b=(0,l.g2)("el-input"),f=(0,l.g2)("el-date-picker"),k=(0,l.g2)("el-form"),y=(0,l.g2)("el-col"),F=(0,l.g2)("right-toolbar"),w=(0,l.g2)("el-row"),C=(0,l.g2)("el-table-column"),v=(0,l.g2)("el-tag"),_=(0,l.g2)("el-table"),S=(0,l.g2)("pagination"),j=(0,l.g2)("el-card"),P=(0,l.g2)("el-dialog"),R=(0,l.gN)("hasPermi"),z=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",o,[(0,l.bF)(j,{class:"box-card"},{header:(0,l.k6)((()=>[(0,l.Lk)("div",s,[t[9]||(t[9]=(0,l.Lk)("span",null,"顶班接单申请",-1)),(0,l.bF)(d,{style:{float:"right",padding:"3px 0"},type:"text",onClick:c.refreshList},{default:(0,l.k6)((()=>t[8]||(t[8]=[(0,l.eW)("刷新")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bo)((0,l.bF)(k,{model:p.queryParams,ref:"queryForm",inline:!0},{default:(0,l.k6)((()=>[(0,l.bF)(m,{label:"申请状态",prop:"status"},{default:(0,l.k6)((()=>[(0,l.bF)(g,{modelValue:p.queryParams.status,"onUpdate:modelValue":t[0]||(t[0]=e=>p.queryParams.status=e),placeholder:"申请状态",clearable:"",style:{width:"150px"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(p.statusOptions,(e=>((0,l.uX)(),(0,l.Wv)(h,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(m,{label:"申请人",prop:"applicantName"},{default:(0,l.k6)((()=>[(0,l.bF)(b,{modelValue:p.queryParams.applicantName,"onUpdate:modelValue":t[1]||(t[1]=e=>p.queryParams.applicantName=e),placeholder:"请输入申请人姓名",clearable:"",style:{width:"200px"},onKeyup:(0,i.jR)(c.handleQuery,["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),(0,l.bF)(m,{label:"顶班时间",prop:"substituteTime"},{default:(0,l.k6)((()=>[(0,l.bF)(f,{modelValue:p.queryParams.substituteTime,"onUpdate:modelValue":t[2]||(t[2]=e=>p.queryParams.substituteTime=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",style:{width:"240px"}},null,8,["modelValue"])])),_:1}),(0,l.bF)(m,null,{default:(0,l.k6)((()=>[(0,l.bF)(d,{type:"primary",icon:"Search",onClick:c.handleQuery},{default:(0,l.k6)((()=>t[10]||(t[10]=[(0,l.eW)("搜索")]))),_:1},8,["onClick"]),(0,l.bF)(d,{icon:"Refresh",onClick:c.resetQuery},{default:(0,l.k6)((()=>t[11]||(t[11]=[(0,l.eW)("重置")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"]),[[i.aG,p.showSearch]]),(0,l.bF)(w,{gutter:10,class:"mb8"},{default:(0,l.k6)((()=>[(0,l.bF)(y,{span:1.5},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(d,{type:"success",plain:"",icon:"Check",disabled:p.single,onClick:c.handleApprove},{default:(0,l.k6)((()=>t[12]||(t[12]=[(0,l.eW)("批准")]))),_:1},8,["disabled","onClick"])),[[R,["substitution:application:approve"]]])])),_:1}),(0,l.bF)(y,{span:1.5},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(d,{type:"danger",plain:"",icon:"Close",disabled:p.single,onClick:c.handleReject},{default:(0,l.k6)((()=>t[13]||(t[13]=[(0,l.eW)("拒绝")]))),_:1},8,["disabled","onClick"])),[[R,["substitution:application:reject"]]])])),_:1}),(0,l.bF)(F,{showSearch:p.showSearch,"onUpdate:showSearch":t[3]||(t[3]=e=>p.showSearch=e),onRefreshList:c.getList},null,8,["showSearch","onRefreshList"])])),_:1}),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(_,{data:p.applicationList,onSelectionChange:c.handleSelectionChange},{default:(0,l.k6)((()=>[(0,l.bF)(C,{type:"selection",width:"55",align:"center"}),(0,l.bF)(C,{label:"申请编号",align:"center",prop:"id",width:"120"}),(0,l.bF)(C,{label:"申请人",align:"center",prop:"applicantName","min-width":"100","show-overflow-tooltip":!0}),(0,l.bF)(C,{label:"联系电话",align:"center",prop:"applicantPhone","min-width":"120"}),(0,l.bF)(C,{label:"顶班驿站",align:"center",prop:"stationName","min-width":"150","show-overflow-tooltip":!0}),(0,l.bF)(C,{label:"顶班日期",align:"center",prop:"substituteDate",width:"100"}),(0,l.bF)(C,{label:"顶班时段",align:"center",prop:"substituteTime",width:"180"}),(0,l.bF)(C,{label:"申请时间",align:"center",prop:"applyTime",width:"160"}),(0,l.bF)(C,{label:"状态",align:"center",prop:"status",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(v,{type:p.statusTypeMap[e.row.status]},{default:(0,l.k6)((()=>[(0,l.eW)((0,n.v_)(p.statusLabelMap[e.row.status]),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(C,{label:"操作",align:"center",width:"180","class-name":"small-padding fixed-width"},{default:(0,l.k6)((e=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(d,{type:"text",icon:"View",onClick:t=>c.handleView(e.row)},{default:(0,l.k6)((()=>t[14]||(t[14]=[(0,l.eW)("查看")]))),_:2},1032,["onClick"])),[[R,["substitution:application:view"]]]),0===e.row.status?(0,l.bo)(((0,l.uX)(),(0,l.Wv)(d,{key:0,type:"text",icon:"Check",onClick:t=>c.handleApprove(e.row)},{default:(0,l.k6)((()=>t[15]||(t[15]=[(0,l.eW)("批准")]))),_:2},1032,["onClick"])),[[R,["substitution:application:approve"]]]):(0,l.Q3)("",!0),0===e.row.status?(0,l.bo)(((0,l.uX)(),(0,l.Wv)(d,{key:1,type:"text",icon:"Close",onClick:t=>c.handleReject(e.row)},{default:(0,l.k6)((()=>t[16]||(t[16]=[(0,l.eW)("拒绝")]))),_:2},1032,["onClick"])),[[R,["substitution:application:reject"]]]):(0,l.Q3)("",!0)])),_:1})])),_:1},8,["data","onSelectionChange"])),[[z,p.loading]]),(0,l.bo)((0,l.bF)(S,{total:p.total,page:p.queryParams.pageNum,"onUpdate:page":t[4]||(t[4]=e=>p.queryParams.pageNum=e),limit:p.queryParams.pageSize,"onUpdate:limit":t[5]||(t[5]=e=>p.queryParams.pageSize=e),onPagination:c.getList},null,8,["total","page","limit","onPagination"]),[[i.aG,p.total>0]])])),_:1}),(0,l.bF)(P,{title:"拒绝申请",modelValue:p.rejectOpen,"onUpdate:modelValue":t[7]||(t[7]=e=>p.rejectOpen=e),width:"500px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",r,[(0,l.bF)(d,{onClick:c.cancelReject},{default:(0,l.k6)((()=>t[17]||(t[17]=[(0,l.eW)("取 消")]))),_:1},8,["onClick"]),(0,l.bF)(d,{type:"primary",onClick:c.confirmReject},{default:(0,l.k6)((()=>t[18]||(t[18]=[(0,l.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bF)(k,{model:p.rejectForm,ref:"rejectFormRef",rules:p.rejectRules,"label-width":"80px"},{default:(0,l.k6)((()=>[(0,l.bF)(m,{label:"拒绝理由",prop:"reason"},{default:(0,l.k6)((()=>[(0,l.bF)(b,{type:"textarea",modelValue:p.rejectForm.reason,"onUpdate:modelValue":t[6]||(t[6]=e=>p.rejectForm.reason=e),placeholder:"请输入拒绝理由",rows:4,maxlength:"200","show-word-limit":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])}var p=a(5720);function c(e){return(0,p.A)({url:"/substitution/application/list",method:"get",params:e})}function d(e){return(0,p.A)({url:"/substitution/application/approve/"+e,method:"put"})}function h(e){return(0,p.A)({url:"/substitution/application/reject",method:"put",data:e})}var g=a(9190);const m={class:"right-toolbar"};function b(e,t,a,i,n,o){const s=(0,l.g2)("el-button"),r=(0,l.g2)("el-tooltip"),u=(0,l.g2)("el-row");return(0,l.uX)(),(0,l.CE)("div",m,[(0,l.bF)(u,null,{default:(0,l.k6)((()=>[(0,l.bF)(r,{content:"刷新",placement:"top"},{default:(0,l.k6)((()=>[(0,l.bF)(s,{circle:"",icon:"Refresh",onClick:o.handleRefresh},null,8,["onClick"])])),_:1}),a.search?((0,l.uX)(),(0,l.Wv)(r,{key:0,content:"显示搜索",placement:"top"},{default:(0,l.k6)((()=>[(0,l.bF)(s,{circle:"",icon:"Search",type:a.showSearch?"primary":"",onClick:o.toggleSearch},null,8,["type","onClick"])])),_:1})):(0,l.Q3)("",!0),a.columns?((0,l.uX)(),(0,l.Wv)(r,{key:1,content:"列设置",placement:"top"},{default:(0,l.k6)((()=>[(0,l.bF)(s,{circle:"",icon:"Menu"})])),_:1})):(0,l.Q3)("",!0)])),_:1})])}const f={name:"RightToolbar",props:{showSearch:{type:Boolean,default:!0},columns:{type:Boolean,default:!1},search:{type:Boolean,default:!0}},emits:["update:showSearch","refreshList"],methods:{handleRefresh(){this.$emit("refreshList")},toggleSearch(){this.$emit("update:showSearch",!this.showSearch)}}};var k=a(1241);const y=(0,k.A)(f,[["render",b],["__scopeId","data-v-a939afba"]]),F=y,w={name:"SubstitutionApplication",components:{Pagination:g.A,RightToolbar:F},data(){return{loading:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,applicationList:[],title:"",rejectOpen:!1,rejectForm:{id:null,reason:""},rejectRules:{reason:[{required:!0,message:"拒绝理由不能为空",trigger:"blur"},{min:5,max:200,message:"内容长度在5-200个字符之间",trigger:"blur"}]},statusOptions:[{value:0,label:"待审核"},{value:1,label:"已批准"},{value:2,label:"已拒绝"}],statusLabelMap:{0:"待审核",1:"已批准",2:"已拒绝"},statusTypeMap:{0:"warning",1:"success",2:"danger"},queryParams:{pageNum:1,pageSize:10,status:void 0,applicantName:void 0,substituteTime:[]}}},created(){this.getList()},methods:{getList(){this.loading=!0;const e={...this.queryParams};e.substituteTime&&2===e.substituteTime.length&&(e.startDate=e.substituteTime[0],e.endDate=e.substituteTime[1]),delete e.substituteTime,c(e).then((e=>{this.applicationList=e.data.list||[],this.total=e.data.total,this.loading=!1})).catch((()=>{this.loading=!1}))},refreshList(){this.getList()},resetQuery(){this.queryParams={pageNum:1,pageSize:10,status:void 0,applicantName:void 0,substituteTime:[]},this.handleQuery()},handleQuery(){this.queryParams.pageNum=1,this.getList()},handleSelectionChange(e){this.ids=e.map((e=>e.id)),this.single=1!==e.length,this.multiple=!e.length},handleView(e){this.$router.push({path:`/substitution/application/detail/${e.id}`})},handleApprove(e){const t=e.id||this.ids[0];this.$confirm("确认要批准该申请吗?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>d(t))).then((()=>{this.getList(),this.$message.success("批准成功")})).catch((()=>{}))},handleReject(e){this.rejectForm.id=e.id||this.ids[0],this.rejectForm.reason="",this.rejectOpen=!0},cancelReject(){this.rejectOpen=!1,this.resetRejectForm()},confirmReject(){this.$refs.rejectFormRef.validate((e=>{e&&h(this.rejectForm).then((()=>{this.$message.success("拒绝成功"),this.rejectOpen=!1,this.getList()}))}))},resetRejectForm(){this.$refs.rejectFormRef&&this.$refs.rejectFormRef.resetFields(),this.rejectForm={id:null,reason:""}}}},C=(0,k.A)(w,[["render",u],["__scopeId","data-v-771990f2"]]),v=C}}]);