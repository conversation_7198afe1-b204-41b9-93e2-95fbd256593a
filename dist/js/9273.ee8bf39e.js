"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[9273],{9273:(e,t,a)=>{a.r(t),a.d(t,{default:()=>P});var l=a(6768),o=a(5130),i=a(4232);const r={class:"app-container"},s={class:"filter-container"},n={key:0,class:"margin-top"},d={key:1,class:"margin-top"},u={key:0,class:"reply"},m={class:"dialog-footer"},p={class:"dialog-footer"};function c(e,t,a,c,b,h){const f=(0,l.g2)("el-input"),g=(0,l.g2)("el-form-item"),k=(0,l.g2)("el-option"),y=(0,l.g2)("el-select"),v=(0,l.g2)("el-date-picker"),_=(0,l.g2)("el-button"),F=(0,l.g2)("el-form"),w=(0,l.g2)("el-col"),C=(0,l.g2)("right-toolbar"),D=(0,l.g2)("el-row"),E=(0,l.g2)("el-table-column"),P=(0,l.g2)("el-tag"),V=(0,l.g2)("el-table"),N=(0,l.g2)("pagination"),L=(0,l.g2)("el-tab-pane"),W=(0,l.g2)("el-tabs"),R=(0,l.g2)("el-descriptions-item"),T=(0,l.g2)("el-descriptions"),q=(0,l.g2)("el-card"),I=(0,l.g2)("el-timeline-item"),U=(0,l.g2)("el-timeline"),A=(0,l.g2)("el-dialog"),j=(0,l.g2)("el-input-number"),M=(0,l.gN)("hasPermi"),S=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",r,[(0,l.Lk)("div",s,[(0,l.bF)(F,{inline:!0,model:b.queryParams,class:"form-inline"},{default:(0,l.k6)((()=>[(0,l.bF)(g,{label:"顶班站点"},{default:(0,l.k6)((()=>[(0,l.bF)(f,{modelValue:b.queryParams.stationName,"onUpdate:modelValue":t[0]||(t[0]=e=>b.queryParams.stationName=e),placeholder:"请输入站点名称",clearable:""},null,8,["modelValue"])])),_:1}),(0,l.bF)(g,{label:"申请状态"},{default:(0,l.k6)((()=>[(0,l.bF)(y,{modelValue:b.queryParams.status,"onUpdate:modelValue":t[1]||(t[1]=e=>b.queryParams.status=e),placeholder:"请选择状态",clearable:""},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(b.statusMap,((e,t)=>((0,l.uX)(),(0,l.Wv)(k,{key:t,label:e,value:t},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(g,{label:"顶班类型"},{default:(0,l.k6)((()=>[(0,l.bF)(y,{modelValue:b.queryParams.type,"onUpdate:modelValue":t[2]||(t[2]=e=>b.queryParams.type=e),placeholder:"请选择类型",clearable:""},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(b.typeMap,((e,t)=>((0,l.uX)(),(0,l.Wv)(k,{key:t,label:e,value:t},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(g,{label:"发布时间"},{default:(0,l.k6)((()=>[(0,l.bF)(v,{modelValue:b.dateRange,"onUpdate:modelValue":t[3]||(t[3]=e=>b.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),(0,l.bF)(g,null,{default:(0,l.k6)((()=>[(0,l.bF)(_,{type:"primary",icon:"Search",onClick:h.handleQuery},{default:(0,l.k6)((()=>t[25]||(t[25]=[(0,l.eW)("搜索")]))),_:1},8,["onClick"]),(0,l.bF)(_,{icon:"Refresh",onClick:h.resetQuery},{default:(0,l.k6)((()=>t[26]||(t[26]=[(0,l.eW)("重置")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])]),(0,l.bF)(W,{modelValue:b.activeTab,"onUpdate:modelValue":t[7]||(t[7]=e=>b.activeTab=e),onTabClick:h.handleTabChange},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(b.tabMap,((e,a)=>((0,l.uX)(),(0,l.Wv)(L,{key:a,label:e,name:a},{default:(0,l.k6)((()=>[(0,l.bF)(D,{gutter:10,class:"mb8"},{default:(0,l.k6)((()=>[(0,l.bF)(w,{span:1.5},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(_,{type:"primary",plain:"",icon:"Plus",onClick:h.handleAdd},{default:(0,l.k6)((()=>t[27]||(t[27]=[(0,l.eW)("新增")]))),_:1},8,["onClick"])),[[M,["substitution:add"]]])])),_:1}),(0,l.bF)(w,{span:1.5},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(_,{type:"success",plain:"",icon:"Check",disabled:b.single,onClick:h.handleApprove},{default:(0,l.k6)((()=>t[28]||(t[28]=[(0,l.eW)("审核通过")]))),_:1},8,["disabled","onClick"])),[[M,["substitution:approve"]],[o.aG,"PENDING"===b.activeTab]])])),_:1}),(0,l.bF)(w,{span:1.5},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(_,{type:"danger",plain:"",icon:"Close",disabled:b.single,onClick:h.handleReject},{default:(0,l.k6)((()=>t[29]||(t[29]=[(0,l.eW)("审核拒绝")]))),_:1},8,["disabled","onClick"])),[[M,["substitution:reject"]],[o.aG,"PENDING"===b.activeTab]])])),_:1}),(0,l.bF)(w,{span:1.5},{default:(0,l.k6)((()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(_,{type:"warning",plain:"",icon:"Download",onClick:h.handleExport},{default:(0,l.k6)((()=>t[30]||(t[30]=[(0,l.eW)("导出")]))),_:1},8,["onClick"])),[[M,["substitution:export"]]])])),_:1}),(0,l.bF)(C,{showSearch:b.showSearch,"onUpdate:showSearch":t[4]||(t[4]=e=>b.showSearch=e),onQueryTable:h.getList},null,8,["showSearch","onQueryTable"])])),_:1}),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(V,{data:b.substitutionList,onSelectionChange:h.handleSelectionChange,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(E,{type:"selection",width:"55",align:"center"}),(0,l.bF)(E,{label:"顶班编号",align:"center",prop:"id",width:"120"}),(0,l.bF)(E,{label:"驿站名称",align:"center",prop:"stationName","min-width":"180","show-overflow-tooltip":!0}),(0,l.bF)(E,{label:"顶班类型",align:"center",prop:"type",width:"120"},{default:(0,l.k6)((e=>[(0,l.bF)(P,{type:h.getTypeTag(e.row.type)},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.typeMap[e.row.type]),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(E,{label:"顶班日期",align:"center",width:"180"},{default:(0,l.k6)((e=>[(0,l.eW)((0,i.v_)(e.row.startDate)+" 至 "+(0,i.v_)(e.row.endDate),1)])),_:1}),(0,l.bF)(E,{label:"顶班时间",align:"center",width:"180",prop:"workHours"}),(0,l.bF)(E,{label:"薪资",align:"center",width:"120"},{default:(0,l.k6)((e=>[(0,l.eW)((0,i.v_)(e.row.salary)+"元/"+(0,i.v_)(e.row.salaryUnit),1)])),_:1}),(0,l.bF)(E,{label:"联系人",align:"center",prop:"contactName",width:"100"}),(0,l.bF)(E,{label:"联系电话",align:"center",prop:"contactPhone",width:"120"}),(0,l.bF)(E,{label:"状态",align:"center",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(P,{type:h.getStatusTag(e.row.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.statusMap[e.row.status]),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(E,{label:"申请日期",align:"center",prop:"createTime",width:"180"}),(0,l.bF)(E,{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"220"},{default:(0,l.k6)((e=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(_,{link:"",type:"primary",icon:"View",onClick:t=>h.handleView(e.row)},{default:(0,l.k6)((()=>t[31]||(t[31]=[(0,l.eW)("查看")]))),_:2},1032,["onClick"])),[[M,["substitution:view"]]]),["PENDING","APPROVED","PUBLISHED"].includes(e.row.status)?(0,l.bo)(((0,l.uX)(),(0,l.Wv)(_,{key:0,link:"",type:"primary",icon:"Edit",onClick:t=>h.handleUpdate(e.row)},{default:(0,l.k6)((()=>t[32]||(t[32]=[(0,l.eW)("编辑")]))),_:2},1032,["onClick"])),[[M,["substitution:edit"]]]):(0,l.Q3)("",!0),"PENDING"===e.row.status?(0,l.bo)(((0,l.uX)(),(0,l.Wv)(_,{key:1,link:"",type:"primary",icon:"Check",onClick:t=>h.handleApprove(e.row)},{default:(0,l.k6)((()=>t[33]||(t[33]=[(0,l.eW)("通过")]))),_:2},1032,["onClick"])),[[M,["substitution:approve"]]]):(0,l.Q3)("",!0),"PENDING"===e.row.status?(0,l.bo)(((0,l.uX)(),(0,l.Wv)(_,{key:2,link:"",type:"primary",icon:"Close",onClick:t=>h.handleReject(e.row)},{default:(0,l.k6)((()=>t[34]||(t[34]=[(0,l.eW)("拒绝")]))),_:2},1032,["onClick"])),[[M,["substitution:reject"]]]):(0,l.Q3)("",!0),"ONGOING"===e.row.status?(0,l.bo)(((0,l.uX)(),(0,l.Wv)(_,{key:3,link:"",type:"primary",icon:"CircleCheck",onClick:t=>h.handleMarkCompleted(e.row)},{default:(0,l.k6)((()=>t[35]||(t[35]=[(0,l.eW)("完成")]))),_:2},1032,["onClick"])),[[M,["substitution:complete"]]]):(0,l.Q3)("",!0),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(_,{link:"",type:"primary",icon:"Delete",onClick:t=>h.handleDelete(e.row)},{default:(0,l.k6)((()=>t[36]||(t[36]=[(0,l.eW)("删除")]))),_:2},1032,["onClick"])),[[M,["substitution:delete"]]])])),_:1})])),_:1},8,["data","onSelectionChange"])),[[S,b.loading]]),(0,l.bo)((0,l.bF)(N,{total:b.total,page:b.queryParams.pageNum,"onUpdate:page":t[5]||(t[5]=e=>b.queryParams.pageNum=e),limit:b.queryParams.pageSize,"onUpdate:limit":t[6]||(t[6]=e=>b.queryParams.pageSize=e),onPagination:h.getList},null,8,["total","page","limit","onPagination"]),[[o.aG,b.total>0]])])),_:2},1032,["label","name"])))),128))])),_:1},8,["modelValue","onTabClick"]),(0,l.bF)(A,{title:b.viewTitle,modelValue:b.viewOpen,"onUpdate:modelValue":t[8]||(t[8]=e=>b.viewOpen=e),width:"700px","append-to-body":""},{default:(0,l.k6)((()=>[(0,l.bF)(T,{class:"margin-top",column:2,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(R,{label:"顶班编号"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.id),1)])),_:1}),(0,l.bF)(R,{label:"驿站名称"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.stationName),1)])),_:1}),(0,l.bF)(R,{label:"顶班类型"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.typeMap[b.form.type]),1)])),_:1}),(0,l.bF)(R,{label:"顶班日期"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.startDate)+" 至 "+(0,i.v_)(b.form.endDate),1)])),_:1}),(0,l.bF)(R,{label:"工作时间"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.workHours),1)])),_:1}),(0,l.bF)(R,{label:"薪资"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.salary)+"元/"+(0,i.v_)(b.form.salaryUnit),1)])),_:1}),(0,l.bF)(R,{label:"联系人"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.contactName),1)])),_:1}),(0,l.bF)(R,{label:"联系电话"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.contactPhone),1)])),_:1}),(0,l.bF)(R,{label:"状态"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.statusMap[b.form.status]),1)])),_:1}),(0,l.bF)(R,{label:"申请日期"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.createTime),1)])),_:1}),(0,l.bF)(R,{label:"顶班需求",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.requirements),1)])),_:1}),(0,l.bF)(R,{label:"工作内容",span:2},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.jobDescription),1)])),_:1})])),_:1}),b.form.staffInfo?((0,l.uX)(),(0,l.CE)("div",n,[t[37]||(t[37]=(0,l.Lk)("h4",null,"顶班人员信息",-1)),(0,l.bF)(T,{column:2,border:""},{default:(0,l.k6)((()=>[(0,l.bF)(R,{label:"姓名"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.staffInfo.name),1)])),_:1}),(0,l.bF)(R,{label:"联系电话"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.staffInfo.phone),1)])),_:1}),(0,l.bF)(R,{label:"身份证号"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.staffInfo.idCard),1)])),_:1}),(0,l.bF)(R,{label:"工作经验"},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(b.form.staffInfo.experience),1)])),_:1})])),_:1})])):(0,l.Q3)("",!0),b.form.evaluations&&b.form.evaluations.length>0?((0,l.uX)(),(0,l.CE)("div",d,[t[38]||(t[38]=(0,l.Lk)("h4",null,"服务评价",-1)),(0,l.bF)(U,null,{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(b.form.evaluations,((e,t)=>((0,l.uX)(),(0,l.Wv)(I,{key:t,timestamp:e.createTime},{default:(0,l.k6)((()=>[(0,l.bF)(q,null,{default:(0,l.k6)((()=>[(0,l.Lk)("h4",null,"评分: "+(0,i.v_)(e.rating)+"/5",1),(0,l.Lk)("p",null,(0,i.v_)(e.comment),1),e.reply?((0,l.uX)(),(0,l.CE)("p",u,"回复: "+(0,i.v_)(e.reply),1)):(0,l.Q3)("",!0)])),_:2},1024)])),_:2},1032,["timestamp"])))),128))])),_:1})])):(0,l.Q3)("",!0)])),_:1},8,["title","modelValue"]),(0,l.bF)(A,{title:b.dialog.title,modelValue:b.dialog.open,"onUpdate:modelValue":t[21]||(t[21]=e=>b.dialog.open=e),width:"700px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",m,[(0,l.bF)(_,{onClick:t[20]||(t[20]=e=>b.dialog.open=!1)},{default:(0,l.k6)((()=>t[39]||(t[39]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(_,{type:"primary",onClick:h.submitForm},{default:(0,l.k6)((()=>t[40]||(t[40]=[(0,l.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bF)(F,{ref:"formRef",model:b.form,rules:b.rules,"label-width":"100px"},{default:(0,l.k6)((()=>[(0,l.bF)(g,{label:"驿站名称",prop:"stationName"},{default:(0,l.k6)((()=>[(0,l.bF)(f,{modelValue:b.form.stationName,"onUpdate:modelValue":t[9]||(t[9]=e=>b.form.stationName=e),placeholder:"请输入驿站名称"},null,8,["modelValue"])])),_:1}),(0,l.bF)(g,{label:"顶班类型",prop:"type"},{default:(0,l.k6)((()=>[(0,l.bF)(y,{modelValue:b.form.type,"onUpdate:modelValue":t[10]||(t[10]=e=>b.form.type=e),placeholder:"请选择顶班类型",style:{width:"100%"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(b.typeMap,((e,t)=>((0,l.uX)(),(0,l.Wv)(k,{key:t,label:e,value:t},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(g,{label:"顶班日期",prop:"dateRange"},{default:(0,l.k6)((()=>[(0,l.bF)(v,{modelValue:b.form.dateRange,"onUpdate:modelValue":t[11]||(t[11]=e=>b.form.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),(0,l.bF)(g,{label:"工作时间",prop:"workHours"},{default:(0,l.k6)((()=>[(0,l.bF)(f,{modelValue:b.form.workHours,"onUpdate:modelValue":t[12]||(t[12]=e=>b.form.workHours=e),placeholder:"例如：09:00-18:00"},null,8,["modelValue"])])),_:1}),(0,l.bF)(g,{label:"薪资",prop:"salary"},{default:(0,l.k6)((()=>[(0,l.bF)(j,{modelValue:b.form.salary,"onUpdate:modelValue":t[13]||(t[13]=e=>b.form.salary=e),min:0,precision:2,style:{width:"70%"}},null,8,["modelValue"]),(0,l.bF)(y,{modelValue:b.form.salaryUnit,"onUpdate:modelValue":t[14]||(t[14]=e=>b.form.salaryUnit=e),style:{width:"28%","margin-left":"2%"}},{default:(0,l.k6)((()=>[(0,l.bF)(k,{label:"天",value:"天"}),(0,l.bF)(k,{label:"小时",value:"小时"})])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(g,{label:"联系人",prop:"contactName"},{default:(0,l.k6)((()=>[(0,l.bF)(f,{modelValue:b.form.contactName,"onUpdate:modelValue":t[15]||(t[15]=e=>b.form.contactName=e),placeholder:"请输入联系人姓名"},null,8,["modelValue"])])),_:1}),(0,l.bF)(g,{label:"联系电话",prop:"contactPhone"},{default:(0,l.k6)((()=>[(0,l.bF)(f,{modelValue:b.form.contactPhone,"onUpdate:modelValue":t[16]||(t[16]=e=>b.form.contactPhone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])])),_:1}),(0,l.bF)(g,{label:"顶班需求",prop:"requirements"},{default:(0,l.k6)((()=>[(0,l.bF)(f,{modelValue:b.form.requirements,"onUpdate:modelValue":t[17]||(t[17]=e=>b.form.requirements=e),type:"textarea",rows:"3",placeholder:"请输入顶班需求，如：经验要求、技能要求等"},null,8,["modelValue"])])),_:1}),(0,l.bF)(g,{label:"工作内容",prop:"jobDescription"},{default:(0,l.k6)((()=>[(0,l.bF)(f,{modelValue:b.form.jobDescription,"onUpdate:modelValue":t[18]||(t[18]=e=>b.form.jobDescription=e),type:"textarea",rows:"4",placeholder:"请输入工作内容详情"},null,8,["modelValue"])])),_:1}),"edit"===b.dialog.type?((0,l.uX)(),(0,l.Wv)(g,{key:0,label:"状态",prop:"status"},{default:(0,l.k6)((()=>[(0,l.bF)(y,{modelValue:b.form.status,"onUpdate:modelValue":t[19]||(t[19]=e=>b.form.status=e),placeholder:"请选择状态",style:{width:"100%"}},{default:(0,l.k6)((()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(b.editableStatusMap,((e,t)=>((0,l.uX)(),(0,l.Wv)(k,{key:t,label:e,value:t},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})):(0,l.Q3)("",!0)])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"]),(0,l.bF)(A,{title:"审核拒绝",modelValue:b.rejectDialog.open,"onUpdate:modelValue":t[24]||(t[24]=e=>b.rejectDialog.open=e),width:"500px","append-to-body":""},{footer:(0,l.k6)((()=>[(0,l.Lk)("div",p,[(0,l.bF)(_,{onClick:t[23]||(t[23]=e=>b.rejectDialog.open=!1)},{default:(0,l.k6)((()=>t[41]||(t[41]=[(0,l.eW)("取 消")]))),_:1}),(0,l.bF)(_,{type:"primary",onClick:h.confirmReject},{default:(0,l.k6)((()=>t[42]||(t[42]=[(0,l.eW)("确 认")]))),_:1},8,["onClick"])])])),default:(0,l.k6)((()=>[(0,l.bF)(F,{ref:"rejectFormRef",model:b.rejectForm,rules:b.rejectRules},{default:(0,l.k6)((()=>[(0,l.bF)(g,{label:"拒绝原因",prop:"reason","label-width":"100px"},{default:(0,l.k6)((()=>[(0,l.bF)(f,{modelValue:b.rejectForm.reason,"onUpdate:modelValue":t[22]||(t[22]=e=>b.rejectForm.reason=e),type:"textarea",rows:"4",placeholder:"请输入拒绝原因"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])}var b=a(5720);function h(e){return(0,b.A)({url:"/substitution/list",method:"get",params:e})}function f(e){return(0,b.A)({url:`/substitution/${e}`,method:"get"})}function g(e){return(0,b.A)({url:"/substitution",method:"post",data:e})}function k(e){return(0,b.A)({url:`/substitution/${e.id}`,method:"put",data:e})}function y(e){return(0,b.A)({url:`/substitution/${e}`,method:"delete"})}function v(e){return(0,b.A)({url:`/substitution/approve/${e}`,method:"put"})}function _(e,t){return(0,b.A)({url:`/substitution/reject/${e}`,method:"put",data:t})}function F(e){return(0,b.A)({url:`/substitution/complete/${e}`,method:"put"})}function w(e){return(0,b.A)({url:"/substitution/export",method:"get",params:e,responseType:"blob"})}const C={name:"SubstitutionList",data(){return{loading:!1,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,substitutionList:[],title:"",open:!1,viewTitle:"查看顶班信息",viewOpen:!1,dateRange:[],activeTab:"ALL",dialog:{open:!1,title:"",type:"add"},rejectDialog:{open:!1},rejectForm:{id:null,reason:""},rejectRules:{reason:[{required:!0,message:"请输入拒绝原因",trigger:"blur"}]},statusMap:{PENDING:"待审核",APPROVED:"已审核",REJECTED:"已拒绝",PUBLISHED:"已发布",ONGOING:"进行中",COMPLETED:"已完成",CANCELLED:"已取消"},editableStatusMap:{PENDING:"待审核",APPROVED:"已审核",PUBLISHED:"已发布",CANCELLED:"已取消"},typeMap:{FULL_TIME:"全职顶班",PART_TIME:"兼职顶班",TEMPORARY:"临时顶班",EMERGENCY:"紧急顶班"},tabMap:{ALL:"全部",PENDING:"待审核",APPROVED:"已审核",PUBLISHED:"已发布",ONGOING:"进行中",COMPLETED:"已完成",REJECTED:"已拒绝",CANCELLED:"已取消"},queryParams:{pageNum:1,pageSize:10,stationName:void 0,status:void 0,type:void 0,startDate:void 0,endDate:void 0},form:{id:void 0,stationName:void 0,type:"TEMPORARY",dateRange:[],startDate:void 0,endDate:void 0,workHours:void 0,salary:0,salaryUnit:"天",contactName:void 0,contactPhone:void 0,requirements:void 0,jobDescription:void 0,status:"PENDING",staffInfo:null,evaluations:[]},rules:{stationName:[{required:!0,message:"驿站名称不能为空",trigger:"blur"}],type:[{required:!0,message:"顶班类型不能为空",trigger:"change"}],dateRange:[{required:!0,message:"顶班日期不能为空",trigger:"change"}],workHours:[{required:!0,message:"工作时间不能为空",trigger:"blur"}],salary:[{required:!0,message:"薪资不能为空",trigger:"blur"}],contactName:[{required:!0,message:"联系人不能为空",trigger:"blur"}],contactPhone:[{required:!0,message:"联系电话不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],requirements:[{required:!0,message:"顶班需求不能为空",trigger:"blur"}],jobDescription:[{required:!0,message:"工作内容不能为空",trigger:"blur"}]}}},created(){this.getList()},methods:{getList(){this.loading=!0,this.dateRange&&this.dateRange.length>0?(this.queryParams.startDate=this.dateRange[0],this.queryParams.endDate=this.dateRange[1]):(this.queryParams.startDate=void 0,this.queryParams.endDate=void 0),"ALL"!==this.activeTab&&(this.queryParams.status=this.activeTab),h(this.queryParams).then((e=>{this.substitutionList=e.rows,this.total=e.total,this.loading=!1}))},resetQuery(){this.dateRange=[],this.resetForm("queryForm"),this.queryParams={pageNum:1,pageSize:10,stationName:void 0,status:void 0,type:void 0,startDate:void 0,endDate:void 0},"ALL"!==this.activeTab&&(this.queryParams.status=this.activeTab),this.handleQuery()},handleQuery(){this.queryParams.pageNum=1,this.getList()},handleTabChange(e){this.activeTab=e.props.name,"ALL"===this.activeTab?this.queryParams.status=void 0:this.queryParams.status=this.activeTab,this.resetQuery()},handleSelectionChange(e){this.ids=e.map((e=>e.id)),this.single=1!==e.length,this.multiple=!e.length},getStatusTag(e){const t={PENDING:"info",APPROVED:"success",REJECTED:"danger",PUBLISHED:"primary",ONGOING:"warning",COMPLETED:"success",CANCELLED:"info"};return t[e]||"info"},getTypeTag(e){const t={FULL_TIME:"primary",PART_TIME:"success",TEMPORARY:"warning",EMERGENCY:"danger"};return t[e]||"info"},handleAdd(){this.resetForm(),this.dialog.open=!0,this.dialog.title="添加顶班信息",this.dialog.type="add",this.form.status="PENDING",this.form.type="TEMPORARY",this.form.salary=0,this.form.salaryUnit="天"},handleUpdate(e){this.resetForm();const t=e.id||this.ids[0];f(t).then((e=>{this.form=e.data,this.form.dateRange=[this.form.startDate,this.form.endDate],this.dialog.open=!0,this.dialog.title="修改顶班信息",this.dialog.type="edit"}))},handleView(e){f(e.id).then((e=>{this.form=e.data,this.viewOpen=!0}))},submitForm(){this.$refs.formRef.validate((e=>{e&&(this.form.dateRange&&this.form.dateRange.length>0&&(this.form.startDate=this.form.dateRange[0],this.form.endDate=this.form.dateRange[1]),this.form.id?k(this.form).then((e=>{this.$modal.msgSuccess("修改成功"),this.dialog.open=!1,this.getList()})):g(this.form).then((e=>{this.$modal.msgSuccess("新增成功"),this.dialog.open=!1,this.getList()})))}))},handleApprove(e){const t=e.id||this.ids;this.$modal.confirm('是否确认审核通过顶班申请编号为"'+t+'"的数据项？').then((()=>v(t))).then((()=>{this.getList(),this.$modal.msgSuccess("审核通过成功")})).catch((()=>{}))},handleReject(e){this.rejectForm.id=e.id||this.ids[0],this.rejectForm.reason="",this.rejectDialog.open=!0},confirmReject(){this.$refs.rejectFormRef.validate((e=>{e&&_(this.rejectForm.id,this.rejectForm).then((e=>{this.$modal.msgSuccess("审核拒绝成功"),this.rejectDialog.open=!1,this.getList()}))}))},handleMarkCompleted(e){const t=e.id;this.$modal.confirm('是否确认将顶班申请编号为"'+t+'"标记为已完成？').then((()=>F(t))).then((()=>{this.getList(),this.$modal.msgSuccess("操作成功")})).catch((()=>{}))},handleDelete(e){const t=e.id||this.ids;this.$modal.confirm('是否确认删除顶班申请编号为"'+t+'"的数据项？').then((()=>y(t))).then((()=>{this.getList(),this.$modal.msgSuccess("删除成功")})).catch((()=>{}))},handleExport(){this.$modal.confirm("是否确认导出所有顶班数据项？").then((()=>(this.exportLoading=!0,w(this.queryParams)))).then((e=>{this.$download.excel(e,"顶班数据.xlsx"),this.exportLoading=!1})).catch((()=>{}))},resetForm(){this.form={id:void 0,stationName:void 0,type:"TEMPORARY",dateRange:[],startDate:void 0,endDate:void 0,workHours:void 0,salary:0,salaryUnit:"天",contactName:void 0,contactPhone:void 0,requirements:void 0,jobDescription:void 0,status:"PENDING",staffInfo:null,evaluations:[]}}}};var D=a(1241);const E=(0,D.A)(C,[["render",c],["__scopeId","data-v-7dfd79dc"]]),P=E}}]);