"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[9641],{9641:(e,a,l)=>{l.r(a),l.d(a,{default:()=>b});var t=l(6768),s=l(4232);const i={class:"app-container"},n={slot:"header",class:"clearfix"};function r(e,a,l,r,d,u){const o=(0,t.g2)("el-card"),b=(0,t.g2)("el-col"),c=(0,t.g2)("el-row"),k=(0,t.g2)("el-radio-button"),p=(0,t.g2)("el-radio-group"),m=(0,t.g2)("el-table-column"),h=(0,t.g2)("el-button"),v=(0,t.g2)("el-table"),f=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",i,[(0,t.bF)(c,{gutter:20},{default:(0,t.k6)((()=>[(0,t.bF)(b,{span:6},{default:(0,t.k6)((()=>[(0,t.bF)(o,{class:"box-card"},{default:(0,t.k6)((()=>a[1]||(a[1]=[(0,t.Lk)("div",{slot:"header",class:"clearfix"},[(0,t.Lk)("span",null,"今日发布职位")],-1),(0,t.Lk)("div",{class:"card-item"},[(0,t.Lk)("div",{class:"card-item-number"},"15"),(0,t.Lk)("div",{class:"card-item-text"},[(0,t.eW)("较昨日"),(0,t.Lk)("span",{class:"up"},"↑10.5%")])],-1)]))),_:1})])),_:1}),(0,t.bF)(b,{span:6},{default:(0,t.k6)((()=>[(0,t.bF)(o,{class:"box-card"},{default:(0,t.k6)((()=>a[2]||(a[2]=[(0,t.Lk)("div",{slot:"header",class:"clearfix"},[(0,t.Lk)("span",null,"有效职位")],-1),(0,t.Lk)("div",{class:"card-item"},[(0,t.Lk)("div",{class:"card-item-number"},"125"),(0,t.Lk)("div",{class:"card-item-text"},[(0,t.eW)("较上周"),(0,t.Lk)("span",{class:"up"},"↑5.2%")])],-1)]))),_:1})])),_:1}),(0,t.bF)(b,{span:6},{default:(0,t.k6)((()=>[(0,t.bF)(o,{class:"box-card"},{default:(0,t.k6)((()=>a[3]||(a[3]=[(0,t.Lk)("div",{slot:"header",class:"clearfix"},[(0,t.Lk)("span",null,"今日投递简历")],-1),(0,t.Lk)("div",{class:"card-item"},[(0,t.Lk)("div",{class:"card-item-number"},"68"),(0,t.Lk)("div",{class:"card-item-text"},[(0,t.eW)("较昨日"),(0,t.Lk)("span",{class:"down"},"↓3.8%")])],-1)]))),_:1})])),_:1}),(0,t.bF)(b,{span:6},{default:(0,t.k6)((()=>[(0,t.bF)(o,{class:"box-card"},{default:(0,t.k6)((()=>a[4]||(a[4]=[(0,t.Lk)("div",{slot:"header",class:"clearfix"},[(0,t.Lk)("span",null,"本月成功招聘")],-1),(0,t.Lk)("div",{class:"card-item"},[(0,t.Lk)("div",{class:"card-item-number"},"42"),(0,t.Lk)("div",{class:"card-item-text"},[(0,t.eW)("较上月"),(0,t.Lk)("span",{class:"up"},"↑12.7%")])],-1)]))),_:1})])),_:1})])),_:1}),(0,t.bF)(c,{gutter:20,class:"mt20"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(o,{class:"box-card"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",n,[a[8]||(a[8]=(0,t.Lk)("span",null,"职位发布趋势",-1)),(0,t.bF)(p,{modelValue:d.jobTrendTimeType,"onUpdate:modelValue":a[0]||(a[0]=e=>d.jobTrendTimeType=e),size:"mini",style:{float:"right"}},{default:(0,t.k6)((()=>[(0,t.bF)(k,{label:"week"},{default:(0,t.k6)((()=>a[5]||(a[5]=[(0,t.eW)("本周")]))),_:1}),(0,t.bF)(k,{label:"month"},{default:(0,t.k6)((()=>a[6]||(a[6]=[(0,t.eW)("本月")]))),_:1}),(0,t.bF)(k,{label:"year"},{default:(0,t.k6)((()=>a[7]||(a[7]=[(0,t.eW)("全年")]))),_:1})])),_:1},8,["modelValue"])]),a[9]||(a[9]=(0,t.Lk)("div",{class:"chart-container"},[(0,t.Lk)("div",{id:"jobTrendChart",style:{height:"300px"}})],-1))])),_:1})])),_:1}),(0,t.bF)(b,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(o,{class:"box-card"},{default:(0,t.k6)((()=>a[10]||(a[10]=[(0,t.Lk)("div",{slot:"header",class:"clearfix"},[(0,t.Lk)("span",null,"职位分类占比")],-1),(0,t.Lk)("div",{class:"chart-container"},[(0,t.Lk)("div",{id:"jobCategoryChart",style:{height:"300px"}})],-1)]))),_:1})])),_:1})])),_:1}),(0,t.bF)(c,{gutter:20,class:"mt20"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{span:24},{default:(0,t.k6)((()=>[(0,t.bF)(o,{class:"box-card"},{default:(0,t.k6)((()=>[a[12]||(a[12]=(0,t.Lk)("div",{slot:"header",class:"clearfix"},[(0,t.Lk)("span",null,"热门职位TOP10")],-1)),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(v,{data:d.hotJobsList,style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(m,{prop:"rank",label:"排名",width:"80",align:"center"}),(0,t.bF)(m,{prop:"jobTitle",label:"职位名称"}),(0,t.bF)(m,{prop:"resumeCount",label:"简历数",align:"center"}),(0,t.bF)(m,{prop:"viewCount",label:"浏览次数",align:"center"}),(0,t.bF)(m,{prop:"publishDays",label:"发布天数",align:"center"}),(0,t.bF)(m,{label:"简历/天",align:"center"},{default:(0,t.k6)((e=>[(0,t.eW)((0,s.v_)((e.row.resumeCount/e.row.publishDays).toFixed(1)),1)])),_:1}),(0,t.bF)(m,{label:"操作",align:"center",width:"120"},{default:(0,t.k6)((e=>[(0,t.bF)(h,{type:"text",onClick:a=>u.handleViewJob(e.row)},{default:(0,t.k6)((()=>a[11]||(a[11]=[(0,t.eW)("查看详情")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[f,d.loading]])])),_:1})])),_:1})])),_:1})])}const d={name:"RecruitmentStatistics",data(){return{loading:!1,jobTrendTimeType:"month",hotJobsList:[{rank:1,jobTitle:"驿站店长",resumeCount:120,viewCount:860,publishDays:15},{rank:2,jobTitle:"快递配送员",resumeCount:98,viewCount:750,publishDays:12},{rank:3,jobTitle:"驿站营业员",resumeCount:85,viewCount:620,publishDays:10},{rank:4,jobTitle:"分拣员",resumeCount:76,viewCount:560,publishDays:8},{rank:5,jobTitle:"站点运营经理",resumeCount:68,viewCount:480,publishDays:20},{rank:6,jobTitle:"驿站客服",resumeCount:52,viewCount:430,publishDays:7},{rank:7,jobTitle:"快递驾驶员",resumeCount:45,viewCount:380,publishDays:5},{rank:8,jobTitle:"仓库管理员",resumeCount:38,viewCount:320,publishDays:6},{rank:9,jobTitle:"区域配送主管",resumeCount:32,viewCount:290,publishDays:9},{rank:10,jobTitle:"夜班理货员",resumeCount:28,viewCount:250,publishDays:4}]}},mounted(){this.initCharts()},methods:{initCharts(){console.log("初始化图表")},handleViewJob(e){this.$router.push(`/recruitment/detail/${e.id||1}`)}}};var u=l(1241);const o=(0,u.A)(d,[["render",r],["__scopeId","data-v-464b0f66"]]),b=o}}]);