"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[4664],{4664:(e,a,t)=>{t.r(a),t.d(a,{default:()=>b});var l=t(6768),i=t(4232);const n={class:"user-list-container"},r={class:"filter-container"},o={class:"table-container"},s={class:"user-info"},c={class:"user-details"},d={class:"nickname"},h={class:"phone"},g={class:"pagination-container"};function u(e,a,t,u,p,m){const k=(0,l.g2)("el-input"),b=(0,l.g2)("el-form-item"),F=(0,l.g2)("el-option"),f=(0,l.g2)("el-select"),w=(0,l.g2)("el-date-picker"),C=(0,l.g2)("el-button"),v=(0,l.g2)("el-form"),_=(0,l.g2)("el-table-column"),L=(0,l.g2)("el-avatar"),S=(0,l.g2)("el-tag"),y=(0,l.g2)("el-switch"),U=(0,l.g2)("el-table"),z=(0,l.g2)("el-pagination"),V=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",n,[a[10]||(a[10]=(0,l.Lk)("div",{class:"page-header"},[(0,l.Lk)("h2",null,[(0,l.Lk)("i",{class:"el-icon-user-solid"}),(0,l.eW)(" 用户管理")]),(0,l.Lk)("p",null,"管理系统中的所有用户信息")],-1)),(0,l.Lk)("div",r,[(0,l.bF)(v,{inline:!0,model:p.searchForm,class:"search-form"},{default:(0,l.k6)((()=>[(0,l.bF)(b,{label:"用户手机号"},{default:(0,l.k6)((()=>[(0,l.bF)(k,{modelValue:p.searchForm.phone,"onUpdate:modelValue":a[0]||(a[0]=e=>p.searchForm.phone=e),placeholder:"请输入手机号",clearable:""},null,8,["modelValue"])])),_:1}),(0,l.bF)(b,{label:"注册来源"},{default:(0,l.k6)((()=>[(0,l.bF)(f,{modelValue:p.searchForm.registerSource,"onUpdate:modelValue":a[1]||(a[1]=e=>p.searchForm.registerSource=e),placeholder:"请选择",clearable:""},{default:(0,l.k6)((()=>[(0,l.bF)(F,{label:"小程序",value:"miniprogram"}),(0,l.bF)(F,{label:"H5",value:"h5"}),(0,l.bF)(F,{label:"APP",value:"app"})])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(b,{label:"注册时间"},{default:(0,l.k6)((()=>[(0,l.bF)(w,{modelValue:p.searchForm.dateRange,"onUpdate:modelValue":a[2]||(a[2]=e=>p.searchForm.dateRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),(0,l.bF)(b,null,{default:(0,l.k6)((()=>[(0,l.bF)(C,{type:"primary",onClick:m.handleSearch},{default:(0,l.k6)((()=>a[5]||(a[5]=[(0,l.Lk)("i",{class:"el-icon-search"},null,-1),(0,l.eW)(" 搜索 ")]))),_:1},8,["onClick"]),(0,l.bF)(C,{onClick:m.handleReset},{default:(0,l.k6)((()=>a[6]||(a[6]=[(0,l.Lk)("i",{class:"el-icon-refresh"},null,-1),(0,l.eW)(" 重置 ")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])]),(0,l.Lk)("div",o,[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(U,{data:p.userList,style:{width:"100%"},onSelectionChange:m.handleSelectionChange},{default:(0,l.k6)((()=>[(0,l.bF)(_,{type:"selection",width:"55"}),(0,l.bF)(_,{prop:"id",label:"用户ID",width:"80"}),(0,l.bF)(_,{label:"用户信息","min-width":"200"},{default:(0,l.k6)((e=>[(0,l.Lk)("div",s,[(0,l.bF)(L,{src:e.row.avatarUrl,size:40},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(e.row.nickName?e.row.nickName.charAt(0):"用"),1)])),_:2},1032,["src"]),(0,l.Lk)("div",c,[(0,l.Lk)("div",d,(0,i.v_)(e.row.nickName||"未设置"),1),(0,l.Lk)("div",h,(0,i.v_)(e.row.phone),1)])])])),_:1}),(0,l.bF)(_,{prop:"registerSource",label:"注册来源",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(S,{type:m.getSourceType(e.row.registerSource)},{default:(0,l.k6)((()=>[(0,l.eW)((0,i.v_)(m.getSourceText(e.row.registerSource)),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(_,{prop:"loginCount",label:"登录次数",width:"100"}),(0,l.bF)(_,{prop:"lastLoginTime",label:"最后登录",width:"160"},{default:(0,l.k6)((e=>[(0,l.eW)((0,i.v_)(m.formatTime(e.row.lastLoginTime)),1)])),_:1}),(0,l.bF)(_,{prop:"createdAt",label:"注册时间",width:"160"},{default:(0,l.k6)((e=>[(0,l.eW)((0,i.v_)(m.formatTime(e.row.createdAt)),1)])),_:1}),(0,l.bF)(_,{label:"状态",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(y,{modelValue:e.row.isActive,"onUpdate:modelValue":a=>e.row.isActive=a,onChange:a=>m.handleStatusChange(e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),(0,l.bF)(_,{label:"操作",width:"200",fixed:"right"},{default:(0,l.k6)((e=>[(0,l.bF)(C,{type:"text",size:"small",onClick:a=>m.handleView(e.row)},{default:(0,l.k6)((()=>a[7]||(a[7]=[(0,l.Lk)("i",{class:"el-icon-view"},null,-1),(0,l.eW)(" 查看 ")]))),_:2},1032,["onClick"]),(0,l.bF)(C,{type:"text",size:"small",onClick:a=>m.handleEdit(e.row)},{default:(0,l.k6)((()=>a[8]||(a[8]=[(0,l.Lk)("i",{class:"el-icon-edit"},null,-1),(0,l.eW)(" 编辑 ")]))),_:2},1032,["onClick"]),(0,l.bF)(C,{type:"text",size:"small",class:"danger",onClick:a=>m.handleDelete(e.row)},{default:(0,l.k6)((()=>a[9]||(a[9]=[(0,l.Lk)("i",{class:"el-icon-delete"},null,-1),(0,l.eW)(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data","onSelectionChange"])),[[V,p.loading]]),(0,l.Lk)("div",g,[(0,l.bF)(z,{"current-page":p.pagination.page,"onUpdate:currentPage":a[3]||(a[3]=e=>p.pagination.page=e),"page-size":p.pagination.limit,"onUpdate:pageSize":a[4]||(a[4]=e=>p.pagination.limit=e),"page-sizes":[10,20,50,100],total:p.pagination.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:m.handleSizeChange,onCurrentChange:m.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])])])}const p={name:"UserList",data(){return{loading:!1,userList:[],selectedUsers:[],searchForm:{phone:"",registerSource:"",dateRange:[]},pagination:{page:1,limit:20,total:0}}},mounted(){this.loadUserList()},methods:{async loadUserList(){this.loading=!0;try{setTimeout((()=>{this.userList=[{id:1,phone:"13800138001",nickName:"张三",avatarUrl:"",registerSource:"miniprogram",loginCount:15,lastLoginTime:new Date,createdAt:new Date,isActive:!0},{id:2,phone:"13800138002",nickName:"李四",avatarUrl:"",registerSource:"h5",loginCount:8,lastLoginTime:new Date,createdAt:new Date,isActive:!0}],this.pagination.total=2,this.loading=!1}),1e3)}catch(e){this.$message.error("加载用户列表失败"),this.loading=!1}},getSearchParams(){const e={page:this.pagination.page,limit:this.pagination.limit};return this.searchForm.phone&&(e.phone=this.searchForm.phone),this.searchForm.registerSource&&(e.registerSource=this.searchForm.registerSource),this.searchForm.dateRange&&2===this.searchForm.dateRange.length&&(e.startDate=this.searchForm.dateRange[0],e.endDate=this.searchForm.dateRange[1]),e},handleSearch(){this.pagination.page=1,this.loadUserList()},handleReset(){this.searchForm={phone:"",registerSource:"",dateRange:[]},this.pagination.page=1,this.loadUserList()},handleSelectionChange(e){this.selectedUsers=e},handleStatusChange(e){this.$message.success("用户状态已"+(e.isActive?"启用":"禁用"))},handleView(e){this.$message.info("查看用户详情功能开发中")},handleEdit(e){this.$message.info("编辑用户功能开发中")},handleDelete(e){this.$confirm("确定要删除这个用户吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$message.success("删除成功")}))},handleSizeChange(e){this.pagination.limit=e,this.loadUserList()},handleCurrentChange(e){this.pagination.page=e,this.loadUserList()},getSourceType(e){const a={miniprogram:"primary",h5:"success",app:"warning"};return a[e]||"info"},getSourceText(e){const a={miniprogram:"小程序",h5:"H5",app:"APP"};return a[e]||"未知"},formatTime(e){return e?new Date(e).toLocaleString("zh-CN"):"-"}}};var m=t(1241);const k=(0,m.A)(p,[["render",u],["__scopeId","data-v-73472bce"]]),b=k}}]);