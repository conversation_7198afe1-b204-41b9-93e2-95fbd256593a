"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[1196],{1196:(e,a,l)=>{l.r(a),l.d(a,{default:()=>c});var t=l(6768),o=l(5130),s=l(4232);const n={class:"app-container"},r={class:"el-dropdown-link"},d={class:"dialog-footer"};function u(e,a,l,u,i,m){const p=(0,t.g2)("el-input"),h=(0,t.g2)("el-form-item"),c=(0,t.g2)("el-option"),b=(0,t.g2)("el-select"),g=(0,t.g2)("el-date-picker"),f=(0,t.g2)("el-button"),k=(0,t.g2)("el-form"),F=(0,t.g2)("el-col"),y=(0,t.g2)("right-toolbar"),w=(0,t.g2)("el-row"),_=(0,t.g2)("el-table-column"),v=(0,t.g2)("el-switch"),V=(0,t.g2)("el-dropdown-item"),C=(0,t.g2)("el-dropdown-menu"),P=(0,t.g2)("el-dropdown"),U=(0,t.g2)("el-table"),q=(0,t.g2)("pagination"),x=(0,t.g2)("el-radio"),S=(0,t.g2)("el-radio-group"),W=(0,t.g2)("el-dialog"),R=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",n,[(0,t.bo)((0,t.bF)(k,{model:i.queryParams,ref:"queryForm",inline:!0},{default:(0,t.k6)((()=>[(0,t.bF)(h,{label:"用户名称",prop:"username"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{modelValue:i.queryParams.username,"onUpdate:modelValue":a[0]||(a[0]=e=>i.queryParams.username=e),placeholder:"请输入用户名称",clearable:"",style:{width:"200px"},onKeyup:(0,o.jR)(m.handleQuery,["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),(0,t.bF)(h,{label:"手机号码",prop:"mobile"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{modelValue:i.queryParams.mobile,"onUpdate:modelValue":a[1]||(a[1]=e=>i.queryParams.mobile=e),placeholder:"请输入手机号码",clearable:"",style:{width:"200px"},onKeyup:(0,o.jR)(m.handleQuery,["enter"])},null,8,["modelValue","onKeyup"])])),_:1}),(0,t.bF)(h,{label:"用户状态",prop:"status"},{default:(0,t.k6)((()=>[(0,t.bF)(b,{modelValue:i.queryParams.status,"onUpdate:modelValue":a[2]||(a[2]=e=>i.queryParams.status=e),placeholder:"用户状态",clearable:"",style:{width:"200px"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(i.statusOptions,(e=>((0,t.uX)(),(0,t.Wv)(c,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(h,{label:"创建时间"},{default:(0,t.k6)((()=>[(0,t.bF)(g,{modelValue:i.dateRange,"onUpdate:modelValue":a[3]||(a[3]=e=>i.dateRange=e),style:{width:"300px"},"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])])),_:1}),(0,t.bF)(h,null,{default:(0,t.k6)((()=>[(0,t.bF)(f,{type:"primary",icon:"Search",onClick:m.handleQuery},{default:(0,t.k6)((()=>a[14]||(a[14]=[(0,t.eW)("搜索")]))),_:1},8,["onClick"]),(0,t.bF)(f,{icon:"Refresh",onClick:m.resetQuery},{default:(0,t.k6)((()=>a[15]||(a[15]=[(0,t.eW)("重置")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"]),[[o.aG,i.showSearch]]),(0,t.bF)(w,{gutter:10,class:"mb8"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{span:1.5},{default:(0,t.k6)((()=>[(0,t.bF)(f,{type:"primary",plain:"",icon:"Plus",onClick:m.handleAdd},{default:(0,t.k6)((()=>a[16]||(a[16]=[(0,t.eW)("新增")]))),_:1},8,["onClick"])])),_:1}),(0,t.bF)(F,{span:1.5},{default:(0,t.k6)((()=>[(0,t.bF)(f,{type:"success",plain:"",icon:"Edit",disabled:i.single,onClick:m.handleUpdate},{default:(0,t.k6)((()=>a[17]||(a[17]=[(0,t.eW)("修改")]))),_:1},8,["disabled","onClick"])])),_:1}),(0,t.bF)(F,{span:1.5},{default:(0,t.k6)((()=>[(0,t.bF)(f,{type:"danger",plain:"",icon:"Delete",disabled:i.multiple,onClick:m.handleDelete},{default:(0,t.k6)((()=>a[18]||(a[18]=[(0,t.eW)("删除")]))),_:1},8,["disabled","onClick"])])),_:1}),(0,t.bF)(y,{showSearch:i.showSearch,"onUpdate:showSearch":a[4]||(a[4]=e=>i.showSearch=e),onRefreshData:m.getList},null,8,["showSearch","onRefreshData"])])),_:1}),(0,t.bo)(((0,t.uX)(),(0,t.Wv)(U,{data:i.userList,onSelectionChange:m.handleSelectionChange},{default:(0,t.k6)((()=>[(0,t.bF)(_,{type:"selection",width:"50",align:"center"}),(0,t.bF)(_,{label:"用户编号",align:"center",prop:"userId"}),(0,t.bF)(_,{label:"用户名称",align:"center",prop:"username"}),(0,t.bF)(_,{label:"用户昵称",align:"center",prop:"nickname","show-overflow-tooltip":!0}),(0,t.bF)(_,{label:"手机号码",align:"center",prop:"mobile",width:"120"}),(0,t.bF)(_,{label:"用户状态",align:"center"},{default:(0,t.k6)((e=>[(0,t.bF)(v,{modelValue:e.row.status,"onUpdate:modelValue":a=>e.row.status=a,"active-value":"0","inactive-value":"1",onChange:a=>m.handleStatusChange(e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),(0,t.bF)(_,{label:"创建时间",align:"center",prop:"createTime",width:"160"}),(0,t.bF)(_,{label:"操作",align:"center",width:"180","class-name":"small-padding fixed-width"},{default:(0,t.k6)((e=>[(0,t.bF)(f,{type:"text",icon:"Edit",onClick:a=>m.handleUpdate(e.row)},{default:(0,t.k6)((()=>a[19]||(a[19]=[(0,t.eW)("修改")]))),_:2},1032,["onClick"]),(0,t.bF)(f,{type:"text",icon:"Delete",onClick:a=>m.handleDelete(e.row)},{default:(0,t.k6)((()=>a[20]||(a[20]=[(0,t.eW)("删除")]))),_:2},1032,["onClick"]),(0,t.bF)(P,{onCommand:a=>m.handleCommand(a,e.row)},{dropdown:(0,t.k6)((()=>[(0,t.bF)(C,null,{default:(0,t.k6)((()=>[(0,t.bF)(V,{command:"resetPwd",icon:"key"},{default:(0,t.k6)((()=>a[22]||(a[22]=[(0,t.eW)("重置密码")]))),_:1}),(0,t.bF)(V,{command:"authRole",icon:"circle-check"},{default:(0,t.k6)((()=>a[23]||(a[23]=[(0,t.eW)("分配角色")]))),_:1})])),_:1})])),default:(0,t.k6)((()=>[(0,t.Lk)("span",r,[(0,t.bF)(f,{type:"text",icon:"d-arrow-right"},{default:(0,t.k6)((()=>a[21]||(a[21]=[(0,t.eW)("更多")]))),_:1})])])),_:2},1032,["onCommand"])])),_:1})])),_:1},8,["data","onSelectionChange"])),[[R,i.loading]]),(0,t.bo)((0,t.bF)(q,{total:i.total,page:i.queryParams.pageNum,"onUpdate:page":a[5]||(a[5]=e=>i.queryParams.pageNum=e),limit:i.queryParams.pageSize,"onUpdate:limit":a[6]||(a[6]=e=>i.queryParams.pageSize=e),onPagination:m.getList},null,8,["total","page","limit","onPagination"]),[[o.aG,i.total>0]]),(0,t.bF)(W,{title:i.title,modelValue:i.open,"onUpdate:modelValue":a[13]||(a[13]=e=>i.open=e),width:"600px","append-to-body":""},{footer:(0,t.k6)((()=>[(0,t.Lk)("div",d,[(0,t.bF)(f,{type:"primary",onClick:m.submitForm},{default:(0,t.k6)((()=>a[24]||(a[24]=[(0,t.eW)("确 定")]))),_:1},8,["onClick"]),(0,t.bF)(f,{onClick:m.cancel},{default:(0,t.k6)((()=>a[25]||(a[25]=[(0,t.eW)("取 消")]))),_:1},8,["onClick"])])])),default:(0,t.k6)((()=>[(0,t.bF)(k,{ref:"userForm",model:i.form,rules:i.rules,"label-width":"80px"},{default:(0,t.k6)((()=>[(0,t.bF)(w,null,{default:(0,t.k6)((()=>[(0,t.bF)(F,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(h,{label:"用户名称",prop:"username"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{modelValue:i.form.username,"onUpdate:modelValue":a[7]||(a[7]=e=>i.form.username=e),placeholder:"请输入用户名称"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(F,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(h,{label:"用户昵称",prop:"nickname"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{modelValue:i.form.nickname,"onUpdate:modelValue":a[8]||(a[8]=e=>i.form.nickname=e),placeholder:"请输入用户昵称"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(F,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(h,{label:"手机号码",prop:"mobile"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{modelValue:i.form.mobile,"onUpdate:modelValue":a[9]||(a[9]=e=>i.form.mobile=e),placeholder:"请输入手机号码",maxlength:"11"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(F,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(h,{label:"邮箱",prop:"email"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{modelValue:i.form.email,"onUpdate:modelValue":a[10]||(a[10]=e=>i.form.email=e),placeholder:"请输入邮箱",maxlength:"50"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(F,{span:12},{default:(0,t.k6)((()=>[void 0==i.form.userId?((0,t.uX)(),(0,t.Wv)(h,{key:0,label:"用户密码",prop:"password"},{default:(0,t.k6)((()=>[(0,t.bF)(p,{modelValue:i.form.password,"onUpdate:modelValue":a[11]||(a[11]=e=>i.form.password=e),placeholder:"请输入密码",type:"password"},null,8,["modelValue"])])),_:1})):(0,t.Q3)("",!0)])),_:1}),(0,t.bF)(F,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(h,{label:"用户状态"},{default:(0,t.k6)((()=>[(0,t.bF)(S,{modelValue:i.form.status,"onUpdate:modelValue":a[12]||(a[12]=e=>i.form.status=e)},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(i.statusOptions,(e=>((0,t.uX)(),(0,t.Wv)(x,{key:e.value,label:e.value},{default:(0,t.k6)((()=>[(0,t.eW)((0,s.v_)(e.label),1)])),_:2},1032,["label"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"])])}var i=l(2549);const m={name:"User",data(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,userList:[],title:"",open:!1,dateRange:[],statusOptions:[{value:"0",label:"正常"},{value:"1",label:"停用"}],queryParams:{pageNum:1,pageSize:10,username:void 0,mobile:void 0,status:void 0},form:{},rules:{username:[{required:!0,message:"用户名称不能为空",trigger:"blur"},{min:2,max:20,message:"用户名称长度必须介于 2 和 20 之间",trigger:"blur"}],nickname:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],password:[{required:!0,message:"用户密码不能为空",trigger:"blur"},{min:6,max:20,message:"用户密码长度必须介于 6 和 20 之间",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],mobile:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}},created(){this.getList()},methods:{getList(){this.loading=!0,(0,i.IK)(this.queryParams).then((e=>{this.userList=e.data.rows,this.total=e.data.total,this.loading=!1}))},handleQuery(){this.queryParams.pageNum=1,this.getList()},resetQuery(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange(e){this.ids=e.map((e=>e.userId)),this.single=1!==e.length,this.multiple=!e.length},handleAdd(){this.reset(),this.open=!0,this.title="添加用户"},handleUpdate(e){this.reset();const a=e.userId||this.ids[0];(0,i.wz)(a).then((e=>{this.form=e.data,this.open=!0,this.title="修改用户",this.form.password=""}))},async handleResetPwd(e){this.$prompt('请输入"'+e.username+'"的新密码',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"password",inputPattern:/^.{6,20}$/,inputErrorMessage:"用户密码长度必须介于 6 和 20 之间"}).then((({value:a})=>{(0,i.CZ)(e.userId,a).then((e=>{this.$message.success("密码重置成功")}))})).catch((()=>{}))},async handleAuthRole(e){this.$router.push("/system/role-auth/user/"+e.userId)},submitForm(){this.$refs["userForm"].validate((e=>{e&&(void 0!==this.form.userId?(0,i.TK)(this.form).then((e=>{this.$message.success("修改成功"),this.open=!1,this.getList()})):(0,i.nu)(this.form).then((e=>{this.$message.success("新增成功"),this.open=!1,this.getList()})))}))},handleDelete(e){const a=e.userId||this.ids;this.$confirm('是否确认删除用户编号为"'+a+'"的数据项?',"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){return(0,i.bz)(a)})).then((()=>{this.getList(),this.$message.success("删除成功")})).catch((()=>{}))},handleCommand(e,a){switch(e){case"resetPwd":this.handleResetPwd(a);break;case"authRole":this.handleAuthRole(a);break;default:break}},handleStatusChange(e){(0,i.e8)(e.userId,e.status).then((e=>{this.$message.success("修改成功")})).catch((()=>{e.status="0"===e.status?"1":"0"}))},reset(){this.form={userId:void 0,username:void 0,nickname:void 0,password:void 0,mobile:void 0,email:void 0,status:"0"},this.resetForm("userForm")},cancel(){this.open=!1,this.reset()}}};var p=l(1241);const h=(0,p.A)(m,[["render",u]]),c=h}}]);