"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[1409],{1409:(e,a,t)=>{t.r(a),t.d(a,{default:()=>S});var l=t(6768),s=t(4232);const n={class:"refund-container"},i={class:"stats-cards"},o={class:"stat-card"},r={class:"stat-content"},d={class:"stat-value"},c={class:"stat-card"},u={class:"stat-content"},p={class:"stat-value"},g={class:"stat-card"},h={class:"stat-content"},m={class:"stat-value"},k={class:"stat-card"},v={class:"stat-content"},b={class:"stat-value"},f={class:"filter-container"},L={class:"table-container"},_={class:"amount"},F={class:"pagination-container"};function C(e,a,t,C,w,y){const z=(0,l.g2)("el-input"),S=(0,l.g2)("el-form-item"),R=(0,l.g2)("el-option"),W=(0,l.g2)("el-select"),N=(0,l.g2)("el-button"),V=(0,l.g2)("el-form"),x=(0,l.g2)("el-table-column"),j=(0,l.g2)("el-tag"),T=(0,l.g2)("el-table"),U=(0,l.g2)("el-pagination"),D=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",n,[a[16]||(a[16]=(0,l.Lk)("div",{class:"page-header"},[(0,l.Lk)("h2",null,[(0,l.Lk)("i",{class:"el-icon-refresh-left"}),(0,l.eW)(" 退款管理")]),(0,l.Lk)("p",null,"处理订单退款申请和退款记录")],-1)),(0,l.Lk)("div",i,[(0,l.Lk)("div",o,[a[5]||(a[5]=(0,l.Lk)("div",{class:"stat-icon total"},[(0,l.Lk)("i",{class:"el-icon-document"})],-1)),(0,l.Lk)("div",r,[(0,l.Lk)("div",d,(0,s.v_)(w.stats.total),1),a[4]||(a[4]=(0,l.Lk)("div",{class:"stat-label"},"总退款申请",-1))])]),(0,l.Lk)("div",c,[a[7]||(a[7]=(0,l.Lk)("div",{class:"stat-icon pending"},[(0,l.Lk)("i",{class:"el-icon-time"})],-1)),(0,l.Lk)("div",u,[(0,l.Lk)("div",p,(0,s.v_)(w.stats.pending),1),a[6]||(a[6]=(0,l.Lk)("div",{class:"stat-label"},"待处理",-1))])]),(0,l.Lk)("div",g,[a[9]||(a[9]=(0,l.Lk)("div",{class:"stat-icon approved"},[(0,l.Lk)("i",{class:"el-icon-check"})],-1)),(0,l.Lk)("div",h,[(0,l.Lk)("div",m,(0,s.v_)(w.stats.approved),1),a[8]||(a[8]=(0,l.Lk)("div",{class:"stat-label"},"已同意",-1))])]),(0,l.Lk)("div",k,[a[11]||(a[11]=(0,l.Lk)("div",{class:"stat-icon amount"},[(0,l.Lk)("i",{class:"el-icon-money"})],-1)),(0,l.Lk)("div",v,[(0,l.Lk)("div",b,"¥"+(0,s.v_)(y.formatMoney(w.stats.amount)),1),a[10]||(a[10]=(0,l.Lk)("div",{class:"stat-label"},"退款金额",-1))])])]),(0,l.Lk)("div",f,[(0,l.bF)(V,{inline:!0,model:w.searchForm,class:"search-form"},{default:(0,l.k6)((()=>[(0,l.bF)(S,{label:"订单编号"},{default:(0,l.k6)((()=>[(0,l.bF)(z,{modelValue:w.searchForm.orderNo,"onUpdate:modelValue":a[0]||(a[0]=e=>w.searchForm.orderNo=e),placeholder:"请输入订单编号",clearable:""},null,8,["modelValue"])])),_:1}),(0,l.bF)(S,{label:"退款状态"},{default:(0,l.k6)((()=>[(0,l.bF)(W,{modelValue:w.searchForm.status,"onUpdate:modelValue":a[1]||(a[1]=e=>w.searchForm.status=e),placeholder:"请选择",clearable:""},{default:(0,l.k6)((()=>[(0,l.bF)(R,{label:"待处理",value:"pending"}),(0,l.bF)(R,{label:"已同意",value:"approved"}),(0,l.bF)(R,{label:"已拒绝",value:"rejected"}),(0,l.bF)(R,{label:"退款中",value:"processing"}),(0,l.bF)(R,{label:"已完成",value:"completed"})])),_:1},8,["modelValue"])])),_:1}),(0,l.bF)(S,null,{default:(0,l.k6)((()=>[(0,l.bF)(N,{type:"primary",onClick:y.handleSearch},{default:(0,l.k6)((()=>a[12]||(a[12]=[(0,l.Lk)("i",{class:"el-icon-search"},null,-1),(0,l.eW)(" 搜索 ")]))),_:1},8,["onClick"]),(0,l.bF)(N,{onClick:y.handleReset},{default:(0,l.k6)((()=>a[13]||(a[13]=[(0,l.Lk)("i",{class:"el-icon-refresh"},null,-1),(0,l.eW)(" 重置 ")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])]),(0,l.Lk)("div",L,[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(T,{data:w.refundList,style:{width:"100%"}},{default:(0,l.k6)((()=>[(0,l.bF)(x,{prop:"refund_no",label:"退款编号",width:"160"}),(0,l.bF)(x,{prop:"order_no",label:"订单编号",width:"160"}),(0,l.bF)(x,{prop:"user_phone",label:"用户手机号",width:"120"}),(0,l.bF)(x,{prop:"refund_amount",label:"退款金额",width:"100"},{default:(0,l.k6)((e=>[(0,l.Lk)("span",_,"¥"+(0,s.v_)(y.formatMoney(e.row.refund_amount)),1)])),_:1}),(0,l.bF)(x,{prop:"refund_reason",label:"退款原因","min-width":"200"}),(0,l.bF)(x,{prop:"status",label:"状态",width:"100"},{default:(0,l.k6)((e=>[(0,l.bF)(j,{type:y.getStatusColor(e.row.status)},{default:(0,l.k6)((()=>[(0,l.eW)((0,s.v_)(y.getStatusText(e.row.status)),1)])),_:2},1032,["type"])])),_:1}),(0,l.bF)(x,{prop:"created_at",label:"申请时间",width:"160"},{default:(0,l.k6)((e=>[(0,l.eW)((0,s.v_)(y.formatTime(e.row.created_at)),1)])),_:1}),(0,l.bF)(x,{label:"操作",width:"200",fixed:"right"},{default:(0,l.k6)((e=>[(0,l.bF)(N,{type:"text",size:"small",onClick:a=>y.handleView(e.row)},{default:(0,l.k6)((()=>a[14]||(a[14]=[(0,l.Lk)("i",{class:"el-icon-view"},null,-1),(0,l.eW)(" 查看 ")]))),_:2},1032,["onClick"]),"pending"===e.row.status?((0,l.uX)(),(0,l.Wv)(N,{key:0,type:"text",size:"small",onClick:a=>y.handleProcess(e.row)},{default:(0,l.k6)((()=>a[15]||(a[15]=[(0,l.Lk)("i",{class:"el-icon-s-tools"},null,-1),(0,l.eW)(" 处理 ")]))),_:2},1032,["onClick"])):(0,l.Q3)("",!0)])),_:1})])),_:1},8,["data"])),[[D,w.loading]]),(0,l.Lk)("div",F,[(0,l.bF)(U,{"current-page":w.pagination.page,"onUpdate:currentPage":a[2]||(a[2]=e=>w.pagination.page=e),"page-size":w.pagination.limit,"onUpdate:pageSize":a[3]||(a[3]=e=>w.pagination.limit=e),"page-sizes":[10,20,50,100],total:w.pagination.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:y.handleSizeChange,onCurrentChange:y.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])])])}const w={name:"OrderRefund",data(){return{loading:!1,refundList:[],searchForm:{orderNo:"",status:""},pagination:{page:1,limit:20,total:0},stats:{total:0,pending:0,approved:0,amount:0}}},mounted(){this.loadStats(),this.loadRefundList()},methods:{async loadStats(){this.stats={total:0,pending:0,approved:0,amount:0}},async loadRefundList(){this.loading=!0;try{this.refundList=[],this.pagination.total=0}catch(e){console.error("加载退款列表失败:",e),this.$message.error("加载退款列表失败")}finally{this.loading=!1}},handleSearch(){this.pagination.page=1,this.loadRefundList()},handleReset(){this.searchForm={orderNo:"",status:""},this.pagination.page=1,this.loadRefundList()},handleView(e){this.$message.info("查看退款详情功能开发中")},handleProcess(e){this.$message.info("处理退款功能开发中")},handleSizeChange(e){this.pagination.limit=e,this.loadRefundList()},handleCurrentChange(e){this.pagination.page=e,this.loadRefundList()},getStatusColor(e){const a={pending:"warning",approved:"success",rejected:"danger",processing:"primary",completed:"success"};return a[e]||"info"},getStatusText(e){const a={pending:"待处理",approved:"已同意",rejected:"已拒绝",processing:"退款中",completed:"已完成"};return a[e]||"未知"},formatMoney(e){return e?new Intl.NumberFormat("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}).format(e):"0.00"},formatTime(e){return e?new Date(e).toLocaleString("zh-CN"):"-"}}};var y=t(1241);const z=(0,y.A)(w,[["render",C],["__scopeId","data-v-70735668"]]),S=z}}]);