(()=>{var e={2549:(e,t,n)=>{"use strict";n.d(t,{CZ:()=>m,IK:()=>r,TK:()=>d,Vp:()=>s,bz:()=>l,e8:()=>u,iD:()=>a,nu:()=>c,ri:()=>o,wz:()=>p});var i=n(5720);function a(e){return(0,i.A)({url:"/auth/login",method:"post",data:e})}function o(){return(0,i.A)({url:"/auth/logout",method:"post"})}function s(){return(0,i.A)({url:"/system/user/info",method:"get"})}function r(e){return(0,i.A)({url:"/system/user/list",method:"get",params:e})}function c(e){return(0,i.A)({url:"/system/user",method:"post",data:e})}function d(e){return(0,i.A)({url:"/system/user",method:"put",data:e})}function l(e){return(0,i.A)({url:"/system/user/"+e,method:"delete"})}function m(e,t){const n={userId:e,password:t};return(0,i.A)({url:"/system/user/resetPwd",method:"put",data:n})}function u(e,t){const n={userId:e,status:t};return(0,i.A)({url:"/system/user/changeStatus",method:"put",data:n})}function p(e){return(0,i.A)({url:"/system/user/"+e,method:"get"})}},2973:(e,t,n)=>{"use strict";var i=n(5130),a=n(6768);const o={id:"app"};function s(e,t,n,i,s,r){const c=(0,a.g2)("router-view");return(0,a.uX)(),(0,a.CE)("div",o,[(0,a.bF)(c)])}const r={name:"App"};var c=n(1241);const d=(0,c.A)(r,[["render",s]]),l=d;var m=n(5696),u=n(5390),p=n(9927),h=(n(4188),n(7477));function b(){const e=e=>{e&&e.message&&e.message.includes("ResizeObserver loop")&&e.stopImmediatePropagation()};return window.addEventListener("error",e,!0),()=>{window.removeEventListener("error",e,!0)}}b();const f=(0,i.Ef)(l);for(const[k,v]of Object.entries(h))f.component(k,v);f.use(u.A),f.use(m.A),f.use(p.A,{size:"default"}),f.mount("#app")},3603:(e,t,n)=>{"use strict";n.d(t,{WG:()=>s,eF:()=>r,gf:()=>o});var i=n(8704);const a="Admin-Token";function o(){return i.A.get(a)}function s(e){return i.A.set(a,e)}function r(){return i.A.remove(a)}},5390:(e,t,n)=>{"use strict";n.d(t,{A:()=>_});var i=n(782),a=n(8704);const o={sidebar:{opened:!a.A.get("sidebarStatus")||!!+a.A.get("sidebarStatus"),withoutAnimation:!1},device:"desktop",size:a.A.get("size")||"default"},s={TOGGLE_SIDEBAR:e=>{e.sidebar.opened=!e.sidebar.opened,e.sidebar.withoutAnimation=!1,e.sidebar.opened?a.A.set("sidebarStatus",1):a.A.set("sidebarStatus",0)},CLOSE_SIDEBAR:(e,t)=>{a.A.set("sidebarStatus",0),e.sidebar.opened=!1,e.sidebar.withoutAnimation=t},TOGGLE_DEVICE:(e,t)=>{e.device=t},SET_SIZE:(e,t)=>{e.size=t,a.A.set("size",t)}},r={toggleSideBar({commit:e}){e("TOGGLE_SIDEBAR")},closeSideBar({commit:e},{withoutAnimation:t}){e("CLOSE_SIDEBAR",t)},toggleDevice({commit:e},t){e("TOGGLE_DEVICE",t)},setSize({commit:e},t){e("SET_SIZE",t)}},c={namespaced:!0,state:o,mutations:s,actions:r};var d=n(2549),l=n(3603);const m={state:{token:(0,l.gf)(),name:"",avatar:"",roles:[],permissions:[]},mutations:{SET_TOKEN:(e,t)=>{e.token=t},SET_NAME:(e,t)=>{e.name=t},SET_AVATAR:(e,t)=>{e.avatar=t},SET_ROLES:(e,t)=>{e.roles=t},SET_PERMISSIONS:(e,t)=>{e.permissions=t}},actions:{login({commit:e},t){const{username:n,password:i}=t;return new Promise(((t,a)=>{(0,d.iD)({username:n,password:i}).then((n=>{(0,l.WG)(n.data.token),e("SET_TOKEN",n.data.token),t()})).catch((e=>{a(e)}))}))},getInfo({commit:e,state:t}){return new Promise(((n,i)=>{(0,d.Vp)(t.token).then((t=>{const{data:a}=t;a||i("验证失败，请重新登录。");const{roles:o,permissions:s,name:r,avatar:c}=a;o&&o.length>0?(e("SET_ROLES",o),e("SET_PERMISSIONS",s)):e("SET_ROLES",["ROLE_DEFAULT"]),e("SET_NAME",r),e("SET_AVATAR",c),n(a)})).catch((e=>{i(e)}))}))},logout({commit:e,state:t}){return new Promise(((n,i)=>{(0,d.ri)(t.token).then((()=>{e("SET_TOKEN",""),e("SET_ROLES",[]),e("SET_PERMISSIONS",[]),(0,l.eF)(),n()})).catch((e=>{i(e)}))}))},fedLogout({commit:e}){return new Promise((t=>{e("SET_TOKEN",""),(0,l.eF)(),t()}))}}},u=m;var p=n(5696);const h={state:{routes:[],addRoutes:[]},mutations:{SET_ROUTES:(e,t)=>{e.addRoutes=t,e.routes=p.b.concat(t)}},actions:{generateRoutes({commit:e}){return new Promise((t=>{const n=p.b;e("SET_ROUTES",[]),t(n)}))}}},b=h;var f=n(9963),k=n.n(f);const v={sidebar:e=>e.app.sidebar,size:e=>e.app.size,device:e=>e.app.device,visitedViews:e=>e.tagsView.visitedViews,cachedViews:e=>e.tagsView.cachedViews,token:e=>e.user.token,avatar:e=>e.user.avatar,name:e=>e.user.name,introduction:e=>e.user.introduction,roles:e=>e.user.roles,permissions:e=>e.user.permissions,routes:e=>e.permission.routes},g=v,S=(0,i.y$)({modules:{app:c,user:u,permission:b,tagsView:k()},getters:g}),_=S},5696:(e,t,n)=>{"use strict";n.d(t,{b:()=>f,A:()=>v});var i=n(1387),a=n(6768),o=n(4232);const s={class:"app-wrapper"},r={class:"main-container"},c={class:"navbar"},d={class:"right-menu"},l={class:"app-main"};function m(e,t,n,i,m,u){const p=(0,a.g2)("el-menu-item"),h=(0,a.g2)("el-sub-menu"),b=(0,a.g2)("el-menu"),f=(0,a.g2)("el-dropdown-item"),k=(0,a.g2)("el-dropdown-menu"),v=(0,a.g2)("el-dropdown"),g=(0,a.g2)("router-view");return(0,a.uX)(),(0,a.CE)("div",s,[(0,a.Lk)("div",{class:(0,o.C4)(["sidebar-container",{"is-collapsed":m.isCollapse}])},[t[42]||(t[42]=(0,a.Lk)("div",{class:"logo-container"},[(0,a.Lk)("h1",{class:"title"},"驿站帮Pro管理系统")],-1)),(0,a.bF)(b,{"default-active":m.activeMenu,"background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#409EFF",collapse:m.isCollapse,"unique-opened":!0,router:"",mode:"vertical"},{default:(0,a.k6)((()=>[(0,a.bF)(p,{index:"/"},{default:(0,a.k6)((()=>t[1]||(t[1]=[(0,a.Lk)("i",{class:"el-icon-s-home"},null,-1),(0,a.Lk)("span",null,"首页",-1)]))),_:1}),(0,a.bF)(h,{index:"/system"},{title:(0,a.k6)((()=>t[2]||(t[2]=[(0,a.Lk)("i",{class:"el-icon-s-tools"},null,-1),(0,a.Lk)("span",null,"系统管理",-1)]))),default:(0,a.k6)((()=>[(0,a.bF)(p,{index:"/system/role"},{default:(0,a.k6)((()=>t[3]||(t[3]=[(0,a.eW)("角色管理")]))),_:1}),(0,a.bF)(p,{index:"/system/menu"},{default:(0,a.k6)((()=>t[4]||(t[4]=[(0,a.eW)("菜单管理")]))),_:1})])),_:1}),(0,a.bF)(h,{index:"/station"},{title:(0,a.k6)((()=>t[5]||(t[5]=[(0,a.Lk)("i",{class:"el-icon-s-shop"},null,-1),(0,a.Lk)("span",null,"站点管理",-1)]))),default:(0,a.k6)((()=>[(0,a.bF)(p,{index:"/station/list"},{default:(0,a.k6)((()=>t[6]||(t[6]=[(0,a.eW)("站点列表")]))),_:1}),(0,a.bF)(p,{index:"/station/verify"},{default:(0,a.k6)((()=>t[7]||(t[7]=[(0,a.eW)("站点审核")]))),_:1})])),_:1}),(0,a.bF)(h,{index:"/order"},{title:(0,a.k6)((()=>t[8]||(t[8]=[(0,a.Lk)("i",{class:"el-icon-s-order"},null,-1),(0,a.Lk)("span",null,"订单管理",-1)]))),default:(0,a.k6)((()=>[(0,a.bF)(p,{index:"/order/list"},{default:(0,a.k6)((()=>t[9]||(t[9]=[(0,a.eW)("订单列表")]))),_:1}),(0,a.bF)(p,{index:"/order/refund"},{default:(0,a.k6)((()=>t[10]||(t[10]=[(0,a.eW)("退款管理")]))),_:1})])),_:1}),(0,a.bF)(h,{index:"/service"},{title:(0,a.k6)((()=>t[11]||(t[11]=[(0,a.Lk)("i",{class:"el-icon-s-cooperation"},null,-1),(0,a.Lk)("span",null,"服务管理",-1)]))),default:(0,a.k6)((()=>[(0,a.bF)(p,{index:"/service/list"},{default:(0,a.k6)((()=>t[12]||(t[12]=[(0,a.eW)("服务列表")]))),_:1})])),_:1}),(0,a.bF)(p,{index:"/job/list"},{default:(0,a.k6)((()=>t[13]||(t[13]=[(0,a.Lk)("i",{class:"el-icon-s-claim"},null,-1),(0,a.Lk)("span",null,"任务管理",-1)]))),_:1}),(0,a.bF)(p,{index:"/device/list"},{default:(0,a.k6)((()=>t[14]||(t[14]=[(0,a.Lk)("i",{class:"el-icon-s-platform"},null,-1),(0,a.Lk)("span",null,"设备管理",-1)]))),_:1}),(0,a.bF)(h,{index:"/transfer"},{title:(0,a.k6)((()=>t[15]||(t[15]=[(0,a.Lk)("i",{class:"el-icon-s-shop"},null,-1),(0,a.Lk)("span",null,"驿站转让",-1)]))),default:(0,a.k6)((()=>[(0,a.bF)(p,{index:"/transfer/list"},{default:(0,a.k6)((()=>t[16]||(t[16]=[(0,a.eW)("转让列表")]))),_:1}),(0,a.bF)(p,{index:"/transfer/statistics"},{default:(0,a.k6)((()=>t[17]||(t[17]=[(0,a.eW)("转让统计")]))),_:1})])),_:1}),(0,a.bF)(h,{index:"/equipment"},{title:(0,a.k6)((()=>t[18]||(t[18]=[(0,a.Lk)("i",{class:"el-icon-printer"},null,-1),(0,a.Lk)("span",null,"设备交易",-1)]))),default:(0,a.k6)((()=>[(0,a.bF)(p,{index:"/equipment/list"},{default:(0,a.k6)((()=>t[19]||(t[19]=[(0,a.eW)("设备列表")]))),_:1}),(0,a.bF)(p,{index:"/equipment/category"},{default:(0,a.k6)((()=>t[20]||(t[20]=[(0,a.eW)("设备分类")]))),_:1}),(0,a.bF)(p,{index:"/equipment/price"},{default:(0,a.k6)((()=>t[21]||(t[21]=[(0,a.eW)("价格参考")]))),_:1})])),_:1}),(0,a.bF)(h,{index:"/recruitment"},{title:(0,a.k6)((()=>t[22]||(t[22]=[(0,a.Lk)("i",{class:"el-icon-suitcase"},null,-1),(0,a.Lk)("span",null,"招聘求职",-1)]))),default:(0,a.k6)((()=>[(0,a.bF)(p,{index:"/recruitment/list"},{default:(0,a.k6)((()=>t[23]||(t[23]=[(0,a.eW)("招聘列表")]))),_:1}),(0,a.bF)(p,{index:"/recruitment/category"},{default:(0,a.k6)((()=>t[24]||(t[24]=[(0,a.eW)("职位分类")]))),_:1}),(0,a.bF)(p,{index:"/recruitment/statistics"},{default:(0,a.k6)((()=>t[25]||(t[25]=[(0,a.eW)("招聘统计")]))),_:1})])),_:1}),(0,a.bF)(h,{index:"/substitution"},{title:(0,a.k6)((()=>t[26]||(t[26]=[(0,a.Lk)("i",{class:"el-icon-alarm-clock"},null,-1),(0,a.Lk)("span",null,"顶班服务",-1)]))),default:(0,a.k6)((()=>[(0,a.bF)(p,{index:"/substitution/list"},{default:(0,a.k6)((()=>t[27]||(t[27]=[(0,a.eW)("顶班列表")]))),_:1}),(0,a.bF)(p,{index:"/substitution/application"},{default:(0,a.k6)((()=>t[28]||(t[28]=[(0,a.eW)("接单申请")]))),_:1}),(0,a.bF)(p,{index:"/substitution/evaluation"},{default:(0,a.k6)((()=>t[29]||(t[29]=[(0,a.eW)("服务评价")]))),_:1})])),_:1}),(0,a.bF)(h,{index:"/shop"},{title:(0,a.k6)((()=>t[30]||(t[30]=[(0,a.Lk)("i",{class:"el-icon-s-goods"},null,-1),(0,a.Lk)("span",null,"商城管理",-1)]))),default:(0,a.k6)((()=>[(0,a.bF)(p,{index:"/shop/decoration"},{default:(0,a.k6)((()=>t[31]||(t[31]=[(0,a.eW)("小程序装修")]))),_:1})])),_:1}),(0,a.bF)(h,{index:"/promotion"},{title:(0,a.k6)((()=>t[32]||(t[32]=[(0,a.Lk)("i",{class:"el-icon-present"},null,-1),(0,a.Lk)("span",null,"推广赚钱",-1)]))),default:(0,a.k6)((()=>[(0,a.bF)(p,{index:"/promotion/user"},{default:(0,a.k6)((()=>t[33]||(t[33]=[(0,a.eW)("推广员管理")]))),_:1}),(0,a.bF)(p,{index:"/promotion/code"},{default:(0,a.k6)((()=>t[34]||(t[34]=[(0,a.eW)("推广码管理")]))),_:1}),(0,a.bF)(p,{index:"/promotion/commission"},{default:(0,a.k6)((()=>t[35]||(t[35]=[(0,a.eW)("佣金规则")]))),_:1}),(0,a.bF)(p,{index:"/promotion/withdrawal"},{default:(0,a.k6)((()=>t[36]||(t[36]=[(0,a.eW)("提现管理")]))),_:1}),(0,a.bF)(p,{index:"/promotion/statistics"},{default:(0,a.k6)((()=>t[37]||(t[37]=[(0,a.eW)("推广统计")]))),_:1})])),_:1}),(0,a.bF)(h,{index:"/statistics"},{title:(0,a.k6)((()=>t[38]||(t[38]=[(0,a.Lk)("i",{class:"el-icon-s-data"},null,-1),(0,a.Lk)("span",null,"统计分析",-1)]))),default:(0,a.k6)((()=>[(0,a.bF)(p,{index:"/statistics/overview"},{default:(0,a.k6)((()=>t[39]||(t[39]=[(0,a.eW)("统计概览")]))),_:1}),(0,a.bF)(p,{index:"/statistics/user"},{default:(0,a.k6)((()=>t[40]||(t[40]=[(0,a.eW)("用户统计")]))),_:1}),(0,a.bF)(p,{index:"/statistics/order"},{default:(0,a.k6)((()=>t[41]||(t[41]=[(0,a.eW)("订单统计")]))),_:1})])),_:1})])),_:1},8,["default-active","collapse"])],2),(0,a.Lk)("div",r,[(0,a.Lk)("div",c,[(0,a.Lk)("div",{class:"hamburger-container",onClick:t[0]||(t[0]=(...e)=>u.toggleSideBar&&u.toggleSideBar(...e))},[(0,a.Lk)("i",{class:(0,o.C4)(m.isCollapse?"el-icon-s-unfold":"el-icon-s-fold")},null,2)]),(0,a.Lk)("div",d,[(0,a.bF)(v,{trigger:"click"},{dropdown:(0,a.k6)((()=>[(0,a.bF)(k,null,{default:(0,a.k6)((()=>[(0,a.bF)(f,null,{default:(0,a.k6)((()=>t[43]||(t[43]=[(0,a.eW)("个人中心")]))),_:1}),(0,a.bF)(f,{divided:""},{default:(0,a.k6)((()=>t[44]||(t[44]=[(0,a.eW)("退出登录")]))),_:1})])),_:1})])),default:(0,a.k6)((()=>[t[45]||(t[45]=(0,a.Lk)("span",{class:"el-dropdown-link"},[(0,a.eW)(" 管理员 "),(0,a.Lk)("i",{class:"el-icon-arrow-down"})],-1))])),_:1})])]),(0,a.Lk)("div",l,[(0,a.bF)(g)])])])}const u={name:"Layout",data(){return{isCollapse:!1,activeMenu:"/"}},methods:{toggleSideBar(){this.isCollapse=!this.isCollapse}}};var p=n(1241);const h=(0,p.A)(u,[["render",m],["__scopeId","data-v-49d9283a"]]),b=h,f=[{path:"/login",component:()=>n.e(7677).then(n.bind(n,7677)),hidden:!0},{path:"/404",component:()=>n.e(6960).then(n.bind(n,6960)),hidden:!0},{path:"/",component:b,redirect:"/dashboard",children:[{path:"dashboard",component:()=>Promise.all([n.e(2032),n.e(2822)]).then(n.bind(n,2822)),name:"Dashboard",meta:{title:"仪表盘",icon:"Odometer",affix:!0}}]},{path:"/system",component:b,redirect:"/system/user",name:"System",meta:{title:"系统管理",icon:"Setting"},children:[{path:"user",component:()=>n.e(1196).then(n.bind(n,1196)),name:"User",meta:{title:"用户管理",icon:"User"}},{path:"role",component:()=>n.e(8591).then(n.bind(n,8591)),name:"Role",meta:{title:"角色管理",icon:"UserFilled"}},{path:"menu",component:()=>n.e(9629).then(n.bind(n,9629)),name:"Menu",meta:{title:"菜单管理",icon:"Menu"}}]},{path:"/station",component:b,redirect:"/station/list",name:"Station",meta:{title:"驿站管理",icon:"House"},children:[{path:"list",component:()=>n.e(5285).then(n.bind(n,5285)),name:"StationList",meta:{title:"驿站列表",icon:"List"}},{path:"verify",component:()=>n.e(6244).then(n.bind(n,6244)),name:"StationVerify",meta:{title:"认证审核",icon:"Checked"}}]},{path:"/order",component:b,redirect:"/order/list",name:"Order",meta:{title:"订单管理",icon:"Tickets"},children:[{path:"list",component:()=>n.e(2045).then(n.bind(n,2045)),name:"OrderList",meta:{title:"订单列表",icon:"List"}},{path:"refund",component:()=>n.e(1674).then(n.bind(n,1674)),name:"OrderRefund",meta:{title:"退款管理",icon:"TurnOff"}}]},{path:"/service",component:b,redirect:"/service/list",name:"Service",meta:{title:"服务管理",icon:"Service"},children:[{path:"list",component:()=>n.e(6074).then(n.bind(n,6074)),name:"ServiceList",meta:{title:"服务列表",icon:"List"}}]},{path:"/job",component:b,redirect:"/job/list",name:"Job",meta:{title:"工作岗位",icon:"Suitcase"},children:[{path:"list",component:()=>n.e(2917).then(n.bind(n,2917)),name:"JobList",meta:{title:"岗位列表",icon:"List"}}]},{path:"/device",component:b,redirect:"/device/list",name:"Device",meta:{title:"设备管理",icon:"Box"},children:[{path:"list",component:()=>n.e(1669).then(n.bind(n,1669)),name:"DeviceList",meta:{title:"设备列表",icon:"List"}}]},{path:"/content",component:b,redirect:"/content/banner",name:"Content",meta:{title:"内容管理",icon:"Document"},children:[{path:"audit",component:()=>n.e(5882).then(n.bind(n,5882)),name:"ContentAudit",meta:{title:"统一审核中心",icon:"Check"}},{path:"audit/detail/:id/:type",component:()=>n.e(5382).then(n.bind(n,5382)),name:"AuditDetail",meta:{title:"审核详情",icon:"View",activeMenu:"/content/audit"},hidden:!0},{path:"management",component:()=>Promise.all([n.e(2032),n.e(8504)]).then(n.bind(n,8504)),name:"ContentManagement",meta:{title:"内容上下架管理",icon:"SwitchButton"}},{path:"quality",component:()=>n.e(5329).then(n.bind(n,5329)),name:"ContentQuality",meta:{title:"内容质量管理",icon:"Star"}},{path:"banner",component:()=>n.e(2520).then(n.bind(n,2520)),name:"Banner",meta:{title:"轮播图管理",icon:"Picture"}},{path:"notice",component:()=>n.e(3758).then(n.bind(n,3758)),name:"Notice",meta:{title:"公告管理",icon:"Bell"}}]},{path:"/statistics",component:b,redirect:"/statistics/overview",name:"Statistics",meta:{title:"统计分析",icon:"DataAnalysis"},children:[{path:"overview",component:()=>n.e(1871).then(n.bind(n,1871)),name:"Overview",meta:{title:"业务概览",icon:"DataLine"}},{path:"user",component:()=>n.e(7232).then(n.bind(n,7232)),name:"UserStatistics",meta:{title:"用户分析",icon:"User"}},{path:"order",component:()=>n.e(5903).then(n.bind(n,5903)),name:"OrderStatistics",meta:{title:"订单分析",icon:"Tickets"}}]},{path:"/profile",component:b,hidden:!0,children:[{path:"",component:()=>n.e(865).then(n.bind(n,865)),name:"Profile",meta:{title:"个人中心",icon:"User"}}]},{path:"/transfer",component:b,redirect:"/transfer/list",name:"Transfer",meta:{title:"驿站转让",icon:"Shop"},children:[{path:"list",component:()=>n.e(5772).then(n.bind(n,5772)),name:"TransferList",meta:{title:"转让列表",icon:"List"}},{path:"detail/:id",component:()=>n.e(9374).then(n.bind(n,9374)),name:"TransferDetail",meta:{title:"转让详情",icon:"View",activeMenu:"/transfer/list"},hidden:!0},{path:"statistics",component:()=>Promise.all([n.e(2032),n.e(8258)]).then(n.bind(n,8258)),name:"TransferStatistics",meta:{title:"转让统计",icon:"PieChart"}}]},{path:"/equipment",component:b,redirect:"/equipment/list",name:"Equipment",meta:{title:"设备交易",icon:"Printer"},children:[{path:"list",component:()=>n.e(5757).then(n.bind(n,5757)),name:"EquipmentList",meta:{title:"设备列表",icon:"List"}},{path:"detail/:id",component:()=>n.e(4685).then(n.bind(n,4685)),name:"EquipmentDetail",meta:{title:"设备详情",icon:"View",activeMenu:"/equipment/list"},hidden:!0},{path:"category",component:()=>n.e(8111).then(n.bind(n,492)),name:"EquipmentCategory",meta:{title:"设备分类",icon:"Folder"}},{path:"price",component:()=>Promise.all([n.e(2032),n.e(8709)]).then(n.bind(n,8709)),name:"EquipmentPrice",meta:{title:"价格参考",icon:"Money"}}]},{path:"/recruitment",component:b,redirect:"/recruitment/list",name:"Recruitment",meta:{title:"招聘求职",icon:"Briefcase"},children:[{path:"list",component:()=>n.e(8945).then(n.bind(n,8945)),name:"RecruitmentList",meta:{title:"招聘列表",icon:"List"}},{path:"detail/:id",component:()=>n.e(6955).then(n.bind(n,6955)),name:"RecruitmentDetail",meta:{title:"招聘详情",icon:"View",activeMenu:"/recruitment/list"},hidden:!0},{path:"category",component:()=>n.e(2508).then(n.bind(n,2508)),name:"JobCategory",meta:{title:"职位分类",icon:"Folder"}},{path:"statistics",component:()=>n.e(9641).then(n.bind(n,9641)),name:"RecruitmentStatistics",meta:{title:"招聘统计",icon:"PieChart"}}]},{path:"/substitution",component:b,redirect:"/substitution/list",name:"Substitution",meta:{title:"顶班服务",icon:"Timer"},children:[{path:"list",component:()=>n.e(9273).then(n.bind(n,9273)),name:"SubstitutionList",meta:{title:"顶班列表",icon:"List"}},{path:"detail/:id",component:()=>n.e(6653).then(n.bind(n,6653)),name:"SubstitutionDetail",meta:{title:"顶班详情",icon:"View",activeMenu:"/substitution/list"},hidden:!0},{path:"application",component:()=>n.e(9298).then(n.bind(n,9298)),name:"SubstitutionApplication",meta:{title:"接单申请",icon:"Finished"}},{path:"evaluation",component:()=>n.e(6610).then(n.bind(n,6610)),name:"SubstitutionEvaluation",meta:{title:"服务评价",icon:"Star"}}]},{path:"/shop",component:b,redirect:"/shop/decoration",name:"Shop",meta:{title:"商城管理",icon:"ShoppingBag"},children:[{path:"decoration",component:()=>n.e(7550).then(n.bind(n,7550)),name:"ShopDecoration",meta:{title:"小程序装修",icon:"Brush"}}]},{path:"/promotion",component:b,redirect:"/promotion/user",name:"Promotion",meta:{title:"推广赚钱",icon:"Present"},children:[{path:"user",component:()=>n.e(9954).then(n.bind(n,9954)),name:"PromoterUser",meta:{title:"推广员管理",icon:"User"}},{path:"code",component:()=>n.e(9414).then(n.bind(n,9414)),name:"PromoCode",meta:{title:"推广码管理",icon:"DocumentCopy"}},{path:"commission",component:()=>n.e(1327).then(n.bind(n,1327)),name:"Commission",meta:{title:"佣金规则",icon:"SetUp"}},{path:"withdrawal",component:()=>n.e(4076).then(n.bind(n,4076)),name:"Withdrawal",meta:{title:"提现管理",icon:"Money"}},{path:"statistics",component:()=>Promise.all([n.e(2032),n.e(7722)]).then(n.bind(n,7722)),name:"PromotionStatistics",meta:{title:"推广统计",icon:"PieChart"}}]},{path:"/identity",component:b,redirect:"/identity/list",name:"Identity",meta:{title:"实名认证管理",icon:"UserFilled"},children:[{path:"list",component:()=>n.e(4167).then(n.bind(n,4167)),name:"IdentityList",meta:{title:"认证列表",icon:"List"}},{path:"detail/:id",component:()=>n.e(6101).then(n.bind(n,6101)),name:"IdentityDetail",meta:{title:"认证详情",icon:"View",activeMenu:"/identity/list"},hidden:!0},{path:"statistics",component:()=>Promise.all([n.e(2032),n.e(4633)]).then(n.bind(n,4633)),name:"IdentityStatistics",meta:{title:"认证统计",icon:"DataAnalysis"}}]},{path:"/:pathMatch(.*)*",redirect:"/404",hidden:!0}],k=(0,i.aE)({history:(0,i.LA)(),routes:f,scrollBehavior:()=>({top:0})}),v=k},5720:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var i=n(788),a=n.n(i),o=n(1219),s=n(2933),r=n(5390),c=n(3603);const d=a().create({baseURL:"/api",timeout:1e4});d.interceptors.request.use((e=>(r.A.getters.token&&(e.headers["Authorization"]="Bearer "+(0,c.gf)()),e)),(e=>(console.log(e),Promise.reject(e)))),d.interceptors.response.use((e=>{const t=e.data;return 0!==t.code?((0,o.nk)({message:t.message||"系统错误",type:"error",duration:5e3}),401!==t.code&&50012!==t.code&&50014!==t.code||s.s.confirm("登录状态已过期，您可以继续留在该页面，或者重新登录","系统提示",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then((()=>{r.A.dispatch("user/logout").then((()=>{location.reload()}))})),Promise.reject(new Error(t.message||"系统错误"))):t}),(e=>{console.log("错误信息："+e);let t=e.message;if(e.response&&e.response.data){const{data:n}=e.response;t=n.message||"系统错误"}return(0,o.nk)({message:t,type:"error",duration:5e3}),Promise.reject(e)}));const l=d},9963:()=>{}},t={};function n(i){var a=t[i];if(void 0!==a)return a.exports;var o=t[i]={exports:{}};return e[i].call(o.exports,o,o.exports,n),o.exports}n.m=e,(()=>{var e=[];n.O=(t,i,a,o)=>{if(!i){var s=1/0;for(l=0;l<e.length;l++){for(var[i,a,o]=e[l],r=!0,c=0;c<i.length;c++)(!1&o||s>=o)&&Object.keys(n.O).every((e=>n.O[e](i[c])))?i.splice(c--,1):(r=!1,o<s&&(s=o));if(r){e.splice(l--,1);var d=a();void 0!==d&&(t=d)}}return t}o=o||0;for(var l=e.length;l>0&&e[l-1][2]>o;l--)e[l]=e[l-1];e[l]=[i,a,o]}})(),(()=>{n.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;return n.d(t,{a:t}),t}})(),(()=>{n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}})(),(()=>{n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,i)=>(n.f[i](e,t),t)),[]))})(),(()=>{n.u=e=>"js/"+e+"."+{865:"7390a71d",1196:"6290a728",1327:"1d2e6086",1669:"e3944e78",1674:"dca4a591",1871:"a9f4d3cd",2032:"75fa505d",2045:"cd5f0a1e",2508:"ff6a988f",2520:"f18e19bb",2672:"66f6f289",2822:"75dd612e",2917:"3dc79c4a",3758:"0c39abfd",4076:"c4eaa254",4167:"be5f8ad7",4633:"f1f8bbdd",4685:"c0409057",5285:"7623939f",5329:"a9beb581",5382:"44452b83",5757:"a01e4a27",5772:"1a58e249",5882:"300c9640",5903:"b8d65e03",6074:"62f21075",6101:"4fcb1c66",6244:"0bf5cf08",6610:"712bb9f4",6653:"e9ac97a1",6955:"baaf1d9d",6960:"9614758a",7232:"f407f396",7550:"0e41a092",7677:"8807310f",7722:"e2934725",8111:"30019e04",8258:"902d6f97",8504:"f544c98c",8591:"847fbc4c",8709:"eb0ec49f",8945:"12f09a55",9273:"ee8bf39e",9298:"cfa2926a",9374:"721673e6",9414:"71b7985e",9629:"74f31cb6",9641:"e68dfbda",9954:"dabad49c"}[e]+".js"})(),(()=>{n.miniCssF=e=>"css/"+e+"."+{865:"5a78d437",1327:"309030ac",1669:"5500b72c",1674:"d8d9fc2e",1871:"0780158b",2045:"de671e1d",2508:"45935be1",2520:"20d59d6d",2822:"63a5d4c9",2917:"9e720526",3758:"c99068b9",4076:"f0e60090",4167:"85b81707",4633:"97d339ea",4685:"761e17a9",5285:"50c03b80",5329:"34c0d69a",5382:"6fede4e7",5757:"457cdc19",5772:"497b9471",5882:"f0b27126",5903:"5189a106",6074:"b11f4ee5",6101:"27398723",6244:"04a54d0d",6955:"b9f35c1b",6960:"1dc5a122",7232:"3e7b243f",7550:"a60b68da",7677:"f58db83a",7722:"70d3eb8b",8111:"a634648c",8258:"78551b2e",8504:"11cd4718",8591:"8f51910b",8709:"3bfe9910",8945:"55c051f6",9273:"0a5f00f2",9298:"2e6071d4",9374:"3d0992ea",9414:"b985a7fc",9629:"a4a3b07b",9641:"211f8e26",9954:"6b836bf7"}[e]+".css"})(),(()=>{n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{var e={},t="yzb-admin-system:";n.l=(i,a,o,s)=>{if(e[i])e[i].push(a);else{var r,c;if(void 0!==o)for(var d=document.getElementsByTagName("script"),l=0;l<d.length;l++){var m=d[l];if(m.getAttribute("src")==i||m.getAttribute("data-webpack")==t+o){r=m;break}}r||(c=!0,r=document.createElement("script"),r.charset="utf-8",r.timeout=120,n.nc&&r.setAttribute("nonce",n.nc),r.setAttribute("data-webpack",t+o),r.src=i),e[i]=[a];var u=(t,n)=>{r.onerror=r.onload=null,clearTimeout(p);var a=e[i];if(delete e[i],r.parentNode&&r.parentNode.removeChild(r),a&&a.forEach((e=>e(n))),t)return t(n)},p=setTimeout(u.bind(null,void 0,{type:"timeout",target:r}),12e4);r.onerror=u.bind(null,r.onerror),r.onload=u.bind(null,r.onload),c&&document.head.appendChild(r)}}})(),(()=>{n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{n.p="/"})(),(()=>{if("undefined"!==typeof document){var e=(e,t,i,a,o)=>{var s=document.createElement("link");s.rel="stylesheet",s.type="text/css",n.nc&&(s.nonce=n.nc);var r=n=>{if(s.onerror=s.onload=null,"load"===n.type)a();else{var i=n&&n.type,r=n&&n.target&&n.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+i+": "+r+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=i,c.request=r,s.parentNode&&s.parentNode.removeChild(s),o(c)}};return s.onerror=s.onload=r,s.href=t,i?i.parentNode.insertBefore(s,i.nextSibling):document.head.appendChild(s),s},t=(e,t)=>{for(var n=document.getElementsByTagName("link"),i=0;i<n.length;i++){var a=n[i],o=a.getAttribute("data-href")||a.getAttribute("href");if("stylesheet"===a.rel&&(o===e||o===t))return a}var s=document.getElementsByTagName("style");for(i=0;i<s.length;i++){a=s[i],o=a.getAttribute("data-href");if(o===e||o===t)return a}},i=i=>new Promise(((a,o)=>{var s=n.miniCssF(i),r=n.p+s;if(t(s,r))return a();e(i,r,null,a,o)})),a={3524:0};n.f.miniCss=(e,t)=>{var n={865:1,1327:1,1669:1,1674:1,1871:1,2045:1,2508:1,2520:1,2822:1,2917:1,3758:1,4076:1,4167:1,4633:1,4685:1,5285:1,5329:1,5382:1,5757:1,5772:1,5882:1,5903:1,6074:1,6101:1,6244:1,6955:1,6960:1,7232:1,7550:1,7677:1,7722:1,8111:1,8258:1,8504:1,8591:1,8709:1,8945:1,9273:1,9298:1,9374:1,9414:1,9629:1,9641:1,9954:1};a[e]?t.push(a[e]):0!==a[e]&&n[e]&&t.push(a[e]=i(e).then((()=>{a[e]=0}),(t=>{throw delete a[e],t})))}}})(),(()=>{var e={3524:0};n.f.j=(t,i)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)i.push(a[2]);else{var o=new Promise(((n,i)=>a=e[t]=[n,i]));i.push(a[2]=o);var s=n.p+n.u(t),r=new Error,c=i=>{if(n.o(e,t)&&(a=e[t],0!==a&&(e[t]=void 0),a)){var o=i&&("load"===i.type?"missing":i.type),s=i&&i.target&&i.target.src;r.message="Loading chunk "+t+" failed.\n("+o+": "+s+")",r.name="ChunkLoadError",r.type=o,r.request=s,a[1](r)}};n.l(s,c,"chunk-"+t,t)}},n.O.j=t=>0===e[t];var t=(t,i)=>{var a,o,[s,r,c]=i,d=0;if(s.some((t=>0!==e[t]))){for(a in r)n.o(r,a)&&(n.m[a]=r[a]);if(c)var l=c(n)}for(t&&t(i);d<s.length;d++)o=s[d],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return n.O(l)},i=self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))})();var i=n.O(void 0,[504],(()=>n(2973)));i=n.O(i)})();