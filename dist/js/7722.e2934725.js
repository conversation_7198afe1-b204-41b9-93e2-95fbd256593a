"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[7722],{2211:(e,t,a)=>{a.d(t,{R7:()=>s,fW:()=>l,fr:()=>h,nU:()=>c,q_:()=>o,tS:()=>d,tb:()=>r,zK:()=>n});var i=a(5720);function s(e){return(0,i.A)({url:"/api/admin/promotion/overview",method:"get",params:e})}function n(e){return(0,i.A)({url:"/api/admin/promotion/trend",method:"get",params:e})}function r(){return(0,i.A)({url:"/api/admin/promotion/level-distribution",method:"get"})}function o(e){return(0,i.A)({url:"/api/admin/promotion/commission-ranking",method:"get",params:e})}function l(e){return(0,i.A)({url:"/api/admin/promotion/invites-ranking",method:"get",params:e})}function d(e,t=1,a={}){return(0,i.A)({url:`/api/admin/promoters/${e}/invites`,method:"get",params:{level:t,...a}})}function h(e,t={}){return(0,i.A)({url:`/api/admin/promoters/${e}/commissions`,method:"get",params:t})}function c(e,t={}){return(0,i.A)({url:`/api/admin/promoters/${e}/withdrawals`,method:"get",params:t})}},7722:(e,t,a)=>{a.r(t),a.d(t,{default:()=>oe});var i=a(6768),s=a(4232);const n={class:"app-container"},r={class:"stat-header"},o={class:"stat-body"},l={class:"stat-value"},d={class:"stat-chart mini-chart"},h={class:"stat-footer"},c={class:"stat-body"},v={class:"stat-value"},m={class:"stat-chart mini-chart"},u={class:"stat-footer"},g={class:"stat-body"},k={class:"stat-value"},b={class:"stat-chart mini-chart"},p={class:"stat-footer"},w={class:"stat-body"},L={class:"stat-value"},f={class:"stat-chart mini-chart"},y={class:"stat-footer"},C={class:"chart-header"},_={class:"chart-actions"},T={class:"chart-body"},F={class:"main-chart"},R={class:"chart-body"},A={class:"pie-chart"},z={class:"ranking-header"},E={class:"ranking-body"},$={class:"ranking-avatar"},x={class:"ranking-info"},S={class:"ranking-name"},V={class:"ranking-level"},H={class:"ranking-value"},X={key:0,class:"empty-text"},D={class:"ranking-header"},W={class:"ranking-body"},I={class:"ranking-avatar"},O={class:"ranking-info"},N={class:"ranking-name"},P={class:"ranking-level"},M={class:"ranking-value"},U={key:0,class:"empty-text"};function q(e,t,a,q,B,K){const Q=(0,i.g2)("el-option"),j=(0,i.g2)("el-select"),G=(0,i.g2)("trend-chart"),J=(0,i.g2)("el-card"),Y=(0,i.g2)("el-col"),Z=(0,i.g2)("el-row"),ee=(0,i.g2)("el-radio-button"),te=(0,i.g2)("el-radio-group"),ae=(0,i.g2)("trendChart"),ie=(0,i.g2)("pieChart"),se=(0,i.g2)("el-avatar"),ne=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)("div",n,[(0,i.bF)(Z,{gutter:20},{default:(0,i.k6)((()=>[(0,i.bF)(Y,{span:6},{default:(0,i.k6)((()=>[(0,i.bF)(J,{class:"stat-card"},{default:(0,i.k6)((()=>[(0,i.Lk)("div",r,[t[5]||(t[5]=(0,i.Lk)("div",{class:"stat-title"},"总推广员数",-1)),(0,i.bF)(j,{modelValue:B.overviewTimeRange,"onUpdate:modelValue":t[0]||(t[0]=e=>B.overviewTimeRange=e),size:"mini",onChange:K.refreshOverview},{default:(0,i.k6)((()=>[(0,i.bF)(Q,{label:"今日",value:"today"}),(0,i.bF)(Q,{label:"昨日",value:"yesterday"}),(0,i.bF)(Q,{label:"本周",value:"week"}),(0,i.bF)(Q,{label:"本月",value:"month"})])),_:1},8,["modelValue","onChange"])]),(0,i.Lk)("div",o,[(0,i.Lk)("div",l,(0,s.v_)(B.overview.totalPromoters),1),(0,i.Lk)("div",d,[(0,i.bF)(G,{data:B.overview.promoterTrend,color:"#409EFF",height:30,"is-smooth":!0},null,8,["data"])])]),(0,i.Lk)("div",h,[(0,i.Lk)("div",{class:(0,s.C4)(["stat-change",B.overview.promoterChange>=0?"up":"down"])},[(0,i.Lk)("i",{class:(0,s.C4)(B.overview.promoterChange>=0?"el-icon-top":"el-icon-bottom")},null,2),(0,i.eW)(" "+(0,s.v_)(Math.abs(B.overview.promoterChange)),1)],2),t[6]||(t[6]=(0,i.Lk)("div",{class:"stat-period"},"较上期",-1))])])),_:1})])),_:1}),(0,i.bF)(Y,{span:6},{default:(0,i.k6)((()=>[(0,i.bF)(J,{class:"stat-card"},{default:(0,i.k6)((()=>[t[8]||(t[8]=(0,i.Lk)("div",{class:"stat-header"},[(0,i.Lk)("div",{class:"stat-title"},"新增推广用户")],-1)),(0,i.Lk)("div",c,[(0,i.Lk)("div",v,(0,s.v_)(B.overview.newInvites),1),(0,i.Lk)("div",m,[(0,i.bF)(G,{data:B.overview.inviteTrend,color:"#67C23A",height:30,"is-smooth":!0},null,8,["data"])])]),(0,i.Lk)("div",u,[(0,i.Lk)("div",{class:(0,s.C4)(["stat-change",B.overview.inviteChange>=0?"up":"down"])},[(0,i.Lk)("i",{class:(0,s.C4)(B.overview.inviteChange>=0?"el-icon-top":"el-icon-bottom")},null,2),(0,i.eW)(" "+(0,s.v_)(Math.abs(B.overview.inviteChange)),1)],2),t[7]||(t[7]=(0,i.Lk)("div",{class:"stat-period"},"较上期",-1))])])),_:1})])),_:1}),(0,i.bF)(Y,{span:6},{default:(0,i.k6)((()=>[(0,i.bF)(J,{class:"stat-card"},{default:(0,i.k6)((()=>[t[10]||(t[10]=(0,i.Lk)("div",{class:"stat-header"},[(0,i.Lk)("div",{class:"stat-title"},"佣金发放")],-1)),(0,i.Lk)("div",g,[(0,i.Lk)("div",k,"¥"+(0,s.v_)(B.overview.totalCommission),1),(0,i.Lk)("div",b,[(0,i.bF)(G,{data:B.overview.commissionTrend,color:"#F56C6C",height:30,"is-smooth":!0},null,8,["data"])])]),(0,i.Lk)("div",p,[(0,i.Lk)("div",{class:(0,s.C4)(["stat-change",B.overview.commissionChange>=0?"up":"down"])},[(0,i.Lk)("i",{class:(0,s.C4)(B.overview.commissionChange>=0?"el-icon-top":"el-icon-bottom")},null,2),(0,i.eW)(" ¥"+(0,s.v_)(Math.abs(B.overview.commissionChange)),1)],2),t[9]||(t[9]=(0,i.Lk)("div",{class:"stat-period"},"较上期",-1))])])),_:1})])),_:1}),(0,i.bF)(Y,{span:6},{default:(0,i.k6)((()=>[(0,i.bF)(J,{class:"stat-card"},{default:(0,i.k6)((()=>[t[12]||(t[12]=(0,i.Lk)("div",{class:"stat-header"},[(0,i.Lk)("div",{class:"stat-title"},"提现总额")],-1)),(0,i.Lk)("div",w,[(0,i.Lk)("div",L,"¥"+(0,s.v_)(B.overview.totalWithdrawal),1),(0,i.Lk)("div",f,[(0,i.bF)(G,{data:B.overview.withdrawalTrend,color:"#E6A23C",height:30,"is-smooth":!0},null,8,["data"])])]),(0,i.Lk)("div",y,[(0,i.Lk)("div",{class:(0,s.C4)(["stat-change",B.overview.withdrawalChange>=0?"up":"down"])},[(0,i.Lk)("i",{class:(0,s.C4)(B.overview.withdrawalChange>=0?"el-icon-top":"el-icon-bottom")},null,2),(0,i.eW)(" ¥"+(0,s.v_)(Math.abs(B.overview.withdrawalChange)),1)],2),t[11]||(t[11]=(0,i.Lk)("div",{class:"stat-period"},"较上期",-1))])])),_:1})])),_:1})])),_:1}),(0,i.bF)(Z,{gutter:20,class:"chart-row"},{default:(0,i.k6)((()=>[(0,i.bF)(Y,{span:16},{default:(0,i.k6)((()=>[(0,i.bF)(J,{class:"chart-card"},{default:(0,i.k6)((()=>[(0,i.Lk)("div",C,[t[16]||(t[16]=(0,i.Lk)("div",{class:"chart-title"},"推广趋势",-1)),(0,i.Lk)("div",_,[(0,i.bF)(te,{modelValue:B.trendType,"onUpdate:modelValue":t[1]||(t[1]=e=>B.trendType=e),size:"mini",onChange:K.refreshTrend},{default:(0,i.k6)((()=>[(0,i.bF)(ee,{label:"users"},{default:(0,i.k6)((()=>t[13]||(t[13]=[(0,i.eW)("推广用户")]))),_:1}),(0,i.bF)(ee,{label:"commissions"},{default:(0,i.k6)((()=>t[14]||(t[14]=[(0,i.eW)("佣金金额")]))),_:1}),(0,i.bF)(ee,{label:"withdrawals"},{default:(0,i.k6)((()=>t[15]||(t[15]=[(0,i.eW)("提现金额")]))),_:1})])),_:1},8,["modelValue","onChange"]),(0,i.bF)(j,{modelValue:B.trendTimeRange,"onUpdate:modelValue":t[2]||(t[2]=e=>B.trendTimeRange=e),size:"mini",onChange:K.refreshTrend,style:{"margin-left":"10px"}},{default:(0,i.k6)((()=>[(0,i.bF)(Q,{label:"最近7天",value:"week"}),(0,i.bF)(Q,{label:"最近30天",value:"month"}),(0,i.bF)(Q,{label:"最近12个月",value:"year"})])),_:1},8,["modelValue","onChange"])])]),(0,i.Lk)("div",T,[(0,i.bo)(((0,i.uX)(),(0,i.CE)("div",F,[(0,i.bF)(ae,{ref:"trendChart",data:B.trendData,labels:B.trendLabels,height:300,"is-smooth":!0},null,8,["data","labels"])])),[[ne,B.trendLoading]])])])),_:1})])),_:1}),(0,i.bF)(Y,{span:8},{default:(0,i.k6)((()=>[(0,i.bF)(J,{class:"chart-card"},{default:(0,i.k6)((()=>[t[17]||(t[17]=(0,i.Lk)("div",{class:"chart-header"},[(0,i.Lk)("div",{class:"chart-title"},"推广员等级分布")],-1)),(0,i.Lk)("div",R,[(0,i.bo)(((0,i.uX)(),(0,i.CE)("div",A,[(0,i.bF)(ie,{ref:"distributionChart",data:B.distributionData,height:300},null,8,["data"])])),[[ne,B.distributionLoading]])])])),_:1})])),_:1})])),_:1}),(0,i.bF)(Z,{gutter:20,class:"ranking-row"},{default:(0,i.k6)((()=>[(0,i.bF)(Y,{span:12},{default:(0,i.k6)((()=>[(0,i.bF)(J,{class:"ranking-card"},{default:(0,i.k6)((()=>[(0,i.Lk)("div",z,[t[18]||(t[18]=(0,i.Lk)("div",{class:"ranking-title"},"佣金排行",-1)),(0,i.bF)(j,{modelValue:B.commissionRankTimeRange,"onUpdate:modelValue":t[3]||(t[3]=e=>B.commissionRankTimeRange=e),size:"mini",onChange:K.getCommissionRanking},{default:(0,i.k6)((()=>[(0,i.bF)(Q,{label:"本周",value:"week"}),(0,i.bF)(Q,{label:"本月",value:"month"}),(0,i.bF)(Q,{label:"今年",value:"year"}),(0,i.bF)(Q,{label:"全部",value:"all"})])),_:1},8,["modelValue","onChange"])]),(0,i.bo)(((0,i.uX)(),(0,i.CE)("div",E,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(B.commissionRanking,((e,t)=>((0,i.uX)(),(0,i.CE)("div",{class:"ranking-item",key:e.id},[(0,i.Lk)("div",{class:(0,s.C4)(["ranking-number",`rank-${t+1}`])},(0,s.v_)(t+1),3),(0,i.Lk)("div",$,[(0,i.bF)(se,{src:e.avatar||"/static/default-avatar.png",size:40},null,8,["src"])]),(0,i.Lk)("div",x,[(0,i.Lk)("div",S,(0,s.v_)(e.nickname),1),(0,i.Lk)("div",V,(0,s.v_)(K.getLevelName(e.level)),1)]),(0,i.Lk)("div",H,"¥"+(0,s.v_)(e.total_commission),1)])))),128)),0===B.commissionRanking.length?((0,i.uX)(),(0,i.CE)("div",X," 暂无数据 ")):(0,i.Q3)("",!0)])),[[ne,B.commissionRankLoading]])])),_:1})])),_:1}),(0,i.bF)(Y,{span:12},{default:(0,i.k6)((()=>[(0,i.bF)(J,{class:"ranking-card"},{default:(0,i.k6)((()=>[(0,i.Lk)("div",D,[t[19]||(t[19]=(0,i.Lk)("div",{class:"ranking-title"},"推广用户排行",-1)),(0,i.bF)(j,{modelValue:B.invitesRankTimeRange,"onUpdate:modelValue":t[4]||(t[4]=e=>B.invitesRankTimeRange=e),size:"mini",onChange:K.getInvitesRanking},{default:(0,i.k6)((()=>[(0,i.bF)(Q,{label:"本周",value:"week"}),(0,i.bF)(Q,{label:"本月",value:"month"}),(0,i.bF)(Q,{label:"今年",value:"year"}),(0,i.bF)(Q,{label:"全部",value:"all"})])),_:1},8,["modelValue","onChange"])]),(0,i.bo)(((0,i.uX)(),(0,i.CE)("div",W,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(B.invitesRanking,((e,t)=>((0,i.uX)(),(0,i.CE)("div",{class:"ranking-item",key:e.id},[(0,i.Lk)("div",{class:(0,s.C4)(["ranking-number",`rank-${t+1}`])},(0,s.v_)(t+1),3),(0,i.Lk)("div",I,[(0,i.bF)(se,{src:e.avatar||"/static/default-avatar.png",size:40},null,8,["src"])]),(0,i.Lk)("div",O,[(0,i.Lk)("div",N,(0,s.v_)(e.nickname),1),(0,i.Lk)("div",P,(0,s.v_)(K.getLevelName(e.level)),1)]),(0,i.Lk)("div",M,(0,s.v_)(e.total_invites)+" 人",1)])))),128)),0===B.invitesRanking.length?((0,i.uX)(),(0,i.CE)("div",U," 暂无数据 ")):(0,i.Q3)("",!0)])),[[ne,B.invitesRankLoading]])])),_:1})])),_:1})])),_:1})])}var B=a(2211);function K(e,t,a,n,r,o){return(0,i.uX)(),(0,i.CE)("div",{ref:"chart",style:(0,s.Tr)({height:a.height+"px"}),class:"trend-chart"},null,4)}var Q=a(2032);const j={data(){return{$_sidebarElm:null,$_resizeHandler:null}},mounted(){this.initListener()},activated(){this.$_resizeHandler||this.initListener(),this.$_resizeHandler&&setTimeout((()=>{this.$_resizeHandler()}),200)},beforeDestroy(){this.destroyListener()},deactivated(){this.destroyListener()},methods:{$_debounce(e,t){let a=null;return function(){const i=this,s=arguments;clearTimeout(a),a=setTimeout((function(){e.apply(i,s)}),t)}},initListener(){this.$_resizeHandler=this.$_debounce((()=>{this.chart&&this.chart.resize()}),100),window.addEventListener("resize",this.$_resizeHandler),this.$_sidebarElm=document.getElementsByClassName("sidebar-container")[0],this.$_sidebarElm&&this.$_sidebarElm.addEventListener("transitionend",this.$_resizeHandler)},destroyListener(){window.removeEventListener("resize",this.$_resizeHandler),this.$_resizeHandler=null,this.$_sidebarElm&&this.$_sidebarElm.removeEventListener("transitionend",this.$_resizeHandler)}}},G={name:"TrendChart",mixins:[j],props:{data:{type:Array,required:!0,default:()=>[]},labels:{type:Array,default:()=>[]},color:{type:String,default:"#409EFF"},height:{type:Number,default:300},isSmooth:{type:Boolean,default:!1}},data(){return{chart:null}},watch:{data:{handler(){this.initChart()},deep:!0},labels:{handler(){this.initChart()},deep:!0}},mounted(){this.initChart()},beforeDestroy(){this.chart&&(this.chart.dispose(),this.chart=null)},methods:{initChart(){if(!this.$refs.chart)return;this.chart=Q.Ts(this.$refs.chart);const e=Array.isArray(this.data)?this.data.map((e=>void 0===e||null===e?0:e)):[],t={grid:{top:10,bottom:10,left:10,right:10,containLabel:!1},xAxis:{type:"category",data:this.labels.length?this.labels:[],show:this.height>100,axisLine:{show:this.height>100},axisTick:{show:this.height>100},axisLabel:{show:this.height>100}},yAxis:{type:"value",show:this.height>100,axisLine:{show:this.height>100},axisTick:{show:this.height>100},axisLabel:{show:this.height>100},splitLine:{show:this.height>100}},series:[{data:e,type:"line",smooth:this.isSmooth,symbol:this.height>100?"circle":"none",symbolSize:4,lineStyle:{width:2,color:this.color},itemStyle:{color:this.color},areaStyle:this.height>100?{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:this.color+"40"},{offset:1,color:this.color+"00"}]}}:null}],tooltip:{trigger:"axis",show:this.height>100,formatter:"{b}: {c}"}};this.chart.setOption(t)}}};var J=a(1241);const Y=(0,J.A)(G,[["render",K],["__scopeId","data-v-17e54942"]]),Z=Y;function ee(e,t,a,n,r,o){return(0,i.uX)(),(0,i.CE)("div",{class:(0,s.C4)(a.className),style:(0,s.Tr)({height:a.height,width:a.width}),ref:"chartRef"},null,6)}var te=a(6301);const ae={name:"PieChart",props:{className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"300px"},data:{type:Array,required:!0},title:{type:String,default:""},showLegend:{type:Boolean,default:!0},colors:{type:Array,default:()=>[]}},data(){return{chart:null,resizeHandler:null}},watch:{data:{deep:!0,handler(e){this.$nextTick((()=>{this.setOptions(e)}))}}},mounted(){this.resizeHandler=(0,te.A)((()=>{this.chart&&this.chart.resize()}),100),this.$nextTick((()=>{this.initChart()})),window.addEventListener("resize",this.resizeHandler)},beforeUnmount(){this.chart&&(window.removeEventListener("resize",this.resizeHandler),this.chart.dispose(),this.chart=null)},methods:{initChart(){this.chart&&this.chart.dispose(),this.chart=Q.Ts(this.$refs.chartRef),this.setOptions(this.data)},setOptions(e){if(this.chart&&e&&0!==e.length)try{const t=e.map((e=>({name:e.name||"未命名",value:e.value||0}))),a={title:this.title?{text:this.title,left:"center"}:null,tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c} ({d}%)"},legend:this.showLegend?{type:"scroll",orient:"vertical",right:10,top:20,bottom:20}:null,series:[{name:this.title,type:"pie",radius:"55%",center:["50%","50%"],data:t,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};this.colors&&this.colors.length>0&&(a.color=this.colors),requestAnimationFrame((()=>{this.chart.setOption(a)}))}catch(t){console.error("PieChart 设置选项时出错:",t)}}}},ie=(0,J.A)(ae,[["render",ee],["__scopeId","data-v-6546550b"]]),se=ie,ne={name:"PromotionStatistics",components:{TrendChart:Z,PieChart:se},data(){return{overviewLoading:!1,overviewTimeRange:"week",overview:{totalPromoters:0,newInvites:0,totalCommission:"0.00",totalWithdrawal:"0.00",promoterChange:0,inviteChange:0,commissionChange:0,withdrawalChange:0,promoterTrend:[],inviteTrend:[],commissionTrend:[],withdrawalTrend:[]},trendLoading:!1,trendType:"users",trendTimeRange:"week",trendData:[],trendLabels:[],distributionLoading:!1,distributionData:[],commissionRankLoading:!1,commissionRankTimeRange:"month",commissionRanking:[],invitesRankLoading:!1,invitesRankTimeRange:"month",invitesRanking:[],levelMap:{1:"初级推广员",2:"中级推广员",3:"高级推广员",4:"资深推广员"}}},created(){this.init()},methods:{async init(){this.refreshOverview(),this.refreshTrend(),this.getPromoterDistribution(),this.getCommissionRanking(),this.getInvitesRanking()},async refreshOverview(){this.overviewLoading=!0;try{const e=await(0,B.R7)({timeRange:this.overviewTimeRange});if(200===e.code){const t=e.data;Array.isArray(t.promoterTrend)?t.promoterTrend=t.promoterTrend.map((e=>void 0===e||null===e?0:e)):t.promoterTrend=[],Array.isArray(t.inviteTrend)?t.inviteTrend=t.inviteTrend.map((e=>void 0===e||null===e?0:e)):t.inviteTrend=[],Array.isArray(t.commissionTrend)?t.commissionTrend=t.commissionTrend.map((e=>void 0===e||null===e?0:e)):t.commissionTrend=[],Array.isArray(t.withdrawalTrend)?t.withdrawalTrend=t.withdrawalTrend.map((e=>void 0===e||null===e?0:e)):t.withdrawalTrend=[],this.overview=t}}catch(e){console.error("获取概览数据失败",e),this.overview.promoterTrend=[],this.overview.inviteTrend=[],this.overview.commissionTrend=[],this.overview.withdrawalTrend=[]}finally{this.overviewLoading=!1}},async refreshTrend(){this.trendLoading=!0;try{const e=await(0,B.zK)({type:this.trendType,timeRange:this.trendTimeRange});200===e.code&&(this.trendData=Array.isArray(e.data.values)?e.data.values.map((e=>void 0===e||null===e?0:e)):[],this.trendLabels=Array.isArray(e.data.labels)?e.data.labels:[])}catch(e){console.error("获取趋势数据失败",e),this.trendData=[],this.trendLabels=[]}finally{this.trendLoading=!1}},async getPromoterDistribution(){this.distributionLoading=!0;try{const e=await(0,B.tb)();200===e.code&&(this.distributionData=e.data.map((e=>({name:this.levelMap[e.level],value:e.count,itemStyle:{color:["#409EFF","#67C23A","#E6A23C","#F56C6C"][e.level-1]}}))))}catch(e){console.error("获取分布数据失败",e)}finally{this.distributionLoading=!1}},async getCommissionRanking(){this.commissionRankLoading=!0;try{const e=await(0,B.q_)({timeRange:this.commissionRankTimeRange,limit:10});200===e.code&&(this.commissionRanking=e.data)}catch(e){console.error("获取佣金排行失败",e)}finally{this.commissionRankLoading=!1}},async getInvitesRanking(){this.invitesRankLoading=!0;try{const e=await(0,B.fW)({timeRange:this.invitesRankTimeRange,limit:10});200===e.code&&(this.invitesRanking=e.data)}catch(e){console.error("获取推广用户排行失败",e)}finally{this.invitesRankLoading=!1}},getLevelName(e){return this.levelMap[e]||"未知等级"}}},re=(0,J.A)(ne,[["render",q],["__scopeId","data-v-ddb9f650"]]),oe=re}}]);