"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[5772],{5772:(e,a,l)=>{l.r(a),l.d(a,{default:()=>F});var t=l(6768),o=l(4232);const i={class:"page-container"},r={class:"action-container"},s={class:"search-box"},n={class:"button-group"},d={class:"pagination-container"},u={class:"dialog-footer"},m=["src"],p={class:"dialog-footer"};function b(e,a,l,b,c,h){const g=(0,t.g2)("el-input"),F=(0,t.g2)("el-option"),k=(0,t.g2)("el-select"),f=(0,t.g2)("el-cascader"),v=(0,t.g2)("el-button"),y=(0,t.g2)("el-table-column"),V=(0,t.g2)("el-tag"),w=(0,t.g2)("el-table"),_=(0,t.g2)("el-pagination"),C=(0,t.g2)("el-tab-pane"),T=(0,t.g2)("el-tabs"),N=(0,t.g2)("el-form-item"),P=(0,t.g2)("el-col"),W=(0,t.g2)("el-row"),x=(0,t.g2)("el-input-number"),U=(0,t.g2)("el-radio"),q=(0,t.g2)("el-radio-group"),L=(0,t.g2)("el-upload"),S=(0,t.g2)("el-form"),z=(0,t.g2)("el-dialog"),$=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",i,[(0,t.Lk)("div",r,[(0,t.Lk)("div",s,[(0,t.bF)(g,{modelValue:c.queryParams.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>c.queryParams.keyword=e),placeholder:"请输入驿站名称/联系人",clearable:"",style:{width:"200px"},class:"mr-10"},null,8,["modelValue"]),(0,t.bF)(k,{modelValue:c.queryParams.status,"onUpdate:modelValue":a[1]||(a[1]=e=>c.queryParams.status=e),placeholder:"转让状态",clearable:"",class:"mr-10",style:{width:"120px"}},{default:(0,t.k6)((()=>[(0,t.bF)(F,{label:"待审核",value:"0"}),(0,t.bF)(F,{label:"已上架",value:"1"}),(0,t.bF)(F,{label:"已交易",value:"2"}),(0,t.bF)(F,{label:"已下架",value:"3"}),(0,t.bF)(F,{label:"已关闭",value:"4"})])),_:1},8,["modelValue"]),(0,t.bF)(k,{modelValue:c.queryParams.type,"onUpdate:modelValue":a[2]||(a[2]=e=>c.queryParams.type=e),placeholder:"驿站类型",clearable:"",class:"mr-10",style:{width:"120px"}},{default:(0,t.k6)((()=>[(0,t.bF)(F,{label:"快递驿站",value:"1"}),(0,t.bF)(F,{label:"社区驿站",value:"2"}),(0,t.bF)(F,{label:"校园驿站",value:"3"}),(0,t.bF)(F,{label:"写字楼驿站",value:"4"})])),_:1},8,["modelValue"]),(0,t.bF)(f,{modelValue:c.queryParams.region,"onUpdate:modelValue":a[3]||(a[3]=e=>c.queryParams.region=e),options:c.regionOptions,placeholder:"选择地区",clearable:"",class:"mr-10",style:{width:"220px"}},null,8,["modelValue","options"]),(0,t.bF)(v,{type:"primary",onClick:h.handleQuery},{default:(0,t.k6)((()=>a[25]||(a[25]=[(0,t.eW)("搜索")]))),_:1},8,["onClick"]),(0,t.bF)(v,{onClick:h.resetQuery},{default:(0,t.k6)((()=>a[26]||(a[26]=[(0,t.eW)("重置")]))),_:1},8,["onClick"])]),(0,t.Lk)("div",n,[(0,t.bF)(v,{type:"primary",onClick:h.handleAdd},{default:(0,t.k6)((()=>a[27]||(a[27]=[(0,t.eW)("新增转让")]))),_:1},8,["onClick"]),(0,t.bF)(v,{type:"success",onClick:h.handleExport},{default:(0,t.k6)((()=>a[28]||(a[28]=[(0,t.eW)("导出")]))),_:1},8,["onClick"])])]),(0,t.bF)(T,{modelValue:c.activeName,"onUpdate:modelValue":a[6]||(a[6]=e=>c.activeName=e),onTabClick:h.handleTabClick},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(c.statusTabs,(e=>((0,t.uX)(),(0,t.Wv)(C,{key:e.name,label:e.label,name:e.name},{default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(w,{data:c.transferList,onSelectionChange:h.handleSelectionChange},{default:(0,t.k6)((()=>[(0,t.bF)(y,{type:"selection",width:"55"}),(0,t.bF)(y,{type:"index",label:"#",width:"50"}),(0,t.bF)(y,{prop:"stationName",label:"驿站名称","min-width":"120","show-overflow-tooltip":""}),(0,t.bF)(y,{prop:"stationType",label:"驿站类型",width:"100"},{default:(0,t.k6)((e=>[(0,t.eW)((0,o.v_)(c.stationTypeMap[e.row.stationType]),1)])),_:1}),(0,t.bF)(y,{prop:"region",label:"所在地区","min-width":"140","show-overflow-tooltip":""}),(0,t.bF)(y,{prop:"address",label:"详细地址","min-width":"160","show-overflow-tooltip":""}),(0,t.bF)(y,{prop:"price",label:"转让价格",width:"100"},{default:(0,t.k6)((e=>[(0,t.eW)((0,o.v_)(e.row.price)+"元 ",1)])),_:1}),(0,t.bF)(y,{prop:"contactName",label:"联系人",width:"100"}),(0,t.bF)(y,{prop:"contactPhone",label:"联系电话",width:"120"}),(0,t.bF)(y,{prop:"createTime",label:"发布时间",width:"160",sortable:""}),(0,t.bF)(y,{prop:"status",label:"状态",width:"100"},{default:(0,t.k6)((e=>[(0,t.bF)(V,{type:h.getStatusTag(e.row.status)},{default:(0,t.k6)((()=>[(0,t.eW)((0,o.v_)(c.statusMap[e.row.status]),1)])),_:2},1032,["type"])])),_:1}),(0,t.bF)(y,{label:"操作",width:"220",fixed:"right"},{default:(0,t.k6)((e=>[(0,t.bF)(v,{type:"text",onClick:a=>h.handleView(e.row)},{default:(0,t.k6)((()=>a[29]||(a[29]=[(0,t.eW)("查看")]))),_:2},1032,["onClick"]),"2"!==e.row.status?((0,t.uX)(),(0,t.Wv)(v,{key:0,type:"text",onClick:a=>h.handleEdit(e.row)},{default:(0,t.k6)((()=>a[30]||(a[30]=[(0,t.eW)("编辑")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0),"2"!==e.row.status?((0,t.uX)(),(0,t.Wv)(v,{key:1,type:"text",onClick:a=>h.handleDelete(e.row)},{default:(0,t.k6)((()=>a[31]||(a[31]=[(0,t.eW)("删除")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0),"1"===e.row.status?((0,t.uX)(),(0,t.Wv)(v,{key:2,type:"text",onClick:a=>h.handleStatus(e.row,"2")},{default:(0,t.k6)((()=>a[32]||(a[32]=[(0,t.eW)("标记交易")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0),"3"===e.row.status||"4"===e.row.status?((0,t.uX)(),(0,t.Wv)(v,{key:3,type:"text",onClick:a=>h.handleStatus(e.row,"1")},{default:(0,t.k6)((()=>a[33]||(a[33]=[(0,t.eW)("上架")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0),"1"===e.row.status?((0,t.uX)(),(0,t.Wv)(v,{key:4,type:"text",onClick:a=>h.handleStatus(e.row,"3")},{default:(0,t.k6)((()=>a[34]||(a[34]=[(0,t.eW)("下架")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0),"1"===e.row.status?((0,t.uX)(),(0,t.Wv)(v,{key:5,type:"text",onClick:a=>h.handleViolation(e.row)},{default:(0,t.k6)((()=>a[35]||(a[35]=[(0,t.eW)("违规")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0)])),_:1})])),_:1},8,["data","onSelectionChange"])),[[$,c.loading]]),(0,t.Lk)("div",d,[(0,t.bF)(_,{"current-page":c.queryParams.pageNum,"onUpdate:currentPage":a[4]||(a[4]=e=>c.queryParams.pageNum=e),"page-size":c.queryParams.pageSize,"onUpdate:pageSize":a[5]||(a[5]=e=>c.queryParams.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:c.total,onSizeChange:h.handleSizeChange,onCurrentChange:h.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])])),_:2},1032,["label","name"])))),128))])),_:1},8,["modelValue","onTabClick"]),(0,t.bF)(z,{title:c.dialogTitle,modelValue:c.dialogVisible,"onUpdate:modelValue":a[19]||(a[19]=e=>c.dialogVisible=e),width:"700px","append-to-body":""},{footer:(0,t.k6)((()=>[(0,t.Lk)("div",u,[(0,t.bF)(v,{onClick:a[18]||(a[18]=e=>c.dialogVisible=!1)},{default:(0,t.k6)((()=>a[42]||(a[42]=[(0,t.eW)("取 消")]))),_:1}),(0,t.bF)(v,{type:"primary",onClick:h.submitForm},{default:(0,t.k6)((()=>a[43]||(a[43]=[(0,t.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,t.k6)((()=>[(0,t.bF)(S,{ref:"transferForm",model:c.transferForm,rules:c.rules,"label-width":"100px"},{default:(0,t.k6)((()=>[(0,t.bF)(W,{gutter:20},{default:(0,t.k6)((()=>[(0,t.bF)(P,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(N,{label:"驿站名称",prop:"stationName"},{default:(0,t.k6)((()=>[(0,t.bF)(g,{modelValue:c.transferForm.stationName,"onUpdate:modelValue":a[7]||(a[7]=e=>c.transferForm.stationName=e),placeholder:"请输入驿站名称"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(P,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(N,{label:"驿站类型",prop:"stationType"},{default:(0,t.k6)((()=>[(0,t.bF)(k,{modelValue:c.transferForm.stationType,"onUpdate:modelValue":a[8]||(a[8]=e=>c.transferForm.stationType=e),placeholder:"请选择驿站类型",style:{width:"100%"}},{default:(0,t.k6)((()=>[(0,t.bF)(F,{label:"快递驿站",value:"1"}),(0,t.bF)(F,{label:"社区驿站",value:"2"}),(0,t.bF)(F,{label:"校园驿站",value:"3"}),(0,t.bF)(F,{label:"写字楼驿站",value:"4"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),(0,t.bF)(N,{label:"所在地区",prop:"region"},{default:(0,t.k6)((()=>[(0,t.bF)(f,{modelValue:c.transferForm.region,"onUpdate:modelValue":a[9]||(a[9]=e=>c.transferForm.region=e),options:c.regionOptions,placeholder:"请选择所在地区",style:{width:"100%"}},null,8,["modelValue","options"])])),_:1}),(0,t.bF)(N,{label:"详细地址",prop:"address"},{default:(0,t.k6)((()=>[(0,t.bF)(g,{modelValue:c.transferForm.address,"onUpdate:modelValue":a[10]||(a[10]=e=>c.transferForm.address=e),placeholder:"请输入详细地址"},null,8,["modelValue"])])),_:1}),(0,t.bF)(W,{gutter:20},{default:(0,t.k6)((()=>[(0,t.bF)(P,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(N,{label:"转让价格",prop:"price"},{default:(0,t.k6)((()=>[(0,t.bF)(x,{modelValue:c.transferForm.price,"onUpdate:modelValue":a[11]||(a[11]=e=>c.transferForm.price=e),min:0,max:9999999,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(P,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(N,{label:"月营业额",prop:"monthlyRevenue"},{default:(0,t.k6)((()=>[(0,t.bF)(x,{modelValue:c.transferForm.monthlyRevenue,"onUpdate:modelValue":a[12]||(a[12]=e=>c.transferForm.monthlyRevenue=e),min:0,max:9999999,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),(0,t.bF)(W,{gutter:20},{default:(0,t.k6)((()=>[(0,t.bF)(P,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(N,{label:"联系人",prop:"contactName"},{default:(0,t.k6)((()=>[(0,t.bF)(g,{modelValue:c.transferForm.contactName,"onUpdate:modelValue":a[13]||(a[13]=e=>c.transferForm.contactName=e),placeholder:"请输入联系人姓名"},null,8,["modelValue"])])),_:1})])),_:1}),(0,t.bF)(P,{span:12},{default:(0,t.k6)((()=>[(0,t.bF)(N,{label:"联系电话",prop:"contactPhone"},{default:(0,t.k6)((()=>[(0,t.bF)(g,{modelValue:c.transferForm.contactPhone,"onUpdate:modelValue":a[14]||(a[14]=e=>c.transferForm.contactPhone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),(0,t.bF)(N,{label:"转让原因",prop:"reason"},{default:(0,t.k6)((()=>[(0,t.bF)(g,{modelValue:c.transferForm.reason,"onUpdate:modelValue":a[15]||(a[15]=e=>c.transferForm.reason=e),type:"textarea",placeholder:"请输入转让原因"},null,8,["modelValue"])])),_:1}),(0,t.bF)(N,{label:"补充说明",prop:"description"},{default:(0,t.k6)((()=>[(0,t.bF)(g,{modelValue:c.transferForm.description,"onUpdate:modelValue":a[16]||(a[16]=e=>c.transferForm.description=e),type:"textarea",placeholder:"请输入补充说明"},null,8,["modelValue"])])),_:1}),(0,t.bF)(N,{label:"状态",prop:"status"},{default:(0,t.k6)((()=>[(0,t.bF)(q,{modelValue:c.transferForm.status,"onUpdate:modelValue":a[17]||(a[17]=e=>c.transferForm.status=e)},{default:(0,t.k6)((()=>[(0,t.bF)(U,{label:"0"},{default:(0,t.k6)((()=>a[36]||(a[36]=[(0,t.eW)("待审核")]))),_:1}),(0,t.bF)(U,{label:"1"},{default:(0,t.k6)((()=>a[37]||(a[37]=[(0,t.eW)("已上架")]))),_:1}),(0,t.bF)(U,{label:"3"},{default:(0,t.k6)((()=>a[38]||(a[38]=[(0,t.eW)("已下架")]))),_:1}),(0,t.bF)(U,{label:"4"},{default:(0,t.k6)((()=>a[39]||(a[39]=[(0,t.eW)("已关闭")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(N,{label:"图片上传"},{default:(0,t.k6)((()=>[(0,t.bF)(L,{action:"#","list-type":"picture-card","auto-upload":!1,"file-list":c.fileList,"on-preview":h.handlePreview,"on-remove":h.handleRemove,"on-change":h.handleChange},{tip:(0,t.k6)((()=>a[40]||(a[40]=[(0,t.Lk)("div",{class:"el-upload__tip"},"请上传驿站门店照片、内部环境等相关图片",-1)]))),default:(0,t.k6)((()=>[a[41]||(a[41]=(0,t.Lk)("i",{class:"el-icon-plus"},null,-1))])),_:1},8,["file-list","on-preview","on-remove","on-change"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"]),(0,t.bF)(z,{modelValue:c.previewVisible,"onUpdate:modelValue":a[20]||(a[20]=e=>c.previewVisible=e),title:"图片预览"},{default:(0,t.k6)((()=>[(0,t.Lk)("img",{src:c.previewUrl,alt:"Preview Image",style:{width:"100%"}},null,8,m)])),_:1},8,["modelValue"]),(0,t.bF)(z,{title:"违规下架",modelValue:c.violationDialogVisible,"onUpdate:modelValue":a[24]||(a[24]=e=>c.violationDialogVisible=e),width:"500px","append-to-body":""},{footer:(0,t.k6)((()=>[(0,t.Lk)("div",p,[(0,t.bF)(v,{onClick:a[23]||(a[23]=e=>c.violationDialogVisible=!1)},{default:(0,t.k6)((()=>a[44]||(a[44]=[(0,t.eW)("取 消")]))),_:1}),(0,t.bF)(v,{type:"primary",onClick:h.submitViolation},{default:(0,t.k6)((()=>a[45]||(a[45]=[(0,t.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,t.k6)((()=>[(0,t.bF)(S,{ref:"violationForm",model:c.violationForm,"label-width":"80px"},{default:(0,t.k6)((()=>[(0,t.bF)(N,{label:"违规类型",prop:"reason"},{default:(0,t.k6)((()=>[(0,t.bF)(k,{modelValue:c.violationForm.reason,"onUpdate:modelValue":a[21]||(a[21]=e=>c.violationForm.reason=e),placeholder:"请选择违规类型",style:{width:"100%"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(c.violationOptions,(e=>((0,t.uX)(),(0,t.Wv)(F,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(N,{label:"补充说明",prop:"remark"},{default:(0,t.k6)((()=>[(0,t.bF)(g,{modelValue:c.violationForm.remark,"onUpdate:modelValue":a[22]||(a[22]=e=>c.violationForm.remark=e),type:"textarea",placeholder:"请输入补充说明"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}const c={name:"TransferList",data(){return{activeName:"all",statusTabs:[{label:"全部转让",name:"all"},{label:"待审核",name:"0"},{label:"已上架",name:"1"},{label:"已交易",name:"2"},{label:"已下架",name:"3"},{label:"已关闭",name:"4"}],loading:!1,selectedIds:[],total:0,stationTypeMap:{1:"快递驿站",2:"社区驿站",3:"校园驿站",4:"写字楼驿站"},statusMap:{0:"待审核",1:"已上架",2:"已交易",3:"已下架",4:"已关闭"},violationOptions:[{label:"虚假信息",value:"虚假信息"},{label:"信息不完整",value:"信息不完整"},{label:"违反平台规则",value:"违反平台规则"},{label:"低质内容",value:"低质内容"},{label:"其他违规",value:"其他违规"}],regionOptions:[{value:"北京市",label:"北京市",children:[{value:"北京市",label:"北京市",children:[{value:"朝阳区",label:"朝阳区"},{value:"海淀区",label:"海淀区"},{value:"东城区",label:"东城区"},{value:"西城区",label:"西城区"},{value:"丰台区",label:"丰台区"}]}]},{value:"上海市",label:"上海市",children:[{value:"上海市",label:"上海市",children:[{value:"黄浦区",label:"黄浦区"},{value:"徐汇区",label:"徐汇区"},{value:"长宁区",label:"长宁区"},{value:"静安区",label:"静安区"},{value:"浦东新区",label:"浦东新区"}]}]}],transferList:[{id:1,stationName:"海淀区中关村社区驿站",stationType:"2",region:"北京市 北京市 海淀区",address:"北京市海淀区中关村大街123号",price:25e3,monthlyRevenue:18e3,contactName:"张先生",contactPhone:"13800138001",reason:"个人原因，无法继续经营",description:"该驿站位于中关村科技园区内，日均快递量200+，客流量大，接手可立即营业。",createTime:"2023-04-15 10:30:00",status:"1"},{id:2,stationName:"朝阳区望京SOHO快递驿站",stationType:"4",region:"北京市 北京市 朝阳区",address:"北京市朝阳区望京SOHO T1",price:35e3,monthlyRevenue:22e3,contactName:"李先生",contactPhone:"13800138002",reason:"另有发展，转让驿站",description:"位于写字楼内的快递驿站，周边写字楼多，客流量稳定，月收入2万以上。",createTime:"2023-04-16 11:20:00",status:"0"},{id:3,stationName:"北京大学校内驿站",stationType:"3",region:"北京市 北京市 海淀区",address:"北京市海淀区颐和园路5号",price:3e4,monthlyRevenue:15e3,contactName:"王先生",contactPhone:"13800138003",reason:"已找到其他工作，无暇经营",description:"校内驿站，寄件、取件和中转业务量大，具有垄断优势，收益稳定。",createTime:"2023-04-14 09:45:00",status:"2"}],queryParams:{pageNum:1,pageSize:10,keyword:"",status:"",type:"",region:null},dialogTitle:"",dialogVisible:!1,transferForm:{id:void 0,stationName:"",stationType:"",region:[],address:"",price:0,monthlyRevenue:0,contactName:"",contactPhone:"",reason:"",description:"",status:"0"},rules:{stationName:[{required:!0,message:"驿站名称不能为空",trigger:"blur"}],stationType:[{required:!0,message:"驿站类型不能为空",trigger:"change"}],region:[{required:!0,message:"所在地区不能为空",trigger:"change"}],address:[{required:!0,message:"详细地址不能为空",trigger:"blur"}],price:[{required:!0,message:"转让价格不能为空",trigger:"blur"}],contactName:[{required:!0,message:"联系人不能为空",trigger:"blur"}],contactPhone:[{required:!0,message:"联系电话不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],reason:[{required:!0,message:"转让原因不能为空",trigger:"blur"}]},previewVisible:!1,previewUrl:"",fileList:[],violationDialogVisible:!1,violationForm:{id:void 0,reason:"",remark:""}}},created(){this.getList()},methods:{getStatusTag(e){const a={0:"warning",1:"success",2:"info",3:"danger",4:"info"};return a[e]||""},getList(){this.loading=!0;const e={...this.queryParams};"all"!==this.activeName&&(e.status=this.activeName),setTimeout((()=>{this.total=this.transferList.length,this.loading=!1}),500)},handleTabClick(){this.queryParams.pageNum=1,this.getList()},handleQuery(){this.queryParams.pageNum=1,this.getList()},resetQuery(){this.queryParams={pageNum:1,pageSize:10,keyword:"",status:"",type:"",region:null},this.handleQuery()},handleSelectionChange(e){this.selectedIds=e.map((e=>e.id))},handleAdd(){this.dialogTitle="新增驿站转让",this.resetForm(),this.dialogVisible=!0},handleEdit(e){this.dialogTitle="编辑驿站转让",this.resetForm();const a=e.region?e.region.split(" "):[];this.transferForm={...e,region:a},this.fileList=[],this.dialogVisible=!0},handleDelete(e){this.$confirm(`确认删除驿站转让信息"${e.stationName}"?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{console.log("删除ID：",e.id),this.$message({type:"success",message:"删除成功!"})})).catch((()=>{}))},handleView(e){this.$router.push(`/transfer/detail/${e.id}`)},handleStatus(e,a){let l="";switch(a){case"1":l="上架";break;case"2":l="标记为已交易";break;case"3":l="下架";break;case"4":l="关闭";break}this.$confirm(`确认要${l}驿站转让"${e.stationName}"?`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{console.log("更新状态ID：",e.id,"状态：",a),this.$message({type:"success",message:"操作成功!"})})).catch((()=>{}))},handleViolation(e){this.violationForm={id:e.id,reason:"",remark:""},this.violationDialogVisible=!0},submitViolation(){this.violationForm.reason?(console.log("违规下架ID：",this.violationForm.id),console.log("违规原因：",this.violationForm.reason),console.log("补充说明：",this.violationForm.remark),this.violationDialogVisible=!1,this.$message({type:"success",message:"违规下架成功!"})):this.$message({type:"warning",message:"请选择违规类型"})},handleExport(){this.$message({type:"success",message:"导出成功!"})},handlePreview(e){this.previewUrl=e.url||URL.createObjectURL(e.raw),this.previewVisible=!0},handleRemove(e,a){this.fileList=a},handleChange(e,a){this.fileList=a},submitForm(){this.$refs.transferForm.validate((e=>{if(e){const e=this.transferForm.region.join(" "),a={...this.transferForm,region:e};this.transferForm.id?console.log("更新数据：",a):console.log("新增数据：",a),this.dialogVisible=!1,this.$message({type:"success",message:this.transferForm.id?"修改成功!":"新增成功!"})}}))},resetForm(){this.fileList=[],this.transferForm={id:void 0,stationName:"",stationType:"",region:[],address:"",price:0,monthlyRevenue:0,contactName:"",contactPhone:"",reason:"",description:"",status:"0"}},handleSizeChange(e){this.queryParams.pageSize=e,this.getList()},handleCurrentChange(e){this.queryParams.pageNum=e,this.getList()}}};var h=l(1241);const g=(0,h.A)(c,[["render",b],["__scopeId","data-v-1e381094"]]),F=g}}]);