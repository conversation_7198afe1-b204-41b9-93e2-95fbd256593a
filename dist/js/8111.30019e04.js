"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[8111],{492:(e,l,o)=>{o.r(l),o.d(l,{default:()=>h});var t=o(6768),i=o(4232);const a={class:"page-container"},n={class:"card-header"},r={class:"dialog-footer"},s={class:"icon-list"},c=["onClick"],d={class:"dialog-footer"};function m(e,l,o,m,u,p){const g=(0,t.g2)("el-button"),h=(0,t.g2)("el-table-column"),b=(0,t.g2)("el-tag"),f=(0,t.g2)("el-table"),k=(0,t.g2)("el-card"),y=(0,t.g2)("el-cascader"),V=(0,t.g2)("el-form-item"),F=(0,t.g2)("el-input"),v=(0,t.g2)("el-input-number"),_=(0,t.g2)("el-radio"),C=(0,t.g2)("el-radio-group"),w=(0,t.g2)("el-form"),x=(0,t.g2)("el-dialog"),T=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",a,[(0,t.bF)(k,{class:"box-card"},{header:(0,t.k6)((()=>[(0,t.Lk)("div",n,[l[12]||(l[12]=(0,t.Lk)("span",null,"设备分类管理",-1)),(0,t.bF)(g,{type:"primary",onClick:p.handleAdd},{default:(0,t.k6)((()=>l[11]||(l[11]=[(0,t.eW)("新增分类")]))),_:1},8,["onClick"])])])),default:(0,t.k6)((()=>[(0,t.bo)(((0,t.uX)(),(0,t.Wv)(f,{data:u.categoryList,"row-key":"id",border:"","tree-props":{children:"children"}},{default:(0,t.k6)((()=>[(0,t.bF)(h,{prop:"name",label:"分类名称","min-width":"200"}),(0,t.bF)(h,{prop:"code",label:"分类编码",width:"150"}),(0,t.bF)(h,{prop:"sort",label:"排序",width:"100",align:"center"}),(0,t.bF)(h,{prop:"createTime",label:"创建时间",width:"180",align:"center"}),(0,t.bF)(h,{prop:"status",label:"状态",width:"100",align:"center"},{default:(0,t.k6)((e=>[(0,t.bF)(b,{type:"0"===e.row.status?"danger":"success"},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)("0"===e.row.status?"停用":"正常"),1)])),_:2},1032,["type"])])),_:1}),(0,t.bF)(h,{label:"操作",width:"250",align:"center",fixed:"right"},{default:(0,t.k6)((e=>[e.row.level<3?((0,t.uX)(),(0,t.Wv)(g,{key:0,type:"text",onClick:l=>p.handleAdd(e.row)},{default:(0,t.k6)((()=>l[13]||(l[13]=[(0,t.eW)("添加子分类")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0),(0,t.bF)(g,{type:"text",onClick:l=>p.handleUpdate(e.row)},{default:(0,t.k6)((()=>l[14]||(l[14]=[(0,t.eW)("修改")]))),_:2},1032,["onClick"]),e.row.children&&0===e.row.children.length?((0,t.uX)(),(0,t.Wv)(g,{key:1,type:"text",onClick:l=>p.handleStatus(e.row)},{default:(0,t.k6)((()=>[(0,t.eW)((0,i.v_)("0"===e.row.status?"启用":"停用"),1)])),_:2},1032,["onClick"])):(0,t.Q3)("",!0),e.row.children&&0===e.row.children.length?((0,t.uX)(),(0,t.Wv)(g,{key:2,type:"text",onClick:l=>p.handleDelete(e.row)},{default:(0,t.k6)((()=>l[15]||(l[15]=[(0,t.eW)("删除")]))),_:2},1032,["onClick"])):(0,t.Q3)("",!0)])),_:1})])),_:1},8,["data"])),[[T,u.loading]])])),_:1}),(0,t.bF)(x,{title:u.dialogTitle,modelValue:u.dialogVisible,"onUpdate:modelValue":l[8]||(l[8]=e=>u.dialogVisible=e),width:"500px","append-to-body":""},{footer:(0,t.k6)((()=>[(0,t.Lk)("div",r,[(0,t.bF)(g,{onClick:l[7]||(l[7]=e=>u.dialogVisible=!1)},{default:(0,t.k6)((()=>l[18]||(l[18]=[(0,t.eW)("取 消")]))),_:1}),(0,t.bF)(g,{type:"primary",onClick:p.submitForm},{default:(0,t.k6)((()=>l[19]||(l[19]=[(0,t.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,t.k6)((()=>[(0,t.bF)(w,{ref:"categoryForm",model:u.form,rules:u.rules,"label-width":"80px"},{default:(0,t.k6)((()=>[0!==u.form.parentId?((0,t.uX)(),(0,t.Wv)(V,{key:0,label:"上级分类",prop:"parentId"},{default:(0,t.k6)((()=>[(0,t.bF)(y,{modelValue:u.form.parentId,"onUpdate:modelValue":l[0]||(l[0]=e=>u.form.parentId=e),options:u.categoryOptions,props:{checkStrictly:!0,value:"id",label:"name"},placeholder:"请选择上级分类",clearable:"",style:{width:"100%"}},null,8,["modelValue","options"])])),_:1})):(0,t.Q3)("",!0),(0,t.bF)(V,{label:"分类名称",prop:"name"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{modelValue:u.form.name,"onUpdate:modelValue":l[1]||(l[1]=e=>u.form.name=e),placeholder:"请输入分类名称"},null,8,["modelValue"])])),_:1}),(0,t.bF)(V,{label:"分类编码",prop:"code"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{modelValue:u.form.code,"onUpdate:modelValue":l[2]||(l[2]=e=>u.form.code=e),placeholder:"请输入分类编码"},null,8,["modelValue"])])),_:1}),(0,t.bF)(V,{label:"显示顺序",prop:"sort"},{default:(0,t.k6)((()=>[(0,t.bF)(v,{modelValue:u.form.sort,"onUpdate:modelValue":l[3]||(l[3]=e=>u.form.sort=e),min:0,max:999,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),(0,t.bF)(V,{label:"分类图标",prop:"icon"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{modelValue:u.form.icon,"onUpdate:modelValue":l[4]||(l[4]=e=>u.form.icon=e),placeholder:"请选择图标",style:{width:"calc(100% - 45px)"}},null,8,["modelValue"]),(0,t.bF)(g,{type:"primary",icon:"Select",onClick:p.showIconSelector,style:{"margin-left":"5px"}},null,8,["onClick"])])),_:1}),(0,t.bF)(V,{label:"状态",prop:"status"},{default:(0,t.k6)((()=>[(0,t.bF)(C,{modelValue:u.form.status,"onUpdate:modelValue":l[5]||(l[5]=e=>u.form.status=e)},{default:(0,t.k6)((()=>[(0,t.bF)(_,{label:"1"},{default:(0,t.k6)((()=>l[16]||(l[16]=[(0,t.eW)("正常")]))),_:1}),(0,t.bF)(_,{label:"0"},{default:(0,t.k6)((()=>l[17]||(l[17]=[(0,t.eW)("停用")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(V,{label:"备注",prop:"remark"},{default:(0,t.k6)((()=>[(0,t.bF)(F,{modelValue:u.form.remark,"onUpdate:modelValue":l[6]||(l[6]=e=>u.form.remark=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"]),(0,t.bF)(x,{title:"选择图标",modelValue:u.iconSelectorVisible,"onUpdate:modelValue":l[10]||(l[10]=e=>u.iconSelectorVisible=e),width:"800px","append-to-body":""},{footer:(0,t.k6)((()=>[(0,t.Lk)("div",d,[(0,t.bF)(g,{onClick:l[9]||(l[9]=e=>u.iconSelectorVisible=!1)},{default:(0,t.k6)((()=>l[20]||(l[20]=[(0,t.eW)("取 消")]))),_:1})])])),default:(0,t.k6)((()=>[(0,t.Lk)("div",s,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(u.iconList,((e,l)=>((0,t.uX)(),(0,t.CE)("div",{key:l,class:"icon-item",onClick:l=>p.selectIcon(e)},[(0,t.Lk)("i",{class:(0,i.C4)(e)},null,2),(0,t.Lk)("span",null,(0,i.v_)(e.split("-")[1]),1)],8,c)))),128))])])),_:1},8,["modelValue"])])}const u={name:"EquipmentCategory",data(){return{loading:!1,categoryList:[{id:1,name:"快递设备",code:"express",sort:1,icon:"el-icon-box",status:"1",level:1,createTime:"2023-04-01 10:00:00",children:[{id:4,name:"快递柜",code:"express-cabinet",sort:1,icon:"el-icon-goods",status:"1",level:2,createTime:"2023-04-01 10:10:00",children:[]},{id:5,name:"分拣设备",code:"express-sorting",sort:2,icon:"el-icon-s-grid",status:"1",level:2,createTime:"2023-04-01 10:15:00",children:[]}]},{id:2,name:"物流设备",code:"logistics",sort:2,icon:"el-icon-truck",status:"1",level:1,createTime:"2023-04-01 10:05:00",children:[{id:6,name:"运输车辆",code:"logistics-vehicle",sort:1,icon:"el-icon-van",status:"1",level:2,createTime:"2023-04-01 10:20:00",children:[]},{id:7,name:"装卸设备",code:"logistics-loading",sort:2,icon:"el-icon-s-operation",status:"1",level:2,createTime:"2023-04-01 10:25:00",children:[]}]},{id:3,name:"办公设备",code:"office",sort:3,icon:"el-icon-office-building",status:"1",level:1,createTime:"2023-04-01 10:30:00",children:[{id:8,name:"电脑设备",code:"office-computer",sort:1,icon:"el-icon-monitor",status:"1",level:2,createTime:"2023-04-01 10:35:00",children:[]},{id:9,name:"打印设备",code:"office-printer",sort:2,icon:"el-icon-printer",status:"1",level:2,createTime:"2023-04-01 10:40:00",children:[]}]}],categoryOptions:[],dialogTitle:"",dialogVisible:!1,iconSelectorVisible:!1,form:{id:void 0,parentId:0,name:"",code:"",sort:0,icon:"",status:"1",remark:""},rules:{name:[{required:!0,message:"分类名称不能为空",trigger:"blur"}],code:[{required:!0,message:"分类编码不能为空",trigger:"blur"},{pattern:/^[a-z0-9-]+$/,message:"分类编码只能包含小写字母、数字和连字符",trigger:"blur"}],sort:[{required:!0,message:"显示顺序不能为空",trigger:"blur"}]},iconList:["el-icon-box","el-icon-goods","el-icon-s-grid","el-icon-truck","el-icon-van","el-icon-s-operation","el-icon-office-building","el-icon-monitor","el-icon-printer","el-icon-shopping-cart","el-icon-shopping-bag","el-icon-s-shop","el-icon-s-goods","el-icon-s-marketing","el-icon-s-tools","el-icon-s-platform","el-icon-s-cooperation","el-icon-s-order","el-icon-s-data","el-icon-s-management","el-icon-mobile","el-icon-phone","el-icon-camera","el-icon-video-camera","el-icon-message","el-icon-chat-dot-round","el-icon-chat-line-round","el-icon-bell","el-icon-location","el-icon-place","el-icon-map-location","el-icon-document","el-icon-folder","el-icon-ticket","el-icon-basketball","el-icon-football","el-icon-soccer"]}},created(){this.getCategoryList()},methods:{getCategoryList(){this.loading=!0,setTimeout((()=>{this.categoryOptions=this.formatCategoryOptions(this.categoryList),this.loading=!1}),500)},formatCategoryOptions(e){const l=[];return e.forEach((e=>{const o={id:e.id,name:e.name,children:[]};e.children&&e.children.length>0&&(o.children=this.formatCategoryOptions(e.children)),l.push(o)})),l},handleAdd(e){this.reset(),e&&e.id?(this.form.parentId=e.id,this.dialogTitle=`添加"${e.name}"的子分类`):(this.form.parentId=0,this.dialogTitle="添加分类"),this.dialogVisible=!0},handleUpdate(e){this.reset(),this.dialogTitle="修改分类",this.form={id:e.id,parentId:e.parentId||0,name:e.name,code:e.code,sort:e.sort,icon:e.icon,status:e.status,remark:e.remark||""},this.dialogVisible=!0},handleStatus(e){const l="0"===e.status?"1":"0",o="0"===l?"停用":"启用";this.$confirm(`确认要${o}分类"${e.name}"吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{e.status=l,this.$message({type:"success",message:`${o}成功!`})})).catch((()=>{}))},handleDelete(e){this.$confirm(`确认要删除分类"${e.name}"吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{this.$message({type:"success",message:"删除成功!"}),this.getCategoryList()})).catch((()=>{}))},reset(){this.form={id:void 0,parentId:0,name:"",code:"",sort:0,icon:"",status:"1",remark:""}},submitForm(){this.$refs.categoryForm.validate((e=>{e&&(void 0!==this.form.id?this.$message({type:"success",message:"修改成功!"}):this.$message({type:"success",message:"新增成功!"}),this.dialogVisible=!1,this.getCategoryList())}))},showIconSelector(){this.iconSelectorVisible=!0},selectIcon(e){this.form.icon=e,this.iconSelectorVisible=!1}}};var p=o(1241);const g=(0,p.A)(u,[["render",m],["__scopeId","data-v-569f99b8"]]),h=g}}]);