"use strict";(self["webpackChunkyzb_admin_system"]=self["webpackChunkyzb_admin_system"]||[]).push([[4685],{4685:(e,t,a)=>{a.r(t),a.d(t,{default:()=>W});var i=a(6768),l=a(4232);const s={class:"page-container"},n={class:"page-header"},u={class:"equipment-header"},c={class:"equipment-images"},m={class:"equipment-price"},o={class:"price-value"},d={class:"equipment-description"},r={class:"contact-info"},p={class:"contact-buttons"},k={class:"admin-actions"},h={class:"view-log"},f={class:"similar-items"},b=["onClick"],g={class:"similar-info"},_={class:"similar-name"},v={class:"similar-price"},q={class:"confirm-content"},D={class:"dialog-footer"};function C(e,t,a,C,F,y){const L=(0,i.g2)("el-page-header"),W=(0,i.g2)("el-tag"),w=(0,i.g2)("el-image"),V=(0,i.g2)("el-carousel-item"),$=(0,i.g2)("el-carousel"),E=(0,i.g2)("el-divider"),T=(0,i.g2)("el-descriptions-item"),X=(0,i.g2)("el-descriptions"),I=(0,i.g2)("el-rate"),S=(0,i.g2)("el-card"),G=(0,i.g2)("el-col"),J=(0,i.g2)("el-button"),P=(0,i.g2)("el-timeline-item"),R=(0,i.g2)("el-timeline"),Q=(0,i.g2)("el-row"),B=(0,i.g2)("el-dialog"),x=(0,i.gN)("loading");return(0,i.uX)(),(0,i.CE)("div",s,[(0,i.Lk)("div",n,[(0,i.bF)(L,{onBack:y.goBack,content:F.equipmentData.name||"设备详情"},null,8,["onBack","content"])]),(0,i.bo)(((0,i.uX)(),(0,i.Wv)(Q,{gutter:20},{default:(0,i.k6)((()=>[(0,i.bF)(G,{span:16},{default:(0,i.k6)((()=>[(0,i.bF)(S,{class:"main-card"},{default:(0,i.k6)((()=>[(0,i.Lk)("div",u,[(0,i.Lk)("h2",null,(0,l.v_)(F.equipmentData.name),1),(0,i.bF)(W,{type:"1"===F.equipmentData.status?"success":"2"===F.equipmentData.status?"info":"danger"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)("1"===F.equipmentData.status?"在售":"2"===F.equipmentData.status?"已售":"已下架"),1)])),_:1},8,["type"])]),(0,i.Lk)("div",c,[(0,i.bF)($,{height:"400px","indicator-position":"outside",arrow:"always",autoplay:!1},{default:(0,i.k6)((()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(F.equipmentData.images,((e,t)=>((0,i.uX)(),(0,i.Wv)(V,{key:t},{default:(0,i.k6)((()=>[(0,i.bF)(w,{src:e,fit:"contain",style:{width:"100%",height:"100%"},"preview-src-list":F.equipmentData.images},null,8,["src","preview-src-list"])])),_:2},1024)))),128))])),_:1})]),(0,i.Lk)("div",m,[t[3]||(t[3]=(0,i.Lk)("span",{class:"price-label"},"价格：",-1)),(0,i.Lk)("span",o,"¥ "+(0,l.v_)(F.equipmentData.price),1)]),(0,i.bF)(E),(0,i.bF)(X,{title:"基本信息",column:2,border:""},{default:(0,i.k6)((()=>[(0,i.bF)(T,{label:"设备名称"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(F.equipmentData.name),1)])),_:1}),(0,i.bF)(T,{label:"设备类型"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(F.equipmentData.typeName),1)])),_:1}),(0,i.bF)(T,{label:"品牌型号"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(F.equipmentData.brand),1)])),_:1}),(0,i.bF)(T,{label:"新旧程度"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(F.equipmentData.condition),1)])),_:1}),(0,i.bF)(T,{label:"使用年限"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(F.equipmentData.useYears)+" 年",1)])),_:1}),(0,i.bF)(T,{label:"交易方式"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(F.equipmentData.tradeType),1)])),_:1}),(0,i.bF)(T,{label:"所在区域"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(F.equipmentData.region),1)])),_:1}),(0,i.bF)(T,{label:"发布时间"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(F.equipmentData.createTime),1)])),_:1})])),_:1}),(0,i.bF)(E),t[4]||(t[4]=(0,i.Lk)("h3",null,"设备描述",-1)),(0,i.Lk)("div",d,(0,l.v_)(F.equipmentData.description),1),(0,i.bF)(E),t[5]||(t[5]=(0,i.Lk)("h3",null,"卖家信息",-1)),(0,i.bF)(X,{column:2,border:""},{default:(0,i.k6)((()=>[(0,i.bF)(T,{label:"卖家"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(F.equipmentData.publisher),1)])),_:1}),(0,i.bF)(T,{label:"联系电话"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(F.equipmentData.phone),1)])),_:1}),(0,i.bF)(T,{label:"信用评级"},{default:(0,i.k6)((()=>[(0,i.bF)(I,{modelValue:F.equipmentData.sellerRate,"onUpdate:modelValue":t[0]||(t[0]=e=>F.equipmentData.sellerRate=e),disabled:"","show-score":"","text-color":"#ff9900"},null,8,["modelValue"])])),_:1}),(0,i.bF)(T,{label:"交易次数"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(F.equipmentData.sellerDeals)+" 次",1)])),_:1})])),_:1})])),_:1})])),_:1}),(0,i.bF)(G,{span:8},{default:(0,i.k6)((()=>[(0,i.bF)(S,{class:"side-card"},{header:(0,i.k6)((()=>t[6]||(t[6]=[(0,i.Lk)("div",{class:"card-header"},[(0,i.Lk)("span",null,"联系卖家")],-1)]))),default:(0,i.k6)((()=>[(0,i.Lk)("div",r,[(0,i.Lk)("p",null,[t[7]||(t[7]=(0,i.Lk)("i",{class:"el-icon-user"},null,-1)),(0,i.eW)(" "+(0,l.v_)(F.equipmentData.publisher),1)]),(0,i.Lk)("p",null,[t[8]||(t[8]=(0,i.Lk)("i",{class:"el-icon-phone"},null,-1)),(0,i.eW)(" "+(0,l.v_)(F.equipmentData.phone),1)]),(0,i.Lk)("div",p,[(0,i.bF)(J,{type:"primary",icon:"el-icon-phone",onClick:y.handleCall},{default:(0,i.k6)((()=>t[9]||(t[9]=[(0,i.eW)("拨打电话")]))),_:1},8,["onClick"]),(0,i.bF)(J,{type:"success",icon:"el-icon-chat-dot-round",onClick:y.handleChat},{default:(0,i.k6)((()=>t[10]||(t[10]=[(0,i.eW)("在线咨询")]))),_:1},8,["onClick"])])])])),_:1}),(0,i.bF)(S,{class:"side-card mt-20"},{header:(0,i.k6)((()=>t[11]||(t[11]=[(0,i.Lk)("div",{class:"card-header"},[(0,i.Lk)("span",null,"管理操作")],-1)]))),default:(0,i.k6)((()=>[(0,i.Lk)("div",k,["2"!==F.equipmentData.status?((0,i.uX)(),(0,i.Wv)(J,{key:0,type:"primary",icon:"Edit",onClick:y.handleEdit},{default:(0,i.k6)((()=>t[12]||(t[12]=[(0,i.eW)("编辑信息")]))),_:1},8,["onClick"])):(0,i.Q3)("",!0),"1"===F.equipmentData.status?((0,i.uX)(),(0,i.Wv)(J,{key:1,type:"warning",icon:"RefreshLeft",onClick:y.handleToggleStatus},{default:(0,i.k6)((()=>t[13]||(t[13]=[(0,i.eW)("下架设备")]))),_:1},8,["onClick"])):(0,i.Q3)("",!0),"0"===F.equipmentData.status?((0,i.uX)(),(0,i.Wv)(J,{key:2,type:"success",icon:"RefreshRight",onClick:y.handleToggleStatus},{default:(0,i.k6)((()=>t[14]||(t[14]=[(0,i.eW)("上架设备")]))),_:1},8,["onClick"])):(0,i.Q3)("",!0),"1"===F.equipmentData.status?((0,i.uX)(),(0,i.Wv)(J,{key:3,type:"info",icon:"Finished",onClick:y.handleMarkSold},{default:(0,i.k6)((()=>t[15]||(t[15]=[(0,i.eW)("标记已售")]))),_:1},8,["onClick"])):(0,i.Q3)("",!0),"2"!==F.equipmentData.status?((0,i.uX)(),(0,i.Wv)(J,{key:4,type:"danger",icon:"Delete",onClick:y.handleDelete},{default:(0,i.k6)((()=>t[16]||(t[16]=[(0,i.eW)("删除设备")]))),_:1},8,["onClick"])):(0,i.Q3)("",!0)])])),_:1}),(0,i.bF)(S,{class:"side-card mt-20"},{header:(0,i.k6)((()=>t[17]||(t[17]=[(0,i.Lk)("div",{class:"card-header"},[(0,i.Lk)("span",null,"浏览记录")],-1)]))),default:(0,i.k6)((()=>[(0,i.Lk)("div",h,[(0,i.bF)(R,null,{default:(0,i.k6)((()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(F.viewLogs,((e,t)=>((0,i.uX)(),(0,i.Wv)(P,{key:t,timestamp:e.time,placement:"top"},{default:(0,i.k6)((()=>[(0,i.eW)((0,l.v_)(e.user)+" "+(0,l.v_)(e.action),1)])),_:2},1032,["timestamp"])))),128))])),_:1})])])),_:1}),(0,i.bF)(S,{class:"side-card mt-20"},{header:(0,i.k6)((()=>t[18]||(t[18]=[(0,i.Lk)("div",{class:"card-header"},[(0,i.Lk)("span",null,"同类设备推荐")],-1)]))),default:(0,i.k6)((()=>[(0,i.Lk)("div",f,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(F.similarItems,((e,t)=>((0,i.uX)(),(0,i.CE)("div",{key:t,class:"similar-item",onClick:t=>y.handleViewSimilar(e)},[(0,i.bF)(w,{src:e.image,fit:"cover",class:"similar-image"},null,8,["src"]),(0,i.Lk)("div",g,[(0,i.Lk)("div",_,(0,l.v_)(e.name),1),(0,i.Lk)("div",v,"¥ "+(0,l.v_)(e.price),1)])],8,b)))),128))])])),_:1})])),_:1})])),_:1})),[[x,F.loading]]),(0,i.bF)(B,{title:F.confirmTitle,modelValue:F.confirmVisible,"onUpdate:modelValue":t[2]||(t[2]=e=>F.confirmVisible=e),width:"400px"},{footer:(0,i.k6)((()=>[(0,i.Lk)("span",D,[(0,i.bF)(J,{onClick:t[1]||(t[1]=e=>F.confirmVisible=!1)},{default:(0,i.k6)((()=>t[19]||(t[19]=[(0,i.eW)("取 消")]))),_:1}),(0,i.bF)(J,{type:"primary",onClick:y.confirmAction},{default:(0,i.k6)((()=>t[20]||(t[20]=[(0,i.eW)("确 定")]))),_:1},8,["onClick"])])])),default:(0,i.k6)((()=>[(0,i.Lk)("div",q,(0,l.v_)(F.confirmContent),1)])),_:1},8,["title","modelValue"])])}const F={name:"EquipmentDetail",data(){return{loading:!1,equipmentId:null,equipmentData:{id:1,name:"丰巢快递柜",typeName:"快递柜",type:"1",brand:"丰巢 FC-200",price:3500,useYears:2,condition:"8成新",tradeType:"自提",region:"北京市朝阳区",images:["https://img1.baidu.com/it/u=413646961,2312577432&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333","https://img2.baidu.com/it/u=1003272215,1878948666&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800","https://img0.baidu.com/it/u=2277942570,1789375638&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333"],description:"9成新丰巢快递柜，使用两年，无划痕，功能完好，支持扫码存取件。设备型号FC-200，共24格，大中小格都有。因为店铺整体装修换代，所以低价转让。可以上门看设备，价格可小刀。仅限自提，可协助搬运。",publisher:"张三",phone:"13800138000",sellerRate:4.5,sellerDeals:12,createTime:"2023-04-10 10:30:00",status:"1"},viewLogs:[{user:"李先生",action:"查看了该设备",time:"2023-04-15 15:30:00"},{user:"王女士",action:"询问了该设备",time:"2023-04-14 10:20:00"},{user:"赵先生",action:"查看了该设备",time:"2023-04-13 14:15:00"},{user:"钱先生",action:"收藏了该设备",time:"2023-04-12 16:40:00"}],similarItems:[{id:2,name:"中邮智能快递柜",image:"https://img2.baidu.com/it/u=1003272215,1878948666&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800",price:4200},{id:3,name:"云柜快递柜",image:"https://img1.baidu.com/it/u=413646961,2312577432&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333",price:3800},{id:4,name:"格格快递柜",image:"https://img0.baidu.com/it/u=2277942570,1789375638&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=333",price:2900}],confirmVisible:!1,confirmTitle:"",confirmContent:"",confirmCallback:null}},created(){this.equipmentId=this.$route.params.id,this.getEquipmentDetail()},methods:{getEquipmentDetail(){this.loading=!0,setTimeout((()=>{this.loading=!1}),500)},goBack(){this.$router.go(-1)},handleEdit(){this.$router.push(`/equipment/edit/${this.equipmentId}`)},handleToggleStatus(){const e="1"===this.equipmentData.status?"下架":"上架";this.confirmTitle=`确认${e}`,this.confirmContent=`确定要${e}设备"${this.equipmentData.name}"吗？`,this.confirmCallback=()=>{this.equipmentData.status="1"===this.equipmentData.status?"0":"1",this.$message({type:"success",message:`${e}成功！`}),this.confirmVisible=!1},this.confirmVisible=!0},handleMarkSold(){this.confirmTitle="标记已售",this.confirmContent=`确定要将设备"${this.equipmentData.name}"标记为已售状态吗？`,this.confirmCallback=()=>{this.equipmentData.status="2",this.$message({type:"success",message:"已成功标记为已售状态！"}),this.confirmVisible=!1},this.confirmVisible=!0},handleDelete(){this.confirmTitle="删除设备",this.confirmContent=`确定要删除设备"${this.equipmentData.name}"吗？此操作不可恢复！`,this.confirmCallback=()=>{this.$message({type:"success",message:"删除成功！"}),this.confirmVisible=!1,this.$router.push("/equipment/list")},this.confirmVisible=!0},handleCall(){window.location.href=`tel:${this.equipmentData.phone}`},handleChat(){this.$message({type:"info",message:"正在连接聊天服务..."})},handleViewSimilar(e){this.$router.push(`/equipment/detail/${e.id}`)},confirmAction(){this.confirmCallback&&this.confirmCallback()}}};var y=a(1241);const L=(0,y.A)(F,[["render",C],["__scopeId","data-v-cb72aa06"]]),W=L}}]);