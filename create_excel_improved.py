import pandas as pd
import json
from datetime import datetime

# 创建users.xlsx
users_data = {
    '_id': [''],
    'openid': ['test_openid_123'],
    'unionid': [''],
    'phone': ['13800138000'],  # 字符串格式
    'nickname': ['测试用户'],
    'avatar': [''],
    'create_time': ['2023-01-01 00:00:00'],
    'last_login_time': ['2023-01-01 00:00:00'],
    'is_verified': [False],    # 小写的布尔值
    'roles': [json.dumps(["user"])],  # JSON格式的数组
    'points': [0],             # 数值类型
    'balance': [0]             # 数值类型
}

# 创建DataFrame
users_df = pd.DataFrame(users_data)

# 确保数据类型正确
users_df['phone'] = users_df['phone'].astype(str)  # 确保手机号为字符串
users_df['code'] = users_df['code'].astype(str) if 'code' in users_df else None  # 如果有code字段，确保是字符串

# 使用ExcelWriter来设置列格式
with pd.ExcelWriter('users.xlsx', engine='openpyxl') as writer:
    users_df.to_excel(writer, index=False)
    
    # 获取工作表
    worksheet = writer.sheets['Sheet1']
    
    # 设置日期列的格式
    date_format = 'yyyy-mm-dd hh:mm:ss'
    for i, col in enumerate(users_df.columns):
        if col in ['create_time', 'last_login_time']:
            for row in range(2, len(users_df) + 2):  # Excel是从1开始，再加上标题行
                cell = worksheet.cell(row=row, column=i+1)
                cell.number_format = date_format

print("users.xlsx 创建成功")

# 创建sms_codes.xlsx
sms_codes_data = {
    '_id': [''],
    'phone': ['13800138000'],  # 字符串格式
    'code': ['123456'],        # 字符串格式
    'create_time': ['2023-01-01 00:00:00'],
    'expire_time': ['2023-01-01 01:00:00'],
    'used': [False]            # 小写的布尔值
}

# 创建DataFrame
sms_codes_df = pd.DataFrame(sms_codes_data)

# 确保数据类型正确
sms_codes_df['phone'] = sms_codes_df['phone'].astype(str)  # 确保手机号为字符串
sms_codes_df['code'] = sms_codes_df['code'].astype(str)    # 确保验证码为字符串

# 使用ExcelWriter来设置列格式
with pd.ExcelWriter('sms_codes.xlsx', engine='openpyxl') as writer:
    sms_codes_df.to_excel(writer, index=False)
    
    # 获取工作表
    worksheet = writer.sheets['Sheet1']
    
    # 设置日期列的格式
    date_format = 'yyyy-mm-dd hh:mm:ss'
    for i, col in enumerate(sms_codes_df.columns):
        if col in ['create_time', 'expire_time']:
            for row in range(2, len(sms_codes_df) + 2):  # Excel是从1开始，再加上标题行
                cell = worksheet.cell(row=row, column=i+1)
                cell.number_format = date_format

print("sms_codes.xlsx 创建成功")
print("所有文件已创建完成，符合微信云开发导入格式要求。") 