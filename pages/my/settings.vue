<template>
	<view class="settings-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">设置</text>
		</view>
		
		<!-- 设置列表 -->
		<view class="settings-list">
			<!-- 账号安全 -->
			<view class="settings-group">
				<view class="group-title">账号安全</view>
				<view class="setting-item" @tap="changePassword">
					<text class="item-name">修改密码</text>
					<view class="item-arrow">
						<image src="/static/icons/arrow-right.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="setting-item" @tap="changePhone">
					<text class="item-name">修改手机号</text>
					<view class="item-arrow">
						<image src="/static/icons/arrow-right.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 收货地址 -->
			<view class="settings-group">
				<view class="group-title">收货地址</view>
				<view class="setting-item" @tap="manageAddress">
					<text class="item-name">管理收货地址</text>
					<view class="item-arrow">
						<image src="/static/icons/arrow-right.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="address-preview" v-if="defaultAddress">
					<view class="address-info">
						<view class="user-info">
							<text class="name">{{defaultAddress.name}}</text>
							<text class="phone">{{formatPhone(defaultAddress.phone)}}</text>
							<text class="default-tag" v-if="defaultAddress.isDefault">默认</text>
						</view>
						<view class="address-text">{{defaultAddress.province}}{{defaultAddress.city}}{{defaultAddress.district}}{{defaultAddress.address}}</view>
					</view>
				</view>
				<view class="no-address" v-else @tap="addAddress">
					<text>添加收货地址</text>
				</view>
			</view>
			
			<!-- 通知设置 -->
			<view class="settings-group">
				<view class="group-title">通知设置</view>
				<view class="setting-item">
					<text class="item-name">接收系统通知</text>
					<switch :checked="settings.systemNotification" @change="onSwitchChange('systemNotification', $event)" color="#ff5a5f" />
				</view>
				<view class="setting-item">
					<text class="item-name">接收营销信息</text>
					<switch :checked="settings.marketingNotification" @change="onSwitchChange('marketingNotification', $event)" color="#ff5a5f" />
				</view>
				<view class="setting-item">
					<text class="item-name">消息提醒声音</text>
					<switch :checked="settings.messageSound" @change="onSwitchChange('messageSound', $event)" color="#ff5a5f" />
				</view>
			</view>
			
			<!-- 隐私设置 -->
			<view class="settings-group">
				<view class="group-title">隐私设置</view>
				<view class="setting-item">
					<text class="item-name">允许陌生人查看资料</text>
					<switch :checked="settings.allowProfileView" @change="onSwitchChange('allowProfileView', $event)" color="#ff5a5f" />
				</view>
				<view class="setting-item">
					<text class="item-name">允许消息推送</text>
					<switch :checked="settings.allowPushMessage" @change="onSwitchChange('allowPushMessage', $event)" color="#ff5a5f" />
				</view>
			</view>
			
			<!-- 其他设置 -->
			<view class="settings-group">
				<view class="group-title">其他设置</view>
				<view class="setting-item" @tap="clearCache">
					<text class="item-name">清除缓存</text>
					<text class="cache-size">{{cacheSize}}</text>
				</view>
				<view class="setting-item" @tap="goToAgreement">
					<text class="item-name">用户协议</text>
					<view class="item-arrow">
						<image src="/static/icons/arrow-right.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="setting-item" @tap="goToPrivacy">
					<text class="item-name">隐私政策</text>
					<view class="item-arrow">
						<image src="/static/icons/arrow-right.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="setting-item" @tap="showAbout">
					<text class="item-name">关于我们</text>
					<view class="item-arrow">
						<image src="/static/icons/arrow-right.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 退出登录 -->
		<view class="logout-btn" @tap="logout">
			<text>退出登录</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				cacheSize: '12.5MB',
				settings: {
					systemNotification: true,
					marketingNotification: false,
					messageSound: true,
					allowProfileView: true,
					allowPushMessage: true
				},
				defaultAddress: null
			}
		},
		onLoad() {
			this.loadSettings();
			this.loadDefaultAddress();
		},
		methods: {
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			
			// 加载设置
			loadSettings() {
				const storedSettings = uni.getStorageSync('userSettings');
				if (storedSettings) {
					this.settings = JSON.parse(storedSettings);
				} else {
					// 保存默认设置
					uni.setStorageSync('userSettings', JSON.stringify(this.settings));
				}
			},
			
			// 加载默认地址
			loadDefaultAddress() {
				const addressStorage = uni.getStorageSync('address_list');
				if (addressStorage) {
					const addresses = JSON.parse(addressStorage);
					// 查找默认地址
					this.defaultAddress = addresses.find(addr => addr.isDefault) || (addresses.length > 0 ? addresses[0] : null);
				}
			},
			
			// 格式化手机号
			formatPhone(phone) {
				if (!phone) return '';
				return phone.substr(0, 3) + '****' + phone.substr(7);
			},
			
			// 管理收货地址
			manageAddress() {
				uni.navigateTo({
					url: '/pages/my/address-list'
				});
			},
			
			// 添加收货地址
			addAddress() {
				uni.navigateTo({
					url: '/pages/my/address-edit'
				});
			},
			
			// 开关变化
			onSwitchChange(key, event) {
				this.settings[key] = event.detail.value;
				
				// 保存设置
				uni.setStorageSync('userSettings', JSON.stringify(this.settings));
				
				// 提示设置已保存
				uni.showToast({
					title: '设置已保存',
					icon: 'success',
					duration: 1500
				});
			},
			
			// 修改密码
			changePassword() {
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			},
			
			// 修改手机号
			changePhone() {
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			},
			
			// 清除缓存
			clearCache() {
				uni.showModal({
					title: '清除缓存',
					content: '确定要清除缓存吗？',
					success: (res) => {
						if (res.confirm) {
							// 模拟清除缓存
							setTimeout(() => {
								this.cacheSize = '0MB';
								uni.showToast({
									title: '缓存已清除',
									icon: 'success'
								});
							}, 500);
						}
					}
				});
			},
			
			// 用户协议
			goToAgreement() {
				uni.navigateTo({
					url: '/pages/my/agreement'
				});
			},
			
			// 隐私政策
			goToPrivacy() {
				uni.navigateTo({
					url: '/pages/my/privacy'
				});
			},
			
			// 关于我们
			showAbout() {
				uni.showModal({
					title: '关于驿站帮Pro',
					content: '驿站帮Pro v1.0.0\n专注于驿站资源、设备交易及人才服务的综合平台',
					showCancel: false
				});
			},
			
			// 退出登录
			logout() {
				uni.showModal({
					title: '退出登录',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							// 清除登录状态
							const userInfo = uni.getStorageSync('userInfo');
							if (userInfo) {
								const parsedInfo = JSON.parse(userInfo);
								parsedInfo.isLoggedIn = false;
								uni.setStorageSync('userInfo', JSON.stringify(parsedInfo));
							}
							
							// 跳转到登录页面
							uni.navigateTo({
								url: '/pages/my/login'
							});
						}
					}
				});
			}
		}
	}
</script>

<style>
	.settings-container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}
	
	.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.settings-list {
		padding: 20rpx;
	}
	
	.settings-group {
		background-color: #ffffff;
		border-radius: 8rpx;
		margin-bottom: 20rpx;
	}
	
	.group-title {
		padding: 20rpx;
		font-size: 28rpx;
		color: #999999;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.setting-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.setting-item:last-child {
		border-bottom: none;
	}
	
	.item-name {
		font-size: 28rpx;
		color: #333333;
	}
	
	.item-arrow image {
		width: 30rpx;
		height: 30rpx;
	}
	
	.cache-size {
		font-size: 28rpx;
		color: #999999;
	}
	
	.logout-btn {
		margin: 40rpx 20rpx;
		height: 80rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.logout-btn text {
		font-size: 30rpx;
		color: #ffffff;
	}
	
	/* 地址相关样式 */
	.address-preview {
		padding: 20rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		margin: 0 20rpx 20rpx;
	}
	
	.address-info {
		display: flex;
		flex-direction: column;
	}
	
	.user-info {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.name {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		margin-right: 15rpx;
	}
	
	.phone {
		font-size: 26rpx;
		color: #666666;
		margin-right: 15rpx;
	}
	
	.default-tag {
		font-size: 22rpx;
		color: #ffffff;
		background-color: #ff5a5f;
		padding: 4rpx 10rpx;
		border-radius: 4rpx;
	}
	
	.address-text {
		font-size: 26rpx;
		color: #333333;
		line-height: 1.4;
	}
	
	.no-address {
		padding: 30rpx 20rpx;
		text-align: center;
		color: #999999;
		margin: 0 20rpx 20rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
	}
</style> 