<template>
	<view class="favorites-container">
		<!-- 标题栏 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">我的收藏</text>
			<view class="placeholder"></view>
		</view>
		
		<!-- 分类选项卡 -->
		<view class="tabs">
			<view class="tab-item" 
				v-for="(tab, index) in tabs" 
				:key="index" 
				:class="{'active': currentTab === index}"
				@tap="switchTab(index)">
				<text>{{tab.name}}</text>
				<view class="tab-line" v-if="currentTab === index"></view>
			</view>
		</view>
		
		<!-- 内容区域 -->
		<swiper class="content-swiper" :current="currentTab" @change="onSwiperChange">
			<!-- 全部收藏 -->
			<swiper-item>
				<scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore('all')">
					<view class="empty-view" v-if="allList.length === 0">
						<image src="/static/images/empty.png" mode="aspectFit"></image>
						<text>暂无收藏内容~</text>
						<view class="add-btn" @tap="navigateTo('/pages/index/index')">
							<text>去浏览</text>
						</view>
					</view>
					
					<view class="favorites-list" v-else>
						<view class="favorite-item" v-for="(item, index) in allList" :key="index" @tap="viewDetail(item)">
							<image class="favorite-image" :src="item.image" mode="aspectFill"></image>
							<view class="favorite-info">
								<text class="favorite-title">{{item.title}}</text>
								<view class="favorite-tags" v-if="item.tags && item.tags.length > 0">
									<text class="tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
								</view>
								<view class="favorite-address" v-if="item.address">
									<image src="/static/icons/location.png" mode="aspectFit"></image>
									<text>{{item.address}}</text>
								</view>
								<view class="favorite-price" v-if="item.price">¥<text class="price-value">{{item.price}}</text></view>
							</view>
							<view class="favorite-actions">
								<view class="action-btn cancel" @tap.stop="cancelFavorite(item)">
									<image src="/static/icons/star-filled.png" mode="aspectFit"></image>
									<text>取消收藏</text>
								</view>
							</view>
						</view>
						
						<view class="load-more" v-if="allHasMore">
							<text>加载更多...</text>
						</view>
						<view class="no-more" v-else>
							<text>已经到底了~</text>
						</view>
					</view>
				</scroll-view>
			</swiper-item>
			
			<!-- 驿站转让 -->
			<swiper-item>
				<scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore('transfer')">
					<view class="empty-view" v-if="transferList.length === 0">
						<image src="/static/images/empty.png" mode="aspectFit"></image>
						<text>暂无收藏的驿站转让~</text>
						<view class="add-btn" @tap="navigateTo('/pages/transfer/transfer')">
							<text>去浏览</text>
						</view>
					</view>
					
					<view class="favorites-list" v-else>
						<view class="favorite-item" v-for="(item, index) in transferList" :key="index" @tap="viewDetail(item)">
							<image class="favorite-image" :src="item.image" mode="aspectFill"></image>
							<view class="favorite-info">
								<text class="favorite-title">{{item.title}}</text>
								<view class="favorite-tags" v-if="item.tags && item.tags.length > 0">
									<text class="tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
								</view>
								<view class="favorite-address">
									<image src="/static/icons/location.png" mode="aspectFit"></image>
									<text>{{item.address}}</text>
								</view>
								<view class="favorite-price">¥<text class="price-value">{{item.price}}</text></view>
							</view>
							<view class="favorite-actions">
								<view class="action-btn cancel" @tap.stop="cancelFavorite(item)">
									<image src="/static/icons/star-filled.png" mode="aspectFit"></image>
									<text>取消收藏</text>
								</view>
							</view>
						</view>
						
						<view class="load-more" v-if="transferHasMore">
							<text>加载更多...</text>
						</view>
						<view class="no-more" v-else>
							<text>已经到底了~</text>
						</view>
					</view>
				</scroll-view>
			</swiper-item>
			
			<!-- 其他类别... -->
		</swiper>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabs: [
					{ name: '全部', type: 'all' },
					{ name: '驿站转让', type: 'transfer' },
					{ name: '招聘信息', type: 'job' },
					{ name: '设备出售', type: 'device' }
				],
				currentTab: 0,
				allList: [],
				transferList: [],
				jobList: [],
				deviceList: [],
				allHasMore: false,
				transferHasMore: false,
				jobHasMore: false,
				deviceHasMore: false,
				userInfo: null
			}
		},
		onLoad() {
			// 获取用户信息
			const userInfoStorage = uni.getStorageSync('userInfo');
			if (userInfoStorage) {
				this.userInfo = JSON.parse(userInfoStorage);
				// 加载收藏数据
				this.loadFavorites();
			} else {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		methods: {
			// 加载收藏数据
			loadFavorites() {
				// 从本地存储获取收藏数据
				const favorites = uni.getStorageSync('favorites_' + this.userInfo.userId) || [];
				
				// 全部收藏
				this.allList = favorites;
				
				// 按类型筛选
				this.transferList = favorites.filter(item => item.type === 'transfer');
				this.jobList = favorites.filter(item => item.type === 'job');
				this.deviceList = favorites.filter(item => item.type === 'device');
				
				// 更新加载状态
				this.allHasMore = false;
				this.transferHasMore = false;
				this.jobHasMore = false;
				this.deviceHasMore = false;
			},
			
			// 切换选项卡
			switchTab(index) {
				this.currentTab = index;
			},
			
			// 滑动切换选项卡
			onSwiperChange(e) {
				this.currentTab = e.detail.current;
			},
			
			// 加载更多
			loadMore(type) {
				// 实际项目中应该分页加载
				// 这里只是模拟
				uni.showToast({
					title: '没有更多数据了',
					icon: 'none'
				});
			},
			
			// 查看详情
			viewDetail(item) {
				let url = '/pages/detail/detail?id=' + item.id + '&type=' + item.type;
				uni.navigateTo({
					url: url
				});
			},
			
			// 取消收藏
			cancelFavorite(item) {
				uni.showModal({
					title: '提示',
					content: '确定要取消收藏该内容吗？',
					success: (res) => {
						if (res.confirm) {
							// 从本地存储中删除
							let favorites = uni.getStorageSync('favorites_' + this.userInfo.userId) || [];
							favorites = favorites.filter(favorite => !(favorite.id === item.id && favorite.type === item.type));
							uni.setStorageSync('favorites_' + this.userInfo.userId, favorites);
							
							// 更新显示
							this.loadFavorites();
							
							uni.showToast({
								title: '已取消收藏',
								icon: 'success'
							});
						}
					}
				});
			},
			
			// 页面导航
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	.favorites-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}
	
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.placeholder {
		width: 60rpx;
	}
	
	.tabs {
		display: flex;
		background-color: #ffffff;
		border-bottom: 1rpx solid #eeeeee;
	}
	
	.tab-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx 0;
		position: relative;
	}
	
	.tab-item text {
		font-size: 28rpx;
		color: #666666;
	}
	
	.tab-item.active text {
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.tab-line {
		position: absolute;
		bottom: 0;
		width: 60rpx;
		height: 6rpx;
		background-color: #ff5a5f;
		border-radius: 3rpx;
	}
	
	.content-swiper {
		flex: 1;
	}
	
	.scroll-view {
		height: 100%;
	}
	
	.empty-view {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 200rpx;
	}
	
	.empty-view image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}
	
	.empty-view text {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 30rpx;
	}
	
	.add-btn {
		padding: 15rpx 40rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
	}
	
	.add-btn text {
		font-size: 28rpx;
		color: #ffffff;
		margin-bottom: 0;
	}
	
	.favorites-list {
		padding: 20rpx;
	}
	
	.favorite-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.favorite-image {
		width: 100%;
		height: 300rpx;
		border-radius: 8rpx;
		margin-bottom: 15rpx;
		background-color: #f9f9f9;
	}
	
	.favorite-info {
		margin-bottom: 15rpx;
	}
	
	.favorite-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
		display: block;
	}
	
	.favorite-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 10rpx;
	}
	
	.tag {
		font-size: 22rpx;
		padding: 6rpx 16rpx;
		background-color: #FFF0F0;
		color: #FF5A5F;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
		border-radius: 6rpx;
	}
	
	.favorite-address {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #666666;
		margin-bottom: 10rpx;
	}
	
	.favorite-address image {
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
	}
	
	.favorite-price {
		font-size: 24rpx;
		color: #FF5A5F;
	}
	
	.price-value {
		font-size: 32rpx;
		font-weight: bold;
	}
	
	.favorite-actions {
		display: flex;
		justify-content: flex-end;
		border-top: 1rpx solid #eeeeee;
		padding-top: 15rpx;
		margin-top: 15rpx;
	}
	
	.action-btn {
		display: flex;
		align-items: center;
		padding: 10rpx 20rpx;
		margin-left: 15rpx;
		border-radius: 30rpx;
	}
	
	.action-btn image {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
	}
	
	.action-btn text {
		font-size: 24rpx;
	}
	
	.cancel {
		background-color: #FFF0F0;
		color: #FF5A5F;
	}
	
	.load-more, .no-more {
		text-align: center;
		padding: 30rpx 0;
	}
	
	.load-more text, .no-more text {
		font-size: 24rpx;
		color: #999999;
	}
</style> 