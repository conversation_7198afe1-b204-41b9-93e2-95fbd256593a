import auth from '../../api/modules/auth';
import { getApiBaseUrl } from '../../api/config';

Page({
  data: {
    serverStatus: '未连接',
    statusColor: '#999',
    serverUrl: '',
    logs: [],
    loginResult: null
  },

  onLoad() {
    // 获取API服务器地址
    const serverUrl = getApiBaseUrl();
    this.setData({ 
      serverUrl,
      logs: ['页面加载完成']
    });
    
    // 检查服务器连接
    this.checkServer();
  },

  // 添加日志
  addLog(message) {
    const time = new Date().toLocaleTimeString();
    const log = `${time}: ${message}`;
    
    const logs = this.data.logs.slice();
    logs.unshift(log); // 新日志放在顶部
    
    this.setData({ logs });
    console.log(message);
  },
  
  // 检查服务器连接
  checkServer() {
    this.addLog('正在检查服务器连接...');
    
    wx.request({
      url: `${this.data.serverUrl}/api/test`,
      method: 'GET',
      success: (res) => {
        this.addLog(`服务器连接成功: ${JSON.stringify(res.data)}`);
        this.setData({
          serverStatus: '已连接',
          statusColor: '#07c160'
        });
      },
      fail: (err) => {
        this.addLog(`服务器连接失败: ${JSON.stringify(err)}`);
        this.setData({
          serverStatus: '连接失败',
          statusColor: '#f00'
        });
      }
    });
  },
  
  // 执行微信登录
  doWxLogin() {
    this.addLog('开始执行微信登录...');
    
    auth.wxLogin()
      .then(res => {
        this.addLog(`登录成功: ${JSON.stringify(res.data)}`);
        this.setData({ loginResult: res.data });
      })
      .catch(err => {
        this.addLog(`登录失败: ${JSON.stringify(err)}`);
      });
  },
  
  // 清除日志
  clearLogs() {
    this.setData({ logs: [] });
    this.addLog('日志已清除');
  },
  
  // 复制服务器地址到剪贴板
  copyServerUrl() {
    wx.setClipboardData({
      data: this.data.serverUrl,
      success: () => {
        wx.showToast({
          title: '已复制服务器地址',
          icon: 'success'
        });
      }
    });
  }
}) 