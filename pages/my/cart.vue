<template>
	<view class="cart-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">购物车</text>
			<view class="right-btn" @tap="editCart">
				<text>{{isEditing ? '完成' : '编辑'}}</text>
			</view>
		</view>
		
		<!-- 空购物车 -->
		<view class="empty-cart" v-if="cartList.length === 0">
			<image src="/static/images/empty-cart.png" mode="aspectFit" class="empty-image"></image>
			<text class="empty-text">购物车还是空的</text>
			<view class="go-shopping-btn" @tap="goShopping">
				<text>去选购</text>
			</view>
		</view>
		
		<!-- 购物车列表 -->
		<view class="cart-content" v-else>
			<scroll-view scroll-y class="cart-scroll">
				<view class="cart-list">
					<view class="cart-item" v-for="(item, index) in cartList" :key="index">
						<view class="check-box" @tap="toggleSelect(index)">
							<view class="check-inner" :class="{ checked: item.selected }">
								<image v-if="item.selected" src="/static/icons/check.png" mode="aspectFit"></image>
							</view>
						</view>
						<image :src="item.productImage" mode="aspectFill" class="product-image"></image>
						<view class="product-info">
							<text class="product-name">{{item.productName}}</text>
							<text class="product-spec">{{item.productSpec}}</text>
							<view class="price-quantity">
								<text class="price">¥{{item.price ? item.price.toFixed(2) : '0.00'}}</text>
								<view class="quantity-control">
									<view class="control-btn" @tap="decreaseQuantity(index)">-</view>
									<input type="number" v-model="item.quantity" class="quantity-input" />
									<view class="control-btn" @tap="increaseQuantity(index)">+</view>
								</view>
							</view>
						</view>
						<view class="delete-btn" v-if="isEditing" @tap="removeItem(index)">
							<image src="/static/icons/delete.png" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</scroll-view>
			
			<!-- 底部结算栏 -->
			<view class="checkout-bar">
				<view class="select-all" @tap="toggleSelectAll">
					<view class="check-box">
						<view class="check-inner" :class="{ checked: isAllSelected }">
							<image v-if="isAllSelected" src="/static/icons/check.png" mode="aspectFit"></image>
						</view>
					</view>
					<text>全选</text>
				</view>
				
				<view class="right-section" v-if="!isEditing">
					<view class="total-price">
						<text>合计：</text>
						<text class="price-value">¥{{totalPrice.toFixed(2)}}</text>
					</view>
					<view class="checkout-btn" @tap="checkout">
						<text>结算({{selectedCount}})</text>
					</view>
				</view>
				
				<view class="right-section" v-else>
					<view class="clear-btn" @tap="clearCart">
						<text>清空购物车</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				cartList: [],
				isEditing: false,
				isAllSelected: false
			}
		},
		computed: {
			// 计算选中商品总价
			totalPrice() {
				return this.cartList.reduce((total, item) => {
					return item.selected ? total + (item.price * item.quantity) : total;
				}, 0);
			},
			// 计算选中商品数量
			selectedCount() {
				return this.cartList.filter(item => item.selected).length;
			}
		},
		onShow() {
			this.loadCartData();
			this.checkSelectAll();
		},
		methods: {
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			
			// 加载购物车数据
			loadCartData() {
				const cart = uni.getStorageSync('cart');
				if (cart) {
					this.cartList = JSON.parse(cart);
				}
			},
			
			// 保存购物车数据
			saveCartData() {
				uni.setStorageSync('cart', JSON.stringify(this.cartList));
			},
			
			// 切换编辑模式
			editCart() {
				this.isEditing = !this.isEditing;
			},
			
			// 去购物
			goShopping() {
				uni.switchTab({
					url: '/pages/station/station'
				});
			},
			
			// 切换单个商品选中状态
			toggleSelect(index) {
				this.cartList[index].selected = !this.cartList[index].selected;
				this.saveCartData();
				this.checkSelectAll();
			},
			
			// 切换全选状态
			toggleSelectAll() {
				this.isAllSelected = !this.isAllSelected;
				this.cartList.forEach(item => {
					item.selected = this.isAllSelected;
				});
				this.saveCartData();
			},
			
			// 检查是否全部选中
			checkSelectAll() {
				this.isAllSelected = this.cartList.length > 0 && this.cartList.every(item => item.selected);
			},
			
			// 减少商品数量
			decreaseQuantity(index) {
				if (this.cartList[index].quantity > 1) {
					this.cartList[index].quantity--;
					this.saveCartData();
				}
			},
			
			// 增加商品数量
			increaseQuantity(index) {
				const item = this.cartList[index];
				if (item.quantity < (item.stock || 99)) {
					item.quantity++;
					this.saveCartData();
				} else {
					uni.showToast({
						title: '已达到最大库存',
						icon: 'none'
					});
				}
			},
			
			// 移除单个商品
			removeItem(index) {
				uni.showModal({
					title: '提示',
					content: '确定要移除该商品吗？',
					success: res => {
						if (res.confirm) {
							this.cartList.splice(index, 1);
							this.saveCartData();
							this.checkSelectAll();
						}
					}
				});
			},
			
			// 清空购物车
			clearCart() {
				uni.showModal({
					title: '提示',
					content: '确定要清空购物车吗？',
					success: res => {
						if (res.confirm) {
							this.cartList = [];
							this.saveCartData();
						}
					}
				});
			},
			
			// 结算
			checkout() {
				if (this.selectedCount === 0) {
					uni.showToast({
						title: '请选择要结算的商品',
						icon: 'none'
					});
					return;
				}
				
				// 创建结算订单
				const checkoutOrder = {
					products: this.cartList.filter(item => item.selected),
					from: 'cart' // 标记为购物车结算
				};
				
				// 保存结算订单
				uni.setStorageSync('tempOrder', JSON.stringify(checkoutOrder));
				
				// 跳转到结算页面
				uni.navigateTo({
					url: '/pages/my/checkout'
				});
			}
		}
	}
</script>

<style>
	.cart-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
	}
	
	.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.right-btn {
		font-size: 28rpx;
		color: #666666;
		padding: 10rpx;
	}
	
	.empty-cart {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
	}
	
	.empty-image {
		width: 240rpx;
		height: 240rpx;
		margin-bottom: 30rpx;
	}
	
	.empty-text {
		font-size: 30rpx;
		color: #999999;
		margin-bottom: 40rpx;
	}
	
	.go-shopping-btn {
		width: 240rpx;
		height: 80rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.go-shopping-btn text {
		font-size: 30rpx;
		color: #ffffff;
	}
	
	.cart-content {
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	.cart-scroll {
		flex: 1;
		height: 0;
	}
	
	.cart-list {
		padding: 20rpx;
	}
	
	.cart-item {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.check-box {
		width: 50rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}
	
	.check-inner {
		width: 36rpx;
		height: 36rpx;
		border-radius: 50%;
		border: 2rpx solid #dddddd;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.check-inner.checked {
		background-color: #ff5a5f;
		border-color: #ff5a5f;
	}
	
	.check-inner image {
		width: 24rpx;
		height: 24rpx;
	}
	
	.product-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
	}
	
	.product-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}
	
	.product-name {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	.product-spec {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 20rpx;
	}
	
	.price-quantity {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.price {
		font-size: 30rpx;
		font-weight: bold;
		color: #ff5a5f;
	}
	
	.quantity-control {
		display: flex;
		align-items: center;
	}
	
	.control-btn {
		width: 50rpx;
		height: 50rpx;
		background-color: #f5f5f5;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #333333;
	}
	
	.quantity-input {
		width: 70rpx;
		height: 50rpx;
		background-color: #f5f5f5;
		margin: 0 2rpx;
		text-align: center;
		font-size: 26rpx;
	}
	
	.delete-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 20rpx;
	}
	
	.delete-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.checkout-bar {
		height: 100rpx;
		background-color: #ffffff;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.select-all {
		display: flex;
		align-items: center;
	}
	
	.select-all text {
		font-size: 28rpx;
		color: #333333;
	}
	
	.right-section {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}
	
	.total-price {
		margin-right: 20rpx;
		font-size: 28rpx;
		color: #333333;
	}
	
	.price-value {
		font-weight: bold;
		color: #ff5a5f;
	}
	
	.checkout-btn {
		width: 200rpx;
		height: 70rpx;
		background-color: #ff5a5f;
		border-radius: 35rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.checkout-btn text {
		font-size: 28rpx;
		color: #ffffff;
	}
	
	.clear-btn {
		width: 200rpx;
		height: 70rpx;
		background-color: #ff5a5f;
		border-radius: 35rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.clear-btn text {
		font-size: 28rpx;
		color: #ffffff;
	}
</style> 