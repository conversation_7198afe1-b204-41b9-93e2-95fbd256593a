<template>
	<view class="verify-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">驿站认证</text>
			<view class="right-btn" @tap="showHelp">
				<image src="/static/icons/help.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 状态提示 -->
		<view class="status-section" v-if="verificationStatus">
			<view class="status-icon">
				<image :src="statusIcon[verificationStatus]" mode="aspectFit"></image>
			</view>
			<text class="status-text">{{statusText[verificationStatus]}}</text>
			<text class="status-desc">{{statusDesc[verificationStatus]}}</text>
		</view>
		
		<!-- 表单区域 -->
		<view class="form-section" v-if="verificationStatus !== 'approved' && verificationStatus !== 'processing'">
			<view class="form-title">驿站信息认证</view>
			
			<!-- 驿站名称 -->
			<view class="form-item">
				<text class="item-label">驿站名称</text>
				<input 
					class="item-input" 
					type="text" 
					placeholder="请输入完整的驿站名称" 
					v-model="formData.stationName"
					:disabled="isDisabled"
				/>
			</view>
			
			<!-- 驿站编号 -->
			<view class="form-item">
				<text class="item-label">驿站编号</text>
				<input 
					class="item-input" 
					type="text" 
					placeholder="请输入驿站编号" 
					v-model="formData.stationCode"
					:disabled="isDisabled"
				/>
			</view>
			
			<!-- 驿站类型 -->
			<view class="form-item">
				<text class="item-label">驿站类型</text>
				<picker 
					:range="stationTypes" 
					@change="onStationTypeChange" 
					:disabled="isDisabled"
				>
					<view class="picker-box">
						<text class="picker-text">{{formData.stationType || '请选择驿站类型'}}</text>
						<image src="/static/icons/arrow-down.png" mode="aspectFit" class="picker-arrow"></image>
					</view>
				</picker>
			</view>
			
			<!-- 所属品牌 -->
			<view class="form-item">
				<text class="item-label">所属品牌</text>
				<picker 
					:range="brands" 
					@change="onBrandChange" 
					:disabled="isDisabled"
				>
					<view class="picker-box">
						<text class="picker-text">{{formData.brand || '请选择所属品牌'}}</text>
						<image src="/static/icons/arrow-down.png" mode="aspectFit" class="picker-arrow"></image>
					</view>
				</picker>
			</view>
			
			<!-- 详细地址 -->
			<view class="form-item">
				<text class="item-label">详细地址</text>
				<view class="address-input-wrapper">
					<input 
						class="item-input" 
						type="text" 
						placeholder="请输入详细地址" 
						v-model="formData.address"
						:disabled="isDisabled"
					/>
					<view 
						class="location-btn" 
						@tap="useCurrentLocation"
						v-if="!isDisabled"
					>
						<text>定位</text>
					</view>
				</view>
			</view>
			
			<!-- 联系人 -->
			<view class="form-item">
				<text class="item-label">联系人</text>
				<input 
					class="item-input" 
					type="text" 
					placeholder="请输入联系人姓名" 
					v-model="formData.contactName"
					:disabled="isDisabled"
				/>
			</view>
			
			<!-- 联系电话 -->
			<view class="form-item">
				<text class="item-label">联系电话</text>
				<input 
					class="item-input" 
					type="number" 
					placeholder="请输入联系电话" 
					v-model="formData.contactPhone"
					:disabled="isDisabled"
				/>
			</view>
			
			<!-- 营业执照 -->
			<view class="form-item">
				<text class="item-label">营业执照</text>
				<view class="upload-area" @tap="uploadImage('license')" v-if="!formData.businessLicense || isDisabled">
					<image 
						v-if="formData.businessLicense" 
						:src="formData.businessLicense" 
						mode="aspectFill" 
						class="preview-image"
					></image>
					<view v-else class="upload-placeholder">
						<image src="/static/icons/camera.png" mode="aspectFit"></image>
						<text>点击上传营业执照照片</text>
					</view>
				</view>
				<view v-else class="image-preview">
					<image :src="formData.businessLicense" mode="aspectFill" class="preview-image"></image>
					<view class="delete-btn" @tap.stop="deleteImage('license')">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 授权书 -->
			<view class="form-item">
				<text class="item-label">经营授权书 <text class="optional">(选填)</text></text>
				<view class="upload-area" @tap="uploadImage('authorization')" v-if="!formData.authorization || isDisabled">
					<image 
						v-if="formData.authorization" 
						:src="formData.authorization" 
						mode="aspectFill" 
						class="preview-image"
					></image>
					<view v-else class="upload-placeholder">
						<image src="/static/icons/camera.png" mode="aspectFit"></image>
						<text>点击上传授权书照片</text>
					</view>
				</view>
				<view v-else class="image-preview">
					<image :src="formData.authorization" mode="aspectFill" class="preview-image"></image>
					<view class="delete-btn" @tap.stop="deleteImage('authorization')">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 驿站门头照 -->
			<view class="form-item">
				<text class="item-label">驿站门头照</text>
				<view class="upload-area" @tap="uploadImage('storefront')" v-if="!formData.storefront || isDisabled">
					<image 
						v-if="formData.storefront" 
						:src="formData.storefront" 
						mode="aspectFill" 
						class="preview-image"
					></image>
					<view v-else class="upload-placeholder">
						<image src="/static/icons/camera.png" mode="aspectFit"></image>
						<text>点击上传门头照片</text>
					</view>
				</view>
				<view v-else class="image-preview">
					<image :src="formData.storefront" mode="aspectFill" class="preview-image"></image>
					<view class="delete-btn" @tap.stop="deleteImage('storefront')">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 驿站内部照 -->
			<view class="form-item">
				<text class="item-label">驿站内部照</text>
				<view class="upload-area" @tap="uploadImage('interior')" v-if="!formData.interior || isDisabled">
					<image 
						v-if="formData.interior" 
						:src="formData.interior" 
						mode="aspectFill" 
						class="preview-image"
					></image>
					<view v-else class="upload-placeholder">
						<image src="/static/icons/camera.png" mode="aspectFit"></image>
						<text>点击上传内部照片</text>
					</view>
				</view>
				<view v-else class="image-preview">
					<image :src="formData.interior" mode="aspectFill" class="preview-image"></image>
					<view class="delete-btn" @tap.stop="deleteImage('interior')">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 隐私条款 -->
			<view class="agreement-item">
				<view class="checkbox" @tap="toggleAgreement">
					<view class="checkbox-inner" v-if="formData.agreement"></view>
				</view>
				<text class="agreement-text">我已阅读并同意</text>
				<text class="agreement-link" @tap="viewServiceTerms">《驿站认证服务条款》</text>
			</view>
			
			<!-- 提交按钮 -->
			<view 
				class="submit-btn" 
				:class="{'disabled': !isFormValid || isDisabled}"
				@tap="submitVerification"
			>
				<text>{{verificationStatus === 'rejected' ? '重新提交' : '提交认证'}}</text>
			</view>
			
			<!-- 提示文本 -->
			<view class="tips-section">
				<view class="tip-item">
					<text class="dot">•</text>
					<text class="tip-text">请确保所有照片清晰完整，信息真实有效</text>
				</view>
				<view class="tip-item">
					<text class="dot">•</text>
					<text class="tip-text">驿站认证审核通过后，将获得平台更多专属权益</text>
				</view>
				<view class="tip-item">
					<text class="dot">•</text>
					<text class="tip-text">认证审核需要1-3个工作日，请耐心等待</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				verificationStatus: '', // 空字符串表示未认证，'processing'表示审核中，'approved'表示已认证，'rejected'表示被拒绝
				statusIcon: {
					'processing': '/static/icons/processing.png',
					'approved': '/static/icons/success.png',
					'rejected': '/static/icons/error.png'
				},
				statusText: {
					'processing': '审核中',
					'approved': '认证成功',
					'rejected': '认证失败'
				},
				statusDesc: {
					'processing': '您的驿站认证信息正在审核中，预计1-3个工作日内完成审核，请耐心等待。',
					'approved': '您已通过驿站认证，可以使用平台的全部功能。',
					'rejected': '很抱歉，您的驿站认证未通过审核，请检查认证信息或联系客服。'
				},
				formData: {
					stationName: '',
					stationCode: '',
					stationType: '',
					brand: '',
					address: '',
					contactName: '',
					contactPhone: '',
					businessLicense: '',
					authorization: '',
					storefront: '',
					interior: '',
					agreement: false
				},
				stationTypes: ['社区驿站', '校园驿站', '商超驿站', '便利店驿站', '其他'],
				brands: ['菜鸟驿站', '京东快递', '顺丰驿站', '中通快递', '韵达快递', '圆通快递', '百世快递', '其他'],
				verificationReason: '' // 拒绝原因
			}
		},
		computed: {
			// 判断表单是否有效
			isFormValid() {
				return this.formData.stationName && 
					this.formData.stationCode && 
					this.formData.stationType && 
					this.formData.brand && 
					this.formData.address && 
					this.formData.contactName && 
					this.formData.contactPhone && 
					this.formData.businessLicense && 
					this.formData.storefront && 
					this.formData.interior && 
					this.formData.agreement;
			},
			// 判断表单是否被禁用
			isDisabled() {
				return this.verificationStatus === 'processing';
			}
		},
		onLoad() {
			this.checkVerificationStatus();
		},
		methods: {
			// 检查认证状态
			checkVerificationStatus() {
				// 从本地存储获取认证状态
				const verificationStorage = uni.getStorageSync('station_verification');
				if (verificationStorage) {
					const verification = JSON.parse(verificationStorage);
					this.verificationStatus = verification.status;
					this.formData = verification.formData || this.formData;
					this.verificationReason = verification.reason || '';
				}
			},
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			// 显示帮助信息
			showHelp() {
				uni.showModal({
					title: '关于驿站认证',
					content: '驿站认证是为了验证您作为驿站经营者的真实身份和资质。通过认证后，您可以在平台上发布驿站转让、招聘、设备购买等信息，并享受更多平台提供的专属权益和服务。',
					showCancel: false
				});
			},
			// 驿站类型变化
			onStationTypeChange(e) {
				const index = e.detail.value;
				this.formData.stationType = this.stationTypes[index];
			},
			// 品牌变化
			onBrandChange(e) {
				const index = e.detail.value;
				this.formData.brand = this.brands[index];
			},
			// 使用当前位置
			useCurrentLocation() {
				if (this.isDisabled) {
					return;
				}
				
				uni.showLoading({
					title: '获取位置中...'
				});
				
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						// 逆地址解析
						this.reverseGeocoding(res.latitude, res.longitude);
					},
					fail: () => {
						uni.hideLoading();
						uni.showModal({
							title: '提示',
							content: '获取位置失败，请检查定位权限或手动输入地址',
							showCancel: false
						});
					}
				});
			},
			// 逆地址解析
			reverseGeocoding(latitude, longitude) {
				// 模拟逆地址解析
				setTimeout(() => {
					uni.hideLoading();
					this.formData.address = '浙江省杭州市西湖区文三路478号华星时代广场';
					uni.showToast({
						title: '已获取当前位置',
						icon: 'success'
					});
				}, 1500);
			},
			// 上传图片
			uploadImage(type) {
				if (this.isDisabled) {
					return;
				}
				
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						
						// 模拟上传过程
						uni.showLoading({
							title: '上传中...'
						});
						
						setTimeout(() => {
							uni.hideLoading();
							
							// 根据类型设置不同字段
							switch(type) {
								case 'license':
									this.formData.businessLicense = tempFilePath;
									break;
								case 'authorization':
									this.formData.authorization = tempFilePath;
									break;
								case 'storefront':
									this.formData.storefront = tempFilePath;
									break;
								case 'interior':
									this.formData.interior = tempFilePath;
									break;
							}
						}, 1500);
					}
				});
			},
			// 删除图片
			deleteImage(type) {
				switch(type) {
					case 'license':
						this.formData.businessLicense = '';
						break;
					case 'authorization':
						this.formData.authorization = '';
						break;
					case 'storefront':
						this.formData.storefront = '';
						break;
					case 'interior':
						this.formData.interior = '';
						break;
				}
			},
			// 切换协议同意状态
			toggleAgreement() {
				if (!this.isDisabled) {
					this.formData.agreement = !this.formData.agreement;
				}
			},
			// 查看服务条款
			viewServiceTerms() {
				uni.navigateTo({
					url: '/pages/my/agreement?type=station'
				});
			},
			// 提交认证
			submitVerification() {
				if (!this.isFormValid || this.isDisabled) {
					return;
				}
				
				// 验证手机号
				if (!/^1\d{10}$/.test(this.formData.contactPhone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				// 提交认证信息
				uni.showLoading({
					title: '提交中...'
				});
				
				setTimeout(() => {
					uni.hideLoading();
					
					// 更新认证状态
					this.verificationStatus = 'processing';
					
					// 保存到本地存储
					const verification = {
						status: this.verificationStatus,
						formData: this.formData,
						submitTime: new Date().getTime()
					};
					
					uni.setStorageSync('station_verification', JSON.stringify(verification));
					
					uni.showToast({
						title: '提交成功',
						icon: 'success'
					});
					
					// 模拟审核过程（开发环境用）
					setTimeout(() => {
						// 随机生成审核结果
						const result = Math.random() > 0.3 ? 'approved' : 'rejected';
						this.verificationStatus = result;
						
						// 更新本地存储
						verification.status = result;
						if (result === 'rejected') {
							verification.reason = '营业执照信息与提交的驿站信息不符，请核对后重新提交';
							this.verificationReason = verification.reason;
						}
						
						uni.setStorageSync('station_verification', JSON.stringify(verification));
						
						// 更新用户信息
						if (result === 'approved') {
							const userInfoStorage = uni.getStorageSync('userInfo');
							if (userInfoStorage) {
								const userInfo = JSON.parse(userInfoStorage);
								userInfo.isStationVerified = true;
								userInfo.stationInfo = {
									name: this.formData.stationName,
									code: this.formData.stationCode,
									type: this.formData.stationType,
									brand: this.formData.brand
								};
								uni.setStorageSync('userInfo', JSON.stringify(userInfo));
							}
						}
					}, 5000); // 5秒后返回审核结果（仅供演示）
				}, 1500);
			}
		}
	}
</script>

<style>
	.verify-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn, .right-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image, .right-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.status-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 50rpx 30rpx;
		background-color: #ffffff;
	}
	
	.status-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
	}
	
	.status-icon image {
		width: 100%;
		height: 100%;
	}
	
	.status-text {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
	}
	
	.status-desc {
		font-size: 28rpx;
		color: #666666;
		text-align: center;
		line-height: 1.6;
	}
	
	.form-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
	}
	
	.form-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 30rpx;
	}
	
	.form-item {
		margin-bottom: 30rpx;
	}
	
	.item-label {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.optional {
		font-size: 24rpx;
		color: #999999;
		font-weight: normal;
	}
	
	.item-input {
		width: 100%;
		height: 90rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333333;
		box-sizing: border-box;
	}
	
	.picker-box {
		width: 100%;
		height: 90rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 0 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-sizing: border-box;
	}
	
	.picker-text {
		font-size: 28rpx;
		color: #333333;
	}
	
	.picker-text:empty:before {
		content: '请选择';
		color: #999999;
	}
	
	.picker-arrow {
		width: 30rpx;
		height: 30rpx;
	}
	
	.address-input-wrapper {
		display: flex;
		align-items: center;
	}
	
	.address-input-wrapper .item-input {
		flex: 1;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}
	
	.location-btn {
		width: 120rpx;
		height: 90rpx;
		background-color: #ff5a5f;
		border-top-right-radius: 8rpx;
		border-bottom-right-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.location-btn text {
		font-size: 28rpx;
		color: #ffffff;
	}
	
	.upload-area, .image-preview {
		width: 100%;
		height: 360rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		overflow: hidden;
		position: relative;
	}
	
	.upload-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
	}
	
	.upload-placeholder image {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 20rpx;
	}
	
	.upload-placeholder text {
		font-size: 28rpx;
		color: #999999;
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
	}
	
	.delete-btn {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		width: 60rpx;
		height: 60rpx;
		background-color: rgba(0, 0, 0, 0.5);
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.delete-btn image {
		width: 30rpx;
		height: 30rpx;
	}
	
	.agreement-item {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
	}
	
	.checkbox {
		width: 40rpx;
		height: 40rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.checkbox-inner {
		width: 24rpx;
		height: 24rpx;
		background-color: #ff5a5f;
		border-radius: 4rpx;
	}
	
	.agreement-text {
		font-size: 26rpx;
		color: #666666;
	}
	
	.agreement-link {
		font-size: 26rpx;
		color: #ff5a5f;
	}
	
	.submit-btn {
		width: 100%;
		height: 90rpx;
		background-color: #ff5a5f;
		border-radius: 45rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
	}
	
	.submit-btn.disabled {
		background-color: #cccccc;
	}
	
	.submit-btn text {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: bold;
	}
	
	.tips-section {
		margin-top: 30rpx;
	}
	
	.tip-item {
		display: flex;
		margin-bottom: 16rpx;
	}
	
	.dot {
		margin-right: 10rpx;
		color: #999999;
	}
	
	.tip-text {
		font-size: 24rpx;
		color: #999999;
		line-height: 1.6;
	}
</style> 