<template>
	<view class="wallet-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">我的钱包</text>
			<view class="right-btn" @tap="showHelp">
				<image src="/static/icons/help.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 钱包信息 -->
		<view class="wallet-info">
			<view class="balance-section">
				<text class="balance-label">账户余额(元)</text>
				<text class="balance-value">{{walletInfo.balance.toFixed(2)}}</text>
				<view class="action-buttons">
					<view class="action-btn" @tap="goToRecharge">
						<text>充值</text>
					</view>
					<view class="action-btn" @tap="goToWithdraw">
						<text>提现</text>
					</view>
				</view>
			</view>
			
			<view class="asset-section">
				<view class="asset-item" @tap="goToPoint">
					<text class="asset-value">{{walletInfo.points}}</text>
					<text class="asset-label">积分</text>
				</view>
				<view class="asset-line"></view>
				<view class="asset-item" @tap="goToCoupon">
					<text class="asset-value">{{walletInfo.coupons}}</text>
					<text class="asset-label">优惠券</text>
				</view>
				<view class="asset-line"></view>
				<view class="asset-item" @tap="goToRedPacket">
					<text class="asset-value">{{walletInfo.redPackets}}</text>
					<text class="asset-label">红包</text>
				</view>
			</view>
		</view>
		
		<!-- 服务选项 -->
		<view class="service-section">
			<view class="service-item" @tap="goToBill">
				<image src="/static/icons/bill.png" mode="aspectFit" class="service-icon"></image>
				<text class="service-name">账单明细</text>
			</view>
			<view class="service-item" @tap="goToBankCard">
				<image src="/static/icons/bankcard.png" mode="aspectFit" class="service-icon"></image>
				<text class="service-name">银行卡</text>
			</view>
			<view class="service-item" @tap="goToSecurity">
				<image src="/static/icons/security.png" mode="aspectFit" class="service-icon"></image>
				<text class="service-name">安全中心</text>
			</view>
			<view class="service-item" @tap="goToService">
				<image src="/static/icons/service.png" mode="aspectFit" class="service-icon"></image>
				<text class="service-name">联系客服</text>
			</view>
		</view>
		
		<!-- 交易记录 -->
		<view class="record-section">
			<view class="section-header">
				<text class="section-title">交易记录</text>
				<view class="more-btn" @tap="goToBill">
					<text>更多</text>
					<image src="/static/icons/right.png" mode="aspectFit" class="right-icon"></image>
				</view>
			</view>
			
			<view v-if="recordList.length === 0" class="empty-tip">
				<image src="/static/images/empty.png" mode="aspectFit"></image>
				<text>暂无交易记录</text>
			</view>
			
			<view v-else class="record-list">
				<view 
					v-for="(record, index) in recordList" 
					:key="record.id"
					class="record-item"
				>
					<view class="record-left">
						<text class="record-title">{{record.title}}</text>
						<text class="record-time">{{formatTime(record.time)}}</text>
					</view>
					<view class="record-right">
						<text :class="['record-amount', record.type === 'in' ? 'income' : 'expense']">
							{{record.type === 'in' ? '+' : '-'}}{{record.amount.toFixed(2)}}
						</text>
						<text class="record-status">{{record.status}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				walletInfo: {
					balance: 0,
					points: 0,
					coupons: 0,
					redPackets: 0
				},
				recordList: []
			}
		},
		onLoad() {
			this.loadWalletData();
			this.loadRecordData();
		},
		methods: {
			// 加载钱包数据
			loadWalletData() {
				// 从本地存储获取钱包数据
				const walletStorage = uni.getStorageSync('wallet_info');
				if (walletStorage) {
					this.walletInfo = JSON.parse(walletStorage);
				} else {
					// 测试数据
					this.walletInfo = {
						balance: 520.88,
						points: 1280,
						coupons: 3,
						redPackets: 2
					};
					// 保存到本地存储
					uni.setStorageSync('wallet_info', JSON.stringify(this.walletInfo));
				}
			},
			// 加载交易记录数据
			loadRecordData() {
				// 从本地存储获取交易记录
				const recordStorage = uni.getStorageSync('transaction_records');
				if (recordStorage) {
					this.recordList = JSON.parse(recordStorage);
				} else {
					// 测试数据
					this.recordList = [
						{
							id: 'tr_001',
							title: '充值',
							time: Date.now() - 3600000,
							amount: 200,
							type: 'in',
							status: '成功'
						},
						{
							id: 'tr_002',
							title: '购买电子秤',
							time: Date.now() - 86400000,
							amount: 258,
							type: 'out',
							status: '成功'
						},
						{
							id: 'tr_003',
							title: '推广收入',
							time: Date.now() - 172800000,
							amount: 88.88,
							type: 'in',
							status: '成功'
						}
					];
					// 保存到本地存储
					uni.setStorageSync('transaction_records', JSON.stringify(this.recordList));
				}
			},
			// 格式化时间
			formatTime(timestamp) {
				const date = new Date(timestamp);
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				const hour = date.getHours().toString().padStart(2, '0');
				const minute = date.getMinutes().toString().padStart(2, '0');
				
				return `${year}-${month}-${day} ${hour}:${minute}`;
			},
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			// 显示帮助信息
			showHelp() {
				uni.showModal({
					title: '钱包使用说明',
					content: '钱包余额可用于在平台内消费。\n充值需满足最低单笔10元，提现需满足最低单笔100元。\n如有问题请联系客服。',
					showCancel: false
				});
			},
			// 前往充值页面
			goToRecharge() {
				uni.showModal({
					title: '充值提示',
					content: '当前为测试环境，无法进行实际充值操作。',
					showCancel: false
				});
			},
			// 前往提现页面
			goToWithdraw() {
				if (this.walletInfo.balance < 100) {
					uni.showModal({
						title: '提示',
						content: '账户余额不足100元，暂时无法提现',
						showCancel: false
					});
					return;
				}
				
				uni.showModal({
					title: '提现提示',
					content: '当前为测试环境，无法进行实际提现操作。',
					showCancel: false
				});
			},
			// 前往积分页面
			goToPoint() {
				uni.showToast({
					title: '积分功能开发中',
					icon: 'none'
				});
			},
			// 前往优惠券页面
			goToCoupon() {
				uni.showToast({
					title: '优惠券功能开发中',
					icon: 'none'
				});
			},
			// 前往红包页面
			goToRedPacket() {
				uni.showToast({
					title: '红包功能开发中',
					icon: 'none'
				});
			},
			// 前往账单明细页面
			goToBill() {
				uni.showToast({
					title: '账单明细功能开发中',
					icon: 'none'
				});
			},
			// 前往银行卡页面
			goToBankCard() {
				uni.showToast({
					title: '银行卡功能开发中',
					icon: 'none'
				});
			},
			// 前往安全中心页面
			goToSecurity() {
				uni.showToast({
					title: '安全中心功能开发中',
					icon: 'none'
				});
			},
			// 前往客服页面
			goToService() {
				uni.showToast({
					title: '正在连接客服...',
					icon: 'none'
				});
			}
		}
	}
</script>

<style>
	.wallet-container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}
	
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn, .right-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image, .right-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.wallet-info {
		background-color: #ff5a5f;
		padding: 40rpx 30rpx 30rpx;
		color: #ffffff;
		border-bottom-left-radius: 30rpx;
		border-bottom-right-radius: 30rpx;
		box-shadow: 0 10rpx 20rpx rgba(255, 90, 95, 0.2);
	}
	
	.balance-section {
		text-align: center;
		margin-bottom: 40rpx;
	}
	
	.balance-label {
		font-size: 28rpx;
		opacity: 0.9;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.balance-value {
		font-size: 60rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
		display: block;
	}
	
	.action-buttons {
		display: flex;
		justify-content: center;
	}
	
	.action-btn {
		width: 160rpx;
		height: 60rpx;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 20rpx;
	}
	
	.action-btn text {
		font-size: 28rpx;
	}
	
	.asset-section {
		display: flex;
		justify-content: space-around;
		margin-top: 20rpx;
	}
	
	.asset-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.asset-value {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}
	
	.asset-label {
		font-size: 24rpx;
		opacity: 0.9;
	}
	
	.asset-line {
		width: 2rpx;
		height: 50rpx;
		background-color: rgba(255, 255, 255, 0.3);
		align-self: center;
	}
	
	.service-section {
		margin: 20rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
	}
	
	.service-item {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.service-icon {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 10rpx;
	}
	
	.service-name {
		font-size: 24rpx;
		color: #666666;
	}
	
	.record-section {
		margin: 0 20rpx 20rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 30rpx;
	}
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.more-btn {
		display: flex;
		align-items: center;
	}
	
	.more-btn text {
		font-size: 24rpx;
		color: #999999;
		margin-right: 10rpx;
	}
	
	.right-icon {
		width: 24rpx;
		height: 24rpx;
	}
	
	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40rpx 0;
	}
	
	.empty-tip image {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 20rpx;
	}
	
	.empty-tip text {
		font-size: 28rpx;
		color: #999999;
	}
	
	.record-list {
		
	}
	
	.record-item {
		display: flex;
		justify-content: space-between;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.record-item:last-child {
		border-bottom: none;
	}
	
	.record-left {
		display: flex;
		flex-direction: column;
	}
	
	.record-title {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 10rpx;
	}
	
	.record-time {
		font-size: 24rpx;
		color: #999999;
	}
	
	.record-right {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}
	
	.record-amount {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}
	
	.income {
		color: #ff5a5f;
	}
	
	.expense {
		color: #333333;
	}
	
	.record-status {
		font-size: 24rpx;
		color: #34c759;
	}
</style> 