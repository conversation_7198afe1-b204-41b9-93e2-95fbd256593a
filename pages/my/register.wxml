<!-- pages/my/register.wxml -->
<view class="register-container">
  <view class="header">
    <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
    <text class="title">注册账号</text>
  </view>
  
  <!-- 注册表单 -->
  <view class="form-container">
    <!-- 用户名输入 -->
    <view class="input-group">
      <text class="label">用户名</text>
      <view class="input-box">
        <input type="text" value="{{username}}" bindinput="inputUsername" placeholder="请设置用户名" maxlength="20" />
      </view>
    </view>
    
    <!-- 手机号输入 -->
    <view class="input-group">
      <text class="label">手机号码</text>
      <view class="input-box">
        <text class="prefix">+86</text>
        <input type="number" value="{{phoneNumber}}" bindinput="inputPhoneNumber" placeholder="请输入手机号码" maxlength="11" />
      </view>
    </view>
    
    <!-- 验证码输入 -->
    <view class="input-group">
      <text class="label">验证码</text>
      <view class="input-box">
        <input type="number" value="{{code}}" bindinput="inputCode" placeholder="请输入验证码" maxlength="6" />
        <view class="verify-btn {{!isValidPhone() || countdown > 0 ? 'disabled' : ''}}" bindtap="getVerificationCode">
          <text>{{codeText}}</text>
        </view>
      </view>
    </view>
    
    <!-- 密码输入 -->
    <view class="input-group">
      <text class="label">密码</text>
      <view class="input-box">
        <input type="{{showPassword ? 'text' : 'password'}}" value="{{password}}" bindinput="inputPassword" placeholder="请设置6-20位密码" />
        <view class="eye-icon" bindtap="togglePasswordVisibility">
          <image src="{{showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'}}" mode="aspectFit"></image>
        </view>
      </view>
    </view>
    
    <!-- 注册按钮 -->
    <view class="register-btn {{!canRegister || loading ? 'disabled' : ''}}" bindtap="register">
      <text>注册</text>
    </view>
    
    <!-- 返回登录 -->
    <view class="back-login" bindtap="navigateToLogin">
      <text>已有账号？返回登录</text>
    </view>
    
    <!-- 用户协议 -->
    <view class="agreement">
      <checkbox checked="{{agreeProtocol}}" bindtap="toggleAgreement" />
      <text>注册即表示同意</text>
      <text class="link" bindtap="navigateToAgreement">《用户协议》</text>
      <text>和</text>
      <text class="link" bindtap="navigateToPrivacy">《隐私政策》</text>
    </view>
  </view>
</view> 