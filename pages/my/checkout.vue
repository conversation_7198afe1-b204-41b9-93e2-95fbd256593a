<template>
	<view class="checkout-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">确认订单</text>
		</view>
		
		<!-- 地址 -->
		<view class="address-section" @tap="selectAddress">
			<view class="address-empty" v-if="!selectedAddress">
				<image src="/static/icons/location.png" mode="aspectFit" class="location-icon"></image>
				<text class="add-address">请添加收货地址</text>
				<image src="/static/icons/right.png" mode="aspectFit" class="right-icon"></image>
			</view>
			<view class="address-info" v-else>
				<view class="address-details">
					<view class="user-info">
						<text class="name">{{selectedAddress.name}}</text>
						<text class="phone">{{formatPhone(selectedAddress.phone)}}</text>
					</view>
					<text class="address-text">{{selectedAddress.province}}{{selectedAddress.city}}{{selectedAddress.district}}{{selectedAddress.address}}</text>
				</view>
				<image src="/static/icons/right.png" mode="aspectFit" class="right-icon"></image>
			</view>
		</view>
		
		<!-- 订单商品列表 -->
		<view class="goods-section">
			<view class="section-title">
				<text>商品信息</text>
			</view>
			<view class="goods-list">
				<view class="goods-item" v-for="(item, index) in orderItems" :key="index">
					<image :src="item.image" mode="aspectFill" class="goods-image"></image>
					<view class="goods-info">
						<text class="goods-name">{{item.name}}</text>
						<text class="goods-spec" v-if="item.spec">{{item.spec}}</text>
						<view class="goods-price-qty">
							<text class="goods-price">¥{{item.price.toFixed(2)}}</text>
							<text class="goods-qty">×{{item.quantity}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 配送方式 -->
		<view class="delivery-section">
			<view class="section-title">
				<text>配送方式</text>
			</view>
			<view class="delivery-options">
				<view class="delivery-option" :class="{'selected': deliveryMethod === 'express'}" @tap="selectDelivery('express')">
					<text>快递配送</text>
					<view class="select-circle" v-if="deliveryMethod === 'express'">
						<view class="inner-circle"></view>
					</view>
					<view class="select-circle" v-else></view>
				</view>
				<view class="delivery-option" :class="{'selected': deliveryMethod === 'self'}" @tap="selectDelivery('self')">
					<text>到店自提</text>
					<view class="select-circle" v-if="deliveryMethod === 'self'">
						<view class="inner-circle"></view>
					</view>
					<view class="select-circle" v-else></view>
				</view>
			</view>
		</view>
		
		<!-- 支付方式 -->
		<view class="payment-section">
			<view class="section-title">
				<text>支付方式</text>
			</view>
			<view class="payment-options">
				<view class="payment-option" :class="{'selected': paymentMethod === 'wechat'}" @tap="selectPayment('wechat')">
					<view class="option-left">
						<image src="/static/icons/wechat-pay.png" mode="aspectFit" class="payment-icon"></image>
						<text>微信支付</text>
					</view>
					<view class="select-circle" v-if="paymentMethod === 'wechat'">
						<view class="inner-circle"></view>
					</view>
					<view class="select-circle" v-else></view>
				</view>
				<view class="payment-option" :class="{'selected': paymentMethod === 'balance'}" @tap="selectPayment('balance')" v-if="userBalance > 0">
					<view class="option-left">
						<image src="/static/icons/wallet.png" mode="aspectFit" class="payment-icon"></image>
						<text>余额支付</text>
						<text class="balance-info">(可用余额: ¥{{userBalance/100}})</text>
					</view>
					<view class="select-circle" v-if="paymentMethod === 'balance'">
						<view class="inner-circle"></view>
					</view>
					<view class="select-circle" v-else></view>
				</view>
			</view>
		</view>
		
		<!-- 订单备注 -->
		<view class="remark-section">
			<view class="section-item">
				<text>订单备注</text>
				<input class="remark-input" type="text" v-model="remark" placeholder="选填，请填写其他需求" maxlength="50" />
			</view>
		</view>
		
		<!-- 金额明细 -->
		<view class="amount-section">
			<view class="amount-item">
				<text>商品金额</text>
				<text>¥{{goodsAmount.toFixed(2)}}</text>
			</view>
			<view class="amount-item">
				<text>运费</text>
				<text>¥{{deliveryFee.toFixed(2)}}</text>
			</view>
		</view>
		
		<!-- 底部提交栏 -->
		<view class="submit-bar">
			<view class="total-amount">
				<text>合计：</text>
				<text class="amount">¥{{totalAmount.toFixed(2)}}</text>
			</view>
			<view class="submit-btn" @tap="submitOrder" :class="{'disabled': isSubmitting}">
				<text>{{isSubmitting ? '提交中...' : '提交订单'}}</text>
			</view>
		</view>
		
		<!-- 支付组件 -->
		<wx-pay ref="wxPay"
			:order-id="createdOrderId"
			:amount="convertToFen(totalAmount)"
			:description="'驿站帮-商品购买'"
			:visible.sync="showPayPanel"
			@pay-success="handlePaySuccess"
		></wx-pay>
	</view>
</template>

<script>
	import WxPay from '@/components/wx-pay.vue'
	
	export default {
		components: {
			WxPay
		},
		data() {
			return {
				selectedAddress: null,
				orderItems: [], // 用于存储所有订单商品，包括购物车或直接购买的商品
				deliveryMethod: 'express',
				paymentMethod: 'wechat',
				deliveryFee: 0,
				remark: '',
				buyNow: false,  // 标记是否为直接购买
				isSubmitting: false, // 是否正在提交订单
				createdOrderId: '', // 创建的订单ID
				showPayPanel: false, // 是否显示支付面板
				userBalance: 0 // 用户余额（单位：分）
			}
		},
		computed: {
			// 商品总额
			goodsAmount() {
				return this.orderItems.reduce((total, item) => {
					return total + (item.price * item.quantity);
				}, 0);
			},
			// 总金额
			totalAmount() {
				return this.goodsAmount + this.deliveryFee;
			}
		},
		onLoad(options) {
			// 检查用户登录状态
			this.checkLoginStatus();
			
			// 检查是否为直接购买模式
			if (options.buyNow && options.goodsId) {
				this.buyNow = true;
				this.loadSingleGoodsInfo(options.goodsId, options.quantity || 1);
			} else {
				// 常规购物车结算
				this.loadCartItems();
			}
			
			this.loadAddress();
			this.calculateDeliveryFee();
		},
		methods: {
			// 检查用户登录状态
			checkLoginStatus() {
				const token = uni.getStorageSync('token');
				const userInfoStr = uni.getStorageSync('userInfo');
				
				if (!token || !userInfoStr) {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/my/login'
						});
					}, 1500);
					return;
				}
				
				// 获取用户余额
				const userInfo = JSON.parse(userInfoStr);
				this.userBalance = userInfo.balance || 0;
			},
			
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			
			// 加载单个商品信息（直接购买）
			async loadSingleGoodsInfo(goodsId, quantity) {
				// 如果有商品ID，获取商品信息
				if (goodsId) {
					let found = false;
					
					// 尝试从API获取商品信息
					try {
						const res = await this.$api.product.getProductDetail(goodsId);
						if (res.code === 0 && res.data) {
							const product = res.data;
							const orderItem = {
								id: product.id,
								name: product.name,
								price: product.price,
								image: product.cover,
								spec: product.spec || '标准规格',
								quantity: parseInt(quantity) || 1
							};
							
							this.orderItems = [orderItem];
							found = true;
						}
					} catch (err) {
						console.error('获取商品信息失败', err);
						uni.showToast({
							title: '获取商品信息失败',
							icon: 'none'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
					
					if (!found) {
						uni.showToast({
							title: '商品信息不存在',
							icon: 'none'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				}
				
				// 计算配送费
				this.calculateDeliveryFee();
			},
			
			// 加载购物车中选中的商品
			loadCartItems() {
				// 从本地存储获取购物车选中的商品
				const cartStorage = uni.getStorageSync('cart');
				if (cartStorage) {
					const cart = JSON.parse(cartStorage);
					this.orderItems = cart.filter(item => item.selected);
					
					// 如果没有选中商品，返回购物车页面
					if (this.orderItems.length === 0) {
						uni.showToast({
							title: '请先选择商品',
							icon: 'none'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				}
			},
			
			// 加载地址
			loadAddress() {
				// 从本地存储获取默认地址
				const addressStorage = uni.getStorageSync('address_list');
				if (addressStorage) {
					const addresses = JSON.parse(addressStorage);
					// 查找默认地址
					this.selectedAddress = addresses.find(addr => addr.isDefault) || (addresses.length > 0 ? addresses[0] : null);
				}
			},
			
			// 选择地址
			selectAddress() {
				// 跳转到地址列表页面
				uni.navigateTo({
					url: '/pages/my/address-list?select=true'
				});
			},
			
			// 选择配送方式
			selectDelivery(method) {
				this.deliveryMethod = method;
				this.calculateDeliveryFee();
			},
			
			// 选择支付方式
			selectPayment(method) {
				// 如果是选择余额支付，检查余额是否足够
				if (method === 'balance' && this.userBalance < this.convertToFen(this.totalAmount)) {
					uni.showToast({
						title: '余额不足',
						icon: 'none'
					});
					return;
				}
				
				this.paymentMethod = method;
			},
			
			// 计算配送费
			calculateDeliveryFee() {
				if (this.deliveryMethod === 'express') {
					this.deliveryFee = this.goodsAmount >= 99 ? 0 : 10;
				} else {
					this.deliveryFee = 0;
				}
			},
			
			// 格式化手机号
			formatPhone(phone) {
				if (!phone) return '';
				return phone.substr(0, 3) + '****' + phone.substr(7);
			},
			
			// 将元转换为分
			convertToFen(amount) {
				return Math.round(amount * 100);
			},
			
			// 提交订单
			async submitOrder() {
				// 验证地址
				if (!this.selectedAddress) {
					uni.showToast({
						title: '请选择收货地址',
						icon: 'none'
					});
					return;
				}
				
				// 防止重复提交
				if (this.isSubmitting) return;
				this.isSubmitting = true;
				
				// 显示加载
				uni.showLoading({
					title: '提交中...'
				});
				
				try {
					// 生成订单
					const orderItems = this.orderItems.map(item => ({
						goods_id: item.id,
						name: item.name,
						price: this.convertToFen(item.price), // 转换为分
						quantity: item.quantity,
						image: item.image,
						spec: item.spec || ''
					}));
					
					// 计算总金额（分）
					const totalFee = this.convertToFen(this.totalAmount);
					
					// 创建订单
					const result = await wx.cloud.callFunction({
						name: 'createOrder',
						data: {
							items: orderItems,
							address: this.selectedAddress,
							delivery_method: this.deliveryMethod,
							payment_method: this.paymentMethod,
							goods_amount: this.convertToFen(this.goodsAmount),
							delivery_fee: this.convertToFen(this.deliveryFee),
							total_fee: totalFee,
							remark: this.remark
						}
					});
					
					// 请求成功
					if (result.result && result.result.code === 0) {
						// 保存创建的订单ID
						this.createdOrderId = result.result.data.order_id;
						
						// 如果是购物车结算，清除已购买的商品
						if (!this.buyNow) {
							const cartStorage = uni.getStorageSync('cart');
							if (cartStorage) {
								const cart = JSON.parse(cartStorage);
								const newCart = cart.filter(item => !item.selected);
								uni.setStorageSync('cart', JSON.stringify(newCart));
							}
						}
						
						// 选择支付方式
						if (this.paymentMethod === 'wechat') {
							// 微信支付
							this.showPayPanel = true;
						} else if (this.paymentMethod === 'balance') {
							// 余额支付
							await this.balancePay();
						}
					} else {
						uni.showToast({
							title: result.result.msg || '创建订单失败',
							icon: 'none'
						});
					}
				} catch (err) {
					console.error('提交订单失败', err);
					uni.showToast({
						title: '订单提交异常，请重试',
						icon: 'none'
					});
				} finally {
					this.isSubmitting = false;
					uni.hideLoading();
				}
			},
			
			// 余额支付
			async balancePay() {
				uni.showLoading({
					title: '支付中...'
				});
				
				try {
					// 调用余额支付云函数
					const result = await wx.cloud.callFunction({
						name: 'balancePay',
						data: {
							orderId: this.createdOrderId,
							amount: this.convertToFen(this.totalAmount)
						}
					});
					
					if (result.result && result.result.code === 0) {
						// 支付成功
						uni.hideLoading();
						uni.showToast({
							title: '支付成功',
							icon: 'success'
						});
						
						// 更新用户余额
						const userInfoStr = uni.getStorageSync('userInfo');
						if (userInfoStr) {
							const userInfo = JSON.parse(userInfoStr);
							userInfo.balance -= this.convertToFen(this.totalAmount);
							uni.setStorageSync('userInfo', JSON.stringify(userInfo));
						}
						
						// 跳转到订单列表
						setTimeout(() => {
							uni.redirectTo({
								url: '/pages/my/orders'
							});
						}, 1500);
					} else {
						uni.hideLoading();
						uni.showToast({
							title: result.result.msg || '支付失败',
							icon: 'none'
						});
					}
				} catch (err) {
					console.error('余额支付失败', err);
					uni.hideLoading();
					uni.showToast({
						title: '支付失败，请重试',
						icon: 'none'
					});
				}
			},
			
			// 处理支付成功回调
			handlePaySuccess(payResult) {
				console.log('支付成功', payResult);
				
				// 支付成功后的处理
				uni.showToast({
					title: '支付成功',
					icon: 'success'
				});
				
				// 跳转到订单列表
				setTimeout(() => {
					uni.redirectTo({
						url: '/pages/my/orders'
					});
				}, 1500);
			}
		}
	}
</script>

<style>
	.checkout-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 150rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.address-section {
		background-color: #ffffff;
		margin-top: 20rpx;
		padding: 30rpx;
	}
	
	.address-empty {
		display: flex;
		align-items: center;
	}
	
	.location-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}
	
	.add-address {
		flex: 1;
		font-size: 30rpx;
		color: #333333;
	}
	
	.right-icon {
		width: 30rpx;
		height: 30rpx;
	}
	
	.address-info {
		display: flex;
		align-items: flex-start;
	}
	
	.address-details {
		flex: 1;
	}
	
	.user-info {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-right: 20rpx;
	}
	
	.phone {
		font-size: 28rpx;
		color: #666666;
	}
	
	.address-text {
		font-size: 28rpx;
		color: #333333;
		line-height: 1.4;
	}
	
	.goods-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
	}
	
	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 30rpx;
	}
	
	.goods-list {
		
	}
	
	.goods-item {
		display: flex;
		margin-bottom: 30rpx;
	}
	
	.goods-item:last-child {
		margin-bottom: 0;
	}
	
	.goods-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
	}
	
	.goods-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
	
	.goods-name {
		font-size: 28rpx;
		color: #333333;
		line-height: 1.4;
		margin-bottom: 10rpx;
	}
	
	.goods-spec {
		font-size: 26rpx;
		color: #999999;
		margin-bottom: 10rpx;
	}
	
	.goods-price-qty {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.goods-price {
		font-size: 30rpx;
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.goods-qty {
		font-size: 26rpx;
		color: #999999;
	}
	
	.delivery-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
	}
	
	.delivery-options {
		
	}
	
	.delivery-option {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.delivery-option:last-child {
		border-bottom: none;
	}
	
	.delivery-option.selected {
		color: #ff5a5f;
	}
	
	.select-circle {
		width: 40rpx;
		height: 40rpx;
		border-radius: 50%;
		border: 2rpx solid #ddd;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.selected .select-circle {
		border-color: #ff5a5f;
	}
	
	.inner-circle {
		width: 24rpx;
		height: 24rpx;
		border-radius: 50%;
		background-color: #ff5a5f;
	}
	
	.payment-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
	}
	
	.payment-options {
		
	}
	
	.payment-option {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.payment-option:last-child {
		border-bottom: none;
	}
	
	.option-left {
		display: flex;
		align-items: center;
	}
	
	.payment-icon {
		width: 60rpx;
		height: 60rpx;
		margin-right: 20rpx;
	}
	
	.payment-option.selected {
		color: #ff5a5f;
	}
	
	.balance-info {
		font-size: 24rpx;
		color: #999999;
		margin-left: 10rpx;
	}
	
	.coupon-section, .remark-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
	}
	
	.section-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.item-right {
		display: flex;
		align-items: center;
	}
	
	.right-text {
		font-size: 28rpx;
		color: #666666;
		margin-right: 10rpx;
	}
	
	.remark-input {
		flex: 1;
		text-align: right;
		font-size: 28rpx;
		color: #333333;
		height: 60rpx;
	}
	
	.amount-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
	}
	
	.amount-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
	}
	
	.amount-item:last-child {
		margin-bottom: 0;
	}
	
	.discount {
		color: #ff5a5f;
	}
	
	.submit-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #ffffff;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.total-amount {
		flex: 1;
		display: flex;
		align-items: center;
	}
	
	.amount {
		font-size: 36rpx;
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.submit-btn {
		width: 240rpx;
		height: 80rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.submit-btn.disabled {
		background-color: #cccccc;
	}
	
	.submit-btn text {
		font-size: 30rpx;
		color: #ffffff;
		font-weight: bold;
	}
</style> 