<template>
	<view class="service-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">在线客服</text>
		</view>
		
		<!-- 客服头部 -->
		<view class="service-header">
			<image src="/static/images/service-banner.png" mode="aspectFill" class="banner-image"></image>
			<view class="service-info">
				<view class="avatar-container">
					<image src="/static/images/service-avatar.png" mode="aspectFill" class="avatar"></image>
				</view>
				<view class="info-content">
					<text class="name">驿站帮客服</text>
					<text class="status">在线 | 9:00-18:00</text>
				</view>
				<view class="contact-btn" @tap="contactService">
					<image src="/static/icons/chat.png" mode="aspectFit"></image>
					<text>联系客服</text>
				</view>
			</view>
		</view>
		
		<!-- 常见问题 -->
		<view class="faq-section">
			<view class="section-title">
				<text>常见问题</text>
				<view class="more-btn" @tap="navigateToFaqList">
					<text>查看全部</text>
					<image src="/static/icons/right.png" mode="aspectFit"></image>
				</view>
			</view>
			
			<view class="faq-list">
				<view 
					v-for="(item, index) in faqList" 
					:key="index" 
					class="faq-item"
					@tap="toggleFaq(index)"
				>
					<view class="faq-header">
						<text class="faq-title">{{item.title}}</text>
						<image 
							:src="item.expanded ? '/static/icons/arrow-up.png' : '/static/icons/arrow-down.png'" 
							mode="aspectFit"
							class="arrow-icon"
						></image>
					</view>
					<view class="faq-content" v-if="item.expanded">
						<text>{{item.content}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 问题反馈 -->
		<view class="feedback-section">
			<view class="section-title">
				<text>问题反馈</text>
			</view>
			
			<view class="feedback-content">
				<view class="input-group">
					<text class="label">问题类型</text>
					<view class="picker-container" @tap="showTypePicker">
						<text class="picker-value">{{feedbackType || '请选择问题类型'}}</text>
						<image src="/static/icons/arrow-down.png" mode="aspectFit" class="picker-icon"></image>
					</view>
				</view>
				
				<view class="input-group">
					<text class="label">问题描述</text>
					<textarea 
						class="feedback-textarea" 
						placeholder="请详细描述您遇到的问题，以便我们更好地为您解决" 
						maxlength="500"
						v-model="feedbackContent"
						placeholder-style="color: #C8C8C8;"
					></textarea>
					<view class="word-count">
						<text>{{feedbackContent.length}}/500</text>
					</view>
				</view>
				
				<view class="input-group">
					<text class="label">上传截图 (选填)</text>
					<view class="photo-list">
						<view 
							v-for="(item, index) in photoList" 
							:key="index" 
							class="photo-item"
						>
							<image :src="item" mode="aspectFill" class="preview-image"></image>
							<view class="delete-btn" @tap="deletePhoto(index)">
								<image src="/static/icons/close.png" mode="aspectFit"></image>
							</view>
						</view>
						<view class="upload-btn" @tap="choosePhoto" v-if="photoList.length < 3">
							<image src="/static/icons/camera.png" mode="aspectFit"></image>
							<text>上传图片</text>
						</view>
					</view>
					<text class="photo-tip">最多可上传3张图片</text>
				</view>
				
				<view class="input-group">
					<text class="label">联系方式</text>
					<input 
						type="text"
						class="contact-input"
						placeholder="请留下您的手机号或邮箱"
						v-model="contactInfo"
					/>
				</view>
				
				<button class="submit-btn" @tap="submitFeedback">提交反馈</button>
			</view>
		</view>
		
		<!-- 底部快捷入口 -->
		<view class="quick-links">
			<view class="quick-item" @tap="navigateTo('/pages/my/history')">
				<image src="/static/icons/history.png" mode="aspectFit"></image>
				<text>浏览历史</text>
			</view>
			<view class="quick-item" @tap="navigateTo('/pages/my/favorites')">
				<image src="/static/icons/favorite.png" mode="aspectFit"></image>
				<text>我的收藏</text>
			</view>
			<view class="quick-item" @tap="navigateTo('/pages/my/orders')">
				<image src="/static/icons/order.png" mode="aspectFit"></image>
				<text>我的订单</text>
			</view>
			<view class="quick-item" @tap="navigateTo('/pages/my/wallet')">
				<image src="/static/icons/wallet.png" mode="aspectFit"></image>
				<text>我的钱包</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				faqList: [
					{ 
						title: '如何发布驿站转让信息？', 
						content: '您可以点击首页的"发布"按钮，选择"驿站转让"，填写相关信息并上传图片，提交审核后即可发布。请确保您提供的信息真实有效，以便更快通过审核。', 
						expanded: false 
					},
					{ 
						title: '如何修改或删除已发布的信息？', 
						content: '您可以在"我的" > "我的发布"中找到您发布的所有信息，点击对应条目后可以选择编辑或删除。请注意，已经审核通过的信息修改后需要重新审核。', 
						expanded: false 
					},
					{ 
						title: '如何联系卖家或服务提供者？', 
						content: '在浏览详情页时，您可以直接点击"联系TA"按钮拨打电话，或者使用站内消息与对方沟通。为保护您的隐私，建议优先使用站内消息功能。', 
						expanded: false 
					},
					{ 
						title: '如何保障交易安全？', 
						content: '为了保障您的交易安全，建议使用平台提供的担保交易服务，先行验货，确认无误后再完成付款。对于大额交易，请谨慎核实对方身份和商品信息，必要时可联系平台客服协助。', 
						expanded: false 
					},
					{ 
						title: '如何提高我的信息曝光量？', 
						content: '您可以选择信息推广服务，包括置顶、加精、首页推荐等，这些都能有效提高您的信息曝光率。此外，完整的信息描述和清晰的图片也能吸引更多潜在买家。', 
						expanded: false 
					}
				],
				feedbackType: '',
				feedbackContent: '',
				photoList: [],
				contactInfo: '',
				typeOptions: ['功能建议', '内容问题', '账号问题', '支付问题', '其他问题']
			}
		},
		methods: {
			// 展开/收起常见问题
			toggleFaq(index) {
				this.faqList = this.faqList.map((item, i) => {
					if (i === index) {
						return { ...item, expanded: !item.expanded };
					} else {
						return { ...item, expanded: false };
					}
				});
			},
			// 显示问题类型选择器
			showTypePicker() {
				uni.showActionSheet({
					itemList: this.typeOptions,
					success: (res) => {
						this.feedbackType = this.typeOptions[res.tapIndex];
					}
				});
			},
			// 选择照片
			choosePhoto() {
				if (this.photoList.length >= 3) {
					uni.showToast({
						title: '最多上传3张图片',
						icon: 'none'
					});
					return;
				}
				
				uni.chooseImage({
					count: 3 - this.photoList.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 模拟上传过程
						uni.showLoading({
							title: '上传中...'
						});
						
						setTimeout(() => {
							uni.hideLoading();
							this.photoList = [...this.photoList, ...res.tempFilePaths];
						}, 1000);
					}
				});
			},
			// 删除照片
			deletePhoto(index) {
				this.photoList.splice(index, 1);
			},
			// 提交反馈
			submitFeedback() {
				// 验证表单
				if (!this.feedbackType) {
					uni.showToast({
						title: '请选择问题类型',
						icon: 'none'
					});
					return;
				}
				
				if (this.feedbackContent.trim().length < 10) {
					uni.showToast({
						title: '问题描述不能少于10个字',
						icon: 'none'
					});
					return;
				}
				
				if (!this.contactInfo) {
					uni.showToast({
						title: '请填写联系方式',
						icon: 'none'
					});
					return;
				}
				
				// 模拟提交过程
				uni.showLoading({
					title: '提交中...'
				});
				
				setTimeout(() => {
					uni.hideLoading();
					
					// 保存反馈信息
					this.saveFeedback();
					
					uni.showToast({
						title: '感谢您的反馈',
						icon: 'success'
					});
					
					// 清空表单
					this.feedbackType = '';
					this.feedbackContent = '';
					this.photoList = [];
					this.contactInfo = '';
					
				}, 1500);
			},
			// 保存反馈信息
			saveFeedback() {
				const feedback = {
					id: 'feedback_' + new Date().getTime(),
					type: this.feedbackType,
					content: this.feedbackContent,
					photos: this.photoList,
					contactInfo: this.contactInfo,
					status: '处理中',
					createTime: new Date().getTime()
				};
				
				// 保存反馈到本地存储
				let feedbacks = uni.getStorageSync('user_feedbacks');
				if (!feedbacks) {
					feedbacks = [];
				} else {
					feedbacks = JSON.parse(feedbacks);
				}
				
				feedbacks.push(feedback);
				uni.setStorageSync('user_feedbacks', JSON.stringify(feedbacks));
			},
			// 联系客服
			contactService() {
				uni.showModal({
					title: '联系客服',
					content: '客服热线：************\n工作时间：周一至周日 9:00-18:00',
					confirmText: '拨打电话',
					success: (res) => {
						if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber: '4001234567',
								fail: () => {
									uni.showToast({
										title: '拨打失败，请手动拨打************',
										icon: 'none'
									});
								}
							});
						}
					}
				});
			},
			// 导航到常见问题列表
			navigateToFaqList() {
				uni.showToast({
					title: '完整问题列表功能开发中',
					icon: 'none'
				});
			},
			// 页面导航
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	.service-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
		margin-right: 60rpx;
	}
	
	.service-header {
		background-color: #ffffff;
	}
	
	.banner-image {
		width: 100%;
		height: 240rpx;
	}
	
	.service-info {
		display: flex;
		align-items: center;
		padding: 30rpx;
		position: relative;
		top: -40rpx;
	}
	
	.avatar-container {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		border: 4rpx solid #ffffff;
		overflow: hidden;
		background-color: #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}
	
	.avatar {
		width: 100%;
		height: 100%;
	}
	
	.info-content {
		flex: 1;
		margin-left: 20rpx;
	}
	
	.name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
		display: block;
	}
	
	.status {
		font-size: 26rpx;
		color: #31c27c;
		display: block;
	}
	
	.contact-btn {
		display: flex;
		align-items: center;
		padding: 12rpx 24rpx;
		background-color: #ff5a5f;
		border-radius: 30rpx;
	}
	
	.contact-btn image {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
	}
	
	.contact-btn text {
		font-size: 28rpx;
		color: #ffffff;
	}
	
	.faq-section, .feedback-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
	}
	
	.section-title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.section-title text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.more-btn {
		display: flex;
		align-items: center;
	}
	
	.more-btn text {
		font-size: 26rpx;
		color: #999999;
		font-weight: normal;
	}
	
	.more-btn image {
		width: 24rpx;
		height: 24rpx;
		margin-left: 10rpx;
	}
	
	.faq-list {
		
	}
	
	.faq-item {
		border-bottom: 1rpx solid #f5f5f5;
		padding-bottom: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.faq-item:last-child {
		border-bottom: none;
		padding-bottom: 0;
		margin-bottom: 0;
	}
	
	.faq-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10rpx 0;
	}
	
	.faq-title {
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
	}
	
	.arrow-icon {
		width: 30rpx;
		height: 30rpx;
	}
	
	.faq-content {
		margin-top: 16rpx;
		padding: 20rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
	}
	
	.faq-content text {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.6;
	}
	
	.feedback-content {
		
	}
	
	.input-group {
		margin-bottom: 30rpx;
	}
	
	.label {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 16rpx;
		display: block;
	}
	
	.picker-container {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 80rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 0 20rpx;
	}
	
	.picker-value {
		font-size: 28rpx;
		color: #333333;
	}
	
	.picker-icon {
		width: 24rpx;
		height: 24rpx;
	}
	
	.feedback-textarea {
		width: 100%;
		height: 200rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		color: #333333;
		box-sizing: border-box;
	}
	
	.word-count {
		text-align: right;
		margin-top: 10rpx;
	}
	
	.word-count text {
		font-size: 24rpx;
		color: #999999;
	}
	
	.photo-list {
		display: flex;
		flex-wrap: wrap;
		margin-top: 10rpx;
	}
	
	.photo-item, .upload-btn {
		width: 158rpx;
		height: 158rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		border-radius: 8rpx;
		overflow: hidden;
	}
	
	.photo-item {
		position: relative;
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
	}
	
	.delete-btn {
		position: absolute;
		top: 0;
		right: 0;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.delete-btn image {
		width: 24rpx;
		height: 24rpx;
	}
	
	.upload-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #f9f9f9;
		border: 1rpx dashed #ddd;
	}
	
	.upload-btn image {
		width: 40rpx;
		height: 40rpx;
		margin-bottom: 10rpx;
	}
	
	.upload-btn text {
		font-size: 24rpx;
		color: #999999;
	}
	
	.photo-tip {
		font-size: 24rpx;
		color: #999999;
		margin-top: 10rpx;
		display: block;
	}
	
	.contact-input {
		height: 80rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333333;
	}
	
	.submit-btn {
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 45rpx;
		background-color: #ff5a5f;
		color: #ffffff;
		font-size: 32rpx;
		font-weight: bold;
		margin-top: 40rpx;
	}
	
	.quick-links {
		display: flex;
		justify-content: space-between;
		padding: 30rpx;
		margin-top: 20rpx;
		background-color: #ffffff;
	}
	
	.quick-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 25%;
	}
	
	.quick-item image {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 10rpx;
	}
	
	.quick-item text {
		font-size: 24rpx;
		color: #666666;
	}
</style> 