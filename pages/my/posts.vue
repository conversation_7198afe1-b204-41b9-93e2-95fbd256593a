<template>
	<view class="posts-container">
		<!-- 标题栏 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">我的发布</text>
			<view class="placeholder"></view>
		</view>
		
		<!-- 分类选项卡 -->
		<view class="tabs">
			<view class="tab-item" 
				v-for="(tab, index) in tabs" 
				:key="index" 
				:class="{'active': currentTab === index}"
				@tap="switchTab(index)">
				<text>{{tab.name}}</text>
				<view class="tab-line" v-if="currentTab === index"></view>
			</view>
		</view>
		
		<!-- 内容区域 -->
		<swiper class="content-swiper" :current="currentTab" @change="onSwiperChange">
			<!-- 驿站转让 -->
			<swiper-item>
				<scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore('transfer')">
					<view class="empty-view" v-if="transferList.length === 0">
						<image src="/static/images/empty.png" mode="aspectFit"></image>
						<text>暂无驿站转让信息~</text>
						<view class="add-btn" @tap="navigateTo('/pages/publish/station')">
							<text>去发布</text>
						</view>
					</view>
					
					<view class="post-list" v-else>
						<view class="post-item" v-for="(item, index) in transferList" :key="index" @tap="viewDetail(item, 'transfer')">
							<image class="post-image" :src="item.images[0]" mode="aspectFill"></image>
							<view class="post-info">
								<text class="post-title">{{item.title}}</text>
								<view class="post-tags">
									<text class="tag" v-if="item.stationType">{{item.stationType}}</text>
								</view>
								<view class="post-address">
									<image src="/static/icons/location.png" mode="aspectFit"></image>
									<text>{{item.address}}</text>
								</view>
								<view class="post-price">¥<text class="price-value">{{item.price.replace('万元', '')}}</text><text class="price-unit">万元</text></view>
							</view>
							<view class="post-actions">
								<view class="action-btn edit" @tap.stop="editPost(item, 'transfer')">
									<image src="/static/icons/edit.png" mode="aspectFit"></image>
									<text>编辑</text>
								</view>
								<view class="action-btn delete" @tap.stop="deletePost(item, 'transfer')">
									<image src="/static/icons/delete.png" mode="aspectFit"></image>
									<text>删除</text>
								</view>
							</view>
						</view>
						
						<view class="load-more" v-if="transferHasMore">
							<text>加载更多...</text>
						</view>
						<view class="no-more" v-else>
							<text>已经到底了~</text>
						</view>
					</view>
				</scroll-view>
			</swiper-item>
			
			<!-- 顶班服务 -->
			<swiper-item>
				<scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore('service')">
					<view class="empty-view" v-if="serviceList.length === 0">
						<image src="/static/images/empty.png" mode="aspectFit"></image>
						<text>暂无顶班服务信息~</text>
						<view class="add-btn" @tap="navigateTo('/pages/publish/service')">
							<text>去发布</text>
						</view>
					</view>
					
					<view class="post-list" v-else>
						<view class="post-item" v-for="(item, index) in serviceList" :key="index" @tap="viewDetail(item, 'service')">
							<view class="service-info">
								<text class="post-title">{{item.title}}</text>
								<view class="service-time">
									<image src="/static/icons/time.png" mode="aspectFit"></image>
									<text>{{item.time}}</text>
								</view>
								<view class="post-address">
									<image src="/static/icons/location.png" mode="aspectFit"></image>
									<text>{{item.address}}</text>
								</view>
								<view class="post-price">¥<text class="price-value">{{item.price.replace('元/天', '')}}</text><text class="price-unit">元/天</text></view>
							</view>
							<view class="post-actions">
								<view class="action-btn edit" @tap.stop="editPost(item, 'service')">
									<image src="/static/icons/edit.png" mode="aspectFit"></image>
									<text>编辑</text>
								</view>
								<view class="action-btn delete" @tap.stop="deletePost(item, 'service')">
									<image src="/static/icons/delete.png" mode="aspectFit"></image>
									<text>删除</text>
								</view>
							</view>
						</view>
						
						<view class="load-more" v-if="serviceHasMore">
							<text>加载更多...</text>
						</view>
						<view class="no-more" v-else>
							<text>已经到底了~</text>
						</view>
					</view>
				</scroll-view>
			</swiper-item>
			
			<!-- 招聘信息 -->
			<swiper-item>
				<scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore('job')">
					<view class="empty-view" v-if="jobList.length === 0">
						<image src="/static/images/empty.png" mode="aspectFit"></image>
						<text>暂无招聘信息~</text>
						<view class="add-btn" @tap="navigateTo('/pages/publish/job')">
							<text>去发布</text>
						</view>
					</view>
					
					<view class="post-list" v-else>
						<view class="post-item" v-for="(item, index) in jobList" :key="index" @tap="viewDetail(item, 'job')">
							<view class="job-info">
								<view class="job-header">
									<text class="post-title">{{item.title}}</text>
									<text class="job-salary">{{item.salary}}</text>
								</view>
								<text class="job-company">{{item.company}}</text>
								<view class="post-address">
									<image src="/static/icons/location.png" mode="aspectFit"></image>
									<text>{{item.address}}</text>
								</view>
								<view class="post-tags">
									<text class="tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
								</view>
							</view>
							<view class="post-actions">
								<view class="action-btn edit" @tap.stop="editPost(item, 'job')">
									<image src="/static/icons/edit.png" mode="aspectFit"></image>
									<text>编辑</text>
								</view>
								<view class="action-btn delete" @tap.stop="deletePost(item, 'job')">
									<image src="/static/icons/delete.png" mode="aspectFit"></image>
									<text>删除</text>
								</view>
							</view>
						</view>
						
						<view class="load-more" v-if="jobHasMore">
							<text>加载更多...</text>
						</view>
						<view class="no-more" v-else>
							<text>已经到底了~</text>
						</view>
					</view>
				</scroll-view>
			</swiper-item>
			
			<!-- 设备出售 -->
			<swiper-item>
				<scroll-view scroll-y class="scroll-view" @scrolltolower="loadMore('device')">
					<view class="empty-view" v-if="deviceList.length === 0">
						<image src="/static/images/empty.png" mode="aspectFit"></image>
						<text>暂无设备出售信息~</text>
						<view class="add-btn" @tap="navigateTo('/pages/publish/device')">
							<text>去发布</text>
						</view>
					</view>
					
					<view class="post-list" v-else>
						<view class="post-item" v-for="(item, index) in deviceList" :key="index" @tap="viewDetail(item, 'device')">
							<image class="post-image" :src="item.images[0]" mode="aspectFill"></image>
							<view class="post-info">
								<text class="post-title">{{item.title}}</text>
								<view class="device-condition">
									<text>{{item.condition}}</text>
								</view>
								<view class="post-address">
									<image src="/static/icons/location.png" mode="aspectFit"></image>
									<text>{{item.address}}</text>
								</view>
								<view class="post-price">¥<text class="price-value">{{item.price.replace('元', '')}}</text><text class="price-unit">元</text></view>
							</view>
							<view class="post-actions">
								<view class="action-btn edit" @tap.stop="editPost(item, 'device')">
									<image src="/static/icons/edit.png" mode="aspectFit"></image>
									<text>编辑</text>
								</view>
								<view class="action-btn delete" @tap.stop="deletePost(item, 'device')">
									<image src="/static/icons/delete.png" mode="aspectFit"></image>
									<text>删除</text>
								</view>
							</view>
						</view>
						
						<view class="load-more" v-if="deviceHasMore">
							<text>加载更多...</text>
						</view>
						<view class="no-more" v-else>
							<text>已经到底了~</text>
						</view>
					</view>
				</scroll-view>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabs: [
					{ name: '驿站转让', type: 'transfer' },
					{ name: '顶班服务', type: 'service' },
					{ name: '招聘信息', type: 'job' },
					{ name: '设备出售', type: 'device' }
				],
				currentTab: 0,
				transferList: [],
				serviceList: [],
				jobList: [],
				deviceList: [],
				transferHasMore: false,
				serviceHasMore: false,
				jobHasMore: false,
				deviceHasMore: false,
				userInfo: null
			}
		},
		onLoad() {
			// 获取用户信息
			const userInfoStorage = uni.getStorageSync('userInfo');
			if (userInfoStorage) {
				this.userInfo = JSON.parse(userInfoStorage);
				// 加载各类型的发布内容
				this.loadTransferList();
				this.loadServiceList();
				this.loadJobList();
				this.loadDeviceList();
			} else {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		methods: {
			// 获取驿站转让列表
			loadTransferList() {
				const list = uni.getStorageSync('transferList') || [];
				// 筛选出当前用户发布的内容
				this.transferList = list.filter(item => {
					return item.contactInfo && item.contactInfo.phone === this.userInfo.phoneNumber;
				});
				this.transferHasMore = false;
			},
			
			// 获取顶班服务列表
			loadServiceList() {
				const list = uni.getStorageSync('serviceList') || [];
				// 筛选出当前用户发布的内容
				this.serviceList = list.filter(item => {
					return item.contactInfo && item.contactInfo.phone === this.userInfo.phoneNumber;
				});
				this.serviceHasMore = false;
			},
			
			// 获取招聘信息列表
			loadJobList() {
				const list = uni.getStorageSync('jobList') || [];
				// 筛选出当前用户发布的内容
				this.jobList = list.filter(item => {
					return item.contactInfo && item.contactInfo.phone === this.userInfo.phoneNumber;
				});
				this.jobHasMore = false;
			},
			
			// 获取设备出售列表
			loadDeviceList() {
				const list = uni.getStorageSync('deviceList') || [];
				// 筛选出当前用户发布的内容
				this.deviceList = list.filter(item => {
					return item.contactInfo && item.contactInfo.phone === this.userInfo.phoneNumber;
				});
				this.deviceHasMore = false;
			},
			
			// 切换选项卡
			switchTab(index) {
				this.currentTab = index;
			},
			
			// 滑动切换选项卡
			onSwiperChange(e) {
				this.currentTab = e.detail.current;
			},
			
			// 加载更多
			loadMore(type) {
				// 实际项目中这里应该是分页加载
				// 这里只是模拟
				uni.showToast({
					title: '没有更多数据了',
					icon: 'none'
				});
			},
			
			// 查看详情
			viewDetail(item, type) {
				let url = '/pages/detail/detail?id=' + item.id + '&type=' + type;
				uni.navigateTo({
					url: url
				});
			},
			
			// 编辑帖子
			editPost(item, type) {
				let url = '';
				switch(type) {
					case 'transfer':
						url = '/pages/publish/station?id=' + item.id;
						break;
					case 'service':
						url = '/pages/publish/service?id=' + item.id;
						break;
					case 'job':
						url = '/pages/publish/job?id=' + item.id;
						break;
					case 'device':
						url = '/pages/publish/device?id=' + item.id;
						break;
				}
				
				if (url) {
					uni.navigateTo({
						url: url
					});
				}
			},
			
			// 删除帖子
			deletePost(item, type) {
				uni.showModal({
					title: '提示',
					content: '确定要删除该发布吗？',
					success: (res) => {
						if (res.confirm) {
							// 从本地存储中删除
							let storageKey = type + 'List';
							let list = uni.getStorageSync(storageKey) || [];
							list = list.filter(post => post.id !== item.id);
							uni.setStorageSync(storageKey, list);
							
							// 更新当前显示的列表
							switch(type) {
								case 'transfer':
									this.transferList = this.transferList.filter(post => post.id !== item.id);
									break;
								case 'service':
									this.serviceList = this.serviceList.filter(post => post.id !== item.id);
									break;
								case 'job':
									this.jobList = this.jobList.filter(post => post.id !== item.id);
									break;
								case 'device':
									this.deviceList = this.deviceList.filter(post => post.id !== item.id);
									break;
							}
							
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},
			
			// 页面导航
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	.posts-container {
		display: flex;
		flex-direction: column;
		height: 100vh;
		background-color: #f5f5f5;
	}
	
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.placeholder {
		width: 60rpx;
	}
	
	.tabs {
		display: flex;
		background-color: #ffffff;
		border-bottom: 1rpx solid #eeeeee;
	}
	
	.tab-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 20rpx 0;
		position: relative;
	}
	
	.tab-item text {
		font-size: 28rpx;
		color: #666666;
	}
	
	.tab-item.active text {
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.tab-line {
		position: absolute;
		bottom: 0;
		width: 60rpx;
		height: 6rpx;
		background-color: #ff5a5f;
		border-radius: 3rpx;
	}
	
	.content-swiper {
		flex: 1;
	}
	
	.scroll-view {
		height: 100%;
	}
	
	.empty-view {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 200rpx;
	}
	
	.empty-view image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}
	
	.empty-view text {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 30rpx;
	}
	
	.add-btn {
		padding: 15rpx 40rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
	}
	
	.add-btn text {
		font-size: 28rpx;
		color: #ffffff;
		margin-bottom: 0;
	}
	
	.post-list {
		padding: 20rpx;
	}
	
	.post-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.post-image {
		width: 100%;
		height: 300rpx;
		border-radius: 8rpx;
		margin-bottom: 15rpx;
		background-color: #f9f9f9;
	}
	
	.post-info {
		margin-bottom: 15rpx;
	}
	
	.post-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
		display: block;
	}
	
	.post-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 10rpx;
	}
	
	.tag {
		font-size: 22rpx;
		padding: 6rpx 16rpx;
		background-color: #FFF0F0;
		color: #FF5A5F;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
		border-radius: 6rpx;
	}
	
	.post-address {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #666666;
		margin-bottom: 10rpx;
	}
	
	.post-address image {
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
	}
	
	.post-price {
		font-size: 24rpx;
		color: #FF5A5F;
	}
	
	.price-value {
		font-size: 32rpx;
		font-weight: bold;
	}
	
	.price-unit {
		font-size: 24rpx;
		margin-left: 5rpx;
	}
	
	.post-actions {
		display: flex;
		justify-content: flex-end;
		border-top: 1rpx solid #eeeeee;
		padding-top: 15rpx;
		margin-top: 15rpx;
	}
	
	.action-btn {
		display: flex;
		align-items: center;
		padding: 10rpx 20rpx;
		margin-left: 15rpx;
		border-radius: 30rpx;
	}
	
	.action-btn image {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
	}
	
	.action-btn text {
		font-size: 24rpx;
	}
	
	.edit {
		background-color: #f8f8f8;
		color: #666666;
	}
	
	.delete {
		background-color: #FFF0F0;
		color: #FF5A5F;
	}
	
	.service-info, .job-info {
		margin-bottom: 15rpx;
	}
	
	.service-time {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #666666;
		margin-bottom: 10rpx;
	}
	
	.service-time image {
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
	}
	
	.job-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.job-salary {
		font-size: 28rpx;
		color: #FF5A5F;
		font-weight: bold;
	}
	
	.job-company {
		font-size: 26rpx;
		color: #666666;
		margin-bottom: 10rpx;
		display: block;
	}
	
	.device-condition {
		font-size: 24rpx;
		color: #666666;
		margin-bottom: 10rpx;
	}
	
	.load-more, .no-more {
		text-align: center;
		padding: 30rpx 0;
	}
	
	.load-more text, .no-more text {
		font-size: 24rpx;
		color: #999999;
	}
</style> 