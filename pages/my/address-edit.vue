<template>
	<view class="address-edit-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">{{isEdit ? '编辑地址' : '新增地址'}}</text>
		</view>
		
		<!-- 表单 -->
		<view class="form-container">
			<view class="form-item">
				<text class="form-label">收货人</text>
				<input type="text" v-model="addressForm.name" placeholder="请输入收货人姓名" class="form-input" />
			</view>
			
			<view class="form-item">
				<text class="form-label">手机号码</text>
				<input type="number" v-model="addressForm.phone" placeholder="请输入手机号码" class="form-input" maxlength="11" />
			</view>
			
			<view class="form-item">
				<text class="form-label">所在地区</text>
				<view class="region-picker" @tap="showRegionPicker">
					<text class="region-text" v-if="addressForm.province">{{addressForm.province}}{{addressForm.city}}{{addressForm.district}}</text>
					<text class="placeholder" v-else>请选择所在地区</text>
					<image src="/static/icons/arrow-right.png" mode="aspectFit" class="arrow-icon"></image>
				</view>
			</view>
			
			<view class="form-item">
				<text class="form-label">详细地址</text>
				<textarea v-model="addressForm.address" placeholder="请输入详细地址，如街道、门牌号、小区、楼栋号、单元室等" class="form-textarea"></textarea>
			</view>
			
			<view class="form-item switch-item">
				<text class="form-label">设为默认地址</text>
				<switch :checked="addressForm.isDefault" @change="onDefaultChange" color="#ff5a5f" />
			</view>
		</view>
		
		<!-- 提交按钮 -->
		<view class="submit-btn" @tap="saveAddress">
			<text>保存</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isEdit: false,
				addressId: null,
				addressForm: {
					id: null,
					name: '',
					phone: '',
					province: '',
					city: '',
					district: '',
					address: '',
					isDefault: false
				}
			}
		},
		onLoad(options) {
			if (options.id) {
				this.isEdit = true;
				this.addressId = parseInt(options.id);
				this.loadAddressData();
			} else {
				// 生成新的ID
				this.addressForm.id = new Date().getTime();
			}
		},
		methods: {
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			
			// 加载地址数据
			loadAddressData() {
				const addressStorage = uni.getStorageSync('address_list');
				if (addressStorage) {
					const addressList = JSON.parse(addressStorage);
					const address = addressList.find(item => item.id === this.addressId);
					if (address) {
						this.addressForm = JSON.parse(JSON.stringify(address));
					}
				}
			},
			
			// 显示地区选择器
			showRegionPicker() {
				// 这里简化实现，实际应该使用地区选择插件
				// 模拟选择地区
				setTimeout(() => {
					this.addressForm.province = '浙江省';
					this.addressForm.city = '杭州市';
					this.addressForm.district = '西湖区';
					
					uni.showToast({
						title: '已选择地区',
						icon: 'none'
					});
				}, 500);
			},
			
			// 默认地址开关变化
			onDefaultChange(e) {
				this.addressForm.isDefault = e.detail.value;
			},
			
			// 保存地址
			saveAddress() {
				// 表单验证
				if (!this.addressForm.name) {
					uni.showToast({
						title: '请输入收货人姓名',
						icon: 'none'
					});
					return;
				}
				
				if (!this.addressForm.phone) {
					uni.showToast({
						title: '请输入手机号码',
						icon: 'none'
					});
					return;
				}
				
				if (!/^1\d{10}$/.test(this.addressForm.phone)) {
					uni.showToast({
						title: '手机号码格式不正确',
						icon: 'none'
					});
					return;
				}
				
				if (!this.addressForm.province || !this.addressForm.city || !this.addressForm.district) {
					uni.showToast({
						title: '请选择所在地区',
						icon: 'none'
					});
					return;
				}
				
				if (!this.addressForm.address) {
					uni.showToast({
						title: '请输入详细地址',
						icon: 'none'
					});
					return;
				}
				
				// 获取地址列表
				let addressList = [];
				const addressStorage = uni.getStorageSync('address_list');
				if (addressStorage) {
					addressList = JSON.parse(addressStorage);
				}
				
				// 如果设置为默认地址，需要取消其他默认地址
				if (this.addressForm.isDefault) {
					addressList.forEach(item => {
						if (item.id !== this.addressForm.id) {
							item.isDefault = false;
						}
					});
				}
				
				// 新增或更新
				if (this.isEdit) {
					// 更新
					const index = addressList.findIndex(item => item.id === this.addressForm.id);
					if (index > -1) {
						addressList[index] = this.addressForm;
					}
				} else {
					// 新增
					// 如果是第一个地址，默认设为默认地址
					if (addressList.length === 0) {
						this.addressForm.isDefault = true;
					}
					addressList.push(this.addressForm);
				}
				
				// 保存到本地存储
				uni.setStorageSync('address_list', JSON.stringify(addressList));
				
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
				
				// 延迟返回
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		}
	}
</script>

<style>
	.address-edit-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.form-container {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 0 30rpx;
	}
	
	.form-item {
		padding: 30rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.form-label {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.form-input {
		height: 80rpx;
		font-size: 28rpx;
		color: #333333;
	}
	
	.form-textarea {
		width: 100%;
		height: 160rpx;
		font-size: 28rpx;
		color: #333333;
	}
	
	.region-picker {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.region-text {
		font-size: 28rpx;
		color: #333333;
	}
	
	.placeholder {
		font-size: 28rpx;
		color: #999999;
	}
	
	.arrow-icon {
		width: 30rpx;
		height: 30rpx;
	}
	
	.switch-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.switch-item .form-label {
		margin-bottom: 0;
	}
	
	.submit-btn {
		position: fixed;
		bottom: 30rpx;
		left: 30rpx;
		right: 30rpx;
		height: 80rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.submit-btn text {
		font-size: 30rpx;
		color: #ffffff;
		font-weight: bold;
	}
</style> 