<template>
	<view class="promotion-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">推广赚钱</text>
			<view class="right-btn" @tap="showRules">
				<image src="/static/icons/question.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 收益概览 -->
		<view class="earnings-overview">
			<view class="total-earnings">
				<text class="earnings-label">累计收益(元)</text>
				<text class="earnings-value">{{totalEarnings.toFixed(2)}}</text>
				<view class="action-buttons">
					<view class="action-btn" @tap="withdraw">
						<text>提现</text>
					</view>
					<view class="action-btn" @tap="viewEarningsDetail">
						<text>收益明细</text>
					</view>
				</view>
			</view>
			
			<view class="earnings-stats">
				<view class="stat-item">
					<text class="stat-value">{{todayEarnings.toFixed(2)}}</text>
					<text class="stat-label">今日收益</text>
				</view>
				<view class="stat-item">
					<text class="stat-value">{{pendingEarnings.toFixed(2)}}</text>
					<text class="stat-label">待结算</text>
				</view>
				<view class="stat-item">
					<text class="stat-value">{{withdrawableAmount.toFixed(2)}}</text>
					<text class="stat-label">可提现</text>
				</view>
			</view>
		</view>
		
		<!-- 推广数据 -->
		<view class="promotion-stats">
			<view class="stat-card">
				<view class="card-top">
					<text class="card-title">已邀请好友</text>
					<text class="card-value">{{invitedCount}}人</text>
				</view>
				<view class="progress-bar">
					<view 
						class="progress-inner" 
						:style="{width: (invitedCount / inviteGoal * 100) + '%'}"
					></view>
				</view>
				<view class="card-bottom">
					<text class="goal-text">目标: {{inviteGoal}}人</text>
					<text class="reward-text">奖励: ¥{{inviteReward.toFixed(2)}}</text>
				</view>
			</view>
			
			<view class="stat-card">
				<view class="card-top">
					<text class="card-title">本月新增</text>
					<text class="card-value">{{monthlyInvites}}人</text>
				</view>
				<view class="progress-bar">
					<view 
						class="progress-inner" 
						:style="{width: (monthlyInvites / monthlyGoal * 100) + '%'}"
					></view>
				</view>
				<view class="card-bottom">
					<text class="goal-text">目标: {{monthlyGoal}}人</text>
					<text class="reward-text">奖励: ¥{{monthlyReward.toFixed(2)}}</text>
				</view>
			</view>
		</view>
		
		<!-- 我的推广码 -->
		<view class="promo-code-section">
			<view class="section-header">
				<text class="section-title">我的推广码</text>
				<view class="save-btn" @tap="savePromoCode">
					<text>保存图片</text>
				</view>
			</view>
			
			<view class="promo-code-card">
				<view class="card-info">
					<image class="avatar" :src="userInfo.avatar" mode="aspectFill"></image>
					<view class="user-info">
						<text class="username">{{userInfo.username}}</text>
						<text class="code-label">我的邀请码：{{userInfo.inviteCode}}</text>
					</view>
				</view>
				
				<view class="qrcode-container">
					<image src="/static/images/qrcode.png" mode="aspectFit" class="qrcode"></image>
					<text class="scan-tip">扫码或长按识别进入驿站帮Pro小程序</text>
				</view>
				
				<view class="share-btns">
					<view class="share-btn wechat" @tap="shareToWechat">
						<image src="/static/icons/wechat.png" mode="aspectFit"></image>
						<text>微信</text>
					</view>
					<view class="share-btn moments" @tap="shareToMoments">
						<image src="/static/icons/moments.png" mode="aspectFit"></image>
						<text>朋友圈</text>
					</view>
					<view class="share-btn copy" @tap="copyInviteLink">
						<image src="/static/icons/link.png" mode="aspectFit"></image>
						<text>复制链接</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 推广收益规则 -->
		<view class="commission-rules">
			<view class="section-header">
				<text class="section-title">推广规则</text>
			</view>
			
			<view class="rule-list">
				<view class="rule-item">
					<view class="rule-icon">1</view>
					<view class="rule-content">
						<text class="rule-title">简单推广</text>
						<text class="rule-desc">分享您的专属二维码或链接给好友，邀请他们使用驿站帮Pro小程序</text>
					</view>
				</view>
				
				<view class="rule-item">
					<view class="rule-icon">2</view>
					<view class="rule-content">
						<text class="rule-title">新用户奖励</text>
						<text class="rule-desc">好友通过您的推广码首次进入小程序，您将获得5元奖励</text>
					</view>
				</view>
				
				<view class="rule-item">
					<view class="rule-icon">3</view>
					<view class="rule-content">
						<text class="rule-title">累计奖励</text>
						<text class="rule-desc">邀请人数达到目标值，额外获得相应金额奖励</text>
					</view>
				</view>
				
				<view class="rule-item">
					<view class="rule-icon">4</view>
					<view class="rule-content">
						<text class="rule-title">月度奖励</text>
						<text class="rule-desc">每月邀请新用户达到目标，获得额外奖励</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 好友列表 -->
		<view class="friends-section">
			<view class="section-header">
				<text class="section-title">我邀请的好友</text>
				<view class="more-btn" @tap="viewAllFriends">
					<text>查看全部</text>
					<image src="/static/icons/right.png" mode="aspectFit"></image>
				</view>
			</view>
			
			<view v-if="invitedFriends.length === 0" class="empty-tip">
				<image src="/static/images/empty.png" mode="aspectFit"></image>
				<text>暂无邀请记录</text>
			</view>
			
			<view v-else class="friends-list">
				<view 
					v-for="(friend, index) in invitedFriends.slice(0, 5)" 
					:key="friend.id"
					class="friend-item"
				>
					<image :src="friend.avatar" mode="aspectFill" class="friend-avatar"></image>
					<view class="friend-info">
						<text class="friend-name">{{friend.username}}</text>
						<text class="join-time">注册时间：{{formatTime(friend.joinTime)}}</text>
					</view>
					<view class="friend-status">
						<text class="status" :class="friend.active ? 'active' : 'inactive'">{{friend.active ? '活跃' : '未活跃'}}</text>
						<text class="reward">+{{friend.reward.toFixed(2)}}元</text>
					</view>
				</view>
			</view>
			
			<!-- 历史邀请统计 -->
			<view class="invite-stats">
				<view class="stats-item">
					<text class="stats-label">活跃率</text>
					<text class="stats-value">{{activeRate}}%</text>
				</view>
				<view class="stats-item">
					<text class="stats-label">7日新增</text>
					<text class="stats-value">{{weeklyInvites}}人</text>
				</view>
				<view class="stats-item">
					<text class="stats-label">30日新增</text>
					<text class="stats-value">{{monthlyInvites}}人</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userInfo: {
					username: '测试用户',
					avatar: '/static/images/default-avatar.png',
					inviteCode: 'YZB888'
				},
				totalEarnings: 0,
				todayEarnings: 0,
				pendingEarnings: 0,
				withdrawableAmount: 0,
				invitedCount: 0,
				inviteGoal: 20,
				inviteReward: 100,
				monthlyInvites: 0,
				monthlyGoal: 10,
				monthlyReward: 50,
				weeklyInvites: 0,
				activeRate: 0,
				invitedFriends: []
			}
		},
		onLoad() {
			this.loadUserInfo();
			this.loadPromotionData();
			this.loadInvitedFriends();
		},
		methods: {
			// 加载用户信息
			loadUserInfo() {
				const userInfoStorage = uni.getStorageSync('userInfo');
				if (userInfoStorage) {
					const userInfo = JSON.parse(userInfoStorage);
					this.userInfo.username = userInfo.username;
					this.userInfo.avatar = userInfo.avatar;
					
					if (!userInfo.inviteCode) {
						// 生成随机邀请码
						const inviteCode = 'YZB' + Math.floor(1000 + Math.random() * 9000);
						userInfo.inviteCode = inviteCode;
						this.userInfo.inviteCode = inviteCode;
						uni.setStorageSync('userInfo', JSON.stringify(userInfo));
					} else {
						this.userInfo.inviteCode = userInfo.inviteCode;
					}
				}
			},
			// 加载推广数据
			loadPromotionData() {
				const promotionStorage = uni.getStorageSync('promotion_data');
				if (promotionStorage) {
					const data = JSON.parse(promotionStorage);
					this.totalEarnings = data.totalEarnings;
					this.todayEarnings = data.todayEarnings;
					this.pendingEarnings = data.pendingEarnings;
					this.withdrawableAmount = data.withdrawableAmount;
					this.invitedCount = data.invitedCount;
					this.monthlyInvites = data.monthlyInvites;
					this.weeklyInvites = data.weeklyInvites;
					this.activeRate = data.activeRate;
				} else {
					// 测试数据
					this.totalEarnings = 658.88;
					this.todayEarnings = 25.50;
					this.pendingEarnings = 120.00;
					this.withdrawableAmount = 538.88;
					this.invitedCount = 12;
					this.monthlyInvites = 5;
					this.weeklyInvites = 2;
					this.activeRate = 75;
					
					// 保存到本地存储
					const promotionData = {
						totalEarnings: this.totalEarnings,
						todayEarnings: this.todayEarnings,
						pendingEarnings: this.pendingEarnings,
						withdrawableAmount: this.withdrawableAmount,
						invitedCount: this.invitedCount,
						monthlyInvites: this.monthlyInvites,
						weeklyInvites: this.weeklyInvites,
						activeRate: this.activeRate
					};
					
					uni.setStorageSync('promotion_data', JSON.stringify(promotionData));
				}
			},
			// 加载邀请的好友
			loadInvitedFriends() {
				const friendsStorage = uni.getStorageSync('invited_friends');
				if (friendsStorage) {
					this.invitedFriends = JSON.parse(friendsStorage);
				} else {
					// 测试数据
					this.invitedFriends = [
						{
							id: 'user_001',
							username: '张三',
							avatar: '/static/images/avatar1.png',
							joinTime: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3天前
							reward: 5.00,
							active: true
						},
						{
							id: 'user_002',
							username: '李四',
							avatar: '/static/images/avatar2.png',
							joinTime: Date.now() - 7 * 24 * 60 * 60 * 1000, // 7天前
							reward: 5.00,
							active: true
						},
						{
							id: 'user_003',
							username: '王五',
							avatar: '/static/images/avatar3.png',
							joinTime: Date.now() - 15 * 24 * 60 * 60 * 1000, // 15天前
							reward: 5.00,
							active: false
						},
						{
							id: 'user_004',
							username: '赵六',
							avatar: '/static/images/avatar4.png',
							joinTime: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30天前
							reward: 5.00,
							active: true
						},
						{
							id: 'user_005',
							username: '孙七',
							avatar: '/static/images/avatar5.png',
							joinTime: Date.now() - 1 * 24 * 60 * 60 * 1000, // 1天前
							reward: 5.00,
							active: true
						},
						{
							id: 'user_006',
							username: '周八',
							avatar: '/static/images/avatar6.png',
							joinTime: Date.now() - 2 * 24 * 60 * 60 * 1000, // 2天前
							reward: 5.00,
							active: false
						}
					];
					
					// 保存到本地存储
					uni.setStorageSync('invited_friends', JSON.stringify(this.invitedFriends));
				}
			},
			// 格式化时间
			formatTime(timestamp) {
				const date = new Date(timestamp);
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				
				return `${year}-${month}-${day}`;
			},
			// 提现
			withdraw() {
				if (this.withdrawableAmount < 100) {
					uni.showModal({
						title: '提示',
						content: '提现金额不足100元，暂时无法提现',
						showCancel: false
					});
					return;
				}
				
				uni.navigateTo({
					url: '/pages/my/withdraw'
				});
			},
			// 查看收益明细
			viewEarningsDetail() {
				uni.navigateTo({
					url: '/pages/my/earnings-detail'
				});
			},
			// 保存推广码
			savePromoCode() {
				uni.showLoading({
					title: '保存中...'
				});
				
				setTimeout(() => {
					uni.hideLoading();
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
				}, 1500);
			},
			// 分享到微信
			shareToWechat() {
				uni.showToast({
					title: '分享功能开发中',
					icon: 'none'
				});
			},
			// 分享到朋友圈
			shareToMoments() {
				uni.showToast({
					title: '分享功能开发中',
					icon: 'none'
				});
			},
			// 复制邀请链接
			copyInviteLink() {
				const inviteLink = `https://yizhanbang.com/invite?code=${this.userInfo.inviteCode}`;
				
				uni.setClipboardData({
					data: inviteLink,
					success: () => {
						uni.showToast({
							title: '链接已复制',
							icon: 'success'
						});
					}
				});
			},
			// 查看所有好友
			viewAllFriends() {
				uni.navigateTo({
					url: '/pages/my/invited-friends'
				});
			},
			// 查看推广规则
			showRules() {
				uni.showModal({
					title: '推广规则',
					content: '1. 邀请好友扫码进入小程序，您将获得5元奖励\n2. 无需好友消费，仅通过二维码推广即可获得奖励\n3. 当月邀请10人及以上，额外奖励50元\n4. 累计邀请20人及以上，额外奖励100元\n5. 佣金将在好友进入小程序24小时后自动结算到您的账户',
					showCancel: false
				});
			},
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	.promotion-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn, .right-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image, .right-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.earnings-overview {
		background-color: #ff5a5f;
		padding: 40rpx 30rpx;
		color: #ffffff;
	}
	
	.total-earnings {
		text-align: center;
		margin-bottom: 40rpx;
	}
	
	.earnings-label {
		font-size: 28rpx;
		opacity: 0.9;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.earnings-value {
		font-size: 60rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
		display: block;
	}
	
	.action-buttons {
		display: flex;
		justify-content: center;
	}
	
	.action-btn {
		width: 160rpx;
		height: 60rpx;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 20rpx;
	}
	
	.action-btn text {
		font-size: 28rpx;
	}
	
	.earnings-stats {
		display: flex;
		justify-content: space-around;
	}
	
	.stat-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.stat-value {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}
	
	.stat-label {
		font-size: 24rpx;
		opacity: 0.9;
	}
	
	.promotion-stats {
		padding: 30rpx;
		display: flex;
		justify-content: space-between;
	}
	
	.stat-card {
		width: 48%;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.card-top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.card-title {
		font-size: 28rpx;
		color: #666666;
	}
	
	.card-value {
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
	}
	
	.progress-bar {
		height: 10rpx;
		background-color: #f5f5f5;
		border-radius: 5rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
	}
	
	.progress-inner {
		height: 100%;
		background-color: #ff5a5f;
		border-radius: 5rpx;
		transition: width 0.3s;
	}
	
	.card-bottom {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.goal-text, .reward-text {
		font-size: 24rpx;
		color: #999999;
	}
	
	.promo-code-section, .commission-rules, .friends-section {
		margin: 20rpx 30rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.save-btn, .more-btn {
		display: flex;
		align-items: center;
	}
	
	.save-btn text, .more-btn text {
		font-size: 26rpx;
		color: #666666;
	}
	
	.more-btn image {
		width: 24rpx;
		height: 24rpx;
		margin-left: 6rpx;
	}
	
	.promo-code-card {
		background-color: #f9f9f9;
		border-radius: 12rpx;
		padding: 30rpx;
	}
	
	.card-info {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		margin-right: 20rpx;
	}
	
	.user-info {
		flex: 1;
	}
	
	.username {
		font-size: 30rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
	}
	
	.code-label {
		font-size: 26rpx;
		color: #666666;
	}
	
	.qrcode-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 30rpx;
	}
	
	.qrcode {
		width: 300rpx;
		height: 300rpx;
		margin-bottom: 20rpx;
	}
	
	.scan-tip {
		font-size: 26rpx;
		color: #666666;
	}
	
	.share-btns {
		display: flex;
		justify-content: space-around;
	}
	
	.share-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.share-btn image {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 10rpx;
	}
	
	.share-btn text {
		font-size: 24rpx;
		color: #666666;
	}
	
	.rule-list {
		
	}
	
	.rule-item {
		display: flex;
		margin-bottom: 30rpx;
	}
	
	.rule-item:last-child {
		margin-bottom: 0;
	}
	
	.rule-icon {
		width: 50rpx;
		height: 50rpx;
		background-color: #ff5a5f;
		color: #ffffff;
		border-radius: 25rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		font-weight: bold;
		margin-right: 20rpx;
	}
	
	.rule-content {
		flex: 1;
	}
	
	.rule-title {
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
	}
	
	.rule-desc {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.4;
	}
	
	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40rpx 0;
	}
	
	.empty-tip image {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 20rpx;
	}
	
	.empty-tip text {
		font-size: 28rpx;
		color: #999999;
	}
	
	.friends-list {
		
	}
	
	.friend-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.friend-item:last-child {
		border-bottom: none;
	}
	
	.friend-avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		margin-right: 20rpx;
	}
	
	.friend-info {
		flex: 1;
	}
	
	.friend-name {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 10rpx;
		display: block;
	}
	
	.join-time {
		font-size: 24rpx;
		color: #999999;
	}
	
	.friend-status {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
	}
	
	.status {
		font-size: 24rpx;
		padding: 6rpx 12rpx;
		border-radius: 20rpx;
		margin-bottom: 10rpx;
	}
	
	.status.active {
		background-color: rgba(46, 204, 113, 0.1);
		color: #2ecc71;
	}
	
	.status.inactive {
		background-color: rgba(189, 195, 199, 0.1);
		color: #bdc3c7;
	}
	
	.reward {
		font-size: 28rpx;
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.invite-stats {
		display: flex;
		margin-top: 30rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.stats-item {
		flex: 1;
		text-align: center;
		padding: 30rpx 0;
		border-right: 1px solid #f0f0f0;
	}
	
	.stats-item:last-child {
		border-right: none;
	}
	
	.stats-label {
		font-size: 24rpx;
		color: #999999;
		display: block;
		margin-bottom: 10rpx;
	}
	
	.stats-value {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}
</style> 