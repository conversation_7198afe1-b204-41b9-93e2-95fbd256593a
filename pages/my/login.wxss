/* pages/my/login.wxss */
.login-container {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 60rpx;
  margin-bottom: 60rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.form-container {
  margin-bottom: 60rpx;
}

.tabs {
  display: flex;
  border-bottom: 1rpx solid #eeeeee;
  margin-bottom: 30rpx;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 30rpx;
  color: #666666;
  position: relative;
}

.tab.active {
  color: #ff5a5f;
  font-weight: bold;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #ff5a5f;
}

.input-group {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.input-box {
  display: flex;
  align-items: center;
  height: 90rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.prefix {
  font-size: 28rpx;
  color: #333333;
  padding-right: 20rpx;
}

input {
  flex: 1;
  height: 90rpx;
  font-size: 28rpx;
}

.verify-btn {
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 30rpx;
  background-color: #ff5a5f;
  color: #ffffff;
  font-size: 26rpx;
  text-align: center;
}

.verify-btn.disabled {
  background-color: #cccccc;
}

.eye-icon {
  padding: 0 20rpx;
}

.eye-icon image {
  width: 40rpx;
  height: 40rpx;
}

.login-btn {
  height: 90rpx;
  line-height: 90rpx;
  background-color: #ff5a5f;
  color: #ffffff;
  border-radius: 45rpx;
  text-align: center;
  font-size: 32rpx;
  margin-top: 50rpx;
  margin-bottom: 30rpx;
}

.login-btn.disabled {
  background-color: #cccccc;
}

.options {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.option-link {
  color: #ff5a5f;
  font-size: 28rpx;
}

.agreement {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999999;
  margin-top: 30rpx;
}

.agreement checkbox {
  transform: scale(0.7);
  margin-right: 6rpx;
}

.link {
  color: #ff5a5f;
} 