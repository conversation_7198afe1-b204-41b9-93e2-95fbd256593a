<!-- pages/my/login.wxml -->
<view class="login-container">
  <view class="header">
    <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
    <text class="title">欢迎使用驿站帮</text>
  </view>
  
  <!-- 登录表单 -->
  <view class="form-container">
    <view class="tabs">
      <view class="tab {{showVerifyLogin ? 'active' : ''}}" bindtap="switchLoginType">
        <text>验证码登录</text>
      </view>
      <view class="tab {{!showVerifyLogin ? 'active' : ''}}" bindtap="switchLoginType">
        <text>密码登录</text>
      </view>
    </view>
    
    <!-- 手机号输入 -->
    <view class="input-group">
      <text class="label">手机号码</text>
      <view class="input-box">
        <text class="prefix">+86</text>
        <input type="number" value="{{phoneNumber}}" bindinput="inputPhoneNumber" placeholder="请输入手机号码" maxlength="11" />
      </view>
    </view>
    
    <!-- 验证码登录 -->
    <block wx:if="{{showVerifyLogin}}">
      <view class="input-group">
        <text class="label">验证码</text>
        <view class="input-box">
          <input type="number" value="{{code}}" bindinput="inputCode" placeholder="请输入验证码" maxlength="6" />
          <view class="verify-btn {{!isValidPhone() || countdown > 0 ? 'disabled' : ''}}" bindtap="getVerificationCode">
            <text>{{codeText}}</text>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 密码登录 -->
    <block wx:else>
      <view class="input-group">
        <text class="label">密码</text>
        <view class="input-box">
          <input type="{{showPassword ? 'text' : 'password'}}" value="{{password}}" bindinput="inputPassword" placeholder="请输入密码" />
          <view class="eye-icon" bindtap="togglePasswordVisibility">
            <image src="{{showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'}}" mode="aspectFit"></image>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 操作按钮 -->
    <view class="login-btn {{loading ? 'disabled' : ''}}" bindtap="login">
      <text>登录</text>
    </view>
    
    <!-- 其他选项 -->
    <view class="options">
      <text class="option-link" bindtap="navigateToResetPassword">忘记密码</text>
      <text class="option-link" bindtap="navigateToRegister">注册账号</text>
    </view>
    
    <!-- 用户协议 -->
    <view class="agreement">
      <checkbox checked="{{agreeProtocol}}" bindtap="toggleAgreement" />
      <text>登录即表示同意</text>
      <text class="link" bindtap="navigateToAgreement">《用户协议》</text>
      <text>和</text>
      <text class="link" bindtap="navigateToPrivacy">《隐私政策》</text>
    </view>
  </view>
</view> 