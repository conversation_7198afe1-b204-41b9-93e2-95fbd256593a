<template>
	<view class="verify-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">实名认证</text>
			<view class="right-btn" @tap="showHelp">
				<image src="/static/icons/help.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 状态提示 -->
		<view class="status-section" v-if="verificationStatus">
			<view class="status-icon">
				<image :src="statusIcon[verificationStatus]" mode="aspectFit"></image>
			</view>
			<text class="status-text">{{statusText[verificationStatus]}}</text>
			<text class="status-desc">{{statusDesc[verificationStatus]}}</text>
		</view>
		
		<!-- 表单区域 -->
		<view class="form-section" v-if="verificationStatus !== 'approved' && verificationStatus !== 'processing'">
			<view class="form-title">请填写真实身份信息</view>
			
			<!-- 姓名 -->
			<view class="form-item">
				<text class="item-label">真实姓名</text>
				<input 
					class="item-input" 
					type="text" 
					placeholder="请输入您的真实姓名" 
					v-model="formData.realName"
					:disabled="isDisabled"
				/>
			</view>
			
			<!-- 身份证号 -->
			<view class="form-item">
				<text class="item-label">身份证号码</text>
				<input 
					class="item-input" 
					type="idcard" 
					placeholder="请输入您的身份证号码" 
					v-model="formData.idNumber"
					:disabled="isDisabled"
				/>
			</view>
			
			<!-- 手机号 -->
			<view class="form-item">
				<text class="item-label">手机号码</text>
				<view class="phone-input-wrapper">
					<input 
						class="item-input" 
						type="number" 
						placeholder="请输入您的手机号码" 
						v-model="formData.phoneNumber"
						:disabled="isDisabled || isPhoneVerified"
					/>
					<view 
						v-if="!isPhoneVerified" 
						class="verify-btn" 
						:class="{'disabled': !isPhoneValid || isCounting}" 
						@tap="sendCode"
					>
						<text>{{codeText}}</text>
					</view>
				</view>
			</view>
			
			<!-- 验证码 -->
			<view class="form-item" v-if="showVerifyCodeInput">
				<text class="item-label">验证码</text>
				<input 
					class="item-input" 
					type="number" 
					placeholder="请输入短信验证码" 
					v-model="formData.verifyCode"
					:disabled="isDisabled"
				/>
			</view>
			
			<!-- 身份证正面 -->
			<view class="form-item">
				<text class="item-label">身份证人像面</text>
				<view class="upload-area" @tap="uploadImage('front')" v-if="!formData.idCardFront || isDisabled">
					<image 
						v-if="formData.idCardFront" 
						:src="formData.idCardFront" 
						mode="aspectFill" 
						class="preview-image"
					></image>
					<view v-else class="upload-placeholder">
						<image src="/static/icons/camera.png" mode="aspectFit"></image>
						<text>点击上传</text>
					</view>
				</view>
				<view v-else class="image-preview">
					<image :src="formData.idCardFront" mode="aspectFill" class="preview-image"></image>
					<view class="delete-btn" @tap.stop="deleteImage('front')">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 身份证背面 -->
			<view class="form-item">
				<text class="item-label">身份证国徽面</text>
				<view class="upload-area" @tap="uploadImage('back')" v-if="!formData.idCardBack || isDisabled">
					<image 
						v-if="formData.idCardBack" 
						:src="formData.idCardBack" 
						mode="aspectFill" 
						class="preview-image"
					></image>
					<view v-else class="upload-placeholder">
						<image src="/static/icons/camera.png" mode="aspectFit"></image>
						<text>点击上传</text>
					</view>
				</view>
				<view v-else class="image-preview">
					<image :src="formData.idCardBack" mode="aspectFill" class="preview-image"></image>
					<view class="delete-btn" @tap.stop="deleteImage('back')">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 手持身份证 -->
			<view class="form-item">
				<text class="item-label">手持身份证照片 <text class="optional">(选填)</text></text>
				<view class="upload-area" @tap="uploadImage('selfie')" v-if="!formData.selfieWithIdCard || isDisabled">
					<image 
						v-if="formData.selfieWithIdCard" 
						:src="formData.selfieWithIdCard" 
						mode="aspectFill" 
						class="preview-image"
					></image>
					<view v-else class="upload-placeholder">
						<image src="/static/icons/camera.png" mode="aspectFit"></image>
						<text>点击上传</text>
					</view>
				</view>
				<view v-else class="image-preview">
					<image :src="formData.selfieWithIdCard" mode="aspectFill" class="preview-image"></image>
					<view class="delete-btn" @tap.stop="deleteImage('selfie')">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 隐私条款 -->
			<view class="agreement-item">
				<view class="checkbox" @tap="toggleAgreement">
					<view class="checkbox-inner" v-if="formData.agreement"></view>
				</view>
				<text class="agreement-text">我已阅读并同意</text>
				<text class="agreement-link" @tap="viewPrivacyPolicy">《实名认证服务协议》</text>
				<text class="agreement-text">和</text>
				<text class="agreement-link" @tap="viewUserAgreement">《个人信息保护声明》</text>
			</view>
			
			<!-- 提交按钮 -->
			<view 
				class="submit-btn" 
				:class="{'disabled': !isFormValid || isDisabled}"
				@tap="submitVerification"
			>
				<text>{{verificationStatus === 'rejected' ? '重新提交' : '提交认证'}}</text>
			</view>
			
			<!-- 提示文本 -->
			<view class="tips-section">
				<view class="tip-item">
					<text class="dot">•</text>
					<text class="tip-text">请确保上传证件清晰可见，信息完整</text>
				</view>
				<view class="tip-item">
					<text class="dot">•</text>
					<text class="tip-text">实名认证信息仅用于身份验证，我们会严格保护您的隐私</text>
				</view>
				<view class="tip-item">
					<text class="dot">•</text>
					<text class="tip-text">认证审核需要1-2个工作日，请耐心等待</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				verificationStatus: '', // 空字符串表示未认证，'processing'表示审核中，'approved'表示已认证，'rejected'表示被拒绝
				statusIcon: {
					'processing': '/static/icons/processing.png',
					'approved': '/static/icons/success.png',
					'rejected': '/static/icons/error.png'
				},
				statusText: {
					'processing': '审核中',
					'approved': '认证成功',
					'rejected': '认证失败'
				},
				statusDesc: {
					'processing': '您的实名认证信息正在审核中，预计1-2个工作日内完成审核，请耐心等待。',
					'approved': '您已通过实名认证，可以使用平台的全部功能。',
					'rejected': '很抱歉，您的实名认证未通过审核，请检查认证信息或联系客服。'
				},
				formData: {
					realName: '',
					idNumber: '',
					phoneNumber: '',
					verifyCode: '',
					idCardFront: '',
					idCardBack: '',
					selfieWithIdCard: '',
					agreement: false
				},
				showVerifyCodeInput: false,
				isPhoneVerified: false,
				isCounting: false,
				countdown: 60,
				codeText: '获取验证码',
				verificationReason: '' // 拒绝原因
			}
		},
		computed: {
			// 判断手机号是否有效
			isPhoneValid() {
				return /^1\d{10}$/.test(this.formData.phoneNumber);
			},
			// 判断表单是否有效
			isFormValid() {
				return this.formData.realName && 
					this.formData.idNumber && 
					this.formData.phoneNumber && 
					(this.isPhoneVerified || this.formData.verifyCode) && 
					this.formData.idCardFront && 
					this.formData.idCardBack && 
					this.formData.agreement;
			},
			// 判断表单是否被禁用
			isDisabled() {
				return this.verificationStatus === 'processing';
			}
		},
		onLoad() {
			this.checkVerificationStatus();
		},
		methods: {
			// 检查认证状态
			checkVerificationStatus() {
				// 从本地存储获取认证状态
				const verificationStorage = uni.getStorageSync('user_verification');
				if (verificationStorage) {
					const verification = JSON.parse(verificationStorage);
					this.verificationStatus = verification.status;
					this.formData = verification.formData || this.formData;
					this.verificationReason = verification.reason || '';
					this.isPhoneVerified = !!this.formData.phoneNumber;
				}
			},
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			// 显示帮助信息
			showHelp() {
				uni.showModal({
					title: '关于实名认证',
					content: '实名认证是为了保障用户的合法权益和平台的安全运营。通过实名认证后，您可以使用平台的全部功能，包括发布信息、交易和提现等操作。我们会严格保护您的个人信息安全。',
					showCancel: false
				});
			},
			// 发送验证码
			sendCode() {
				if (!this.isPhoneValid || this.isCounting) {
					return;
				}
				
				this.isCounting = true;
				this.countdown = 60;
				this.codeText = `${this.countdown}秒后重发`;
				
				// 模拟发送验证码
				uni.showToast({
					title: '验证码已发送',
					icon: 'success'
				});
				
				this.showVerifyCodeInput = true;
				
				// 倒计时
				const timer = setInterval(() => {
					this.countdown--;
					this.codeText = `${this.countdown}秒后重发`;
					
					if (this.countdown <= 0) {
						clearInterval(timer);
						this.isCounting = false;
						this.codeText = '获取验证码';
					}
				}, 1000);
			},
			// 验证手机号
			verifyPhone() {
				if (!this.formData.verifyCode) {
					uni.showToast({
						title: '请输入验证码',
						icon: 'none'
					});
					return;
				}
				
				// 模拟验证
				if (this.formData.verifyCode === '123456' || this.formData.verifyCode === '1234') {
					this.isPhoneVerified = true;
					uni.showToast({
						title: '手机号验证成功',
						icon: 'success'
					});
				} else {
					uni.showToast({
						title: '验证码错误',
						icon: 'none'
					});
				}
			},
			// 上传图片
			uploadImage(type) {
				if (this.isDisabled) {
					return;
				}
				
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						
						// 模拟上传过程
						uni.showLoading({
							title: '上传中...'
						});
						
						setTimeout(() => {
							uni.hideLoading();
							
							// 根据类型设置不同字段
							switch(type) {
								case 'front':
									this.formData.idCardFront = tempFilePath;
									break;
								case 'back':
									this.formData.idCardBack = tempFilePath;
									break;
								case 'selfie':
									this.formData.selfieWithIdCard = tempFilePath;
									break;
							}
						}, 1500);
					}
				});
			},
			// 删除图片
			deleteImage(type) {
				switch(type) {
					case 'front':
						this.formData.idCardFront = '';
						break;
					case 'back':
						this.formData.idCardBack = '';
						break;
					case 'selfie':
						this.formData.selfieWithIdCard = '';
						break;
				}
			},
			// 切换协议同意状态
			toggleAgreement() {
				if (!this.isDisabled) {
					this.formData.agreement = !this.formData.agreement;
				}
			},
			// 查看用户协议
			viewUserAgreement() {
				uni.navigateTo({
					url: '/pages/my/agreement?type=user'
				});
			},
			// 查看隐私政策
			viewPrivacyPolicy() {
				uni.navigateTo({
					url: '/pages/my/agreement?type=privacy'
				});
			},
			// 提交认证
			submitVerification() {
				if (!this.isFormValid || this.isDisabled) {
					return;
				}
				
				// 验证手机号
				if (!this.isPhoneVerified) {
					this.verifyPhone();
					if (!this.isPhoneVerified) {
						return;
					}
				}
				
				// 验证身份证号
				if (!this.validateIdNumber(this.formData.idNumber)) {
					uni.showToast({
						title: '身份证号格式不正确',
						icon: 'none'
					});
					return;
				}
				
				// 提交认证信息
				uni.showLoading({
					title: '提交中...'
				});
				
				setTimeout(() => {
					uni.hideLoading();
					
					// 更新认证状态
					this.verificationStatus = 'processing';
					
					// 保存到本地存储
					const verification = {
						status: this.verificationStatus,
						formData: this.formData,
						submitTime: new Date().getTime()
					};
					
					uni.setStorageSync('user_verification', JSON.stringify(verification));
					
					uni.showToast({
						title: '提交成功',
						icon: 'success'
					});
					
					// 模拟审核过程（开发环境用）
					setTimeout(() => {
						// 随机生成审核结果
						const result = Math.random() > 0.3 ? 'approved' : 'rejected';
						this.verificationStatus = result;
						
						// 更新本地存储
						verification.status = result;
						if (result === 'rejected') {
							verification.reason = '身份证信息与实际不符，请核对后重新提交';
							this.verificationReason = verification.reason;
						}
						
						uni.setStorageSync('user_verification', JSON.stringify(verification));
						
						// 更新用户信息
						if (result === 'approved') {
							const userInfoStorage = uni.getStorageSync('userInfo');
							if (userInfoStorage) {
								const userInfo = JSON.parse(userInfoStorage);
								userInfo.isVerified = true;
								userInfo.realName = this.formData.realName;
								uni.setStorageSync('userInfo', JSON.stringify(userInfo));
							}
						}
					}, 5000); // 5秒后返回审核结果（仅供演示）
				}, 1500);
			},
			// 验证身份证号
			validateIdNumber(idNumber) {
				// 简单验证，实际应用应该有更严格的验证
				const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
				return reg.test(idNumber);
			}
		}
	}
</script>

<style>
	.verify-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn, .right-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image, .right-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.status-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 50rpx 30rpx;
		background-color: #ffffff;
	}
	
	.status-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
	}
	
	.status-icon image {
		width: 100%;
		height: 100%;
	}
	
	.status-text {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
	}
	
	.status-desc {
		font-size: 28rpx;
		color: #666666;
		text-align: center;
		line-height: 1.6;
	}
	
	.form-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
	}
	
	.form-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 30rpx;
	}
	
	.form-item {
		margin-bottom: 30rpx;
	}
	
	.item-label {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.optional {
		font-size: 24rpx;
		color: #999999;
		font-weight: normal;
	}
	
	.item-input {
		width: 100%;
		height: 90rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333333;
		box-sizing: border-box;
	}
	
	.phone-input-wrapper {
		display: flex;
		align-items: center;
	}
	
	.phone-input-wrapper .item-input {
		flex: 1;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}
	
	.verify-btn {
		width: 200rpx;
		height: 90rpx;
		background-color: #ff5a5f;
		border-top-right-radius: 8rpx;
		border-bottom-right-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.verify-btn.disabled {
		background-color: #cccccc;
	}
	
	.verify-btn text {
		font-size: 28rpx;
		color: #ffffff;
	}
	
	.upload-area, .image-preview {
		width: 100%;
		height: 360rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		overflow: hidden;
		position: relative;
	}
	
	.upload-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100%;
	}
	
	.upload-placeholder image {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 20rpx;
	}
	
	.upload-placeholder text {
		font-size: 28rpx;
		color: #999999;
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
	}
	
	.delete-btn {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		width: 60rpx;
		height: 60rpx;
		background-color: rgba(0, 0, 0, 0.5);
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.delete-btn image {
		width: 30rpx;
		height: 30rpx;
	}
	
	.agreement-item {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
	}
	
	.checkbox {
		width: 40rpx;
		height: 40rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.checkbox-inner {
		width: 24rpx;
		height: 24rpx;
		background-color: #ff5a5f;
		border-radius: 4rpx;
	}
	
	.agreement-text {
		font-size: 26rpx;
		color: #666666;
	}
	
	.agreement-link {
		font-size: 26rpx;
		color: #ff5a5f;
	}
	
	.submit-btn {
		width: 100%;
		height: 90rpx;
		background-color: #ff5a5f;
		border-radius: 45rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
	}
	
	.submit-btn.disabled {
		background-color: #cccccc;
	}
	
	.submit-btn text {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: bold;
	}
	
	.tips-section {
		margin-top: 30rpx;
	}
	
	.tip-item {
		display: flex;
		margin-bottom: 16rpx;
	}
	
	.dot {
		margin-right: 10rpx;
		color: #999999;
	}
	
	.tip-text {
		font-size: 24rpx;
		color: #999999;
		line-height: 1.6;
	}
</style> 