<template>
	<view class="orders-container">
		<!-- 固定头部区域 -->
		<view class="fixed-header">
			<!-- 头部导航 -->
			<view class="header">
				<view class="back-btn" @tap="navigateBack">
					<image src="/static/icons/back.png" mode="aspectFit"></image>
				</view>
				<text class="title">我的订单</text>
				<view class="right-btn">
					<text></text>
				</view>
			</view>
			
			<!-- 订单状态tab - 固定位置 -->
			<view class="tab-section">
				<view 
					v-for="(tab, index) in tabs" 
					:key="index" 
					:class="['tab-item', activeTab === index ? 'active-tab' : '']"
					@tap="switchTab(index)"
				>
					<text>{{tab.name}}</text>
				</view>
			</view>
		</view>
		
		<!-- 订单列表区域 - 设置高度自适应 -->
		<scroll-view 
			scroll-y 
			class="orders-list" 
			@scrolltolower="loadMore"
			:refresher-enabled="true"
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
			:style="{ height: scrollViewHeight }"
			show-scrollbar="false"
		>
			<view v-if="filteredOrders.length === 0" class="empty-tip">
				<image src="/static/images/empty.png" mode="aspectFit"></image>
				<text>暂无订单</text>
			</view>
			
			<view v-else class="orders-content">
				<view 
					v-for="(order, index) in filteredOrders" 
					:key="order.id"
					class="order-item"
				>
					<view class="order-header">
						<view class="store-info">
							<image src="/static/icons/store.png" mode="aspectFit" class="store-icon"></image>
							<text class="store-name">{{order.storeName}}</text>
						</view>
						<text :class="['order-status', 'status-' + order.status]">{{getStatusText(order.status)}}</text>
					</view>
					
					<view class="order-content" @tap="viewOrderDetail(order)">
						<image :src="order.productImage" mode="aspectFill" class="product-image"></image>
						<view class="order-info">
							<view class="product-name">{{order.productName}}</view>
							<view class="product-spec">{{order.productSpec}}</view>
							<view class="price-quantity">
								<text class="price">¥{{order.price}}</text>
								<text class="quantity">x{{order.quantity}}</text>
							</view>
						</view>
					</view>
					
					<view class="order-footer">
						<view class="total-section">
							<text>共{{order.quantity}}件商品</text>
							<text>实付：<text class="total-price">¥{{order.totalAmount}}</text></text>
						</view>
						
						<view class="action-section">
							<view 
								v-for="(action, actionIndex) in getActions(order.status)" 
								:key="actionIndex"
								:class="['action-btn', action.primary ? 'primary-btn' : '']"
								@tap.stop="handleAction(action.type, order)"
							>
								<text>{{action.text}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<view class="load-more" v-if="hasMore && filteredOrders.length > 0">
				<text>加载中...</text>
			</view>
			
			<view class="no-more" v-if="!hasMore && filteredOrders.length > 0">
				<text>已加载全部内容</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabs: [
					{ name: '全部', status: 'all' },
					{ name: '待付款', status: 'pending' },
					{ name: '进行中', status: 'processing' },
					{ name: '待评价', status: 'delivered' },
					{ name: '已完成', status: 'completed' }
				],
				activeTab: 0,
				ordersList: [],
				pageNum: 1,
				pageSize: 10,
				hasMore: true,
				isRefreshing: false,
				scrollViewHeight: 'calc(100vh - 180rpx)', // 默认高度，将在onReady中计算
				loading: false
			}
		},
		computed: {
			filteredOrders() {
				if (this.activeTab === 0) {
					return this.ordersList;
				} else {
					const status = this.tabs[this.activeTab].status;
					return this.ordersList.filter(order => order.status === status);
				}
			}
		},
		onLoad() {
			this.loadOrdersData();
		},
		onReady() {
			this.calculateScrollViewHeight();
		},
		methods: {
			// 计算滚动区域的高度
			calculateScrollViewHeight() {
				const systemInfo = uni.getSystemInfoSync();
				const headerHeight = 180; // 头部+Tab高度，单位rpx
				const headerPx = (headerHeight * systemInfo.windowWidth) / 750; // 转换为px
				const scrollHeight = systemInfo.windowHeight - headerPx;
				// 设置scroll-view的高度
				this.scrollViewHeight = scrollHeight + 'px';
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true;
				// 重新加载数据
				setTimeout(() => {
					this.refreshData();
					this.isRefreshing = false;
				}, 1000);
			},
			
			// 刷新数据
			refreshData() {
				this.ordersList = [];
				this.pageNum = 1;
				this.hasMore = true;
				this.loadOrdersData();
			},
			
			// 加载订单数据
			loadOrdersData() {
				this.loading = true;
				
				// 使用API接口获取订单数据
				const params = {
					page: this.pageNum,
					pageSize: this.pageSize,
					status: this.activeTab === 0 ? '' : this.getStatusByTab(this.activeTab)
				};
				
				// 调用订单API
				this.$api.order.getOrderList(params).then(res => {
					if (res.code === 0) {
						if (this.pageNum === 1) {
							this.ordersList = res.data.list || [];
						} else {
							this.ordersList = [...this.ordersList, ...(res.data.list || [])];
						}
						
						this.hasMore = this.ordersList.length < res.data.total;
					} else {
						uni.showToast({
							title: res.message || '获取订单数据失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					console.error('获取订单数据失败', err);
					uni.showToast({
						title: '网络异常，请稍后重试',
						icon: 'none'
					});
				}).finally(() => {
					this.loading = false;
					uni.stopPullDownRefresh();
				});
			},
			
			// 获取状态对应的Tab索引
			getStatusByTab(tabIndex) {
				switch (tabIndex) {
					case 1: return 'pending'; // 待付款
					case 2: return 'processing'; // 处理中
					case 3: return 'delivered'; // 待评价
					case 4: return 'completed'; // 已完成
					default: return '';
				}
			},
			
			// 加载更多数据
			loadMore() {
				if (!this.hasMore || this.loading) return;
				this.pageNum++;
				this.loadOrdersData();
			},
			
			// 切换标签
			switchTab(index) {
				this.activeTab = index;
			},
			
			// 获取订单状态文本
			getStatusText(status) {
				switch (status) {
					case 'pending':
						return '待付款';
					case 'processing':
						return '处理中';
					case 'shipping':
						return '配送中';
					case 'delivered':
						return '待评价';
					case 'completed':
						return '已完成';
					case 'cancelled':
						return '已取消';
					default:
						return '未知状态';
				}
			},
			
			// 获取订单操作按钮
			getActions(status) {
				switch (status) {
					case 'pending':
						return [
							{ text: '取消订单', type: 'cancel' },
							{ text: '立即付款', type: 'pay', primary: true }
						];
					case 'processing':
						return [
							{ text: '查看物流', type: 'logistics' },
							{ text: '联系客服', type: 'contact' }
						];
					case 'delivered':
						return [
							{ text: '联系客服', type: 'contact' },
							{ text: '立即评价', type: 'review', primary: true }
						];
					case 'completed':
						return [
							{ text: '删除订单', type: 'delete' },
							{ text: '再次购买', type: 'rebuy', primary: true }
						];
					default:
						return [];
				}
			},
			
			// 处理订单操作
			handleAction(type, order) {
				switch (type) {
					case 'cancel':
						this.cancelOrder(order);
						break;
					case 'pay':
						this.payOrder(order);
						break;
					case 'logistics':
						this.viewLogistics(order);
						break;
					case 'contact':
						this.contactService(order);
						break;
					case 'review':
						this.reviewOrder(order);
						break;
					case 'delete':
						this.deleteOrder(order);
						break;
					case 'rebuy':
						this.rebuyOrder(order);
						break;
				}
			},
			
			// 取消订单
			cancelOrder(order) {
				uni.showModal({
					title: '提示',
					content: '确定要取消此订单吗？',
					success: (res) => {
						if (res.confirm) {
							// 更新订单状态
							const index = this.ordersList.findIndex(item => item.id === order.id);
							if (index !== -1) {
								this.ordersList[index].status = 'cancelled';
								// 更新本地存储
								uni.setStorageSync('user_orders', JSON.stringify(this.ordersList));
							}
							
							uni.showToast({
								title: '订单已取消',
								icon: 'success'
							});
						}
					}
				});
			},
			
			// 支付订单
			payOrder(order) {
				uni.showLoading({
					title: '正在跳转支付...'
				});
				
				setTimeout(() => {
					uni.hideLoading();
					uni.showModal({
						title: '模拟支付',
						content: '这是一个模拟的支付过程，点击确定表示支付成功',
						success: (res) => {
							if (res.confirm) {
								// 更新订单状态
								const index = this.ordersList.findIndex(item => item.id === order.id);
								if (index !== -1) {
									this.ordersList[index].status = 'processing';
									// 更新本地存储
									uni.setStorageSync('user_orders', JSON.stringify(this.ordersList));
								}
								
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								});
							}
						}
					});
				}, 1000);
			},
			
			// 查看物流
			viewLogistics(order) {
				uni.showToast({
					title: '正在查询物流信息',
					icon: 'none'
				});
			},
			
			// 联系客服
			contactService(order) {
				uni.showToast({
					title: '正在连接客服...',
					icon: 'none'
				});
			},
			
			// 评价订单
			reviewOrder(order) {
				uni.navigateTo({
					url: '/pages/my/review?orderId=' + order.id
				});
			},
			
			// 删除订单
			deleteOrder(order) {
				uni.showModal({
					title: '提示',
					content: '确定要删除此订单吗？删除后无法恢复',
					success: (res) => {
						if (res.confirm) {
							// 从列表中删除订单
							this.ordersList = this.ordersList.filter(item => item.id !== order.id);
							// 更新本地存储
							uni.setStorageSync('user_orders', JSON.stringify(this.ordersList));
							
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},
			
			// 再次购买
			rebuyOrder(order) {
				uni.showToast({
					title: '已加入购物车',
					icon: 'success'
				});
			},
			
			// 查看订单详情
			viewOrderDetail(order) {
				uni.navigateTo({
					url: '/pages/my/order-detail?id=' + order.id
				});
			},
			
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	.orders-container {
		position: relative;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background-color: #f5f5f5;
	}
	
	/* 固定头部区域样式 */
	.fixed-header {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 100;
		background-color: #fff;
		/* 使用transform硬件加速提高性能 */
		transform: translateZ(0);
		will-change: transform;
	}
	
	.header {
		display: flex;
		align-items: center;
		height: 90rpx;
		padding: 0 30rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.right-btn {
		width: 60rpx;
	}
	
	/* Tab栏样式 - 固定高度 */
	.tab-section {
		display: flex;
		height: 90rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.tab-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666666;
		position: relative;
	}
	
	.active-tab {
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.active-tab::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 25%;
		width: 50%;
		height: 4rpx;
		background-color: #ff5a5f;
	}
	
	/* 订单列表区域样式 */
	.orders-list {
		flex: 1;
		margin-top: 180rpx; /* 与固定头部高度一致 */
		position: relative;
		/* 优化滚动性能 */
		-webkit-overflow-scrolling: touch; 
	}
	
	.orders-content {
		padding-bottom: 30rpx;
	}
	
	.empty-tip {
		padding-top: 200rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.empty-tip image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}
	
	.empty-tip text {
		font-size: 28rpx;
		color: #999999;
	}
	
	.order-item {
		margin: 20rpx 20rpx 0;
		background-color: #ffffff;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		/* 使用transform创建GPU层，提高性能 */
		transform: translateZ(0);
	}
	
	.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.store-info {
		display: flex;
		align-items: center;
	}
	
	.store-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}
	
	.store-name {
		font-size: 28rpx;
		color: #333333;
	}
	
	.order-status {
		font-size: 28rpx;
	}
	
	.status-pending {
		color: #ff5a5f;
	}
	
	.status-processing {
		color: #ff9500;
	}
	
	.status-delivered {
		color: #4cd964;
	}
	
	.status-completed {
		color: #999999;
	}
	
	.order-content {
		display: flex;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.product-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
		background-color: #f5f5f5;
	}
	
	.order-info {
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	.product-name {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 10rpx;
		line-height: 1.4;
	}
	
	.product-spec {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 20rpx;
	}
	
	.price-quantity {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.price {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff5a5f;
	}
	
	.quantity {
		font-size: 28rpx;
		color: #999999;
	}
	
	.order-footer {
		padding: 20rpx 30rpx;
	}
	
	.total-section {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.total-section text {
		font-size: 26rpx;
		color: #666666;
		margin-left: 20rpx;
	}
	
	.total-price {
		font-size: 30rpx;
		font-weight: bold;
		color: #ff5a5f;
	}
	
	.action-section {
		display: flex;
		justify-content: flex-end;
	}
	
	.action-btn {
		min-width: 140rpx;
		height: 60rpx;
		border: 1rpx solid #dddddd;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 20rpx;
	}
	
	.action-btn text {
		font-size: 26rpx;
		color: #666666;
	}
	
	.primary-btn {
		background-color: #ff5a5f;
		border-color: #ff5a5f;
	}
	
	.primary-btn text {
		color: #ffffff;
	}
	
	.load-more, .no-more {
		text-align: center;
		padding: 20rpx 0;
	}
	
	.load-more text, .no-more text {
		font-size: 24rpx;
		color: #999999;
	}
</style> 