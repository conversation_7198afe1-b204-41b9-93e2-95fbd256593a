<template>
	<view class="service-orders-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">顶班接单</text>
		</view>
		
		<!-- 搜索框 -->
		<view class="search-box">
			<view class="search-input">
				<image src="/static/icons/search.png" mode="aspectFit" class="search-icon"></image>
				<input 
					type="text" 
					placeholder="搜索站点名称、订单号" 
					v-model="searchKeyword"
					@input="onSearch"
				/>
				<view class="clear-icon" v-if="searchKeyword" @tap="clearSearch">
					<image src="/static/icons/close.png" mode="aspectFit"></image>
				</view>
			</view>
		</view>
		
		<!-- 选项卡 -->
		<view class="tabs">
			<view 
				class="tab-item" 
				v-for="(tab, index) in tabs" 
				:key="index"
				:class="{active: currentTab === index}"
				@tap="changeTab(index)"
			>
				<text>{{tab}}</text>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="filteredOrderList.length === 0">
			<image src="/static/icons/empty-order.png" mode="aspectFit" class="empty-icon"></image>
			<text class="empty-text" v-if="searchKeyword">未找到相关订单</text>
			<text class="empty-text" v-else>暂无{{tabs[currentTab]}}订单</text>
		</view>
		
		<!-- 订单列表 -->
		<view class="order-list" v-else>
			<view class="order-item" v-for="(item, index) in filteredOrderList" :key="index" @tap="viewOrderDetail(item)">
				<view class="order-header">
					<text class="order-id">订单号：{{item.orderId}}</text>
					<text class="order-status" :class="item.status">{{getStatusText(item.status)}}</text>
				</view>
				<view class="order-content">
					<image :src="item.stationImage" mode="aspectFill" class="station-image"></image>
					<view class="order-info">
						<text class="station-name">{{item.stationName}}</text>
						<text class="station-address">{{item.stationAddress}}</text>
						<view class="info-item">
							<text class="label">工作时间</text>
							<text class="value">{{item.workTime}}</text>
						</view>
						<view class="info-item">
							<text class="label">时薪</text>
							<text class="value">{{item.hourlyRate}}元/小时</text>
						</view>
						<view class="info-item">
							<text class="label">工作时长</text>
							<text class="value">{{item.duration}}小时</text>
						</view>
					</view>
				</view>
				<view class="order-footer">
					<view class="order-time">发布时间：{{formatDate(item.createTime)}}</view>
					<view class="order-btns">
						<view class="btn" @tap.stop="contactStation(item)">立即联系</view>
						<view class="btn primary" @tap.stop="applyOrder(item)">立即接单</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabs: ['全部', '待接单', '进行中', '已完成'],
				currentTab: 0,
				orderList: [],
				searchKeyword: '',
				hasMoreData: true,
				isLoading: false
			}
		},
		computed: {
			// 过滤后的订单列表
			filteredOrderList() {
				let filtered = this.orderList;
				
				// 按选项卡筛选
				if (this.currentTab !== 0) {
					const statusMap = ['', 'pending', 'processing', 'completed'];
					filtered = filtered.filter(item => item.status === statusMap[this.currentTab]);
				}
				
				// 按关键词搜索
				if (this.searchKeyword) {
					const keyword = this.searchKeyword.toLowerCase();
					filtered = filtered.filter(item => {
						return item.orderId.toLowerCase().includes(keyword) || 
							item.stationName.toLowerCase().includes(keyword) || 
							item.stationAddress.toLowerCase().includes(keyword);
					});
				}
				
				return filtered;
			}
		},
		onLoad() {
			this.loadOrders();
		},
		methods: {
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			
			// 切换选项卡
			changeTab(index) {
				this.currentTab = index;
			},
			
			// 搜索
			onSearch(e) {
				this.searchKeyword = e.detail.value;
			},
			
			// 清除搜索
			clearSearch() {
				this.searchKeyword = '';
			},
			
			// 加载订单数据
			loadOrders() {
				// 从本地存储获取订单数据
				const storedOrders = uni.getStorageSync('serviceOrders');
				if (storedOrders) {
					this.orderList = JSON.parse(storedOrders);
					this.hasMoreData = false;
				} else {
					// 初始化示例数据
					const sampleOrders = [
						{
							orderId: 'SO2023112401',
							stationName: '菜鸟驿站-西湖文化广场店',
							stationAddress: '杭州市西湖区文化广场12号',
							stationContact: '李经理',
							stationPhone: '13800138001',
							stationImage: '/static/images/station1.jpg',
							workTime: '2023-12-05 09:00-18:00',
							hourlyRate: 20,
							duration: 8,
							totalAmount: 160,
							workContent: '负责快递包裹的收发和管理工作，需要熟悉菜鸟驿站系统操作。',
							requirements: '有驿站工作经验优先，工作认真负责，有良好的沟通能力。',
							status: 'pending',
							createTime: new Date('2023-11-24 10:30:00')
						},
						{
							orderId: 'SO2023112402',
							stationName: '京东快递-滨江星光店',
							stationAddress: '杭州市滨江区星光大道125号',
							stationContact: '张店长',
							stationPhone: '13900139002',
							stationImage: '/static/images/station2.jpg',
							workTime: '2023-12-06 14:00-22:00',
							hourlyRate: 22,
							duration: 8,
							totalAmount: 176,
							workContent: '负责京东快递的收派件工作，需要熟练使用PDA设备扫描和登记包裹。',
							requirements: '有快递行业经验，能吃苦耐劳，有责任心。',
							status: 'pending',
							createTime: new Date('2023-11-24 14:15:00')
						},
						{
							orderId: 'SO2023112301',
							stationName: '顺丰驿站-湖滨银泰店',
							stationAddress: '杭州市上城区湖滨银泰5楼',
							stationContact: '王站长',
							stationPhone: '13700137003',
							stationImage: '/static/images/station3.jpg',
							workTime: '2023-12-03 10:00-18:00',
							hourlyRate: 25,
							duration: 8,
							totalAmount: 200,
							workContent: '负责顺丰快递的收发工作，包括包裹验收、分类、存储和交付。',
							requirements: '形象气质佳，熟悉快递业务流程，有良好的服务意识。',
							status: 'processing',
							createTime: new Date('2023-11-23 09:45:00')
						},
						{
							orderId: 'SO2023112201',
							stationName: '中通驿站-城西银泰店',
							stationAddress: '杭州市拱墅区城西银泰2楼',
							stationContact: '周经理',
							stationPhone: '13600136004',
							stationImage: '/static/images/station4.jpg',
							workTime: '2023-11-25 09:30-17:30',
							hourlyRate: 18,
							duration: 8,
							totalAmount: 144,
							workContent: '负责中通快递包裹的收发和客户服务工作。',
							requirements: '有耐心，做事细心，能适应高强度工作。',
							status: 'completed',
							createTime: new Date('2023-11-22 11:20:00')
						},
						{
							orderId: 'SO2023112101',
							stationName: '韵达快递-西溪印象城店',
							stationAddress: '杭州市余杭区五常大道59号',
							stationContact: '赵站长',
							stationPhone: '13500135005',
							stationImage: '/static/images/station5.jpg',
							workTime: '2023-11-24 13:00-21:00',
							hourlyRate: 20,
							duration: 8,
							totalAmount: 160,
							workContent: '负责韵达快递的收派件和日常运营工作。',
							requirements: '熟悉电脑操作，有快递行业经验优先考虑。',
							status: 'completed',
							createTime: new Date('2023-11-21 16:30:00')
						}
					];
					
					// 保存到本地存储
					uni.setStorageSync('serviceOrders', JSON.stringify(sampleOrders));
					this.orderList = sampleOrders;
					this.hasMoreData = false;
				}
				
				this.isLoading = false;
			},
			
			// 格式化日期
			formatDate(date) {
				const d = new Date(date);
				const year = d.getFullYear();
				const month = String(d.getMonth() + 1).padStart(2, '0');
				const day = String(d.getDate()).padStart(2, '0');
				const hours = String(d.getHours()).padStart(2, '0');
				const minutes = String(d.getMinutes()).padStart(2, '0');
				
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			},
			
			// 获取状态文本
			getStatusText(status) {
				switch (status) {
					case 'pending':
						return '待接单';
					case 'processing':
						return '进行中';
					case 'completed':
						return '已完成';
					case 'cancelled':
						return '已取消';
					default:
						return '未知状态';
				}
			},
			
			// 查看订单详情
			viewOrderDetail(item) {
				// 将当前订单保存到本地存储
				uni.setStorageSync('currentOrder', JSON.stringify(item));
				
				// 跳转到订单详情页
				uni.navigateTo({
					url: '/pages/my/service-order-detail'
				});
			},
			
			// 联系驿站
			contactStation(item) {
				uni.showActionSheet({
					itemList: ['拨打电话', '复制微信号'],
					success: (res) => {
						if (res.tapIndex === 0) {
							// 拨打电话
							uni.makePhoneCall({
								phoneNumber: item.stationPhone,
								fail: () => {
									uni.showToast({
										title: '拨号已取消',
										icon: 'none'
									});
								}
							});
						} else if (res.tapIndex === 1) {
							// 复制微信号
							uni.setClipboardData({
								data: item.stationPhone,
								success: () => {
									uni.showToast({
										title: '微信号已复制',
										icon: 'success'
									});
								}
							});
						}
					}
				});
			},
			
			// 接单
			applyOrder(item) {
				if (item.status !== 'pending') {
					uni.showToast({
						title: '该订单不可接',
						icon: 'none'
					});
					return;
				}
				
				uni.showModal({
					title: '确认接单',
					content: `您确定要接${item.stationName}的顶班订单吗？`,
					success: (res) => {
						if (res.confirm) {
							// 更新订单状态
							const index = this.orderList.findIndex(order => order.orderId === item.orderId);
							if (index !== -1) {
								this.orderList[index].status = 'processing';
								// 更新本地存储
								uni.setStorageSync('serviceOrders', JSON.stringify(this.orderList));
								
								// 提示成功
								uni.showToast({
									title: '接单成功',
									icon: 'success'
								});
							}
						}
					}
				});
			}
		}
	}
</script>

<style>
	.service-orders-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		height: 90rpx;
		padding: 0 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.search-box {
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.search-input {
		height: 70rpx;
		background-color: #f5f5f5;
		border-radius: 35rpx;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
	}
	
	.search-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}
	
	.search-input input {
		flex: 1;
		height: 70rpx;
		font-size: 28rpx;
	}
	
	.clear-icon {
		width: 40rpx;
		height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.clear-icon image {
		width: 24rpx;
		height: 24rpx;
	}
	
	.tabs {
		display: flex;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
		margin-bottom: 20rpx;
	}
	
	.tab-item {
		flex: 1;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666666;
		position: relative;
	}
	
	.tab-item.active {
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.tab-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 4rpx;
		background-color: #ff5a5f;
	}
	
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 200rpx;
	}
	
	.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
	
	.order-list {
		padding: 0 20rpx;
	}
	
	.order-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
	}
	
	.order-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.order-id {
		font-size: 26rpx;
		color: #666666;
	}
	
	.order-status {
		font-size: 26rpx;
		font-weight: bold;
	}
	
	.order-status.pending {
		color: #ff9500;
	}
	
	.order-status.processing {
		color: #007aff;
	}
	
	.order-status.completed {
		color: #34c759;
	}
	
	.order-status.cancelled {
		color: #999999;
	}
	
	.order-content {
		display: flex;
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.station-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
		background-color: #f0f0f0;
		flex-shrink: 0;
	}
	
	.order-info {
		flex: 1;
	}
	
	.station-name {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
	}
	
	.station-address {
		font-size: 26rpx;
		color: #666666;
		margin-bottom: 20rpx;
	}
	
	.info-item {
		display: flex;
		margin-bottom: 10rpx;
	}
	
	.label {
		width: 140rpx;
		font-size: 26rpx;
		color: #999999;
	}
	
	.value {
		flex: 1;
		font-size: 26rpx;
		color: #333333;
	}
	
	.order-footer {
		padding: 20rpx;
	}
	
	.order-time {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 20rpx;
	}
	
	.order-btns {
		display: flex;
		justify-content: flex-end;
	}
	
	.btn {
		min-width: 160rpx;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		background-color: #ffffff;
		border: 1rpx solid #dddddd;
		border-radius: 30rpx;
		font-size: 26rpx;
		color: #666666;
		margin-left: 20rpx;
	}
	
	.btn.primary {
		background-color: #ff5a5f;
		color: #ffffff;
		border-color: #ff5a5f;
	}
</style> 