<template>
	<view class="business-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">商务合作设置</text>
		</view>
		
		<!-- 验证提示 -->
		<view class="verify-notice" v-if="!isStationVerified">
			<view class="notice-content">
				<text class="notice-title">请先完成驿站认证</text>
				<text class="notice-desc">完成认证后可设置商务合作选项</text>
			</view>
			<view class="notice-btn" @tap="goToVerify">去认证</view>
		</view>
		
		<!-- 设置区域 -->
		<view class="settings-section" v-if="isStationVerified">
			<!-- 联系方式 -->
			<view class="settings-group">
				<view class="group-title">联系方式</view>
				<view class="settings-item">
					<text class="item-label">联系人</text>
					<input class="item-input" v-model="settings.contactName" placeholder="请输入联系人姓名" />
				</view>
				<view class="settings-item">
					<text class="item-label">联系电话</text>
					<input class="item-input" v-model="settings.contactPhone" placeholder="请输入联系电话" type="number" />
				</view>
				<view class="settings-item">
					<text class="item-label">微信号</text>
					<input class="item-input" v-model="settings.wechat" placeholder="请输入微信号" />
				</view>
			</view>
			
			<!-- 业务合作 -->
			<view class="settings-group">
				<view class="group-title">业务合作</view>
				<view class="switch-item">
					<text class="switch-label">接收设备转让询价</text>
					<switch :checked="settings.acceptEquipment" @change="onSwitchChange('acceptEquipment', $event)" color="#ff5a5f" />
				</view>
				<view class="switch-item">
					<text class="switch-label">接收驿站转让询价</text>
					<switch :checked="settings.acceptStation" @change="onSwitchChange('acceptStation', $event)" color="#ff5a5f" />
				</view>
				<view class="switch-item">
					<text class="switch-label">接收人才招聘合作</text>
					<switch :checked="settings.acceptRecruitment" @change="onSwitchChange('acceptRecruitment', $event)" color="#ff5a5f" />
				</view>
			</view>
			
			<!-- 定价设置 -->
			<view class="settings-group">
				<view class="group-title">收件定价</view>
				<view class="settings-item">
					<text class="item-label">小件收费(元/件)</text>
					<input class="item-input price-input" v-model="settings.smallPrice" type="digit" placeholder="0.00" />
				</view>
				<view class="settings-item">
					<text class="item-label">大件收费(元/件)</text>
					<input class="item-input price-input" v-model="settings.largePrice" type="digit" placeholder="0.00" />
				</view>
			</view>
			
			<!-- 营业时间 -->
			<view class="settings-group">
				<view class="group-title">营业时间</view>
				<view class="time-item">
					<view class="time-label">营业时间</view>
					<view class="time-picker-group">
						<picker mode="time" :value="settings.openTime" @change="onTimeChange('openTime', $event)">
							<view class="time-picker">{{settings.openTime}}</view>
						</picker>
						<text class="time-separator">至</text>
						<picker mode="time" :value="settings.closeTime" @change="onTimeChange('closeTime', $event)">
							<view class="time-picker">{{settings.closeTime}}</view>
						</picker>
					</view>
				</view>
				<view class="switch-item">
					<text class="switch-label">周末营业</text>
					<switch :checked="settings.weekendOpen" @change="onSwitchChange('weekendOpen', $event)" color="#ff5a5f" />
				</view>
			</view>
			
			<!-- 提交按钮 -->
			<view class="submit-btn" @tap="saveSettings">
				<text>保存设置</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isStationVerified: false,
				settings: {
					contactName: '',
					contactPhone: '',
					wechat: '',
					acceptEquipment: true,
					acceptStation: true,
					acceptRecruitment: true,
					smallPrice: '1.00',
					largePrice: '3.00',
					openTime: '08:00',
					closeTime: '20:00',
					weekendOpen: true
				}
			}
		},
		onLoad() {
			this.checkVerificationStatus();
			this.loadSettings();
		},
		methods: {
			// 检查认证状态
			checkVerificationStatus() {
				const userInfoStorage = uni.getStorageSync('userInfo');
				if (userInfoStorage) {
					const userInfo = JSON.parse(userInfoStorage);
					this.isStationVerified = userInfo.isStationVerified || false;
				}
			},
			
			// 加载设置
			loadSettings() {
				const settingsStorage = uni.getStorageSync('business_settings');
				if (settingsStorage) {
					this.settings = {...this.settings, ...JSON.parse(settingsStorage)};
				}
			},
			
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			
			// 前往认证页面
			goToVerify() {
				uni.navigateTo({
					url: '/pages/my/verify-station'
				});
			},
			
			// 开关变化
			onSwitchChange(key, event) {
				this.settings[key] = event.detail.value;
			},
			
			// 时间变化
			onTimeChange(key, event) {
				this.settings[key] = event.detail.value;
			},
			
			// 保存设置
			saveSettings() {
				// 验证联系方式
				if (!this.settings.contactName || !this.settings.contactPhone) {
					uni.showToast({
						title: '请完善联系人信息',
						icon: 'none'
					});
					return;
				}
				
				// 验证手机号
				if (!/^1\d{10}$/.test(this.settings.contactPhone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				// 保存设置
				uni.showLoading({
					title: '保存中...'
				});
				
				// 模拟保存过程
				setTimeout(() => {
					uni.hideLoading();
					
					// 保存到本地存储
					uni.setStorageSync('business_settings', JSON.stringify(this.settings));
					
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
				}, 1000);
			}
		}
	}
</script>

<style>
	.business-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 30rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
		flex: 1;
		text-align: center;
		margin-right: 60rpx;
	}
	
	.verify-notice {
		display: flex;
		align-items: center;
		padding: 30rpx;
		background-color: #fff8f8;
		margin: 20rpx;
		border-radius: 8rpx;
	}
	
	.notice-content {
		flex: 1;
	}
	
	.notice-title {
		font-size: 28rpx;
		color: #ff5a5f;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
	}
	
	.notice-desc {
		font-size: 24rpx;
		color: #999999;
		display: block;
	}
	
	.notice-btn {
		padding: 10rpx 30rpx;
		background-color: #ff5a5f;
		color: #ffffff;
		font-size: 28rpx;
		border-radius: 30rpx;
	}
	
	.settings-section {
		padding: 0 20rpx;
	}
	
	.settings-group {
		background-color: #ffffff;
		border-radius: 8rpx;
		padding: 20rpx 30rpx;
		margin-bottom: 20rpx;
	}
	
	.group-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 30rpx;
		position: relative;
		padding-left: 20rpx;
	}
	
	.group-title:before {
		content: '';
		position: absolute;
		left: 0;
		top: 6rpx;
		width: 6rpx;
		height: 32rpx;
		background-color: #ff5a5f;
		border-radius: 3rpx;
	}
	
	.settings-item {
		margin-bottom: 30rpx;
	}
	
	.item-label {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 15rpx;
		display: block;
	}
	
	.item-input {
		width: 100%;
		height: 90rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333333;
		box-sizing: border-box;
	}
	
	.price-input {
		text-align: right;
	}
	
	.switch-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.switch-item:last-child {
		border-bottom: none;
	}
	
	.switch-label {
		font-size: 28rpx;
		color: #333333;
	}
	
	.time-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.time-label {
		font-size: 28rpx;
		color: #333333;
	}
	
	.time-picker-group {
		display: flex;
		align-items: center;
	}
	
	.time-picker {
		background-color: #f9f9f9;
		padding: 10rpx 20rpx;
		border-radius: 6rpx;
		font-size: 28rpx;
		color: #333333;
		min-width: 140rpx;
		text-align: center;
	}
	
	.time-separator {
		margin: 0 15rpx;
		color: #999999;
		font-size: 24rpx;
	}
	
	.submit-btn {
		width: 100%;
		height: 90rpx;
		background-color: #ff5a5f;
		border-radius: 45rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 30rpx;
		margin-bottom: 50rpx;
	}
	
	.submit-btn text {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: bold;
	}
</style> 