<!-- pages/my/reset-password.wxml -->
<view class="reset-password-container">
  <view class="header">
    <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
    <text class="title">重置密码</text>
  </view>
  
  <!-- 重置密码表单 -->
  <view class="form-container">
    <!-- 手机号输入 -->
    <view class="input-group">
      <text class="label">手机号码</text>
      <view class="input-box">
        <text class="prefix">+86</text>
        <input type="number" value="{{phoneNumber}}" bindinput="inputPhoneNumber" placeholder="请输入手机号码" maxlength="11" />
      </view>
    </view>
    
    <!-- 验证码输入 -->
    <view class="input-group">
      <text class="label">验证码</text>
      <view class="input-box">
        <input type="number" value="{{code}}" bindinput="inputCode" placeholder="请输入验证码" maxlength="6" />
        <view class="verify-btn {{!isValidPhone() || countdown > 0 ? 'disabled' : ''}}" bindtap="getVerificationCode">
          <text>{{codeText}}</text>
        </view>
      </view>
    </view>
    
    <!-- 新密码输入 -->
    <view class="input-group">
      <text class="label">新密码</text>
      <view class="input-box">
        <input type="{{showPassword ? 'text' : 'password'}}" value="{{password}}" bindinput="inputPassword" placeholder="请设置6-20位新密码" />
        <view class="eye-icon" bindtap="togglePasswordVisibility">
          <image src="{{showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'}}" mode="aspectFit"></image>
        </view>
      </view>
    </view>
    
    <!-- 确认密码输入 -->
    <view class="input-group">
      <text class="label">确认密码</text>
      <view class="input-box">
        <input type="{{showConfirmPassword ? 'text' : 'password'}}" value="{{confirmPassword}}" bindinput="inputConfirmPassword" placeholder="请再次输入新密码" />
        <view class="eye-icon" bindtap="toggleConfirmPasswordVisibility">
          <image src="{{showConfirmPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'}}" mode="aspectFit"></image>
        </view>
      </view>
    </view>
    
    <!-- 重置按钮 -->
    <view class="reset-btn {{!canReset || loading ? 'disabled' : ''}}" bindtap="resetPassword">
      <text>重置密码</text>
    </view>
    
    <!-- 返回登录 -->
    <view class="back-login" bindtap="navigateToLogin">
      <text>返回登录</text>
    </view>
  </view>
</view> 