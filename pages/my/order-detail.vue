<template>
	<view class="order-detail-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">订单详情</text>
			<view class="right-btn" @tap="contactService">
				<image src="/static/icons/service.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 订单状态 -->
		<view class="status-section">
			<view class="status-content">
				<text class="status-text">{{getStatusText(orderInfo.status)}}</text>
				<text class="status-desc">{{getStatusDesc(orderInfo.status)}}</text>
			</view>
		</view>
		
		<!-- 配送信息 -->
		<view class="delivery-section" v-if="orderInfo.status !== 'pending' && orderInfo.status !== 'cancelled'">
			<view class="section-title">
				<text>配送信息</text>
			</view>
			<view class="delivery-info">
				<view class="info-item">
					<text class="label">配送方式：</text>
					<text class="value">{{orderInfo.deliveryMethod}}</text>
				</view>
				<view class="info-item">
					<text class="label">物流单号：</text>
					<text class="value">{{orderInfo.trackingNumber}}</text>
					<view class="copy-btn" @tap="copyTrackingNumber">
						<text>复制</text>
					</view>
				</view>
				<view class="info-item">
					<text class="label">收货地址：</text>
					<text class="value">{{orderInfo.address}}</text>
				</view>
				<view class="info-item">
					<text class="label">收货人：</text>
					<text class="value">{{orderInfo.receiver}} {{orderInfo.phone}}</text>
				</view>
			</view>
		</view>
		
		<!-- 商品信息 -->
		<view class="product-section">
			<view class="section-title">
				<text>商品信息</text>
			</view>
			<view class="store-info">
				<image src="/static/icons/store.png" mode="aspectFit" class="store-icon"></image>
				<text class="store-name">{{orderInfo.storeName}}</text>
				<image src="/static/icons/right.png" mode="aspectFit" class="right-icon"></image>
			</view>
			<view class="product-info">
				<image :src="orderInfo.productImage" mode="aspectFill" class="product-image"></image>
				<view class="product-details">
					<text class="product-name">{{orderInfo.productName}}</text>
					<text class="product-spec">{{orderInfo.productSpec}}</text>
					<view class="price-quantity">
						<text class="price">¥{{orderInfo.price}}</text>
						<text class="quantity">x{{orderInfo.quantity}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 价格信息 -->
		<view class="price-section">
			<view class="section-title">
				<text>价格信息</text>
			</view>
			<view class="price-info">
				<view class="price-item">
					<text class="label">商品金额</text>
					<text class="value">¥{{orderInfo.price * orderInfo.quantity}}</text>
				</view>
				<view class="price-item">
					<text class="label">运费</text>
					<text class="value">¥{{orderInfo.shippingFee}}</text>
				</view>
				<view class="price-item" v-if="orderInfo.discount > 0">
					<text class="label">优惠</text>
					<text class="value discount">-¥{{orderInfo.discount}}</text>
				</view>
				<view class="price-item total">
					<text class="label">实付款</text>
					<text class="value total-value">¥{{orderInfo.totalAmount}}</text>
				</view>
			</view>
		</view>
		
		<!-- 订单信息 -->
		<view class="order-info-section">
			<view class="section-title">
				<text>订单信息</text>
			</view>
			<view class="order-info">
				<view class="order-item">
					<text class="label">订单编号：</text>
					<text class="value">{{orderInfo.orderNumber}}</text>
					<view class="copy-btn" @tap="copyOrderNumber">
						<text>复制</text>
					</view>
				</view>
				<view class="order-item">
					<text class="label">下单时间：</text>
					<text class="value">{{formatTime(orderInfo.orderTime)}}</text>
				</view>
				<view class="order-item">
					<text class="label">支付方式：</text>
					<text class="value">{{orderInfo.paymentMethod}}</text>
				</view>
				<view class="order-item">
					<text class="label">发票信息：</text>
					<text class="value">{{orderInfo.invoice ? orderInfo.invoice : '不开发票'}}</text>
				</view>
				<view class="order-item">
					<text class="label">订单备注：</text>
					<text class="value">{{orderInfo.remark ? orderInfo.remark : '无'}}</text>
				</view>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-action-bar">
			<view 
				v-for="(action, index) in getActions(orderInfo.status)" 
				:key="index"
				:class="['action-btn', action.primary ? 'primary-btn' : '']"
				@tap="handleAction(action.type)"
			>
				<text>{{action.text}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				orderId: '',
				orderInfo: {
					id: '',
					orderNumber: '',
					status: '',
					storeName: '',
					productName: '',
					productSpec: '',
					productImage: '',
					price: 0,
					quantity: 0,
					shippingFee: 0,
					discount: 0,
					totalAmount: 0,
					orderTime: 0,
					paymentMethod: '',
					deliveryMethod: '',
					trackingNumber: '',
					address: '',
					receiver: '',
					phone: '',
					invoice: '',
					remark: ''
				}
			}
		},
		onLoad(options) {
			if (options.id) {
				this.orderId = options.id;
				this.loadOrderDetail();
			}
		},
		methods: {
			// 加载订单详情
			loadOrderDetail() {
				// 显示加载提示
				uni.showLoading({
					title: '加载中...'
				});
				
				// 先尝试从云数据库加载
				this.loadOrderFromCloud();
			},
			
			// 从云数据库加载订单数据
			loadOrderFromCloud() {
				wx.cloud.callFunction({
					name: 'getOrderDetail',
					data: { 
						orderId: this.orderId 
					}
				}).then(res => {
					uni.hideLoading();
					
					const result = res.result;
					if (result.code === 0 && result.data) {
						this.orderInfo = this.formatOrderData(result.data);
					} else {
						// 云函数加载失败，尝试从本地存储获取
						this.loadOrderFromStorage();
					}
				}).catch(err => {
					console.error('从云数据库加载订单失败', err);
					uni.hideLoading();
					
					// 加载失败，尝试从本地存储获取
					this.loadOrderFromStorage();
				});
			},
			
			// 从本地存储加载订单数据（备用方案）
			loadOrderFromStorage() {
				// 从本地存储获取订单数据
				const ordersStorage = uni.getStorageSync('user_orders');
				if (ordersStorage) {
					const orderList = JSON.parse(ordersStorage);
					const order = orderList.find(item => item.id === this.orderId);
					
					if (order) {
						// 补充订单详情数据
						this.orderInfo = this.formatOrderData(order);
					} else {
						uni.showToast({
							title: '订单不存在',
							icon: 'none'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				} else {
					uni.showToast({
						title: '订单不存在',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}
			},
			
			// 格式化订单数据
			formatOrderData(order) {
				return {
					...order,
					orderNumber: order.order_no || ('DD' + new Date(order.orderTime || Date.now()).getTime()),
					shippingFee: order.shipping_fee || 10,
					discount: order.discount || 0,
					paymentMethod: order.pay_method || '微信支付',
					deliveryMethod: order.delivery_method || '顺丰速运',
					trackingNumber: order.tracking_number || ('SF' + Math.floor(Math.random() * 1000000000)),
					address: order.address || '浙江省杭州市西湖区文三路443号',
					receiver: order.receiver || '张三',
					phone: order.phone || '138****1234',
					invoice: order.invoice || '',
					remark: order.remark || ''
				};
			},
			// 获取订单状态文本
			getStatusText(status) {
				switch (status) {
					case 'pending':
						return '待付款';
					case 'processing':
						return '处理中';
					case 'shipping':
						return '配送中';
					case 'delivered':
						return '待评价';
					case 'completed':
						return '已完成';
					case 'cancelled':
						return '已取消';
					default:
						return '未知状态';
				}
			},
			// 获取订单状态描述
			getStatusDesc(status) {
				switch (status) {
					case 'pending':
						return '请在30分钟内完成支付，超时订单将自动取消';
					case 'processing':
						return '商家正在处理您的订单，请耐心等待';
					case 'shipping':
						return '商品已发货，请注意查收';
					case 'delivered':
						return '商品已送达，请及时确认收货并评价';
					case 'completed':
						return '订单已完成，感谢您的购买';
					case 'cancelled':
						return '订单已取消';
					default:
						return '';
				}
			},
			// 获取订单操作按钮
			getActions(status) {
				switch (status) {
					case 'pending':
						return [
							{ text: '取消订单', type: 'cancel' },
							{ text: '立即付款', type: 'pay', primary: true }
						];
					case 'processing':
						return [
							{ text: '查看物流', type: 'logistics' },
							{ text: '联系卖家', type: 'contact' }
						];
					case 'shipping':
						return [
							{ text: '查看物流', type: 'logistics' },
							{ text: '确认收货', type: 'confirm', primary: true }
						];
					case 'delivered':
						return [
							{ text: '申请售后', type: 'refund' },
							{ text: '立即评价', type: 'review', primary: true }
						];
					case 'completed':
						return [
							{ text: '申请售后', type: 'refund' },
							{ text: '再次购买', type: 'rebuy', primary: true }
						];
					case 'cancelled':
						return [
							{ text: '删除订单', type: 'delete' },
							{ text: '再次购买', type: 'rebuy', primary: true }
						];
					default:
						return [];
				}
			},
			// 处理订单操作
			handleAction(type) {
				switch (type) {
					case 'cancel':
						this.cancelOrder();
						break;
					case 'pay':
						this.payOrder();
						break;
					case 'logistics':
						this.viewLogistics();
						break;
					case 'contact':
						this.contactSeller();
						break;
					case 'confirm':
						this.confirmReceipt();
						break;
					case 'refund':
						this.requestRefund();
						break;
					case 'review':
						this.reviewOrder();
						break;
					case 'rebuy':
						this.rebuyOrder();
						break;
					case 'delete':
						this.deleteOrder();
						break;
				}
			},
			// 取消订单
			cancelOrder() {
				uni.showModal({
					title: '提示',
					content: '确定要取消此订单吗？',
					success: (res) => {
						if (res.confirm) {
							// 更新订单状态
							this.updateOrderStatus('cancelled');
							uni.showToast({
								title: '订单已取消',
								icon: 'success'
							});
							
							// 延时返回上一页
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						}
					}
				});
			},
			// 支付订单
			payOrder() {
				uni.showLoading({
					title: '发起支付...'
				});
				
				// 调用云函数发起支付
				wx.cloud.callFunction({
					name: 'payOrder',
					data: { 
						orderId: this.orderId 
					}
				}).then(res => {
					uni.hideLoading();
					
					const result = res.result;
					
					if (result.code === 0) {
						// 调用微信支付API
						uni.requestPayment({
							...result.data,
							success: () => {
								// 支付成功，刷新订单状态
								this.loadOrderDetail();
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								});
							},
							fail: (err) => {
								console.error('支付失败', err);
								
								// 判断是用户取消还是其他原因
								if (err.errMsg === 'requestPayment:fail cancel') {
									uni.showToast({
										title: '支付已取消',
										icon: 'none'
									});
								} else {
									uni.showToast({
										title: '支付失败，请重试',
										icon: 'none'
									});
								}
							}
						});
					} else {
						uni.showToast({
							title: result.msg || '创建支付失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('调用支付云函数失败', err);
					uni.showToast({
						title: '支付异常，请重试',
						icon: 'none'
					});
				});
			},
			// 查看物流
			viewLogistics() {
				uni.showToast({
					title: '正在查询物流信息',
					icon: 'none'
				});
			},
			// 联系卖家
			contactSeller() {
				uni.showToast({
					title: '正在连接卖家...',
					icon: 'none'
				});
			},
			// 确认收货
			confirmReceipt() {
				uni.showModal({
					title: '提示',
					content: '确认已收到商品吗？',
					success: (res) => {
						if (res.confirm) {
							// 更新订单状态
							this.updateOrderStatus('delivered');
							uni.showToast({
								title: '已确认收货',
								icon: 'success'
							});
						}
					}
				});
			},
			// 申请售后
			requestRefund() {
				uni.showToast({
					title: '售后功能开发中',
					icon: 'none'
				});
			},
			// 评价订单
			reviewOrder() {
				uni.navigateTo({
					url: '/pages/my/review?orderId=' + this.orderId
				});
			},
			// 再次购买
			rebuyOrder() {
				uni.showToast({
					title: '已加入购物车',
					icon: 'success'
				});
			},
			// 删除订单
			deleteOrder() {
				uni.showModal({
					title: '提示',
					content: '确定要删除此订单吗？删除后无法恢复',
					success: (res) => {
						if (res.confirm) {
							// 从本地存储中删除订单
							const ordersStorage = uni.getStorageSync('user_orders');
							if (ordersStorage) {
								let orderList = JSON.parse(ordersStorage);
								orderList = orderList.filter(item => item.id !== this.orderId);
								uni.setStorageSync('user_orders', JSON.stringify(orderList));
							}
							
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
							
							// 延时返回上一页
							setTimeout(() => {
								uni.navigateBack();
							}, 1500);
						}
					}
				});
			},
			// 更新订单状态
			updateOrderStatus(status) {
				// 更新本地数据
				this.orderInfo.status = status;
				
				// 更新本地存储中的订单状态
				const ordersStorage = uni.getStorageSync('user_orders');
				if (ordersStorage) {
					let orderList = JSON.parse(ordersStorage);
					const index = orderList.findIndex(item => item.id === this.orderId);
					if (index !== -1) {
						orderList[index].status = status;
						uni.setStorageSync('user_orders', JSON.stringify(orderList));
					}
				}
			},
			// 复制订单号
			copyOrderNumber() {
				uni.setClipboardData({
					data: this.orderInfo.orderNumber,
					success: () => {
						uni.showToast({
							title: '订单号已复制',
							icon: 'success'
						});
					}
				});
			},
			// 复制物流单号
			copyTrackingNumber() {
				uni.setClipboardData({
					data: this.orderInfo.trackingNumber,
					success: () => {
						uni.showToast({
							title: '物流单号已复制',
							icon: 'success'
						});
					}
				});
			},
			// 联系客服
			contactService() {
				uni.showToast({
					title: '正在连接客服...',
					icon: 'none'
				});
			},
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			// 格式化时间
			formatTime(timestamp) {
				const date = new Date(timestamp);
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				const day = date.getDate().toString().padStart(2, '0');
				const hour = date.getHours().toString().padStart(2, '0');
				const minute = date.getMinutes().toString().padStart(2, '0');
				const second = date.getSeconds().toString().padStart(2, '0');
				
				return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
			}
		}
	}
</script>

<style>
	.order-detail-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn, .right-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image, .right-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.status-section {
		background-color: #ff5a5f;
		padding: 40rpx 30rpx;
		color: #ffffff;
	}
	
	.status-text {
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
		display: block;
	}
	
	.status-desc {
		font-size: 28rpx;
		opacity: 0.9;
	}
	
	.delivery-section, .product-section, .price-section, .order-info-section {
		margin-top: 20rpx;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.delivery-info, .order-info {
		
	}
	
	.info-item, .order-item {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
	}
	
	.info-item:last-child, .order-item:last-child {
		margin-bottom: 0;
	}
	
	.label {
		font-size: 28rpx;
		color: #999999;
		width: 160rpx;
	}
	
	.value {
		font-size: 28rpx;
		color: #333333;
		flex: 1;
	}
	
	.copy-btn {
		padding: 6rpx 16rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		margin-left: 20rpx;
	}
	
	.copy-btn text {
		font-size: 24rpx;
		color: #666666;
	}
	
	.store-info {
		display: flex;
		align-items: center;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.store-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
	}
	
	.store-name {
		font-size: 28rpx;
		color: #333333;
		flex: 1;
	}
	
	.right-icon {
		width: 24rpx;
		height: 24rpx;
	}
	
	.product-info {
		display: flex;
		padding: 20rpx 0;
	}
	
	.product-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
	}
	
	.product-details {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
	
	.product-name {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 10rpx;
	}
	
	.product-spec {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 20rpx;
	}
	
	.price-quantity {
		display: flex;
		justify-content: space-between;
	}
	
	.price {
		font-size: 28rpx;
		color: #ff5a5f;
	}
	
	.quantity {
		font-size: 26rpx;
		color: #999999;
	}
	
	.price-info {
		
	}
	
	.price-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 16rpx;
	}
	
	.price-item:last-child {
		margin-bottom: 0;
	}
	
	.price-item .label {
		width: auto;
		flex: none;
	}
	
	.price-item .value {
		flex: none;
	}
	
	.discount {
		color: #ff5a5f;
	}
	
	.total {
		margin-top: 20rpx;
		padding-top: 20rpx;
		border-top: 1rpx solid #f5f5f5;
	}
	
	.total .label, .total .value {
		font-weight: bold;
	}
	
	.total-value {
		color: #ff5a5f;
		font-size: 32rpx;
	}
	
	.bottom-action-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #ffffff;
		border-top: 1rpx solid #eeeeee;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding: 0 30rpx;
	}
	
	.action-btn {
		padding: 12rpx 30rpx;
		border-radius: 40rpx;
		border: 1rpx solid #ccc;
		margin-left: 20rpx;
	}
	
	.action-btn text {
		font-size: 28rpx;
		color: #666666;
	}
	
	.primary-btn {
		background-color: #ff5a5f;
		border: 1rpx solid #ff5a5f;
	}
	
	.primary-btn text {
		color: #ffffff;
	}
</style> 