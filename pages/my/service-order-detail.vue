<template>
	<view class="order-detail-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">订单详情</text>
		</view>
		
		<!-- 订单状态 -->
		<view class="status-card">
			<view class="status-header" :class="orderDetail.status">
				<text class="status-text">{{getStatusText(orderDetail.status)}}</text>
			</view>
			<view class="status-info" v-if="orderDetail.status === 'pending'">
				<text class="info-text">待接单状态，请尽快确认是否接单</text>
			</view>
			<view class="status-info" v-else-if="orderDetail.status === 'processing'">
				<text class="info-text">已接单，请按时到岗完成工作</text>
			</view>
			<view class="status-info" v-else-if="orderDetail.status === 'completed'">
				<text class="info-text">工作已完成，感谢您的辛勤付出</text>
			</view>
		</view>
		
		<!-- 驿站信息 -->
		<view class="info-card">
			<view class="station-header">
				<image :src="orderDetail.stationImage" mode="aspectFill" class="station-image"></image>
				<view class="station-info">
					<text class="station-name">{{orderDetail.stationName}}</text>
					<text class="station-address">{{orderDetail.stationAddress}}</text>
				</view>
			</view>
			
			<view class="divider"></view>
			
			<!-- 工作详情 -->
			<view class="work-info">
				<view class="info-row">
					<text class="info-label">工作时间</text>
					<text class="info-value">{{orderDetail.workTime}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">时薪</text>
					<text class="info-value">{{orderDetail.hourlyRate}}元/小时</text>
				</view>
				<view class="info-row">
					<text class="info-label">工作时长</text>
					<text class="info-value">{{orderDetail.duration}}小时</text>
				</view>
				<view class="info-row">
					<text class="info-label">预计收入</text>
					<text class="info-value highlight">{{orderDetail.totalAmount}}元</text>
				</view>
			</view>
		</view>
		
		<!-- 工作内容 -->
		<view class="info-card">
			<view class="card-title">工作内容</view>
			<view class="content-text">
				{{orderDetail.workContent || '暂无工作内容描述'}}
			</view>
		</view>
		
		<!-- 任职要求 -->
		<view class="info-card">
			<view class="card-title">任职要求</view>
			<view class="content-text">
				{{orderDetail.requirements || '暂无特殊要求'}}
			</view>
		</view>
		
		<!-- 订单信息 -->
		<view class="info-card">
			<view class="card-title">订单信息</view>
			<view class="order-info">
				<view class="info-row">
					<text class="info-label">订单编号</text>
					<text class="info-value">{{orderDetail.orderId}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">发布时间</text>
					<text class="info-value">{{formatDate(orderDetail.createTime)}}</text>
				</view>
				<view class="info-row" v-if="orderDetail.acceptTime">
					<text class="info-label">接单时间</text>
					<text class="info-value">{{formatDate(orderDetail.acceptTime)}}</text>
				</view>
				<view class="info-row" v-if="orderDetail.completeTime">
					<text class="info-label">完成时间</text>
					<text class="info-value">{{formatDate(orderDetail.completeTime)}}</text>
				</view>
			</view>
		</view>
		
		<!-- 联系信息 -->
		<view class="info-card">
			<view class="card-title">联系信息</view>
			<view class="contact-info">
				<view class="info-row">
					<text class="info-label">联系人</text>
					<text class="info-value">{{orderDetail.stationContact}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">联系电话</text>
					<text class="info-value">{{orderDetail.stationPhone}}</text>
					<view class="copy-btn" @tap="copyPhone">复制</view>
				</view>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-btns" v-if="orderDetail.status === 'pending'">
			<view class="btn cancel-btn" @tap="showCancelModal">取消</view>
			<view class="btn contact-btn" @tap="contactStation">立即联系</view>
			<view class="btn confirm-btn" @tap="confirmOrder">立即接单</view>
		</view>
		
		<view class="bottom-btns" v-else-if="orderDetail.status === 'processing'">
			<view class="btn cancel-btn" @tap="showCancelModal">取消接单</view>
			<view class="btn contact-btn" @tap="contactStation">联系驿站</view>
			<view class="btn confirm-btn" @tap="completeOrder">确认完成</view>
		</view>
		
		<view class="bottom-btns" v-else-if="orderDetail.status === 'completed'">
			<view class="btn wide-btn" @tap="navigateToList">查看更多顶班机会</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				orderDetail: {}
			}
		},
		onLoad() {
			this.loadOrderDetail();
		},
		methods: {
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			
			// 加载订单详情
			loadOrderDetail() {
				const currentOrder = uni.getStorageSync('currentOrder');
				if (currentOrder) {
					this.orderDetail = JSON.parse(currentOrder);
					
					// 确保日期对象正确
					if (typeof this.orderDetail.createTime === 'string') {
						this.orderDetail.createTime = new Date(this.orderDetail.createTime);
					}
					if (this.orderDetail.acceptTime && typeof this.orderDetail.acceptTime === 'string') {
						this.orderDetail.acceptTime = new Date(this.orderDetail.acceptTime);
					}
					if (this.orderDetail.completeTime && typeof this.orderDetail.completeTime === 'string') {
						this.orderDetail.completeTime = new Date(this.orderDetail.completeTime);
					}
				} else {
					uni.showToast({
						title: '订单详情加载失败',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}
			},
			
			// 获取状态文本
			getStatusText(status) {
				switch (status) {
					case 'pending':
						return '待接单';
					case 'processing':
						return '进行中';
					case 'completed':
						return '已完成';
					case 'cancelled':
						return '已取消';
					default:
						return '未知状态';
				}
			},
			
			// 格式化日期
			formatDate(date) {
				if (!date) return '';
				
				const d = new Date(date);
				const year = d.getFullYear();
				const month = String(d.getMonth() + 1).padStart(2, '0');
				const day = String(d.getDate()).padStart(2, '0');
				const hours = String(d.getHours()).padStart(2, '0');
				const minutes = String(d.getMinutes()).padStart(2, '0');
				
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			},
			
			// 复制电话号码
			copyPhone() {
				uni.setClipboardData({
					data: this.orderDetail.stationPhone,
					success: () => {
						uni.showToast({
							title: '电话号码已复制',
							icon: 'success'
						});
					}
				});
			},
			
			// 联系驿站
			contactStation() {
				uni.showActionSheet({
					itemList: ['拨打电话', '复制微信号'],
					success: (res) => {
						if (res.tapIndex === 0) {
							// 拨打电话
							uni.makePhoneCall({
								phoneNumber: this.orderDetail.stationPhone,
								fail: () => {
									uni.showToast({
										title: '拨号已取消',
										icon: 'none'
									});
								}
							});
						} else if (res.tapIndex === 1) {
							// 复制微信号
							uni.setClipboardData({
								data: this.orderDetail.stationPhone,
								success: () => {
									uni.showToast({
										title: '微信号已复制',
										icon: 'success'
									});
								}
							});
						}
					}
				});
			},
			
			// 显示取消订单弹窗
			showCancelModal() {
				uni.showModal({
					title: '确认取消',
					content: '您确定要取消此订单吗？',
					confirmColor: '#ff5a5f',
					success: (res) => {
						if (res.confirm) {
							this.cancelOrder();
						}
					}
				});
			},
			
			// 取消订单
			cancelOrder() {
				// 获取所有订单
				const orders = uni.getStorageSync('serviceOrders');
				if (orders) {
					let orderList = JSON.parse(orders);
					const index = orderList.findIndex(order => order.orderId === this.orderDetail.orderId);
					
					if (index !== -1) {
						// 更新订单状态为已取消
						orderList[index].status = 'cancelled';
						// 保存回本地存储
						uni.setStorageSync('serviceOrders', JSON.stringify(orderList));
						
						// 更新当前订单状态
						this.orderDetail.status = 'cancelled';
						uni.setStorageSync('currentOrder', JSON.stringify(this.orderDetail));
						
						uni.showToast({
							title: '订单已取消',
							icon: 'success'
						});
						
						// 1.5秒后返回上一页
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				}
			},
			
			// 确认接单
			confirmOrder() {
				if (this.orderDetail.status !== 'pending') {
					uni.showToast({
						title: '该订单不可接',
						icon: 'none'
					});
					return;
				}
				
				uni.showModal({
					title: '确认接单',
					content: `您确定要接${this.orderDetail.stationName}的顶班订单吗？`,
					confirmColor: '#ff5a5f',
					success: (res) => {
						if (res.confirm) {
							// 获取所有订单
							const orders = uni.getStorageSync('serviceOrders');
							if (orders) {
								let orderList = JSON.parse(orders);
								const index = orderList.findIndex(order => order.orderId === this.orderDetail.orderId);
								
								if (index !== -1) {
									// 更新订单状态为进行中
									orderList[index].status = 'processing';
									orderList[index].acceptTime = new Date();
									// 保存回本地存储
									uni.setStorageSync('serviceOrders', JSON.stringify(orderList));
									
									// 更新当前订单状态
									this.orderDetail.status = 'processing';
									this.orderDetail.acceptTime = new Date();
									uni.setStorageSync('currentOrder', JSON.stringify(this.orderDetail));
									
									uni.showToast({
										title: '接单成功',
										icon: 'success'
									});
								}
							}
						}
					}
				});
			},
			
			// 确认完成
			completeOrder() {
				if (this.orderDetail.status !== 'processing') {
					uni.showToast({
						title: '无法完成此订单',
						icon: 'none'
					});
					return;
				}
				
				uni.showModal({
					title: '确认完成',
					content: '您确定已经完成此订单的工作内容吗？',
					confirmColor: '#ff5a5f',
					success: (res) => {
						if (res.confirm) {
							// 获取所有订单
							const orders = uni.getStorageSync('serviceOrders');
							if (orders) {
								let orderList = JSON.parse(orders);
								const index = orderList.findIndex(order => order.orderId === this.orderDetail.orderId);
								
								if (index !== -1) {
									// 更新订单状态为已完成
									orderList[index].status = 'completed';
									orderList[index].completeTime = new Date();
									// 保存回本地存储
									uni.setStorageSync('serviceOrders', JSON.stringify(orderList));
									
									// 更新当前订单状态
									this.orderDetail.status = 'completed';
									this.orderDetail.completeTime = new Date();
									uni.setStorageSync('currentOrder', JSON.stringify(this.orderDetail));
									
									uni.showToast({
										title: '工作已完成',
										icon: 'success'
									});
								}
							}
						}
					}
				});
			},
			
			// 导航到顶班接单列表
			navigateToList() {
				uni.navigateBack({
					delta: 1
				});
			}
		}
	}
</script>

<style>
	.order-detail-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		height: 90rpx;
		padding: 0 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.status-card {
		margin: 20rpx;
		border-radius: 12rpx;
		overflow: hidden;
		background-color: #ffffff;
	}
	
	.status-header {
		padding: 30rpx;
		background-color: #ff9500;
	}
	
	.status-header.pending {
		background-color: #ff9500;
	}
	
	.status-header.processing {
		background-color: #007aff;
	}
	
	.status-header.completed {
		background-color: #34c759;
	}
	
	.status-header.cancelled {
		background-color: #8e8e93;
	}
	
	.status-text {
		font-size: 36rpx;
		font-weight: bold;
		color: #ffffff;
	}
	
	.status-info {
		padding: 20rpx 30rpx;
		background-color: rgba(255, 255, 255, 0.8);
	}
	
	.info-text {
		font-size: 28rpx;
		color: #666666;
	}
	
	.info-card {
		margin: 20rpx;
		padding: 30rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
	}
	
	.station-header {
		display: flex;
		margin-bottom: 30rpx;
	}
	
	.station-image {
		width: 120rpx;
		height: 120rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
		background-color: #f0f0f0;
	}
	
	.station-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}
	
	.station-name {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
	}
	
	.station-address {
		font-size: 28rpx;
		color: #666666;
	}
	
	.divider {
		height: 1rpx;
		background-color: #f0f0f0;
		margin: 20rpx 0;
	}
	
	.work-info {
		padding-top: 10rpx;
	}
	
	.info-row {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.info-label {
		width: 160rpx;
		font-size: 28rpx;
		color: #999999;
	}
	
	.info-value {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
	}
	
	.info-value.highlight {
		color: #ff5a5f;
		font-weight: bold;
		font-size: 32rpx;
	}
	
	.card-title {
		font-size: a2rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
		position: relative;
		padding-left: 20rpx;
	}
	
	.card-title::before {
		content: '';
		position: absolute;
		left: 0;
		top: 8rpx;
		width: 6rpx;
		height: 28rpx;
		background-color: #ff5a5f;
		border-radius: 3rpx;
	}
	
	.content-text {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.6;
	}
	
	.copy-btn {
		width: 80rpx;
		height: 50rpx;
		line-height: 50rpx;
		text-align: center;
		font-size: 24rpx;
		color: #007aff;
		border: 1rpx solid #007aff;
		border-radius: 25rpx;
		margin-left: 20rpx;
	}
	
	.bottom-btns {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		height: 100rpx;
		background-color: #ffffff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.btn {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		font-size: 32rpx;
	}
	
	.cancel-btn {
		color: #666666;
		background-color: #ffffff;
	}
	
	.contact-btn {
		color: #ff9500;
		background-color: #ffffff;
	}
	
	.confirm-btn {
		color: #ffffff;
		background-color: #ff5a5f;
	}
	
	.wide-btn {
		color: #ffffff;
		background-color: #ff5a5f;
	}
</style> 