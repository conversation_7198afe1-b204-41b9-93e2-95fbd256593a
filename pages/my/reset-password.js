import { checkPhoneExists, sendSmsCode, resetPassword } from '../../api/auth';

Page({
  data: {
    loading: false,
    phoneNumber: '',
    code: '',
    password: '',
    confirmPassword: '',
    showPassword: false,
    showConfirmPassword: false,
    countdown: 0,
    codeText: '获取验证码',
    timer: null
  },
  
  // 输入手机号
  inputPhoneNumber(e) {
    this.setData({
      phoneNumber: e.detail.value
    });
  },
  
  // 输入验证码
  inputCode(e) {
    this.setData({
      code: e.detail.value
    });
  },
  
  // 输入密码
  inputPassword(e) {
    this.setData({
      password: e.detail.value
    });
  },
  
  // 输入确认密码
  inputConfirmPassword(e) {
    this.setData({
      confirmPassword: e.detail.value
    });
  },
  
  // 切换密码显示状态
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },
  
  // 切换确认密码显示状态
  toggleConfirmPasswordVisibility() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword
    });
  },
  
  // 检查手机号是否有效
  isValidPhone() {
    return /^1[3-9]\d{9}$/.test(this.data.phoneNumber);
  },
  
  // 检查是否可以重置密码
  get canReset() {
    const { phoneNumber, code, password, confirmPassword } = this.data;
    return this.isValidPhone() && 
           code.length === 6 && 
           password.length >= 6 && 
           password === confirmPassword;
  },
  
  // 获取验证码
  async getVerificationCode() {
    const { phoneNumber } = this.data;
    
    if (!this.isValidPhone()) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.countdown > 0) {
      return;
    }
    
    this.setData({ loading: true });
    
    try {
      // 检查手机号是否已注册
      const checkResult = await checkPhoneExists(phoneNumber);
      
      if (!checkResult.data.exists) {
        wx.showToast({
          title: '该手机号未注册，请先注册',
          icon: 'none'
        });
        return;
      }
      
      // 发送验证码
      const result = await sendSmsCode(phoneNumber, 'resetPassword');
      
      if (result.code === 0) {
        wx.showToast({
          title: '验证码已发送',
          icon: 'success'
        });
        
        // 开始倒计时
        this.setData({
          countdown: 60,
          codeText: '60秒'
        });
        
        this.data.timer = setInterval(() => {
          const countdown = this.data.countdown - 1;
          this.setData({
            countdown,
            codeText: countdown + '秒'
          });
          
          if (countdown === 0) {
            clearInterval(this.data.timer);
            this.setData({
              codeText: '获取验证码'
            });
          }
        }, 1000);
      } else {
        wx.showToast({
          title: result.message || '发送失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('发送验证码失败', error);
      wx.showToast({
        title: '发送失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },
  
  // 重置密码
  async resetPassword() {
    if (!this.canReset || this.data.loading) return;
    
    const { phoneNumber, code, password, confirmPassword } = this.data;
    
    if (password !== confirmPassword) {
      wx.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ loading: true });
    wx.showLoading({
      title: '重置中...'
    });
    
    try {
      const result = await resetPassword(phoneNumber, code, password);
      
      if (result.code === 0) {
        wx.showToast({
          title: '密码重置成功',
          icon: 'success'
        });
        
        setTimeout(() => {
          this.navigateToLogin();
        }, 1500);
      } else {
        wx.showToast({
          title: result.message || '重置失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('重置密码失败', error);
      wx.showToast({
        title: '重置失败，请检查网络并重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
      this.setData({ loading: false });
    }
  },
  
  // 跳转到登录页面
  navigateToLogin() {
    wx.navigateBack();
  },
  
  onUnload() {
    // 页面销毁时清除定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  }
}); 