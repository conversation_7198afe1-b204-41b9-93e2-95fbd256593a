/* pages/my/test-login.wxss */
.container {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  box-sizing: border-box;
}

.header {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
}

.server-info, .status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 26rpx;
}

.main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.result-panel, .log-panel {
  background-color: #f8f8f8;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.panel-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.result-content {
  font-size: 26rpx;
}

.log-content {
  height: 400rpx;
  font-size: 24rpx;
}

.log-item {
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
}

button {
  margin: 0;
} 