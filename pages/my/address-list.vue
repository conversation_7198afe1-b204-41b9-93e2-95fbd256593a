<template>
	<view class="address-list-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">收货地址</text>
		</view>
		
		<!-- 地址列表 -->
		<view class="address-list">
			<view class="address-item" v-for="(item, index) in addressList" :key="index">
				<view class="address-info" @tap="selectAddress(item)">
					<view class="user-info">
						<text class="name">{{item.name}}</text>
						<text class="phone">{{formatPhone(item.phone)}}</text>
						<text class="default-tag" v-if="item.isDefault">默认</text>
					</view>
					<text class="address-text">{{item.province}}{{item.city}}{{item.district}}{{item.address}}</text>
				</view>
				<view class="address-actions">
					<view class="action-btn" @tap="editAddress(item)">
						<image src="/static/icons/edit.png" mode="aspectFit"></image>
						<text>编辑</text>
					</view>
					<view class="action-btn" @tap="deleteAddress(index)">
						<image src="/static/icons/delete.png" mode="aspectFit"></image>
						<text>删除</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-state" v-if="addressList.length === 0">
			<image src="/static/icons/address-empty.png" mode="aspectFit" class="empty-icon"></image>
			<text class="empty-text">您还没有添加收货地址</text>
		</view>
		
		<!-- 添加地址按钮 -->
		<view class="add-address-btn" @tap="goToAddAddress">
			<text>添加新地址</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				addressList: []
			}
		},
		onShow() {
			// 每次显示页面时重新加载地址列表
			this.loadAddressList();
		},
		methods: {
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			
			// 加载地址列表
			loadAddressList() {
				const addressStorage = uni.getStorageSync('address_list');
				if (addressStorage) {
					this.addressList = JSON.parse(addressStorage);
				} else {
					// 没有地址数据，设置为空数组
					this.addressList = [];
					uni.setStorageSync('address_list', JSON.stringify(this.addressList));
				}
			},
			
			// 格式化手机号
			formatPhone(phone) {
				if (!phone) return '';
				return phone.substr(0, 3) + '****' + phone.substr(7);
			},
			
			// 选择地址
			selectAddress(address) {
				// 回到上一页并传递选中的地址
				const pages = getCurrentPages();
				if (pages.length > 1) {
					const prevPage = pages[pages.length - 2];
					// 设置上一页面的地址
					if (prevPage.route === 'pages/my/checkout') {
						prevPage.$vm.selectedAddress = address;
					}
					uni.navigateBack();
				}
			},
			
			// 编辑地址
			editAddress(address) {
				uni.navigateTo({
					url: `/pages/my/address-edit?id=${address.id}`
				});
			},
			
			// 删除地址
			deleteAddress(index) {
				uni.showModal({
					title: '删除地址',
					content: '确定要删除该地址吗？',
					success: (res) => {
						if (res.confirm) {
							this.addressList.splice(index, 1);
							// 更新存储
							uni.setStorageSync('address_list', JSON.stringify(this.addressList));
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},
			
			// 跳转到添加地址页面
			goToAddAddress() {
				uni.navigateTo({
					url: '/pages/my/address-edit'
				});
			}
		}
	}
</script>

<style>
	.address-list-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.address-list {
		padding: 20rpx;
	}
	
	.address-item {
		background-color: #ffffff;
		border-radius: 8rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
	}
	
	.address-info {
		padding: 20rpx;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.user-info {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.name {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		margin-right: 15rpx;
	}
	
	.phone {
		font-size: 26rpx;
		color: #666666;
		margin-right: 15rpx;
	}
	
	.default-tag {
		font-size: 22rpx;
		color: #ffffff;
		background-color: #ff5a5f;
		padding: 4rpx 10rpx;
		border-radius: 4rpx;
	}
	
	.address-text {
		font-size: 26rpx;
		color: #333333;
		line-height: 1.4;
	}
	
	.address-actions {
		display: flex;
		border-top: 1rpx solid #f5f5f5;
	}
	
	.action-btn {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx 0;
	}
	
	.action-btn image {
		width: 30rpx;
		height: 30rpx;
		margin-right: 10rpx;
	}
	
	.action-btn text {
		font-size: 26rpx;
		color: #666666;
	}
	
	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
	}
	
	.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}
	
	.add-address-btn {
		position: fixed;
		bottom: 30rpx;
		left: 30rpx;
		right: 30rpx;
		height: 80rpx;
		background-color: #ff5a5f;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.add-address-btn text {
		font-size: 30rpx;
		color: #ffffff;
		font-weight: bold;
	}
</style> 