<template>
	<view class="review-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">评价订单</text>
		</view>
		
		<!-- 商品信息 -->
		<view class="product-section">
			<image :src="orderInfo.productImage" mode="aspectFill" class="product-image"></image>
			<view class="product-details">
				<text class="product-name">{{orderInfo.productName}}</text>
				<text class="product-spec">{{orderInfo.productSpec}}</text>
				<text class="price">¥{{orderInfo.price}}</text>
			</view>
		</view>
		
		<!-- 评分 -->
		<view class="rating-section">
			<text class="section-title">商品评分</text>
			<view class="star-rating">
				<view 
					v-for="index in 5" 
					:key="index" 
					class="star-item"
					@tap="setRating(index)"
				>
					<image 
						:src="index <= rating ? '/static/icons/star-filled.png' : '/static/icons/star-empty.png'"
						mode="aspectFit"
					></image>
				</view>
				<text class="rating-text">{{getRatingText()}}</text>
			</view>
		</view>
		
		<!-- 评价内容 -->
		<view class="content-section">
			<text class="section-title">评价内容</text>
			<textarea 
				class="review-textarea" 
				placeholder="请详细描述您对商品的使用感受，对他人帮助很大哦~" 
				maxlength="500"
				v-model="reviewContent"
				placeholder-style="color: #C8C8C8;"
			></textarea>
			<view class="word-count">
				<text>{{reviewContent.length}}/500</text>
			</view>
		</view>
		
		<!-- 图片上传 -->
		<view class="photo-section">
			<view class="section-header">
				<text class="section-title">添加图片</text>
				<text class="photo-count">{{photoList.length}}/6</text>
			</view>
			<view class="photo-list">
				<view 
					v-for="(item, index) in photoList" 
					:key="index" 
					class="photo-item"
				>
					<image :src="item" mode="aspectFill" class="preview-image"></image>
					<view class="delete-btn" @tap="deletePhoto(index)">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="upload-btn" @tap="choosePhoto" v-if="photoList.length < 6">
					<image src="/static/icons/camera.png" mode="aspectFit"></image>
					<text>添加图片</text>
				</view>
			</view>
		</view>
		
		<!-- 物流服务评分 -->
		<view class="service-section">
			<text class="section-title">物流服务评分</text>
			<view class="star-rating">
				<view 
					v-for="index in 5" 
					:key="index" 
					class="star-item"
					@tap="setLogisticsRating(index)"
				>
					<image 
						:src="index <= logisticsRating ? '/static/icons/star-filled.png' : '/static/icons/star-empty.png'"
						mode="aspectFit"
					></image>
				</view>
			</view>
		</view>
		
		<!-- 商家服务评分 -->
		<view class="service-section">
			<text class="section-title">商家服务评分</text>
			<view class="star-rating">
				<view 
					v-for="index in 5" 
					:key="index" 
					class="star-item"
					@tap="setSellerRating(index)"
				>
					<image 
						:src="index <= sellerRating ? '/static/icons/star-filled.png' : '/static/icons/star-empty.png'"
						mode="aspectFit"
					></image>
				</view>
			</view>
		</view>
		
		<!-- 匿名评价 -->
		<view class="anonymous-section">
			<text class="anonymous-text">匿名评价</text>
			<switch 
				:checked="isAnonymous" 
				@change="toggleAnonymous" 
				color="#ff5a5f"
				style="transform:scale(0.8)"
			/>
		</view>
		
		<!-- 底部按钮 -->
		<view class="bottom-buttons">
			<button class="submit-btn" @tap="submitReview">提交评价</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				orderId: '',
				orderInfo: {
					id: '',
					storeName: '',
					productName: '',
					productSpec: '',
					productImage: '',
					price: 0
				},
				rating: 5,
				logisticsRating: 5,
				sellerRating: 5,
				reviewContent: '',
				photoList: [],
				isAnonymous: false
			}
		},
		onLoad(options) {
			if (options.orderId) {
				this.orderId = options.orderId;
				this.loadOrderInfo();
			}
		},
		methods: {
			// 加载订单信息
			loadOrderInfo() {
				// 从本地存储获取订单数据
				const ordersStorage = uni.getStorageSync('user_orders');
				if (ordersStorage) {
					const orderList = JSON.parse(ordersStorage);
					const order = orderList.find(item => item.id === this.orderId);
					
					if (order) {
						this.orderInfo = {
							id: order.id,
							storeName: order.storeName,
							productName: order.productName,
							productSpec: order.productSpec,
							productImage: order.productImage,
							price: order.price
						};
					} else {
						uni.showToast({
							title: '订单不存在',
							icon: 'none'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					}
				}
			},
			// 设置商品评分
			setRating(value) {
				this.rating = value;
			},
			// 设置物流评分
			setLogisticsRating(value) {
				this.logisticsRating = value;
			},
			// 设置商家评分
			setSellerRating(value) {
				this.sellerRating = value;
			},
			// 获取评分文字描述
			getRatingText() {
				switch(this.rating) {
					case 1:
						return '非常差';
					case 2:
						return '差';
					case 3:
						return '一般';
					case 4:
						return '好';
					case 5:
						return '非常好';
					default:
						return '';
				}
			},
			// 选择照片
			choosePhoto() {
				if (this.photoList.length >= 6) {
					uni.showToast({
						title: '最多上传6张图片',
						icon: 'none'
					});
					return;
				}
				
				uni.chooseImage({
					count: 6 - this.photoList.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						// 模拟上传过程
						uni.showLoading({
							title: '上传中...'
						});
						
						setTimeout(() => {
							uni.hideLoading();
							this.photoList = [...this.photoList, ...res.tempFilePaths];
						}, 1000);
					}
				});
			},
			// 删除照片
			deletePhoto(index) {
				this.photoList.splice(index, 1);
			},
			// 切换匿名评价
			toggleAnonymous(e) {
				this.isAnonymous = e.detail.value;
			},
			// 提交评价
			submitReview() {
				// 验证评价内容
				if (this.reviewContent.trim().length < 5) {
					uni.showToast({
						title: '评价内容不能少于5个字',
						icon: 'none'
					});
					return;
				}
				
				// 模拟提交过程
				uni.showLoading({
					title: '提交中...'
				});
				
				setTimeout(() => {
					uni.hideLoading();
					
					// 更新订单状态为已完成
					this.updateOrderStatus('completed');
					
					// 保存评价信息到本地存储
					this.saveReview();
					
					uni.showToast({
						title: '评价成功',
						icon: 'success'
					});
					
					// 延时返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}, 1500);
			},
			// 更新订单状态
			updateOrderStatus(status) {
				// 更新本地存储中的订单状态
				const ordersStorage = uni.getStorageSync('user_orders');
				if (ordersStorage) {
					let orderList = JSON.parse(ordersStorage);
					const index = orderList.findIndex(item => item.id === this.orderId);
					if (index !== -1) {
						orderList[index].status = status;
						uni.setStorageSync('user_orders', JSON.stringify(orderList));
					}
				}
			},
			// 保存评价信息
			saveReview() {
				const review = {
					id: 'review_' + new Date().getTime(),
					orderId: this.orderId,
					productId: this.orderInfo.id,
					productName: this.orderInfo.productName,
					productImage: this.orderInfo.productImage,
					rating: this.rating,
					logisticsRating: this.logisticsRating,
					sellerRating: this.sellerRating,
					content: this.reviewContent,
					photos: this.photoList,
					isAnonymous: this.isAnonymous,
					userName: this.isAnonymous ? '匿名用户' : '测试用户',
					createTime: new Date().getTime()
				};
				
				// 保存评价到本地存储
				let reviews = uni.getStorageSync('product_reviews');
				if (!reviews) {
					reviews = [];
				} else {
					reviews = JSON.parse(reviews);
				}
				
				reviews.push(review);
				uni.setStorageSync('product_reviews', JSON.stringify(reviews));
			},
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	.review-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
		margin-right: 60rpx;
	}
	
	.product-section {
		display: flex;
		padding: 30rpx;
		background-color: #ffffff;
		margin-bottom: 20rpx;
	}
	
	.product-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
	}
	
	.product-details {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
	
	.product-name {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 10rpx;
	}
	
	.product-spec {
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 20rpx;
	}
	
	.price {
		font-size: 28rpx;
		color: #ff5a5f;
	}
	
	.rating-section, .content-section, .photo-section, .service-section {
		padding: 30rpx;
		background-color: #ffffff;
		margin-bottom: 20rpx;
	}
	
	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
	}
	
	.star-rating {
		display: flex;
		align-items: center;
	}
	
	.star-item {
		margin-right: 20rpx;
	}
	
	.star-item image {
		width: 50rpx;
		height: 50rpx;
	}
	
	.rating-text {
		font-size: 28rpx;
		color: #ff5a5f;
		margin-left: 10rpx;
	}
	
	.review-textarea {
		width: 100%;
		height: 200rpx;
		background-color: #f9f9f9;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		color: #333333;
		box-sizing: border-box;
	}
	
	.word-count {
		text-align: right;
		margin-top: 10rpx;
	}
	
	.word-count text {
		font-size: 24rpx;
		color: #999999;
	}
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.photo-count {
		font-size: 24rpx;
		color: #999999;
	}
	
	.photo-list {
		display: flex;
		flex-wrap: wrap;
		margin-top: 20rpx;
	}
	
	.photo-item, .upload-btn {
		width: 158rpx;
		height: 158rpx;
		margin-right: 15rpx;
		margin-bottom: 15rpx;
		border-radius: 8rpx;
		overflow: hidden;
	}
	
	.photo-item {
		position: relative;
	}
	
	.preview-image {
		width: 100%;
		height: 100%;
	}
	
	.delete-btn {
		position: absolute;
		top: 0;
		right: 0;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.delete-btn image {
		width: 24rpx;
		height: 24rpx;
	}
	
	.upload-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #f9f9f9;
		border: 1rpx dashed #ddd;
	}
	
	.upload-btn image {
		width: 40rpx;
		height: 40rpx;
		margin-bottom: 10rpx;
	}
	
	.upload-btn text {
		font-size: 24rpx;
		color: #999999;
	}
	
	.anonymous-section {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		background-color: #ffffff;
		margin-bottom: 20rpx;
	}
	
	.anonymous-text {
		font-size: 30rpx;
		color: #333333;
	}
	
	.bottom-buttons {
		padding: 30rpx;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #ffffff;
		box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.05);
	}
	
	.submit-btn {
		height: 90rpx;
		line-height: 90rpx;
		border-radius: 45rpx;
		background-color: #ff5a5f;
		color: #ffffff;
		font-size: 32rpx;
		font-weight: bold;
	}
</style> 