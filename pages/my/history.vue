<template>
	<view class="history-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">浏览历史</text>
			<view class="right-btn" @tap="clearHistory">
				<text>清空</text>
			</view>
		</view>
		
		<!-- 分类tab -->
		<view class="tab-section">
			<view 
				v-for="(tab, index) in tabs" 
				:key="index" 
				:class="['tab-item', activeTab === index ? 'active-tab' : '']"
				@tap="switchTab(index)"
			>
				<text>{{tab.name}}</text>
			</view>
		</view>
		
		<!-- 历史记录列表 -->
		<scroll-view scroll-y class="history-list" @scrolltolower="loadMore">
			<view v-if="filteredHistory.length === 0" class="empty-tip">
				<image src="/static/images/empty.png" mode="aspectFit"></image>
				<text>暂无浏览记录</text>
			</view>
			
			<view v-else>
				<!-- 驿站转让历史 -->
				<view 
					v-if="activeTab === 0 || activeTab === 1" 
					v-for="(item, index) in filteredHistory" 
					:key="item.id"
					class="history-item station-item"
					@tap="viewDetail(item)"
				>
					<view class="item-content">
						<view class="item-main">
							<view class="item-title">{{item.title}}</view>
							<view class="item-meta">
								<text class="price">¥{{item.price}}</text>
								<text class="area">{{item.area}}㎡</text>
								<text class="address">{{item.address}}</text>
							</view>
							<view class="item-advantage">
								<text class="label">优势：</text>
								<text class="value">{{item.advantage}}</text>
							</view>
							<view class="item-time">
								<text>浏览时间：{{formatTime(item.viewTime)}}</text>
							</view>
						</view>
					</view>
					<view class="delete-btn" @tap.stop="deleteItem(item.id)">
						<image src="/static/icons/delete.png" mode="aspectFit"></image>
					</view>
				</view>
				
				<!-- 顶班服务历史 -->
				<view 
					v-if="activeTab === 0 || activeTab === 2" 
					v-for="(item, index) in filteredHistory" 
					:key="item.id"
					class="history-item service-item"
					@tap="viewDetail(item)"
				>
					<view class="item-content">
						<view class="item-main">
							<view class="item-title">{{item.title}}</view>
							<view class="item-meta">
								<text class="price">¥{{item.price}}/天</text>
								<text class="time">{{item.workTime}}</text>
								<text class="address">{{item.address}}</text>
							</view>
							<view class="item-tags">
								<text v-for="(tag, tagIndex) in item.tags" :key="tagIndex" class="tag">{{tag}}</text>
							</view>
							<view class="item-time">
								<text>浏览时间：{{formatTime(item.viewTime)}}</text>
							</view>
						</view>
					</view>
					<view class="delete-btn" @tap.stop="deleteItem(item.id)">
						<image src="/static/icons/delete.png" mode="aspectFit"></image>
					</view>
				</view>
				
				<!-- 招聘信息历史 -->
				<view 
					v-if="activeTab === 0 || activeTab === 3" 
					v-for="(item, index) in filteredHistory" 
					:key="item.id"
					class="history-item job-item"
					@tap="viewDetail(item)"
				>
					<view class="item-content">
						<view class="item-main">
							<view class="item-title">{{item.title}}</view>
							<view class="item-meta">
								<text class="salary">{{item.salary}}</text>
								<text class="exp">{{item.experience}}</text>
								<text class="address">{{item.address}}</text>
							</view>
							<view class="item-company">
								<text class="label">公司：</text>
								<text class="value">{{item.company}}</text>
							</view>
							<view class="item-time">
								<text>浏览时间：{{formatTime(item.viewTime)}}</text>
							</view>
						</view>
					</view>
					<view class="delete-btn" @tap.stop="deleteItem(item.id)">
						<image src="/static/icons/delete.png" mode="aspectFit"></image>
					</view>
				</view>
				
				<!-- 设备信息历史 -->
				<view 
					v-if="activeTab === 0 || activeTab === 4" 
					v-for="(item, index) in filteredHistory" 
					:key="item.id"
					class="history-item device-item"
					@tap="viewDetail(item)"
				>
					<view class="item-content">
						<view class="item-main">
							<view class="item-title">{{item.title}}</view>
							<view class="item-meta">
								<text class="price">¥{{item.price}}</text>
								<text class="condition">{{item.condition}}</text>
								<text class="address">{{item.address}}</text>
							</view>
							<view class="item-desc">
								<text class="label">描述：</text>
								<text class="value">{{item.description}}</text>
							</view>
							<view class="item-time">
								<text>浏览时间：{{formatTime(item.viewTime)}}</text>
							</view>
						</view>
					</view>
					<view class="delete-btn" @tap.stop="deleteItem(item.id)">
						<image src="/static/icons/delete.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<view class="load-more" v-if="hasMore && filteredHistory.length > 0">
				<text>加载中...</text>
			</view>
			
			<view class="no-more" v-if="!hasMore && filteredHistory.length > 0">
				<text>已加载全部内容</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabs: [
					{ name: '全部', type: 'all' },
					{ name: '驿站转让', type: 'station' },
					{ name: '顶班服务', type: 'service' },
					{ name: '招聘信息', type: 'job' },
					{ name: '设备信息', type: 'device' }
				],
				activeTab: 0,
				historyList: [],
				pageNum: 1,
				pageSize: 10,
				hasMore: true,
				loading: false
			}
		},
		computed: {
			filteredHistory() {
				if (this.activeTab === 0) {
					return this.historyList;
				} else {
					const type = this.tabs[this.activeTab].type;
					return this.historyList.filter(item => item.type === type);
				}
			}
		},
		onLoad() {
			this.loadHistoryData();
		},
		methods: {
			// 加载历史记录数据
			loadHistoryData() {
				this.loading = true;
				
				// 使用API接口获取浏览历史
				const params = {
					page: this.pageNum,
					pageSize: this.pageSize,
					type: this.activeTab === 0 ? '' : this.tabs[this.activeTab].type
				};
				
				// 调用浏览历史API
				this.$api.user.getHistory(params).then(res => {
					if (res.code === 0) {
						if (this.pageNum === 1) {
							this.historyList = res.data.list || [];
						} else {
							this.historyList = [...this.historyList, ...(res.data.list || [])];
						}
						
						this.hasMore = this.historyList.length < res.data.total;
					} else {
						uni.showToast({
							title: res.message || '获取浏览历史失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					console.error('获取浏览历史失败', err);
					uni.showToast({
						title: '网络异常，请稍后重试',
						icon: 'none'
					});
				}).finally(() => {
					this.loading = false;
					uni.stopPullDownRefresh();
				});
			},
			// 加载更多数据
			loadMore() {
				if (!this.hasMore || this.loading) return;
				this.pageNum++;
				this.loadHistoryData();
			},
			// 切换标签
			switchTab(index) {
				this.activeTab = index;
			},
			// 删除历史记录
			deleteItem(id) {
				uni.showModal({
					title: '提示',
					content: '确定要删除此条浏览记录吗？',
					success: (res) => {
						if (res.confirm) {
							this.historyList = this.historyList.filter(item => item.id !== id);
							// 更新本地存储
							uni.setStorageSync('browsing_history', JSON.stringify(this.historyList));
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							});
						}
					}
				});
			},
			// 清空历史记录
			clearHistory() {
				uni.showModal({
					title: '提示',
					content: '确定要清空全部浏览记录吗？',
					success: (res) => {
						if (res.confirm) {
							if (this.activeTab === 0) {
								// 清空全部
								this.historyList = [];
							} else {
								// 清空当前类型
								const type = this.tabs[this.activeTab].type;
								this.historyList = this.historyList.filter(item => item.type !== type);
							}
							
							// 更新本地存储
							uni.setStorageSync('browsing_history', JSON.stringify(this.historyList));
							uni.showToast({
								title: '清空成功',
								icon: 'success'
							});
						}
					}
				});
			},
			// 查看详情
			viewDetail(item) {
				let url = '';
				switch (item.type) {
					case 'station':
						url = '/pages/detail/station?id=' + item.id;
						break;
					case 'service':
						url = '/pages/detail/service?id=' + item.id;
						break;
					case 'job':
						url = '/pages/detail/job?id=' + item.id;
						break;
					case 'device':
						url = '/pages/detail/device?id=' + item.id;
						break;
				}
				
				if (url) {
					uni.navigateTo({
						url: url
					});
				}
			},
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			// 格式化时间
			formatTime(timestamp) {
				const now = Date.now();
				const diff = now - timestamp;
				
				// 1小时内
				if (diff < 3600000) {
					return Math.floor(diff / 60000) + '分钟前';
				}
				// 24小时内
				else if (diff < 86400000) {
					return Math.floor(diff / 3600000) + '小时前';
				}
				// 超过24小时
				else {
					const date = new Date(timestamp);
					return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()}`;
				}
			}
		}
	}
</script>

<style>
	.history-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
	}
	
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.right-btn {
		font-size: 28rpx;
		color: #666666;
	}
	
	.tab-section {
		display: flex;
		background-color: #ffffff;
		border-bottom: 1rpx solid #eeeeee;
	}
	
	.tab-item {
		flex: 1;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666666;
		position: relative;
	}
	
	.active-tab {
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.active-tab::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 4rpx;
		background-color: #ff5a5f;
	}
	
	.history-list {
		flex: 1;
		padding: 20rpx;
	}
	
	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		margin-top: 200rpx;
	}
	
	.empty-tip image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 20rpx;
	}
	
	.empty-tip text {
		font-size: 28rpx;
		color: #999999;
	}
	
	.history-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
		display: flex;
		align-items: center;
	}
	
	.item-content {
		flex: 1;
	}
	
	.item-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 16rpx;
	}
	
	.item-meta {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 16rpx;
	}
	
	.price, .salary {
		font-size: 28rpx;
		color: #ff5a5f;
		margin-right: 20rpx;
	}
	
	.area, .time, .exp, .condition {
		font-size: 26rpx;
		color: #666666;
		margin-right: 20rpx;
	}
	
	.address {
		font-size: 26rpx;
		color: #999999;
	}
	
	.item-advantage, .item-company, .item-desc {
		font-size: 26rpx;
		color: #666666;
		margin-bottom: 16rpx;
	}
	
	.label {
		color: #999999;
	}
	
	.item-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 16rpx;
	}
	
	.tag {
		font-size: 24rpx;
		color: #ff5a5f;
		background-color: rgba(255, 90, 95, 0.1);
		padding: 4rpx 12rpx;
		border-radius: 4rpx;
		margin-right: 12rpx;
		margin-bottom: 10rpx;
	}
	
	.item-time {
		font-size: 24rpx;
		color: #999999;
	}
	
	.delete-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.delete-btn image {
		width: 32rpx;
		height: 32rpx;
	}
	
	.load-more, .no-more {
		text-align: center;
		padding: 20rpx 0;
	}
	
	.load-more text, .no-more text {
		font-size: 24rpx;
		color: #999999;
	}
</style> 