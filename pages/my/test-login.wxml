<!-- pages/my/test-login.wxml -->
<view class="container">
  <view class="header">
    <view class="title">微信登录测试</view>
    <view class="server-info">
      <text>服务器地址: {{serverUrl}}</text>
      <button size="mini" bindtap="copyServerUrl">复制</button>
    </view>
    <view class="status">
      服务器状态: <text style="color: {{statusColor}}">{{serverStatus}}</text>
      <button size="mini" bindtap="checkServer">刷新</button>
    </view>
  </view>
  
  <view class="main">
    <button type="primary" bindtap="doWxLogin">执行微信登录</button>
    
    <view class="result-panel" wx:if="{{loginResult}}">
      <view class="panel-title">登录结果</view>
      <view class="result-content">
        <view>状态: {{loginResult.message}}</view>
        <view wx:if="{{loginResult.data.token}}">Token: {{loginResult.data.token}}</view>
        <view wx:if="{{loginResult.data.user}}">
          <view>用户ID: {{loginResult.data.user.id}}</view>
          <view>昵称: {{loginResult.data.user.nickname || '未设置'}}</view>
          <view>新用户: {{loginResult.data.user.isNewUser ? '是' : '否'}}</view>
        </view>
      </view>
    </view>
    
    <view class="log-panel">
      <view class="panel-header">
        <view class="panel-title">操作日志</view>
        <button size="mini" bindtap="clearLogs">清除</button>
      </view>
      <scroll-view scroll-y class="log-content">
        <view wx:for="{{logs}}" wx:key="index" class="log-item">
          {{item}}
        </view>
      </scroll-view>
    </view>
  </view>
</view> 