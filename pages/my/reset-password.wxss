/* pages/my/reset-password.wxss */
.reset-password-container {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 60rpx;
  margin-bottom: 60rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.form-container {
  margin-bottom: 60rpx;
}

.input-group {
  margin-bottom: 30rpx;
}

.label {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.input-box {
  display: flex;
  align-items: center;
  height: 90rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.prefix {
  font-size: 28rpx;
  color: #333333;
  padding-right: 20rpx;
}

input {
  flex: 1;
  height: 90rpx;
  font-size: 28rpx;
}

.verify-btn {
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 30rpx;
  background-color: #ff5a5f;
  color: #ffffff;
  font-size: 26rpx;
  text-align: center;
}

.verify-btn.disabled {
  background-color: #cccccc;
}

.eye-icon {
  padding: 0 20rpx;
}

.eye-icon image {
  width: 40rpx;
  height: 40rpx;
}

.reset-btn {
  height: 90rpx;
  line-height: 90rpx;
  background-color: #ff5a5f;
  color: #ffffff;
  border-radius: 45rpx;
  text-align: center;
  font-size: 32rpx;
  margin-top: 50rpx;
  margin-bottom: 30rpx;
}

.reset-btn.disabled {
  background-color: #cccccc;
}

.back-login {
  text-align: center;
  font-size: 28rpx;
  color: #ff5a5f;
  margin-bottom: 30rpx;
} 