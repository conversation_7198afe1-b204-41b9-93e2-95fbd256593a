<template>
	<view class="profile-container">
		<!-- 标题栏 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">个人资料</text>
			<view class="placeholder"></view>
		</view>
		
		<!-- 个人资料表单 -->
		<view class="profile-form">
			<!-- 头像 -->
			<view class="form-item avatar-item">
				<text class="label">头像</text>
				<view class="avatar-picker" @tap="chooseAvatar">
					<image class="avatar" :src="userInfo.avatar"></image>
					<view class="avatar-edit-icon">
						<image src="/static/icons/edit.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 昵称 -->
			<view class="form-item">
				<text class="label">昵称</text>
				<input type="text" v-model="userInfo.username" placeholder="请输入昵称" />
			</view>
			
			<!-- 性别 -->
			<view class="form-item">
				<text class="label">性别</text>
				<view class="picker-box" @tap="showGenderPicker">
					<text>{{userInfo.gender || '未设置'}}</text>
					<image class="right-icon" src="/static/icons/right.png" mode="aspectFit"></image>
				</view>
			</view>
			
			<!-- 手机号 -->
			<view class="form-item">
				<text class="label">手机号</text>
				<view class="phone-box">
					<text>{{formatPhone(userInfo.phoneNumber)}}</text>
					<view class="modify-btn" @tap="modifyPhone">
						<text>修改</text>
					</view>
				</view>
			</view>
			
			<!-- 认证状态 -->
			<view class="form-item">
				<text class="label">认证状态</text>
				<view class="verify-status-box">
					<view class="verify-item" @tap="goToIdentityVerify">
						<text>实名认证</text>
						<view class="status-tag" :class="userInfo.isVerified ? 'verified' : 'unverified'">
							{{userInfo.isVerified ? '已认证' : '未认证'}}
						</view>
						<image class="right-icon" src="/static/icons/right.png" mode="aspectFit"></image>
					</view>
					<view class="verify-item" @tap="goToStationVerify">
						<text>驿站认证</text>
						<view class="status-tag" :class="userInfo.isStationVerified ? 'verified' : 'unverified'">
							{{userInfo.isStationVerified ? '已认证' : '未认证'}}
						</view>
						<image class="right-icon" src="/static/icons/right.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 个人简介 -->
			<view class="form-item">
				<text class="label">个人简介</text>
				<textarea v-model="userInfo.bio" placeholder="请输入个人简介" maxlength="100" />
				<text class="word-count">{{userInfo.bio ? userInfo.bio.length : 0}}/100</text>
			</view>
		</view>
		
		<!-- 底部保存按钮 -->
		<view class="save-btn" @tap="saveProfile">
			<text>保存</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userInfo: {
					username: '',
					avatar: '/static/images/default-avatar.png',
					gender: '',
					phoneNumber: '',
					bio: '',
					userId: '',
					isLoggedIn: false
				},
				genderOptions: ['男', '女', '保密']
			}
		},
		onLoad() {
			// 获取用户信息
			this.getUserInfo();
		},
		methods: {
			// 获取用户信息
			getUserInfo() {
				const userInfoStorage = uni.getStorageSync('userInfo');
				if (userInfoStorage) {
					const storedInfo = JSON.parse(userInfoStorage);
					this.userInfo = {
						...storedInfo,
						gender: storedInfo.gender || '',
						bio: storedInfo.bio || ''
					};
				} else {
					uni.showToast({
						title: '请先登录',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}
			},
			// 选择头像
			chooseAvatar() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						this.userInfo.avatar = res.tempFilePaths[0];
					}
				});
			},
			// 显示性别选择器
			showGenderPicker() {
				uni.showActionSheet({
					itemList: this.genderOptions,
					success: (res) => {
						this.userInfo.gender = this.genderOptions[res.tapIndex];
					}
				});
			},
			// 格式化手机号
			formatPhone(phone) {
				if (!phone) return '未绑定';
				return phone.substr(0, 3) + '****' + phone.substr(7);
			},
			// 修改手机号
			modifyPhone() {
				uni.showToast({
					title: '暂不支持修改手机号',
					icon: 'none'
				});
			},
			// 前往实名认证页
			goToIdentityVerify() {
				uni.navigateTo({
					url: '/pages/my/verify-identity'
				});
			},
			// 前往驿站认证页
			goToStationVerify() {
				uni.navigateTo({
					url: '/pages/my/verify-station'
				});
			},
			// 保存个人资料
			saveProfile() {
				if (!this.userInfo.username.trim()) {
					uni.showToast({
						title: '昵称不能为空',
						icon: 'none'
					});
					return;
				}
				
				// 保存到本地存储
				uni.setStorageSync('userInfo', JSON.stringify(this.userInfo));
				
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
				
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			},
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			}
		}
	}
</script>

<style>
	.profile-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
	}
	
	.header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.placeholder {
		width: 60rpx;
	}
	
	.profile-form {
		background-color: #ffffff;
		margin-top: 20rpx;
		padding: 0 30rpx;
	}
	
	.form-item {
		padding: 30rpx 0;
		border-bottom: 1rpx solid #eeeeee;
	}
	
	.form-item:last-child {
		border-bottom: none;
	}
	
	.label {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 20rpx;
		display: block;
	}
	
	input {
		height: 80rpx;
		font-size: 28rpx;
		color: #333333;
	}
	
	textarea {
		width: 100%;
		height: 200rpx;
		font-size: 28rpx;
		color: #333333;
	}
	
	.word-count {
		position: absolute;
		right: 20rpx;
		bottom: 20rpx;
		font-size: 24rpx;
		color: #999999;
	}
	
	.avatar-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.avatar-item .label {
		margin-bottom: 0;
	}
	
	.avatar-picker {
		position: relative;
	}
	
	.avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		border: 2rpx solid #eeeeee;
	}
	
	.avatar-edit-icon {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 40rpx;
		height: 40rpx;
		background-color: #ff5a5f;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.avatar-edit-icon image {
		width: 24rpx;
		height: 24rpx;
	}
	
	.picker-box {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.picker-box text {
		font-size: 28rpx;
		color: #333333;
	}
	
	.right-icon {
		width: 30rpx;
		height: 30rpx;
	}
	
	.phone-box {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.phone-box text {
		font-size: 28rpx;
		color: #333333;
	}
	
	.modify-btn {
		padding: 8rpx 20rpx;
		background-color: #f8f8f8;
		border-radius: 30rpx;
	}
	
	.modify-btn text {
		font-size: 24rpx;
		color: #666666;
	}
	
	.verify-status-box {
		background-color: #f8f8f8;
		border-radius: 8rpx;
		overflow: hidden;
	}
	
	.verify-item {
		display: flex;
		align-items: center;
		padding: 24rpx 20rpx;
		border-bottom: 1rpx solid #eeeeee;
	}
	
	.verify-item:last-child {
		border-bottom: none;
	}
	
	.verify-item text {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
	}
	
	.status-tag {
		font-size: 24rpx;
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
		margin-right: 20rpx;
	}
	
	.status-tag.verified {
		background-color: rgba(52, 199, 89, 0.1);
		color: #34c759;
	}
	
	.status-tag.unverified {
		background-color: rgba(255, 149, 0, 0.1);
		color: #ff9500;
	}
	
	.save-btn {
		margin: 60rpx 30rpx;
		height: 90rpx;
		background-color: #ff5a5f;
		border-radius: 45rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.save-btn text {
		font-size: 32rpx;
		color: #ffffff;
	}
</style> 