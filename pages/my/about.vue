<template>
	<view class="about-container">
		<!-- 头部导航 -->
		<view class="header">
			<view class="back-btn" @tap="navigateBack">
				<image src="/static/icons/back.png" mode="aspectFit"></image>
			</view>
			<text class="title">关于我们</text>
		</view>
		
		<!-- 应用信息 -->
		<view class="app-info">
			<image src="/static/images/logo.png" mode="aspectFit" class="logo"></image>
			<text class="app-name">驿站帮</text>
			<text class="app-version">v1.0.0</text>
		</view>
		
		<!-- 内容卡片 -->
		<view class="content-card">
			<view class="card-item" @tap="showAppIntro">
				<text class="item-title">应用介绍</text>
				<image src="/static/icons/right.png" mode="aspectFit" class="right-icon"></image>
			</view>
			<view class="card-item" @tap="showTeamIntro">
				<text class="item-title">团队介绍</text>
				<image src="/static/icons/right.png" mode="aspectFit" class="right-icon"></image>
			</view>
			<view class="card-item" @tap="showUserAgreement">
				<text class="item-title">用户协议</text>
				<image src="/static/icons/right.png" mode="aspectFit" class="right-icon"></image>
			</view>
			<view class="card-item" @tap="showPrivacyPolicy">
				<text class="item-title">隐私政策</text>
				<image src="/static/icons/right.png" mode="aspectFit" class="right-icon"></image>
			</view>
			<view class="card-item">
				<text class="item-title">当前版本</text>
				<text class="item-value">1.0.0</text>
			</view>
		</view>
		
		<!-- 联系我们 -->
		<view class="contact-section">
			<view class="section-title">联系我们</view>
			<view class="contact-item">
				<text class="contact-label">客服电话：</text>
				<text class="contact-value" @tap="callService">************</text>
			</view>
			<view class="contact-item">
				<text class="contact-label">客服邮箱：</text>
				<text class="contact-value"><EMAIL></text>
			</view>
			<view class="contact-item">
				<text class="contact-label">官方网站：</text>
				<text class="contact-value">www.yizhanbang.com</text>
			</view>
			<view class="contact-item">
				<text class="contact-label">公司地址：</text>
				<text class="contact-value">浙江省杭州市西湖区文三路</text>
			</view>
		</view>
		
		<!-- 社交媒体 -->
		<view class="social-section">
			<view class="section-title">关注我们</view>
			<view class="social-list">
				<view class="social-item" @tap="showQrCode('weixin')">
					<image src="/static/icons/wechat.png" mode="aspectFit"></image>
					<text>微信公众号</text>
				</view>
				<view class="social-item" @tap="showQrCode('weibo')">
					<image src="/static/icons/weibo.png" mode="aspectFit"></image>
					<text>官方微博</text>
				</view>
				<view class="social-item" @tap="showQrCode('douyin')">
					<image src="/static/icons/tiktok.png" mode="aspectFit"></image>
					<text>抖音号</text>
				</view>
				<view class="social-item" @tap="showQrCode('xiaohongshu')">
					<image src="/static/icons/redbook.png" mode="aspectFit"></image>
					<text>小红书</text>
				</view>
			</view>
		</view>
		
		<!-- 底部版权 -->
		<view class="copyright">
			<text>Copyright © 2023-2024 驿站帮</text>
			<text>All Rights Reserved</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			// 返回上一页
			navigateBack() {
				uni.navigateBack();
			},
			// 显示应用介绍
			showAppIntro() {
				uni.showModal({
					title: '应用介绍',
					content: '"驿站帮"是一个专注于快递驿站资源流转的平台，为有意向转让或接手快递驿站的用户提供信息发布、查询和交流的服务。平台覆盖全国主要城市，提供驿站转让、设备买卖、人员招聘等一站式服务，致力于打造快递驿站行业高效、透明的资源共享平台。',
					showCancel: false,
					confirmText: '我知道了'
				});
			},
			// 显示团队介绍
			showTeamIntro() {
				uni.showModal({
					title: '团队介绍',
					content: '"驿站帮"由一群有着丰富物流行业和互联网产品经验的年轻人创立。团队成员来自阿里巴巴、京东、顺丰等知名企业，拥有深厚的行业资源和技术实力。我们秉承"让快递驿站资源流转更简单"的理念，不断优化产品体验，为用户提供更优质的服务。',
					showCancel: false,
					confirmText: '我知道了'
				});
			},
			// 显示用户协议
			showUserAgreement() {
				uni.navigateTo({
					url: '/pages/my/agreement?type=user'
				});
			},
			// 显示隐私政策
			showPrivacyPolicy() {
				uni.navigateTo({
					url: '/pages/my/agreement?type=privacy'
				});
			},
			// 拨打客服电话
			callService() {
				uni.makePhoneCall({
					phoneNumber: '4001234567',
					fail: () => {
						uni.showToast({
							title: '拨打失败，请手动拨打************',
							icon: 'none'
						});
					}
				});
			},
			// 显示二维码
			showQrCode(type) {
				let title = '';
				switch(type) {
					case 'weixin':
						title = '微信公众号';
						break;
					case 'weibo':
						title = '官方微博';
						break;
					case 'douyin':
						title = '抖音号';
						break;
					case 'xiaohongshu':
						title = '小红书';
						break;
				}
				
				uni.showToast({
					title: `${title}功能开发中`,
					icon: 'none'
				});
			}
		}
	}
</script>

<style>
	.about-container {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 50rpx;
	}
	
	.header {
		display: flex;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
	}
	
	.back-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.title {
		flex: 1;
		text-align: center;
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
		margin-right: 60rpx;
	}
	
	.app-info {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 50rpx 0;
		background-color: #ffffff;
	}
	
	.logo {
		width: 160rpx;
		height: 160rpx;
		border-radius: 30rpx;
		margin-bottom: 20rpx;
	}
	
	.app-name {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
	}
	
	.app-version {
		font-size: 28rpx;
		color: #999999;
	}
	
	.content-card {
		background-color: #ffffff;
		margin-top: 20rpx;
		padding: 0 30rpx;
	}
	
	.card-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.card-item:last-child {
		border-bottom: none;
	}
	
	.item-title {
		font-size: 30rpx;
		color: #333333;
	}
	
	.item-value {
		font-size: 30rpx;
		color: #999999;
	}
	
	.right-icon {
		width: 30rpx;
		height: 30rpx;
	}
	
	.contact-section, .social-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		padding: 30rpx;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 30rpx;
	}
	
	.contact-item {
		display: flex;
		margin-bottom: 20rpx;
	}
	
	.contact-item:last-child {
		margin-bottom: 0;
	}
	
	.contact-label {
		font-size: 28rpx;
		color: #666666;
		width: 160rpx;
	}
	
	.contact-value {
		font-size: 28rpx;
		color: #333333;
		flex: 1;
	}
	
	.social-list {
		display: flex;
		justify-content: space-between;
	}
	
	.social-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 25%;
	}
	
	.social-item image {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 16rpx;
	}
	
	.social-item text {
		font-size: 24rpx;
		color: #666666;
	}
	
	.copyright {
		margin-top: 60rpx;
		text-align: center;
	}
	
	.copyright text {
		font-size: 24rpx;
		color: #999999;
		display: block;
		line-height: 1.8;
	}
</style> 