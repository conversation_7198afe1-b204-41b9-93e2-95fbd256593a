import { checkPhoneExists, sendSmsCode, loginByPhone, loginByPassword } from '../../api/auth';

Page({
  data: {
    loading: false,
    phoneNumber: '',
    code: '',
    password: '',
    showPassword: false,
    showVerifyLogin: true, // 默认显示验证码登录
    countdown: 0,
    codeText: '获取验证码',
    timer: null,
    agreeProtocol: true
  },
  
  // 切换登录方式
  switchLoginType() {
    this.setData({
      showVerifyLogin: !this.data.showVerifyLogin,
      code: '',
      password: ''
    });
  },
  
  // 输入手机号
  inputPhoneNumber(e) {
    this.setData({
      phoneNumber: e.detail.value
    });
  },
  
  // 输入验证码
  inputCode(e) {
    this.setData({
      code: e.detail.value
    });
  },
  
  // 输入密码
  inputPassword(e) {
    this.setData({
      password: e.detail.value
    });
  },
  
  // 切换密码显示状态
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },
  
  // 检查手机号是否有效
  isValidPhone() {
    return /^1[3-9]\d{9}$/.test(this.data.phoneNumber);
  },
  
  // 获取验证码
  async getVerificationCode() {
    const { phoneNumber } = this.data;
    
    if (!this.isValidPhone()) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    
    if (this.data.countdown > 0) {
      return;
    }
    
    this.setData({ loading: true });
    
    try {
      // 检查手机号是否已注册
      const checkResult = await checkPhoneExists(phoneNumber);
      
      // 发送验证码
      const result = await sendSmsCode(phoneNumber, 'login');
      
      if (result.code === 0) {
        wx.showToast({
          title: '验证码已发送',
          icon: 'success'
        });
        
        // 开始倒计时
        this.setData({
          countdown: 60,
          codeText: '60秒'
        });
        
        this.data.timer = setInterval(() => {
          const countdown = this.data.countdown - 1;
          this.setData({
            countdown,
            codeText: countdown + '秒'
          });
          
          if (countdown === 0) {
            clearInterval(this.data.timer);
            this.setData({
              codeText: '获取验证码'
            });
          }
        }, 1000);
      } else {
        wx.showToast({
          title: result.message || '发送失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('发送验证码失败', error);
      wx.showToast({
        title: '发送失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },
  
  // 登录
  async login() {
    if (this.data.loading) return;
    
    const { phoneNumber, code, password, showVerifyLogin } = this.data;
    
    if (!this.isValidPhone()) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }
    
    if (showVerifyLogin && !code) {
      wx.showToast({
        title: '请输入验证码',
        icon: 'none'
      });
      return;
    }
    
    if (!showVerifyLogin && (!password || password.length < 6)) {
      wx.showToast({
        title: '请输入至少6位密码',
        icon: 'none'
      });
      return;
    }
    
    if (!this.data.agreeProtocol) {
      wx.showToast({
        title: '请先同意用户协议和隐私政策',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ loading: true });
    wx.showLoading({
      title: '登录中...'
    });
    
    try {
      let response;
      
      // 根据登录类型调用不同的登录接口
      if (showVerifyLogin) {
        response = await loginByPhone(phoneNumber, code);
      } else {
        response = await loginByPassword(phoneNumber, password);
      }
      
      if (response.code === 0) {
        // 登录成功，保存登录信息
        wx.setStorageSync('token', response.data.token);
        wx.setStorageSync('userInfo', JSON.stringify(response.data.user));
        
        // 更新全局登录状态
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.isLoggedIn = true;
          app.globalData.userInfo = response.data.user;
        }
        
        // 判断是否新用户
        if (response.data.user.isNewUser) {
          // 新用户，引导完善资料
          wx.showModal({
            title: '登录成功',
            content: '欢迎加入驿站帮，是否立即完善个人资料？',
            confirmText: '立即完善',
            cancelText: '以后再说',
            success: (res) => {
              if (res.confirm) {
                // 跳转到个人资料页
                wx.navigateTo({
                  url: '/pages/my/profile'
                });
              } else {
                // 返回上一页或首页
                this.navigateBack();
              }
            }
          });
        } else {
          // 已注册用户，直接返回
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });
          setTimeout(() => {
            this.navigateBack();
          }, 1500);
        }
      } else {
        wx.showToast({
          title: response.message || '登录失败，请重试',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('登录失败', error);
      wx.showToast({
        title: '登录失败，请检查网络并重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
      this.setData({ loading: false });
    }
  },
  
  // 跳转到注册页面
  navigateToRegister() {
    wx.navigateTo({
      url: '/pages/my/register'
    });
  },
  
  // 跳转到找回密码页面
  navigateToResetPassword() {
    wx.navigateTo({
      url: '/pages/my/reset-password'
    });
  },
  
  // 切换协议同意状态
  toggleAgreement() {
    this.setData({
      agreeProtocol: !this.data.agreeProtocol
    });
  },
  
  // 返回上一页或首页
  navigateBack() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },
  
  // 用户协议
  navigateToAgreement() {
    wx.navigateTo({
      url: '/pages/common/agreement?type=user'
    });
  },
  
  // 隐私政策
  navigateToPrivacy() {
    wx.navigateTo({
      url: '/pages/common/agreement?type=privacy'
    });
  },
  
  onUnload() {
    // 页面销毁时清除定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
  }
}); 