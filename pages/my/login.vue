<template>
	<view class="login-container">
		<view class="header">
			<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
			<text class="title">欢迎使用驿站帮</text>
		</view>
		
		<!-- 登录表单 -->
		<view v-if="currentMode === 'login'" class="form-container">
			<view class="tabs">
				<view class="tab" :class="{ active: loginType === 'phone' }" @tap="loginType = 'phone'">
					<text>验证码登录</text>
				</view>
				<view class="tab" :class="{ active: loginType === 'password' }" @tap="loginType = 'password'">
					<text>密码登录</text>
				</view>
			</view>
			
			<view class="input-group">
				<text class="label">手机号码</text>
				<view class="input-box">
					<text class="prefix">+86</text>
					<input type="number" v-model="phoneNumber" placeholder="请输入手机号码" maxlength="11" />
				</view>
			</view>
			
			<view v-if="loginType === 'phone'" class="input-group">
				<text class="label">验证码</text>
				<view class="input-box">
					<input type="number" v-model="verifyCode" placeholder="请输入验证码" maxlength="6" />
					<view class="verify-btn" :class="{'disabled': !canSendCode}" @tap="sendVerifyCode">
						<text>{{codeText}}</text>
					</view>
				</view>
			</view>
			
			<view v-if="loginType === 'password'" class="input-group">
				<text class="label">密码</text>
				<view class="input-box">
					<input :type="showPassword ? 'text' : 'password'" v-model="password" placeholder="请输入密码" />
					<view class="eye-icon" @tap="togglePasswordVisibility">
						<image :src="showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<view class="login-btn" :class="{'disabled': !canLogin}" @tap="login">
				<text>登录</text>
			</view>
			
			<view class="options">
				<text class="option-link" @tap="switchToForgotPassword">忘记密码</text>
				<text class="option-link" @tap="switchToRegister">注册账号</text>
			</view>
			
			<view class="agreement">
				<checkbox :checked="agreeProtocol" @tap="toggleAgreement" />
				<text>登录即表示同意</text>
				<text class="link" @tap="navigateTo('/pages/my/agreement')">《用户协议》</text>
				<text>和</text>
				<text class="link" @tap="navigateTo('/pages/my/privacy')">《隐私政策》</text>
			</view>
		</view>
		
		<!-- 注册表单 -->
		<view v-if="currentMode === 'register'" class="form-container">
			<view class="form-title">
				<text>注册账号</text>
				<text class="back-btn" @tap="switchToLogin">返回登录</text>
			</view>
			
			<view class="input-group">
				<text class="label">用户名</text>
				<view class="input-box">
					<input type="text" v-model="username" placeholder="请设置用户名" maxlength="20" />
				</view>
			</view>
			
			<view class="input-group">
				<text class="label">手机号码</text>
				<view class="input-box">
					<text class="prefix">+86</text>
					<input type="number" v-model="phoneNumber" placeholder="请输入手机号码" maxlength="11" />
				</view>
			</view>
			
			<view class="input-group">
				<text class="label">验证码</text>
				<view class="input-box">
					<input type="number" v-model="verifyCode" placeholder="请输入验证码" maxlength="6" />
					<view class="verify-btn" :class="{'disabled': !canSendCode}" @tap="sendVerifyCode">
						<text>{{codeText}}</text>
					</view>
				</view>
			</view>
			
			<view class="input-group">
				<text class="label">密码</text>
				<view class="input-box">
					<input :type="showPassword ? 'text' : 'password'" v-model="password" placeholder="请设置6-20位密码" />
					<view class="eye-icon" @tap="togglePasswordVisibility">
						<image :src="showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<view class="login-btn" :class="{'disabled': !canRegister}" @tap="register">
				<text>注册</text>
			</view>
			
			<view class="agreement">
				<checkbox :checked="agreeProtocol" @tap="toggleAgreement" />
				<text>注册即表示同意</text>
				<text class="link" @tap="navigateTo('/pages/my/agreement')">《用户协议》</text>
				<text>和</text>
				<text class="link" @tap="navigateTo('/pages/my/privacy')">《隐私政策》</text>
			</view>
		</view>
		
		<!-- 找回密码表单 -->
		<view v-if="currentMode === 'forgotPassword'" class="form-container">
			<view class="form-title">
				<text>找回密码</text>
				<text class="back-btn" @tap="switchToLogin">返回登录</text>
			</view>
			
			<view class="input-group">
				<text class="label">手机号码</text>
				<view class="input-box">
					<text class="prefix">+86</text>
					<input type="number" v-model="phoneNumber" placeholder="请输入手机号码" maxlength="11" />
				</view>
			</view>
			
			<view class="input-group">
				<text class="label">验证码</text>
				<view class="input-box">
					<input type="number" v-model="verifyCode" placeholder="请输入验证码" maxlength="6" />
					<view class="verify-btn" :class="{'disabled': !canSendCode}" @tap="sendVerifyCode">
						<text>{{codeText}}</text>
					</view>
				</view>
			</view>
			
			<view class="input-group">
				<text class="label">新密码</text>
				<view class="input-box">
					<input :type="showPassword ? 'text' : 'password'" v-model="password" placeholder="请设置6-20位新密码" />
					<view class="eye-icon" @tap="togglePasswordVisibility">
						<image :src="showPassword ? '/static/icons/eye-open.png' : '/static/icons/eye-close.png'" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<view class="login-btn" :class="{'disabled': !canResetPassword}" @tap="resetPassword">
				<text>重置密码</text>
			</view>
		</view>
	</view>
</template>

<script>
import { checkPhoneExists, sendSmsCode, loginByPhone, loginByPassword, register, resetPassword } from '../../api/auth';

export default {
	data() {
		return {
			currentMode: 'login', // login, register, forgotPassword
			loginType: 'phone', // phone, password
			phoneNumber: '',
			verifyCode: '',
			password: '',
			username: '',
			showPassword: false,
			countdown: 0,
			codeText: '获取验证码',
			agreeProtocol: true,
			timer: null,
			loading: false
		}
	},
	computed: {
		canSendCode() {
			// 检查手机号格式是否正确且不在倒计时中
			return this.isValidPhone() && this.countdown === 0;
		},
		canLogin() {
			// 检查是否满足登录条件
			if (this.loginType === 'phone') {
				return this.isValidPhone() && this.verifyCode.length === 6 && this.agreeProtocol;
			} else {
				return this.isValidPhone() && this.password.length >= 6 && this.agreeProtocol;
			}
		},
		canRegister() {
			// 检查是否满足注册条件
			return this.isValidPhone() && this.verifyCode.length === 6 && 
				this.password.length >= 6 && this.username.length >= 2 && this.agreeProtocol;
		},
		canResetPassword() {
			// 检查是否满足重置密码条件
			return this.isValidPhone() && this.verifyCode.length === 6 && this.password.length >= 6;
		}
	},
	onUnload() {
		// 页面销毁时清除定时器
		if (this.timer) {
			clearInterval(this.timer);
			this.timer = null;
		}
	},
	methods: {
		// 检查手机号是否有效
		isValidPhone() {
			return /^1[3-9]\d{9}$/.test(this.phoneNumber);
		},
		
		// 切换密码显示状态
		togglePasswordVisibility() {
			this.showPassword = !this.showPassword;
		},
		
		// 切换到登录模式
		switchToLogin() {
			this.currentMode = 'login';
			this.clearForm();
		},
		
		// 切换到注册模式
		switchToRegister() {
			this.currentMode = 'register';
			this.clearForm();
		},
		
		// 切换到找回密码模式
		switchToForgotPassword() {
			this.currentMode = 'forgotPassword';
			this.clearForm();
		},
		
		// 清除表单
		clearForm() {
			this.verifyCode = '';
			this.password = '';
			this.username = '';
		},
		
		// 切换协议同意状态
		toggleAgreement() {
			this.agreeProtocol = !this.agreeProtocol;
		},
		
		// 跳转页面
		navigateTo(url) {
			uni.navigateTo({
				url
			});
		},
		
		// 发送验证码
		async sendVerifyCode() {
			if (!this.canSendCode || this.loading) return;
			
			let smsType = 'login';
			if (this.currentMode === 'register') {
				smsType = 'register';
			} else if (this.currentMode === 'forgotPassword') {
				smsType = 'resetPassword';
			}
			
			this.loading = true;
			try {
				// 检查手机号是否已注册
				if (smsType === 'register') {
					const checkResult = await checkPhoneExists(this.phoneNumber);
					if (checkResult.data.exists) {
						uni.showToast({
							title: '该手机号已注册，请直接登录',
							icon: 'none'
						});
						this.switchToLogin();
						return;
					}
				}
				
				// 发送验证码
				const result = await sendSmsCode(this.phoneNumber, smsType);
				
				if (result.code === 0) {
					uni.showToast({
						title: '验证码已发送',
						icon: 'success'
					});
					
					// 开始倒计时
					this.countdown = 60;
					this.codeText = `${this.countdown}秒`;
					
					this.timer = setInterval(() => {
						this.countdown--;
						this.codeText = `${this.countdown}秒`;
						
						if (this.countdown === 0) {
							clearInterval(this.timer);
							this.timer = null;
							this.codeText = '获取验证码';
						}
					}, 1000);
				} else {
					uni.showToast({
						title: result.message || '发送失败，请重试',
						icon: 'none'
					});
				}
			} catch (err) {
				console.error('发送验证码失败', err);
				uni.showToast({
					title: '发送失败，请重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},
		
		// 登录
		async login() {
			if (!this.canLogin || this.loading) return;
			
			if (!this.agreeProtocol) {
				uni.showToast({
					title: '请先同意用户协议和隐私政策',
					icon: 'none'
				});
				return;
			}
			
			this.loading = true;
			uni.showLoading({
				title: '登录中...'
			});
			
			try {
				let result;
				
				// 根据登录类型调用不同的登录接口
				if (this.loginType === 'phone') {
					result = await loginByPhone(this.phoneNumber, this.verifyCode);
				} else {
					result = await loginByPassword(this.phoneNumber, this.password);
				}
				
				if (result.code === 0) {
					// 保存登录信息
					uni.setStorageSync('token', result.data.token);
					uni.setStorageSync('userInfo', result.data.user);
					
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					});
					
					// 登录成功后返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					uni.showToast({
						title: result.message || '登录失败，请检查账号密码',
						icon: 'none'
					});
				}
			} catch (err) {
				console.error('登录失败', err);
				uni.showToast({
					title: '登录失败，请检查网络并重试',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
				this.loading = false;
			}
		},
		
		// 注册
		async register() {
			if (!this.canRegister || this.loading) return;
			
			if (!this.agreeProtocol) {
				uni.showToast({
					title: '请先同意用户协议和隐私政策',
					icon: 'none'
				});
				return;
			}
			
			this.loading = true;
			uni.showLoading({
				title: '注册中...'
			});
			
			try {
				const result = await register({
					username: this.username,
					phone: this.phoneNumber,
					password: this.password,
					code: this.verifyCode
				});
				
				if (result.code === 0) {
					// 保存登录信息
					uni.setStorageSync('token', result.data.token);
					uni.setStorageSync('userInfo', result.data.user);
					
					uni.showToast({
						title: '注册成功',
						icon: 'success'
					});
					
					// 注册成功后返回上一页
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				} else {
					uni.showToast({
						title: result.message || '注册失败，请重试',
						icon: 'none'
					});
				}
			} catch (err) {
				console.error('注册失败', err);
				uni.showToast({
					title: '注册失败，请检查网络并重试',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
				this.loading = false;
			}
		},
		
		// 重置密码
		async resetPassword() {
			if (!this.canResetPassword || this.loading) return;
			
			this.loading = true;
			uni.showLoading({
				title: '重置中...'
			});
			
			try {
				const result = await resetPassword(this.phoneNumber, this.verifyCode, this.password);
				
				if (result.code === 0) {
					uni.showToast({
						title: '密码重置成功，请登录',
						icon: 'success'
					});
					
					// 重置成功后切换到登录页面
					setTimeout(() => {
						this.switchToLogin();
					}, 1500);
				} else {
					uni.showToast({
						title: result.message || '重置失败，请重试',
						icon: 'none'
					});
				}
			} catch (err) {
				console.error('重置密码失败', err);
				uni.showToast({
					title: '重置失败，请检查网络并重试',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
				this.loading = false;
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.login-container {
	padding: 30rpx;
	min-height: 100vh;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	
	.header {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin: 60rpx 0;
		
		.logo {
			width: 160rpx;
			height: 160rpx;
			margin-bottom: 20rpx;
		}
		
		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
	}
	
	.form-container {
		margin-top: 20rpx;
		
		.tabs {
			display: flex;
			border-bottom: 1rpx solid #eee;
			margin-bottom: 30rpx;
			
			.tab {
				flex: 1;
				text-align: center;
				padding: 20rpx 0;
				font-size: 30rpx;
				color: #666;
				position: relative;
				
				&.active {
					color: #007AFF;
					font-weight: bold;
					
					&::after {
						content: '';
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						width: 60rpx;
						height: 4rpx;
						background-color: #007AFF;
					}
				}
			}
		}
		
		.form-title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 30rpx;
			
			text {
				font-size: 34rpx;
				font-weight: bold;
				color: #333;
			}
			
			.back-btn {
				font-size: 28rpx;
				color: #007AFF;
				font-weight: normal;
			}
		}
		
		.input-group {
			margin-bottom: 30rpx;
			
			.label {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 10rpx;
				display: block;
			}
			
			.input-box {
				display: flex;
				align-items: center;
				border: 1rpx solid #ddd;
				border-radius: 8rpx;
				padding: 0 20rpx;
				height: 90rpx;
				position: relative;
				
				.prefix {
					color: #666;
					margin-right: 10rpx;
					font-size: 28rpx;
				}
				
				input {
					flex: 1;
					height: 90rpx;
					font-size: 30rpx;
				}
				
				.verify-btn {
					width: 200rpx;
					height: 70rpx;
					line-height: 70rpx;
					text-align: center;
					background-color: #007AFF;
					color: #fff;
					border-radius: 6rpx;
					font-size: 26rpx;
					
					&.disabled {
						background-color: #ccc;
					}
				}
				
				.eye-icon {
					width: 40rpx;
					height: 40rpx;
					padding: 0 10rpx;
					
					image {
						width: 40rpx;
						height: 40rpx;
					}
				}
			}
		}
		
		.login-btn {
			background-color: #007AFF;
			color: #fff;
			height: 90rpx;
			line-height: 90rpx;
			border-radius: 8rpx;
			text-align: center;
			font-size: 32rpx;
			margin-top: 50rpx;
			
			&.disabled {
				background-color: #ccc;
			}
		}
		
		.options {
			display: flex;
			justify-content: space-between;
			margin-top: 30rpx;
			
			.option-link {
				color: #007AFF;
				font-size: 28rpx;
			}
		}
		
		.agreement {
			margin-top: 40rpx;
			display: flex;
			align-items: center;
			font-size: 24rpx;
			color: #666;
			
			checkbox {
				transform: scale(0.7);
				margin-right: 6rpx;
			}
			
			.link {
				color: #007AFF;
			}
		}
	}
}
</style> 