<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input-box">
				<image class="search-icon" src="/static/icons/search.png" mode="aspectFit"></image>
				<input type="text" placeholder="搜索设备名称/品牌" v-model="searchKeyword" confirm-type="search" @confirm="handleSearch" />
			</view>
			<view class="filter-btn" @tap="showFilter">
				<image src="/static/icons/filter.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 筛选区域 -->
		<view class="filter-section" v-if="showFilterSection">
			<view class="filter-row">
				<view class="filter-title">区域</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': activeLocation === 'all'}" @tap="selectLocation('all')">全部</view>
					<view class="filter-option" :class="{'active': activeLocation === 'haidian'}" @tap="selectLocation('haidian')">海淀区</view>
					<view class="filter-option" :class="{'active': activeLocation === 'chaoyang'}" @tap="selectLocation('chaoyang')">朝阳区</view>
					<view class="filter-option" :class="{'active': activeLocation === 'dongcheng'}" @tap="selectLocation('dongcheng')">东城区</view>
					<view class="filter-option" :class="{'active': activeLocation === 'xicheng'}" @tap="selectLocation('xicheng')">西城区</view>
				</view>
			</view>
			<view class="filter-row">
				<view class="filter-title">价格</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': activePrice === 'all'}" @tap="selectPrice('all')">全部</view>
					<view class="filter-option" :class="{'active': activePrice === 'low'}" @tap="selectPrice('low')">1000元以下</view>
					<view class="filter-option" :class="{'active': activePrice === 'medium'}" @tap="selectPrice('medium')">1000-3000元</view>
					<view class="filter-option" :class="{'active': activePrice === 'high'}" @tap="selectPrice('high')">3000元以上</view>
				</view>
			</view>
			<view class="filter-row">
				<view class="filter-title">设备类型</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': activeType === 'all'}" @tap="selectType('all')">全部</view>
					<view class="filter-option" :class="{'active': activeType === 'scanner'}" @tap="selectType('scanner')">扫描设备</view>
					<view class="filter-option" :class="{'active': activeType === 'printer'}" @tap="selectType('printer')">打印设备</view>
					<view class="filter-option" :class="{'active': activeType === 'shelf'}" @tap="selectType('shelf')">货架/柜子</view>
					<view class="filter-option" :class="{'active': activeType === 'vehicle'}" @tap="selectType('vehicle')">运输工具</view>
					<view class="filter-option" :class="{'active': activeType === 'other'}" @tap="selectType('other')">其他设备</view>
				</view>
			</view>
			<view class="filter-actions">
				<view class="reset-btn" @tap="resetFilter">重置</view>
				<view class="confirm-btn" @tap="applyFilter">确认</view>
			</view>
		</view>
		
		<!-- 设备类型选项卡 -->
		<view class="device-tabs">
			<scroll-view scroll-x class="tabs-scroll-view">
				<view class="tab-item" :class="{'active': activeTab === 'all'}" @tap="changeTab('all')">全部</view>
				<view class="tab-item" :class="{'active': activeTab === 'scanner'}" @tap="changeTab('scanner')">扫描设备</view>
				<view class="tab-item" :class="{'active': activeTab === 'printer'}" @tap="changeTab('printer')">打印设备</view>
				<view class="tab-item" :class="{'active': activeTab === 'shelf'}" @tap="changeTab('shelf')">货架柜子</view>
				<view class="tab-item" :class="{'active': activeTab === 'scale'}" @tap="changeTab('scale')">电子秤</view>
				<view class="tab-item" :class="{'active': activeTab === 'vehicle'}" @tap="changeTab('vehicle')">运输工具</view>
			</scroll-view>
		</view>
		
		<!-- 设备列表 (改为网格布局) -->
		<view class="device-grid">
			<view class="device-card" v-for="(item, index) in deviceList" :key="index" @tap="viewDetail(item)">
				<image class="device-image" :src="item.image" mode="aspectFill"></image>
				<view class="device-info">
					<view class="device-title">{{item.title}}</view>
					<view class="device-tags">
						<text class="device-tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
					</view>
					<view class="device-price">¥{{item.price}}</view>
					<view class="device-meta">
						<view class="device-address">{{item.address}}</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view class="loading-more" v-if="hasMore">
			<text>正在加载更多...</text>
		</view>
		<view class="no-more" v-else>
			<text>没有更多数据了</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchKeyword: '',
				showFilterSection: false,
				activeLocation: 'all',
				activePrice: 'all',
				activeType: 'all',
				activeTab: 'all',
				hasMore: true,
				pageNum: 1,
				pageSize: 10,
				deviceList: [
					{
						id: 1,
						title: '9成新菜鸟智能电子秤',
						price: '380',
						location: '北京·海淀',
						type: '电子秤',
						images: ['/static/device/scale.jpg'],
						condition: '9成新',
						publishTime: '1小时前',
						contactInfo: {
							name: '王先生',
							phone: '13312345678'
						}
					},
					{
						id: 2,
						title: '二手标签打印机 小票热敏打印机',
						price: '580',
						location: '北京·朝阳',
						type: '打印机',
						images: ['/static/device/printer.jpg'],
						condition: '95成新',
						publishTime: '3小时前',
						contactInfo: {
							name: '李小姐',
							phone: '13588889999'
						}
					},
					{
						id: 3,
						title: '快递包装货架 五层置物架',
						price: '260',
						location: '北京·丰台',
						type: '货架',
						images: ['/static/device/shelf.jpg'],
						condition: '8成新',
						publishTime: '昨天',
						contactInfo: {
							name: '张经理',
							phone: '18612345678'
						}
					},
					{
						id: 4,
						title: '全新驿站收银系统 扫码枪套装',
						price: '1200',
						location: '北京·西城',
						type: '收银设备',
						images: ['/static/device/scanner.jpg'],
						condition: '全新',
						publishTime: '前天',
						contactInfo: {
							name: '赵先生',
							phone: '13912345678'
						}
					},
					{
						id: 5,
						title: '二手快递打包台 工作台',
						price: '350',
						location: '北京·通州',
						type: '工作台',
						images: ['/static/device/table.jpg'],
						condition: '7成新',
						publishTime: '3天前',
						contactInfo: {
							name: '刘小姐',
							phone: '13800138000'
						}
					}
				]
			}
		},
		onLoad() {
			// 页面加载时读取本地存储的数据
			this.loadStoredData();
		},
		onShow() {
			// 每次页面显示时重新加载数据，确保数据最新
			this.loadStoredData();
		},
		onReachBottom() {
			// 上拉加载更多
			if (this.hasMore) {
				this.loadMoreData();
			}
		},
		methods: {
			handleSearch() {
				// 处理搜索逻辑
				uni.showToast({
					title: '搜索：' + this.searchKeyword,
					icon: 'none'
				});
			},
			showFilter() {
				this.showFilterSection = !this.showFilterSection;
			},
			selectLocation(location) {
				this.activeLocation = location;
			},
			selectPrice(price) {
				this.activePrice = price;
			},
			selectType(type) {
				this.activeType = type;
			},
			resetFilter() {
				this.activeLocation = 'all';
				this.activePrice = 'all';
				this.activeType = 'all';
			},
			applyFilter() {
				// 应用筛选条件并刷新数据
				this.showFilterSection = false;
				uni.showToast({
					title: '筛选条件已应用',
					icon: 'none'
				});
				// 这里应该重新加载符合筛选条件的数据
			},
			changeTab(tab) {
				this.activeTab = tab;
				// 切换标签重新加载数据
				// 这里应该根据选中的标签筛选设备
			},
			viewDetail(item) {
				uni.navigateTo({
					url: '/pages/detail/detail?id=' + item.id + '&type=device'
				});
			},
			loadMoreData() {
				// 模拟加载更多数据
				setTimeout(() => {
					if (this.pageNum >= 3) {
						this.hasMore = false;
					} else {
						this.pageNum++;
						// 添加更多数据
						// 真实场景应该调用API获取更多数据
					}
				}, 1000);
			},
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			// 加载本地存储的数据
			loadStoredData() {
				const storedData = uni.getStorageSync('deviceList');
				if (storedData && storedData.length > 0) {
					// 使用存储的数据替换默认数据
					this.deviceList = storedData;
				}
			}
		}
	}
</script>

<style>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		position: relative;
	}
	
	.search-bar {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.search-input-box {
		flex: 1;
		background-color: #ffffff;
		border-radius: 60rpx;
		padding: 15rpx 30rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.search-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 15rpx;
	}
	
	.filter-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.filter-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.filter-section {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.filter-row {
		margin-bottom: 20rpx;
	}
	
	.filter-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 15rpx;
	}
	
	.filter-options {
		display: flex;
		flex-wrap: wrap;
	}
	
	.filter-option {
		padding: 10rpx 20rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		margin-right: 15rpx;
		margin-bottom: 15rpx;
		font-size: 24rpx;
		color: #666666;
	}
	
	.filter-option.active {
		background-color: #ff5a5f;
		color: #ffffff;
	}
	
	.filter-actions {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
	}
	
	.reset-btn, .confirm-btn {
		width: 45%;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 40rpx;
		font-size: 28rpx;
	}
	
	.reset-btn {
		background-color: #f5f5f5;
		color: #666666;
	}
	
	.confirm-btn {
		background-color: #ff5a5f;
		color: #ffffff;
	}
	
	.device-tabs {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
	}
	
	.tabs-scroll-view {
		white-space: nowrap;
		padding: 0 20rpx;
	}
	
	.tab-item {
		display: inline-block;
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		color: #666666;
		position: relative;
	}
	
	.tab-item.active {
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.tab-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background-color: #ff5a5f;
	}
	
	/* 网格布局 */
	.device-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-top: 20rpx;
	}
	
	.device-card {
		width: 345rpx;
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
	}
	
	.device-image {
		width: 100%;
		height: 345rpx;
		background-color: #f9f9f9;
	}
	
	.device-info {
		padding: 16rpx;
	}
	
	.device-title {
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
		margin-bottom: 12rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		height: 78rpx;
	}
	
	.device-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 12rpx;
	}
	
	.device-tag {
		font-size: 20rpx;
		padding: 4rpx 10rpx;
		background-color: #f8f8f8;
		color: #999999;
		margin-right: 10rpx;
		margin-bottom: 8rpx;
		border-radius: 6rpx;
	}
	
	.device-price {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff5a5f;
		margin-bottom: 8rpx;
	}
	
	.device-meta {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.device-address {
		font-size: 22rpx;
		color: #999999;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	.loading-more, .no-more {
		text-align: center;
		padding: 30rpx 0;
	}
	
	.loading-more text, .no-more text {
		font-size: 26rpx;
		color: #999999;
	}
</style> 