<template>
	<view class="container">
		<!-- 顶部城市选择和搜索框 -->
		<view class="header">
			<view class="city-selector">北京市</view>
			<view class="search-bar" @tap="showSearchPanel = true">
				<image src="/static/icons/search.png" mode="aspectFit" class="search-icon"></image>
				<text class="search-placeholder">搜索驿站/设备/职位</text>
			</view>
		</view>
		
		<!-- 搜索面板 -->
		<view class="search-panel" v-if="showSearchPanel">
			<!-- 搜索头部 -->
			<view class="search-panel-header">
				<view class="search-input-container">
					<image class="search-icon" src="/static/icons/search.png" mode="aspectFit"></image>
					<input 
						type="text" 
						placeholder="搜索驿站/设备/职位" 
						v-model="searchKeyword" 
						confirm-type="search" 
						focus 
						@confirm="handleSearch"
						@input="onSearchInput" 
					/>
					<view class="clear-btn" v-if="searchKeyword" @tap.stop="clearSearch">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="cancel-btn" @tap="closeSearchPanel">
					<text>取消</text>
				</view>
			</view>
			
			<!-- 搜索内容区域 -->
			<view class="search-panel-content">
				<!-- 搜索结果 -->
				<view class="search-results" v-if="searchKeyword && searchResults.length > 0">
					<view class="section-title">搜索结果</view>
					<view class="result-list">
						<view class="result-item" v-for="(item, index) in searchResults" :key="index" @tap="selectSearchResult(item)">
							<image src="/static/icons/search-result.png" mode="aspectFit" class="result-icon"></image>
							<rich-text class="result-text" :nodes="highlightKeyword(item.title)"></rich-text>
						</view>
					</view>
				</view>
				
				<!-- 无搜索结果提示 -->
				<view class="no-results" v-if="searchKeyword && isSearching && searchResults.length === 0">
					<image src="/static/icons/no-result.png" mode="aspectFit" class="no-result-icon"></image>
					<text class="no-result-text">未找到与"{{searchKeyword}}"相关的内容</text>
					<text class="no-result-tip">换个关键词试试吧</text>
				</view>
				
				<!-- 搜索历史 -->
				<view class="search-history" v-if="searchHistory.length > 0 && !searchKeyword">
					<view class="history-header">
						<text class="section-title">搜索历史</text>
						<view class="clear-history" @tap="clearHistory">
							<image src="/static/icons/delete.png" mode="aspectFit"></image>
							<text>清空</text>
						</view>
					</view>
					<view class="history-list">
						<view class="history-item" v-for="(item, index) in searchHistory" :key="index" @tap="useHistoryKeyword(item)">
							<image class="history-icon" src="/static/icons/history.png" mode="aspectFit"></image>
							<text>{{item}}</text>
						</view>
					</view>
				</view>
				
				<!-- 热门搜索 -->
				<view class="hot-searches" v-if="!searchKeyword">
					<view class="section-title">热门搜索</view>
					<view class="hot-list">
						<view class="hot-item" v-for="(item, index) in hotSearches" :key="index" @tap="useHistoryKeyword(item)">
							<view class="hot-rank" :class="'rank-' + (index + 1)" v-if="index < 3">{{index + 1}}</view>
							<text>{{item}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 轮播图区域 -->
		<swiper class="banner" indicator-dots autoplay circular>
			<swiper-item v-for="(item, index) in banners" :key="index">
				<image :src="item.image" mode="aspectFill"></image>
			</swiper-item>
		</swiper>
		
		<!-- 功能导航区域 -->
		<view class="nav-section">
			<view class="nav-item" @tap="navigateTo('/pages/transfer/transfer')">
				<image src="/static/icons/station.png" mode="aspectFit"></image>
				<text>驿站转让</text>
			</view>
			<view class="nav-item" @tap="navigateTo('/pages/device/device')">
				<image src="/static/icons/device.png" mode="aspectFit"></image>
				<text>设备交易</text>
			</view>
			<view class="nav-item" @tap="navigateTo('/pages/job/job')">
				<image src="/static/icons/job.png" mode="aspectFit"></image>
				<text>招聘求职</text>
			</view>
			<view class="nav-item" @tap="navigateTo('/pages/service/service')">
				<image src="/static/icons/service.png" mode="aspectFit"></image>
				<text>顶班服务</text>
			</view>
		</view>
		
		<!-- 驿站转让区域 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">驿站转让</text>
				<view class="view-more" @tap="navigateTo('/pages/transfer/transfer')">查看更多 ></view>
			</view>
			<view class="station-list">
				<view class="station-item" v-for="(item, index) in stationList" :key="index" @tap="viewDetail(item)">
					<image :src="item.image" mode="aspectFill" class="station-image"></image>
					<view class="station-info">
						<view class="station-title">{{item.title}}</view>
						<view class="station-address">{{item.address}}</view>
						<view class="station-price">¥{{item.price}}<text class="price-unit">{{item.priceUnit}}</text></view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 招聘求职区域 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">招聘求职</text>
				<view class="view-more" @tap="navigateTo('/pages/job/job')">查看更多 ></view>
			</view>
			<view class="job-list">
				<view class="job-item" v-for="(item, index) in jobList" :key="index" @tap="viewDetail(item)">
					<view class="job-header">
						<view class="job-title">{{item.title}}</view>
						<view class="job-salary">{{item.salary}}</view>
					</view>
					<view class="job-company">{{item.company}}</view>
					<view class="job-tags">
						<text class="job-tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 顶班服务区域 -->
		<view class="section">
			<view class="section-header">
				<text class="section-title">顶班服务</text>
				<view class="view-more" @tap="navigateTo('/pages/service/service')">查看更多 ></view>
			</view>
			<view class="service-list">
				<view class="service-item" v-for="(item, index) in serviceList" :key="index" @tap="viewDetail(item)">
					<view class="service-info">
						<view class="service-title">{{item.title}}</view>
						<view class="service-time">{{item.time}}</view>
						<view class="service-address">{{item.address}}</view>
					</view>
					<view class="service-price">{{item.price}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				banners: [
					{ image: '/static/images/banner1.jpg' },
					{ image: '/static/images/banner2.jpg' }
				],
				stationList: [
					{ 
						id: 1,
						title: '城北社区驿站转让', 
						address: '海淀区北太平庄', 
						price: '45000',
						priceUnit: '元',
						image: '/static/images/station1.jpg'
					},
					{ 
						id: 2,
						title: '校园驿站急转', 
						address: '朝阳区北苑路', 
						price: '3800',
						priceUnit: '元/月',
						image: '/static/images/station2.jpg'
					}
				],
				jobList: [
					{
						id: 1,
						title: '驿站分拣员',
						salary: '4500-5500元/月',
						company: '城北速递驿站',
						tags: ['五险', '餐补', '全职']
					},
					{
						id: 2,
						title: '快递配送员',
						salary: '8000-10000元/月',
						company: '优速快递',
						tags: ['无需经验', '兼职可选', '多劳多得']
					}
				],
				serviceList: [
					{
						id: 1,
						title: '社区驿站收发员顶班',
						time: '09:00-18:00',
						address: '海淀区中关村南大街',
						price: '200元/天'
					},
					{
						id: 2,
						title: '快递分拣打包顶班',
						time: '18:00-22:00',
						address: '朝阳区望京西园',
						price: '35元/小时'
					}
				],
				// 搜索相关数据
				searchKeyword: '',
				showSearchPanel: false,
				searchHistory: [],
				searchResults: [],
				isSearching: false,
				hotSearches: [
					'驿站转让',
					'电子秤',
					'驿站工作',
					'顶班服务',
					'快递柜',
					'打印机',
					'收银系统',
					'货架'
				]
			}
		},
		onLoad() {
			// 页面加载时从本地存储加载搜索历史
			this.loadSearchHistory();
		},
		methods: {
			navigateTo(url) {
				// 检查是否为 tabBar 页面
				if (url === '/pages/station/station') {
					uni.switchTab({
						url: url
					});
				} else {
					uni.navigateTo({
						url: url
					});
				}
			},
			viewDetail(item) {
				uni.navigateTo({
					url: '/pages/detail/detail?id=' + item.id + '&type=' + this.getTypeFromItem(item)
				});
			},
			getTypeFromItem(item) {
				if(item.priceUnit) return 'station';
				if(item.salary) return 'job';
				if(item.time) return 'service';
				return 'other';
			},
			
			// 打开搜索面板
			showSearch() {
				this.showSearchPanel = true;
			},
			
			// 关闭搜索面板
			closeSearchPanel() {
				this.showSearchPanel = false;
				this.searchKeyword = '';
				this.searchResults = [];
				this.isSearching = false;
			},
			
			// 清除搜索关键词
			clearSearch() {
				this.searchKeyword = '';
				this.searchResults = [];
				this.isSearching = false;
			},
			
			// 使用历史关键词
			useHistoryKeyword(keyword) {
				this.searchKeyword = keyword;
				this.performLiveSearch();
			},
			
			// 加载搜索历史
			loadSearchHistory() {
				const history = uni.getStorageSync('searchHistory');
				if (history) {
					this.searchHistory = JSON.parse(history);
				}
			},
			
			// 保存搜索历史
			saveSearchHistory(keyword) {
				// 如果关键词为空，不保存
				if (!keyword.trim()) return;
				
				// 移除旧的相同关键词
				let history = this.searchHistory.filter(item => item !== keyword);
				
				// 添加到历史开头
				history.unshift(keyword);
				
				// 只保留最近10条
				if (history.length > 10) {
					history = history.slice(0, 10);
				}
				
				// 更新本地和数据
				this.searchHistory = history;
				uni.setStorageSync('searchHistory', JSON.stringify(history));
			},
			
			// 清空搜索历史
			clearHistory() {
				uni.showModal({
					title: '提示',
					content: '确定要清空搜索历史吗？',
					success: (res) => {
						if (res.confirm) {
							this.searchHistory = [];
							uni.removeStorageSync('searchHistory');
						}
					}
				});
			},
			
			// 高亮关键词
			highlightKeyword(text) {
				if (!this.searchKeyword) return text;
				
				const keyword = this.searchKeyword.trim();
				const parts = text.split(new RegExp(`(${keyword})`, 'gi'));
				
				let result = '';
				parts.forEach(part => {
					if (part.toLowerCase() === keyword.toLowerCase()) {
						result += `<span style="color: #ff5a5f;">${part}</span>`;
					} else {
						result += part;
					}
				});
				
				return result;
			},
			
			// 搜索输入变化
			onSearchInput() {
				this.performLiveSearch();
			},
			
			// 执行实时搜索
			performLiveSearch() {
				// 如果关键词为空，清空结果
				if (!this.searchKeyword.trim()) {
					this.searchResults = [];
					this.isSearching = false;
					return;
				}
				
				this.isSearching = true;
				
				// 搜索所有列表数据
				const keyword = this.searchKeyword.toLowerCase();
				const results = [
					...this.stationList.map(item => ({...item, type: 'station'})),
					...this.jobList.map(item => ({...item, type: 'job'})),
					...this.serviceList.map(item => ({...item, type: 'service'}))
				].filter(item => {
					// 根据不同类型搜索不同字段
					if (item.type === 'station') {
						return item.title.toLowerCase().includes(keyword) || 
							   item.address.toLowerCase().includes(keyword);
					} else if (item.type === 'job') {
						return item.title.toLowerCase().includes(keyword) || 
							   item.company.toLowerCase().includes(keyword);
					} else if (item.type === 'service') {
						return item.title.toLowerCase().includes(keyword) || 
							   item.address.toLowerCase().includes(keyword);
					}
					return false;
				});
				
				this.searchResults = results;
				this.isSearching = false;
			},
			
			// 选择搜索结果
			selectSearchResult(item) {
				// 保存到搜索历史
				this.saveSearchHistory(this.searchKeyword);
				
				// 根据结果类型跳转到对应页面
				let targetPage = '';
				if (item.type === 'station') {
					targetPage = '/pages/transfer/transfer';
				} else if (item.type === 'job') {
					targetPage = '/pages/job/job';
				} else if (item.type === 'service') {
					targetPage = '/pages/service/service';
				} else if (item.type === 'device') {
					targetPage = '/pages/device/device';
				}
				
				// 关闭搜索面板
				this.closeSearchPanel();
				
				// 跳转到对应页面并携带搜索关键词
				if (targetPage) {
					uni.navigateTo({
						url: `${targetPage}?keyword=${encodeURIComponent(this.searchKeyword)}`
					});
				}
			},
			
			// 处理搜索提交
			handleSearch() {
				// 保存到搜索历史
				this.saveSearchHistory(this.searchKeyword);
				
				// 根据关键词判断跳转目标页面
				let targetPage = '';
				const keyword = this.searchKeyword.toLowerCase();
				
				// 根据关键词智能匹配目标页面
				if (keyword.includes('电子秤') || keyword.includes('打印机') || keyword.includes('货架') || 
					keyword.includes('出库') || keyword.includes('灯带') || keyword.includes('设备')) {
					targetPage = '/pages/device/device';
				} else if (keyword.includes('驿站') || keyword.includes('转让')) {
					targetPage = '/pages/transfer/transfer';
				} else if (keyword.includes('招聘') || keyword.includes('求职') || keyword.includes('工作')) {
					targetPage = '/pages/job/job';
				} else if (keyword.includes('顶班') || keyword.includes('服务')) {
					targetPage = '/pages/service/service';
				} else {
					// 默认跳转到设备页面
					targetPage = '/pages/device/device';
				}
				
				// 关闭搜索面板
				this.closeSearchPanel();
				
				// 跳转到对应页面并携带搜索关键词
				uni.navigateTo({
					url: `${targetPage}?keyword=${encodeURIComponent(this.searchKeyword)}`
				});
			}
		}
	}
</script>

<style>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
	}
	
	.header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.city-selector {
		font-size: 32rpx;
		font-weight: bold;
		padding-right: 20rpx;
	}
	
	.search-bar {
		flex: 1;
		background-color: #eee;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		height: 60rpx;
	}
	
	.search-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}
	
	.search-placeholder {
		color: #999;
		font-size: 28rpx;
	}
	
	/* 搜索面板样式 */
	.search-panel {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		z-index: 999;
		padding: 20rpx;
		display: flex;
		flex-direction: column;
	}
	
	.search-panel-header {
		display: flex;
		align-items: center;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.search-input-container {
		flex: 1;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		padding: 10rpx 20rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		height: 60rpx;
	}
	
	.search-input-container input {
		flex: 1;
		font-size: 28rpx;
		height: 60rpx;
	}
	
	.clear-btn {
		width: 40rpx;
		height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.clear-btn image {
		width: 24rpx;
		height: 24rpx;
	}
	
	.cancel-btn {
		font-size: 28rpx;
		color: #333;
	}
	
	.search-panel-content {
		flex: 1;
		overflow-y: auto;
		padding-top: 20rpx;
	}
	
	.section-title {
		font-size: 28rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
	}
	
	.result-list {
		margin-bottom: 30rpx;
	}
	
	.result-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.result-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 20rpx;
	}
	
	.result-text {
		font-size: 28rpx;
		color: #333;
	}
	
	.no-results {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 60rpx 0;
	}
	
	.no-result-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 20rpx;
	}
	
	.no-result-text {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
	}
	
	.no-result-tip {
		font-size: 26rpx;
		color: #999;
	}
	
	.history-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.clear-history {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #999;
	}
	
	.clear-history image {
		width: 24rpx;
		height: 24rpx;
		margin-right: 6rpx;
	}
	
	.history-list {
		margin-bottom: 30rpx;
	}
	
	.history-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.history-icon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 20rpx;
	}
	
	.history-item text {
		font-size: 28rpx;
		color: #333;
	}
	
	.hot-list {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 30rpx;
	}
	
	.hot-item {
		margin: 0 20rpx 20rpx 0;
		padding: 10rpx 20rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		font-size: 24rpx;
		color: #666;
		display: flex;
		align-items: center;
		position: relative;
	}
	
	.hot-rank {
		width: 30rpx;
		height: 30rpx;
		border-radius: 50%;
		font-size: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 10rpx;
		color: #fff;
	}
	
	.rank-1 {
		background-color: #ff5a5f;
	}
	
	.rank-2 {
		background-color: #ff9500;
	}
	
	.rank-3 {
		background-color: #34c759;
	}
	
	.banner {
		width: 100%;
		height: 300rpx;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 30rpx;
	}
	
	.banner image {
		width: 100%;
		height: 100%;
	}
	
	.nav-section {
		display: flex;
		justify-content: space-between;
		padding: 30rpx 0;
		background-color: #fff;
		border-radius: 12rpx;
		margin-bottom: 30rpx;
	}
	
	.nav-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 25%;
	}
	
	.nav-item image {
		width: 80rpx;
		height: 80rpx;
		margin-bottom: 10rpx;
	}
	
	.nav-item text {
		font-size: 26rpx;
		color: #333;
	}
	
	.section {
		background-color: #fff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
	}
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		position: relative;
		padding-left: 20rpx;
	}
	
	.section-title::before {
		content: '';
		position: absolute;
		left: 0;
		top: 6rpx;
		width: 6rpx;
		height: 30rpx;
		background-color: #ff5a5f;
		border-radius: 3rpx;
	}
	
	.view-more {
		font-size: 24rpx;
		color: #999;
	}
	
	.station-list, .job-list, .service-list {
		display: flex;
		flex-direction: column;
	}
	
	.station-item {
		display: flex;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.station-image {
		width: 180rpx;
		height: 140rpx;
		border-radius: 8rpx;
		margin-right: 20rpx;
	}
	
	.station-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
	
	.station-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
	}
	
	.station-address {
		font-size: 24rpx;
		color: #666;
	}
	
	.station-price {
		font-size: 30rpx;
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.price-unit {
		font-size: 22rpx;
		font-weight: normal;
		margin-left: 6rpx;
	}
	
	.job-item {
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.job-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.job-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
	}
	
	.job-salary {
		font-size: 26rpx;
		color: #ff5a5f;
	}
	
	.job-company {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 10rpx;
	}
	
	.job-tags {
		display: flex;
		flex-wrap: wrap;
	}
	
	.job-tag {
		font-size: 22rpx;
		color: #666;
		padding: 4rpx 12rpx;
		background-color: #f5f5f5;
		border-radius: 4rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
	}
	
	.service-item {
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	
	.service-info {
		flex: 1;
	}
	
	.service-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}
	
	.service-time {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 10rpx;
	}
	
	.service-address {
		font-size: 24rpx;
		color: #999;
	}
	
	.service-price {
		font-size: 26rpx;
		color: #ff5a5f;
	}
</style>
