<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input-box">
				<image class="search-icon" src="/static/icons/search.png" mode="aspectFit"></image>
				<input type="text" placeholder="搜索顶班位置/时间" v-model="searchKeyword" confirm-type="search" @confirm="handleSearch" />
			</view>
			<view class="filter-btn" @tap="showFilter">
				<image src="/static/icons/filter.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 快捷分类 -->
		<view class="quick-tags">
			<scroll-view scroll-x class="tags-scroll-view">
				<view class="tag-item" :class="{'active': activeShift === 'all'}" @tap="selectShift('all')">全部时段</view>
				<view class="tag-item" :class="{'active': activeShift === 'morning'}" @tap="selectShift('morning')">上午班</view>
				<view class="tag-item" :class="{'active': activeShift === 'afternoon'}" @tap="selectShift('afternoon')">下午班</view>
				<view class="tag-item" :class="{'active': activeShift === 'evening'}" @tap="selectShift('evening')">晚班</view>
				<view class="tag-item" :class="{'active': activeShift === 'fullday'}" @tap="selectShift('fullday')">全天班</view>
				<view class="tag-item" :class="{'active': activeShift === 'urgent'}" @tap="selectShift('urgent')">急聘</view>
			</scroll-view>
		</view>
		
		<!-- 筛选区域 -->
		<view class="filter-section" v-if="showFilterSection">
			<view class="filter-row">
				<view class="filter-title">区域</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': activeArea === 'all'}" @tap="selectArea('all')">全部</view>
					<view class="filter-option" :class="{'active': activeArea === 'haidian'}" @tap="selectArea('haidian')">海淀区</view>
					<view class="filter-option" :class="{'active': activeArea === 'chaoyang'}" @tap="selectArea('chaoyang')">朝阳区</view>
					<view class="filter-option" :class="{'active': activeArea === 'dongcheng'}" @tap="selectArea('dongcheng')">东城区</view>
					<view class="filter-option" :class="{'active': activeArea === 'xicheng'}" @tap="selectArea('xicheng')">西城区</view>
				</view>
			</view>
			<view class="filter-row">
				<view class="filter-title">薪资</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': activeSalary === 'all'}" @tap="selectSalary('all')">全部</view>
					<view class="filter-option" :class="{'active': activeSalary === 'low'}" @tap="selectSalary('low')">150元以下/天</view>
					<view class="filter-option" :class="{'active': activeSalary === 'medium'}" @tap="selectSalary('medium')">150-200元/天</view>
					<view class="filter-option" :class="{'active': activeSalary === 'high'}" @tap="selectSalary('high')">200元以上/天</view>
				</view>
			</view>
			<view class="filter-row">
				<view class="filter-title">工作时间</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': activeTime === 'all'}" @tap="selectTime('all')">全部</view>
					<view class="filter-option" :class="{'active': activeTime === 'morning'}" @tap="selectTime('morning')">上午</view>
					<view class="filter-option" :class="{'active': activeTime === 'afternoon'}" @tap="selectTime('afternoon')">下午</view>
					<view class="filter-option" :class="{'active': activeTime === 'evening'}" @tap="selectTime('evening')">晚上</view>
					<view class="filter-option" :class="{'active': activeTime === 'fullday'}" @tap="selectTime('fullday')">全天</view>
				</view>
			</view>
			<view class="filter-actions">
				<view class="reset-btn" @tap="resetFilter">重置</view>
				<view class="confirm-btn" @tap="applyFilter">确认</view>
			</view>
		</view>
		
		<!-- 顶班服务列表 -->
		<view class="service-list">
			<view class="service-item" v-for="(item, index) in serviceList" :key="index" @tap="viewDetail(item)">
				<view class="service-top">
					<view class="service-title">{{item.title}}</view>
					<view class="service-price">{{item.price}}</view>
				</view>
				<view class="service-time">
					<image src="/static/icons/time.png" mode="aspectFit"></image>
					<text>{{item.time}}</text>
				</view>
				<view class="service-address">
					<image src="/static/icons/location.png" mode="aspectFit"></image>
					<text>{{item.address}}</text>
				</view>
				<view class="service-tags">
					<text class="service-tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
				</view>
				<view class="service-bottom">
					<view class="service-contact" @tap.stop="contactService(item)">
						<image src="/static/icons/contact.png" mode="aspectFit"></image>
						<text>联系</text>
					</view>
					<view class="service-time-ago">{{item.publishTime}}</view>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view class="loading-more" v-if="hasMore">
			<text>正在加载更多...</text>
		</view>
		<view class="no-more" v-else>
			<text>没有更多数据了</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchKeyword: '',
				showFilterSection: false,
				activeShift: 'all',
				activeArea: 'all',
				activeSalary: 'all',
				activeTime: 'all',
				hasMore: true,
				pageNum: 1,
				pageSize: 10,
				serviceList: [
					{
						id: 1,
						title: '朝阳区劲松地铁站附近顶班',
						price: '180元/天',
						time: '全天班（8小时）',
						address: '朝阳区劲松地铁站北口200米',
						tags: ['快递员', '中通快递', '日均100件'],
						publishTime: '1小时前',
						contactInfo: {
							name: '王师傅',
							phone: '13312345678'
						}
					},
					{
						id: 2,
						title: '海淀区中关村顶班一个月',
						price: '210元/天',
						time: '全天班（9小时）',
						address: '海淀区中关村大街28号',
						tags: ['快递员', '顺丰速运', '日均80件'],
						publishTime: '3小时前',
						contactInfo: {
							name: '李师傅',
							phone: '13687654321'
						}
					},
					{
						id: 3,
						title: '丰台区角门西地铁站附近驿站临时工',
						price: '160元/天',
						time: '上午班（5小时）',
						address: '丰台区角门西路甲1号',
						tags: ['驿站业务员', '京东物流', '日均200件'],
						publishTime: '5小时前',
						contactInfo: {
							name: '张经理',
							phone: '13898765432'
						}
					},
					{
						id: 4,
						title: '西城区西单商圈快递员急需顶班',
						price: '230元/天',
						time: '全天班（10小时）',
						address: '西城区西单北大街155号',
						tags: ['快递员', '百世快递', '日均150件'],
						publishTime: '昨天',
						contactInfo: {
							name: '刘师傅',
							phone: '13765432109'
						}
					},
					{
						id: 5,
						title: '东城区东直门驿站急招分拣员',
						price: '170元/天',
						time: '晚班（4小时）',
						address: '东城区东直门外大街42号',
						tags: ['分拣员', '圆通速递', '日均300件'],
						publishTime: '前天',
						contactInfo: {
							name: '赵经理',
							phone: '13456789012'
						}
					}
				]
			}
		},
		onLoad() {
			// 页面加载时读取本地存储的数据
			this.loadStoredData();
		},
		onShow() {
			// 每次页面显示时重新加载数据，确保数据最新
			this.loadStoredData();
		},
		onReachBottom() {
			// 上拉加载更多
			if (this.hasMore) {
				this.loadMoreData();
			}
		},
		methods: {
			handleSearch() {
				// 处理搜索逻辑
				uni.showToast({
					title: '搜索：' + this.searchKeyword,
					icon: 'none'
				});
			},
			showFilter() {
				this.showFilterSection = !this.showFilterSection;
			},
			selectShift(shift) {
				this.activeShift = shift;
				// 根据选中的班次筛选数据
				uni.showToast({
					title: '已选择：' + shift,
					icon: 'none'
				});
			},
			selectArea(area) {
				this.activeArea = area;
			},
			selectSalary(salary) {
				this.activeSalary = salary;
			},
			selectTime(time) {
				this.activeTime = time;
			},
			resetFilter() {
				this.activeArea = 'all';
				this.activeSalary = 'all';
				this.activeTime = 'all';
			},
			applyFilter() {
				// 应用筛选条件并刷新数据
				this.showFilterSection = false;
				uni.showToast({
					title: '筛选条件已应用',
					icon: 'none'
				});
				// 这里应该重新加载符合筛选条件的数据
			},
			viewDetail(item) {
				uni.navigateTo({
					url: '/pages/detail/detail?id=' + item.id + '&type=service'
				});
			},
			contactService(item) {
				uni.makePhoneCall({
					phoneNumber: item.contactInfo.phone
				});
			},
			loadMoreData() {
				// 模拟加载更多数据
				setTimeout(() => {
					if (this.pageNum >= 3) {
						this.hasMore = false;
					} else {
						this.pageNum++;
						// 添加更多数据
						// 真实场景应该调用API获取更多数据
					}
				}, 1000);
			},
			// 加载本地存储的数据
			loadStoredData() {
				const storedData = uni.getStorageSync('serviceList');
				if (storedData && storedData.length > 0) {
					// 使用存储的数据替换默认数据
					this.serviceList = storedData;
				}
			}
		}
	}
</script>

<style>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		position: relative;
	}
	
	.search-bar {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.search-input-box {
		flex: 1;
		background-color: #ffffff;
		border-radius: 60rpx;
		padding: 15rpx 30rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.search-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 15rpx;
	}
	
	.filter-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.filter-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	/* 快捷分类标签 */
	.quick-tags {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx 0;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.tags-scroll-view {
		white-space: nowrap;
		padding: 0 20rpx;
	}
	
	.tag-item {
		display: inline-block;
		padding: 10rpx 30rpx;
		margin-right: 15rpx;
		font-size: 26rpx;
		color: #666666;
		background-color: #f5f5f5;
		border-radius: 30rpx;
	}
	
	.tag-item.active {
		background-color: #ff5a5f;
		color: #ffffff;
	}
	
	.filter-section {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.filter-row {
		margin-bottom: 20rpx;
	}
	
	.filter-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 15rpx;
	}
	
	.filter-options {
		display: flex;
		flex-wrap: wrap;
	}
	
	.filter-option {
		padding: 10rpx 20rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		margin-right: 15rpx;
		margin-bottom: 15rpx;
		font-size: 24rpx;
		color: #666666;
	}
	
	.filter-option.active {
		background-color: #ff5a5f;
		color: #ffffff;
	}
	
	.filter-actions {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
	}
	
	.reset-btn, .confirm-btn {
		width: 45%;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 40rpx;
		font-size: 28rpx;
	}
	
	.reset-btn {
		background-color: #f5f5f5;
		color: #666666;
	}
	
	.confirm-btn {
		background-color: #ff5a5f;
		color: #ffffff;
	}
	
	.service-list {
		margin-top: 10rpx;
	}
	
	.service-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 25rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.service-top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
	}
	
	.service-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		max-width: 70%;
	}
	
	.service-price {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff5a5f;
	}
	
	.service-time, .service-address {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
		font-size: 26rpx;
		color: #666666;
	}
	
	.service-time image, .service-address image {
		width: 28rpx;
		height: 28rpx;
		margin-right: 10rpx;
	}
	
	.service-tags {
		display: flex;
		flex-wrap: wrap;
		margin: 15rpx 0;
	}
	
	.service-tag {
		padding: 6rpx 16rpx;
		background-color: #f8f8f8;
		border-radius: 6rpx;
		font-size: 22rpx;
		color: #666666;
		margin-right: 15rpx;
		margin-bottom: 10rpx;
	}
	
	.service-bottom {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 15rpx;
		padding-top: 15rpx;
		border-top: 1rpx solid #f0f0f0;
	}
	
	.service-contact {
		display: flex;
		align-items: center;
		background-color: #ff5a5f;
		color: #ffffff;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		font-size: 24rpx;
	}
	
	.service-contact image {
		width: 24rpx;
		height: 24rpx;
		margin-right: 8rpx;
	}
	
	.service-time-ago {
		font-size: 24rpx;
		color: #999999;
	}
	
	.loading-more, .no-more {
		text-align: center;
		padding: 30rpx 0;
	}
	
	.loading-more text, .no-more text {
		font-size: 26rpx;
		color: #999999;
	}
</style> 