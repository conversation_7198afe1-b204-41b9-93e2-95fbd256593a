<template>
	<view class="container">
		<!-- 固定头部 -->
		<view class="header">
			<text class="title">消息中心</text>
		</view>
		
		<!-- 消息类型选择 -->
		<view class="message-types">
			<view 
				v-for="(tab, index) in tabs" 
				:key="index" 
				:class="['type-item', activeTab === index ? 'active' : '']"
				@tap="switchTab(index)"
			>
				<text>{{tab.name}}</text>
			</view>
		</view>
		
		<!-- 消息列表 -->
		<scroll-view 
			scroll-y 
			class="message-list"
			:refresher-enabled="true"
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
			show-scrollbar="false"
		>
			<view v-if="filteredMessages.length === 0" class="empty-tip">
				<image src="/static/icons/empty.png" mode="aspectFit"></image>
				<text>暂无消息</text>
			</view>
			
			<view v-else>
				<view 
					v-for="(message, index) in filteredMessages" 
					:key="index"
					class="message-item"
					:class="message.type"
					@tap="viewMessageDetail(message)"
				>
					<view class="message-avatar">
						<image :src="getMessageIcon(message.type)" mode="aspectFill"></image>
					</view>
					<view class="message-content">
						<view class="message-header">
							<text class="message-title">{{message.title}}</text>
							<text class="message-time">{{formatTime(message.time)}}</text>
						</view>
						<view class="message-desc">{{message.content}}</view>
					</view>
					<view class="unread-badge" v-if="message.unread > 0">{{message.unread}}</view>
				</view>
			</view>
			
			<view class="no-more" v-if="filteredMessages.length > 0">
				<text>没有更多消息了</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabs: [
					{ name: '全部', type: 'all' },
					{ name: '系统通知', type: 'system' },
					{ name: '官方客服', type: 'service' }
				],
				activeTab: 0,
				isRefreshing: false,
				messages: [
					{
						id: 1,
						type: 'system',
						title: '系统通知',
						content: '欢迎使用驿站帮Pro，希望能帮助您更好地管理驿站业务！',
						time: Date.now() - 3600000, // 1小时前
						unread: 1
					},
					{
						id: 2,
						type: 'service',
						title: '官方客服',
						content: '您好，有任何使用问题可以随时咨询我们的客服团队。',
						time: Date.now() - 86400000, // 1天前
						unread: 2
					},
					{
						id: 3,
						type: 'system',
						title: '认证提醒',
						content: '您的驿站身份认证已通过审核，现在可以使用更多功能啦！',
						time: Date.now() - 172800000, // 2天前
						unread: 0
					},
					{
						id: 4,
						type: 'service',
						title: '订单通知',
						content: '您有一个新的订单已经确认，请及时处理。',
						time: Date.now() - 259200000, // 3天前
						unread: 0
					},
					{
						id: 5,
						type: 'system',
						title: '系统更新',
						content: '系统已更新到最新版本，新增了多项功能和优化体验。',
						time: Date.now() - 432000000, // 5天前
						unread: 0
					}
				]
			}
		},
		computed: {
			filteredMessages() {
				if (this.activeTab === 0) {
					return this.messages;
				} else {
					const type = this.tabs[this.activeTab].type;
					return this.messages.filter(msg => msg.type === type);
				}
			}
		},
		methods: {
			// 切换标签
			switchTab(index) {
				this.activeTab = index;
			},
			
			// 下拉刷新
			onRefresh() {
				this.isRefreshing = true;
				// 模拟刷新数据
				setTimeout(() => {
					// 随机添加一条新消息
					const types = ['system', 'service'];
					const titles = {
						system: ['系统通知', '安全提醒', '更新提示'],
						service: ['官方客服', '订单通知', '售后服务']
					};
					const contents = [
						'您的账号安全等级较低，建议设置更复杂的密码。',
						'您有一个新的通知，请注意查看。',
						'系统将于今晚22:00进行例行维护，期间服务可能短暂不可用。'
					];
					
					const type = types[Math.floor(Math.random() * types.length)];
					const title = titles[type][Math.floor(Math.random() * titles[type].length)];
					const content = contents[Math.floor(Math.random() * contents.length)];
					
					// 在列表前添加新消息
					this.messages.unshift({
						id: this.messages.length + 1,
						type: type,
						title: title,
						content: content,
						time: Date.now(),
						unread: 1
					});
					
					this.isRefreshing = false;
				}, 1000);
			},
			
			// 获取消息图标
			getMessageIcon(type) {
				switch (type) {
					case 'system':
						return '/static/icons/system-notice.png';
					case 'service':
						return '/static/icons/service.png';
					default:
						return '/static/icons/system-notice.png';
				}
			},
			
			// 格式化时间
			formatTime(timestamp) {
				const now = Date.now();
				const diff = now - timestamp;
				
				// 一小时内
				if (diff < 3600000) {
					return '刚刚';
				}
				// 一天内
				else if (diff < 86400000) {
					return '今天 ' + new Date(timestamp).toTimeString().substr(0, 5);
				}
				// 昨天
				else if (diff < 172800000) {
					return '昨天 ' + new Date(timestamp).toTimeString().substr(0, 5);
				}
				// 一周内
				else if (diff < 604800000) {
					return Math.floor(diff / 86400000) + '天前';
				}
				// 超过一周
				else {
					const date = new Date(timestamp);
					return `${date.getMonth() + 1}月${date.getDate()}日`;
				}
			},
			
			// 查看消息详情
			viewMessageDetail(message) {
				// 更新消息为已读
				if (message.unread > 0) {
					const index = this.messages.findIndex(m => m.id === message.id);
					if (index !== -1) {
						this.messages[index].unread = 0;
					}
				}
				
				// 跳转到详情页
				uni.navigateTo({
					url: `/pages/message/detail?id=${message.id}&type=${message.type}`
				});
			}
		}
	}
</script>

<style>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
	}
	
	/* 头部样式 */
	.header {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 90rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
		position: sticky;
		top: 0;
		z-index: 10;
	}
	
	.title {
		font-size: 34rpx;
		font-weight: bold;
		color: #333333;
	}
	
	/* 分类标签样式 */
	.message-types {
		display: flex;
		background-color: #ffffff;
		margin-bottom: 20rpx;
		overflow: hidden;
		height: 90rpx;
		position: sticky;
		top: 90rpx;
		z-index: 9;
	}
	
	.type-item {
		flex: 1;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #666666;
		position: relative;
	}
	
	.type-item.active {
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.type-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 4rpx;
		background-color: #ff5a5f;
	}
	
	/* 消息列表样式 */
	.message-list {
		flex: 1;
		padding: 0 20rpx;
	}
	
	/* 空消息提示 */
	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 200rpx;
	}
	
	.empty-tip image {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}
	
	.empty-tip text {
		font-size: 28rpx;
		color: #999999;
	}
	
	/* 消息项样式 */
	.message-item {
		display: flex;
		align-items: flex-start;
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		position: relative;
		/* 提高渲染性能 */
		transform: translateZ(0);
	}
	
	.message-avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-right: 20rpx;
		overflow: hidden;
		background-color: #f5f5f5;
	}
	
	.message-avatar image {
		width: 100%;
		height: 100%;
	}
	
	.message-content {
		flex: 1;
	}
	
	.message-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}
	
	.message-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.message-time {
		font-size: 24rpx;
		color: #999999;
	}
	
	.message-desc {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.5;
	}
	
	/* 未读标记 */
	.unread-badge {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		min-width: 36rpx;
		height: 36rpx;
		background-color: #ff5a5f;
		border-radius: 18rpx;
		color: #ffffff;
		font-size: 24rpx;
		text-align: center;
		line-height: 36rpx;
		padding: 0 8rpx;
	}
	
	/* 系统消息样式 */
	.message-item.system .message-avatar {
		background-color: #FFF2F2;
	}
	
	/* 客服消息样式 */
	.message-item.service .message-avatar {
		background-color: #F2F7FF;
	}
	
	/* 底部加载提示 */
	.no-more {
		text-align: center;
		padding: 30rpx 0;
	}
	
	.no-more text {
		font-size: 26rpx;
		color: #999999;
	}
</style> 