<template>
	<view class="container">
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<view class="loading-spinner"></view>
			<text class="loading-text">正在加载...</text>
		</view>
		
		<!-- 错误提示 -->
		<view class="error-container" v-else-if="error">
			<image src="/static/icons/error.png" mode="aspectFit" class="error-icon"></image>
			<text class="error-text">{{error}}</text>
			<view class="retry-btn" @tap="retryLoading">重新加载</view>
		</view>
		
		<!-- 内容为空提示 -->
		<view class="empty-container" v-else-if="isEmpty">
			<image src="/static/icons/empty.png" mode="aspectFit" class="empty-icon"></image>
			<text class="empty-text">暂无数据</text>
		</view>
		
		<!-- 内容区域 - 仅在非加载、非错误、非空时显示 -->
		<block v-else>
			<!-- 公共顶部操作栏 -->
			<view class="action-bar">
				<view class="action-left">
					<view class="action-btn" @tap="goBack">
						<image src="/static/icons/back.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="action-right">
					<view class="action-btn" @tap="toggleCollect">
						<image :src="isCollected ? '/static/icons/collected.png' : '/static/icons/collect.png'" mode="aspectFit"></image>
					</view>
					<view class="action-btn" @tap="shareItem">
						<image src="/static/icons/share.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			
			<!-- 驿站转让详情 -->
			<block v-if="detailType === 'station'">
				<!-- 图片轮播 -->
				<swiper class="detail-swiper" indicator-dots autoplay circular>
					<swiper-item v-for="(img, index) in stationDetail.images" :key="index">
						<image :src="img" mode="aspectFill" class="swiper-image"></image>
					</swiper-item>
				</swiper>
				
				<!-- 基本信息 -->
				<view class="detail-section">
					<view class="detail-title">{{stationDetail.title}}</view>
					<view class="detail-price">¥{{stationDetail.price}}<text class="price-unit">{{stationDetail.priceUnit}}</text></view>
					<view class="detail-tags">
						<text class="detail-tag" v-for="(tag, index) in stationDetail.tags" :key="index">{{tag}}</text>
					</view>
					<view class="detail-publish-info">
						<text>发布时间: {{stationDetail.publishTime}}</text>
						<text>浏览: {{stationDetail.viewCount}}次</text>
					</view>
				</view>
				
				<!-- 联系信息 -->
				<view class="detail-section">
					<view class="section-title">联系方式</view>
					<view class="contact-info">
						<view class="contact-item">
							<text class="contact-label">联系人:</text>
							<text class="contact-value">{{stationDetail.contact?.name || '未提供'}}</text>
						</view>
						<view class="contact-item">
							<text class="contact-label">联系电话:</text>
							<text class="contact-value">{{stationDetail.contact?.phone || '未提供'}}</text>
							<view class="contact-btn" @tap="callPhone(stationDetail.contact?.phone)">
								<image src="/static/icons/phone.png" mode="aspectFit"></image>
								<text>拨打</text>
							</view>
						</view>
						<view class="contact-item" v-if="stationDetail.contact?.wechat">
							<text class="contact-label">微信:</text>
							<text class="contact-value">{{stationDetail.contact?.wechat}}</text>
							<view class="contact-btn" @tap="copyWechat(stationDetail.contact?.wechat)">
								<image src="/static/icons/wechat.png" mode="aspectFit"></image>
								<text>复制</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 驿站信息 -->
				<view class="detail-section">
					<view class="section-title">驿站信息</view>
					<view class="station-info">
						<view class="info-item">
							<text class="info-label">地址:</text>
							<text class="info-value">{{stationDetail.address || '未提供'}}</text>
						</view>
						<view class="info-item">
							<text class="info-label">面积:</text>
							<text class="info-value">{{stationDetail.area || '0'}}㎡</text>
						</view>
						<view class="info-item">
							<text class="info-label">月营业额:</text>
							<text class="info-value">{{stationDetail.monthlyIncome || '0'}}元</text>
						</view>
						<view class="info-item">
							<text class="info-label">日派件量:</text>
							<text class="info-value">{{stationDetail.dailyPackage || '0'}}件</text>
						</view>
						<view class="info-item">
							<text class="info-label">转让原因:</text>
							<text class="info-value">{{stationDetail.reason || '未提供'}}</text>
						</view>
					</view>
				</view>
				
				<!-- 详细描述 -->
				<view class="detail-section">
					<view class="section-title">详细描述</view>
					<view class="detail-description">
						<text>{{stationDetail.description || '暂无详细描述'}}</text>
					</view>
				</view>
			</block>
			
			<!-- 职位详情 -->
			<block v-if="detailType === 'job'">
				<!-- 基本信息 -->
				<view class="detail-section">
					<view class="detail-title">{{jobDetail.title}}</view>
					<view class="detail-price">{{jobDetail.salary}}</view>
					<view class="detail-company">{{jobDetail.company}}</view>
					<view class="detail-address">{{jobDetail.address}}</view>
					<view class="detail-tags">
						<text class="detail-tag" v-for="(tag, index) in jobDetail.tags" :key="index">{{tag}}</text>
					</view>
					<view class="detail-publish-info">
						<text>发布时间: {{jobDetail.publishTime}}</text>
						<text>浏览: {{jobDetail.viewCount}}次</text>
					</view>
				</view>
				
				<!-- 职位要求 -->
				<view class="detail-section">
					<view class="section-title">职位要求</view>
					<view class="job-requirements">
						<view class="requirement-item">
							<text class="requirement-label">经验要求:</text>
							<text class="requirement-value">{{jobDetail.experience}}</text>
						</view>
						<view class="requirement-item">
							<text class="requirement-label">学历要求:</text>
							<text class="requirement-value">{{jobDetail.education}}</text>
						</view>
						<view class="requirement-item">
							<text class="requirement-label">年龄要求:</text>
							<text class="requirement-value">{{jobDetail.ageRequirement}}</text>
						</view>
						<view class="requirement-item">
							<text class="requirement-label">工作性质:</text>
							<text class="requirement-value">{{jobDetail.jobType}}</text>
						</view>
						<view class="requirement-item">
							<text class="requirement-label">工作时间:</text>
							<text class="requirement-value">{{jobDetail.workTime}}</text>
						</view>
					</view>
				</view>
				
				<!-- 工作内容 -->
				<view class="detail-section">
					<view class="section-title">工作内容</view>
					<view class="job-description">
						<text>{{jobDetail.jobDescription}}</text>
					</view>
				</view>
				
				<!-- 福利待遇 -->
				<view class="detail-section">
					<view class="section-title">福利待遇</view>
					<view class="job-benefits">
						<text>{{jobDetail.benefits}}</text>
					</view>
				</view>
				
				<!-- 联系信息 -->
				<view class="detail-section">
					<view class="section-title">联系方式</view>
					<view class="contact-info">
						<view class="contact-item">
							<text class="contact-label">联系人:</text>
							<text class="contact-value">{{jobDetail.contact?.name || '未提供'}}</text>
						</view>
						<view class="contact-item">
							<text class="contact-label">联系电话:</text>
							<text class="contact-value">{{jobDetail.contact?.phone || '未提供'}}</text>
							<view class="contact-btn" @tap="callPhone(jobDetail.contact?.phone)">
								<image src="/static/icons/phone.png" mode="aspectFit"></image>
								<text>拨打</text>
							</view>
						</view>
					</view>
				</view>
			</block>
			
			<!-- 设备详情 -->
			<block v-if="detailType === 'device'">
				<!-- 图片轮播 -->
				<swiper class="detail-swiper" indicator-dots autoplay circular>
					<swiper-item v-for="(img, index) in deviceDetail.images" :key="index">
						<image :src="img" mode="aspectFill" class="swiper-image"></image>
					</swiper-item>
				</swiper>
				
				<!-- 基本信息 -->
				<view class="detail-section">
					<view class="detail-title">{{deviceDetail.title}}</view>
					<view class="detail-price">¥{{deviceDetail.price}}</view>
					<view class="detail-address">{{deviceDetail.location}}</view>
					<view class="detail-tags">
						<text class="detail-tag">{{deviceDetail.type}}</text>
						<text class="detail-tag">{{deviceDetail.condition}}</text>
					</view>
					<view class="detail-publish-info">
						<text>发布时间: {{deviceDetail.publishTime}}</text>
					</view>
				</view>
				
				<!-- 设备描述 -->
				<view class="detail-section">
					<view class="section-title">设备描述</view>
					<view class="detail-description">
						<text>{{deviceDetail.description || '卖家暂未提供详细描述'}}</text>
					</view>
				</view>
				
				<!-- 联系信息 -->
				<view class="detail-section">
					<view class="section-title">联系方式</view>
					<view class="contact-info">
						<view class="contact-item">
							<text class="contact-label">联系人:</text>
							<text class="contact-value">{{deviceDetail.contactInfo?.name || '未提供'}}</text>
						</view>
						<view class="contact-item">
							<text class="contact-label">联系电话:</text>
							<text class="contact-value">{{deviceDetail.contactInfo?.phone || '未提供'}}</text>
							<view class="contact-btn" @tap="callPhone(deviceDetail.contactInfo?.phone)">
								<image src="/static/icons/phone.png" mode="aspectFit"></image>
								<text>拨打</text>
							</view>
						</view>
						<view class="contact-item" v-if="deviceDetail.contactInfo?.wechat">
							<text class="contact-label">微信:</text>
							<text class="contact-value">{{deviceDetail.contactInfo.wechat}}</text>
							<view class="contact-btn" @tap="copyWechat(deviceDetail.contactInfo.wechat)">
								<image src="/static/icons/wechat.png" mode="aspectFit"></image>
								<text>复制</text>
							</view>
						</view>
					</view>
				</view>
			</block>
			
			<!-- 服务详情 -->
			<block v-if="detailType === 'service'">
				<!-- 基本信息 -->
				<view class="detail-section">
					<view class="detail-title">{{serviceDetail.title}}</view>
					<view class="detail-price">¥{{serviceDetail.price}}</view>
					<view class="detail-address" v-if="serviceDetail.address">{{serviceDetail.address}}</view>
					<view class="detail-tags" v-if="serviceDetail.tags && serviceDetail.tags.length">
						<text class="detail-tag" v-for="(tag, index) in serviceDetail.tags" :key="index">{{tag}}</text>
					</view>
					<view class="detail-publish-info">
						<text>发布时间: {{serviceDetail.publishTime || '未知'}}</text>
						<text>浏览: {{serviceDetail.viewCount || 0}}次</text>
					</view>
				</view>
				
				<!-- 服务描述 -->
				<view class="detail-section">
					<view class="section-title">服务描述</view>
					<view class="detail-description">
						<text>{{serviceDetail.description || '暂无服务描述'}}</text>
					</view>
				</view>
				
				<!-- 服务信息 -->
				<view class="detail-section">
					<view class="section-title">服务信息</view>
					<view class="service-info">
						<view class="info-item">
							<text class="info-label">服务类型:</text>
							<text class="info-value">{{serviceDetail.serviceType || '未提供'}}</text>
						</view>
						<view class="info-item">
							<text class="info-label">服务时间:</text>
							<text class="info-value">{{serviceDetail.serviceTime || '未提供'}}</text>
						</view>
						<view class="info-item">
							<text class="info-label">工作周期:</text>
							<text class="info-value">{{serviceDetail.servicePeriod || '未提供'}}</text>
						</view>
						<view class="info-item">
							<text class="info-label">特殊要求:</text>
							<text class="info-value">{{serviceDetail.specialRequirements || '无'}}</text>
						</view>
					</view>
				</view>
				
				<!-- 联系信息 -->
				<view class="detail-section">
					<view class="section-title">联系方式</view>
					<view class="contact-info">
						<view class="contact-item">
							<text class="contact-label">联系人:</text>
							<text class="contact-value">{{serviceDetail.contact?.name || '未提供'}}</text>
						</view>
						<view class="contact-item">
							<text class="contact-label">联系电话:</text>
							<text class="contact-value">{{serviceDetail.contact?.phone || '未提供'}}</text>
							<view class="contact-btn" @tap="callPhone(serviceDetail.contact?.phone)">
								<image src="/static/icons/phone.png" mode="aspectFit"></image>
								<text>拨打</text>
							</view>
						</view>
					</view>
				</view>
			</block>
			
			<!-- 产品详情 -->
			<block v-if="detailType === 'product'">
				<!-- 图片轮播 -->
				<swiper class="detail-swiper" indicator-dots autoplay circular>
					<swiper-item>
						<image :src="productDetail.image" mode="aspectFill" class="swiper-image"></image>
					</swiper-item>
					<!-- 假设有多张图片可以添加更多swiper-item -->
				</swiper>
				
				<!-- 基本信息 -->
				<view class="detail-section">
					<view class="detail-title">{{productDetail.title}}</view>
					<view class="detail-price">¥{{productDetail.price}}</view>
					<view class="detail-tags">
						<text class="detail-tag" v-for="(tag, index) in productDetail.tags" :key="index">{{tag}}</text>
					</view>
					<view class="detail-sales">
						<text>销量 {{productDetail.sales}}</text>
						<text>分类: {{getCategoryName(productDetail.category)}}</text>
					</view>
				</view>
				
				<!-- 规格参数 -->
				<view class="detail-section">
					<view class="section-title">规格参数</view>
					<view class="spec-list">
						<view class="spec-item">
							<text class="spec-label">商品类型:</text>
							<text class="spec-value">{{getConditionName(productDetail.condition)}}</text>
						</view>
						<view class="spec-item">
							<text class="spec-label">发布时间:</text>
							<text class="spec-value">{{productDetail.publishTime}}</text>
						</view>
						<view class="spec-item">
							<text class="spec-label">适用场景:</text>
							<text class="spec-value">驿站、快递中转站、仓库等</text>
						</view>
						<view class="spec-item">
							<text class="spec-label">保修说明:</text>
							<text class="spec-value">{{productDetail.condition === 'new' ? '全新商品，保修1年' : '二手商品，保修3个月'}}</text>
						</view>
					</view>
				</view>
				
				<!-- 商品详情 -->
				<view class="detail-section">
					<view class="section-title">商品详情</view>
					<view class="product-description">
						<text>本商品为驿站专业设备，适合各类型驿站使用。品质保证，使用简单方便，可提高驿站工作效率。{{productDetail.title}}，是您驿站的必备选择。</text>
						<!-- 详情图片 -->
						<image :src="productDetail.image" mode="widthFix" class="desc-image"></image>
					</view>
				</view>
				
				<!-- 店铺信息 -->
				<view class="detail-section">
					<view class="section-title">店铺信息</view>
					<view class="shop-info">
						<image src="/static/images/shop-logo.jpg" mode="aspectFill" class="shop-logo"></image>
						<view class="shop-detail">
							<text class="shop-name">驿站帮供应商城</text>
							<text class="shop-desc">专业驿站设备与耗材供应商</text>
						</view>
						<view class="shop-btn" @tap="enterShop">
							<text>进店逛逛</text>
						</view>
					</view>
				</view>
			</block>
			
			<!-- 底部操作栏 - 针对产品定制 -->
			<view class="bottom-action-bar" v-if="detailType === 'product'">
				<view class="action-left">
					<view class="icon-btn" @tap="navToHome">
						<image src="/static/icons/home.png" mode="aspectFit"></image>
						<text>首页</text>
					</view>
					<view class="icon-btn" @tap="navToCart">
						<image src="/static/icons/cart.png" mode="aspectFit"></image>
						<text>购物车</text>
					</view>
					<view class="icon-btn" @tap="contactService">
						<image src="/static/icons/service.png" mode="aspectFit"></image>
						<text>客服</text>
					</view>
				</view>
				<view class="action-right">
					<view class="action-btn-large add-cart" @tap="addToCart">加入购物车</view>
					<view class="action-btn-large buy-now" @tap="buyNow">立即购买</view>
				</view>
			</view>
			
			<!-- 其他业务类型的底部操作栏 -->
			<view class="bottom-action-bar" v-else>
				<!-- 将原来的"立即联系"按钮改为"接单"按钮，但仅在service类型时显示 -->
				<view class="action-btn-large primary" @tap="takeOrder" v-if="detailType === 'service'">接单</view>
				<view class="action-btn-large primary" @tap="contactSeller" v-else>立即联系</view>
				<view class="action-btn-large outline" @tap="reportItem">举报</view>
			</view>
		</block>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: 0,
				detailType: 'station',
				isCollected: false,
				loading: true,
				error: null,
				stationDetail: {},
				jobDetail: {},
				productDetail: {},
				deviceDetail: {},
				serviceDetail: {},
				isEmpty: false,
				debugInfo: {}
			}
		},
		onLoad(options) {
			console.log('详情页载入参数:', options);
			
			// 获取页面参数
			if (options.id) {
				this.id = options.id;
				// 确保ID是字符串类型
				this.id = this.id.toString();
			} else {
				this.error = '未提供有效的ID参数';
				this.loading = false;
				return;
			}
			
			if (options.type) {
				this.detailType = options.type;
			} else {
				this.error = '未提供详情类型参数';
				this.loading = false;
				return;
			}
			
			console.log('开始加载详情数据, ID:', this.id, '类型:', this.detailType);
			
			// 根据类型加载对应数据
			setTimeout(() => {
				this.loadDetailData();
			}, 300);
			
			// 检查收藏状态
			this.checkCollectStatus();
		},
		computed: {
			// 根据详情类型返回对应的详情数据
			currentDetail() {
				switch (this.detailType) {
					case 'station': return this.stationDetail;
					case 'job': return this.jobDetail;
					case 'device': return this.deviceDetail;
					case 'product': return this.productDetail;
					case 'service': return this.serviceDetail;
					default: return {};
				}
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},
			
			// 重试加载
			retryLoading() {
				this.error = null;
				this.loading = true;
				this.isEmpty = false;
				this.loadDetailData();
			},
			
			// 检查数据是否为空
			checkIfEmpty() {
				let detail = this.currentDetail;
				this.isEmpty = !detail || Object.keys(detail).length === 0;
				
				console.log(`检查${this.detailType}数据是否为空:`, this.isEmpty);
				
				if(this.isEmpty) {
					console.error('详情数据为空，请检查数据加载是否正确');
				}
				
				return this.isEmpty;
			},
			
			// 加载详情数据
			async loadDetailData() {
				this.loading = true;
				this.error = null;
				
				try {
					console.log('开始获取详情数据...');
					
					// 根据不同类型调用不同API
					if (this.detailType === 'station') {
						await this.fetchStationDetail();
					} else if (this.detailType === 'job') {
						await this.fetchJobDetail();
					} else if (this.detailType === 'product') {
						await this.fetchProductDetail();
					} else if (this.detailType === 'device') {
						await this.fetchDeviceDetail();
					} else if (this.detailType === 'service') {
						await this.fetchServiceDetail();
					} else {
						throw new Error('未知的详情类型: ' + this.detailType);
					}
					
					// 检查是否数据为空
					const isEmpty = this.checkIfEmpty();
					if (isEmpty) {
						this.useStaticData(); // 使用静态数据填充
					}
					
					console.log('详情数据加载完成');
				} catch (error) {
					console.error('加载详情数据失败', error);
					this.error = '加载详情数据失败，请重试: ' + (error.message || '未知错误');
					this.useStaticData(); // 出错时使用静态数据
					
					uni.showToast({
						title: '加载失败，已使用默认数据',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},
			
			// 使用静态数据
			useStaticData() {
				// 根据类型使用对应的静态数据
				if (this.detailType === 'station') {
					this.useStaticStationData();
				} else if (this.detailType === 'job') {
					this.useStaticJobData();
				} else if (this.detailType === 'product') {
					this.useStaticProductData();
				} else if (this.detailType === 'device') {
					this.useStaticDeviceData();
				} else if (this.detailType === 'service') {
					this.useStaticServiceData();
				}
				this.isEmpty = false;
			},
			
			// 获取驿站详情
			async fetchStationDetail() {
				try {
					// 首先尝试从本地存储获取驿站列表
					const transferList = uni.getStorageSync('transferList') || [];
					
					// 根据ID查找对应的驿站信息
					const localStation = transferList.find(item => item.id.toString() === this.id.toString());
					
					// 如果在本地找到了对应的驿站信息
					if (localStation) {
						// 确保数据格式符合模板要求
						this.stationDetail = {
							id: localStation.id,
							title: localStation.title,
							price: localStation.price,
							priceUnit: localStation.priceUnit || '万元',
							tags: localStation.tags || [],
							publishTime: localStation.publishTime || this.formatDateTime(new Date()),
							viewCount: localStation.viewCount || 0,
							// 确保图片数据是数组格式
							images: Array.isArray(localStation.images) 
								? localStation.images 
								: (localStation.image ? [localStation.image] : ['/static/images/station-default.jpg']),
							address: localStation.address || '',
							area: localStation.space || '0',
							monthlyIncome: localStation.monthlyIncome || '0',
							dailyPackage: localStation.package || '0',
							reason: localStation.reason || '未提供',
							description: localStation.description || localStation.advantage || '未提供详细描述',
							contact: {
								name: localStation.contactInfo?.name || '未提供',
								phone: localStation.contactInfo?.phone || '未提供',
								wechat: localStation.contactInfo?.wechat || '未提供'
							}
						};
						return;
					}
					
					// 如果本地没找到，则尝试从API获取
					const that = this;
					uni.request({
						url: '/api/station/detail',
						method: 'GET',
						data: {
							id: this.id
						},
						success: (res) => {
							if (res.data.code === 200 && res.data.data) {
								that.stationDetail = {
									...res.data.data,
									// 兼容原有数据结构
									contact: {
										name: res.data.data.contact_name || '未提供',
										phone: res.data.data.contact_phone || '未提供',
										wechat: res.data.data.additional_fields?.wechat || '未提供'
									},
									priceUnit: res.data.data.price_unit || '元',
									publishTime: that.formatDateTime(res.data.data.publish_time),
									viewCount: res.data.data.views || 0,
									area: res.data.data.area || '0',
									monthlyIncome: res.data.data.monthly_income || '0',
									dailyPackage: res.data.data.daily_package || '0',
									reason: res.data.data.reason || '未提供',
									// 确保API返回的图片数据也是数组格式
									images: Array.isArray(res.data.data.images) 
										? res.data.data.images 
										: ['/static/images/station-default.jpg']
								};
							} else {
								console.error('获取驿站详情失败:', res.data.msg);
								that.useStaticStationData();
							}
						},
						fail: (err) => {
							console.error('请求驿站详情失败:', err);
							that.useStaticStationData();
						}
					});
					
				} catch (error) {
					console.error('获取驿站详情失败', error);
					this.useStaticStationData();
				}
			},
			
			// 使用静态驿站数据（作为备用）
			useStaticStationData() {
				this.stationDetail = {
					title: '城北社区驿站转让',
					price: '45000',
					priceUnit: '元',
					tags: ['社区驿站', '有固定客源', '设备齐全'],
					publishTime: '2023-04-07 09:32',
					viewCount: 256,
					images: [
						'/static/images/station1.jpg',
						'/static/images/station2.jpg',
						'/static/images/station3.jpg'
					],
					address: '海淀区北太平庄街道',
					area: '60',
					monthlyIncome: '25000',
					dailyPackage: '300',
					reason: '个人原因无暇经营，转手给有意向的伙伴',
					description: '本驿站位于北太平庄小区门口，周边居民区密集，客源稳定。驿站面积60平米，每日快递包裹300件左右，月均营业额2.5万元。现因个人原因无暇经营，寻求有意向的伙伴接手。驿站内设备齐全，可直接经营，接手即可盈利。价格可小刀，诚心人联系！',
					contact: {
						name: '王先生',
						phone: '13812345678',
						wechat: 'wx123456'
					}
				};
			},
			
			// 获取职位详情
			async fetchJobDetail() {
				console.log('开始获取工作详情，ID:', this.id);
				
				// 首先尝试从本地存储中获取数据
				try {
					const jobList = uni.getStorageSync('jobList') || [];
					const localJob = jobList.find(job => job.id == this.id);
					
					if (localJob) {
						console.log('从本地存储找到工作详情:', localJob);
						// 将本地存储的数据格式化为详情页需要的格式
						this.jobDetail = {
							id: localJob.id,
							title: localJob.title,
							salary: localJob.salary,
							company: localJob.company,
							address: localJob.address,
							tags: localJob.tags,
							publishTime: localJob.publishTime || '刚刚',
							viewCount: '100+',  // 默认值
							experience: localJob.experience,
							education: localJob.education || '学历不限',
							ageRequirement: localJob.ageRequirement || '不限',
							jobType: localJob.jobType,
							workTime: localJob.workTime,
							jobDescription: localJob.jobDescription || localJob.responsibilities || '',
							benefits: localJob.benefits || '',
							contact: {
								name: localJob.contactInfo?.name || '',
								phone: localJob.contactInfo?.phone || '',
								wechat: localJob.contactInfo?.wechat || ''
							}
						};
						return;
					}
				} catch (e) {
					console.error('获取本地工作数据失败:', e);
				}
				
				// 如果本地没有找到，再尝试从API获取
				console.log('本地未找到数据，尝试从API获取');
				const that = this;
				uni.request({
					url: '/api/job/detail', // 修改为相对路径
					method: 'GET',
					data: {
						id: this.id
					},
					success: (res) => {
						console.log('API响应:', res.data);
						if (res.data.code === 200) {
							that.jobDetail = res.data.data;
						} else {
							console.error('获取工作详情失败:', res.data.msg);
							// 如果API也失败，使用静态数据
							that.useStaticJobData();
						}
					},
					fail: (err) => {
						console.error('请求工作详情失败:', err);
						// 使用静态数据
						that.useStaticJobData();
					}
				});
			},
			
			// 使用静态工作数据（作为备用）
			useStaticJobData() {
				console.log('使用静态工作数据');
				this.jobDetail = {
					id: this.id,
					title: '招聘驿站理货员',
					salary: '3000-5000元/月',
					company: '顺丰驿站',
					address: '上海市浦东新区张江高科技园区',
					tags: ['全职', '工作时间灵活', '有福利'],
					publishTime: '2小时前',
					viewCount: '253',
					experience: '经验不限',
					education: '学历不限',
					ageRequirement: '18-45岁',
					jobType: '全职',
					workTime: '9:00-18:00，单休',
					jobDescription: '负责驿站日常收发快递、整理货架、清点库存等工作。\n要求认真负责，有耐心，能吃苦耐劳。',
					benefits: '五险一金、节日福利、团队旅游',
					contact: {
						name: '张经理',
						phone: '13812345678',
						wechat: 'sf_12345'
					}
				};
			},
			
			// 获取商品详情
			async fetchProductDetail() {
				// 如果是从本地存储获取，保持原有逻辑
				const productData = uni.getStorageSync('currentProduct');
				if (productData) {
					this.productDetail = JSON.parse(productData);
					return;
				}
				
				// 否则从API获取
				try {
					const that = this;
					uni.request({
						url: '/api/product/detail',
						method: 'GET',
						data: {
							id: this.id
						},
						success: (res) => {
							if (res.data.code === 200 && res.data.data) {
								that.productDetail = {
									id: res.data.data.id,
									title: res.data.data.title,
									price: res.data.data.price,
									tags: res.data.data.tags || [],
									sales: res.data.data.sales || 0,
									image: res.data.data.images && res.data.data.images.length > 0 ? res.data.data.images[0] : '/static/images/device-default.jpg',
									category: res.data.data.type || '',
									condition: res.data.data.condition || 'new',
									publishTime: that.formatDateTime(res.data.data.publish_time)
								};
							} else {
								throw new Error('获取商品详情失败');
							}
						},
						fail: (err) => {
							console.error('请求商品详情失败:', err);
							that.useStaticProductData();
						}
					});
				} catch (error) {
					console.error('获取商品详情失败', error);
					this.useStaticProductData();
				}
			},
			
			// 使用静态商品数据（作为备用）
			useStaticProductData() {
				this.productDetail = {
					id: this.id,
					title: '快递电子秤',
					price: '499',
					tags: ['耐用', '精准', '全新'],
					sales: 28,
					image: '/static/images/device1.jpg',
					category: 'scales',
					condition: 'new',
					publishTime: '2023-04-05'
				};
				uni.showToast({
					title: '商品数据加载失败',
					icon: 'none'
				});
			},
			
			// 获取设备详情
			async fetchDeviceDetail() {
				try {
					// 首先尝试从本地存储获取设备列表
					const deviceList = uni.getStorageSync('deviceList') || [];
					
					// 根据ID查找对应的设备信息
					const localDevice = deviceList.find(item => item.id.toString() === this.id.toString());
					
					// 如果在本地找到了对应的设备信息
					if (localDevice) {
						this.deviceDetail = {
							...localDevice,
							// 确保图片数据是数组格式
							images: Array.isArray(localDevice.images) 
								? localDevice.images 
								: ['/static/images/device-default.jpg'],
							description: localDevice.description || '卖家暂未提供详细描述'
						};
						return;
					}
					
					// 如果本地没找到，则尝试从API获取
					const that = this;
					uni.request({
						url: '/api/device/detail',
						method: 'GET',
						data: {
							id: this.id
						},
						success: (res) => {
							if (res.data.code === 200 && res.data.data) {
								that.deviceDetail = {
									...res.data.data,
									location: res.data.data.address || '未提供地址',
									publishTime: that.formatDateTime(res.data.data.publish_time)
								};
							} else {
								throw new Error('获取设备详情失败');
							}
						},
						fail: (err) => {
							console.error('请求设备详情失败:', err);
							that.useStaticDeviceData();
						}
					});
				} catch (error) {
					console.error('获取设备详情失败', error);
					this.useStaticDeviceData();
				}
			},
			
			// 使用静态设备数据（作为备用）
			useStaticDeviceData() {
				this.deviceDetail = {
					title: '9成新菜鸟智能电子秤',
					price: '380',
					location: '北京·海淀',
					type: '电子秤',
					images: ['/static/device/scale.jpg'],
					condition: '9成新',
					publishTime: '1小时前',
					description: '这是一台使用仅三个月的电子秤，功能完好，称重精准，外观几乎全新，适合快递驿站使用。',
					contactInfo: {
						name: '王先生',
						phone: '13312345678'
					}
				};
			},
			
			// 获取服务详情
			async fetchServiceDetail() {
				try {
					// 首先尝试从本地存储获取服务列表
					const serviceList = uni.getStorageSync('serviceList') || [];
					
					// 根据ID查找对应的服务信息
					const localService = serviceList.find(item => item.id.toString() === this.id.toString());
					
					// 如果在本地找到了对应的服务信息
					if (localService) {
						this.serviceDetail = {
							...localService,
							publishTime: localService.publishTime || this.formatDateTime(new Date()),
							viewCount: localService.viewCount || 0,
							contact: {
								name: localService.contactInfo?.name || '未提供',
								phone: localService.contactInfo?.phone || '未提供'
							}
						};
						return;
					}
					
					// 如果本地没找到，尝试从API获取
					const that = this;
					uni.request({
						url: '/api/service/detail',
						method: 'GET',
						data: {
							id: this.id
						},
						success: (res) => {
							if (res.data.code === 200 && res.data.data) {
								// 处理额外字段
								const additionalFields = res.data.data.additional_fields || {};
								
								that.serviceDetail = {
									...res.data.data,
									// 兼容原有数据结构
									publishTime: that.formatDateTime(res.data.data.publish_time),
									viewCount: res.data.data.views || 0,
									contact: {
										name: additionalFields.contact_name || '未提供',
										phone: additionalFields.contact_phone || '未提供'
									},
									serviceType: additionalFields.service_type || '未提供',
									serviceTime: additionalFields.service_time || res.data.data.time || '未提供',
									servicePeriod: additionalFields.service_period || '未提供',
									specialRequirements: additionalFields.special_requirements || '无'
								};
							} else {
								throw new Error('获取服务详情失败');
							}
						},
						fail: (err) => {
							console.error('请求服务详情失败:', err);
							that.useStaticServiceData();
						}
					});
				} catch (error) {
					console.error('获取服务详情失败', error);
					this.useStaticServiceData();
				}
			},
			
			// 使用静态服务数据（作为备用）
			useStaticServiceData() {
				this.serviceDetail = {
					title: '顶班服务 - 包裹分拣员',
					price: '300',
					address: '海淀区北太平庄',
					description: '驿站分拣员一名，有经验者优先，熟悉快递分拣、扫描、地址识别等工作。每天工作8小时，主要负责包裹分拣、整理和交付。',
					publishTime: '2023-04-07 15:28',
					viewCount: 42,
					contact: {
						name: '张站长',
						phone: '13800138000'
					},
					serviceType: '临时顶班',
					serviceTime: '2023-04-15 至 2023-04-20',
					servicePeriod: '早9:00-晚17:00',
					specialRequirements: '要求有驿站工作经验，能吃苦耐劳，工作认真负责。'
				};
			},
			
			// 格式化日期时间
			formatDateTime(dateTimeStr) {
				if (!dateTimeStr) return '';
				
				try {
					const date = new Date(dateTimeStr);
					return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
				} catch (e) {
					return dateTimeStr;
				}
			},
			
			// 检查收藏状态
			checkCollectStatus() {
				// 从本地存储获取收藏列表
				const collects = uni.getStorageSync('collects') || '[]';
				const collectList = JSON.parse(collects);
				
				// 检查当前项目是否已收藏
				if (this.detailType === 'product') {
					this.isCollected = collectList.some(item => 
						item.type === 'product' && item.id === this.id
					);
				} else if (this.detailType === 'station') {
					this.isCollected = collectList.some(item => 
						item.type === 'station' && item.id === this.id
					);
				} else if (this.detailType === 'job') {
					this.isCollected = collectList.some(item => 
						item.type === 'job' && item.id === this.id
					);
				} else if (this.detailType === 'service') {
					this.isCollected = collectList.some(item => 
						item.type === 'service' && item.id === this.id
					);
				}
			},
			
			// 切换收藏状态
			toggleCollect() {
				// 从本地存储获取收藏列表
				const collects = uni.getStorageSync('collects') || '[]';
				const collectList = JSON.parse(collects);
				
				let collectItem = null;
				
				// 根据不同类型准备收藏数据
				if (this.detailType === 'product') {
					collectItem = {
						id: this.productDetail.id,
						type: 'product',
						title: this.productDetail.title,
						image: this.productDetail.image,
						price: this.productDetail.price,
						collectTime: new Date().toISOString()
					};
				} else if (this.detailType === 'station') {
					collectItem = {
						id: this.stationDetail.id || this.id,
						type: 'station',
						title: this.stationDetail.title,
						image: this.stationDetail.images && this.stationDetail.images.length > 0 ? this.stationDetail.images[0] : '/static/images/station-default.jpg',
						price: this.stationDetail.price,
						collectTime: new Date().toISOString()
					};
				} else if (this.detailType === 'job') {
					collectItem = {
						id: this.jobDetail.id || this.id,
						type: 'job',
						title: this.jobDetail.title,
						image: '/static/images/job-default.jpg',
						salary: this.jobDetail.salary,
						collectTime: new Date().toISOString()
					};
				} else if (this.detailType === 'service') {
					collectItem = {
						id: this.serviceDetail.id || this.id,
						type: 'service',
						title: this.serviceDetail.title,
						image: '/static/images/service-default.jpg',
						price: this.serviceDetail.price,
						collectTime: new Date().toISOString()
					};
				}
				
				if (this.isCollected) {
					// 如果已收藏，则取消收藏
					const index = collectList.findIndex(item => 
						item.type === collectItem.type && item.id === collectItem.id
					);
					if (index !== -1) {
						collectList.splice(index, 1);
					}
					this.isCollected = false;
					uni.showToast({
						title: '已取消收藏',
						icon: 'success'
					});
				} else {
					// 如果未收藏，则添加收藏
					collectList.push(collectItem);
					this.isCollected = true;
					uni.showToast({
						title: '收藏成功',
						icon: 'success'
					});
				}
				
				// 更新本地存储
				uni.setStorageSync('collects', JSON.stringify(collectList));
			},
			
			// 分享商品
			shareItem() {
				uni.showToast({
					title: '分享功能开发中',
					icon: 'none'
				});
			},
			
			// 联系卖家
			contactSeller() {
				// 根据不同类型联系不同人
				if (this.detailType === 'station' && this.stationDetail.contact) {
					this.callPhone(this.stationDetail.contact.phone);
				} else if (this.detailType === 'job' && this.jobDetail.contact) {
					this.callPhone(this.jobDetail.contact.phone);
				} else if (this.detailType === 'service' && this.serviceDetail.contact) {
					this.callPhone(this.serviceDetail.contact.phone);
				} else {
					uni.showToast({
						title: '联系信息不可用',
						icon: 'none'
					});
				}
			},
			
			// 接单功能
			takeOrder() {
				// 检查用户是否登录
				const userInfo = uni.getStorageSync('userInfo');
				if (!userInfo) {
					uni.showModal({
						title: '提示',
						content: '请先登录后再接单',
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/login/login'
								});
							}
						}
					});
					return;
				}
				
				// 检查用户是否通过顶班认证
				const certificationInfo = uni.getStorageSync('certificationInfo');
				const isCertified = certificationInfo && certificationInfo.substituteVerified;
				
				if (!isCertified) {
					uni.showModal({
						title: '认证提示',
						content: '您需要通过顶班认证后才能接单',
						confirmText: '去认证',
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/certification/substitute'
								});
							}
						}
					});
					return;
				}
				
				// 通过认证，可以接单
				uni.showLoading({
					title: '接单中...'
				});
				
				setTimeout(() => {
					uni.hideLoading();
					
					// 接单成功后更新状态并保存接单记录
					const orderRecord = {
						id: this.id,
						type: this.detailType,
						title: this.serviceDetail.title,
						price: this.serviceDetail.price,
						orderTime: new Date().toISOString(),
						status: '已接单'
					};
					
					// 保存到本地接单记录中
					const myOrders = uni.getStorageSync('myOrders') || '[]';
					const myOrdersList = JSON.parse(myOrders);
					myOrdersList.push(orderRecord);
					uni.setStorageSync('myOrders', JSON.stringify(myOrdersList));
					
					// 显示成功提示
					uni.showToast({
						title: '接单成功',
						icon: 'success'
					});
					
					// 1.5秒后跳转到我的订单页面
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/my/orders'
						});
					}, 1500);
				}, 1000);
			},
			
			// 拨打电话
			callPhone(phone) {
				if (!phone) {
					uni.showToast({
						title: '电话号码不可用',
						icon: 'none'
					});
					return;
				}
				
				uni.makePhoneCall({
					phoneNumber: phone,
					fail: () => {
						uni.showToast({
							title: '拨号已取消',
							icon: 'none'
						});
					}
				});
			},
			
			// 复制微信号
			copyWechat(wechat) {
				if (!wechat) {
					uni.showToast({
						title: '微信号不可用',
						icon: 'none'
					});
					return;
				}
				
				uni.setClipboardData({
					data: wechat,
					success: () => {
						uni.showToast({
							title: '微信号已复制',
							icon: 'success'
						});
					}
				});
			},
			
			// 举报
			reportItem() {
				uni.showToast({
					title: '举报功能开发中',
					icon: 'none'
				});
			},
			
			// 获取分类名称
			getCategoryName(category) {
				switch(category) {
					case 'scales':
						return '电子秤';
					case 'printer':
						return '打印机';
					case 'shelves':
						return '货架柜';
					case 'scanner':
						return '扫描器';
					default:
						return '其他设备';
				}
			},
			
			// 获取商品状态名称
			getConditionName(condition) {
				switch(condition) {
					case 'new':
						return '全新';
					case 'used':
						return '二手';
					case 'refurbished':
						return '翻新';
					default:
						return '未知';
				}
			},
			
			// 导航到首页
			navToHome() {
				uni.switchTab({
					url: '/pages/station/station'
				});
			},
			
			// 导航到购物车
			navToCart() {
				uni.navigateTo({
					url: '/pages/my/cart'
				});
			},
			
			// 联系客服
			contactService() {
				uni.showToast({
					title: '客服功能开发中',
					icon: 'none'
				});
			},
			
			// 进入店铺
			enterShop() {
				uni.showToast({
					title: '店铺页面开发中',
					icon: 'none'
				});
			},
			
			// 加入购物车
			addToCart() {
				// 获取当前购物车数据
				const cart = uni.getStorageSync('cart') || '[]';
				const cartList = JSON.parse(cart);
				
				// 构建购物车项目
				const cartItem = {
					id: this.productDetail.id,
					title: this.productDetail.title,
					image: this.productDetail.image,
					price: this.productDetail.price,
					count: 1,
					selected: true,
					addTime: new Date().toISOString()
				};
				
				// 检查是否已存在
				const existingIndex = cartList.findIndex(item => item.id === cartItem.id);
				
				if (existingIndex !== -1) {
					// 如果已存在，增加数量
					cartList[existingIndex].count += 1;
				} else {
					// 如果不存在，添加到购物车
					cartList.push(cartItem);
				}
				
				// 更新本地存储
				uni.setStorageSync('cart', JSON.stringify(cartList));
				
				uni.showToast({
					title: '已加入购物车',
					icon: 'success'
				});
			},
			
			// 立即购买
			buyNow() {
				// 获取当前商品信息
				const orderItems = [{
					id: this.productDetail.id,
					title: this.productDetail.title,
					image: this.productDetail.image,
					price: this.productDetail.price,
					count: 1,
					selected: true
				}];
				
				// 保存到本地存储，传递给结算页面
				uni.setStorageSync('checkoutItems', JSON.stringify(orderItems));
				
				// 跳转到结算页面
				uni.navigateTo({
					url: '/pages/my/checkout'
				});
			}
		}
	}
</script>

<style>
	.container {
		padding-bottom: 120rpx;
		background-color: #f5f5f5;
		min-height: 100vh; /* 确保容器至少有一屏高度 */
	}
	
	/* 加载状态样式 */
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
		min-height: 50vh;
	}
	
	.loading-spinner {
		width: 80rpx;
		height: 80rpx;
		border: 6rpx solid #f3f3f3;
		border-top: 6rpx solid #1989fa;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	.loading-text {
		font-size: 28rpx;
		color: #999;
	}
	
	/* 错误提示样式 */
	.error-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 30rpx;
		min-height: 50vh;
	}
	
	.error-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
	}
	
	.error-text {
		font-size: 28rpx;
		color: #666;
		text-align: center;
		margin-bottom: 40rpx;
	}
	
	.retry-btn {
		padding: 20rpx 60rpx;
		background-color: #1989fa;
		color: #fff;
		border-radius: 40rpx;
		font-size: 28rpx;
	}
	
	/* 空状态样式 */
	.empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 0;
		min-height: 50vh;
	}
	
	.empty-icon {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 30rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999;
	}
	
	.action-bar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		height: 88rpx;
		background-color: rgba(0, 0, 0, 0.3);
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20rpx;
		z-index: 100;
	}
	
	.action-left, .action-right {
		display: flex;
	}
	
	.action-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.action-btn image {
		width: 40rpx;
		height: 40rpx;
		filter: brightness(2);
	}
	
	.detail-swiper {
		width: 100%;
		height: 500rpx;
		margin-top: 0;
	}
	
	.swiper-image {
		width: 100%;
		height: 100%;
	}
	
	.detail-section {
		background-color: #ffffff;
		margin-bottom: 20rpx;
		padding: 30rpx;
	}
	
	.detail-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
	}
	
	.detail-price {
		font-size: 40rpx;
		font-weight: bold;
		color: #ff5a5f;
		margin-bottom: 20rpx;
	}
	
	.price-unit {
		font-size: 28rpx;
		font-weight: normal;
	}
	
	.detail-company {
		font-size: 32rpx;
		color: #333333;
		margin-bottom: 10rpx;
	}
	
	.detail-address {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 20rpx;
	}
	
	.detail-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 20rpx;
	}
	
	.detail-tag {
		font-size: 24rpx;
		padding: 6rpx 16rpx;
		background-color: #f8f8f8;
		color: #999999;
		margin-right: 15rpx;
		margin-bottom: 10rpx;
		border-radius: 6rpx;
	}
	
	.detail-publish-info {
		display: flex;
		justify-content: space-between;
		font-size: 24rpx;
		color: #999999;
	}
	
	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
		position: relative;
		padding-left: 20rpx;
	}
	
	.section-title::before {
		content: '';
		position: absolute;
		left: 0;
		top: 6rpx;
		height: 30rpx;
		width: 6rpx;
		background-color: #ff5a5f;
	}
	
	.contact-info, .station-info, .job-requirements, .service-info {
		padding: 10rpx 0;
	}
	
	.contact-item, .info-item, .requirement-item {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.contact-label, .info-label, .requirement-label {
		width: 160rpx;
		font-size: 28rpx;
		color: #666666;
	}
	
	.contact-value, .info-value, .requirement-value {
		flex: 1;
		font-size: 28rpx;
		color: #333333;
	}
	
	.contact-btn {
		display: flex;
		align-items: center;
		background-color: #f8f8f8;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		margin-left: 20rpx;
	}
	
	.contact-btn image {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
	}
	
	.contact-btn text {
		font-size: 24rpx;
		color: #666666;
	}
	
	.detail-description, .job-description, .job-benefits {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.6;
	}
	
	.bottom-action-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		background-color: #ffffff;
		display: flex;
		justify-content: space-around;
		align-items: center;
		padding: 0 30rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.action-btn-large {
		width: 45%;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 40rpx;
		font-size: 28rpx;
	}
	
	.action-btn-large.primary {
		background-color: #ff5a5f;
		color: #ffffff;
	}
	
	.action-btn-large.outline {
		background-color: #ffffff;
		color: #666666;
		border: 1rpx solid #dddddd;
	}
	
	/* 添加产品详情相关样式 */
	.shop-info {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
	}
	
	.shop-logo {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		margin-right: 20rpx;
	}
	
	.shop-detail {
		flex: 1;
		display: flex;
		flex-direction: column;
	}
	
	.shop-name {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 6rpx;
	}
	
	.shop-desc {
		font-size: 24rpx;
		color: #999;
	}
	
	.shop-btn {
		padding: 10rpx 30rpx;
		border: 1rpx solid #ff5a5f;
		border-radius: 30rpx;
	}
	
	.shop-btn text {
		font-size: 24rpx;
		color: #ff5a5f;
	}
	
	.detail-sales {
		display: flex;
		justify-content: space-between;
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
	}
	
	.spec-list {
		margin-top: 20rpx;
	}
	
	.spec-item {
		display: flex;
		margin-bottom: 16rpx;
	}
	
	.spec-label {
		width: 160rpx;
		font-size: 26rpx;
		color: #999;
	}
	
	.spec-value {
		flex: 1;
		font-size: 26rpx;
		color: #333;
	}
	
	.product-description {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
	}
	
	.desc-image {
		width: 100%;
		margin-top: 20rpx;
		border-radius: 8rpx;
	}
	
	/* 底部按钮样式 */
	.action-left {
		display: flex;
	}
	
	.icon-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 0 20rpx;
	}
	
	.icon-btn image {
		width: 50rpx;
		height: 50rpx;
		margin-bottom: 6rpx;
	}
	
	.icon-btn text {
		font-size: 22rpx;
		color: #666;
	}
	
	.action-right {
		flex: 1;
		display: flex;
	}
	
	.add-cart {
		background-color: #ffecee;
		color: #ff5a5f;
		border: 1rpx solid #ff5a5f;
	}
	
	.buy-now {
		background-color: #ff5a5f;
		color: #fff;
	}
</style> 