<template>
	<view class="container">
		<view class="form-section">
			<view class="section-title">
				<view class="title-bar"></view>
				<text>基本信息</text>
			</view>
			
			<!-- 添加职位类型选择器 -->
			<view class="job-position-selector">
				<scroll-view scroll-x class="position-scroll-view">
					<view class="position-item" :class="{'active': selectedPosition === 'sorting'}" @tap="selectPosition('sorting')">分拣员</view>
					<view class="position-item" :class="{'active': selectedPosition === 'delivery'}" @tap="selectPosition('delivery')">配送员</view>
					<view class="position-item" :class="{'active': selectedPosition === 'manager'}" @tap="selectPosition('manager')">站点管理</view>
					<view class="position-item" :class="{'active': selectedPosition === 'customer'}" @tap="selectPosition('customer')">客服</view>
					<view class="position-item" :class="{'active': selectedPosition === 'warehouse'}" @tap="selectPosition('warehouse')">仓储</view>
				</scroll-view>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>职位名称</text>
					<text class="required">*</text>
				</view>
				<input type="text" placeholder="请输入招聘职位名称" v-model="title" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>薪资待遇</text>
					<text class="required">*</text>
				</view>
				<picker mode="selector" :range="salaryRanges" @change="onSalaryChange">
					<view class="picker-value">
						<text v-if="selectedSalary !== null">{{salaryRanges[selectedSalary]}}</text>
						<text v-else class="placeholder">请选择薪资范围</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>所在区域</text>
					<text class="required">*</text>
				</view>
				<picker mode="selector" :range="areas" @change="onAreaChange">
					<view class="picker-value">
						<text v-if="selectedArea !== null">{{areas[selectedArea]}}</text>
						<text v-else class="placeholder">请选择所在区域</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>工作地址</text>
					<text class="required">*</text>
				</view>
				<input type="text" placeholder="请输入详细工作地址" v-model="address" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>工作类型</text>
					<text class="required">*</text>
				</view>
				<picker mode="selector" :range="jobTypes" @change="onJobTypeChange">
					<view class="picker-value">
						<text v-if="selectedJobType !== null">{{jobTypes[selectedJobType]}}</text>
						<text v-else class="placeholder">请选择工作类型</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
		</view>
		
		<view class="form-section">
			<view class="section-title">
				<view class="title-bar"></view>
				<text>职位描述</text>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>岗位职责</text>
					<text class="required">*</text>
				</view>
				<textarea placeholder="请描述岗位的主要职责内容" v-model="responsibilities" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>任职要求</text>
					<text class="required">*</text>
				</view>
				<textarea placeholder="请描述对应聘者的要求" v-model="requirements" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<text class="label">工作时间</text>
				<input type="text" placeholder="请输入工作时间，如：9:00-18:00" v-model="workTime" />
			</view>
			
			<view class="form-item">
				<text class="label">公司名称</text>
				<input type="text" placeholder="请输入公司/驿站名称" v-model="company" />
			</view>
			
			<view class="form-item">
				<text class="label">福利待遇</text>
				<textarea placeholder="请描述岗位福利待遇" v-model="benefits" />
			</view>
		</view>
		
		<view class="form-section">
			<view class="section-title">
				<view class="title-bar"></view>
				<text>联系方式</text>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>联系人</text>
					<text class="required">*</text>
				</view>
				<input type="text" placeholder="请输入联系人姓名" v-model="contactName" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>手机号码</text>
					<text class="required">*</text>
				</view>
				<input type="number" placeholder="请输入手机号码" v-model="contactPhone" maxlength="11" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>微信号</text>
				</view>
				<input type="text" placeholder="请输入微信号" v-model="wechatId" placeholder-class="placeholder" />
			</view>
		</view>
		
		<view class="button-group">
			<button class="btn-draft" @tap="saveDraft">保存草稿</button>
			<button class="btn-publish" @tap="publish">立即发布</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				apiBaseUrl: '', // 添加API基础URL
				selectedPosition: 'sorting', // 默认选中分拣员
				title: '',
				selectedSalary: null,
				salaryRanges: ['3000-4000元/月', '4000-5000元/月', '5000-6000元/月', '6000-7000元/月', '7000-8000元/月', '8000元以上/月', '面议'],
				selectedArea: null,
				areas: ['海淀区', '朝阳区', '西城区', '东城区', '丰台区', '石景山区', '通州区', '昌平区', '大兴区', '顺义区', '房山区', '门头沟区', '平谷区', '密云区', '怀柔区', '延庆区'],
				address: '',
				selectedJobType: null,
				jobTypes: ['全职', '兼职', '临时工'],
				responsibilities: '',
				requirements: '',
				workTime: '',
				company: '',
				benefits: '',
				contactName: '',
				contactPhone: '',
				wechatId: ''
			}
		},
		computed: {
			// 根据所选职位动态计算可用的工作类型
			availableJobTypes() {
				// 分拣员和配送员可以选择临时工
				if (this.selectedPosition === 'sorting' || this.selectedPosition === 'delivery') {
					return ['全职', '兼职', '临时工'];
				} else {
					// 站点管理、客服和仓储不能选择临时工
					return ['全职', '兼职'];
				}
			}
		},
		watch: {
			// 当职位类型变化时，重置工作类型选择
			selectedPosition(newValue) {
				this.selectedJobType = null;
				// 自动填充职位名称
				this.setTitleBasedOnPosition(newValue);
			}
		},
		methods: {
			// 选择职位类型
			selectPosition(position) {
				this.selectedPosition = position;
			},
			
			// 根据职位类型设置职位名称
			setTitleBasedOnPosition(position) {
				switch(position) {
					case 'sorting':
						this.title = '招聘驿站分拣员';
						break;
					case 'delivery':
						this.title = '招聘快递配送员';
						break;
					case 'manager':
						this.title = '招聘驿站管理员';
						break;
					case 'customer':
						this.title = '招聘驿站客服';
						break;
					case 'warehouse':
						this.title = '招聘仓储人员';
						break;
				}
			},
			
			onSalaryChange(e) {
				this.selectedSalary = e.detail.value;
			},
			onAreaChange(e) {
				this.selectedArea = e.detail.value;
			},
			onJobTypeChange(e) {
				this.selectedJobType = e.detail.value;
			},
			validateForm() {
				if (!this.title.trim()) {
					uni.showToast({
						title: '请输入职位名称',
						icon: 'none'
					});
					return false;
				}
				if (this.selectedSalary === null) {
					uni.showToast({
						title: '请选择薪资范围',
						icon: 'none'
					});
					return false;
				}
				if (this.selectedArea === null) {
					uni.showToast({
						title: '请选择所在区域',
						icon: 'none'
					});
					return false;
				}
				if (!this.address.trim()) {
					uni.showToast({
						title: '请输入工作地址',
						icon: 'none'
					});
					return false;
				}
				if (this.selectedJobType === null) {
					uni.showToast({
						title: '请选择工作类型',
						icon: 'none'
					});
					return false;
				}
				if (!this.responsibilities.trim()) {
					uni.showToast({
						title: '请描述岗位职责',
						icon: 'none'
					});
					return false;
				}
				if (!this.requirements.trim()) {
					uni.showToast({
						title: '请描述任职要求',
						icon: 'none'
					});
					return false;
				}
				if (!this.contactName.trim()) {
					uni.showToast({
						title: '请输入联系人姓名',
						icon: 'none'
					});
					return false;
				}
				if (!this.contactPhone.trim()) {
					uni.showToast({
						title: '请输入手机号码',
						icon: 'none'
					});
					return false;
				}
				
				return true;
			},
			saveDraft() {
				// 保存草稿逻辑
				uni.showToast({
					title: '已保存草稿',
					icon: 'success'
				});
			},
			publish() {
				if (!this.validateForm()) {
					return;
				}
				
				// 显示加载中
				uni.showLoading({
					title: '发布中...'
				});
				
				// 创建工作信息对象
				const jobInfo = {
					id: Date.now().toString(), // 使用时间戳作为临时ID
					title: this.title,
					salary: this.salaryRanges[this.selectedSalary],
					company: this.company,
					address: this.areas[this.selectedArea] + ' ' + this.address,
					tags: [
						this.availableJobTypes[this.selectedJobType],
						this.workTime ? '工作时间: ' + this.workTime : '',
						this.benefits ? '有福利' : ''
					].filter(tag => tag), // 过滤掉空字符串
					experience: this.requirements.split('\n')[0] || '经验不限',
					education: '学历不限',
					publishTime: new Date().toLocaleString(),
					responsibilities: this.responsibilities,
					jobDescription: this.responsibilities, // 兼容详情页的jobDescription字段
					benefits: this.benefits,
					workTime: this.workTime,
					jobType: this.availableJobTypes[this.selectedJobType],
					ageRequirement: '不限',
					contactInfo: {
						name: this.contactName,
						phone: this.contactPhone,
						wechat: this.wechatId
					},
					// 添加职位类型信息，用于在列表页筛选
					positionType: this.selectedPosition
				};
				
				try {
					// 获取现有的工作列表
					let jobList = uni.getStorageSync('jobList') || [];
					
					// 添加新工作到列表开头
					jobList.unshift(jobInfo);
					
					// 保存更新后的列表
					uni.setStorageSync('jobList', jobList);
					
					// 向API发送请求（如果有网络）
					uni.request({
						url: '/api/job/publish', // 使用相对路径，移除this.apiBaseUrl
						method: 'POST',
						data: jobInfo,
						success: (res) => {
							console.log('发布成功响应:', res.data);
							// 可以更新本地存储的ID为API返回的ID
							if (res.data && res.data.code === 200 && res.data.data && res.data.data.id) {
								jobInfo.id = res.data.data.id;
								uni.setStorageSync('jobList', jobList);
							}
						},
						fail: (err) => {
							console.error('API请求失败:', err);
							// 即使API失败，本地保存已经完成
						}
					});
					
					// 隐藏加载提示
					uni.hideLoading();
					
					// 显示成功提示
					uni.showToast({
						title: '发布成功',
						icon: 'success'
					});
					
					// 延迟后跳转到主页或详情页
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/detail/detail?id=' + jobInfo.id + '&type=job'
						});
					}, 1500);
					
				} catch (e) {
					console.error('保存工作信息失败:', e);
					uni.hideLoading();
					uni.showToast({
						title: '发布失败，请重试',
						icon: 'none'
					});
				}
			}
		}
	}
</script>

<style>
	.container {
		padding-bottom: 120rpx;
		background-color: #f7f7f7;
	}
	
	.form-section {
		background-color: #ffffff;
		margin-bottom: 30rpx;
	}
	
	.section-title {
		display: flex;
		align-items: center;
		padding: 25rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
	}
	
	.title-bar {
		width: 8rpx;
		height: 40rpx;
		background-color: #1989fa;
		margin-right: 20rpx;
		border-radius: 6rpx;
	}
	
	.section-title text {
		font-size: 34rpx;
		font-weight: 500;
		color: #333;
	}
	
	.form-item {
		padding: 40rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
	}
	
	.form-item:last-child {
		border-bottom: none;
	}
	
	.label {
		display: flex;
		margin-bottom: 30rpx;
		font-size: 32rpx;
		color: #333;
	}
	
	.required {
		color: #ff4d4f;
		margin-left: 6rpx;
	}
	
	input, textarea {
		width: 100%;
		background-color: #f7f7f7;
		padding: 0 30rpx;
		font-size: 32rpx;
		border-radius: 12rpx;
		box-sizing: border-box;
		height: 92rpx;
		line-height: 92rpx;
	}
	
	.placeholder {
		color: #999;
	}
	
	textarea {
		height: 240rpx;
		line-height: 1.5;
		padding: 30rpx;
	}
	
	.picker-value {
		background-color: #f7f7f7;
		padding: 30rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		position: relative;
		height: 92rpx;
		line-height: 32rpx;
		box-sizing: border-box;
	}
	
	.picker-arrow {
		position: absolute;
		right: 25rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #bbb;
		font-size: 24rpx;
	}
	
	.button-group {
		display: flex;
		padding: 25rpx 30rpx 40rpx;
		background-color: #fff;
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 10;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.btn-draft, .btn-publish {
		flex: 1;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 32rpx;
		border-radius: 45rpx;
	}
	
	.btn-draft {
		background-color: #f5f5f5;
		color: #666;
		margin-right: 20rpx;
	}
	
	.btn-publish {
		background-color: #1989fa;
		color: #fff;
	}
	
	/* 职位类型选择器样式 */
	.job-position-selector {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 30rpx;
		overflow: hidden;
	}
	
	.position-scroll-view {
		white-space: nowrap;
		padding: 20rpx 0;
		display: flex;
		justify-content: space-around;
	}
	
	.position-item {
		display: inline-block;
		height: 70rpx;
		line-height: 70rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		color: #666666;
		position: relative;
		text-align: center;
	}
	
	.position-item.active {
		color: #1989fa;
		font-weight: bold;
	}
	
	.position-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background-color: #1989fa;
	}
</style> 