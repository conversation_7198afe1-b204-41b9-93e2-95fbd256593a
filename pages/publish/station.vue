<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="nav-bar">
			<view class="nav-back" @tap="goBack">
				<text class="back-icon">←</text>
			</view>
			<view class="nav-title">发布驿站</view>
			<view class="nav-placeholder"></view>
		</view>
		
		<!-- 基本信息区块 -->
		<view class="form-block">
			<view class="block-title">
				<view class="title-bar"></view>
				<text>基本信息</text>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>驿站名称</text>
					<text class="required">*</text>
				</view>
				<input type="text" placeholder="请输入驿站名称" v-model="stationName" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>转让价格</text>
					<text class="required">*</text>
				</view>
				<view class="price-input">
					<text class="price-symbol">¥</text>
					<input type="digit" placeholder="请输入转让价格" v-model="price" placeholder-class="placeholder" />
				</view>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>驿站类型</text>
					<text class="required">*</text>
				</view>
				<picker mode="selector" :range="stationTypes" @change="onStationTypeChange">
					<view class="picker-box">
						<text v-if="stationType !== null">{{stationTypes[stationType]}}</text>
						<text v-else class="placeholder">请选择驿站类型</text>
						<view class="picker-arrow">▼</view>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>所在区域</text>
					<text class="required">*</text>
				</view>
				<picker mode="selector" :range="areas" @change="onAreaChange">
					<view class="picker-box">
						<text v-if="selectedArea !== null">{{areas[selectedArea]}}</text>
						<text v-else class="placeholder">请选择所在区域</text>
						<view class="picker-arrow">▼</view>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>详细地址</text>
					<text class="required">*</text>
				</view>
				<input type="text" placeholder="请输入详细地址" v-model="address" placeholder-class="placeholder" />
			</view>
		</view>
		
		<!-- 驿站信息区块 -->
		<view class="form-block">
			<view class="block-title">
				<view class="title-bar"></view>
				<text>驿站信息</text>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>驿站面积</text>
					<text class="required">*</text>
				</view>
				<view class="area-input">
					<input type="digit" placeholder="请输入面积" v-model="area" placeholder-class="placeholder" />
					<text class="area-unit">平方米</text>
				</view>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>月均收入</text>
				</view>
				<view class="price-input">
					<text class="price-symbol">¥</text>
					<input type="digit" placeholder="请输入月均收入" v-model="monthlyIncome" placeholder-class="placeholder" />
				</view>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>日均件量</text>
				</view>
				<input type="number" placeholder="请输入日均件量" v-model="dailyPackages" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>驿站优势</text>
				</view>
				<textarea placeholder="请输入驿站优势、特点等信息" v-model="advantage" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>转让原因</text>
				</view>
				<textarea placeholder="请输入转让原因" v-model="reason" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>上传照片</text>
					<text class="tip">(最多可上传5张)</text>
				</view>
				<view class="upload-area">
					<view class="upload-list">
						<view class="upload-item" v-for="(item, index) in uploadImages" :key="index">
							<image :src="item" mode="aspectFill"></image>
							<view class="delete-btn" @tap="deleteImage(index)">×</view>
						</view>
						<view class="upload-btn" @tap="chooseImage" v-if="uploadImages.length < 5">
							<text class="upload-icon">+</text>
							<text class="upload-text">上传图片</text>
						</view>
					</view>
					<view class="upload-hint" v-if="uploadImages.length > 0">
						<text>已上传 {{uploadImages.length}}/5 张</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 联系方式区块 -->
		<view class="form-block">
			<view class="block-title">
				<view class="title-bar"></view>
				<text>联系方式</text>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>联系人</text>
					<text class="required">*</text>
				</view>
				<input type="text" placeholder="请输入联系人姓名" v-model="contactName" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>手机号码</text>
					<text class="required">*</text>
				</view>
				<input type="number" placeholder="请输入手机号码" v-model="contactPhone" maxlength="11" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>微信号</text>
				</view>
				<input type="text" placeholder="请输入微信号" v-model="wechatId" placeholder-class="placeholder" />
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="footer-actions">
			<button class="btn-draft" @tap="saveDraft">保存草稿</button>
			<button class="btn-publish" @tap="publish">立即发布</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				stationName: '',
				price: '',
				stationType: null,
				stationTypes: ['社区驿站', '校园驿站', '商场驿站', '门店驿站', '其他类型'],
				selectedArea: null,
				areas: ['海淀区', '朝阳区', '西城区', '东城区', '丰台区', '石景山区', '通州区', '昌平区', '大兴区', '顺义区', '房山区', '门头沟区', '平谷区', '密云区', '怀柔区', '延庆区'],
				address: '',
				area: '',
				monthlyIncome: '',
				dailyPackages: '',
				advantage: '',
				reason: '',
				uploadImages: [],
				contactName: '',
				contactPhone: '',
				wechatId: ''
			}
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},
			onStationTypeChange(e) {
				this.stationType = e.detail.value;
			},
			onAreaChange(e) {
				this.selectedArea = e.detail.value;
			},
			chooseImage() {
				const maxImages = 5; // 最多上传5张图片
				const currentCount = this.uploadImages.length;
				const remainCount = maxImages - currentCount;
				
				if (remainCount <= 0) {
					uni.showToast({
						title: '最多只能上传5张图片',
						icon: 'none'
					});
					return;
				}
				
				uni.chooseImage({
					count: remainCount, // 最多可以选择的图片张数
					sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album', 'camera'], // 从相册选择或使用相机拍摄
					success: (res) => {
						this.uploadImages = [...this.uploadImages, ...res.tempFilePaths];
					}
				});
			},
			deleteImage(index) {
				this.uploadImages.splice(index, 1);
			},
			validateForm() {
				if (!this.stationName.trim()) {
					uni.showToast({
						title: '请输入驿站名称',
						icon: 'none'
					});
					return false;
				}
				if (!this.price.trim()) {
					uni.showToast({
						title: '请输入转让价格',
						icon: 'none'
					});
					return false;
				}
				if (this.stationType === null) {
					uni.showToast({
						title: '请选择驿站类型',
						icon: 'none'
					});
					return false;
				}
				if (this.selectedArea === null) {
					uni.showToast({
						title: '请选择所在区域',
						icon: 'none'
					});
					return false;
				}
				if (!this.address.trim()) {
					uni.showToast({
						title: '请输入详细地址',
						icon: 'none'
					});
					return false;
				}
				if (!this.area.trim()) {
					uni.showToast({
						title: '请输入驿站面积',
						icon: 'none'
					});
					return false;
				}
				if (!this.contactName.trim()) {
					uni.showToast({
						title: '请输入联系人姓名',
						icon: 'none'
					});
					return false;
				}
				if (!this.contactPhone.trim()) {
					uni.showToast({
						title: '请输入手机号码',
						icon: 'none'
					});
					return false;
				}
				
				return true;
			},
			saveDraft() {
				// 保存草稿逻辑
				uni.showToast({
					title: '已保存草稿',
					icon: 'success'
				});
			},
			publish() {
				if (this.validateForm()) {
					// 显示加载中
					uni.showLoading({
						title: '发布中...'
					});
					
					// 创建新的驿站转让信息
					const newTransferInfo = {
						id: Date.now(), // 使用时间戳作为临时ID
						title: this.stationName,
						price: this.price,
						priceUnit: '万元',
						address: this.areas[this.selectedArea] + ' ' + this.address,
						area: this.areas[this.selectedArea],
						space: this.area,
						package: this.dailyPackages,
						type: this.stationTypes[this.stationType],
						tags: [this.stationTypes[this.stationType], '新发布', '待转让'],
						advantage: this.advantage || '无详细描述',
						reason: this.reason || '无详细原因',
						description: this.advantage + '\n' + this.reason,
						// 保存所有上传的图片数组
						images: this.uploadImages.length > 0 ? [...this.uploadImages] : ['/static/images/station-default.jpg'],
						// 保留image字段用于兼容
						image: this.uploadImages.length > 0 ? this.uploadImages[0] : '/static/images/station-default.jpg',
						publishTime: '刚刚发布',
						contactInfo: {
							name: this.contactName,
							phone: this.contactPhone,
							wechat: this.wechatId || '未提供'
						}
					};
					
					// 获取已有的转让列表
					let transferList = uni.getStorageSync('transferList') || [];
					
					// 将新转让信息添加到列表开头
					transferList.unshift(newTransferInfo);
					
					// 保存更新后的转让列表
					uni.setStorageSync('transferList', transferList);
					
					// 模拟发布过程
					setTimeout(() => {
						uni.hideLoading();
						uni.showToast({
							title: '发布成功',
							icon: 'success',
							duration: 1500
						});
						
						// 发布成功后跳转到驿站转让页面
						setTimeout(() => {
							uni.redirectTo({
								url: '/pages/transfer/transfer'
							});
						}, 1500);
					}, 1000);
				}
			}
		}
	}
</script>

<style>
	.container {
		padding-bottom: 120rpx;
		padding-top: 100rpx; /* 为顶部导航栏留出空间 */
		background-color: #f7f7f7;
	}
	
	/* 导航栏样式 */
	.nav-bar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #ffffff;
		padding: 0 30rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		z-index: 100;
	}
	
	.nav-back {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.back-icon {
		font-size: 40rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.nav-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.nav-placeholder {
		width: 60rpx;
	}
	
	.form-block {
		background-color: #ffffff;
		margin-bottom: 30rpx;
	}
	
	.block-title {
		display: flex;
		align-items: center;
		padding: 25rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
	}
	
	.title-bar {
		width: 8rpx;
		height: 40rpx;
		background-color: #1989fa;
		margin-right: 20rpx;
		border-radius: 6rpx;
	}
	
	.block-title text {
		font-size: 34rpx;
		font-weight: 500;
		color: #333;
	}
	
	.form-item {
		padding: 40rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
	}
	
	.form-item:last-child {
		border-bottom: none;
	}
	
	.label {
		display: flex;
		margin-bottom: 30rpx;
		align-items: center;
	}
	
	.label text {
		font-size: 32rpx;
		color: #333;
	}
	
	.label .required {
		color: #ff5a5f;
		margin-left: 6rpx;
	}
	
	.label .tip {
		font-size: 24rpx;
		color: #999999;
		margin-left: 10rpx;
	}
	
	input, textarea {
		width: 100%;
		background-color: #f7f7f7;
		padding: 0 30rpx;
		font-size: 32rpx;
		border-radius: 12rpx;
		box-sizing: border-box;
		height: 92rpx;
		line-height: 92rpx;
	}
	
	.placeholder {
		color: #999;
	}
	
	textarea {
		height: 240rpx;
		line-height: 1.5;
		padding: 30rpx;
	}
	
	.picker-box {
		background-color: #f7f7f7;
		padding: 30rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		position: relative;
		height: 92rpx;
		line-height: 32rpx;
		box-sizing: border-box;
	}
	
	.picker-arrow {
		position: absolute;
		right: 25rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #bbb;
		font-size: 24rpx;
	}
	
	.price-input, .area-input {
		display: flex;
		align-items: center;
		background-color: #f7f7f7;
		border-radius: 12rpx;
		height: 92rpx;
	}
	
	.price-symbol {
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333;
	}
	
	.area-unit {
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333;
	}
	
	.price-input input, .area-input input {
		flex: 1;
		background-color: transparent;
		height: 92rpx;
		line-height: 92rpx;
		padding: 0;
	}
	
	.upload-list {
		display: flex;
		flex-wrap: wrap;
	}
	
	.upload-item, .upload-btn {
		width: 200rpx;
		height: 200rpx;
		margin-right: 26rpx;
		margin-bottom: 26rpx;
		border-radius: 12rpx;
		overflow: hidden;
	}
	
	.upload-item {
		position: relative;
	}
	
	.upload-item image {
		width: 100%;
		height: 100%;
	}
	
	.delete-btn {
		position: absolute;
		top: 0;
		right: 0;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0,0,0,0.5);
		color: #fff;
		text-align: center;
		line-height: 38rpx;
		font-size: 28rpx;
	}
	
	.upload-btn {
		background-color: #f7f7f7;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		border: 1rpx dashed #ddd;
	}
	
	.upload-icon {
		font-size: 56rpx;
		color: #bbb;
		margin-bottom: 10rpx;
	}
	
	.upload-text {
		font-size: 28rpx;
		color: #bbb;
	}
	
	.upload-area {
		position: relative;
	}
	
	.upload-hint {
		position: absolute;
		top: 0;
		right: 0;
		background-color: rgba(0,0,0,0.5);
		color: #fff;
		padding: 5rpx 10rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
	}
	
	.footer-actions {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		padding: 25rpx 30rpx 40rpx;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 10;
	}
	
	.btn-draft, .btn-publish {
		flex: 1;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 32rpx;
		border-radius: 45rpx;
	}
	
	.btn-draft {
		background-color: #f5f5f5;
		color: #666;
		margin-right: 20rpx;
	}
	
	.btn-publish {
		background-color: #1989fa;
		color: #fff;
	}
</style> 