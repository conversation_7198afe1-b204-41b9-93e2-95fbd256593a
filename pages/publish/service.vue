<template>
	<view class="container">
		<view class="form-section">
			<view class="section-title">
				<view class="title-bar"></view>
				<text>基本信息</text>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>发布标题</text>
					<text class="required">*</text>
				</view>
				<input type="text" placeholder="请输入标题，如：金三角地铁站附近快递员急需顶班" v-model="title" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>顶班价格</text>
					<text class="required">*</text>
				</view>
				<view class="price-input">
					<text class="price-symbol">¥</text>
					<input type="digit" placeholder="请输入顶班价格/天" v-model="price" placeholder-class="placeholder" />
				</view>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>所在区域</text>
					<text class="required">*</text>
				</view>
				<picker mode="selector" :range="areas" @change="onAreaChange">
					<view class="picker-value">
						<text v-if="selectedArea">{{areas[selectedArea]}}</text>
						<text v-else class="placeholder">请选择所在区域</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>具体位置</text>
					<text class="required">*</text>
				</view>
				<input type="text" placeholder="请输入具体位置" v-model="address" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>工作时间</text>
					<text class="required">*</text>
				</view>
				<picker mode="selector" :range="workTimes" @change="onWorkTimeChange">
					<view class="picker-value">
						<text v-if="selectedWorkTime !== null">{{workTimes[selectedWorkTime]}}</text>
						<text v-else class="placeholder">请选择工作时间</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="label">日期选择 <text class="required">*</text></text>
				<view class="date-select">
					<picker mode="date" :value="startDate" :start="today" @change="onStartDateChange">
						<view class="date-picker">
							<text v-if="startDate">{{startDate}}</text>
							<text v-else class="placeholder">开始日期</text>
						</view>
					</picker>
					<text class="date-separator">至</text>
					<picker mode="date" :value="endDate" :start="startDate || today" @change="onEndDateChange">
						<view class="date-picker">
							<text v-if="endDate">{{endDate}}</text>
							<text v-else class="placeholder">结束日期</text>
						</view>
					</picker>
				</view>
			</view>
		</view>
		
		<view class="form-section">
			<view class="section-title">
				<view class="title-bar"></view>
				<text>工作描述</text>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>工作类型</text>
					<text class="required">*</text>
				</view>
				<picker mode="selector" :range="jobTypes" @change="onJobTypeChange">
					<view class="picker-value">
						<text v-if="selectedJobType !== null">{{jobTypes[selectedJobType]}}</text>
						<text v-else class="placeholder">请选择工作类型</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="label">快递公司</text>
				<picker mode="selector" :range="expressCompanies" @change="onCompanyChange">
					<view class="picker-value">
						<text v-if="selectedCompany !== null">{{expressCompanies[selectedCompany]}}</text>
						<text v-else class="placeholder">请选择快递公司</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<text class="label">日均件量</text>
				<input type="number" placeholder="请输入日均派件量/揽件量" v-model="dailyPackages" />
			</view>
			
			<view class="form-item">
				<text class="label">工作说明</text>
				<textarea placeholder="请详细描述工作内容、要求等信息" v-model="description" />
			</view>
		</view>
		
		<view class="form-section">
			<view class="section-title">
				<view class="title-bar"></view>
				<text>联系方式</text>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>联系人</text>
					<text class="required">*</text>
				</view>
				<input type="text" placeholder="请输入联系人姓名" v-model="contactName" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>手机号码</text>
					<text class="required">*</text>
				</view>
				<input type="number" placeholder="请输入手机号码" v-model="contactPhone" maxlength="11" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>微信号</text>
				</view>
				<input type="text" placeholder="请输入微信号" v-model="wechatId" placeholder-class="placeholder" />
			</view>
		</view>
		
		<view class="button-group">
			<button class="btn-draft" @tap="saveDraft">保存草稿</button>
			<button class="btn-publish" @tap="publish">立即发布</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			const now = new Date();
			const year = now.getFullYear();
			const month = (now.getMonth() + 1).toString().padStart(2, '0');
			const day = now.getDate().toString().padStart(2, '0');
			const today = `${year}-${month}-${day}`;
			
			return {
				title: '',
				price: '',
				selectedArea: null,
				areas: ['海淀区', '朝阳区', '西城区', '东城区', '丰台区', '石景山区', '通州区', '昌平区', '大兴区', '顺义区', '房山区', '门头沟区', '平谷区', '密云区', '怀柔区', '延庆区'],
				address: '',
				selectedWorkTime: null,
				workTimes: ['全天班（8-10小时）', '上午班（4-5小时）', '下午班（4-5小时）', '晚班（4-5小时）', '其他时间'],
				today: today,
				startDate: '',
				endDate: '',
				selectedJobType: null,
				jobTypes: ['快递员', '分拣员', '驿站业务员', '仓库管理员', '其他'],
				selectedCompany: null,
				expressCompanies: ['顺丰速运', '中通快递', '圆通速递', '申通快递', '韵达快递', '百世快递', '京东物流', '邮政EMS', '其他'],
				dailyPackages: '',
				description: '',
				contactName: '',
				contactPhone: '',
				wechatId: ''
			}
		},
		methods: {
			onAreaChange(e) {
				this.selectedArea = e.detail.value;
			},
			onWorkTimeChange(e) {
				this.selectedWorkTime = e.detail.value;
			},
			onStartDateChange(e) {
				this.startDate = e.detail.value;
				// 如果结束日期早于开始日期，重置结束日期
				if (this.endDate && this.endDate < this.startDate) {
					this.endDate = '';
				}
			},
			onEndDateChange(e) {
				this.endDate = e.detail.value;
			},
			onJobTypeChange(e) {
				this.selectedJobType = e.detail.value;
			},
			onCompanyChange(e) {
				this.selectedCompany = e.detail.value;
			},
			validateForm() {
				if (!this.title.trim()) {
					uni.showToast({
						title: '请输入发布标题',
						icon: 'none'
					});
					return false;
				}
				if (!this.price.trim()) {
					uni.showToast({
						title: '请输入顶班价格',
						icon: 'none'
					});
					return false;
				}
				if (this.selectedArea === null) {
					uni.showToast({
						title: '请选择所在区域',
						icon: 'none'
					});
					return false;
				}
				if (!this.address.trim()) {
					uni.showToast({
						title: '请输入具体位置',
						icon: 'none'
					});
					return false;
				}
				if (this.selectedWorkTime === null) {
					uni.showToast({
						title: '请选择工作时间',
						icon: 'none'
					});
					return false;
				}
				if (!this.startDate) {
					uni.showToast({
						title: '请选择开始日期',
						icon: 'none'
					});
					return false;
				}
				if (!this.endDate) {
					uni.showToast({
						title: '请选择结束日期',
						icon: 'none'
					});
					return false;
				}
				if (this.selectedJobType === null) {
					uni.showToast({
						title: '请选择工作类型',
						icon: 'none'
					});
					return false;
				}
				if (!this.contactName.trim()) {
					uni.showToast({
						title: '请输入联系人姓名',
						icon: 'none'
					});
					return false;
				}
				if (!this.contactPhone.trim()) {
					uni.showToast({
						title: '请输入手机号码',
						icon: 'none'
					});
					return false;
				}
				
				return true;
			},
			saveDraft() {
				// 保存草稿逻辑
				uni.showToast({
					title: '已保存草稿',
					icon: 'success'
				});
			},
			publish() {
				if (this.validateForm()) {
					// 发布逻辑
					uni.showLoading({
						title: '发布中...'
					});
					
					// 创建新的顶班服务信息
					const newServiceInfo = {
						id: Date.now(), // 使用时间戳作为临时ID
						title: this.title,
						price: this.price + '元/天',
						time: this.workTimes[this.selectedWorkTime],
						date: this.startDate + ' 至 ' + this.endDate,
						address: this.areas[this.selectedArea] + ' ' + this.address,
						tags: [
							this.jobTypes[this.selectedJobType],
							this.expressCompanies[this.selectedCompany] || '无指定公司',
							'日均' + (this.dailyPackages || '不详') + '件'
						],
						description: this.description || '无详细描述',
						publishTime: '刚刚发布',
						contactInfo: {
							name: this.contactName,
							phone: this.contactPhone,
							wechat: this.wechatId || '未提供'
						}
					};
					
					// 获取已有的服务列表
					let serviceList = uni.getStorageSync('serviceList') || [];
					
					// 将新服务信息添加到列表开头
					serviceList.unshift(newServiceInfo);
					
					// 保存更新后的服务列表
					uni.setStorageSync('serviceList', serviceList);
					
					// 模拟提交请求
					setTimeout(() => {
						uni.hideLoading();
						uni.showToast({
							title: '发布成功',
							icon: 'success',
							duration: 1500
						});
						
						// 发布成功后跳转到顶班服务页面
						setTimeout(() => {
							uni.navigateTo({
								url: '/pages/service/service'
							});
						}, 1500);
					}, 1000);
				}
			}
		}
	}
</script>

<style>
	.container {
		padding-bottom: 120rpx;
		background-color: #f7f7f7;
	}
	
	.form-section {
		background-color: #ffffff;
		margin-bottom: 30rpx;
	}
	
	.section-title {
		display: flex;
		align-items: center;
		padding: 25rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
	}
	
	.title-bar {
		width: 8rpx;
		height: 40rpx;
		background-color: #1989fa;
		margin-right: 20rpx;
		border-radius: 6rpx;
	}
	
	.section-title text {
		font-size: 34rpx;
		font-weight: 500;
		color: #333;
	}
	
	.form-item {
		padding: 40rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
	}
	
	.form-item:last-child {
		border-bottom: none;
	}
	
	.label {
		display: flex;
		margin-bottom: 30rpx;
		font-size: 32rpx;
		color: #333;
	}
	
	.required {
		color: #ff4d4f;
		margin-left: 6rpx;
	}
	
	input, textarea {
		width: 100%;
		background-color: #f7f7f7;
		padding: 0 30rpx;
		font-size: 32rpx;
		border-radius: 12rpx;
		box-sizing: border-box;
		height: 92rpx;
		line-height: 92rpx;
	}
	
	.placeholder {
		color: #999;
	}
	
	textarea {
		height: 240rpx;
		line-height: 1.5;
		padding: 30rpx;
	}
	
	.picker-value {
		background-color: #f7f7f7;
		padding: 30rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		position: relative;
		height: 92rpx;
		line-height: 32rpx;
		box-sizing: border-box;
	}
	
	.picker-arrow {
		position: absolute;
		right: 25rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #bbb;
		font-size: 24rpx;
	}
	
	.price-input {
		display: flex;
		align-items: center;
		background-color: #f7f7f7;
		border-radius: 12rpx;
		height: 92rpx;
	}
	
	.price-symbol {
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333;
	}
	
	.price-input input {
		flex: 1;
		background-color: transparent;
		height: 92rpx;
		line-height: 92rpx;
		padding: 0;
	}
	
	.date-select {
		display: flex;
		align-items: center;
	}
	
	.date-picker {
		flex: 1;
		background-color: #f7f7f7;
		padding: 30rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		height: 92rpx;
		line-height: 32rpx;
		box-sizing: border-box;
	}
	
	.date-separator {
		padding: 0 20rpx;
		font-size: 32rpx;
		color: #333;
	}
	
	.button-group {
		display: flex;
		padding: 25rpx 30rpx 40rpx;
		background-color: #fff;
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 10;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.btn-draft, .btn-publish {
		flex: 1;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 32rpx;
		border-radius: 45rpx;
	}
	
	.btn-draft {
		background-color: #f5f5f5;
		color: #666;
		margin-right: 20rpx;
	}
	
	.btn-publish {
		background-color: #1989fa;
		color: #fff;
	}
</style> 