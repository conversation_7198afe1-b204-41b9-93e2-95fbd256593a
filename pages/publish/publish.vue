<template>
	<view class="container">
		<!-- 顶部标题区域 -->
		<view class="header-section">
			<view class="title">选择发布类型</view>
			<view class="subtitle">一键发布，轻松共享</view>
		</view>
		
		<!-- 选项卡片区域 -->
		<view class="option-cards">
			<view class="option-card" @tap="navigateTo('/pages/publish/station')">
				<view class="card-icon station">
					<view class="icon-box">
						<image src="/static/icons/station.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="card-content">
					<view class="card-title">驿站转让</view>
					<view class="card-desc">快速找到优质接手方</view>
				</view>
				<view class="card-arrow">〉</view>
			</view>
			
			<view class="option-card" @tap="navigateTo('/pages/publish/service')">
				<view class="card-icon service">
					<view class="icon-box">
						<image src="/static/icons/service.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="card-content">
					<view class="card-title">发布顶班</view>
					<view class="card-desc">发布临时顶班需求信息</view>
				</view>
				<view class="card-arrow">〉</view>
			</view>
			
			<view class="option-card" @tap="navigateTo('/pages/publish/job')">
				<view class="card-icon job">
					<view class="icon-box">
						<image src="/static/icons/job.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="card-content">
					<view class="card-title">驿站招聘</view>
					<view class="card-desc">招募驿站工作人员</view>
				</view>
				<view class="card-arrow">〉</view>
			</view>
			
			<view class="option-card" @tap="navigateTo('/pages/publish/device')">
				<view class="card-icon device">
					<view class="icon-box">
						<image src="/static/icons/device.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="card-content">
					<view class="card-title">出售设备</view>
					<view class="card-desc">二手设备交易平台</view>
				</view>
				<view class="card-arrow">〉</view>
			</view>
		</view>
		
		<!-- 发布小贴士 -->
		<view class="publish-tips">
			<view class="tips-title">
				<view class="tips-icon">
					<image src="/static/icons/tips.png" mode="aspectFit"></image>
				</view>
				<text>发布小贴士</text>
			</view>
			
			<view class="tips-list">
				<view class="tip-item">
					<text class="dot">•</text>
					<text>发布的内容需真实有效</text>
				</view>
				<view class="tip-item">
					<text class="dot">•</text>
					<text>信息审核通过后才能展示</text>
				</view>
				<view class="tip-item">
					<text class="dot">•</text>
					<text>优质发布内容有机会获得推荐</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {}
		},
		methods: {
			navigateTo(url) {
				uni.navigateTo({
					url: url
				})
			}
		}
	}
</script>

<style>
	.container {
		min-height: 100vh;
		background-color: #f8f8f8;
	}
	
	.header-section {
		background: linear-gradient(to right, #ff758c, #ff7eb3);
		padding: 60rpx 30rpx;
		text-align: center;
		color: #ffffff;
		border-radius: 0 0 30rpx 30rpx;
		margin-bottom: 40rpx;
	}
	
	.title {
		font-size: 42rpx;
		font-weight: bold;
		margin-bottom: 15rpx;
	}
	
	.subtitle {
		font-size: 28rpx;
		opacity: 0.9;
	}
	
	.option-cards {
		padding: 0 30rpx;
	}
	
	.option-card {
		background-color: #ffffff;
		border-radius: 15rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.card-icon {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		margin-right: 30rpx;
		position: relative;
	}
	
	.icon-box {
		width: 100%;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.card-icon image {
		width: 50rpx;
		height: 50rpx;
	}
	
	.station {
		background-color: #ffeff0;
	}
	
	.service {
		background-color: #e8f4ff;
	}
	
	.job {
		background-color: #f0e8ff;
	}
	
	.device {
		background-color: #ffe8e8;
	}
	
	.card-content {
		flex: 1;
	}
	
	.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
	}
	
	.card-desc {
		font-size: 24rpx;
		color: #999999;
	}
	
	.card-arrow {
		color: #cccccc;
		font-size: 32rpx;
	}
	
	.publish-tips {
		margin: 30rpx 30rpx 80rpx 30rpx;
		background-color: #ffffff;
		border-radius: 15rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.tips-title {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.tips-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 10rpx;
	}
	
	.tips-icon image {
		width: 100%;
		height: 100%;
	}
	
	.tips-list {
		padding-left: 20rpx;
	}
	
	.tip-item {
		display: flex;
		align-items: flex-start;
		font-size: 26rpx;
		color: #ff758c;
		line-height: 2;
	}
	
	.dot {
		margin-right: 10rpx;
	}
</style> 