<template>
	<view class="container">
		<view class="form-section">
			<view class="section-title">
				<view class="title-bar"></view>
				<text>基本信息</text>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>设备名称</text>
					<text class="required">*</text>
				</view>
				<input type="text" placeholder="请输入设备名称，如：标签打印机" v-model="title" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>设备价格</text>
					<text class="required">*</text>
				</view>
				<view class="price-input">
					<text class="price-symbol">¥</text>
					<input type="digit" placeholder="请输入设备价格" v-model="price" placeholder-class="placeholder" />
				</view>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>设备类型</text>
					<text class="required">*</text>
				</view>
				<picker mode="selector" :range="deviceTypes" @change="onDeviceTypeChange">
					<view class="picker-value">
						<text v-if="selectedDeviceType !== null">{{deviceTypes[selectedDeviceType]}}</text>
						<text v-else class="placeholder">请选择设备类型</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>设备品牌</text>
				</view>
				<input type="text" placeholder="请输入设备品牌" v-model="brand" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>新旧程度</text>
					<text class="required">*</text>
				</view>
				<picker mode="selector" :range="conditions" @change="onConditionChange">
					<view class="picker-value">
						<text v-if="selectedCondition !== null">{{conditions[selectedCondition]}}</text>
						<text v-else class="placeholder">请选择新旧程度</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>所在区域</text>
					<text class="required">*</text>
				</view>
				<picker mode="selector" :range="areas" @change="onAreaChange">
					<view class="picker-value">
						<text v-if="selectedArea !== null">{{areas[selectedArea]}}</text>
						<text v-else class="placeholder">请选择所在区域</text>
						<text class="picker-arrow">▼</text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>具体位置</text>
				</view>
				<input type="text" placeholder="请输入详细地址" v-model="address" placeholder-class="placeholder" />
			</view>
		</view>
		
		<view class="form-section">
			<view class="section-title">
				<view class="title-bar"></view>
				<text>设备详情</text>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>设备描述</text>
					<text class="required">*</text>
				</view>
				<textarea placeholder="请详细描述设备的参数、配置、使用情况等信息" v-model="description" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<text class="label">上传照片 <text class="required">*</text></text>
				<view class="upload-area">
					<view class="upload-list">
						<view class="upload-item" v-for="(item, index) in uploadImages" :key="index">
							<image :src="item" mode="aspectFill"></image>
							<view class="delete-btn" @tap="deleteImage(index)">×</view>
						</view>
						<view class="upload-btn" @tap="chooseImage" v-if="uploadImages.length < 6">
							<text class="upload-icon">+</text>
							<text class="upload-text">上传图片</text>
						</view>
					</view>
					<text class="upload-tips">最多上传6张图片</text>
				</view>
			</view>
			
			<view class="form-item">
				<text class="label">交易方式</text>
				<view class="checkbox-group">
					<view class="checkbox-item" :class="{'checked': tradeWays.includes('self')}" @tap="toggleTradeWay('self')">
						<text class="checkbox"></text>
						<text>自提</text>
					</view>
					<view class="checkbox-item" :class="{'checked': tradeWays.includes('deliver')}" @tap="toggleTradeWay('deliver')">
						<text class="checkbox"></text>
						<text>送货上门</text>
					</view>
					<view class="checkbox-item" :class="{'checked': tradeWays.includes('express')}" @tap="toggleTradeWay('express')">
						<text class="checkbox"></text>
						<text>快递物流</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="form-section">
			<view class="section-title">
				<view class="title-bar"></view>
				<text>联系方式</text>
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>联系人</text>
					<text class="required">*</text>
				</view>
				<input type="text" placeholder="请输入联系人姓名" v-model="contactName" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>手机号码</text>
					<text class="required">*</text>
				</view>
				<input type="number" placeholder="请输入手机号码" v-model="contactPhone" maxlength="11" placeholder-class="placeholder" />
			</view>
			
			<view class="form-item">
				<view class="label">
					<text>微信号</text>
				</view>
				<input type="text" placeholder="请输入微信号" v-model="wechatId" placeholder-class="placeholder" />
			</view>
		</view>
		
		<view class="button-group">
			<button class="btn-draft" @tap="saveDraft">保存草稿</button>
			<button class="btn-publish" @tap="publish">立即发布</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				title: '',
				price: '',
				selectedDeviceType: null,
				deviceTypes: ['扫描设备', '打印设备', '货架柜子', '电子秤', '运输工具', '其他设备'],
				brand: '',
				selectedCondition: null,
				conditions: ['全新', '9成新', '8成新', '7成新', '6成新', '5成新及以下'],
				selectedArea: null,
				areas: ['海淀区', '朝阳区', '西城区', '东城区', '丰台区', '石景山区', '通州区', '昌平区', '大兴区', '顺义区', '房山区', '门头沟区', '平谷区', '密云区', '怀柔区', '延庆区'],
				address: '',
				description: '',
				uploadImages: [],
				tradeWays: ['self'], // 默认选中自提
				contactName: '',
				contactPhone: '',
				wechatId: ''
			}
		},
		methods: {
			onDeviceTypeChange(e) {
				this.selectedDeviceType = e.detail.value;
			},
			onConditionChange(e) {
				this.selectedCondition = e.detail.value;
			},
			onAreaChange(e) {
				this.selectedArea = e.detail.value;
			},
			chooseImage() {
				const that = this;
				uni.chooseImage({
					count: 6 - that.uploadImages.length,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: function(res) {
						that.uploadImages = [...that.uploadImages, ...res.tempFilePaths];
					}
				});
			},
			deleteImage(index) {
				this.uploadImages.splice(index, 1);
			},
			toggleTradeWay(way) {
				const index = this.tradeWays.indexOf(way);
				if (index === -1) {
					this.tradeWays.push(way);
				} else {
					// 至少要保留一种交易方式
					if (this.tradeWays.length > 1) {
						this.tradeWays.splice(index, 1);
					}
				}
			},
			validateForm() {
				if (!this.title.trim()) {
					uni.showToast({
						title: '请输入设备名称',
						icon: 'none'
					});
					return false;
				}
				if (!this.price.trim()) {
					uni.showToast({
						title: '请输入设备价格',
						icon: 'none'
					});
					return false;
				}
				if (this.selectedDeviceType === null) {
					uni.showToast({
						title: '请选择设备类型',
						icon: 'none'
					});
					return false;
				}
				if (this.selectedCondition === null) {
					uni.showToast({
						title: '请选择新旧程度',
						icon: 'none'
					});
					return false;
				}
				if (this.selectedArea === null) {
					uni.showToast({
						title: '请选择所在区域',
						icon: 'none'
					});
					return false;
				}
				if (!this.description.trim()) {
					uni.showToast({
						title: '请输入设备描述',
						icon: 'none'
					});
					return false;
				}
				if (this.uploadImages.length === 0) {
					uni.showToast({
						title: '请上传设备照片',
						icon: 'none'
					});
					return false;
				}
				if (!this.contactName.trim()) {
					uni.showToast({
						title: '请输入联系人姓名',
						icon: 'none'
					});
					return false;
				}
				if (!this.contactPhone.trim()) {
					uni.showToast({
						title: '请输入手机号码',
						icon: 'none'
					});
					return false;
				}
				
				return true;
			},
			saveDraft() {
				// 保存草稿逻辑
				uni.showToast({
					title: '已保存草稿',
					icon: 'success'
				});
			},
			publish() {
				if (this.validateForm()) {
					// 发布逻辑
					uni.showLoading({
						title: '发布中...'
					});
					
					// 创建新的设备信息
					const newDeviceInfo = {
						id: Date.now(), // 使用时间戳作为临时ID
						title: this.title,
						price: this.price,
						location: '北京·' + this.areas[this.selectedArea],
						type: this.deviceTypes[this.selectedDeviceType],
						images: this.uploadImages.length > 0 ? this.uploadImages : ['/static/device/default.jpg'],
						condition: this.conditions[this.selectedCondition],
						publishTime: '刚刚发布',
						contactInfo: {
							name: this.contactName,
							phone: this.contactPhone,
							wechat: this.wechatId || '未提供'
						}
					};
					
					// 获取已有的设备列表
					let deviceList = uni.getStorageSync('deviceList') || [];
					
					// 将新设备信息添加到列表开头
					deviceList.unshift(newDeviceInfo);
					
					// 保存更新后的设备列表
					uni.setStorageSync('deviceList', deviceList);
					
					// 模拟提交请求
					setTimeout(() => {
						uni.hideLoading();
						uni.showToast({
							title: '发布成功',
							icon: 'success',
							duration: 1500
						});
						
						// 发布成功后跳转到设备交易页面
						setTimeout(() => {
							uni.navigateTo({
								url: '/pages/device/device'
							});
						}, 1500);
					}, 1000);
				}
			}
		}
	}
</script>

<style>
	.container {
		padding-bottom: 120rpx;
		background-color: #f7f7f7;
	}
	
	.form-section {
		background-color: #ffffff;
		margin-bottom: 30rpx;
	}
	
	.section-title {
		display: flex;
		align-items: center;
		padding: 25rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
	}
	
	.title-bar {
		width: 8rpx;
		height: 40rpx;
		background-color: #1989fa;
		margin-right: 20rpx;
		border-radius: 6rpx;
	}
	
	.section-title text {
		font-size: 34rpx;
		font-weight: 500;
		color: #333;
	}
	
	.form-item {
		padding: 40rpx 30rpx;
		border-bottom: 1rpx solid #f2f2f2;
	}
	
	.form-item:last-child {
		border-bottom: none;
	}
	
	.label {
		display: flex;
		margin-bottom: 30rpx;
		font-size: 32rpx;
		color: #333;
	}
	
	.required {
		color: #ff4d4f;
		margin-left: 6rpx;
	}
	
	input, textarea {
		width: 100%;
		background-color: #f7f7f7;
		padding: 0 30rpx;
		font-size: 32rpx;
		border-radius: 12rpx;
		box-sizing: border-box;
		height: 92rpx;
		line-height: 92rpx;
	}
	
	.placeholder {
		color: #999;
	}
	
	textarea {
		height: 240rpx;
		line-height: 1.5;
		padding: 30rpx;
	}
	
	.picker-value {
		background-color: #f7f7f7;
		padding: 30rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		position: relative;
		height: 92rpx;
		line-height: 32rpx;
		box-sizing: border-box;
	}
	
	.picker-arrow {
		position: absolute;
		right: 25rpx;
		top: 50%;
		transform: translateY(-50%);
		color: #bbb;
		font-size: 24rpx;
	}
	
	.price-input {
		display: flex;
		align-items: center;
		background-color: #f7f7f7;
		border-radius: 12rpx;
		height: 92rpx;
	}
	
	.price-symbol {
		padding: 0 30rpx;
		font-size: 32rpx;
		color: #333;
	}
	
	.price-input input {
		flex: 1;
		background-color: transparent;
		height: 92rpx;
		line-height: 92rpx;
		padding: 0;
	}
	
	.upload-area {
		margin-top: 20rpx;
	}
	
	.upload-list {
		display: flex;
		flex-wrap: wrap;
	}
	
	.upload-item, .upload-btn {
		width: 200rpx;
		height: 200rpx;
		margin-right: 26rpx;
		margin-bottom: 26rpx;
		border-radius: 12rpx;
		overflow: hidden;
	}
	
	.upload-item {
		position: relative;
	}
	
	.upload-item image {
		width: 100%;
		height: 100%;
	}
	
	.delete-btn {
		position: absolute;
		top: 0;
		right: 0;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0,0,0,0.5);
		color: #fff;
		text-align: center;
		line-height: 38rpx;
		font-size: 28rpx;
	}
	
	.upload-btn {
		background-color: #f7f7f7;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		border: 1rpx dashed #ddd;
	}
	
	.upload-icon {
		font-size: 56rpx;
		color: #bbb;
		margin-bottom: 10rpx;
	}
	
	.upload-text {
		font-size: 28rpx;
		color: #bbb;
	}
	
	.upload-tips {
		font-size: 28rpx;
		color: #999;
		margin-top: 10rpx;
	}
	
	.checkbox-group {
		display: flex;
		flex-wrap: wrap;
	}
	
	.checkbox-item {
		display: flex;
		align-items: center;
		margin-right: 40rpx;
		margin-bottom: 20rpx;
		font-size: 32rpx;
	}
	
	.checkbox {
		width: 36rpx;
		height: 36rpx;
		border: 1rpx solid #ddd;
		border-radius: 36rpx;
		margin-right: 10rpx;
		position: relative;
	}
	
	.checkbox-item.checked .checkbox:after {
		content: "";
		position: absolute;
		width: 24rpx;
		height: 24rpx;
		background-color: #1989fa;
		border-radius: 24rpx;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}
	
	.button-group {
		display: flex;
		padding: 25rpx 30rpx 40rpx;
		background-color: #fff;
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 10;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	.btn-draft, .btn-publish {
		flex: 1;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 32rpx;
		border-radius: 45rpx;
	}
	
	.btn-draft {
		background-color: #f5f5f5;
		color: #666;
		margin-right: 20rpx;
	}
	
	.btn-publish {
		background-color: #1989fa;
		color: #fff;
	}
</style> 