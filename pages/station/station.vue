<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input-box" @tap="showSearch">
				<image class="search-icon" src="../../static/icons/search.png" mode="aspectFit"></image>
				<input 
					type="text" 
					placeholder="搜索驿站用品" 
					v-model="searchKeyword" 
					confirm-type="search" 
					@confirm="handleSearch"
					@focus="showSearchPanel = true"
					@input="onSearchInput" 
				/>
				<view class="clear-btn" v-if="searchKeyword" @tap.stop="clearSearch">
					<image src="../../static/icons/close.png" mode="aspectFit"></image>
				</view>
			</view>
			<view class="filter-btn" @tap="showFilter">
				<image src="../../static/icons/filter.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 搜索面板 -->
		<view class="search-panel" v-if="showSearchPanel">
			<!-- 搜索头部 -->
			<view class="search-panel-header">
				<view class="search-input-container">
					<image class="search-icon" src="../../static/icons/search.png" mode="aspectFit"></image>
					<input 
						type="text" 
						placeholder="搜索驿站用品" 
						v-model="searchKeyword" 
						confirm-type="search" 
						focus 
						@confirm="handleSearch"
						@input="onSearchInput" 
					/>
					<view class="clear-btn" v-if="searchKeyword" @tap.stop="clearSearch">
						<image src="../../static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="cancel-btn" @tap="closeSearchPanel">
					<text>取消</text>
				</view>
			</view>
			
			<!-- 搜索内容区域 -->
			<scroll-view class="search-panel-content" scroll-y>
				<!-- 搜索结果 -->
				<view class="search-results" v-if="searchKeyword && searchResults.length > 0">
					<view class="section-title">搜索结果</view>
					<view class="result-list">
						<view class="result-item" v-for="(item, index) in searchResults" :key="index" @tap="selectSearchResult(item)">
							<image class="result-icon" src="../../static/icons/search-result.png" mode="aspectFit"></image>
							<view class="result-info">
								<rich-text class="result-title" :nodes="highlightKeyword(item.title)"></rich-text>
								<view class="result-tags" v-if="item.tags && item.tags.length > 0">
									<text class="result-tag" v-for="(tag, tagIndex) in item.tags.slice(0, 2)" :key="tagIndex">{{tag}}</text>
								</view>
							</view>
							<view class="result-category">{{getCategoryName(item.category)}}</view>
						</view>
					</view>
				</view>
				
				<!-- 无搜索结果提示 -->
				<view class="no-results" v-if="searchKeyword && !isSearching && searchResults.length === 0">
					<image class="no-result-icon" src="../../static/icons/no-result.png" mode="aspectFit"></image>
					<text class="no-result-text">未找到与"{{searchKeyword}}"相关的商品</text>
					<text class="no-result-tip">换个关键词试试吧</text>
				</view>
				
				<!-- 搜索历史 -->
				<view class="search-history" v-if="searchHistory.length > 0 && !searchKeyword">
					<view class="history-header">
						<text class="section-title">搜索历史</text>
						<view class="clear-history" @tap="clearHistory">
							<image src="../../static/icons/delete.png" mode="aspectFit"></image>
							<text>清空</text>
						</view>
					</view>
					<view class="history-list">
						<view class="history-item" v-for="(item, index) in searchHistory" :key="index" @tap="useHistoryKeyword(item)">
							<image class="history-icon" src="../../static/icons/history.png" mode="aspectFit"></image>
							<text>{{item}}</text>
						</view>
					</view>
				</view>
				
				<!-- 热门搜索 -->
				<view class="hot-searches" v-if="!searchKeyword">
					<view class="section-title">热门搜索</view>
					<view class="hot-list">
						<view class="hot-item" v-for="(item, index) in hotSearches" :key="index" @tap="useHistoryKeyword(item)">
							<view class="hot-rank" :class="'rank-' + (index + 1)" v-if="index < 3">{{index + 1}}</view>
							<text>{{item}}</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		
		<!-- 分类导航 -->
		<view class="category-nav">
			<view class="category-item" :class="{'active': activeCategory === 'all'}" @tap="changeCategory('all')">
				<image src="../../static/icons/all.png" mode="aspectFit"></image>
				<text>全部</text>
			</view>
			<view class="category-item" :class="{'active': activeCategory === 'scales'}" @tap="changeCategory('scales')">
				<image src="../../static/icons/scales.png" mode="aspectFit"></image>
				<text>电子秤</text>
			</view>
			<view class="category-item" :class="{'active': activeCategory === 'printer'}" @tap="changeCategory('printer')">
				<image src="../../static/icons/printer.png" mode="aspectFit"></image>
				<text>打印机</text>
			</view>
			<view class="category-item" :class="{'active': activeCategory === 'shelves'}" @tap="changeCategory('shelves')">
				<image src="../../static/icons/shelves.png" mode="aspectFit"></image>
				<text>货架柜</text>
			</view>
			<view class="category-item" :class="{'active': activeCategory === 'outdevice'}" @tap="changeCategory('outdevice')">
				<image src="../../static/icons/all.png" mode="aspectFit"></image>
				<text>出库仪</text>
			</view>
			<view class="category-item" :class="{'active': activeCategory === 'lightstrip'}" @tap="changeCategory('lightstrip')">
				<image src="../../static/icons/all.png" mode="aspectFit"></image>
				<text>智能灯带</text>
			</view>
		</view>
		
		<!-- 快捷筛选 -->
		<view class="quick-filter">
			<view class="filter-tab" :class="{'active': activeTab === 'all'}" @tap="changeTab('all')">综合</view>
			<view class="filter-tab" :class="{'active': activeTab === 'sales'}" @tap="changeTab('sales')">销量</view>
			<view class="filter-tab" :class="{'active': activeTab === 'newest'}" @tap="changeTab('newest')">新品</view>
			<view class="filter-tab" :class="{'active': activeTab === 'price'}" @tap="changeTab('price')">价格
				<text class="sort-arrow" v-if="activeTab === 'price'">{{ priceSortAsc ? '↑' : '↓' }}</text>
			</view>
		</view>
		
		<!-- 筛选区域 -->
		<view class="filter-section" v-if="showFilterSection">
			<view class="filter-row">
				<view class="filter-title">价格区间</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': activePrice === 'all'}" @tap="selectPrice('all')">全部</view>
					<view class="filter-option" :class="{'active': activePrice === 'low'}" @tap="selectPrice('low')">0-500元</view>
					<view class="filter-option" :class="{'active': activePrice === 'medium'}" @tap="selectPrice('medium')">500-2000元</view>
					<view class="filter-option" :class="{'active': activePrice === 'high'}" @tap="selectPrice('high')">2000元以上</view>
				</view>
			</view>
			<view class="filter-row">
				<view class="filter-title">商品类型</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': activeType === 'all'}" @tap="selectType('all')">全部</view>
					<view class="filter-option" :class="{'active': activeType === 'new'}" @tap="selectType('new')">全新</view>
					<view class="filter-option" :class="{'active': activeType === 'used'}" @tap="selectType('used')">二手</view>
					<view class="filter-option" :class="{'active': activeType === 'refurbished'}" @tap="selectType('refurbished')">翻新</view>
				</view>
			</view>
			<view class="filter-actions">
				<view class="reset-btn" @tap="resetFilter">重置</view>
				<view class="confirm-btn" @tap="applyFilter">确认</view>
			</view>
		</view>
		
		<!-- 商品列表 (改为网格布局) -->
		<view class="product-grid">
			<view class="product-card" v-for="(item, index) in productList" :key="index" @tap="viewDetail(item)">
				<view class="product-image-placeholder">
					<text>商品图片</text>
				</view>
				<view class="product-info">
					<view class="product-title">{{item.title}}</view>
					<view class="product-tags">
						<text class="product-tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
					</view>
					<view class="product-price">¥<text class="price-value">{{item.price}}</text></view>
					<view class="product-sales">已售{{item.sales}}件</view>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view class="loading-more" v-if="hasMore">
			<text>正在加载更多...</text>
		</view>
		<view class="no-more" v-else>
			<text>没有更多数据了</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchKeyword: '',
				showFilterSection: false,
				activeCategory: 'all',
				activePrice: 'all',
				activeType: 'all',
				activeTab: 'all',
				priceSortAsc: false,
				hasMore: true,
				pageNum: 1,
				pageSize: 10,
				allProducts: [
					{
						id: 1,
						title: '菜鸟驿站专用电子秤 高精度计价台秤',
						tags: ['全新', '顺丰可用', '包邮'],
						price: '298',
						sales: 235,
						image: '/static/images/scale1.jpg',
						category: 'scales',
						condition: 'new',
						priceRange: 'low',
						publishTime: '2023-12-15'
					},
					{
						id: 2,
						title: '热敏标签打印机 快递面单打印',
						tags: ['二手9成新', '通用', '送纸'],
						price: '880',
						sales: 158,
						image: '/static/images/printer1.jpg',
						category: 'printer',
						condition: 'used',
						priceRange: 'medium',
						publishTime: '2023-11-20'
					},
					{
						id: 3,
						title: '驿站分拣架 多层快递存放柜',
						tags: ['全新', '多规格', '可定制'],
						price: '1680',
						sales: 86,
						image: '/static/images/shelf1.jpg',
						category: 'shelves',
						condition: 'new',
						priceRange: 'medium',
						publishTime: '2023-10-25'
					},
					{
						id: 4,
						title: '二手条码扫描枪 有线扫描器',
						tags: ['二手', '通用', '保修3个月'],
						price: '120',
						sales: 367,
						image: '/static/images/scanner1.jpg',
						category: 'scanner',
						condition: 'used',
						priceRange: 'low',
						publishTime: '2024-01-10'
					},
					{
						id: 5,
						title: '快递打包封箱机 胶带切割器',
						tags: ['全新', '包邮', '送胶带'],
						price: '89',
						sales: 512,
						image: '/static/images/sealer1.jpg',
						category: 'other',
						condition: 'new',
						priceRange: 'low',
						publishTime: '2024-02-05'
					},
					// 添加更多电子秤产品
					{
						id: 6,
						title: '高精度电子台秤 不锈钢称重平台',
						tags: ['全新', '带蓝牙', '大显示屏'],
						price: '458',
						sales: 127,
						image: '/static/images/scale2.jpg',
						category: 'scales',
						condition: 'new',
						priceRange: 'low',
						publishTime: '2024-01-12'
					},
					{
						id: 7,
						title: '快递专用电子秤 防水耐用型',
						tags: ['全新', '顺丰专供', '快递可用'],
						price: '329',
						sales: 203,
						image: '/static/images/scale3.jpg',
						category: 'scales',
						condition: 'new',
						priceRange: 'low',
						publishTime: '2023-11-15'
					},
					{
						id: 8,
						title: '二手电子台秤 商用精准计价秤',
						tags: ['二手8成新', '校准精准', '送充电器'],
						price: '199',
						sales: 89,
						image: '/static/images/scale4.jpg',
						category: 'scales',
						condition: 'used',
						priceRange: 'low',
						publishTime: '2023-12-20'
					},
					// 添加更多打印机产品
					{
						id: 9,
						title: '热敏不干胶标签打印机 网口USB接口',
						tags: ['全新', '高速打印', '驱动安装简单'],
						price: '1180',
						sales: 97,
						image: '/static/images/printer2.jpg',
						category: 'printer',
						condition: 'new',
						priceRange: 'medium',
						publishTime: '2024-01-25'
					},
					{
						id: 10,
						title: '二手立象条码打印机 工业级标签机',
						tags: ['二手9成新', '高清打印', '兼容性好'],
						price: '850',
						sales: 65,
						image: '/static/images/printer3.jpg',
						category: 'printer',
						condition: 'used',
						priceRange: 'medium',
						publishTime: '2023-10-18'
					},
					{
						id: 11,
						title: '快递面单打印机 热敏打印 自动切纸',
						tags: ['全新', '兼容多快递', '一键打印'],
						price: '1280',
						sales: 155,
						image: '/static/images/printer4.jpg',
						category: 'printer',
						condition: 'new',
						priceRange: 'medium',
						publishTime: '2024-02-08'
					},
					// 添加更多货架柜产品
					{
						id: 12,
						title: '驿站快递分拣柜 多格收纳架',
						tags: ['全新', '组装简单', '可扩展'],
						price: '1280',
						sales: 72,
						image: '/static/images/shelf2.jpg',
						category: 'shelves',
						condition: 'new',
						priceRange: 'medium',
						publishTime: '2023-11-30'
					},
					{
						id: 13,
						title: '钢制货架 多层重型仓储架',
						tags: ['全新', '承重大', '稳固耐用'],
						price: '2380',
						sales: 56,
						image: '/static/images/shelf3.jpg',
						category: 'shelves',
						condition: 'new',
						priceRange: 'high',
						publishTime: '2024-01-18'
					},
					{
						id: 14,
						title: '可移动快递分拣车 多层推车',
						tags: ['全新', '带刹车', '方便移动'],
						price: '860',
						sales: 108,
						image: '/static/images/shelf4.jpg',
						category: 'shelves',
						condition: 'new',
						priceRange: 'medium',
						publishTime: '2023-12-05'
					},
					{
						id: 15,
						title: '大容量包裹存放柜 DIY组装',
						tags: ['全新', '经济实惠', '包安装'],
						price: '980',
						sales: 87,
						image: '/static/images/shelf5.jpg',
						category: 'shelves',
						condition: 'new',
						priceRange: 'medium',
						publishTime: '2024-02-01'
					},
					// 添加出库仪产品
					{
						id: 16,
						title: '智能出库仪 自动验货拍照',
						tags: ['全新', '自动称重', '智能验货'],
						price: '3980',
						sales: 42,
						image: '/static/images/outdevice1.jpg',
						category: 'outdevice',
						condition: 'new',
						priceRange: 'high',
						publishTime: '2024-01-15'
					},
					{
						id: 17,
						title: '快递出库仪 带扫描拍照功能',
						tags: ['全新', '一体化', '智能防呆'],
						price: '2880',
						sales: 65,
						image: '/static/images/outdevice2.jpg',
						category: 'outdevice',
						condition: 'new',
						priceRange: 'high',
						publishTime: '2023-12-18'
					},
					{
						id: 18,
						title: '二手智能出库仪 自动重量检测',
						tags: ['二手9成新', '带摄像头', '数据上传'],
						price: '1980',
						sales: 37,
						image: '/static/images/outdevice3.jpg',
						category: 'outdevice',
						condition: 'used',
						priceRange: 'medium',
						publishTime: '2024-02-10'
					},
					// 添加智能灯带产品
					{
						id: 19,
						title: '智能分拣灯带 按钮控制系统',
						tags: ['全新', '颜色可调', '自动感应'],
						price: '1580',
						sales: 78,
						image: '/static/images/lightstrip1.jpg',
						category: 'lightstrip',
						condition: 'new',
						priceRange: 'medium',
						publishTime: '2024-01-25'
					},
					{
						id: 20,
						title: '驿站智能灯带 格口指示系统',
						tags: ['全新', 'USB供电', '安装简单'],
						price: '680',
						sales: 126,
						image: '/static/images/lightstrip2.jpg',
						category: 'lightstrip',
						condition: 'new',
						priceRange: 'medium',
						publishTime: '2023-11-30'
					},
					{
						id: 21,
						title: 'LED智能提示灯带 分拣辅助',
						tags: ['全新', '低功耗', '可连接系统'],
						price: '880',
						sales: 95,
						image: '/static/images/lightstrip3.jpg',
						category: 'lightstrip',
						condition: 'new',
						priceRange: 'medium',
						publishTime: '2024-02-05'
					}
				],
				productList: [],
				// 添加搜索相关数据
				searchHistory: [],
				hotSearches: [
					'电子秤',
					'打印机',
					'货架',
					'收银机',
					'包装材料',
					'标签打印机',
					'扫码枪',
					'快递单打印机'
				],
				showSearchPanel: false,
				searchResults: [],
				isSearching: false,
				// 增加搜索相关的数据
				recentlyViewed: [],
				popularCategories: ['出库仪', '智能灯带', '电子秤', '打印机'],
				searchDebounceTimer: null
			}
		},
		onLoad() {
			// 页面加载时执行初始化
			this.initProductList();
			// 加载搜索历史
			this.loadSearchHistory();
		},
		onShow() {
			// 每次显示页面时刷新商品列表
			this.filterProducts();
		},
		onReachBottom() {
			// 上拉加载更多
			if (this.hasMore) {
				this.loadMoreData();
			}
		},
		methods: {
			// 初始化商品列表
			initProductList() {
				// 默认展示全部商品
				this.filterProducts();
			},
			
			// 根据当前筛选条件过滤商品
			filterProducts() {
				try {
					let filteredList = [...this.allProducts];
					
					// 按分类筛选
					if (this.activeCategory !== 'all') {
						filteredList = filteredList.filter(item => item.category === this.activeCategory);
					}
					
					// 按价格区间筛选
					if (this.activePrice !== 'all') {
						filteredList = filteredList.filter(item => item.priceRange === this.activePrice);
					}
					
					// 按商品类型筛选
					if (this.activeType !== 'all') {
						filteredList = filteredList.filter(item => item.condition === this.activeType);
					}
					
					// 加强关键词搜索
					if (this.searchKeyword && this.searchKeyword.trim()) {
						const keyword = this.searchKeyword.trim().toLowerCase();
						filteredList = filteredList.filter(item => {
							// 匹配标题
							if (item.title.toLowerCase().includes(keyword)) {
								return true;
							}
							
							// 匹配标签
							if (item.tags && item.tags.some(tag => tag.toLowerCase().includes(keyword))) {
								return true;
							}
							
							// 匹配分类
							const category = this.getCategoryName(item.category).toLowerCase();
							if (category.includes(keyword)) {
								return true;
							}
							
							return false;
						});
					}
					
					// 根据排序标签排序
					this.sortProducts(filteredList);
					
					// 更新商品列表
					this.productList = filteredList;
					
					// 重置分页状态
					this.pageNum = 1;
					this.hasMore = this.productList.length > this.pageSize;
					
					console.log('过滤后的商品列表:', this.productList.length);
				} catch (error) {
					console.error('过滤商品时出错:', error);
					this.productList = [...this.allProducts].slice(0, 10);
				}
			},
			
			// 对商品列表进行排序
			sortProducts(list) {
				switch(this.activeTab) {
					case 'sales':
						// 按销量排序（从高到低）
						list.sort((a, b) => b.sales - a.sales);
						break;
					case 'newest':
						// 按上架时间排序（从新到旧）
						list.sort((a, b) => new Date(b.publishTime) - new Date(a.publishTime));
						break;
					case 'price':
						// 按价格排序
						if (this.priceSortAsc) {
							// 价格从低到高
							list.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
						} else {
							// 价格从高到低
							list.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
						}
						break;
					default:
						// 综合排序（默认按销量和上架时间的综合）
						list.sort((a, b) => {
							// 销量权重0.7，新品权重0.3
							const scoreA = a.sales * 0.7 + (new Date(a.publishTime).getTime()) * 0.3;
							const scoreB = b.sales * 0.7 + (new Date(b.publishTime).getTime()) * 0.3;
							return scoreB - scoreA;
						});
				}
			},
			
			handleSearch() {
				// 处理搜索逻辑
				this.filterProducts();
			},
			
			showFilter() {
				this.showFilterSection = !this.showFilterSection;
			},
			
			changeCategory(category) {
				this.activeCategory = category;
				// 根据分类过滤商品
				this.filterProducts();
			},
			
			changeTab(tab) {
				if (this.activeTab === tab && tab === 'price') {
					// 已经选中价格，再次点击切换排序方向
					this.priceSortAsc = !this.priceSortAsc;
				} else {
					this.activeTab = tab;
					if (tab === 'price') {
						this.priceSortAsc = true;
					}
				}
				// 重新过滤并排序商品
				this.filterProducts();
			},
			
			selectPrice(price) {
				this.activePrice = price;
			},
			
			selectType(type) {
				this.activeType = type;
			},
			
			resetFilter() {
				this.activePrice = 'all';
				this.activeType = 'all';
			},
			
			applyFilter() {
				// 应用筛选条件并刷新数据
				this.showFilterSection = false;
				this.filterProducts();
			},
			
			viewDetail(item) {
				// 添加到最近浏览
				this.addToRecentlyViewed(item);
				
				// 如果从搜索来的，保存关键词
				if (this.searchKeyword) {
					this.saveSearchHistory(this.searchKeyword);
				}
				
				// 保存当前商品到本地存储，用于详情页显示
				uni.setStorageSync('currentProduct', JSON.stringify(item));
				
				// 跳转到商品详情页
				uni.navigateTo({
					url: '/pages/detail/detail?id=' + item.id + '&type=product'
				});
			},
			
			loadMoreData() {
				// 模拟加载更多数据
				setTimeout(() => {
					// 真实场景应该分页加载数据
					// 这里简单模拟已加载完所有数据
					this.hasMore = false;
				}, 1000);
			},
			navigateTo(url) {
				// 对于 tabBar 页面使用 switchTab，其他页面使用 navigateTo
				if (url.includes('/pages/station/') || 
					url.includes('/pages/device/') || 
					url.includes('/pages/job/') || 
					url.includes('/pages/service/') || 
					url.includes('/pages/message/') || 
					url.includes('/pages/my/')) {
					uni.switchTab({
						url: url
					});
				} else {
					uni.navigateTo({
						url: url
					});
				}
			},
			// 显示搜索面板
			showSearch() {
				this.showSearchPanel = true;
				// 加载搜索历史
				this.loadSearchHistory();
			},
			
			// 关闭搜索面板
			closeSearchPanel() {
				this.showSearchPanel = false;
				// 如果有搜索关键词但未执行搜索，清空关键词
				if (this.searchKeyword && !this.productList.length) {
					this.searchKeyword = '';
				}
			},
			
			// 加载搜索历史
			loadSearchHistory() {
				const history = uni.getStorageSync('stationSearchHistory');
				if (history) {
					this.searchHistory = JSON.parse(history);
				}
			},
			
			// 保存搜索历史
			saveSearchHistory(keyword) {
				// 如果关键词为空，不保存
				if (!keyword.trim()) return;
				
				// 移除旧的相同关键词
				let history = this.searchHistory.filter(item => item !== keyword);
				
				// 添加到历史开头
				history.unshift(keyword);
				
				// 只保留最近10条
				if (history.length > 10) {
					history = history.slice(0, 10);
				}
				
				// 更新本地和数据
				this.searchHistory = history;
				uni.setStorageSync('stationSearchHistory', JSON.stringify(history));
			},
			
			// 清空搜索历史
			clearHistory() {
				uni.showModal({
					title: '提示',
					content: '确定要清空搜索历史吗？',
					success: (res) => {
						if (res.confirm) {
							this.searchHistory = [];
							uni.removeStorageSync('stationSearchHistory');
						}
					}
				});
			},
			
			// 使用历史关键词
			useHistoryKeyword(keyword) {
				this.searchKeyword = keyword;
				this.performLiveSearch();
			},
			
			// 选择搜索结果
			selectSearchResult(item) {
				// 保存到搜索历史
				this.saveSearchHistory(this.searchKeyword);
				
				// 关闭搜索面板
				this.closeSearchPanel();
				
				// 查看商品详情
				this.viewDetail(item);
			},
			
			// 高亮关键词
			highlightKeyword(text) {
				if (!this.searchKeyword) return text;
				
				const keyword = this.searchKeyword.trim();
				const parts = text.split(new RegExp(`(${keyword})`, 'gi'));
				
				let result = '';
				parts.forEach(part => {
					if (part.toLowerCase() === keyword.toLowerCase()) {
						result += `<span style="color: #ff5a5f;">${part}</span>`;
					} else {
						result += part;
					}
				});
				
				return result;
			},
			
			// 搜索输入变化
			onSearchInput() {
				this.performLiveSearch();
			},
			
			// 执行实时搜索
			performLiveSearch() {
				// 如果关键词为空，清空结果
				if (!this.searchKeyword.trim()) {
					this.searchResults = [];
					this.isSearching = false;
					return;
				}
				
				this.isSearching = true;
				
				// 计算搜索分数，优化搜索结果排序
				const keyword = this.searchKeyword.toLowerCase();
				const weightedResults = this.allProducts.map(item => {
					// 搜索分数初始为0
					let score = 0;
					
					// 标题中包含关键词，得高分
					if (item.title.toLowerCase().includes(keyword)) {
						score += 10;
						// 标题以关键词开头，额外加分
						if (item.title.toLowerCase().startsWith(keyword)) {
							score += 5;
						}
					}
					
					// 标签中包含关键词，也得分
					if (item.tags && item.tags.some(tag => tag.toLowerCase().includes(keyword))) {
						score += 5;
					}
					
					// 分类匹配，得分
					if (item.category.toLowerCase().includes(keyword)) {
						score += 3;
					}
					
					return { item, score };
				}).filter(result => result.score > 0)
				  .sort((a, b) => b.score - a.score)
				  .map(result => result.item);
				
				this.searchResults = weightedResults;
				this.isSearching = false;
			},
			
			// 获取分类名称
			getCategoryName(category) {
				const categoryMap = {
					'scales': '电子秤',
					'printer': '打印机',
					'shelves': '货架柜',
					'outdevice': '出库仪',
					'lightstrip': '智能灯带'
				};
				return categoryMap[category] || '其他';
			},
			
			// 增加最近浏览记录
			addToRecentlyViewed(product) {
				// 从本地存储加载
				const viewed = uni.getStorageSync('recentlyViewed');
				let recentList = viewed ? JSON.parse(viewed) : [];
				
				// 检查是否已存在
				const index = recentList.findIndex(item => item.id === product.id);
				if (index !== -1) {
					recentList.splice(index, 1);
				}
				
				// 添加到列表开头
				recentList.unshift(product);
				
				// 限制数量
				if (recentList.length > 5) {
					recentList = recentList.slice(0, 5);
				}
				
				// 保存回本地
				uni.setStorageSync('recentlyViewed', JSON.stringify(recentList));
				this.recentlyViewed = recentList;
			},
			
			// 清除搜索关键词
			clearSearch() {
				this.searchKeyword = '';
				this.searchResults = [];
			}
		}
	}
</script>

<style>
	/* container */
	.container {
		padding-bottom: 30rpx;
	}
	
	/* 搜索栏 */
	.search-bar {
		position: sticky;
		top: 0;
		background-color: #fff;
		z-index: 100;
		padding: 20rpx;
		display: flex;
		align-items: center;
	}
	
	/* 搜索输入框 */
	.search-input-box {
		flex: 1;
		background-color: #f5f5f5;
		border-radius: 36rpx;
		height: 72rpx;
		display: flex;
		align-items: center;
		padding: 0 20rpx;
		margin-right: 20rpx;
	}
	
	/* 搜索图标 */
	.search-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 15rpx;
	}
	
	/* 输入框 */
	.search-input-box input {
		flex: 1;
		height: 72rpx;
		font-size: 28rpx;
	}
	
	/* 清除按钮 */
	.clear-btn {
		width: 36rpx;
		height: 36rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	/* 清除按钮图标 */
	.clear-btn image {
		width: 24rpx;
		height: 24rpx;
	}
	
	/* 筛选按钮 */
	.filter-btn {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	/* 筛选按钮图标 */
	.filter-btn image {
		width: 36rpx;
		height: 36rpx;
	}
	
	/* 分类导航 */
	.category-nav {
		display: flex;
		padding: 20rpx 0;
		background-color: #ffffff;
		margin-bottom: 20rpx;
		overflow-x: auto;
		white-space: nowrap;
	}
	
	/* 分类项 */
	.category-item {
		flex: 0 0 auto;
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 16.666%;
		padding: 0 10rpx;
		box-sizing: border-box;
	}
	
	/* 分类图标 */
	.category-item image {
		width: 60rpx;
		height: 60rpx;
		margin-bottom: 10rpx;
	}
	
	/* 分类文本 */
	.category-item text {
		font-size: 24rpx;
		color: #666666;
		white-space: nowrap;
	}
	
	/* 当前选中分类文本 */
	.category-item.active text {
		color: #ff5a5f;
		font-weight: bold;
	}
	
	/* 当前选中分类图标 */
	.category-item.active image {
		transform: scale(1.1);
	}
	
	.quick-filter {
		display: flex;
		background-color: #ffffff;
		padding: 20rpx 0;
		margin-bottom: 20rpx;
	}
	
	.filter-tab {
		flex: 1;
		text-align: center;
		font-size: 28rpx;
		color: #666666;
		position: relative;
	}
	
	.filter-tab.active {
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.sort-arrow {
		margin-left: 5rpx;
	}
	
	.filter-section {
		background-color: #ffffff;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}
	
	.filter-row {
		margin-bottom: 30rpx;
	}
	
	.filter-title {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
	}
	
	.filter-options {
		display: flex;
		flex-wrap: wrap;
	}
	
	.filter-option {
		padding: 10rpx 30rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		font-size: 26rpx;
	}
	
	.filter-option.active {
		background-color: #ffecee;
		color: #ff5a5f;
		border: 1rpx solid #ff5a5f;
	}
	
	.filter-actions {
		display: flex;
		justify-content: space-between;
	}
	
	.reset-btn, .confirm-btn {
		width: 48%;
		height: 80rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
	}
	
	.reset-btn {
		background-color: #f5f5f5;
		color: #666666;
	}
	
	.confirm-btn {
		background-color: #ff5a5f;
		color: #ffffff;
	}
	
	/* 商品图片占位符 */
	.product-image-placeholder {
		width: 100%;
		height: 320rpx;
		background-color: #f5f5f5;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.product-image-placeholder text {
		color: #999;
		font-size: 28rpx;
	}
	
	/* 商品网格 */
	.product-grid {
		padding: 0 20rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}
	
	/* 商品卡片 */
	.product-card {
		width: 48%;
		background-color: #ffffff;
		border-radius: 12rpx;
		overflow: hidden;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	/* 商品信息 */
	.product-info {
		padding: 20rpx;
	}
	
	/* 商品标题 */
	.product-title {
		font-size: 28rpx;
		color: #333333;
		line-height: 1.4;
		height: 78rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		margin-bottom: 10rpx;
	}
	
	/* 商品标签 */
	.product-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 10rpx;
	}
	
	/* 标签项 */
	.product-tag {
		font-size: 22rpx;
		color: #ff5a5f;
		background-color: #ffecee;
		border-radius: 4rpx;
		padding: 4rpx 10rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
	}
	
	/* 商品价格 */
	.product-price {
		font-size: 26rpx;
		color: #ff5a5f;
	}
	
	/* 价格数值 */
	.price-value {
		font-size: 32rpx;
		font-weight: bold;
	}
	
	/* 商品销量 */
	.product-sales {
		font-size: 24rpx;
		color: #999;
	}
	
	/* 加载更多 */
	.loading-more, .no-more {
		text-align: center;
		padding: 20rpx 0;
	}
	
	/* 加载文本样式 */
	.loading-more text, .no-more text {
		font-size: 24rpx;
		color: #999;
	}
	
	/* 搜索面板样式 */
	.search-panel {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #fff;
		z-index: 1000;
		display: flex;
		flex-direction: column;
	}
	
	.search-panel-header {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.search-input-container {
		flex: 1;
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		border-radius: 36rpx;
		padding: 0 20rpx;
		height: 72rpx;
		margin-right: 20rpx;
	}
	
	.search-input-container input {
		flex: 1;
		height: 72rpx;
		font-size: 28rpx;
	}
	
	.search-panel-content {
		flex: 1;
		padding: 20rpx;
	}
	
	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}
	
	.result-list {
		margin-bottom: 30rpx;
	}
	
	.result-item {
		display: flex;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.result-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 20rpx;
	}
	
	.result-info {
		flex: 1;
	}
	
	.result-title {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
	}
	
	.result-tags {
		display: flex;
	}
	
	.result-tag {
		font-size: 22rpx;
		color: #666;
		background-color: #f5f5f5;
		padding: 4rpx 12rpx;
		border-radius: 4rpx;
		margin-right: 10rpx;
	}
	
	.result-category {
		font-size: 24rpx;
		color: #999;
		margin-left: 20rpx;
	}
	
	.no-results {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 80rpx 0;
	}
	
	.no-result-icon {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 30rpx;
	}
	
	.no-result-text {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
	}
	
	.no-result-tip {
		font-size: 26rpx;
		color: #999;
	}
	
	.history-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.clear-history {
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #999;
	}
	
	.clear-history image {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
	}
	
	.history-list {
		margin-bottom: 30rpx;
	}
	
	.history-item {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;
	}
	
	.history-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 20rpx;
	}
	
	.history-item text {
		font-size: 28rpx;
		color: #333;
	}
	
	.hot-list {
		display: flex;
		flex-wrap: wrap;
	}
	
	.hot-item {
		padding: 12rpx 24rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		margin: 0 20rpx 20rpx 0;
		font-size: 24rpx;
		color: #666;
		display: flex;
		align-items: center;
	}
	
	.hot-rank {
		width: 32rpx;
		height: 32rpx;
		border-radius: 50%;
		font-size: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 10rpx;
		color: #fff;
	}
	
	.rank-1 {
		background-color: #ff5a5f;
	}
	
	.rank-2 {
		background-color: #ff9500;
	}
	
	.rank-3 {
		background-color: #34c759;
	}
	
	.cancel-btn {
		font-size: 28rpx;
		color: #333;
		padding: 0 10rpx;
	}
</style> 