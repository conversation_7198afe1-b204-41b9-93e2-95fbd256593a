<template>
	<view class="container">
		<!-- 搜索栏 -->
		<view class="search-bar">
			<view class="search-input-box">
				<image class="search-icon" src="/static/icons/search.png" mode="aspectFit"></image>
				<input type="text" placeholder="搜索职位/薪资/公司" v-model="searchKeyword" confirm-type="search" @confirm="handleSearch" />
			</view>
			<view class="filter-btn" @tap="showFilter">
				<image src="/static/icons/filter.png" mode="aspectFit"></image>
			</view>
		</view>
		
		<!-- 筛选区域 -->
		<view class="filter-section" v-if="showFilterSection">
			<view class="filter-row">
				<view class="filter-title">区域</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': activeArea === 'all'}" @tap="selectArea('all')">全部</view>
					<view class="filter-option" :class="{'active': activeArea === 'haidian'}" @tap="selectArea('haidian')">海淀区</view>
					<view class="filter-option" :class="{'active': activeArea === 'chaoyang'}" @tap="selectArea('chaoyang')">朝阳区</view>
					<view class="filter-option" :class="{'active': activeArea === 'dongcheng'}" @tap="selectArea('dongcheng')">东城区</view>
					<view class="filter-option" :class="{'active': activeArea === 'xicheng'}" @tap="selectArea('xicheng')">西城区</view>
				</view>
			</view>
			<view class="filter-row">
				<view class="filter-title">薪资范围</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': activeSalary === 'all'}" @tap="selectSalary('all')">全部</view>
					<view class="filter-option" :class="{'active': activeSalary === 'low'}" @tap="selectSalary('low')">4000以下</view>
					<view class="filter-option" :class="{'active': activeSalary === 'medium'}" @tap="selectSalary('medium')">4000-6000</view>
					<view class="filter-option" :class="{'active': activeSalary === 'high'}" @tap="selectSalary('high')">6000-8000</view>
					<view class="filter-option" :class="{'active': activeSalary === 'very_high'}" @tap="selectSalary('very_high')">8000以上</view>
				</view>
			</view>
			<view class="filter-row">
				<view class="filter-title">工作类型</view>
				<view class="filter-options">
					<view class="filter-option" :class="{'active': activeType === 'all'}" @tap="selectType('all')">全部</view>
					<view class="filter-option" :class="{'active': activeType === 'full'}" @tap="selectType('full')">全职</view>
					<view class="filter-option" :class="{'active': activeType === 'part'}" @tap="selectType('part')">兼职</view>
					<view class="filter-option" :class="{'active': activeType === 'intern'}" @tap="selectType('intern')">实习</view>
				</view>
			</view>
			<view class="filter-actions">
				<view class="reset-btn" @tap="resetFilter">重置</view>
				<view class="confirm-btn" @tap="applyFilter">确认</view>
			</view>
		</view>
		
		<!-- 职位分类选项卡 -->
		<view class="job-tabs">
			<scroll-view scroll-x class="tabs-scroll-view">
				<view class="tab-item" :class="{'active': activeTab === 'all'}" @tap="changeTab('all')">全部</view>
				<view class="tab-item" :class="{'active': activeTab === 'sorting'}" @tap="changeTab('sorting')">分拣员</view>
				<view class="tab-item" :class="{'active': activeTab === 'delivery'}" @tap="changeTab('delivery')">配送员</view>
				<view class="tab-item" :class="{'active': activeTab === 'manager'}" @tap="changeTab('manager')">站点管理</view>
				<view class="tab-item" :class="{'active': activeTab === 'customer'}" @tap="changeTab('customer')">客服</view>
				<view class="tab-item" :class="{'active': activeTab === 'warehouse'}" @tap="changeTab('warehouse')">仓储</view>
			</scroll-view>
		</view>
		
		<!-- 职位列表 -->
		<view class="job-list">
			<view class="job-item" v-for="(item, index) in jobList" :key="index" @tap="viewDetail(item)">
				<view class="job-header">
					<view class="job-title">{{item.title}}</view>
					<view class="job-salary">{{item.salary}}</view>
				</view>
				<view class="job-company">{{item.company}}</view>
				<view class="job-address">{{item.address}}</view>
				<view class="job-tags">
					<text class="job-tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
				</view>
				<view class="job-bottom">
					<view class="job-requirements">
						<text class="requirement">{{item.experience}}</text>
						<text class="requirement">{{item.education}}</text>
					</view>
					<view class="job-time">{{item.publishTime}}</view>
				</view>
			</view>
		</view>
		
		<!-- 加载更多 -->
		<view class="loading-more" v-if="hasMore">
			<text>正在加载更多...</text>
		</view>
		<view class="no-more" v-else>
			<text>没有更多数据了</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchKeyword: '',
				showFilterSection: false,
				activeArea: 'all',
				activeSalary: 'all',
				activeType: 'all',
				activeTab: 'all',
				hasMore: true,
				pageNum: 1,
				pageSize: 10,
				jobList: [
					{
						id: 1,
						title: '驿站分拣员',
						salary: '4500-5500元/月',
						company: '城北速递驿站',
						address: '海淀区北太平庄',
						tags: ['五险', '餐补', '全职'],
						experience: '经验不限',
						education: '学历不限',
						publishTime: '1小时前'
					},
					{
						id: 2,
						title: '快递配送员',
						salary: '8000-10000元/月',
						company: '优速快递',
						address: '朝阳区望京',
						tags: ['无需经验', '兼职可选', '多劳多得'],
						experience: '经验不限',
						education: '学历不限',
						publishTime: '3小时前'
					},
					{
						id: 3,
						title: '驿站管理员',
						salary: '6000-7000元/月',
						company: '中通快递',
						address: '东城区东四十条',
						tags: ['五险一金', '双休', '有晋升'],
						experience: '1-3年',
						education: '大专及以上',
						publishTime: '6小时前'
					},
					{
						id: 4,
						title: '快递分拣兼职',
						salary: '180元/天',
						company: '申通快递',
						address: '西城区德胜门',
						tags: ['日结', '兼职', '包吃'],
						experience: '经验不限',
						education: '学历不限',
						publishTime: '12小时前'
					},
					{
						id: 5,
						title: '驿站客服专员',
						salary: '4000-5000元/月',
						company: '韵达快递',
						address: '丰台区方庄',
						tags: ['五险', '包吃住', '有提成'],
						experience: '无经验可培训',
						education: '中专及以上',
						publishTime: '1天前'
					}
				]
			}
		},
		onLoad() {
			// 页面加载时读取本地存储的数据
			this.loadStoredData();
		},
		onShow() {
			// 每次页面显示时重新加载数据，确保数据最新
			this.loadStoredData();
		},
		onReachBottom() {
			// 上拉加载更多
			if (this.hasMore) {
				this.loadMoreData();
			}
		},
		methods: {
			handleSearch() {
				// 处理搜索逻辑
				uni.showToast({
					title: '搜索：' + this.searchKeyword,
					icon: 'none'
				});
			},
			showFilter() {
				this.showFilterSection = !this.showFilterSection;
			},
			selectArea(area) {
				this.activeArea = area;
			},
			selectSalary(salary) {
				this.activeSalary = salary;
			},
			selectType(type) {
				this.activeType = type;
			},
			resetFilter() {
				this.activeArea = 'all';
				this.activeSalary = 'all';
				this.activeType = 'all';
			},
			applyFilter() {
				// 应用筛选条件并刷新数据
				this.showFilterSection = false;
				uni.showToast({
					title: '筛选条件已应用',
					icon: 'none'
				});
				// 筛选数据
				this.filterJobList();
			},
			changeTab(tab) {
				this.activeTab = tab;
				// 切换标签重新加载数据
				this.filterJobList();
			},
			// 添加筛选职位数据的方法
			filterJobList() {
				// 获取原始数据
				let originalJobList = uni.getStorageSync('jobList') || [];
				if (originalJobList.length === 0) {
					originalJobList = this.jobList; // 如果没有存储数据，使用默认数据
				}
				
				// 根据选中的标签筛选数据
				if (this.activeTab !== 'all') {
					const positionMap = {
						'sorting': 'sorting',    // 分拣员
						'delivery': 'delivery',  // 配送员
						'manager': 'manager',    // 站点管理
						'customer': 'customer',  // 客服
						'warehouse': 'warehouse' // 仓储
					};
					
					originalJobList = originalJobList.filter(job => 
						job.positionType === positionMap[this.activeTab] || 
						// 为了兼容旧数据，对标题进行关键词匹配
						(this.activeTab === 'sorting' && job.title.includes('分拣')) ||
						(this.activeTab === 'delivery' && job.title.includes('配送')) ||
						(this.activeTab === 'manager' && job.title.includes('管理')) ||
						(this.activeTab === 'customer' && job.title.includes('客服')) ||
						(this.activeTab === 'warehouse' && job.title.includes('仓储'))
					);
				}
				
				// 应用其他筛选条件
				// 区域筛选
				if (this.activeArea !== 'all') {
					const areaMap = {
						'haidian': '海淀区',
						'chaoyang': '朝阳区',
						'dongcheng': '东城区',
						'xicheng': '西城区'
					};
					originalJobList = originalJobList.filter(job => 
						job.address && job.address.includes(areaMap[this.activeArea])
					);
				}
				
				// 薪资筛选
				if (this.activeSalary !== 'all') {
					originalJobList = originalJobList.filter(job => {
						const salary = job.salary || '';
						// 提取薪资数字
						const salaryNumber = parseInt(salary.match(/\d+/g)?.[0] || '0');
						
						switch(this.activeSalary) {
							case 'low':
								return salaryNumber < 4000;
							case 'medium':
								return salaryNumber >= 4000 && salaryNumber < 6000;
							case 'high':
								return salaryNumber >= 6000 && salaryNumber < 8000;
							case 'very_high':
								return salaryNumber >= 8000;
							default:
								return true;
						}
					});
				}
				
				// 工作类型筛选
				if (this.activeType !== 'all') {
					const typeMap = {
						'full': '全职',
						'part': '兼职',
						'intern': '临时工'
					};
					originalJobList = originalJobList.filter(job => {
						const jobType = job.jobType || '';
						const tags = job.tags || [];
						return jobType.includes(typeMap[this.activeType]) || 
							tags.some(tag => tag.includes(typeMap[this.activeType]));
					});
				}
				
				// 更新显示的数据
				this.jobList = originalJobList;
				
				// 重置翻页状态
				this.pageNum = 1;
				this.hasMore = originalJobList.length >= this.pageSize;
			},
			viewDetail(item) {
				uni.navigateTo({
					url: '/pages/detail/detail?id=' + item.id + '&type=job'
				});
			},
			loadMoreData() {
				// 模拟加载更多数据
				setTimeout(() => {
					if (this.pageNum >= 3) {
						this.hasMore = false;
					} else {
						this.pageNum++;
						// 添加更多数据
						// 真实场景应该调用API获取更多数据
					}
				}, 1000);
			},
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			// 加载本地存储的数据
			loadStoredData() {
				const storedData = uni.getStorageSync('jobList');
				if (storedData && storedData.length > 0) {
					// 使用存储的数据替换默认数据
					this.jobList = storedData;
				}
			}
		}
	}
</script>

<style>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
		position: relative;
	}
	
	.search-bar {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.search-input-box {
		flex: 1;
		background-color: #ffffff;
		border-radius: 60rpx;
		padding: 15rpx 30rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.search-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 15rpx;
	}
	
	.filter-btn {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.filter-btn image {
		width: 40rpx;
		height: 40rpx;
	}
	
	.filter-section {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.filter-row {
		margin-bottom: 20rpx;
	}
	
	.filter-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 15rpx;
	}
	
	.filter-options {
		display: flex;
		flex-wrap: wrap;
	}
	
	.filter-option {
		padding: 10rpx 20rpx;
		background-color: #f5f5f5;
		border-radius: 30rpx;
		margin-right: 15rpx;
		margin-bottom: 15rpx;
		font-size: 24rpx;
		color: #666666;
	}
	
	.filter-option.active {
		background-color: #ff5a5f;
		color: #ffffff;
	}
	
	.filter-actions {
		display: flex;
		justify-content: space-between;
		margin-top: 30rpx;
	}
	
	.reset-btn, .confirm-btn {
		width: 45%;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 40rpx;
		font-size: 28rpx;
	}
	
	.reset-btn {
		background-color: #f5f5f5;
		color: #666666;
	}
	
	.confirm-btn {
		background-color: #ff5a5f;
		color: #ffffff;
	}
	
	.job-tabs {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
	}
	
	.tabs-scroll-view {
		white-space: nowrap;
		padding: 0 20rpx;
	}
	
	.tab-item {
		display: inline-block;
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 30rpx;
		font-size: 28rpx;
		color: #666666;
		position: relative;
	}
	
	.tab-item.active {
		color: #ff5a5f;
		font-weight: bold;
	}
	
	.tab-item.active::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background-color: #ff5a5f;
	}
	
	.job-list {
		margin-top: 20rpx;
	}
	
	.job-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}
	
	.job-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
	}
	
	.job-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}
	
	.job-salary {
		font-size: 32rpx;
		font-weight: bold;
		color: #ff5a5f;
	}
	
	.job-company {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 10rpx;
	}
	
	.job-address {
		font-size: 26rpx;
		color: #666666;
		margin-bottom: 15rpx;
	}
	
	.job-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 15rpx;
	}
	
	.job-tag {
		font-size: 22rpx;
		padding: 6rpx 16rpx;
		background-color: #f8f8f8;
		color: #666666;
		margin-right: 15rpx;
		margin-bottom: 10rpx;
		border-radius: 30rpx;
	}
	
	.job-bottom {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-top: 1rpx solid #f0f0f0;
		padding-top: 15rpx;
	}
	
	.job-requirements {
		display: flex;
	}
	
	.requirement {
		font-size: 24rpx;
		color: #999999;
		margin-right: 20rpx;
	}
	
	.job-time {
		font-size: 24rpx;
		color: #999999;
	}
	
	.loading-more, .no-more {
		text-align: center;
		padding: 30rpx 0;
	}
	
	.loading-more text, .no-more text {
		font-size: 26rpx;
		color: #999999;
	}
</style> 