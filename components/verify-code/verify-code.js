Component({
  properties: {
    phone: {
      type: String,
      value: ''
    },
    type: {
      type: String,
      value: 'login' // login, register, resetPassword
    },
    disabled: {
      type: Boolean,
      value: false
    },
    value: {
      type: String,
      value: ''
    },
    placeholder: {
      type: String,
      value: '请输入验证码'
    }
  },
  
  data: {
    countdown: 0,
    btnText: '获取验证码'
  },
  
  lifetimes: {
    attached() {
      this.restoreCountdown();
    },
    detached() {
      if (this.countdownTimer) {
        clearTimeout(this.countdownTimer);
      }
    }
  },
  
  observers: {
    'countdown': function(countdown) {
      if (countdown > 0) {
        this.setData({
          btnText: `${countdown}秒后重新获取`
        });
        
        this.countdownTimer = setTimeout(() => {
          this.setData({
            countdown: this.data.countdown - 1
          });
        }, 1000);
      } else {
        this.setData({
          btnText: '获取验证码'
        });
        
        if (this.countdownTimer) {
          clearTimeout(this.countdownTimer);
          this.countdownTimer = null;
        }
      }
    }
  },
  
  methods: {
    // 恢复倒计时状态
    restoreCountdown() {
      const { phone, type } = this.properties;
      const key = `verifyCode_${phone}_${type}`;
      
      try {
        const expireTime = wx.getStorageSync(key);
        if (expireTime) {
          const now = Date.now();
          if (expireTime > now) {
            const remainingSeconds = Math.ceil((expireTime - now) / 1000);
            this.setData({
              countdown: remainingSeconds
            });
          } else {
            wx.removeStorageSync(key);
          }
        }
      } catch (e) {
        console.error('恢复倒计时状态失败', e);
      }
    },
    
    // 处理发送验证码
    handleSendCode() {
      const { phone, disabled, type } = this.properties;
      const { countdown } = this.data;
      
      if (countdown > 0 || !phone || disabled) {
        return;
      }
      
      // 触发发送验证码事件
      this.triggerEvent('send', { phone, type });
      
      // 设置倒计时
      this.setData({
        countdown: 60
      });
      
      // 保存倒计时状态
      const expireTime = Date.now() + 60 * 1000;
      const key = `verifyCode_${phone}_${type}`;
      
      try {
        wx.setStorageSync(key, expireTime);
      } catch (e) {
        console.error('保存倒计时状态失败', e);
      }
    },
    
    // 处理输入变化
    handleInput(e) {
      const value = e.detail.value;
      // 只允许输入数字，最多6位
      if (/^\d{0,6}$/.test(value)) {
        this.triggerEvent('change', { value });
      }
    },
    
    // 强制更新倒计时
    updateCountdown(seconds) {
      if (typeof seconds === 'number' && seconds >= 0) {
        const { phone, type } = this.properties;
        const key = `verifyCode_${phone}_${type}`;
        
        this.setData({
          countdown: seconds
        });
        
        if (seconds > 0) {
          const expireTime = Date.now() + seconds * 1000;
          try {
            wx.setStorageSync(key, expireTime);
          } catch (e) {
            console.error('保存倒计时状态失败', e);
          }
        } else {
          try {
            wx.removeStorageSync(key);
          } catch (e) {
            console.error('移除倒计时状态失败', e);
          }
        }
      }
    },
    
    // 重置组件状态
    reset() {
      const { phone, type } = this.properties;
      const key = `verifyCode_${phone}_${type}`;
      
      if (this.countdownTimer) {
        clearTimeout(this.countdownTimer);
        this.countdownTimer = null;
      }
      
      this.setData({
        countdown: 0,
        btnText: '获取验证码'
      });
      
      try {
        wx.removeStorageSync(key);
      } catch (e) {
        console.error('移除倒计时状态失败', e);
      }
    }
  }
}) 