import React, { useState, useEffect } from 'react';

/**
 * 验证码输入组件
 * @param {Object} props 组件属性
 * @param {String} props.phone 手机号码
 * @param {Function} props.onSendCode 发送验证码回调函数
 * @param {Function} props.onChange 验证码改变回调函数
 * @param {String} props.type 验证码类型(login/register/resetPassword)
 * @param {String} props.value 验证码值
 * @param {String} props.placeholder 占位文本
 * @param {Boolean} props.disabled 是否禁用
 */
const VerifyCodeInput = ({
  phone,
  onSendCode,
  onChange,
  type = 'login',
  value = '',
  placeholder = '请输入验证码',
  disabled = false
}) => {
  const [countdown, setCountdown] = useState(0);
  const [btnText, setBtnText] = useState('获取验证码');
  
  // 处理倒计时
  useEffect(() => {
    let timer = null;
    
    if (countdown > 0) {
      setBtnText(`${countdown}秒后重新获取`);
      timer = setTimeout(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
    } else {
      setBtnText('获取验证码');
    }
    
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [countdown]);
  
  // 从localStorage恢复倒计时状态
  useEffect(() => {
    const savedCountdown = localStorage.getItem(`verifyCode_${phone}_${type}`);
    if (savedCountdown) {
      const expireTime = parseInt(savedCountdown, 10);
      const now = Date.now();
      
      if (expireTime > now) {
        const remainingSeconds = Math.ceil((expireTime - now) / 1000);
        setCountdown(remainingSeconds);
      } else {
        localStorage.removeItem(`verifyCode_${phone}_${type}`);
      }
    }
  }, [phone, type]);
  
  // 发送验证码
  const handleSendCode = async () => {
    if (countdown > 0 || !phone) return;
    
    try {
      // 调用发送验证码回调
      await onSendCode();
      
      // 设置倒计时
      setCountdown(60);
      
      // 保存倒计时到localStorage
      const expireTime = Date.now() + 60 * 1000;
      localStorage.setItem(`verifyCode_${phone}_${type}`, expireTime.toString());
    } catch (error) {
      console.error('发送验证码失败:', error);
    }
  };
  
  // 处理输入变化
  const handleChange = (e) => {
    const { value } = e.target;
    // 只允许输入数字，最多6位
    if (/^\d{0,6}$/.test(value)) {
      onChange && onChange(value);
    }
  };
  
  return (
    <div className="verify-code-input">
      <input
        type="text"
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        maxLength={6}
        disabled={disabled}
      />
      <button
        type="button"
        onClick={handleSendCode}
        disabled={countdown > 0 || !phone || disabled}
        className={countdown > 0 ? 'counting' : ''}
      >
        {btnText}
      </button>
    </div>
  );
};

export default VerifyCodeInput; 