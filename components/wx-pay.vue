<template>
  <view class="wx-pay-container">
    <!-- 支付弹窗 -->
    <view class="pay-popup" v-if="showPayPanel">
      <view class="pay-panel">
        <view class="pay-header">
          <text class="pay-title">支付订单</text>
          <view class="close-btn" @tap="closePayPanel">
            <text>×</text>
          </view>
        </view>
        
        <view class="pay-amount">
          <text class="currency">¥</text>
          <text class="amount">{{(payAmount / 100).toFixed(2)}}</text>
        </view>
        
        <view class="pay-method">
          <view class="method-item" @tap="selectPayMethod('wxpay')">
            <view class="method-info">
              <image src="/static/icons/wxpay.png" mode="aspectFit"></image>
              <text>微信支付</text>
            </view>
            <view class="check-icon" :class="{'checked': payMethod === 'wxpay'}">
              <view class="inner-circle" v-if="payMethod === 'wxpay'"></view>
            </view>
          </view>
          
          <view class="method-item" @tap="selectPayMethod('balance')" v-if="userBalance >= payAmount">
            <view class="method-info">
              <image src="/static/icons/wallet.png" mode="aspectFit"></image>
              <text>余额支付</text>
              <text class="balance-info">(可用余额: ¥{{(userBalance / 100).toFixed(2)}})</text>
            </view>
            <view class="check-icon" :class="{'checked': payMethod === 'balance'}">
              <view class="inner-circle" v-if="payMethod === 'balance'"></view>
            </view>
          </view>
        </view>
        
        <view class="pay-action">
          <view class="pay-btn" @tap="confirmPay">立即支付</view>
        </view>
      </view>
    </view>
    
    <!-- 支付结果弹窗 -->
    <view class="result-popup" v-if="showResultPanel">
      <view class="result-panel">
        <view class="result-icon" :class="{'success': paySuccess, 'fail': !paySuccess}">
          <text v-if="paySuccess">✓</text>
          <text v-else>×</text>
        </view>
        
        <view class="result-title">
          <text v-if="paySuccess">支付成功</text>
          <text v-else>支付失败</text>
        </view>
        
        <view class="result-desc">
          <text v-if="paySuccess">您的订单支付已完成</text>
          <text v-else>{{payErrorMsg || '支付过程中出现错误，请重试'}}</text>
        </view>
        
        <view class="result-action">
          <view class="result-btn" @tap="handleResultClose">
            <text v-if="paySuccess">完成</text>
            <text v-else>重新支付</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    // 订单ID
    orderId: {
      type: String,
      default: ''
    },
    // 支付金额（单位：分）
    amount: {
      type: Number,
      default: 0
    },
    // 支付描述
    description: {
      type: String,
      default: '驿站帮-商品购买'
    },
    // 显示支付面板
    visible: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      showPayPanel: false,
      showResultPanel: false,
      payMethod: 'wxpay',
      payAmount: 0,
      userBalance: 0,
      paySuccess: false,
      payErrorMsg: '',
      paymentId: '',
      tradeNo: '',
      isProcessing: false,
      pollingTimer: null
    }
  },
  
  watch: {
    visible(newVal) {
      this.showPayPanel = newVal;
      if (newVal) {
        this.payAmount = this.amount;
        this.loadUserInfo();
      }
    },
    
    // 监控支付面板状态
    showPayPanel(newVal) {
      if (!newVal) {
        this.$emit('update:visible', false);
      }
    }
  },
  
  beforeDestroy() {
    // 清除计时器
    this.clearPollingTimer();
  },
  
  methods: {
    // 加载用户信息
    async loadUserInfo() {
      try {
        const userInfoStr = uni.getStorageSync('userInfo');
        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          this.userBalance = userInfo.balance || 0;
        }
      } catch (err) {
        console.error('获取用户信息失败', err);
        this.userBalance = 0;
      }
    },
    
    // 选择支付方式
    selectPayMethod(method) {
      this.payMethod = method;
    },
    
    // 关闭支付面板
    closePayPanel() {
      this.showPayPanel = false;
    },
    
    // 确认支付
    async confirmPay() {
      if (this.isProcessing) return;
      
      // 余额支付
      if (this.payMethod === 'balance') {
        await this.balancePay();
        return;
      }
      
      // 微信支付
      await this.wxPay();
    },
    
    // 微信支付
    async wxPay() {
      if (!this.orderId || this.payAmount <= 0) {
        this.showPayError('无效的订单信息');
        return;
      }
      
      this.isProcessing = true;
      uni.showLoading({ title: '处理中...' });
      
      try {
        // 调用云函数，创建支付订单
        const result = await wx.cloud.callFunction({
          name: 'pay',
          data: {
            action: 'createPayment',
            orderId: this.orderId,
            totalFee: this.payAmount,
            body: this.description
          }
        });
        
        if (result.result && result.result.code === 0) {
          const { paymentId, tradeNo, payParams } = result.result.data;
          this.paymentId = paymentId;
          this.tradeNo = tradeNo;
          
          // 发起微信支付
          wx.requestPayment({
            ...payParams,
            success: () => {
              this.startPaymentPolling();
            },
            fail: (err) => {
              console.error('支付失败', err);
              if (err.errMsg === 'requestPayment:fail cancel') {
                // 用户取消支付
                this.closePayPanel();
              } else {
                this.showPayError('支付失败: ' + err.errMsg);
              }
            },
            complete: () => {
              this.isProcessing = false;
              uni.hideLoading();
            }
          });
        } else {
          this.showPayError(result.result.msg || '创建支付订单失败');
          this.isProcessing = false;
          uni.hideLoading();
        }
      } catch (err) {
        console.error('微信支付调用失败', err);
        this.showPayError('支付异常，请重试');
        this.isProcessing = false;
        uni.hideLoading();
      }
    },
    
    // 余额支付
    async balancePay() {
      if (!this.orderId || this.payAmount <= 0) {
        this.showPayError('无效的订单信息');
        return;
      }
      
      if (this.userBalance < this.payAmount) {
        this.showPayError('余额不足');
        return;
      }
      
      this.isProcessing = true;
      uni.showLoading({ title: '处理中...' });
      
      try {
        // 调用云函数，使用余额支付
        const result = await wx.cloud.callFunction({
          name: 'balancePay',
          data: {
            orderId: this.orderId,
            amount: this.payAmount
          }
        });
        
        if (result.result && result.result.code === 0) {
          // 支付成功
          this.showPayPanel = false;
          this.paySuccess = true;
          this.showResultPanel = true;
          
          // 通知父组件支付成功
          this.$emit('pay-success', {
            orderId: this.orderId,
            paymentMethod: 'balance',
            amount: this.payAmount
          });
        } else {
          this.showPayError(result.result.msg || '余额支付失败');
        }
      } catch (err) {
        console.error('余额支付失败', err);
        this.showPayError('支付异常，请重试');
      } finally {
        this.isProcessing = false;
        uni.hideLoading();
      }
    },
    
    // 开始轮询支付结果
    startPaymentPolling() {
      this.clearPollingTimer();
      
      // 立即查询一次
      this.queryPaymentResult();
      
      // 设置轮询定时器 (每2秒查询一次，最多查询10次)
      let count = 0;
      const maxCount = 10;
      
      this.pollingTimer = setInterval(async () => {
        count++;
        const success = await this.queryPaymentResult();
        
        // 如果支付成功或者已查询10次，则停止轮询
        if (success || count >= maxCount) {
          this.clearPollingTimer();
          
          // 如果最后一次查询仍未成功，显示支付失败
          if (!success && count >= maxCount) {
            this.showPayError('支付结果查询超时，请稍后在订单中心查看');
          }
        }
      }, 2000);
    },
    
    // 清除轮询定时器
    clearPollingTimer() {
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
      }
    },
    
    // 查询支付结果
    async queryPaymentResult() {
      try {
        if (!this.orderId) return false;
        
        const result = await wx.cloud.callFunction({
          name: 'pay',
          data: {
            action: 'queryPayment',
            orderId: this.orderId
          }
        });
        
        if (result.result && result.result.code === 0 && result.result.data.isPaid) {
          // 支付成功
          this.showPayPanel = false;
          this.paySuccess = true;
          this.showResultPanel = true;
          
          // 通知父组件支付成功
          this.$emit('pay-success', {
            orderId: this.orderId,
            paymentMethod: 'wxpay',
            amount: this.payAmount,
            tradeNo: result.result.data.tradeNo
          });
          
          return true;
        }
        
        return false;
      } catch (err) {
        console.error('查询支付结果失败', err);
        return false;
      }
    },
    
    // 显示支付错误
    showPayError(msg) {
      this.showPayPanel = false;
      this.paySuccess = false;
      this.payErrorMsg = msg;
      this.showResultPanel = true;
    },
    
    // 处理结果面板关闭
    handleResultClose() {
      if (this.paySuccess) {
        // 支付成功，关闭结果面板
        this.showResultPanel = false;
      } else {
        // 支付失败，重新打开支付面板
        this.showResultPanel = false;
        this.showPayPanel = true;
      }
    }
  }
}
</script>

<style>
.wx-pay-container {
  /* 容器样式 */
}

/* 支付弹窗 */
.pay-popup {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: flex-end;
}

.pay-panel {
  width: 100%;
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.pay-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.pay-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn text {
  font-size: 40rpx;
  color: #999999;
}

.pay-amount {
  text-align: center;
  margin-bottom: 50rpx;
}

.currency {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.amount {
  font-size: 60rpx;
  font-weight: bold;
  color: #333333;
}

.pay-method {
  margin-bottom: 50rpx;
}

.method-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.method-info {
  display: flex;
  align-items: center;
}

.method-info image {
  width: 50rpx;
  height: 50rpx;
  margin-right: 20rpx;
}

.method-info text {
  font-size: 30rpx;
  color: #333333;
}

.balance-info {
  font-size: 24rpx;
  color: #999999;
  margin-left: 10rpx;
}

.check-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #dddddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon.checked {
  border-color: #ff5a5f;
}

.inner-circle {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #ff5a5f;
}

.pay-action {
  margin-top: 50rpx;
}

.pay-btn {
  width: 100%;
  height: 90rpx;
  background-color: #ff5a5f;
  color: #ffffff;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

/* 支付结果弹窗 */
.result-popup {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-panel {
  width: 80%;
  background-color: #ffffff;
  border-radius: 20rpx;
  padding: 50rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.result-icon {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.result-icon.success {
  background-color: #07c160;
}

.result-icon.fail {
  background-color: #ff5a5f;
}

.result-icon text {
  font-size: 60rpx;
  color: #ffffff;
  font-weight: bold;
}

.result-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.result-desc {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 40rpx;
  text-align: center;
}

.result-action {
  width: 100%;
}

.result-btn {
  width: 100%;
  height: 90rpx;
  background-color: #ff5a5f;
  color: #ffffff;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}
</style> 