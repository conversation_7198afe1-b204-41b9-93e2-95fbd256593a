/**
 * API接口统一导出
 */
import request from './request';
import userApi from './modules/user';
import stationApi from './modules/station';
import orderApi from './modules/order';
import serviceApi from './modules/service';
import jobApi from './modules/job';
import deviceApi from './modules/device';
import commonApi from './modules/common';
import productApi from './modules/product';
import authApi from './modules/auth';

// 统一导出所有API模块
const api = {
  // 请求方法
  request,
  
  // 各模块API
  user: userApi,
  station: stationApi,
  order: orderApi,
  service: serviceApi,
  job: jobApi,
  device: deviceApi,
  common: commonApi,
  product: productApi,
  auth: authApi
};

// 导出默认API对象
export default api;

// 导出各个模块，方便按需引入
export {
  request,
  userApi as user,
  stationApi as station,
  orderApi as order,
  serviceApi as service,
  jobApi as job,
  deviceApi as device,
  commonApi as common,
  productApi as product,
  authApi as auth
}; 