import request from './request';

/**
 * 检查手机号是否已注册
 * @param {string} phone 手机号
 */
export function checkPhoneExists(phone) {
  return request.get('/api/auth/check-phone', { phone });
}

/**
 * 发送短信验证码
 * @param {string} phone 手机号
 * @param {string} type 验证码类型 (login/register/resetPassword)
 */
export function sendSmsCode(phone, type = 'login') {
  return request.post('/api/sms/send', { phone, type });
}

/**
 * 使用手机号和验证码登录
 * @param {string} phone 手机号
 * @param {string} code 验证码
 */
export function loginByPhone(phone, code) {
  return request.post('/api/auth/phone-login', { phone, code });
}

/**
 * 使用手机号和密码登录
 * @param {string} phone 手机号
 * @param {string} password 密码
 */
export function loginByPassword(phone, password) {
  return request.post('/api/auth/password-login', { phone, password });
}

/**
 * 微信登录
 * @param {Object} data 登录参数
 * @param {string} data.code 微信登录返回的code
 * @param {Object} data.userInfo 微信用户信息
 */
export function wxLogin(data) {
  return request.post('/api/auth/wx-login', data);
}

/**
 * 用户注册
 * @param {Object} data 注册参数
 * @param {string} data.username 用户名
 * @param {string} data.phone 手机号
 * @param {string} data.code 验证码
 * @param {string} data.password 密码
 */
export function register(data) {
  return request.post('/api/auth/register', data);
}

/**
 * 重置密码
 * @param {string} phone 手机号
 * @param {string} code 验证码
 * @param {string} password 新密码
 */
export function resetPassword(phone, code, password) {
  return request.post('/api/auth/reset-password', { phone, code, password });
}

/**
 * 退出登录
 */
export function logout() {
  return request.post('/api/auth/logout');
}

/**
 * 刷新令牌
 */
export function refreshToken() {
  return request.post('/api/auth/refresh-token');
}

/**
 * 获取登录状态
 */
export function getLoginStatus() {
  return request.get('/api/auth/status');
} 