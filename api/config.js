/**
 * API配置文件
 */

// 不同环境下的API基础URL
export const API_BASE_URL = {
  development: 'http://localhost:3000',  // 开发环境
  test: 'https://test-api.yizhanbang.com',  // 测试环境
  production: 'https://yizhanbang-backend-xxxx.ap-shanghai.service.tcloudbase.com'  // 微信云托管环境
};

// 微信小程序特定的API基础URL
export const MINIPROGRAM_API_BASE_URL = {
  development: 'http://***********:3000',  // 本机IP，确保微信开发者工具可以访问
  test: 'https://test-api.yizhanbang.com',
  production: 'https://yizhanbang-backend-xxxx.ap-shanghai.service.tcloudbase.com'
};

// 获取当前环境
export const getCurrentEnv = () => {
  // 根据环境变量或其他配置获取当前环境
  // 这里简单地根据process.env.NODE_ENV判断
  return process.env.NODE_ENV || 'development';
};

// 获取当前环境的API基础URL
export const getApiBaseUrl = () => {
  const currentEnv = getCurrentEnv();
  
  // 判断是否在微信小程序环境中
  // #ifdef MP-WEIXIN
  return MINIPROGRAM_API_BASE_URL[currentEnv] || MINIPROGRAM_API_BASE_URL.development;
  // #endif
  
  // 其他环境
  return API_BASE_URL[currentEnv] || API_BASE_URL.development;
};

// API超时设置
export const API_TIMEOUT = 60000; // 60秒

// 响应成功的状态码
export const SUCCESS_CODE = 200;

// 需要刷新token的状态码
export const REFRESH_TOKEN_CODE = 401;

// 跳转登录页的状态码
export const REDIRECT_LOGIN_CODE = [401, 403];

// 是否开启接口请求日志
export const ENABLE_LOG = true; 