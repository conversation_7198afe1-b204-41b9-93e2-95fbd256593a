/**
 * API请求封装
 */
import { getToken } from '../utils/auth';

// 配置信息
const config = {
  baseURL: process.env.NODE_ENV === 'production' ? '' : 'http://localhost:3000',
  timeout: 30000, // 增加超时时间到30秒
  success_code: 0,
  redirect_login_code: 401,
  enable_log: true
};

// 获取API基础URL
const getApiBaseUrl = () => {
  return config.baseURL;
};

// 请求拦截器
const requestInterceptor = (options) => {
  const token = uni.getStorageSync('token');
  if (token) {
    if (!options.header) {
      options.header = {};
    }
    options.header['Authorization'] = 'Bearer ' + token;
  }
  
  if (config.enable_log) {
    console.log('Request:', options.url, options.data || options.params);
  }
  
  return options;
};

// 响应拦截器
const responseInterceptor = (response) => {
  const res = response.data;
  
  if (config.enable_log) {
    console.log('Response:', response.config?.url, res);
  }
  
  // 如果返回的状态码不是0，说明接口请求有问题
  if (res.code !== config.success_code) {
    // 处理特定的错误码
    if (res.code === config.redirect_login_code) {
      // 401: 未授权，token过期等
      console.error('身份验证失败，请重新登录');
      // 清除token
      uni.removeStorageSync('token');
      uni.removeStorageSync('userInfo');
      
      // 可以在这里跳转到登录页面
      // uni.navigateTo({ url: '/pages/my/login' });
    }
    
    uni.showToast({
      title: res.message || '请求失败',
      icon: 'none'
    });
    
    throw new Error(res.message || '未知错误');
  }
  
  return res;
};

// 封装请求方法
const request = (options) => {
  // 合并选项
  const finalOptions = {
    ...options,
    url: options.url.startsWith('http') ? options.url : `${getApiBaseUrl()}${options.url}`,
    timeout: options.timeout || config.timeout,
  };
  
  // 应用请求拦截器
  const finalConfig = requestInterceptor(finalOptions);
  
  // 发送请求
  return new Promise((resolve, reject) => {
    // 添加重试计数
    let retryCount = 0;
    const maxRetries = 2;
    
    function sendRequest() {
      uni.request({
        ...finalConfig,
        success: (res) => {
          try {
            const result = responseInterceptor(res);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        },
        fail: (err) => {
          console.error('Request failed:', finalConfig.url, err);
          
          // 超时时自动重试
          if (err.errMsg && err.errMsg.includes('timeout') && retryCount < maxRetries) {
            retryCount++;
            console.log(`请求超时，第${retryCount}次重试...`);
            setTimeout(() => {
              sendRequest();
            }, 1000);
            return;
          }
          
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          });
          
          reject(err);
        }
      });
    }
    
    // 开始请求
    sendRequest();
  });
};

// 封装各种请求方法
export default {
  get(url, params = {}, options = {}) {
    return request({
      url,
      method: 'GET',
      data: params,
      ...options
    });
  },
  
  post(url, data = {}, options = {}) {
    return request({
      url,
      method: 'POST',
      data,
      ...options
    });
  },
  
  put(url, data = {}, options = {}) {
    return request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  },
  
  delete(url, data = {}, options = {}) {
    return request({
      url,
      method: 'DELETE',
      data,
      ...options
    });
  },
  
  // 上传文件
  upload(url, filePath, name = 'file', formData = {}, options = {}) {
    const token = uni.getStorageSync('token');
    const header = token ? { 'Authorization': `Bearer ${token}` } : {};
    
    // 开启日志
    if (config.enable_log) {
      console.log('Upload request:', `${getApiBaseUrl()}${url}`, { filePath, name, formData });
    }
    
    return new Promise((resolve, reject) => {
      uni.uploadFile({
        url: `${getApiBaseUrl()}${url}`,
        filePath,
        name,
        formData,
        header,
        ...options,
        success: (res) => {
          // 上传成功后的处理
          if (res.statusCode >= 200 && res.statusCode < 300) {
            try {
              // 注意：uploadFile返回的数据是字符串，需要解析成对象
              const data = JSON.parse(res.data);
              
              if (config.enable_log) {
                console.log('Upload response:', url, data);
              }
              
              if (data.code === config.success_code) {
                resolve(data);
              } else {
                uni.showToast({
                  title: data.message || '上传失败',
                  icon: 'none'
                });
                reject(data);
              }
            } catch (e) {
              uni.showToast({
                title: '上传结果解析失败',
                icon: 'none'
              });
              reject(e);
            }
          } else {
            uni.showToast({
              title: `上传失败(${res.statusCode})`,
              icon: 'none'
            });
            reject(res);
          }
        },
        fail: (err) => {
          uni.showToast({
            title: '网络请求失败',
            icon: 'none'
          });
          
          if (config.enable_log) {
            console.error('Upload failed:', url, err);
          }
          
          reject(err);
        }
      });
    });
  }
}; 