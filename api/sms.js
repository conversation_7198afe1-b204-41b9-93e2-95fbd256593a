import { request } from './request';

/**
 * 检查手机号是否已注册
 * @param {String} phone 手机号码
 * @returns {Promise} API响应
 */
export const checkPhoneExists = (phone) => {
  return request({
    url: '/api/auth/check-phone',
    method: 'get',
    params: { phone }
  });
};

/**
 * 发送短信验证码
 * @param {String} phone 手机号码
 * @param {String} type 验证码类型 (login/register/resetPassword)
 * @returns {Promise} API响应
 */
export const sendSmsCode = (phone, type) => {
  return request({
    url: '/api/sms/send',
    method: 'post',
    data: { phone, type }
  });
};

/**
 * 手机号验证码登录
 * @param {String} phone 手机号码
 * @param {String} code 验证码
 * @returns {Promise} API响应
 */
export const loginByPhone = (phone, code) => {
  return request({
    url: '/api/auth/phone-login',
    method: 'post',
    data: { phone, code }
  });
};

/**
 * 手机号密码登录
 * @param {String} phone 手机号码
 * @param {String} password 密码
 * @returns {Promise} API响应
 */
export const loginByPassword = (phone, password) => {
  return request({
    url: '/api/auth/password-login',
    method: 'post',
    data: { phone, password }
  });
};

/**
 * 用户注册
 * @param {Object} data 注册信息
 * @param {String} data.username 用户名
 * @param {String} data.phone 手机号码
 * @param {String} data.code 验证码
 * @param {String} data.password 密码
 * @returns {Promise} API响应
 */
export const register = (data) => {
  return request({
    url: '/api/auth/register',
    method: 'post',
    data
  });
};

/**
 * 重置密码
 * @param {String} phone 手机号码
 * @param {String} code 验证码
 * @param {String} password 新密码
 * @returns {Promise} API响应
 */
export const resetPassword = (phone, code, password) => {
  return request({
    url: '/api/auth/reset-password',
    method: 'post',
    data: { phone, code, password }
  });
}; 