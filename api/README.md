# 驿站帮Pro小程序后台管理系统 API 接口

本文档描述了驿站帮Pro小程序后台管理系统的API接口设计和使用方法。

## 目录结构

```
api/
├── config.js         # API配置文件
├── request.js        # 请求封装
├── index.js          # 接口统一导出
├── modules/          # 接口模块
│   ├── common.js     # 公共接口
│   ├── device.js     # 设备相关接口
│   ├── job.js        # 工作岗位相关接口
│   ├── order.js      # 订单相关接口
│   ├── service.js    # 服务相关接口
│   ├── station.js    # 驿站相关接口
│   └── user.js       # 用户相关接口
└── README.md         # 文档说明
```

## 使用方法

### 导入API

```javascript
// 导入整个API对象
import api from '@/api';

// 按需导入特定模块
import { user, order } from '@/api';
```

### 调用接口

```javascript
// 使用整个API对象调用
api.user.wxLogin(data).then(res => {
  console.log('登录成功', res);
}).catch(err => {
  console.error('登录失败', err);
});

// 使用按需导入的模块调用
user.wxLogin(data).then(res => {
  console.log('登录成功', res);
}).catch(err => {
  console.error('登录失败', err);
});
```

## 接口模块说明

### 用户相关接口 (user)

用户账户管理和个人中心相关功能。

#### 账户管理
- `wxLogin(data)` - 微信登录授权接口，获取用户openid和session_key
- `getUserProfile()` - 获取用户基本信息（昵称、头像等）
- `bindPhone(data)` - 绑定用户手机号
- `updateUserInfo(data)` - 更新用户信息（头像、昵称等）
- `getUserInfo()` - 获取用户个人信息
- `logout()` - 用户退出登录

#### 个人中心
- `getUserOrderStats()` - 获取用户订单统计（待付款、待服务、已完成等）
- `getUserAddressList()` - 获取用户地址列表
- `addUserAddress(data)` - 添加用户地址
- `updateUserAddress(id, data)` - 更新用户地址
- `deleteUserAddress(id)` - 删除用户地址
- `setDefaultAddress(id)` - 设置默认地址
- `getUserCouponList(params)` - 获取用户优惠券列表
- `getUserPoints()` - 获取用户积分
- `getPointsHistory(params)` - 获取积分历史记录

### 驿站相关接口 (station)

驿站浏览和评价相关功能。

#### 驿站浏览
- `getNearbyStations(params)` - 获取附近驿站列表（基于地理位置）
- `getRecommendedStations(params)` - 获取推荐驿站列表
- `getStationDetail(id)` - 获取驿站详情
- `getStationServices(id, params)` - 获取驿站提供的服务列表
- `searchStations(params)` - 搜索驿站（按名称、地址、服务类型等）
- `favoriteStation(id, data)` - 收藏/取消收藏驿站
- `getUserFavoriteStations(params)` - 获取用户收藏的驿站列表

#### 驿站评价
- `getStationReviews(id, params)` - 获取驿站评价列表
- `addStationReview(id, data)` - 添加驿站评价
- `getMyStationReviews(params)` - 获取我的驿站评价

### 订单相关接口 (order)

订单管理和支付相关功能。

#### 下单流程
- `createOrder(data)` - 创建订单
- `getOrderDetail(id)` - 获取订单详情
- `calculateOrderAmount(data)` - 计算订单金额（含优惠券、折扣等）
- `cancelOrder(id, data)` - 取消订单
- `getAvailableCoupons(params)` - 获取可用优惠券列表（基于订单金额和服务类型）

#### 支付相关
- `createPayment(id, data)` - 创建支付单，获取支付参数
- `checkPaymentStatus(id)` - 查询支付状态
- `getPaymentHistory(params)` - 获取支付历史
- `requestRefund(id, data)` - 申请退款
- `getRefundStatus(id)` - 查询退款状态

#### 订单管理
- `getUserOrderList(params)` - 获取用户订单列表（可按状态筛选）
- `confirmServiceCompleted(id)` - 确认服务完成
- `addOrderReview(id, data)` - 添加订单评价
- `getOrderProgress(id)` - 获取订单进度

### 工作岗位接口 (job)

工作岗位和顶班接单相关功能。

#### 工作岗位
- `getJobList(params)` - 获取工作岗位列表
- `getJobDetail(id)` - 获取工作岗位详情
- `applyJob(id, data)` - 申请工作岗位
- `getMyJobApplications(params)` - 获取我的岗位申请列表

#### 顶班接单
- `publishShiftDemand(data)` - 发布顶班需求
- `getShiftDemandList(params)` - 获取顶班需求列表
- `getShiftDemandDetail(id)` - 获取顶班需求详情
- `acceptShift(id, data)` - 接受顶班任务
- `cancelShiftDemand(id, data)` - 取消顶班任务（发布方取消）
- `cancelShiftAcceptance(id, data)` - 取消顶班任务（接单方取消）
- `confirmShiftCompleted(id)` - 确认顶班任务完成
- `reviewShift(id, data)` - 评价顶班任务
- `getMyPublishedShifts(params)` - 获取我的顶班任务列表（发布的）
- `getMyAcceptedShifts(params)` - 获取我的顶班任务列表（接单的）
- `getShiftSettlements(params)` - 获取顶班结算记录
- `getAvailableShiftTimes()` - 获取可提供顶班的时间段
- `setAvailableShiftTimes(data)` - 设置可提供顶班的时间段
- `getShiftStatistics()` - 获取顶班统计数据

### 系统配置接口 (common)

系统配置和基础功能相关接口。

#### 基础配置
- `getAppConfig()` - 获取小程序基础配置（联系方式、关于我们等）
- `getServiceAgreement()` - 获取服务协议
- `getPrivacyPolicy()` - 获取隐私政策
- `getVersionInfo()` - 获取版本信息

#### 消息通知
- `getMessageList(params)` - 获取消息列表
- `getMessageDetail(id)` - 获取消息详情
- `readMessage(id)` - 标记消息为已读
- `deleteMessage(id)` - 删除消息

#### 其他功能
- `getUserLocation(data)` - 获取用户位置
- `searchAddress(params)` - 搜索地址（可对接地图API）
- `uploadImage(filePath, formData)` - 上传图片
- `uploadFile(filePath, formData)` - 上传文件
- `reportUserBehavior(data)` - 上报用户行为数据（页面访问、停留时间等）
- `reportError(data)` - 上报错误信息

## 安全性和性能优化

### 安全性
- 所有接口都需要进行token验证
- 敏感数据接口需要增加额外的安全检查
- 防止CSRF和XSS攻击

### 性能优化
- 列表接口支持分页
- 支持数据缓存策略
- 减少请求数量，合并相关数据

### 用户体验
- 支持断网数据缓存和恢复
- 提供数据预加载接口
- 支持增量更新

### 错误处理
- 统一错误码和错误信息
- 友好的错误提示
- 日志记录与错误上报

## 示例代码

### 微信登录示例

```javascript
// 页面中调用登录
import { user } from '@/api';

// 调用微信登录
wx.login({
  success: (res) => {
    if (res.code) {
      // 发送 res.code 到后台换取 openId, sessionKey, unionId
      user.wxLogin({ code: res.code }).then(result => {
        // 登录成功，保存token
        uni.setStorageSync('token', result.data.token);
        // 获取用户信息
        return user.getUserInfo();
      }).then(info => {
        // 处理用户信息
        console.log('用户信息', info.data);
      }).catch(err => {
        console.error('登录失败', err);
      });
    } else {
      console.error('登录失败', res.errMsg);
    }
  }
});
```

### 获取附近驿站示例

```javascript
import { station } from '@/api';
import { getLocation } from '@/utils/location';

// 获取当前位置并搜索附近驿站
getLocation().then(location => {
  return station.getNearbyStations({
    latitude: location.latitude,
    longitude: location.longitude,
    radius: 5000,  // 搜索半径，单位米
    page: 1,
    pageSize: 10
  });
}).then(res => {
  // 处理附近驿站数据
  this.stations = res.data.records;
}).catch(err => {
  console.error('获取附近驿站失败', err);
  uni.showToast({
    title: '获取附近驿站失败',
    icon: 'none'
  });
});
```

### 创建订单示例

```javascript
import { order } from '@/api';

// 创建订单
order.createOrder({
  stationId: this.stationId,
  serviceId: this.serviceId,
  appointmentTime: this.appointmentTime,
  remark: this.remark,
  addressId: this.addressId,
  couponId: this.couponId
}).then(res => {
  // 订单创建成功，跳转到支付页面
  const orderId = res.data.orderId;
  uni.navigateTo({
    url: `/pages/payment/index?orderId=${orderId}`
  });
}).catch(err => {
  console.error('创建订单失败', err);
  uni.showToast({
    title: err.message || '创建订单失败',
    icon: 'none'
  });
});
```

## 接口请求/响应格式

### 请求格式

```javascript
// GET请求
{
  url: '/api/user/info',
  method: 'GET',
  header: {
    'Authorization': 'Bearer xxxxx'
  }
}

// POST请求
{
  url: '/api/order/create',
  method: 'POST',
  header: {
    'Authorization': 'Bearer xxxxx',
    'Content-Type': 'application/json'
  },
  data: {
    // 请求数据
  }
}
```

### 响应格式

```javascript
// 成功响应
{
  code: 0,
  message: 'success',
  data: {
    // 响应数据
  }
}

// 失败响应
{
  code: 10001,
  message: '请求参数错误',
  data: null
}
```

## 错误码说明

| 错误码 | 说明 |
|-------|------|
| 0     | 成功 |
| 401   | 未登录或登录过期 |
| 403   | 无权限访问 |
| 404   | 资源不存在 |
| 10001 | 参数错误 |
| 10002 | 用户不存在 |
| 20001 | 订单创建失败 |
| 20002 | 支付失败 |
| 30001 | 服务不可用 |

## 注意事项

1. 所有请求必须携带有效的Authorization头
2. 列表类接口支持分页，默认每页20条
3. 上传文件接口使用multipart/form-data格式
4. 接口返回的时间格式为ISO 8601标准格式
5. 接口调用失败会自动重试，最多3次
6. 敏感操作需要验证码或短信验证 