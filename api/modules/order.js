/**
 * 订单相关API接口
 */
import request from '../request';

// API路径前缀
const API_PREFIX = '/order';

export default {
  /**
   * 获取订单列表（可按状态筛选）
   * @param {Object} params - 查询参数，包括状态、分页等
   */
  getOrderList(params) {
    return request.get(`${API_PREFIX}/list`, params);
  },
  
  /**
   * 创建订单
   * @param {Object} data - 订单数据
   */
  createOrder(data) {
    return request.post(`${API_PREFIX}/create`, data);
  },
  
  /**
   * 获取订单详情
   * @param {String} id - 订单ID
   */
  getOrderDetail(id) {
    return request.get(`${API_PREFIX}/detail/${id}`);
  },
  
  /**
   * 计算订单金额（含优惠券、折扣等）
   * @param {Object} data - 计算订单金额所需数据
   */
  calculateOrderAmount(data) {
    return request.post(`${API_PREFIX}/calculate`, data);
  },
  
  /**
   * 取消订单
   * @param {String} id - 订单ID
   * @param {Object} data - 取消原因等数据
   */
  cancelOrder(id, data = {}) {
    return request.post(`${API_PREFIX}/cancel/${id}`, data);
  },
  
  /**
   * 获取可用优惠券列表（基于订单金额和服务类型）
   * @param {Object} params - 查询参数
   */
  getAvailableCoupons(params) {
    return request.get(`${API_PREFIX}/available-coupons`, params);
  },
  
  /**
   * 创建支付单，获取支付参数
   * @param {String} id - 订单ID
   * @param {Object} data - 支付相关数据
   */
  createPayment(id, data) {
    return request.post(`${API_PREFIX}/payment/${id}`, data);
  },
  
  /**
   * 查询支付状态
   * @param {String} id - 订单ID
   */
  checkPaymentStatus(id) {
    return request.get(`${API_PREFIX}/payment-status/${id}`);
  },
  
  /**
   * 获取支付历史
   * @param {Object} params - 查询参数
   */
  getPaymentHistory(params) {
    return request.get(`${API_PREFIX}/payment-history`, params);
  },
  
  /**
   * 申请退款
   * @param {String} id - 订单ID
   * @param {Object} data - 退款原因等数据
   */
  requestRefund(id, data) {
    return request.post(`${API_PREFIX}/refund/${id}`, data);
  },
  
  /**
   * 查询退款状态
   * @param {String} id - 订单ID
   */
  getRefundStatus(id) {
    return request.get(`${API_PREFIX}/refund-status/${id}`);
  },
  
  /**
   * 获取用户订单列表（可按状态筛选）
   * @param {Object} params - 查询参数，包括状态、分页等
   */
  getUserOrderList(params) {
    return request.get(`${API_PREFIX}/user-orders`, params);
  },
  
  /**
   * 确认服务完成
   * @param {String} id - 订单ID
   */
  confirmServiceCompleted(id) {
    return request.post(`${API_PREFIX}/confirm-service/${id}`);
  },
  
  /**
   * 添加订单评价
   * @param {String} id - 订单ID
   * @param {Object} data - 评价数据
   */
  addOrderReview(id, data) {
    return request.post(`${API_PREFIX}/review/${id}`, data);
  },
  
  /**
   * 获取订单进度
   * @param {String} id - 订单ID
   */
  getOrderProgress(id) {
    return request.get(`${API_PREFIX}/progress/${id}`);
  },
  
  /**
   * 获取订单统计
   */
  getOrderStatistics() {
    return request.get(`${API_PREFIX}/statistics`);
  },
  
  /**
   * 获取订单物流信息
   * @param {String} id - 订单ID
   */
  getOrderLogistics(id) {
    return request.get(`${API_PREFIX}/logistics/${id}`);
  },
  
  /**
   * 延长自动收货时间
   * @param {String} id - 订单ID
   */
  extendReceiveTime(id) {
    return request.post(`${API_PREFIX}/extend-receive-time/${id}`);
  }
}; 