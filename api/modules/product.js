/**
 * 产品相关API接口
 */
import request from '../request';

// API路径前缀
const API_PREFIX = '/product';

export default {
  /**
   * 获取产品列表
   * @param {Object} params - 查询参数，包括分类、分页等
   */
  getProductList(params) {
    return request.get(`${API_PREFIX}/list`, params);
  },
  
  /**
   * 获取产品详情
   * @param {String} id - 产品ID
   */
  getProductDetail(id) {
    return request.get(`${API_PREFIX}/detail/${id}`);
  },
  
  /**
   * 获取产品分类
   */
  getProductCategories() {
    return request.get(`${API_PREFIX}/categories`);
  },
  
  /**
   * 获取热门产品
   * @param {Object} params - 查询参数，如数量限制等
   */
  getHotProducts(params) {
    return request.get(`${API_PREFIX}/hot`, params);
  },
  
  /**
   * 获取推荐产品
   * @param {Object} params - 查询参数，如数量限制等
   */
  getRecommendProducts(params) {
    return request.get(`${API_PREFIX}/recommend`, params);
  },
  
  /**
   * 获取新品
   * @param {Object} params - 查询参数，如数量限制等
   */
  getNewProducts(params) {
    return request.get(`${API_PREFIX}/new`, params);
  },
  
  /**
   * 搜索产品
   * @param {Object} params - 搜索参数，如关键词、分类等
   */
  searchProducts(params) {
    return request.get(`${API_PREFIX}/search`, params);
  },
  
  /**
   * 获取产品评价
   * @param {String} id - 产品ID
   * @param {Object} params - 查询参数，如分页等
   */
  getProductReviews(id, params) {
    return request.get(`${API_PREFIX}/reviews/${id}`, params);
  },
  
  /**
   * 添加产品评价
   * @param {String} id - 产品ID
   * @param {Object} data - 评价数据
   */
  addProductReview(id, data) {
    return request.post(`${API_PREFIX}/reviews/${id}`, data);
  }
}; 