/**
 * 文件上传相关API
 */
import request from '../request';

export default {
  /**
   * 上传单张图片
   * @param {String} filePath 本地文件路径
   * @param {Object} formData 附加表单数据
   * @param {String} type 文件类型标识
   * @returns {Promise}
   */
  uploadImage(filePath, formData = {}, type = 'default') {
    return request.upload('/upload/image', filePath, 'image', {
      ...formData,
      type
    });
  },
  
  /**
   * 上传多张图片
   * @param {Array<String>} filePaths 本地文件路径数组
   * @param {Object} formData 附加表单数据
   * @param {String} type 文件类型标识
   * @returns {Promise}
   */
  uploadImages(filePaths, formData = {}, type = 'default') {
    return Promise.all(filePaths.map(filePath => 
      this.uploadImage(filePath, formData, type)
    ));
  },
  
  /**
   * 删除文件
   * @param {String} filename 文件名
   * @returns {Promise}
   */
  deleteFile(filename) {
    return request({
      url: `/upload/file/${filename}`,
      method: 'DELETE'
    });
  }
}; 