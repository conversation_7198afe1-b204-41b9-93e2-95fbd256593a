/**
 * 公共API接口
 */
import request from '../request';

// API路径前缀
const API_PREFIX = '/common';

export default {
  /**
   * 获取小程序基础配置（联系方式、关于我们等）
   */
  getAppConfig() {
    return request.get(`${API_PREFIX}/app-config`);
  },
  
  /**
   * 获取服务协议
   */
  getServiceAgreement() {
    return request.get(`${API_PREFIX}/service-agreement`);
  },
  
  /**
   * 获取隐私政策
   */
  getPrivacyPolicy() {
    return request.get(`${API_PREFIX}/privacy-policy`);
  },
  
  /**
   * 获取版本信息
   */
  getVersionInfo() {
    return request.get(`${API_PREFIX}/version`);
  },
  
  /**
   * 获取消息列表
   * @param {Object} params - 查询参数
   */
  getMessageList(params) {
    return request.get(`${API_PREFIX}/messages`, params);
  },
  
  /**
   * 获取消息详情
   * @param {String} id - 消息ID
   */
  getMessageDetail(id) {
    return request.get(`${API_PREFIX}/messages/${id}`);
  },
  
  /**
   * 标记消息为已读
   * @param {String} id - 消息ID
   */
  readMessage(id) {
    return request.put(`${API_PREFIX}/messages/${id}/read`);
  },
  
  /**
   * 删除消息
   * @param {String} id - 消息ID
   */
  deleteMessage(id) {
    return request.delete(`${API_PREFIX}/messages/${id}`);
  },
  
  /**
   * 获取用户位置
   * @param {Object} data - 位置数据
   */
  getUserLocation(data) {
    return request.post(`${API_PREFIX}/location`, data);
  },
  
  /**
   * 搜索地址（可对接地图API）
   * @param {Object} params - 搜索参数
   */
  searchAddress(params) {
    return request.get(`${API_PREFIX}/search-address`, params);
  },
  
  /**
   * 上传图片
   * @param {String} filePath - 文件路径
   * @param {Object} formData - 额外表单数据
   */
  uploadImage(filePath, formData = {}) {
    return request.upload(`${API_PREFIX}/upload-image`, filePath, 'image', formData);
  },
  
  /**
   * 上传文件
   * @param {String} filePath - 文件路径
   * @param {Object} formData - 额外表单数据
   */
  uploadFile(filePath, formData = {}) {
    return request.upload(`${API_PREFIX}/upload-file`, filePath, 'file', formData);
  },
  
  /**
   * 上报用户行为数据（页面访问、停留时间等）
   * @param {Object} data - 行为数据
   */
  reportUserBehavior(data) {
    return request.post(`${API_PREFIX}/report-behavior`, data);
  },
  
  /**
   * 上报错误信息
   * @param {Object} data - 错误信息
   */
  reportError(data) {
    return request.post(`${API_PREFIX}/report-error`, data);
  },
  
  /**
   * 获取地区列表
   */
  getRegions() {
    return request.get(`${API_PREFIX}/regions`);
  },
  
  /**
   * 获取服务分类
   */
  getServiceCategories() {
    return request.get(`${API_PREFIX}/service-categories`);
  },
  
  /**
   * 获取支付方式
   */
  getPaymentMethods() {
    return request.get(`${API_PREFIX}/payment-methods`);
  },
  
  /**
   * 获取首页数据
   */
  getHomeData() {
    return request.get(`${API_PREFIX}/home`);
  },
  
  /**
   * 反馈意见
   * @param {Object} data - 反馈数据
   */
  submitFeedback(data) {
    return request.post(`${API_PREFIX}/feedback`, data);
  },
  
  /**
   * 获取首页轮播图
   */
  getBanners() {
    return request.get(`${API_PREFIX}/banners`);
  },
  
  /**
   * 获取首页活动公告
   */
  getAnnouncements() {
    return request.get(`${API_PREFIX}/announcements`);
  },
  
  /**
   * 获取配置参数
   * @param {Object} params - 参数名称
   */
  getConfig(params) {
    return request.get(`${API_PREFIX}/config`, params);
  },
  
  /**
   * 获取应用版本信息
   */
  getAppVersion() {
    return request.get(`${API_PREFIX}/app-version`);
  },
  
  /**
   * 获取协议内容
   * @param {Object} params - 协议类型参数
   */
  getAgreement(params) {
    return request.get(`${API_PREFIX}/agreement`, params);
  },
  
  /**
   * 获取帮助中心内容
   */
  getHelpCenter() {
    return request.get(`${API_PREFIX}/help-center`);
  },
  
  /**
   * 获取常见问题列表
   */
  getFaqs() {
    return request.get(`${API_PREFIX}/faqs`);
  }
}; 