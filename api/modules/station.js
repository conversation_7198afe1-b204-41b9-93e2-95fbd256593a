/**
 * 驿站相关API
 */
import request from '../request';

export default {
  /**
   * 获取驿站列表
   * @param {Object} params 查询参数
   * @returns {Promise}
   */
  getList(params = {}) {
    return request({
      url: '/stations',
      method: 'GET',
      data: params
    });
  },
  
  /**
   * 获取驿站详情
   * @param {String} id 驿站ID
   * @returns {Promise}
   */
  getDetail(id) {
    return request({
      url: `/stations/${id}`,
      method: 'GET'
    });
  },
  
  /**
   * 创建驿站信息
   * @param {Object} data 驿站数据
   * @returns {Promise}
   */
  create(data) {
    return request({
      url: '/stations',
      method: 'POST',
      data
    });
  },
  
  /**
   * 更新驿站信息
   * @param {String} id 驿站ID
   * @param {Object} data 更新数据
   * @returns {Promise}
   */
  update(id, data) {
    return request({
      url: `/stations/${id}`,
      method: 'PUT',
      data
    });
  },
  
  /**
   * 删除驿站信息
   * @param {String} id 驿站ID
   * @returns {Promise}
   */
  remove(id) {
    return request({
      url: `/stations/${id}`,
      method: 'DELETE'
    });
  },
  
  /**
   * 发布驿站信息
   * @param {Object} data - 驿站数据
   */
  publishStation(data) {
    return request.post('/stations/publish', data);
  },
  
  /**
   * 获取驿站类型列表
   */
  getStationTypes() {
    return request.get('/stations/types');
  },
  
  /**
   * 获取区域列表
   */
  getAreaList() {
    return request.get('/stations/areas');
  },
  
  /**
   * 驿站认证申请
   * @param {Object} data - 认证数据
   */
  verifyStation(data) {
    return request.post('/stations/verify', data);
  },
  
  /**
   * 获取认证状态
   */
  getVerifyStatus() {
    return request.get('/stations/verify-status');
  },
  
  /**
   * 获取我的驿站列表
   * @param {Object} params - 查询参数
   */
  getMyStations(params) {
    return request.get('/stations/my-stations', params);
  },
  
  /**
   * 购买驿站服务
   * @param {Object} data - 购买数据
   */
  purchaseStation(data) {
    return request.post('/stations/purchase', data);
  },
  
  /**
   * 获取周边驿站
   * @param {Object} params - 位置参数，包括经纬度和半径
   */
  getNearbyStations(params) {
    return request.get('/stations/nearby', params);
  },
  
  /**
   * 评价驿站
   * @param {String} id - 驿站ID
   * @param {Object} data - 评价数据
   */
  reviewStation(id, data) {
    return request.post(`/stations/${id}/review`, data);
  },
  
  /**
   * 获取驿站评价列表
   * @param {String} id - 驿站ID
   * @param {Object} params - 查询参数
   */
  getStationReviews(id, params) {
    return request.get(`/stations/${id}/reviews`, params);
  },
  
  /**
   * 获取推荐驿站列表
   * @param {Object} params - 查询参数，如分页等
   */
  getRecommendedStations(params) {
    return request.get('/stations/recommended', params);
  },
  
  /**
   * 获取驿站提供的服务列表
   * @param {String} id - 驿站ID
   * @param {Object} params - 查询参数，如分类、分页等
   */
  getStationServices(id, params) {
    return request.get(`/stations/${id}/services`, params);
  },
  
  /**
   * 搜索驿站（按名称、地址、服务类型等）
   * @param {Object} params - 搜索参数
   */
  searchStations(params) {
    return request.get('/stations/search', params);
  },
  
  /**
   * 收藏/取消收藏驿站
   * @param {String} id - 驿站ID
   * @param {Object} data - 收藏数据，如isFavorite等
   */
  favoriteStation(id, data) {
    return request.post(`/stations/${id}/favorite`, data);
  },
  
  /**
   * 获取用户收藏的驿站列表
   * @param {Object} params - 查询参数，如分页等
   */
  getUserFavoriteStations(params) {
    return request.get('/stations/favorites', params);
  },
  
  /**
   * 获取驿站评价列表
   * @param {String} id - 驿站ID
   * @param {Object} params - 查询参数，如分页、评分筛选等
   */
  getStationReviews(id, params) {
    return request.get(`/stations/${id}/reviews`, params);
  },
  
  /**
   * 添加驿站评价
   * @param {String} id - 驿站ID
   * @param {Object} data - 评价数据
   */
  addStationReview(id, data) {
    return request.post(`/stations/${id}/reviews`, data);
  },
  
  /**
   * 获取我的驿站评价
   * @param {Object} params - 查询参数
   */
  getMyStationReviews(params) {
    return request.get('/stations/my-reviews', params);
  },
  
  /**
   * 申请成为驿站
   * @param {Object} data - 驿站申请数据
   */
  applyStation(data) {
    return request.post('/stations/apply', data);
  },
  
  /**
   * 获取驿站申请状态
   */
  getStationApplicationStatus() {
    return request.get('/stations/application-status');
  },
  
  /**
   * 获取驿站管理信息
   * @param {String} id - 驿站ID
   */
  getStationManageInfo(id) {
    return request.get(`/stations/${id}/manage`);
  },
  
  /**
   * 更新驿站信息
   * @param {String} id - 驿站ID
   * @param {Object} data - 驿站信息
   */
  updateStationInfo(id, data) {
    return request.put(`/stations/${id}`, data);
  },
  
  /**
   * 上传驿站图片
   * @param {String} id - 驿站ID
   * @param {String} filePath - 文件路径
   */
  uploadStationImage(id, filePath) {
    return request.upload(`/stations/${id}/image`, filePath, 'image');
  },
  
  /**
   * 获取驿站工作人员列表
   * @param {String} id - 驿站ID
   * @param {Object} params - 查询参数
   */
  getStationStaff(id, params) {
    return request.get(`/stations/${id}/staff`, params);
  },
  
  /**
   * 添加驿站工作人员
   * @param {String} id - 驿站ID
   * @param {Object} data - 工作人员数据
   */
  addStationStaff(id, data) {
    return request.post(`/stations/${id}/staff`, data);
  },
  
  /**
   * 删除驿站工作人员
   * @param {String} id - 驿站ID
   * @param {String} staffId - 工作人员ID
   */
  removeStationStaff(id, staffId) {
    return request.delete(`/stations/${id}/staff/${staffId}`);
  }
}; 